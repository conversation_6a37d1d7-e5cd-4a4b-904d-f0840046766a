# 依赖镜像
image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci

services:
  - name: harbor-registry.inner.youdao.com/devops/docker:20-dind
    entrypoint: [ "dockerd-entrypoint.sh", "--tls=false" ]
    alias: docker

variables:
  DOCKER_IMAGE_TEST_NAME: '$CI_HARBOR_REGISTRY/ead-test/pacioli-test:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  DOCKER_IMAGE_SANDBOX_NAME: '$CI_HARBOR_REGISTRY/ead-test/pacioli-sandbox:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  DOCKER_IMAGE_DEV_NAME: '$CI_HARBOR_REGISTRY/ead-test/pacioli-dev:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  DOCKER_IMAGE_ONLINE_NAME: '$CI_HARBOR_REGISTRY/ead/pacioli-online:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'

before_script:
  - echo "======== before script ========"

after_script:
  - echo "======== after script ========"

# docker templating config
.docker_build_tpl: &docker_build_def
  script:
    - docker logout $CI_HARBOR_REGISTRY
    - echo $CI_HARBOR_TOKEN | docker login -u $CI_HARBOR_USER --password-stdin $CI_HARBOR_REGISTRY
    - eval echo ${DOCKER_IMAGE_NAME} $ENV
    - eval docker build --build-arg ENV=${ENV} -t $DOCKER_IMAGE_NAME .
    - eval docker push $DOCKER_IMAGE_NAME
    - echo "finish docker-build stage"

.deploy_tpl: &deploy_def
  script:
    - WEB_URL="https://rancher-new.inner.youdao.com/dashboard/c/${RANCHER_CLUSTER}/explorer/apps.deployment/${RANCHER_NAMESPACE}/${RANCHER_WORKLOAD}"
    - MSG="开始部署 ${RANCHER_NAMESPACE}/${RANCHER_WORKLOAD} ${WEB_URL}"
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA By = $GITLAB_USER_EMAIL $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - ydci notify popo -t $GITLAB_USER_EMAIL  -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"
    - eval ydci deploy set-image ${DOCKER_IMAGE_NAME} -c ${RANCHER_CLUSTER_NAME} -n ${RANCHER_NAMESPACE} -w ${RANCHER_WORKLOAD}

.notify_tpl: &notify_def
  script:
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - POPOID=1446877
    - ydci notify group -t $POPOID  -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"

.notify_popo_tpl: &notify_popo_def
  script:
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA By = $GITLAB_USER_EMAIL $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - echo $FULL_MSG
    - ydci notify popo -t $GITLAB_USER_EMAIL -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"

# 定义阶段 stages
stages:
  - build
  - docker-build
  - deploy
  - notify

# 定义 job build 项目
job:build:
  image: harbor-registry.inner.youdao.com/ead/centos7-jdk17-gradle-7.5-font:latest
  tags:
    - k8s
  stage: build
  script:
    - echo "start build stage"
    - gradle clean bootJar --info -g .
    - echo "finish build stage"
  cache:
    key: pacioli
    paths:
      - .gradle/
  artifacts:
    paths:
      - build
  allow_failure: false

job:build-online:
  image: harbor-registry.inner.youdao.com/ead/centos7-jdk17-gradle-7.5-font:latest
  tags:
    - k8s
  stage: build
  script:
    - echo "start build stage"
    - gradle clean bootJar --info -g .
    - echo "finish build stage"
  cache:
    key: pacioli
    paths:
      - .gradle/
  artifacts:
    paths:
      - build
  only:
    - triggers
  allow_failure: false

# 定义 job，构建镜像推送到测试远程
job:docker-build-test:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_TEST_NAME
    ENV: 'test'
  script:
  <<: *docker_build_def
  only:
    - test
  environment:
    name: test
  dependencies:
    - job:build


# 定义 job，构建镜像推送到测试远程
job:docker-build-sandbox:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_SANDBOX_NAME
    ENV: 'sandbox'
  script:
  <<: *docker_build_def
  only:
    - sandbox
    - master
  environment:
    name: sandbox
  dependencies:
    - job:build


# 定义job，构建构建镜像推送到开发远程
job:docker-build-dev:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_DEV_NAME
    ENV: 'dev'
  script:
  <<: *docker_build_def
  only:
    - dev
  environment:
    name: dev
  dependencies:
    - job:build

# 定义 job，构建镜像推送到线上远程
job:docker-build-online:
  tags:
    - k8s
  stage: docker-build
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_ONLINE_NAME
    ENV: 'online'
  script:
  <<: *docker_build_def
  only:
    - triggers
  dependencies:
    - job:build-online

job:deploy-dev:
  stage: deploy
  tags:
    - k8s
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_DEV_NAME
    RANCHER_CLUSTER: "c-m-nh6klrxk"
    RANCHER_CLUSTER_NAME: "k8s-dev-common1"
    RANCHER_NAMESPACE: "ad"
    RANCHER_WORKLOAD: "pacioli-dev"
  script:
  <<: *deploy_def
  only:
    - dev
  environment:
    name: dev
    url: https://pacioli-dev.inner.youdao.com/swagger-ui/index.html
  dependencies:
    - job:docker-build-dev

# 定义 job，测试环境部署
job:deploy-test:
  stage: deploy
  tags:
    - k8s
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_TEST_NAME
    RANCHER_CLUSTER: "c-m-nh6klrxk"
    RANCHER_CLUSTER_NAME: "k8s-dev-common1"
    RANCHER_NAMESPACE: "ad"
    RANCHER_WORKLOAD: "pacioli-test"
  script:
  <<: *deploy_def
  only:
    - test
  environment:
    name: test
    url: https://pacioli-test.inner.youdao.com/swagger-ui/index.html
  dependencies:
    - job:docker-build-test

# 定义 job，测试环境部署
job:deploy-sandbox:
  stage: deploy
  tags:
    - k8s
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_SANDBOX_NAME
    RANCHER_CLUSTER: "c-m-nh6klrxk"
    RANCHER_CLUSTER_NAME: "k8s-dev-common1"
    RANCHER_NAMESPACE: "ad"
    RANCHER_WORKLOAD: "pacioli-sandbox"
  script:
  <<: *deploy_def
  only:
    - sandbox
    - master
  environment:
    name: sandbox
    url: https://pacioli-sandbox.inner.youdao.com/swagger-ui/index.html
  dependencies:
    - job:docker-build-sandbox

# 定义 job，线上环境部署
job:deploy-online:
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  stage: deploy
  tags:
    - k8s
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_ONLINE_NAME
    RANCHER_CLUSTER: "c-m-tfstzpcf"
    RANCHER_CLUSTER_NAME: "k8s-prod-common1"
    RANCHER_NAMESPACE: "ad-web"
    RANCHER_WORKLOAD: "pacioli-online"
  script:
  <<: *deploy_def
  only:
    - triggers
  dependencies:
    - job:docker-build-online

job:notify_success:
  stage: notify
  tags:
    - k8s
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  variables:
    MSG: 'CI PIPELINE SUCCESS'
  script:
  <<: *notify_def
  only:
    - triggers

job:notify_failure:
  stage: notify
  tags:
    - k8s
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  when: on_failure
  variables:
    MSG: 'CI PIPELINE FAIL'
  script:
  <<: *notify_def
  only:
    - triggers

job:notify_popo_success:
  stage: notify
  tags:
    - k8s
  variables:
    MSG: 'pacioli gitlab ci pipeline SUCCESS'
  script:
  <<: *notify_popo_def
  only:
    - test
    - dev
    - sandbox
    - master
  except:
    - triggers

job:notify_popo_failure:
  stage: notify
  tags:
    - k8s
  when: on_failure
  variables:
    MSG: 'pacioli gitlab ci pipeline FAILED'
  script:
  <<: *notify_popo_def
  except:
    - triggers
