# 依赖镜像
image: harbor-registry.inner.youdao.com/ead/centos7-openjdk8-maven3.8.6:1.2.0

services:
  - name: harbor-registry.inner.youdao.com/devops/docker:20-dind
    entrypoint: ["dockerd-entrypoint.sh", "--tls=false"]
    alias: docker

variables:
  DOCKER_IMAGE_TEST_NAME: '$CI_HARBOR_REGISTRY/ead-test/convert-tracking-test:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  DOCKER_IMAGE_ONLINE_NAME: '$CI_HARBOR_REGISTRY/ead/convert-tracking-online:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA'
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

before_script:
  - echo "======== before script ========"

after_script:
  - echo "======== after script ========"


.docker_build_tpl: &docker_build_def
  script:
    - docker logout $CI_HARBOR_REGISTRY
    - echo $CI_HARBOR_TOKEN | docker login -u $CI_HARBOR_USER --password-stdin $CI_HARBOR_REGISTRY
    - eval echo ${DOCKER_IMAGE_NAME} $ENV
    - eval docker build --build-arg ENV=${ENV} -t $DOCKER_IMAGE_NAME .
    - eval docker push $DOCKER_IMAGE_NAME
    - echo "finish docker-build stage"

.docker_build_online_tpl: &docker_build_online_def
  script:
    - docker logout $CI_HARBOR_REGISTRY
    - echo $CI_HARBOR_TOKEN | docker login -u $CI_HARBOR_USER --password-stdin $CI_HARBOR_REGISTRY
    - echo $DOCKER_IMAGE_ONLINE_NAME
    - ENV="online"
    - docker build --build-arg ENV=${ENV} -t $DOCKER_IMAGE_ONLINE_NAME .
    - docker push $DOCKER_IMAGE_ONLINE_NAME
    - echo "finish docker-build stage"

.deploy_tpl: &deploy_def
  script:
    - WEB_URL="${RANCHER_URL}/p/${RANCHER_PROJECT}/workload/deployment:${RANCHER_NAMESPACE}:${RANCHER_WORKLOAD}"
    - MSG="开始部署 ${RANCHER_NAMESPACE}/${RANCHER_WORKLOAD} ${WEB_URL}"
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA By = $GITLAB_USER_EMAIL $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - echo $FULL_MSG
    - ydci notify popo -t $GITLAB_USER_EMAIL -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"
    - eval ydci deploy set-image $DOCKER_IMAGE_NAME -c ${RANCHER_CLUSTER} -p ${RANCHER_PROJECT_NAME} -n ${RANCHER_NAMESPACE} -w ${RANCHER_WORKLOAD}

.deploy_online_tpl: &deploy_online_def
  script:
    - WEB_URL="${RANCHER_URL}/p/${RANCHER_PROJECT}/workload/deployment:${RANCHER_NAMESPACE}:${RANCHER_WORKLOAD}"
    - MSG="开始部署convert-tracking线上环境 ${RANCHER_NAMESPACE}/${RANCHER_WORKLOAD} ${WEB_URL}"
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA By = $GITLAB_USER_EMAIL $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - POPOID=1446877
    - ydci notify group -t $POPOID  -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"
    - ydci deploy set-image $DOCKER_IMAGE_ONLINE_NAME -c ${RANCHER_CLUSTER} -p ${RANCHER_PROJECT_NAME} -n ${RANCHER_NAMESPACE} -w ${RANCHER_WORKLOAD}

.notify_tpl: &notify_def
  script:
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - POPOID=1446877
    - ydci notify group -t $POPOID  -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"

.notify_popo_tpl: &notify_popo_def
  script:
    - FULL_MSG="$MSG Branch = $CI_COMMIT_REF_NAME SHA = $CI_COMMIT_SHA By = $GITLAB_USER_EMAIL $CI_PROJECT_URL/pipelines/$CI_PIPELINE_ID"
    - echo $FULL_MSG
    - ydci notify popo -t $GITLAB_USER_EMAIL -s "$CI_PROJECT_NAME" -m "${FULL_MSG}"

# 定义阶段 stages
stages:
  - build
  - docker-build
  - deploy
  - test
  - notify

# 定义 job build 项目
job:build:
  tags:
    - k8s
  stage: build
  script:
    - echo "start build test env"
    - echo ${JAVA_HOME} ${MAVEN_HOME}
    - mvn clean $MAVEN_OPTS package -U -Ptest
    - echo "finish build test"
  cache:
    key: convert-tracking
    paths:
      - .m2/repository
  artifacts:
    paths:
      - target/
  allow_failure: false

job:build-online:
  tags:
    - k8s
  stage: build
  script:
    - echo "start build stage"
    - mvn clean $MAVEN_OPTS package -U -Ponline
    - echo "finish build stage"
  cache:
    key: convert-tracking
    paths:
      - .m2/repository
  artifacts:
    paths:
      - target/
  only:
    variables:
      - $RUN_NIGHTLY_STAGE
      - $RUN_NIGHTLY_ONLINE
  allow_failure: false

# 定义 job，构建镜像推送到测试远程
job:docker-build-test:
  tags:
    - k8s
  stage: docker-build
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  variables:
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_TEST_NAME
    ENV: 'test'
  script:
  <<: *docker_build_def
  environment:
    name: test
  dependencies:
    - job:build
  except:
    - triggers

# 定义 job，构建镜像推送到线上远程
job:docker-build-online:
  tags:
    - k8s
  stage: docker-build
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  script:
  <<: *docker_build_online_def
  only:
    variables:
      - $RUN_NIGHTLY_STAGE
      - $RUN_NIGHTLY_ONLINE
  dependencies:
    - job:build-online

job:deploy-test:
  stage: deploy
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: 'k8s-dev-common1'
    RANCHER_PROJECT: 'c-m-nh6klrxk'
    RANCHER_PROJECT_NAME: 'ad'
    RANCHER_NAMESPACE: 'ad'
    RANCHER_WORKLOAD: 'convert-tracking-test'
    DOCKER_IMAGE_NAME: $DOCKER_IMAGE_TEST_NAME
  script:
  <<: *deploy_def
  environment:
    name: test
  dependencies:
    - job:docker-build-test

# 线上环境部署
# https://rancher-new.inner.youdao.com/dashboard/c/c-m-tfstzpcf/explorer/apps.deployment/ad/convert-tracking-stage
deploy_online_stage_gz:
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  stage: deploy
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: 'k8s-prod-common1'
    RANCHER_PROJECT: 'c-m-tfstzpcf'
    RANCHER_PROJECT_NAME: 'ad'
    RANCHER_NAMESPACE: 'ad'
    RANCHER_WORKLOAD: 'convert-tracking-stage'
  script:
  <<: *deploy_online_def
  only:
    variables:
      - $RUN_NIGHTLY_STAGE
  dependencies:
    - job:docker-build-online

# 线上环境部署
# https://rancher-new.inner.youdao.com/dashboard/c/c-m-tfstzpcf/explorer/apps.deployment/ad/convert-tracking#pods
deploy_online_gz:
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  stage: deploy
  tags:
    - k8s
  variables:
    RANCHER_CLUSTER: 'k8s-prod-common1'
    RANCHER_PROJECT: 'c-m-tfstzpcf'
    RANCHER_PROJECT_NAME: 'ad'
    RANCHER_NAMESPACE: 'ad'
    RANCHER_WORKLOAD: 'convert-tracking'
  script:
  <<: *deploy_online_def
  only:
    variables:
      - $RUN_NIGHTLY_ONLINE
  dependencies:
    - job:docker-build-online

# 通知
job:notify_success:
  stage: notify
  tags:
    - k8s
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  variables:
    MSG: 'CI PIPELINE SUCCESS'
  script:
  <<: *notify_def
  only:
    - triggers

job:notify_failure:
  stage: notify
  tags:
    - k8s
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  when: on_failure
  variables:
    MSG: 'CI PIPELINE FAIL'
  script:
  <<: *notify_def
  only:
    - triggers

job:notify_popo_success:
  stage: notify
  tags:
    - k8s
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  variables:
    MSG: 'convert-tracking CI PIPELINE SUCCESS'
  script:
  <<: *notify_popo_def
  only:
    - pre
    - master
  except:
    - triggers

job:notify_popo_failure:
  stage: notify
  tags:
    - k8s
  image: harbor-registry.inner.youdao.com/devops/docker:19.03-ydci
  when: on_failure
  variables:
    MSG: 'convert-tracking CI PIPELINE FAIL'
  script:
  <<: *notify_popo_def
  except:
    - triggers
