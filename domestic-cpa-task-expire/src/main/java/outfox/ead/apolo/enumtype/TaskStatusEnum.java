package outfox.ead.apolo.enumtype;

import lombok.Getter;

@Getter
public enum TaskStatusEnum {

    ALL(0, "all", "不限"),

    PUBLISHED(10, "published", "任务已发布"),

    CANCELED(20, "canceled", "任务已取消"),

    CANCEL_EXPIRED(22, "canceledAndExpired", "任务取消后又过期的状态"),

    EXPIRED(30, "expired", "任务已过期");

    TaskStatusEnum(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    private final int code;

    private final String name;

    private final String desc;

    @Override
    public String toString() {
        return super.toString();
    }
}
