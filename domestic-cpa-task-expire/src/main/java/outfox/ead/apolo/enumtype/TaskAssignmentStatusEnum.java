package outfox.ead.apolo.enumtype;

import lombok.Getter;

@Getter
public enum TaskAssignmentStatusEnum {

    ALL(0, "all", "不限"),

    PUBLISHED(10, "published", "任务已被指派"),

    CANCELED_BY_TASK(20, "canceled", "PUBLISHED状态，在任务取消时变为CANCELED_BY_TASK"),

    CANCELED_BY_NMG(21, "canceled", "运营人员取消了任务对KOL的分配"),

    CANCELED_EXPIRED(22, "canceled", "CANCELED_BY_NMG状态,在任务过期时变为EXPIRED_CANCEL_BY_MNG"),

    CANCELED_BY_USER_STOPPED(23, "canceled", "运营人员暂停了下游账号，导致指派状态变为取消"),

    CANCELED_STOPPED_EXPIRED(24, "canceled", "CANCELED_BY_USER_STOPPED状态,在任务过期时变为当前状态"),

    EXPIRED(30, "expired", "PUBLISHED或CANCELED_BY_TASK状态，在任务过期时变为EXPIRED");


    TaskAssignmentStatusEnum(int code, String category, String desc) {
        this.code = code;
        this.name = category;
        this.desc = desc;
    }

    private final int code;

    private final String name;

    private final String desc;

    @Override
    public String toString() {
        return super.toString();
    }
}
