package com.youdao.ead.feature

import com.youdao.ead.feature.appListUtil.AppListUtil
import com.youdao.ead.feature.appListUtil.AppListUtil._
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.sql.functions._
import org.slf4j.LoggerFactory
import outfox.venus.client2.VenusClient
import outfox.venus.client2.data.{BasicData, Data, Id}
import outfox.venus.client2.util.lang.ByteUtils

import java.time.LocalDate
import java.util
import scala.collection.JavaConversions._

object AppListToVenus {
  private val logger = LoggerFactory.getLogger(AppListToVenus.getClass)

  def main(args: Array[String]): Unit = {
    implicit val spark: SparkSession = SparkSession
      .builder()
      .appName("app-batch-to-venus")
      .getOrCreate()
    val date = LocalDate.parse(AppListUtil.getTargetDate(args), dateTimeFormatter)
    // 处理用户上报的应用安装列表数据
    //dealAppList(date)
    // 处理dp上报事件中的应用安装和卸载数据
    dealCallUpEvent(date)
    dealWechatSuccess(date)
  }

  /**
   * 处理接受到的app安装列表数据，读取每个有效的设备号下的安装列表，并将数据写入venus
   *
   * @param date
   */
  def dealAppList(date: LocalDate)(implicit spark: SparkSession): Unit = {
    logger.warn(s"start dealAppList,date is $date")
    val inputHdfsPath = appInstallListHdfsPath + date.format(eadHdfsTimeFormatter) + "/*"

    val installedApp = spark.read.format("avro")
      .load(inputHdfsPath)
      .select((Seq("apps", "timestamp") ++ idCols).map(x => col(x)): _*)

    val window = Window.partitionBy("deviceId").orderBy(desc("timestamp"))


    idCols.foreach(id => {
      val deviceId2Apps = installedApp
        .withColumn("deviceId", if (id == "imei") upper(md5(col(id))) else upper(col(id)))
        .filter("deviceId is not null and deviceId != ''")
        .withColumn("timestamp", to_timestamp(col("timestamp")))
        .withColumn("rank", row_number().over(window))
        .filter(col("rank") === 1)
        .select("deviceId", "apps")
        .toDF()

      deviceId2Apps.repartition(venusClients).foreachPartition(partition => {
        implicit val venusClient: VenusClient = new VenusClient(venusAddress, venusRedisUri, venusTimeout)
        partition.foreach(row => {
          val deviceId = row.getAs[String]("deviceId")
          if (StringUtils.isNotBlank(deviceId) && !INVALID_DEVICE_ID_SEQ.contains(deviceId)) {
            // 本次上报上来的应用列表
            val curApps = row.getAs[Seq[Row]]("apps")
              .map(app => AppInfo(app.getAs[String]("package_name"), app.getAs[Int]("version_code")))
              .toSet
            val datas: util.Collection[Data] = venusClient.getAllData(deviceId, INSTALLED_APP)
            // venus中此设备已有的应用安装列表
            val oldApps = loadOldApps(datas)
            val appBehavior = generateUserAppBehavior(oldApps, curApps)
            // 保存应用安装行为
            saveUserBehavior(deviceId, appBehavior)
            // 清理venus中当前存储的应用安装信息
            clearAppField(deviceId)
            // 写入本次上报的应用安装列表
            saveToVenus(deviceId, INSTALLED_APP, curApps)
          }
        }
        )
      })
      logger.warn(s"end dealAppList, device id type is $id, device id count: ${deviceId2Apps.count()}")
    })
  }

  /**
   * 处理应用调起日志，提取出有意义的应用安装和卸载信息，写入venus对应的field
   *
   */
  private def dealCallUpEvent(date: LocalDate)(implicit spark: SparkSession): Unit = {
    //todo 目前优选app定向中会使用的设备号，因数据源不全，还有idfv，udid两种设备号未摄入
    logger.info(s"start dealCallUpEvent,date is $date")
    // 同一设备号，同一应用，最终只保留最新的有效调用状态（即明确知道此应用是否安装的状态）
    val window = Window.partitionBy("deviceId", "bundle", "deeplink_app").orderBy(desc("timestamp"))

    val brandCallUpEvents = spark.read.format("avro")
      .load(callUpInfoHdfsPath + "brand_common_track/hourly" + date.format(eadHdfsTimeFormatter) + "/*")
      .select((Seq("timestamp", "deeplink_app") ++ brandIdCols ++ callUpInstallEvents ++ callUpUninstallEvents).map(x => col(x)): _*)
      .filter("deeplink_app is not null and deeplink_app != ''")
      .filter("dp_installed + dp_success + dp_installed_fail + apk_install_complete + dp_not_install > 0")
      .withColumn("alid", lit(""))
      .withColumn("android_id", lit(""))
      .withColumn("imei_md5", lit(""))
      .withColumn("oaid_md5", lit(""))
      .withColumn("idfa_md5", lit(""))
      .withColumn("caid_md5", lit(""))
      .withColumn("android_id_md5", lit(""))
      .withColumn("bundle", lit(""))


    val yexCallUpEvents = spark.read.format("avro")
      .load(callUpInfoHdfsPath + "yex_common_track/hourly" + date.format(eadHdfsTimeFormatter) + "/*")
      .select((Seq("bundle", "timestamp", "deeplink_app") ++ yexIdCols ++ callUpInstallEvents ++ callUpUninstallEvents).map(x => col(x)): _*)
      .filter("(bundle is not null and bundle != '') or (deeplink_app is not null and deeplink_app != '')")
      .filter("dp_installed + dp_success + dp_installed_fail + apk_install_complete + dp_not_install > 0")
      .withColumnRenamed("ifa", "idfa")
      .withColumnRenamed("did", "imei")
      .withColumn("aaid", lit(""))


    val sdkCallUpEvents = spark.read.format("avro")
      .load(callUpInfoHdfsPath + "sdk_common_track/hourly" + date.format(eadHdfsTimeFormatter) + "/*")
      .select((Seq("bundle", "timestamp", "deeplink_app") ++ zhixuanIdCols ++ callUpInstallEvents ++ callUpUninstallEvents).map(x => col(x)): _*)
      .filter("(bundle is not null and bundle != '') or (deeplink_app is not null and deeplink_app != '')")
      .filter("dp_installed + dp_success + dp_installed_fail + apk_install_complete + dp_not_install > 0")
      .withColumn("oaid_md5", lit(""))
      .withColumn("idfa_md5", lit(""))
      .withColumn("caid_md5", lit(""))

    val uniteCallUpEvents = brandCallUpEvents.unionByName(yexCallUpEvents).unionByName(sdkCallUpEvents)

    Seq("imei", "oaid", "idfa", "aaid", "alid", "dimei", "caid", "android_id", "imei_md5", "oaid_md5", "idfa_md5", "caid_md5", "android_id_md5").foreach(
      id => {
        val deviceId2Events = uniteCallUpEvents
          .withColumn("deviceId", if (id == "imei") upper(md5(col(id))) else upper(col(id)))
          .withColumn("pkgName", when(col("bundle").isNull || col("bundle") === "", col("deeplink_app")).otherwise(col("bundle")))
          .filter(" deviceId is not null and deviceId != ''")
          .withColumn("timestamp", to_timestamp(col("timestamp")))
          .withColumn("rank", row_number().over(window))
          .filter(col("rank") === 1)

        val installedApps = deviceId2Events
          .filter("dp_installed + dp_success + dp_installed_fail + apk_install_complete > 0")
          .rdd
          .map(row => (row.getAs[String]("deviceId"), Set(row.getAs[String]("pkgName"))))
          .reduceByKey(_ ++ _)

        val unInstallApps = deviceId2Events.filter("dp_not_install > 0")
          .rdd
          .map(row => (row.getAs[String]("deviceId"), Set(row.getAs[String]("pkgName"))))
          .reduceByKey(_ ++ _)

      installedApps.repartition(venusClients).foreachPartition(partition => {
        implicit val venusClient: VenusClient = new VenusClient(venusAddress, venusRedisUri, venusTimeout)
        partition.foreach(info => {
          val deviceId = info._1
          val installedAppSet = info._2.map(pkgName => AppInfo(pkgName, Int.MaxValue))
          // venus中此设备已有的应用安装列表
          val datas: util.Collection[Data] = venusClient.getAllData(deviceId, INSTALLED_APP)
          val oldApps = loadOldApps(datas)
          // 将installedAppSet中包含，oldApps中不包含的应用，写入新安装应用的field
          saveToVenus(deviceId, APP_INSTALL, installedAppSet.filter(app => !oldApps.map(_.pkgName).contains(app.pkgName)))
          saveToVenus(deviceId, INSTALLED_APP, installedAppSet)
        })
      })

      unInstallApps.repartition(venusClients).foreachPartition(partition => {
        implicit val venusClient: VenusClient = new VenusClient(venusAddress,venusRedisUri, venusTimeout)
        partition.foreach(info => {
          val wechatAndroidPkgName: String = "com.tencent.mm"
          val wechatIosPkgName: String = "com.tencent.xin"
          val deviceId = info._1
          val unInstalledAppSet = info._2.map(pkgName => AppInfo(pkgName, Int.MaxValue))
          if (info._2.contains(wechatAndroidPkgName)||info._2.contains(wechatIosPkgName)) {
            unInstalledAppSet.add(AppInfo(wechatAndroidPkgName, Int.MaxValue))
            unInstalledAppSet.add(AppInfo(wechatIosPkgName, Int.MaxValue))
          }
          val datas: util.Collection[Data] = venusClient.getAllData(deviceId, INSTALLED_APP)
          // venus中此设备已有的应用安装列表
          val oldApps = loadOldApps(datas)
          // 将unInstalledAppSet中包含，oldApps中也包含的应用，写入应用卸载的field
          saveToVenus(deviceId, APP_UNINSTALL, unInstalledAppSet.filter(app => oldApps.map(_.pkgName).contains(app.pkgName)))
          clearDeviceApp(deviceId, unInstalledAppSet)
        })
      })
      logger.warn(s"end dealCallUpEvent, device id type is $id, install device id count: ${installedApps.count()}, un install device id count: ${unInstallApps.count()}")

      }
    )

  }

  private def dealWechatSuccess(date: LocalDate)(implicit spark: SparkSession): Unit = {
    logger.info(s"start dealWechatSuccess,date is $date")
    // 同一设备号，同一应用，最终只保留最新的有效调用状态（即明确知道此应用是否安装的状态）
    val window = Window.partitionBy("deviceId").orderBy(desc("timestamp"))

    val brandCallUpEvents = spark.read.format("avro")
      .load(callUpInfoHdfsPath + "brand_common_track/hourly" + date.format(eadHdfsTimeFormatter) + "/*")
      .select((Seq("timestamp") ++ brandIdCols ++ wechatSuccessEvents).map(x => col(x)): _*)
      .filter("wechat_success > 0")
      .withColumn("alid", lit(""))
      .withColumn("android_id", lit(""))
      .withColumn("imei_md5", lit(""))
      .withColumn("oaid_md5", lit(""))
      .withColumn("idfa_md5", lit(""))
      .withColumn("caid_md5", lit(""))
      .withColumn("android_id_md5", lit(""))


    val yexCallUpEvents = spark.read.format("avro")
      .load(callUpInfoHdfsPath + "yex_common_track/hourly" + date.format(eadHdfsTimeFormatter) + "/*")
      .select((Seq("timestamp") ++ yexIdCols ++ wechatSuccessEvents).map(x => col(x)): _*)
      .filter("wechat_success> 0")
      .withColumnRenamed("ifa", "idfa")
      .withColumnRenamed("did", "imei")
      .withColumn("aaid", lit(""))


    val sdkCallUpEvents = spark.read.format("avro")
      .load(callUpInfoHdfsPath + "sdk_common_track/hourly" + date.format(eadHdfsTimeFormatter) + "/*")
      .select((Seq("timestamp") ++ zhixuanIdCols ++ wechatSuccessEvents).map(x => col(x)): _*)
      .filter("wechat_success > 0")
      .withColumn("oaid_md5", lit(""))
      .withColumn("idfa_md5", lit(""))
      .withColumn("caid_md5", lit(""))

    val uniteCallUpEvents = brandCallUpEvents.unionByName(yexCallUpEvents).unionByName(sdkCallUpEvents)


    Seq("imei", "oaid", "idfa", "aaid", "alid", "dimei", "caid", "android_id", "imei_md5", "oaid_md5", "idfa_md5", "caid_md5", "android_id_md5").foreach(
      id => {
        val deviceId2Events = uniteCallUpEvents
          .withColumn("deviceId", if (id == "imei") upper(md5(col(id))) else upper(col(id)))
          .filter(" deviceId is not null and deviceId != ''")
          .withColumn("timestamp", to_timestamp(col("timestamp")))
          .withColumn("rank", row_number().over(window))

        val wechatAndroidPkgName: String = "com.tencent.mm"
        val wechatIosPkgName: String = "com.tencent.xin"

        val installedApps = deviceId2Events
          .rdd
          .map(row => (row.getAs[String]("deviceId"), Set(wechatIosPkgName, wechatAndroidPkgName)))
          .reduceByKey(_ ++ _)

        installedApps.repartition(venusClients).foreachPartition(partition => {
          implicit val venusClient: VenusClient = new VenusClient(venusAddress,venusRedisUri, venusTimeout)
          partition.foreach(info => {
            val deviceId = info._1
            val installedAppSet = info._2.map(pkgName => AppInfo(pkgName, Int.MaxValue))

            // venus中此设备已有的应用安装列表
            val datas: util.Collection[Data] = venusClient.getAllData(deviceId, INSTALLED_APP)
            val oldApps = loadOldApps(datas)
            // 将installedAppSet中包含，oldApps中不包含的应用，写入新安装应用的field
            saveToVenus(deviceId, APP_INSTALL, installedAppSet.filter(app => !oldApps.map(_.pkgName).contains(app.pkgName)))
            saveToVenus(deviceId, INSTALLED_APP, installedAppSet)
          })
        })
        logger.warn(s"end dealWechatSuccess, device id type is $id,size is ${deviceId2Events.count()}")

      }
    )

  }

  private def loadOldApps(datas: util.Collection[Data]): Seq[AppInfo] = {
    datas.map(data => {
      val versionCode = if (data.getValue == null) -1 else ByteUtils.getInt(data.getValue)
      AppInfo(data.getId.getDataId, versionCode)
    }).toSeq
  }

  /**
   * 根据本次接收到的应用安装列表，与venus现在存储的应用安装列表对比，计算出有哪些应用是新安装、新卸载、新升级的
   */
  private def generateUserAppBehavior(installAppsBefore: util.Collection[AppInfo], installAppsAfter: util.Collection[AppInfo]): UserAppBehavior = {
    val appsBeforeSet = installAppsBefore.map(_.pkgName).toSet
    val appsAfterSet = installAppsAfter.map(_.pkgName).toSet

    // 新安装的应用列表
    val newInstallApps = installAppsAfter.filter(app => !appsBeforeSet.contains(app.pkgName)).toSet
    // 新卸载的应用列表
    val uninstallApps = installAppsBefore.filter(app => !appsAfterSet.contains(app.pkgName)).toSet

    val pkgName2VersionCode = installAppsBefore.map(appInfo => (appInfo.pkgName, appInfo.versionCode)).toMap
    // 本次发现有更新的应用列表
    val upgradeApps = installAppsAfter.filter(appInfo =>
      appInfo.versionCode > pkgName2VersionCode.getOrElse(appInfo.pkgName, Int.MaxValue))
      .toSet

    UserAppBehavior(newInstallApps, uninstallApps, upgradeApps)

  }

  private def saveUserBehavior(deviceId: String, appBehavior: UserAppBehavior)(implicit venusClient: VenusClient): Unit = {
    saveToVenus(deviceId, APP_INSTALL, appBehavior.installApps)
    saveToVenus(deviceId, APP_UNINSTALL, appBehavior.uninstallApps)
    saveToVenus(deviceId, APP_UPGRADE, appBehavior.upgradeApps)
  }

  /**
   * 将用户的应用安装列表行为，写入venus
   *
   * @param deviceId
   * @param fieldId
   * @param appInfoSet
   * @param venusClient
   */
  private def saveToVenus(deviceId: String, fieldId: String, appInfoSet: Set[AppInfo])(implicit venusClient: VenusClient): Unit = {
    if (CollectionUtils.isNotEmpty(appInfoSet) && StringUtils.isNotBlank(deviceId)) {
      appInfoSet.foreach(appInfo => {
        //todo 每个data都要写入一次venus，若一个设备号对应多次写入行为，可以使用venusclient.replaceByField方法，将新旧数据merge后一次性写入
        try {
          val curData = new BasicData(new Id(deviceId, fieldId, appInfo.pkgName),
            ByteUtils.getBytes(appInfo.versionCode))
          val datas: util.Collection[Data] = venusClient.getAllData(deviceId, fieldId)
          if (!datas.exists(_.getId.getDataId == appInfo.pkgName)) {
            // 只有当venus中对应field中不存在此字段时，再写入，防止重复写入数据
            venusClient.set(curData)
            logger.warn(s"save app info to venus,device id: $deviceId, fieldId: $fieldId, data: $curData")
          }
        } catch {
          case ex: Exception =>
            logger.error(s"VenusClient set error when save app list, deviceId =  $deviceId , " +
              s"fieldId = $fieldId , app = $appInfo", ex);
        }
      })
    }
  }

  /**
   * 清空一个设备号下全部field为app的记录
   *
   */
  private def clearAppField(deviceId: String)(implicit venusClient: VenusClient): Unit = {
    try {
      venusClient.remove(deviceId.toUpperCase, INSTALLED_APP)
      logger.warn(s"clear app field from venus,device id: $deviceId")

    } catch {
      case e: Exception =>
        logger.error("Clear user installed app error: deviceId = " + deviceId, e)
    }
  }

  /**
   * 根据卸载app列表，清除deviceId下指定app的记录
   *
   * @param deviceId
   * @param uninstalledApps
   * @param venusClient
   */
  def clearDeviceApp(deviceId: String, uninstalledApps: Set[AppInfo])(implicit venusClient: VenusClient): Unit = {
    try {
      uninstalledApps.foreach(appInfo => {
        val id: Id = new Id(deviceId, INSTALLED_APP, appInfo.pkgName)
        venusClient.remove(id)
      })
      logger.warn(s"clear app field from venus,device id: $deviceId, uninstalledApps: $uninstalledApps")
    } catch {
      case e: Exception =>
        logger.error("Clear user installed app error: deviceId = " + deviceId, e)
    }
  }

  case class AppInfo(pkgName: String, versionCode: Int)


  private case class UserAppBehavior(installApps: Set[AppInfo], uninstallApps: Set[AppInfo], upgradeApps: Set[AppInfo])

}
