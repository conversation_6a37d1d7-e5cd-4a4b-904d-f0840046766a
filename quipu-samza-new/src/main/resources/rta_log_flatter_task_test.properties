job.factory.class=org.apache.samza.job.local.ThreadJobFactory
job.name=third_rta_flatter_task_test

app.name=third_rta_flatter_task_test
app.class=com.youdao.quipu.samza.app.RtaLogFlatterApplication

job.default.system=kafka
job.coordinator.system=kafka
job.coordinator.replication.factor=1
# the container thread pool will be used to run synchronous operations of each task in parallel
job.container.thread.pool.size=10
task.max.concurrency=8

task.systems=kafka
task.inputs=kafka.third_rta_bid_jar
task.opts=-Xmx2048m

##################### Checkpoints
task.checkpoint.factory=org.apache.samza.checkpoint.kafka.KafkaCheckpointManagerFactory
task.checkpoint.system=kafka
task.checkpoint.replication.factor=1

##################### Metrics
metrics.reporter.jmx.class=org.apache.samza.metrics.reporter.JmxReporterFactory
metrics.reporters=kafka-reportor,jmx
metrics.reporter.kafka-reportor.class=org.apache.samza.metrics.reporter.MetricsSnapshotReporterFactory
metrics.reporter.kafka-reportor.stream=kafka.samza_metrics
serializers.registry.metrics-serde.class=org.apache.samza.serializers.MetricsSnapshotSerdeFactory
systems.kafka.streams.samza_metrics.samza.msg.serde=metrics-serde

##################### Systems
systems.kafka.samza.factory=org.apache.samza.system.kafka.KafkaSystemFactory
systems.kafka.samza.msg.serde=byte
systems.kafka.samza.key.serde=string
systems.kafka.samza.offset.default=upcoming
systems.kafka.consumer.auto.offset.reset=smallest
systems.kafka.consumer.zookeeper.connect=ead-sandbox-kafka.inner.youdao.com:2181/kafka
systems.kafka.producer.bootstrap.servers=ead-sandbox-kafka.inner.youdao.com:9092
# Normally, we'd set this much higher, but we want things to look snappy in the demo.
systems.kafka.producer.acks=1
systems.kafka.producer.buffer.memory=134217728
systems.kafka.producer.batch.size=65536
systems.kafka.producer.request.timeout.ms=300000
#systems.kafka.producer.batch.num.messages=2000
#systems.kafka.producer.queue.buffering.max.ms=1000

## Avro codecs
avro.codec.encoder.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
avro.codec.encoder.etl.schema.registry.url=http://sandbox-schema-repo.inner.youdao.com/schema-repo
avro.codec.decoder.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
avro.codec.decoder.etl.schema.registry.url=http://sandbox-schema-repo.inner.youdao.com/schema-repo

# Serializers
serializers.registry.string.class=org.apache.samza.serializers.StringSerdeFactory
serializers.registry.byte.class=org.apache.samza.serializers.ByteSerdeFactory

