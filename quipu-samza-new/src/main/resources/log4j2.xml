<?xml version="1.0" encoding="UTF-8" ?>
<!--

 Licensed to the Apache Software Foundation (ASF) under one
 or more contributor license agreements.  See the NOTICE file
 distributed with this work for additional information
 regarding copyright ownership.  The ASF licenses this file
 to you under the Apache License, Version 2.0 (the
 "License"); you may not use this file except in compliance
 with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing,
 software distributed under the License is distributed on an
 "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied.  See the License for the
 specific language governing permissions and limitations
 under the License.

-->

<Configuration>
  <Appenders>
    <RollingFile name="RollingFile" fileName="${sys:samza.log.dir}/${sys:samza.container.name}.log"
                 filePattern="${sys:samza.log.dir}/${sys:samza.container.name}-%d{yyyy-MM-dd}-%i.log">
      <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} [%p] %m%n"/>
      <Policies>
        <SizeBasedTriggeringPolicy size="256MB" />
      </Policies>
      <DefaultRolloverStrategy max="20"/>
    </RollingFile>

    <RollingFile name="StartupAppender" fileName="${sys:samza.log.dir}/${sys:samza.container.name}-startup.log"
                 filePattern="${sys:samza.log.dir}/${sys:samza.container.name}-startup-%d{MM-dd-yyyy}-%i.log">
      <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c{1} [%p] %m%n"/>
      <Policies>
        <SizeBasedTriggeringPolicy size="256MB" />
      </Policies>
      <DefaultRolloverStrategy max="1"/>
    </RollingFile>
  </Appenders>

  <Loggers>
    <Logger name="STARTUP_LOGGER" level="info" additivity="false">
      <AppenderRef ref="StartupAppender"/>
    </Logger>
    <Root level="info">
      <AppenderRef ref="RollingFile"/>
    </Root>
  </Loggers>
</Configuration>
