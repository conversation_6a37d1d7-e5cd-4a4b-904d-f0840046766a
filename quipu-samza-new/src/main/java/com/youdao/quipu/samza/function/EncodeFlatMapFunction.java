package com.youdao.quipu.samza.function;

import com.youdao.quipu.avro.schema.ActivityItem;
import com.youdao.quipu.avro.schema.ThirdRtaBidRecord;
import com.youdao.quipu.avro.schema.ThirdRtaJar;
import com.youdao.quipu.samza.enums.AntiFraudStatEnum;
import com.youdao.quipu.samza.serde.KafkaAvroCodecs;
import com.youdao.quipu.samza.serde.KafkaAvroCoderException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.samza.config.Config;
import org.apache.samza.context.Context;
import org.apache.samza.operators.functions.FlatMapFunction;

import java.io.Serializable;
import java.util.*;

import static com.youdao.quipu.samza.app.RtaLogFlatterApplication.OUTPUT_TOPIC;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Slf4j
public class EncodeFlatMapFunction extends KafkaAvroCodecs implements FlatMapFunction<ThirdRtaJar, byte[]>, Serializable {

    @Override
    public void close() {
    }

    @Override
    public void init(Context context) {
        Config config = context.getJobContext().getConfig();

        decoderProperties = toProperties(config.subset("avro.codec.decoder."));
        encoderProperties = toProperties(config.subset("avro.codec.encoder."));
    }

    @Override
    public Collection<byte[]> apply(ThirdRtaJar message) {
        List<byte[]> flatMap = new ArrayList<>();
        try {
            if (message != null) {
                for (ActivityItem activityItem : message.getActivityItems()) {
                    ThirdRtaBidRecord.Builder builder = ThirdRtaBidRecord.newBuilder();
                    builder.setGuid(UUID.randomUUID().toString());
                    builder.setTimestamp(message.getTimestamp());
                    builder.setReqId(message.getReqId());
                    builder.setRtaName(message.getRtaName());
                    builder.setTdpPlatform(message.getTdpPlatform());
                    builder.setApiMode(message.getApiMode());
                    builder.setChannelDid(message.getChannelDid());
                    builder.setOs(message.getOs());

                    builder.setActivityId(activityItem.getActivityId());
                    builder.setSponsorId(activityItem.getSponsorId());
                    builder.setCampaignId(activityItem.getCampaignId());
                    builder.setGroupId(activityItem.getGroupId());
                    builder.setRequestCount(1 - activityItem.getFraudRtaFiltered());
                    builder.setBidCount(activityItem.getBidCount());
                    builder.setExceptionCount(message.getIsException() ? 1 : 0);
                    if (message.getFromCache()) {
                        builder.setFromCache(true);
                        builder.setCacheQueryCount(1);
                        builder.setCacheHitCount(activityItem.getBidCount());
                    }
                    builder.setSourceId(message.getSourceId());
                    updateRtaStatField(builder, activityItem);
                    try {
                        flatMap.add(getEncoder(OUTPUT_TOPIC).toBytes(builder.build()));
                    } catch (KafkaAvroCoderException e) {
                        log.error("encode avro message error, msg:{}.", message, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("flat message error, msg:{}", message, e);
        }
        return flatMap;
    }

    private void updateRtaStatField(ThirdRtaBidRecord.Builder builder, ActivityItem activityItem) {
        BitSet mark = toBitSet(activityItem.getFraudMarkItems());
        BitSet deal = toBitSet(activityItem.getFraudDealItems());
        BitSet send = toBitSet(activityItem.getFraudSendItems());
        BitSet tags = toBitSet(activityItem.getFraudTagItems());
        BitSet tagsFinal = toBitSet(activityItem.getFraudTagFinalItems());
        Set<CharSequence> tagsSet = new HashSet<>();
        Set<CharSequence> tagsFinalSet = new HashSet<>();

        for (AntiFraudStatEnum statEnum : AntiFraudStatEnum.values()) {
            if (mark.get(statEnum.getStatId())) {
                if (statEnum.getMarkSetter() != null ) {
                    statEnum.getMarkSetter().accept(builder, 1);
                }
            }
            if (deal.get(statEnum.getStatId())) {
                if (statEnum.getDealSetter() != null) {
                    statEnum.getDealSetter().accept(builder, 1);
                }
            }
            if (send.get(statEnum.getStatId())) {
                if (statEnum.getSendSetter() != null) {
                    statEnum.getSendSetter().accept(builder, 1);
                }
            }
            String name = statEnum.getName();
            if (StringUtils.isNotEmpty(name)) {
                if (tags.get(statEnum.getStatId())) {
                    tagsSet.add(name);
                }
                if (tagsFinal.get(statEnum.getStatId())) {
                    tagsFinalSet.add(name);
                }
            }
        }
        builder.setFraudMark(activityItem.getFraudMark());
        builder.setFraudMarkSend(activityItem.getFraudMarkSend());
        builder.setFraudRtaFiltered(activityItem.getFraudRtaFiltered());
        builder.setFraudTags(new ArrayList<>(tagsSet));
        builder.setFraudTagsFinal(new ArrayList<>(tagsFinalSet));
        builder.setBlankParamRtaFiltered(activityItem.getBlankParamRtaFiltered());
        builder.setModelRtaFiltered(activityItem.getModelRtaFiltered());
        builder.setSourceRtaFiltered(activityItem.getSourceRtaFiltered());
        builder.setFraudRuleId(activityItem.getFraudRuleId());
    }

    private BitSet toBitSet(List<Long> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            long[] longs = new long[list.size()];
            for (int i = 0; i < list.size(); i++) {
                longs[i] = list.get(i);
            }
            return BitSet.valueOf(longs);
        } else {
            return new BitSet();
        }
    }
}
