package com.youdao.quipu.samza.serde;

import com.linkedin.camus.coders.CamusWrapper;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageDecoder;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageEncoder;
import lombok.NonNull;
import org.apache.avro.generic.GenericData;
import org.apache.hadoop.conf.Configuration;
import org.apache.samza.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> on 15/8/23.
 * <AUTHOR>
 */
public class KafkaAvroCodecs {

    private static final int MAX_TIMES_TRY_GET_AVRO_CODER = 10;

    protected Properties decoderProperties;
    protected Properties encoderProperties;
    private final Map<String, KafkaAvroMessageDecoder> decoders = new ConcurrentHashMap<>();
    private final Map<String, KafkaAvroMessageEncoder> encoders = new ConcurrentHashMap<>();
    private final Logger logger = LoggerFactory.getLogger(KafkaAvroCodecs.class);

    /**
     * 目前主要问题是在yarn node上初始化task时出现无法访问avro schema repo的问题，导致解析数据进而丢失数据的问题。
     * 一旦启动后，decoder就会被缓存起来。
     */
    protected KafkaAvroMessageDecoder getDecoder(String topic) throws Exception {
        KafkaAvroMessageDecoder decoder;
        int tryCount = 0;

        while ((decoder = decoders.get(topic)) == null) {
            try {
                tryCount++;
                decoder = new KafkaAvroMessageDecoder();
                decoder.init(decoderProperties, topic, false);
                decoders.put(topic, decoder);
                return decoders.get(topic);
            } catch (Exception e) {
                if (tryCount >= MAX_TIMES_TRY_GET_AVRO_CODER) {
                    logger.info("finally failed to get decoder for topic {} after try {} times", topic, tryCount);
                    throw new KafkaAvroCoderException(String.format("failed to get decoder for topic %s after try %s times",
                            topic, tryCount));
                }
                // 1 min 后尝试重新获取
                try {
                    Thread.sleep(60000);
                } catch (InterruptedException interruptedException) {
                    logger.warn("thread sleep failed.");
                }
                logger.error("failed to get decoder for topic {} with after try {} times.", topic, tryCount, e);
            }
        }

        decoder = decoders.get(topic);
        if (decoder != null) {
            return decoder;
        } else {
            throw new KafkaAvroCoderException(String.format("failed to get decoder for topic %s", topic));
        }
    }

    protected KafkaAvroMessageEncoder getEncoder(String topic) throws KafkaAvroCoderException {
        KafkaAvroMessageEncoder encoder;
        int tryCount = 0;

        while ((encoder = encoders.get(topic)) == null) {
            try {
                tryCount++;
                encoder = new KafkaAvroMessageEncoder(topic, new Configuration());
                encoder.init(encoderProperties, topic);
                encoders.put(topic, encoder);
                return encoders.get(topic);
            } catch (Exception e) {
                if (tryCount >= MAX_TIMES_TRY_GET_AVRO_CODER) {
                    logger.info("finally failed to get encoder for topic {} after try {} times", topic, tryCount);
                    throw new KafkaAvroCoderException(String.format("failed to get encoder for topic %s after try %s times",
                            topic, tryCount));
                }
                // 1 min 后尝试重新获取
                try {
                    Thread.sleep(60000);
                } catch (InterruptedException interruptedException) {
                    logger.warn("thread sleep failed.");
                }
                logger.error("failed to get encoder for topic {} with after try {} times.", topic, tryCount, e);
            }
        }

        encoder = encoders.get(topic);
        if (encoder != null) {
            return encoder;
        } else {
            throw new KafkaAvroCoderException(String.format("failed to get decoder for topic %s", topic));
        }
    }

    public CamusWrapper<GenericData.Record> decode(@NonNull String kafkaTopic, @NonNull byte[] message) throws Exception {
        return getDecoder(kafkaTopic).decode(message);
    }

    protected Properties toProperties(Config config) {
        Properties props = new Properties();
        for (Map.Entry<String, String> entry : config.entrySet()) {
            props.put(entry.getKey(), entry.getValue());
        }
        return props;
    }
}
