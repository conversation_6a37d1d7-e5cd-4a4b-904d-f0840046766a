package com.youdao.quipu.samza.function;

import com.youdao.quipu.avro.schema.ThirdRtaJar;
import com.youdao.quipu.samza.serde.KafkaAvroCodecs;
import lombok.extern.slf4j.Slf4j;
import org.apache.samza.config.Config;
import org.apache.samza.context.Context;
import org.apache.samza.operators.functions.MapFunction;

import java.io.Serializable;

import static com.youdao.quipu.samza.app.RtaLogFlatterApplication.INPUT_TOPIC;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Slf4j
public class DecodeMapFunction extends KafkaAvroCodecs implements MapFunction<byte[], ThirdRtaJar>, Serializable {

    @Override
    public ThirdRtaJar apply(byte[] message) {
        try {
            return getDecoder(INPUT_TOPIC).decode(ThirdRtaJar.getClassSchema(), message);
        } catch (Exception e) {
            log.error("decode avro msg error.", e);
            return null;
        }
    }

    @Override
    public void close() {

    }

    @Override
    public void init(Context context) {
        Config config = context.getJobContext().getConfig();

        decoderProperties = toProperties(config.subset("avro.codec.decoder."));
        encoderProperties = toProperties(config.subset("avro.codec.encoder."));
    }
}
