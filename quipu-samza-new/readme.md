## 服务介绍
- 升级samza version至1.8, 使用新版samza的high level api
- task list
   - RtaLogFlattenApplication：将third_rta_bid_jar 消息转换并输出到 third_rta_bid topic 
    
## 编译打包
```
env JAVA_HOME=/usr/local/youdao/ad/jdk1.8.0_202 mvn clean package -pl  quipu-samza-new -am

```
## 上传tar包到hdfs
```
/disk2/eadata/hadoop-2.7.4/bin/hadoop fs -put -f target/quipu-samza-new-A.B.C-dist.tar.gz /user/eadata/samza/
```

## 运行
```
cd quipu-samza-new
rm -rf deploy
mkdir deploy
cp target/quipu-samza-new-A.B.C-dist.tar.gz ./
tar -zxvf quipu-samza-new-A.B.C-dist.tar.gz -C deploy
cd deploy

env HADOOP_CONF_DIR=/mfs_ead/eadata/online/hadoop-conf PATH=/usr/local/youdao/ad/jdk1.8.0_202/:$PATH JAVA_HOME=/usr/local/youdao/ad/jdk1.8.0_202 bin/run-app.sh --config-path=config/rta_log_flatter_task.properties
```