<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>apolo</artifactId>
        <groupId>outfox.ead</groupId>
        <version>0.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ivt-traffic-processor</artifactId>

    <properties>
        <scopt.version>3.5.0</scopt.version>
        <spark.version>2.4.5</spark.version>
        <scala.version>2.11</scala.version>
        <spark.avro.version>2.4.0</spark.avro.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
            <version>1.3.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_${scala.version}</artifactId>
            <version>${spark.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql_${scala.version}</artifactId>
            <version>${spark.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.scopt</groupId>
            <artifactId>scopt_${scala.version}</artifactId>
            <version>${scopt.version}</version>
        </dependency>
        <dependency>
            <groupId>com.typesafe.scala-logging</groupId>
            <artifactId>scala-logging_${scala.version}</artifactId>
            <version>3.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.youdao.ead</groupId>
            <artifactId>venus2-client</artifactId>
            <version>4.5.0</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.11</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>org.uaparser</groupId>
            <artifactId>uap-scala_2.11</artifactId>
            <version>0.11.0</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>

            <dependency>
                <artifactId>log4j</artifactId>
                <groupId>log4j</groupId>
                <version>1.2.17</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <artifactId>slf4j-log4j12</artifactId>
                <groupId>org.slf4j</groupId>
                <version>1.7.16</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <artifactId>slf4j-api</artifactId>
                <groupId>org.slf4j</groupId>
                <version>1.7.16</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>2.12.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>zookeeper</artifactId>
                        <groupId>org.apache.zookeeper</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>2.12.0</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <repositories>
        <repository>
            <id>ead</id>
            <url>https://nexus.corp.youdao.com/nexus/content/repositories/ead/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>ead_snapshot</id>
            <url>https://nexus.corp.youdao.com/nexus/content/repositories/ead_snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <finalName>ivt-traffic-processor-spark</finalName>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>3.2.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <skipTests>false</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <relocations>
                                <relocation>
                                    <pattern>org.apache.commons.io</pattern>
                                    <shadedPattern>youdao.org.apache.commons.io</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>io.netty</pattern>
                                    <shadedPattern>youdao.io.netty</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.apache.curator</pattern>
                                    <shadedPattern>youdao.org.apache.curator</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>com.google.common</pattern>
                                    <shadedPattern>youdao.com.google.common</shadedPattern>
                                </relocation>
                            </relocations>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/maven/**</exclude>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>