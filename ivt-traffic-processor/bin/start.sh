#!/bin/bash
source ./bin/env.sh

process_args() {
  while [[ $# -gt 0 ]]; do
    case "$1" in
    --tasks) confOpt="$confOpt $1 $2" && shift && shift ;;
    --startDate) confOpt="$confOpt $1 $2" && shift && shift ;;
    --endDate) confOpt="$confOpt $1 $2" && shift && shift ;;
    --hours) confOpt="$confOpt $1 $2" && shift && shift ;;
    --writeMode) confOpt="$confOpt $1 $2" && shift && shift ;;
    --help | -h) usage && shift ;;
    *) shift ;;
    esac
  done
  if [[ ! "$confOpt" =~ "tasks" ]]; then
    confOpt="$confOpt --tasks req_slot_stat,req_stat_10m,ip_10m_impr,impr_stat_10m,ip_10m_click,click_stat_10m,impr_slot_stat,click_slot_stat,impr_click_diff"
  fi
  if [[ ! "$confOpt" =~ "startDate" ]]; then
    confOpt="$confOpt --startDate $(date -d "1 days ago" +%Y-%m-%d)"
  fi
  if [[ ! "$confOpt" =~ "endDate" ]]; then
    confOpt="$confOpt --endDate $(date +%Y-%m-%d)"
  fi
   if [[ ! "$confOpt" =~ "writeMode" ]]; then
    confOpt="$confOpt --writeMode all"
  fi

}

usage() {
  cat <<EOM
Usage: test.sh [options]

  --startTime <yyyy/MM/dd/HH> : set start time of the task data, include
  --endTime <yyyy/MM/dd/HH> : set end time of the task data, exclude
EOM
}

process_args "$@"

$SPARK_HOME/bin/spark-submit \
  --class com.youdao.ead.ivt.MainApplication \
  --master yarn \
  --deploy-mode client \
  --driver-memory 8G \
  --executor-memory 16G \
  --executor-cores 5 \
  --num-executors 40 \
  --packages org.apache.spark:spark-avro_2.11:2.4.0 \
  --repositories https://nexus.corp.youdao.com/nexus/content/groups/public \
  --conf spark.network.timeout=600 \
  --conf spark.yarn.am.memory=2048 \
  --conf spark.executor.heartbeatInterval=60 \
  --conf spark.sql.shuffle.partitions=1000 \
  --conf spark.hadoop.yarn.timeline-service.enabled=false \
  --conf spark.executor.extraJavaOptions='-XX:+PrintGCDetails -XX:+HeapDumpOnOutOfMemoryError' \
  $(pwd)/target/ivt-traffic-processor-spark.jar ${confOpt} 2>&1
