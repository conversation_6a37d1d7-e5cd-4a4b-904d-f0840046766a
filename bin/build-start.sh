#!/usr/bin/env bash

echo '#########配置jdk8##############'
JAVA_HOME=/usr/local/youdao/ad/jdk1.8.0_202
MAVEN_HOME=/usr/local/youdao/ad/apache-maven-3.5.4
echo $JAVA_HOME
echo $MAVEN_HOME
export PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$PATH

if [[ $1 == 'online' ]] ; then
    mvn clean package -Ponline
elif [[ $1 == 'test' ]] ; then
    mvn clean package -Ptest
else
    echo '请选择测试环境或线上环境'
    exit
fi

JAVA_OPTS="-Dserver=converttracking -server -Xms2g -Xmx4g -XX:+UnlockExperimentalVMOptions -XX:+UseG1GC
                        -XX:MaxGCPauseMillis=100  -XX:+AlwaysPreTouch -XX:+UseStringDeduplication -XX:-ResizePLAB
                        -XX:+ParallelRefProcEnabled -XX:-ReduceInitialCardMarks -XX:-UseBiasedLocking
                        -verbose:gc -XX:+PrintGCDetails -XX:+PrintAdaptiveSizePolicy -XX:+PrintTenuringDistribution
                        -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=3
                        -XX:GCLogFileSize=200M -XX:+PrintGCDateStamps -Xloggc:logs/gc.log.%t
                        -XX:-OmitStackTraceInFastThrow

                        -Dcom.sun.management.jmxremote.port=18092 -Dcom.sun.management.jmxremote.authenticate=false
                        -Dcom.sun.management.jmxremote.ssl=false"

if [[ $1 == 'test' ]] ; then
   JAVA_OPTS="$JAVA_OPTS -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8013"
fi

setsid nohup $JAVA_HOME/bin/java $JAVA_OPTS -jar target/convert-tracking.jar 2>&1 >log &
pid=$!
echo $pid > convert-tracking.pid