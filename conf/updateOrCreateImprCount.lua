-- Interface to handle data.
-- @param KEYS[1] deviceId
-- @param ARGV[1] funcName GET or UPDATE
-- @param ARGV[2] adPlanId
-- @param ARGV[3] windowIndex
-- @param ARGV[4] frequency type 0-NONE 1-DAY 2-WEEK 3-ALL
-- @param ARGV[5] order start date format yyyyMMdd
-- @param ARGV[6] currentTimestamp

-- 联合频控
-- @param ARGV[7] unionFrequencyRuleId (u_前缀+unionFrequencyRuleId e.g. u_123213)
-- @param ARGV[8] windowIndex_UnionFrequencyRule
-- @param ARGV[9] frequencyType_UnionFrequencyRule 0-NONE 1-DAY 2-WEEK 3-ALL
-- @param ARGV[10] startDate_UnionFrequencyRule format yyyyMMdd

-- @version 1.0.1


-- 只变更推广计划频控
local function createOrUpdate4AdPlan()
    local oldRecords = redis.call('hget', KEYS[1], ARGV[2]);
    local imprObject4AdPlan = {}

    if oldRecords then
        imprObject4AdPlan = cmsgpack.unpack(oldRecords)
    else
        imprObject4AdPlan = {
            type = ARGV[4],
            startDate = ARGV[5],
        }
    end

    if imprObject4AdPlan[ARGV[3]] then
        imprObject4AdPlan[ARGV[3]] = imprObject4AdPlan[ARGV[3]] + 1
    else
        imprObject4AdPlan[ARGV[3]] = 1
    end

    imprObject4AdPlan['updateTime'] = ARGV[6]

    redis.call('hset', KEYS[1], ARGV[2], cmsgpack.pack(imprObject4AdPlan))
end


-- 只变更联合频控
local function createOrUpdate4UnionFrequencyRule()
    local oldRecords = redis.call('hget', KEYS[1], ARGV[7]);
    local imprObject4UnionFrequencyRule = {}

    if oldRecords then
        imprObject4UnionFrequencyRule = cmsgpack.unpack(oldRecords)
    else
        imprObject4UnionFrequencyRule = {
            type = ARGV[9],
            startDate = ARGV[10],
        }
    end

    if imprObject4UnionFrequencyRule[ARGV[8]] then
        imprObject4UnionFrequencyRule[ARGV[8]] = imprObject4UnionFrequencyRule[ARGV[8]] + 1
    else
        imprObject4UnionFrequencyRule[ARGV[8]] = 1
    end

    imprObject4UnionFrequencyRule['updateTime'] = ARGV[6]

    redis.call('hset', KEYS[1], ARGV[7], cmsgpack.pack(imprObject4UnionFrequencyRule))
end


-- 推广计划 & 联合频控 都变更
local function createOrUpdate4All()
    local oldRecords = redis.call('hmget', KEYS[1], ARGV[2], ARGV[7]);
    local imprObject4AdPlan = {}
    local imprObject4UnionFrequencyRule = {}

    -- 推广计划频控
    if oldRecords[1] then
        imprObject4AdPlan = cmsgpack.unpack(oldRecords[1])
    else
        imprObject4AdPlan = {
            type = ARGV[4],
            startDate = ARGV[5],
        }
    end

    if imprObject4AdPlan[ARGV[3]] then
        imprObject4AdPlan[ARGV[3]] = imprObject4AdPlan[ARGV[3]] + 1
    else
        imprObject4AdPlan[ARGV[3]] = 1
    end

    imprObject4AdPlan['updateTime'] = ARGV[6]


    -- 联合频控
    if oldRecords[2] then
        imprObject4UnionFrequencyRule = cmsgpack.unpack(oldRecords[2])
    else
        imprObject4UnionFrequencyRule = {
            type = ARGV[9],
            startDate = ARGV[10],
        }
    end

    if imprObject4UnionFrequencyRule[ARGV[8]] then
        imprObject4UnionFrequencyRule[ARGV[8]] = imprObject4UnionFrequencyRule[ARGV[8]] + 1
    else
        imprObject4UnionFrequencyRule[ARGV[8]] = 1
    end

    imprObject4UnionFrequencyRule['updateTime'] = ARGV[6]

    redis.call('hset', KEYS[1], ARGV[2], cmsgpack.pack(imprObject4AdPlan), ARGV[7], cmsgpack.pack(imprObject4UnionFrequencyRule))
end


local function getWithIndex()
    local record = redis.call('hget', KEYS[1], ARGV[2]);
    if record then
        local imprObject = cmsgpack.unpack(record)
        if imprObject[ARGV[3]] then
            return imprObject[ARGV[3]]
        end
    end
    return 0
end


-- call --
if ARGV[1] == 'GET' then
    return getWithIndex()
else
    if ARGV[1] == 'UPDATE_ALL' then
        createOrUpdate4All()
    elseif ARGV[1] == 'UPDATE_AD_PLAN' then
        createOrUpdate4AdPlan()
    elseif ARGV[1] == 'UPDATE_UNION_FREQUENCY_RULE' then
        createOrUpdate4UnionFrequencyRule()
    else
        return { err='Invalid operation: '..ARGV[1] }
    end
end



