###############################################################################
# SDK广告归属AD_EXCHANGE
platform_name=ad_exchange

# 数据库的驱动
sqldriver=com.mysql.jdbc.Driver
sdk_data_db=***************************************************************************************************
sdk_user_profile_db=***************************************************************************************************************
youxuan_db=*****************************************************************************************************************************************************************************************************************************************************************************

deeplink_app_head_conf=/deeplink_app_head_conf

# producer server
producer_port=1040

# noticer
database_delay=10

# DSL
cache_port=2518
zookeeper_hosts=ead-zookeeper4.inner.youdao.com:2181,ead-zookeeper5.inner.youdao.com:2181,ead-zookeeper6.inner.youdao.com:2181


#impressive server host
multiImpr.imprServerHost=http://dsp-impr2.youdao.com/k.gif
multiImpr.yexImprServer=http://log.yex.youdao.com/it
multiImpr.consumerCount=4
multiImpr.totalMaxConnCount=1024
multiImpr.perRouteMaxConnCount=512
multiImpr.connectTimeout=10000
multiImpr.socketTimeout=10000

#词典品牌广告
adbrand_db_url=*******************************************************************************
adbrand_db_username=ead
adbrand_db_password=ea89,d24

#品牌广告使用的点击服务器
brand_click_web_url_https=https://b-trackservice.youdao.com/clk/request.s
brand_click_web_url=http://b-trackservice.youdao.com/clk/request.s

# 品牌广告排期时间范围，单位：天
adbrand_schedule=2

#save apps to venus switch
save_apps_to_venus_on=true

#file location of ad provider configuration
ad_provider_config=/mfs_ead/global/exec/ead/dsp/gorgon/providerV3.xml

# yex host
exchange.yex.bid.url=https://test-yex-log.youdao.com/imp/request.s
exchange.yex-vender.bid.url=http://test-yex-vender.inner.youdao.com/imp/request.s
# since only request to exchange.yex.bid.url so far, maxPerRoute=maxTotal.
exchange.maxPerRoute=600
exchange.maxTotal=1200
exchange.connectionRequestTimeout=1
exchange.connectTimeout=250
exchange.socketTimeout=330
#will set into bidRequest
exchange.maxBidTimeout=200
exchange.maxRequestTimeout=330

#落地页跟踪配置信息
dest_link_trace_config_path=/mfs_ead/ead/dsp/gorgon/online/dest_link_trace.json
dest_link_trace_config_watch_delay=60000

#video callback
#从连接池中获取连接的超时时间，超过该时间未拿到可用连接，
#会抛出org.apache.http.conn.ConnectionPoolTimeoutException: Timeout waiting for connection from pool
video.callback.connectionRequestTimeout=5000
#等待与服务器建立连接的时间,超出该时间抛出connect timeout
video.callback.connectTimeout=1000
#与服务器的连接建立了，等待服务器发送数据的时间,超过该时间抛出read timeout
video.callback.socketTimeout=1000

#视频回调日志发送到kafka的topic
quipu.topic.videoCallback=video_callback_sdk

#按照广告位udid、app id和平台类型发送报错信息到kafka的topic
quipu.topic.abnormalSdkAdRequest=abnormal_sdk_request

#词典的易投ssp广告的kafka的topic
quipu.topic.sspPv=pv_eadd

#品牌广告展示数据发往kafka的topic
quipu.topic.brandImpr=brand_impr
quipu.topic.brandImprV2=brand_impr_v2
quipu.topic.brandPv=brand_pv
quipu.topic.brandBid=brand_bid

#响应时间 tracker
rsptime.tracker.adload=http://dsp-impr2.youdao.com/adload.s
rsptime.tracker.adready=http://dsp-impr2.youdao.com/adready.s
rsptime.tracker.brand.valid.load=http://test-impr.youdao.com/brand/adValidLoad.s

common_trackers_domain_http=http://test-impr.youdao.com
common_trackers_domain_https=https://test-impr.youdao.com
zhixuan_common_trackers=/zhixuan/common-tracker.s
brand_common_trackers=/brand/common-tracker.s
yex_common_trackers=/yex/common-tracker.s


#联盟站点数据库(for eadm)
union_site_db_2=****************************************************************************************************

######################### add with EADD ##########################################
#联盟站点数据库(for eadd)
union_site_db=****************************************************************************************************
kafka.topic.pv.log.eadd=pv_eadd
#底部通栏效果广告对应的url
dict_bottom_cpc_url=http://gorgon.youdao.com/gorgon/eadd/dict_bottom/adCpc.html
#resin端公益广告监控
resin_universal_ad_monitor_key=impr.dict.universal_count
channel_type=eadd
#tanx API domain
tanx_api_url=http://ope.tanx.com/api
#hy API domain
hy_api_url=http://pmprtb-youdao.gentags.net/youdao/bid
hy_is_test=false
#pinyou API domain
pinyou_api_url=http://i.ipinyou.com/163yd/bid
pinyou_is_test=false
#MadHouse API domain
madhouse_api_domain=http://ad.youdao.madserving.com/adcall/bidrequest?

#品牌广告小图对应的url
brand_small_img_url=http://gorgon.youdao.com/gorgon/eadd/dict_bottom/adBrandMin.html
#品牌广告大图对应的url
brand_big_img_url=http://gorgon.youdao.com/gorgon/eadd/dict_bottom/adBrandMax.html
#品牌广告大图展示反馈服务器
brand_feedback_url=http://gorgon.youdao.com/gorgon/eadd/request.s

click_web_url=https://d-clkservice.youdao.com/clk/request.s
###############################################################################

# gorgon encourage to set configuration into zookeeper
zk_config_connection_string=ad1-test-01.zookeeper.yodao.cn:2181,ad1-test-02.zookeeper.yodao.cn,ad1-test-03.zookeeper.yodao.cn
zk_config_connection_timeout_ms=10000
zk_config_session_timeout_ms=10000
#当前服务配置在zk上的根目录
zk_config_base_namespace=dsp/gorgon
zk_config_path_dispatch_setting=/config/dispatch_config_tcpcopy
zk_config_path_bs_max_process_time=/config/bid_server_max_process_time_ms
zk_config_path_bid_timeout=/config/bid_timeout_ms
# gorgon实例注册信息
zk_config_path_gorgon_instance=/config/instance
# bid client 相关配置
zk_config_path_bid_client_configs=/config/bid_client_configs

# elements下videourl和coverimage的id，用于判断信息流视频
elements.videourl.id=882
elements.coverimage.id=886
elements.ctaText.id=10

# 信息流视频impr(impplay2)，有别于激励视频的impr
impr_url_video_play=http://dsp-impr2.youdao.com/impplay2.s
impr_url_video_play_https=https://dsp-impr2.youdao.com/impplay2.s

#信息流视频广告位(非轮播)，用于接入品牌投放系统
VIDEO_IDS=c5d21cd70963cdf9789d0e644ee9cc1a:eaa18376753d18c11bac6ed396221ba8:3260d7f4f587fa7c328761ef75302609:d621763552cf52e370d1828897b3d65c
#信息流视频广告位（批量位置）
MUTLI_VIDEO_IDS=5c870d2c4e32bad31165eaed0b2289da
#词典开屏广告位（双端）
DICT_OPEN_SCREEN_IDS=cb6d26a7758272c37a5f60b19b06a295,46dc09666522cc68bf389bebbea444c3

# venus config
venus_timeout=1000
# zk on infra cloud.
# See: https://infracloud.inner.youdao.com/workbench/project/130/zookeeper
venus_zk_address=venus-test-zk.corp.youdao.com:2181
venus_redis_uri=redis://<EMAIL>:6379

venus_read_timeout=30
create_venus_client_read_only=false
vd-device-redis-uri=redis://<EMAIL>:6379
vd-device-redis-timeout-millis=30
vd-device-frequency-max-times=1
vd-device-frequency-prefix=test
vd-device-expire-days=60

# yex impr server host
yex_impr_url=http://log.yex.youdao.com/it
yex_impr_url_https=https://log-yex.youdao.com/it

# yex click server host
yex_click_server_url=http://log.yex.youdao.com/ct
yex_click_server_url_https=https://log-yex.youdao.com/ct

# apple product mapping
apple_product_mapping_path=/mfs_ead/ead/dsp/gorgon/online/apple_product_mapping.txt
# android product mapping
android_product_mapping_path=/mfs_ead/ead/dsp/bs/online/android_model_brand.txt

# druid info
druid_addr=http://eadata-druid-broker.corp.youdao.com/druid/v2?pretty
druid_max_wait_timeout=180000
druid_conn_request_timeout=10000
druid_conn_timeout=10000
druid_socket_timeout=150000
druid_context_timeout=150000

composite_link_schema_config_path=/mfs_ead/ead/dsp/bs/online/composite_link_schema.json
composite_link_schema_config_check_delay = 60000

# SDK广告调起APK和APK下载结果上报地址,如果存在多个，用英文逗号隔开即可
invoke_trackers=http://dsp-impr2.youdao.com/invoke.s
invoke_trackers_https=https://dsp-impr2.youdao.com/invoke.s
apk_download_trackers=http://dsp-impr2.youdao.com/apkdl.s
apk_download_trackers_https=https://dsp-impr2.youdao.com/apkdl.s

# cookie mapping 回调地址
cookie_mapping_addr=http://yex.youdao.com/cm?
cookie_mapping_addr_https=https://yex.youdao.com/cm?

# central_dogma 相关配置
central_dogma_hosts=central-dogma1-inner.yodao.com,central-dogma2-inner.yodao.com,central-dogma3-inner.yodao.com
central_dogma_port=36462
central_dogma_gorgon_project=gorgon
central_dogma_gorgon_repo=online

should_log_cd_conf=/should_log_conf

# 广告样式优先级配置
style_ranking_mask_conf=/style-weight-map

# 安卓应用市场urlSchema
android_market_cd_conf=/android_market_url_schema

# 品牌广告补量配置
brand_supplement_slot_conf=/brand-supplement-slot-conf

# eadd广告位映射配置
eadd_sdk_slot_map_cd_conf=/eadd_sdk_slot_map

# 品牌广告指定dsp配置
mark_to_brand_dsp_map_cd_conf=/brand_dsp_names

# 广告是否允许全屏跳转
should_fullScreen_jump_conf=/should_fullScreen_jump

# 是否开屏广告位
splash_slot_conf=/splash_slot.json

ab_test_block_conf=/ab_test_block.json

# 品牌广告全局配置参数
brand_global_cd_conf=/brand_global_config

# eadd广告位映射配置
eadd_youxuan_slot_map_cd_conf=/eadd_youxuan_slot_map

# 优选合并广告位相关配置
youxuan_merge_position_cd_conf=/youxuan_merge_position

# 优选广告生产配置
youxuan_brand_producer_cd_conf=/youxuan_brand_producer

sdk_app_id_wechat_id_map_conf=/sdk_app_id_wechat_id_map

# 扩量设备号开关配置
vd-device-cd-conf=/vd_device_config

info_stream_video_element_id_list_conf=/info_stream_video_element_id_list

video_size_param_slots_conf=/video_size_param_slot_v2

# 需要过滤的无效设备ID
invalid_device_id_list_conf=/invalid_device_id_list

# 需要为panda过滤的有道词典的广告位和样式
panda_ad_filter_conf=/panda_ad_filter

brand_model_filter_conf=/brand_model_filter
schedule_brand_conf=/schedule_brand_conf.json
brand_realtime_slot_blacklist_conf=/brand_realtime_slot_blacklist_conf
yex_click_interact_conf=/yex_click_interact_conf
brand_ad_repairer_conf=/brand_ad_repairer.json
yex_long_timeout_conf=/yex_long_timeout_conf.json
vender_ad_conf=/vender_ad_conf.json

company_ip_conf=/company_ip_list
trace_conf=/trace_conf.json
brand_direct_test_device_conf=/brand_direct_test_device_conf.json
default_click_type_conf=/default_click_type_conf.json
hy_dmp_feature_conf=/hy_dmp_feature_conf.json
ydid_mapping_source_conf=/ydid_mapping_source_conf.json
sentinel_conf=/sentinel_config.json

tracker_unique_report_conf=/tracker_unique_report.json

brand_impr_url_http=http://b-trackservice.youdao.com/brand/it.s
brand_impr_url_https=https://b-trackservice.youdao.com/brand/it.s
BRAND_IMPR_REDIS_ADDRESS=196975.redis.baichuan.ynode.cn:6379
BRAND_IMPR_REDIS_PASSWORD=zX0Zsvzp
BRAND_IMPR_REDIS_SCRIPT_PATH=/updateOrCreateImprCount.lua
BRAND_IMPR_REDIS_TIMEOUT=30

second_brand_nomination_timeout_ms=220

ip_black_list_conf=/mfs_ead/global/ead/yex/ip-blacklist.properties
ip_black_list_local_conf=/mfs_ead/global/ead/gorgon/ip-blacklist.properties
ip_black_list_conf_watch_delay=600000

trace-dir=/mfs_ead/global/exec/ead/dsp/trace/logs/gorgon
dict_age_distributed_file=dict_age_distributed.csv


wechat_transit_dp_head=yddict://youdao.com/jumpWxMiniProgram
wechat_transit_h5_url=https://youdaoad-6gphtwkq46404f5d-1300062662.tcloudbaseapp.com/
wechat_transit_h5_path=/pages/redirect_third_party/index

s3.accessKey=2627af6f-2822-927c-6fc8-d4a33890c1d2
s3.secretKey=NlIRzRxDtQIbvrhSZeiPm2ws8atefJCN
s3.entpoint=https://yds3-infra-arch.inner.youdao.com
s3.bucketName=givt

environment=test
