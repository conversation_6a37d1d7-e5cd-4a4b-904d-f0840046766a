<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- ############ 工具bean BEGIN##################### -->

    <!-- 点击加密 -->
    <bean id="brandClickTrackEncoder" class="outfox.ead.brand.util.encoder.BrandClickTrackEncoder">
        <property name="aesCipher" ref="aesCipher"/>
    </bean>

    <!-- 加密算法  -->
    <bean id="aesCipher" class="outfox.ead.brand.util.encoder.AESCipher">
        <constructor-arg>
            <value>/mfs_ead/global/exec/ead/dsp/gorgon/ead-aes-key.1195118241296</value>
        </constructor-arg>
    </bean>

    <!-- 用于创建AdRequest -->
    <!-- Note: pvLoggerProcessor should be the last of list: adRequestPostProcessors -->
    <bean id="dictAdRequestFactory" class="outfox.ead.brand.service.AdRequestFactoryImpl">
        <property name="adRequestProcessor" ref="adRequestProcessor" />
        <property name="adRequestPostProcessors">
            <list>
                <ref bean="refererOfRefererExtractionProcessor" />
                <ref bean="userInformationExtractionProcessor" />
            </list>
        </property>
    </bean>

    <bean id="dictHandlerProvider" class="outfox.ead.brand.service.DictHandlerProvider" autowire="byName">
        <property name="dictAdPositionInfoProvider" ref="dictAdPositionInfoProvider"/>
    </bean>

    <bean id="dictAdPositionInfoProvider" class="outfox.ead.brand.service.DictAdPositionInfoProviderImp"  autowire="byName" init-method="init">
        <property name="dataSource" ref="unionDataSource"/>
    </bean>

    <bean id="unionDataSource" class="com.zaxxer.hikari.HikariDataSource">
        <constructor-arg type="com.zaxxer.hikari.HikariConfig">
            <bean class="com.zaxxer.hikari.HikariConfig">
                <property name="driverClassName" value="com.mysql.jdbc.Driver" />
                <property name="jdbcUrl" value="${union_site_db}"/>
                <property name="maximumPoolSize" value="10"/>
                <property name="minimumIdle" value="2"/>
                <property name="connectionTimeout" value="30000"/>
				<property name="idleTimeout" value="30000"/>
                <!--7.5小时生命周期，因为mysql的生命周期是8小时-->
                <property name="maxLifetime" value="27000000"/>
                <property name="metricRegistry" ref="metricRegistry"/>
                <property name="poolName" value="HikariPool-unionDataSource"/>
                <property name="dataSourceProperties">
                    <props>
                        <prop key="cachePrepStmts">true</prop>
                        <prop key="prepStmtCacheSize">250</prop>
                        <prop key="prepStmtCacheSqlLimit">2048</prop>
                        <prop key="useServerPrepStmts">true</prop>
                        <prop key="useLocalSessionState">true</prop>
                        <prop key="rewriteBatchedStatements">true</prop>
                        <prop key="cacheResultSetMetadata">true</prop>
                        <prop key="elideSetAutoCommits">true</prop>
                        <prop key="maintainTimeStats">false</prop>
                        <!--why set these? https://github.com/brettwooldridge/HikariCP/wiki/MySQL-Configuration-->
                    </props>
                </property>
            </bean>
        </constructor-arg>
    </bean>

    <bean id="metricRegistry" class="com.codahale.metrics.SharedMetricRegistries" factory-method="getOrCreate">
        <constructor-arg value="gorgon-metric-registry" />
    </bean>

    <!-- resin端渲染使用的renderer -->
    <bean id="dictRenderer" class="outfox.ead.brand.util.render.DictMultRenderer"
          init-method="init" lazy-init="false" autowire="byName">
        <property name="dictAdPositionInfoProvider" ref="dictAdPositionInfoProvider"/>
        <property name="dictBottomCpcAdUrl" value="${dict_bottom_cpc_url}"/>
        <property name="univMqKey" value="${resin_universal_ad_monitor_key}"/>
        <property name="monitorUnivAd" value="true"/>
    </bean>

    <!-- 修改生成的点击服务器的前缀为branding_click_web_url -->
    <bean id="brandLinkCreator" class="outfox.ead.brand.processor.BrandLinkCreator">
        <property name="clickServerUrl" value="${brand_click_web_url_https}"/>
        <property name="encoder" ref="brandClickTrackEncoder"/>
    </bean>

    <bean id="universalAdvsReadProxy"/>
    <!-- 品牌广告的日志单独记录到另一个文件里，和原有stat.log分开 -->
    <bean id="brandImprLogger" class="outfox.ead.brand.util.log.BrandImprLogger">
        <property name="brandImprStatService" ref="brandImprStatService"/>
    </bean>

    <bean id="brandImprStatService" class="outfox.ead.brand.service.BrandImprStatServiceImpl">
        <property name="brandImprTopic" value="${quipu.topic.brandImpr}"/>
        <property name="brandImprTopicV2" value="${quipu.topic.brandImprV2}"/>
    </bean>


    <!-- ############ 工具bean END ##################### -->

    <!-- ############ handler BEGIN ##################### -->
    <!-- 词典品牌广告处理单元 -->
    <bean id="brandAdRequestHandler" class="outfox.ead.brand.handler.DictBrandAdRequestHandlerImpl" autowire="byName">
        <property name="brandAdProvider" ref="brandAdNominator" />
        <property name="winnerProcessors" >
            <list>
                <ref bean="dictBottomBrandAdvMappingProcessor" />
                <ref bean="recordAdRequestInfoProcessor" />
                <ref bean="setCodeIdProcessor" />
                <ref bean="adTypeInfoProcessor" />
                <ref bean="brandLinkFillInfoProcessor" />
                <ref bean="secureBrandLinkCreatorProcessor"/>
                <ref bean="dictAdItemAttributeProcessor"/>
                <ref bean="brandDictBottomPageUrlProcessor" />
                <ref bean="brandFeedBackLinkCreatorProcessor" />
                <ref bean="addBrandInfoProcessor" />
                <ref bean="dictBrandImprLoggerProcessor" />
                <ref bean="youthModeProcessor" />
                <ref bean="brandImprTrackerProcessor"/>
            </list>
        </property>
        <property name="transferAttribute">
            <value>true</value>
        </property>
    </bean>

    <!-- 取词品牌广告处理单元，一次取得对应位置上的所有广告 -->
    <bean id="dictWordBrandAdRequestHandler" class="outfox.ead.brand.handler.DictBrandAdRequestHandlerImpl" autowire="byName">
        <property name="brandAdProvider" ref="brandAdNominator" />
        <property name="winnerProcessors" >
            <list>
                <ref bean="recordAdRequestInfoProcessor" />
                <ref bean="setCodeIdProcessor" />
                <ref bean="adTypeInfoProcessor" />
                <ref bean="brandLinkFillInfoProcessor" />
                <ref bean="secureBrandLinkCreatorProcessor"/>
                <ref bean="dictAdItemAttributeProcessor"/>
                <ref bean="brandFeedBackLinkCreatorProcessor" />
                <ref bean="addBrandInfoProcessor" />
                <ref bean="youthModeProcessor" />
                <ref bean="brandImprTrackerProcessor"/>

            </list>
        </property>
        <property name="transferAttribute">
            <value>true</value>
        </property>
    </bean>

    <!-- 获取品牌广告排期的处理单元，一次取得对应位置上若干天正在投放、即将投放的所有广告 -->
    <bean id="brandScheduledAdRequestHandler" class="outfox.ead.brand.handler.DictBrandScheduledAdRequestHandlerImpl">
        <property name="brandAdProvider" ref="brandAdNominator" />
        <property name="queryDays">
            <value>7</value>
        </property>
        <property name="winnerProcessors" >
            <list>
                <ref bean="recordAdRequestInfoProcessor" />
                <ref bean="setCodeIdProcessor" />
                <ref bean="adTypeInfoProcessor" />
                <ref bean="brandLinkFillInfoProcessor" />
                <ref bean="secureBrandLinkCreatorProcessor"/>
                <ref bean="dictAdItemAttributeProcessor"/>
                <ref bean="brandFeedBackLinkCreatorProcessor" />
                <ref bean="addBrandInfoProcessor" />
                <ref bean="youthModeProcessor" />
                <ref bean="brandImprTrackerProcessor"/>

            </list>
        </property>
        <property name="transferAttribute">
            <value>true</value>
        </property>
        <property name="dictAdPositionInfoProvider" ref="dictAdPositionInfoProvider"/>
    </bean>


    <!-- 词典客户端(有道词典及手机端)展示服务器 -->
    <bean id="imprServerImpl" class="outfox.ead.brand.service.ImprServerImpl">
        <property name="handlers">
            <map>
                <entry>
                    <key><value>DictBrand</value></key>
                    <ref bean="brandAdRequestHandler"/>
                </entry>
                <entry>
                    <key><value>DictBrandText</value></key>
                    <ref bean="brandAdRequestHandler" />
                </entry>
                <entry>
                    <key><value>DictBrandImage</value></key>
                    <ref bean="brandAdRequestHandler" />
                </entry>
                <entry>
                    <key><value>DictWordBrand</value></key>
                    <ref bean="dictWordBrandAdRequestHandler" />
                </entry>
                <entry>
                    <key><value>DictWordText</value></key>
                    <ref bean="dictWordBrandAdRequestHandler" />
                </entry>
                <entry>
                    <key><value>DictBrandScheduled</value></key>
                    <ref bean="brandScheduledAdRequestHandler" />
                </entry>
                <entry>
                    <key><value>DictWordBrandAppendNormal</value></key>
                    <ref bean="dictWordBrandAdRequestHandler" />
                </entry>
                <entry>
                    <key><value>DictMultPosBrandText</value></key>
                    <ref bean="brandAdRequestHandler" />
                </entry>
            </map>
        </property>
        <property name="serviceTimeout">
            <value>200</value>
        </property>
    </bean>

    <!-- ############ handler END ##################### -->

    <!-- ############ Processor BEGIN ##################### -->
    <!-- 品牌广告底部通栏进行对应小图大图的processor  -->

    <bean id="dictBottomBrandAdvMappingProcessor"
          class="outfox.ead.brand.processor.DictBottomBrandAdvMappingProcessor">
        <property name="brandAdNominator" ref="brandAdNominator" />
    </bean>

    <!-- process pageurl attribute of brand ad in dict bottom -->
    <bean id="brandDictBottomPageUrlProcessor" class="outfox.ead.brand.processor.DictBottomPageUrlProcessor">
        <property name="smallImgUrl">
            <value>${brand_small_img_url}</value>
        </property>
        <property name="bigImgUrl">
            <value>${brand_big_img_url}</value>
        </property>
    </bean>

    <!-- 获取页面的referer -->
    <bean id="refererOfRefererExtractionProcessor"
          class="outfox.ead.brand.processor.RefererOfRefererExtractionProcessor" />
    <!-- 获取用户信息 -->
    <bean id="userInformationExtractionProcessor"
          class="outfox.ead.brand.processor.UserInformationExtractionProcessor">
        <property name="headers">
            <list>
                <value>User-Agent</value>
                <value>UA-Pixels</value>
                <value>UA-Color</value>
                <value>UA-OS</value>
                <value>UA-CPU</value>
                <value>Accept-Language</value>
            </list>
        </property>
    </bean>

    <bean id="adRequestProcessor" class="outfox.ead.brand.processor.DictAttributesProcessor" autowire="byName">
        <property name="brandImprLogger" ref="brandImprLogger"/>
        <property name="handlerProvider" ref="dictHandlerProvider"/>
        <property name="dictAdPositionInfoProvider" ref="dictAdPositionInfoProvider"/>
    </bean>
    <!-- 记录品牌广告的日志，继承winnerProcessor -->
    <bean id="dictBrandImprLoggerProcessor" class="outfox.ead.brand.processor.DictBrandImprLoggerProcessor">
        <property name="brandImprLogger" ref="brandImprLogger"/>
    </bean>

    <!--用于记录展示时间、请求URL和IP到AdRequest-->
    <bean id="recordAdRequestInfoProcessor" class="outfox.ead.brand.processor.RecordAdRequestInfoProcessor"/>
    <!-- 设置winner中的codeId -->
    <bean id="setCodeIdProcessor" class="outfox.ead.brand.processor.SetCodeIdProcessor"/>
    <!-- winner processor: add type informations and filter ads type not supported -->
    <bean id="adTypeInfoProcessor" class="outfox.ead.brand.processor.AdTypeInfoProcessor"/>
    <!-- 品牌广告点击链接填充客户端参数 -->
    <bean id="brandLinkFillInfoProcessor" class="outfox.ead.brand.processor.BrandLinkFillInfoProcessor"/>
    <!-- 生成品牌广告专用的点击服务器地址 -->
    <bean id="brandLinkCreatorProcessor"
          class="outfox.ead.brand.processor.BrandLinkCreatorProcessor">
        <property name="brandLinkCreator" ref="brandLinkCreator"/>
    </bean>
    <!-- 处理特殊的需要传递的字段 -->
    <bean id="dictAdItemAttributeProcessor" class="outfox.ead.brand.processor.DictAdItemAttributeProcessor"/>

    <!-- 生成品牌广告大图反馈链接 -->
    <bean id="brandFeedBackLinkCreatorProcessor" class="outfox.ead.brand.processor.FeedBackLinkCreatorProcessor">
        <property name="requestPrefix" value="${brand_feedback_url}"/>
    </bean>
    <!-- winner processor: for brand handlers -->
    <bean id="addBrandInfoProcessor" class="outfox.ead.brand.processor.AddIsBrandInfoProcessor">
        <property name="isbrand" value="brand"/>
    </bean>
    <!-- 屏蔽青少年模式广告 -->
    <bean id="youthModeProcessor" class="outfox.ead.brand.processor.YouthModeProcessor"/>
    <bean id="brandImprTrackerProcessor" class="outfox.ead.brand.processor.BrandImprTrackerProcessor"/>
    <!-- CPD和CPM品牌广告的nominator -->
    <bean id="brandAdNominator" class="outfox.ead.adnet.dsp.rpc.bs.nominate.BrandAdNominator" autowire="byName">
        <property name="dictAdPositionInfoProvider" ref="dictAdPositionInfoProvider"/>
        <property name="adBrandProducer" ref="brandAdProducer"/>
        <property name="brandAdFilterService" ref="brandAdFilterService"/>
        <property name="brandDspAdService" ref="brandDspAdService"/>
        <property name="brandGlobalConfig" ref="brandGlobalConfig"/>
        <property name="dictHandlerProvider" ref="dictHandlerProvider"/>
        <property name="fullChannelFlagMarker" ref="fullChannelFlagMarker"/>
    </bean>
    <bean id="brandAdFilterService" class="outfox.ead.gorgon.service.brand.BrandAdFilterService" autowire="byName">
        <property name="brandFilters">
            <list>
                <ref bean="brandModelFilter"/>
                <ref bean="brandGenderFilter"/>
                <ref bean="brandBlankDeviceIdFilter"/>
                <ref bean="brandAgeFilter"/>
                <ref bean="brandStateFilter"/>
                <ref bean="brandStyleFilter"/>
                <ref bean="brandCrowdPackageFilter"/>
                <ref bean="brandCrowdPackFilter"/>
                <ref bean="brandAppInstalledFilter"/>
                <ref bean="brandWechatOrientationFilter"/>
                <ref bean="brandExpiredFilter"/>
                <ref bean="brandVenderAdFilter"/>
                <ref bean="youthModeFilter"/>
                <ref bean="pandaAdFilter"/>
            </list>
        </property>
        <property name="globalFilters">
            <list>
                <ref bean="fullChannelFilter"/>
                <ref bean="brandAreaFilter"/>
                <ref bean="brandFrequencyFilter"/>
                <ref bean="cpmSmoothRecallFilter"/>
            </list>
        </property>
    </bean>


    <bean id="brandModelFilter" class="outfox.ead.gorgon.service.filter.BrandModelFilter"/>
    <bean id="brandGenderFilter" class="outfox.ead.gorgon.service.filter.BrandGenderFilter"/>
    <bean id="brandBlankDeviceIdFilter" class="outfox.ead.gorgon.service.filter.BlankDeviceIdFilter"/>
    <bean id="brandAgeFilter" class="outfox.ead.gorgon.service.filter.BrandAgeFilter"/>
    <bean id="brandStateFilter" class="outfox.ead.gorgon.service.filter.BrandStateFilter"/>
    <bean id="brandStyleFilter" class="outfox.ead.gorgon.service.filter.BrandStyleFilter"/>
    <bean id="brandFrequencyFilter" class="outfox.ead.gorgon.service.filter.BrandFrequencyFilter"/>
    <bean id="brandAreaFilter" class="outfox.ead.gorgon.service.filter.BrandAreaFilter"/>
    <bean id="brandCrowdPackageFilter" class="outfox.ead.gorgon.service.filter.BrandCrowdPackageFilter"/>
    <bean id="brandCrowdPackFilter" class="outfox.ead.gorgon.service.filter.BrandCrowdPackFilter"/>
    <bean id="brandAppInstalledFilter" class="outfox.ead.gorgon.service.filter.BrandAppInstalledFilter"/>
    <bean id="brandWechatOrientationFilter" class="outfox.ead.gorgon.service.filter.BrandWechatOrientationFilter"/>
    <bean id="brandExpiredFilter" class="outfox.ead.gorgon.service.filter.BrandExpiredFilter"/>
    <bean id="cpmSmoothRecallFilter" class="outfox.ead.gorgon.service.filter.CpmSmoothRecallFilter"/>
    <bean id="fullChannelFilter" class="outfox.ead.gorgon.service.filter.FullChannelFilter"/>
    <bean id="brandVenderAdFilter" class="outfox.ead.gorgon.service.filter.VenderTargetAdFilter"/>
    <bean id="pandaAdFilter" class="outfox.ead.gorgon.service.filter.PandaAdFilter" autowire="byName"/>
    <bean id="youthModeFilter" class="outfox.ead.gorgon.service.filter.YouthModeFilter"/>
    <bean id="brandBlankDeviceIdFilter" class="outfox.ead.gorgon.service.filter.BlankDeviceIdFilter"/>

    <bean id="fullChannelFlagMarker" class="outfox.ead.gorgon.service.filter.FullChannelFlagMarker"/>
    <!-- 生成品牌广告专用的点击服务器地址 -->
    <bean id="secureBrandLinkCreatorProcessor" name="secureBrandLinkCreatorProcessor" class="outfox.ead.brand.processor.BrandLinkCreatorProcessor">
        <property name="brandLinkCreator" ref="secureBrandLinkCreator"/>
    </bean>

    <!-- 修改生成的点击服务器的前缀为branding_click_web_url -->
    <bean id="secureBrandLinkCreator" class="outfox.ead.brand.processor.BrandLinkCreator">
        <property name="clickServerUrl" value="${brand_click_web_url_https}"/>
        <property name="encoder" ref="brandClickTrackEncoder"/>
    </bean>


    <bean id="youxuanDataSource" class="com.zaxxer.hikari.HikariDataSource">
        <constructor-arg type="com.zaxxer.hikari.HikariConfig">
            <bean class="com.zaxxer.hikari.HikariConfig">
                <property name="driverClassName" value="com.mysql.jdbc.Driver" />
                <property name="jdbcUrl" value="${youxuan_db}"/>
                <property name="maximumPoolSize" value="10"/>
                <property name="minimumIdle" value="2"/>
                <property name="connectionTimeout" value="30000"/>
				<property name="idleTimeout" value="30000"/>
                <!--7.5小时生命周期，因为mysql的生命周期是8小时-->
                <property name="maxLifetime" value="27000000"/>
                <property name="metricRegistry" ref="metricRegistry"/>
                <property name="poolName" value="HikariPool-youxuanDataSource"/>
                <property name="dataSourceProperties">
                    <props>
                        <prop key="cachePrepStmts">true</prop>
                        <prop key="prepStmtCacheSize">250</prop>
                        <prop key="prepStmtCacheSqlLimit">2048</prop>
                        <prop key="useServerPrepStmts">true</prop>
                        <prop key="useLocalSessionState">true</prop>
                        <prop key="rewriteBatchedStatements">true</prop>
                        <prop key="cacheResultSetMetadata">true</prop>
                        <prop key="elideSetAutoCommits">true</prop>
                        <prop key="maintainTimeStats">false</prop>
                        <!--why set these? https://github.com/brettwooldridge/HikariCP/wiki/MySQL-Configuration-->
                    </props>
                </property>
            </bean>
        </constructor-arg>
    </bean>

    <bean id="youxuanAdBrandDao" class="outfox.ead.data.datamanager.YouxuanAdBrandDao" autowire="byName">
        <property name="dataSource" ref="youxuanDataSource"/>
    </bean>

    <bean id="helper" class="outfox.ead.dataserv2.datamanager.DataManagerHelper" />
    <bean id="gzipCompressor" class="outfox.ead.dataserv2.datamanager.compressor.GZipCompressor" />

    <bean id="druidHttpClient" class="outfox.ead.data.util.DruidHttpClient"
          init-method="connect" destroy-method="close" >
        <property name="druidAddr" value="${druid_addr}" />
    </bean>


    <bean id="brandAdProducer" class="outfox.ead.data.datamanager.AdBrandProducer" init-method="init" destroy-method="destroy">
        <property name="youxuanAdBrandDao" ref="youxuanAdBrandDao"/>
        <!-- 品牌广告 -->
        <property name="adBrandCacheDomain" value="CachedBrandAdsV2"/>
        <property name="druidClient" ref="druidHttpClient" />
        <property name="scheduledDays" value="3"/>
        <property name="brandUpdateInterval" value="60000"/>
        <property name="druidContextTimeout" value="${druid_context_timeout}"/>
        <property name="brandAdFilterService" ref="brandAdFilterService"/>
    </bean>

    <!-- ############ Processor END ##################### -->


</beans>
