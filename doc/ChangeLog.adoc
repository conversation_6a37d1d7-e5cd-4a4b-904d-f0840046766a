= gorgon 变更记录

== 2.0.9.1 (2016-03-04)

* 版本
** ead-datamodel: 3.18.590970
** dsp-base: 1.26.591355.0
* 功能
** 根据广告位配置广告服务商分流
** isrd 请求参数支持：通过 clktracker 跳转
** clktrackers 支持
* BugFix
** 为品牌广告展示链接 URLEncode
** Bid floor currency: USD-&gt;CNY
** 升级 httpasyncclient

== 2.0.10 (2016-03-25)

* 版本
** ead-datamodel: 3.19.591611
** dsp-base: 1.30.592606
* 功能
** 版本大于 3.9.2 的 Android SDK 请求，去除 `yd_apk_download` 相关的后缀
** Gorgon打给Yex的请求里增加字段：区分是否SDK请求
** Gorgon打给Yex的请求里，添加 elementId, schemaId

== 2.0.12 (2016-04-05)

* 功能
** 移除动态加载 br 功能，将 br 代码合入 gorgon
* Bugfix
** 自测物料图片链接强制等比压缩
** 发往 YEX 的请求，不再强制要求必需有 ip

== 2.0.13 (2016-04-19)

* 功能
** 为品牌广告 header/body 添加 MAGIC_NO
* Bugfix
** 品牌广告展示上报检查参数，参数错误，信息不全时，均返回 fail
* 合并 2.0.14(yaoxm)
** Bugfix
*** DspBidDispatcherClient 不会从pool中移除，因为每次请求会重新连接
*** JSSDK XSS 漏洞修复
*** DspBidDispatcherClient pool 设置max wait time

== 2.0.17 (2016-05-05)

* Bugfix
** 使用 mcc 而非 ip 来判断是否海外流量

== 2.0.18 (2016-05-26)

* 功能
** 支持 adnet 参数:是否使用第三方网络

== 2.0.19 (2016-05-31)

* 功能
** 支持 cpm 底价配置
** 添加点击宏
* Bugfix
** 第三方广告分流配置，加载时 new Map ，而非 clear 原有 Map，避免数据不一致

== 2.0.20 (2016-06-21)

* 功能
** 非标元素的名字映射成标准元素的名字
** 非标尺寸的图片映射成标准尺寸的图片
* Bugfix
** OpenRTB 广告请求与 sdkAdRequest 中 isTest 字段不一致

== 2.0.21 (2016-06-27)

* 功能
** 分流配置中添加 mcc 排除列表

== 2.0.22 (2016-07-06)

* 功能
** 品牌广告图片预取，提供 prereq.s 接口，返回七天内需要使用的品牌广告图片

== ******** (2016-07-28)

* 功能
** 支持 imeimd5/udidmd5
** DEVICE_ID 取 md5 后的 imei/udid
** OpenRTB 支持多个 clktracker

== 2.0.23

* 功能
** 支持视频广告
* 改进
** 重构项目

== 2.0.23

* 功能
** 支持 imeimd5/udidmd5
** DEVICE_ID 取 md5 后的 imei/udid

== 2.1.1(2016-09-01)

* 功能
** udidmd5 -&gt; auidmd5, 对 IDFA 不再进行 md5
** 根据 adnet 值出相应广告

== 2.1.3

* 功能
** 视频回调信息发送到quipu-kafka。
** 将imei/androidId/idfa/aaid直接传入到BidRequest.Device中，依然支持传入strategyInfo中。
** 支持单独接受Android Advertising Id， HTTP请求key为aaid。
** 添加请求操作系统类型到YEX和智选,HTTP请求key为osv。
** 添加Gorgon访问YEX的metrics timer监控。

== *******(2016-09-09)

* 功能
** 增加 gorgon 到 bd 监控
** failURL 打到单独日志中
** 捕获 gorgon 到 YEX 超时异常

== *******(2016-09-12)

* 功能
** YEX 返回的智选广告，设置落地页链接中广告商，广告系列宏
** faillog 日志按天分割
** 修复 YEX 超时异常捕获 bug

== 2.1.5(2016-10-26)

* 功能
** 优化了获取BDClient的策略，使其尽量返回可用的BDClient。
* 改进
** 将IP解析失败的log由ERROR改到WARN级别。
** 重构了Spring-MVC的使用。

== 2.1.6(2016-11-16)

* 功能
** clktracker 中添加了广告位信息
** ios 请求不再设置 DEVICE_ID 为 md5(imei)

== 2.1.8(2016-12-05)

* 功能
** 系统提供Https接口，老的http接口依然保留。当请求中传入参数isSecure=1时返回https连接数据。
** Https接口数据有：
.. 智选返回的效果广告的OImageUrl
.. YEX返回的效果广告的OImageUrl
.. 视频广告的视频Url(codown)
.. 品牌广告的MainImageUUrl
.. 智选返回的展示点击tracker
.. YEX返回的展示点击tracker
.. 视频广告的回调接口、播放tracker
.. 品牌广告的点击上报Url
** 在请求为secure且落地页不为Https协议的Url时，广告Json对象中添加"action" : int 表示广告落地页在媒体端的打开方式。值参考enum对象link:outfox.ead.data.OpenAction

== 2.1.9(2016-12-15)

* 版本
** dsp-base-ext -&gt; 0.0.10
* 功能
** 将secure参数传递到BS。
** 如果为iOS请求且版本号大于9.X且落地页为http协议Url返回广告json对象中包含"action":2, 2表示SFSafariViewController打开。

== 2.2.1(2017-1-3)

* 功能
** SdkAdRequest转成youdao.BidRequest，添加到openrtb.BidRequest中
** 设置developer_id到openrtb的app.publisherID
* Bugfix
** 打给yex的批量请求，修复判断广告不足的逻辑错误，广告不足时应该返回状态partical success而不是complete success.

== 2.3.0(2017-01-10)

* 功能
** 添加响应时间 tracker
** 给 YEX 的请求中添加 User Agent 信息
* bugfix
** MAGIC_NO 在响应中统一为 string 类型，当前线上在响应多条广告时，为 int 类型, 响应单个广告时，为 string 类型
** 展示上报链接在 header 中设置错误，当前线上为 `[http:\\dsp-impr2...]`
** `ant deploy-test` 使用测试的 kafka 配置，当前使用的是线上配置
* 优化
** 统一 log 格式: (slot, reason, context)
** 请求参数解析后，存储在 SdkAdRequest 类的成员变量中，而不是存在 SdkAdRequest 的 attributes 中，避免失去类型检查的约束
 避免读写参数内容方法不统一
** 为方便测试用例编写，添加了 MockData 类，mock 测试过程中需要的各种数据，包括请求，响应，数据服务层等等
** build 脚本添加 `ant deploy-tcpcopy` 方便使用 tcpcopy 环境

== 2.3.1(2017-01-13)

* bugfix
** YEX 返回 crid 不为数字的广告时，响应时间 tracker 生成错误

== 2.3.3(2017-01-20)

* 优化
** metrics从metrics 2.2升级到metrics 3.1.2

== 2.3.4(2017-03-03)

* 功能

** 设备已安装App列表发送到Quipu Kafka.
* bugfix

** 设备已安装App列表发送到Venus开关设置失效。

== 2.3.5(2017-04-12)

* 优化
** 用GenericKeyedObjectPool替换GenericObjectPool，以Bid Dispatcher Server address作为key,通过轮训来访问各BD，以减小以前访问BD Server不均匀的情况。
** 设置Geo信息异常日志调整，打印出错误内容和广告位信息。
** 将因请求设置不合适的日志打到logs/appsidewarn.log， 如请求网络异常、Geo信息异常。

== 2.3.6(2017-04-27)

* 功能
** 添加ESP统计 https://dev.corp.youdao.com/outfoxwiki/yuhang/gorgon/externalStoragePermission[wiki]。

== 2.3.7(2017-04-20)

* 功能
** 添加eadm投放接口 https://dev.corp.youdao.com/outfoxwiki/yaoxm/eadm-transfer[wiki]。

== 2.3.8(2017-05-17)

* 功能

** 广告添加deep link url及相应trackers https://dev.corp.youdao.com/outfoxwiki/yaoxm/deeplink[wiki]。
* 优化

** 一部分因广告请求参数的warn打到logs/appsidewarn.log。

== 2.3.9(2017-05-25)

* 功能
** 动态创意支持 https://gitlab.corp.youdao.com/yaoxm/ead-work/blob/master/2017/dynamic-title/request.md[wiki]。
* change
** 品牌广告"clk"字段内容使用变体link字段值替换url字段值，link字段不能为空。
* 优化
** 一部分因广告请求参数的warn打到logs/appsidewarn.log。

== 2.4.0(2017-06-07)

* 功能
** 响应信息添加 X-Yex-Debug 头，表示 Yex 对接 Dsp 时竞价不成功的详细信息

== 2.4.1(2017-07-21)

* 功能
** 使用建立连接时重试的BDClient。
** 删除一些不需要的log。

== 2.4.2(2017-07-27)

* bugfix: 只给 youdao 的 clktracker 添加 slot 参数

== 2.4.4(2017-08-08)

* 功能
** 增加gorgon错误监控，统计，展示到grafana。https://dev.corp.youdao.com/outfoxwiki/Advertisement/gorgon/abnormal[wiki]。
** Grafana监控页面：http://eadata-grafana.corp.youdao.com/dashboard/db/gorgon-abnormal。

== 2.5.0(2017-08-21)

* 功能
** 支持落地页模板投放https://dev.corp.youdao.com/outfoxwiki/yaoxm/landingpageTemplate[wiki]。

== 2.5.2(2017-08-23)

* 功能
** gorgon日志增加ERROR TAG；
* 优化
** 重新整理AdState Code信息，并增加说明文档。

== 2.5.4(2017-09-07)

* 功能
** 完成设备品牌和价格定向https://dev.corp.youdao.com/outfoxwiki/Advertisement/DSP/BidServer/BrandPriceOrient[wiki]。

== 2.5.5(2017-09-11)

* 功能
** 按平台发送异常统计到kafka。

== 2.5.6(2017-09-18)

* 功能
** 当请求的广告为安卓下载型广告时，返回字段中增加appName和packageName。

== 2.5.7(2017-09-18)

* change
** resin 4 有http://bugs.caucho.com/view.php?id=6093[bug],到目前为止
 的Resin 4.0.53(2017-06-21)也没有修复。所以降级用resin 3.1.16。其目前已支
 持 JDK8。所以在线上版本中勿使用4.0.53及以前的resin4版本。
** resin 3.1.16有http://bugs.caucho.com/view.php?id=3509[bug],所以一定
 不能使用resin自己带的accesslog。
----
  284       <!-- can lead dead lock, do not use resin's access log -->
  285       <!--access-log path="logs/access.log" level="off"
  286             format='%h %l %u %t "%r" %s %b "%{Referer}i" "%{User-Agent}i"'
  287             rollover-period="1D">
  288       </access-log-->
----
** 实现了自己的accesslog功能outfox.ead.gorgon.server.AccessLogFilter。
 只打印/*.s的请求信息。格式为：
yyyy-MM-dd HH:mm:ss.SSS serverIp realIp method URI queryString referer UA
 如：
----
  2017-09-20 17:21:55.310 ************** *********** GET /gorgon/request.s id=03ab5cf13791c9623c10bcde025cabe5&ct=2&imei=864744032609503&dn=Xiaomi&mcc=460&mnc=00&iso=cn&cn=%E4%B8%AD%E5%9B%BD%
  E7%A7%BB%E5%8A%A8&rip=*********** null vamv1.0
----

== 2.6.0(2017-10-16)

* 功能
** 新增“信息流视频”广告渲染返回。

== 2.6.1(2017-10-17)

* Bugfix
** 信息流视频ext的压缩方式为commonExt，解压方式也应为commonExt，而非激励视频的PlayExt。

== 2.7.0(2017-10-20)

* 功能
** 添加广告位实时生产者 http://jira.corp.youdao.com/browse/BIDSYSTEM-1837[jira]
** 将广告样式、元素、广告位与样式对应关系这三个生产者从BS迁到Gorgon，广告位变化时候，也实时生产这三个域。域内数据在一起可以防止实时生产者生产出来的东西被覆盖。

== 2.7.1(2017-10-25)

* 功能
** 在返回的imptracker和clktrackers中增加用于第三方监测的展示和点击链接

== 2.7.2(2017-10-30)

* 功能
** 动态词包替换，新增词包种类，支持“{xx|yy}”格式的用户自定义默认词，兼容旧的词包"+{xx}+"替换。

== 2.8.0(2017-11-14)

* 功能
** 落地页链接宏支持有道统一 id: +{youdao_id}+
** gorgon直接访问BS,不再通过Bid Dispatcher服务。

== 2.8.1(2017-11-29)

* 功能
** 支持按照应用/广告位/设备 ID 前缀/比例分流 https://dev.corp.youdao.com/outfoxwiki/yuhang/gorgon/dispatcher[wiki]

== 2.8.2(2017-12-01)

* Bug fix
** OpenRtbYDExt与OpenRtbYDExtForDsp中定义的字段冲突问题解决。

== 2.9.0(2017-12-07)

* 功能
** 使用resin 4.0.55，所以不再使用自己打印的access log。
** api文档格式修改。

== 2.9.1(2017-11-14)

* 功能
** 激励视频改版，添加ctaText，ctaTrackers，videowitdh，wideoHeight等；
** 增加生产者Id2VideoInfoProducer，生产视频的宽高信息。

== 2.10.0

* https://dev.corp.youdao.com/outfoxwiki/zhaoxk/gorgon/add-trace-in-landingpage[文档]
* 功能
** 在落地页地址中添加 bid, sponsor_id, 用于跟踪转化; 对应配置位于 `/mfs_ead/ead/dsp/gorgon/online/dest_link_trace.json`

== 2.10.1(2017-12-15)

* 功能
** 第三方监测的URL支持所有落地页URL中的宏替换。

== 2.10.2(2017-12-15)

* Bug fix
** OpenRtbYDExt与OpenRtbYDExtForDsp中定义的字段冲突问题解决，删除旧版本协议字段

== 2.11.0(2017-11-23)

* 功能
** 新增：返回广告时，新尺寸图片兼容映射为旧尺寸广告位比例，支持广告样式粒度的设定；
** 修改：修改数据域，ADSlotStyleV3 -&gt; ADSlotStyleV4；
* 注意
** bid-server中也有需要读取广告位样式的地方，因此需要将其客户端adSlotStyleReadProxy的域也更新到V4。

== 2.12.0(2017-12-27)

* 功能
** eadd广告迁移到移动投放流程。

== 2.13.0(2018-01-02)

* 功能
** YEX 从智选返回的广告支持deeplink。

== 2.13.1(2018-01-17)

* 功能
** eadd中的"有道翻译网页版底部文字链"广告位映射关系改为 syndid=58 posid=302 和 syndid=58 posid=306。

== 2.13.2(2018-01-30)

* 功能
** YEX：从Dsp返回的广告支持deeplink。
** YEX：从Dsp返回的BidResponse，落地页url信息只依赖NativeResponse.Link, 不再依赖NativeResponse.Asset.Link

== 2.13.3(2018-02-05)

* 功能
** 新增：给 imptracker &amp; clktracker[s] 增加图片尺寸映射(2.11.0)的追踪信息，以统计某图片映射后的的展示、点击次数

== 2.13.4(2018-02-07)

* Bug fix
** 修复广告header中imptracker和广告体不一致的问题。

== 2.13.5(2018-02-08)

* 功能
** 为高斯模糊添加模糊程度值。

== 2.14.0(2018-01-24)

* 功能
** 支持品牌广告多样式：可以通过配置 eadb1.SdkSlot.IS_NOMINATE_FROM_ALL_BRAND_POSITION=true
 来使效果广告位可以从其配置的多个品牌广告位中从前往后提名出品牌广告，目前设置此属性为 true 时只支持
 单个广告请求，不支持批量请求。
** 支持只请求品牌广告，无论有无品牌广告返回，不再请求效果广告。可以通过设置
 eadb1.SdkSlot.IS_ONLY_NOMINATE_BRAND=true 。
** 支持广告位针对特定的设备id请求广告时，如果其样式有'正在审核中'的状态的样式，则返回自测物料。
** 数据域名变化如下，变化后的数据域使用 protoBuf(2.6.1) 序列化反序列化，兼容性
 较好，添加新的属性后上线生产者，使用者更新代码即可使用相应的新属性、不更新代码亦不影响正常使用。
*** SdkAppId2AppV3 -&gt; SdkAppId2AppVFinal
*** ADSlotStyleV4 -&gt; ADSlotStyleVFinal
*** SlotId2SdkSlotV6 -&gt; SlotId2SdkSlotVFinal
** 修改数据域以增加样式支持的最小app版本号（可理解为样式的版本号），修改生产者；
** 修改对接API，增加av参数，请求广告的时候带上当前app的版本。
* Bug fix
** 修复超时时间设置的错误：bidServerMaxProcessTimeMs和bidClientMaxLoad设置反了。

== 2.14.1(2018-03-15)

* Bug fix
** 获取自测设备id时，直接从请求中按优先级从前向后依次获取IMEI/AUID/UDID/AAID作为设备id，不区分SDK类型。

== 2.14.2(2018-03-19)

* 功能
** 发送广告请求所在的brand/model给yex。
** 图片尺寸映射，图片高度5%微调的逻辑和开发者系统保持一致
** 给Android app设置packageName，以便在日志中记录。
* Bug fix
** eadd/eadm会将设备id拼接到上报tracker中，需要防止XSS。
** 删除掉eadm中不需要的js脚本。
** eadm图片广告Oimage物料一定返回https格式。

== 2.15.0(2018-02-08)

* 功能
** 新增：从数据库读取广告位底价。
** 删除：删除原有的临时设置底价的逻辑。

== 2.15.1(2018-04-11)

* Bug fix
** 修复过YEX后遗漏智选广告第三广告展示、点击监控URL。

== 2.16.0(2018-04-26)

* 功能
** 所有修改venus的地方，同时写往venus3；
** 写venus3时，deviceId都转大写；

== 2.17.0(2018-05-04)

* 功能
** 只写venus3，且deviceId都大写；

== 2.18.0(2018-05-30)

* 功能
** 无论是否进行图片映射，都生成样式id信息，拼接到点击、展示上报链接中；

== 2.19.0(2018-06-19)

* 功能
** 易投流量（eadd）增加原易投SSP逻辑，支持将词典易投PC流量转发给外部广告主；

== 2.20.0(2018-06-20)

* 功能
** 品牌广告多样式目前不支持批量请求，提供修正错误的请求功能。

== 2.20.1(2018-06-22)

* Bugfix
** 修复传给yex的didmd5参数双重md5的bug，增加dpidmd5参数。

== 2.20.2(2018-06-26)

* Bugfix
** 修复在只有返回1个品牌广告点位符的情况时不渲染 X-Ad-Loaded/X-Ad-Ready。

== 2.21.0(2018-06-29)

* 功能
** https://dev.corp.youdao.com/outfoxwiki/huqj/infor-flow-carousel[品牌广告支持轮播]

== 2.21.1(2018-07-03)

* Bugfix
** VenusClient使用connection_timeout和socket_timeout进行初始化，加大连接超时时间。

== 2.22.0(2018-07-03)

* 功能
** https://dev.corp.youdao.com/outfoxwiki/huqj/video2brand[信息流视频接入品牌投放系统]

== 2.23.0(2018-07-11)

* 功能
** YEX支持激励视频广告。
** 从YEX返回的广告直接使用OpenRtb.BidResponse.SeatBid.Bid.extension(ydAd,
Bid.BidResponse.AdSlotResponse.Ad)对象，弃用以前的Bid.setAdm(AdSerializedInJson)。

== 2.24.0(2018-07-19)

* Bugfix
** 修复只有轮播的信息流视频广告可以在品牌系统投放

== 2.25.0(2018-07-12)

* 功能
** YEX非智选广告也为有道imp/clk tracker增加样式id后缀；
* Bugfix
** 修复一些错误的test case；

== 2.25.1(2018-08-02)

* Bugfix
** YEX非智选广告imp/clk tracker域名修正；

== 2.26.0(2018-08-03)

* 功能：
** VAST 广告展示、点击上报支持数组形式。
* Bugfix
** 激励视频广告NPE,
 http://jira.corp.youdao.com/browse/BIDSYSTEM-2045?filter=-2[wiki]。

== 2.27.0(2018-08-03)

* 功能
** SdkAdSlot添加以下属性，同时将其设置到bidRequest中：
*** minCpc，广告位同意接受的最小CPC
*** blackSponsorIds,禁止展示其广告的广告主列表
*** blackCategoryIds,禁止展示的广告类别

== 2.28.0(2018-08-09)

* 功能
** 升级ead.jar版本，支持品牌广告国家定向投放

== 2.29.0(2018-08-03)

* 功能：

** gorgon中的有关device的id最终都统一为大写形式。其中imei、auid、aaid都先明文大写，再md5，再大写；
** 实现方式：保证原始的id均为大写 + 保证所有id在md5后都改为大写；
* 其他

** 添加gitlab-ci；
** 解决powermock依赖冲突；

== 2.30.0(2018-08-16)

* 功能
** eadController流程支持品牌广告国家定向

== 2.31.0(2018-08-10)

* 功能
** 收集用户的应用行为信息，写到 venus
* bugfix
** fix mockito-core 版本依赖问题

== 2.31.1(2018-08-16)

* 功能：
** eadd/eadm流量deviceId改为大写；
** rtd文档个别字段、语义冲突的部分小修改；

== 2.31.2(2018-08-16)

* Bugfix
** 构造完SdkAdRequest时，优先进行正确性校验，防止调用时出现NPE；

== 2.32.0(2018-08-15)

* 功能：
* https://dev.corp.youdao.com/outfoxwiki/huqj/gorgon-brand-test[支持品牌广告测试功能]。

== 2.32.1(2018-08-21)

* Bugfix
** 启停脚本正确返回`exit 0`以使用Jenkins。

== 2.33.0(2018-08-22)

* 功能
** https://dev.corp.youdao.com/outfoxwiki/huqj/gorgon-dynamic-config[轮播广告位和品牌广告位支持数据库动态配置]
** https://dev.corp.youdao.com/outfoxwiki/huqj/dict-result-page-big-slot[有道词典查词结果页新增大图和视频广告位]
* Bugfix
** 品牌广告测试设备只取品牌测试广告，不取效果自测物料。

== 2.33.1(2018-08-23)

* Bugfix
** MultiStyleBrandAdRequestFix 中 adCount 可能为 null，需要等于1来处理。

== 2.33.2(2018-08-23)

* Bugfix
** 更新datamodel版本，AdItem的序列化方法修改回原来的版本，和老易投保持一致。

== 2.34.0(2018-09-12)

* 功能
* 支持互动广告：请求参数中添加了互动广告 ID、类型、入口应用 ID、入口广告位 ID。

== 2.35.0(2018-09-12)

* 功能
** 广告请求中添加支持屏幕信息，目前只用于外部 DSP 使用，智选并没有使用。
** 广告请求中添加支持Android设备的AdvertisingId。
** 已安装app列表日志单独输出到logs/gatherApp.log中。

== 2.35.1(2018-09-12)

* Bugfix
** 修复打印品牌广告日志。从SDK投放的品牌广告展示为SDK上报的展示量，但从eadd/eadm迁移过来的品牌广告不记展示，而是将bid当作展示。

== 2.36.0(2018-09-19)

* BugFix
** 渲染 OpenRTB 广告时展示trackers、点击trackers、落地页Url需要区分优先级，以更小范围的值为准。如Asset中有展示tracker则不使用其父节点的tracker值。
** 修复VAST返回的广告展示、点击只取第一个url的bug，现在支持全量返回到相应的数组中。
* 功能
** 对渲染部分进行了重构，赋值与修改分离。
** 效果、品牌点击tracker中拼接广告位信息重构。
** 有道域名的展示、点击tracker（非第三方tracker）拼接上样式id重构。

== 2.37.0(2018-09-06)

* 给媒体返回视频广告的时长、宽高和大小

== 2.37.1(2018-09-21)
 * 修复SdkSlot表中由于BlockSponsorIds和BlockCategoryIds配置错误导致整条数据被忽略的问题

== 2.37.2(2018-08-30)

* Bugfix
** 使用参数`q`传递查询关键词，代替`keyword`参数。

== 2.38.0(2018-08-30)

* 功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-1984[品牌广告展示使用新统计，将展示上报数据发给kafka]

== 2.38.1(2018-09-29)

* 功能
** OpenRTB协议中传入 device.ida/device.carrier。
** 支持超时错误返回码43001。
** 在配置文件中将bourse（yex前称）改为yex以统一ADX名称。

== 2.39.0(2018.09.14)

* Improvement
** Change log4j to logback.
** Exclude slf4j-log4j12.
** Mod ivy-report to find dependencies' relationship.
** Add AppRunner2 to config logback.xml.
** Mod start.sh to use AppRunner2.

== 2.40.0(2018.09.30)

* 优化
** 更新ead-ip-1.4.+ =&gt; ead-ip-core 1.8.1.

== 2.41.0(2018-09-10)

* jira: http://jira.corp.youdao.com/browse/BIDSYSTEM-2078
* wiki: https://dev.corp.youdao.com/outfoxwiki/Advertisement/DSP/BidServer/BrandPriceOrient#A.2BlwBsQk4J-[设备品牌、价格 定向投放-需求三]
* 功能
** 删除无用的api参数brand/model（没有在功能上使用，也未在api文档中出现过）；
** 保留原始品牌型号信息，删除将非热门品牌设为others的逻辑，挪入bs，且只在bs里的ruleFilter处临时转换为others；
** 修改获取设备品牌和型号的优先级：
*** 首选SDK传来的dn参数SdkAdRequestAttributes.DEVICE_NAME解析出的brand/model；
*** 次选user-agent解析出的brand/model；
*** 最后使用api传来的dn参数解析出来的brand/model。

== 2.41.1(2018-10-30)

* bugfix
** 修复DevicePriceService返回null的问题；

== 2.42.0(2018-10-31)

* Bugfix
** 激励视频回调接口在解析奖励内容时需要优先使用用户自定义秘钥。
* 功能
** 激励视频回调提供参数v，如果为v=2表示不再处理用户自定义激励数据，直接转发到第三方服务器。
** 收集请求refer到bidRequest中以使用反作弊使用。jira:http://jira.corp.youdao.com/browse/QUIPU-70[sdk_stat_new表的aggregation新增点击refer项]
** 更新deeplink白名单。

== 2.42.1(2018-10-31)

* 功能
** 激励视频回调第三方服务器支持Https，目前忽略所有证书。

== 2.42.2(2018-11-08)

* bugfix
** 生产者生产广告位和样式映射时，只生产有效样式的映射；

== 2.43.0(2018-10-08)

* wiki：https://dev.corp.youdao.com/outfoxwiki/liuhaibo/price-by-template[模板投放cpc出价溢价系数调整]；
* 功能
** 生产者从druid查询过去一段时间广告位的平均点击价格；

== 2.43.1(2018-11-23)

* bugfix
** 修复：druidry引入新版的jersey-client，和quipu-avro中老版的冲突，导致kafka producer启动失败；

== 2.43.2(2018-11-23)

* 优化
** 网络类型为MOBILE，子网络类型设置为OTHER时不需要打印异常，如2G网络我们设置为OTHER。

== 2.43.3(2018-12-06)

* 优化
** exchange.maxTotal 200-&gt;400。

== 2.44.0(2018-12-07)

* Improvement
** 干掉JsonAdRender上本地宝的代码

== 2.45.0(2018-12-10)

* 优化
** 如果请求中的广告位无有效样式，直接终止该请求，并返回新错误码20003；

== 2.46.0(2019-01-15)

* Bugfix
** 升级dataserv2，修复以下bug:
... https://gitlab.corp.youdao.com/ead/dataserv2/merge_requests/3[修复生产数据域时有可能出现域名没有放入同步列表的bug]
... https://gitlab.corp.youdao.com/ead/dataserv2/merge_requests/1[DataReadProxy 不断尝试同步数据，不因异常退出]

== 2.47.0(2018-01-17)

* 功能
** 兼容SDK处理激励视频广告落地页逻辑。http://jira.corp.youdao.com/browse/BIDSYSTEM-2116http://jira.corp.youdao.com/browse/BIDSYSTEM-2116[JIRA]。

== 2.48.0(2019-01-10)

* 功能
** 智选效果广告开启合成链接时，落地页链接替换成合成链接

== 2.48.1(2019-01-26)

* Bugfix
** 修复合成链接不生效的问题

== 2.49.0(2019-01-03)

* wiki：http://confluence.inner.youdao.com/pages/viewpage.action?pageId=2530896[场景定向]；
* 功能
** SlotId2SdkSlotVFinal数据域中SdkAdSlot增加广告位对应的场景id；

== 2.50.0(2019-01-18)

* 功能
** 返回应用直达调起结果上报地址和APK下载结果上报地址

== 2.51.0(2019-02-28)

* 功能
** 屏蔽词典查词结果页敏感词汇广告请求，即查询敏感词汇时放弃展示机会。
* 优化
** quipu-kafka升级到1.1.0。
** SlotId2SdkSlotVFinal数据域中SdkAdSlot增加广告位对应的场景id；

== 2.52.0(2019-02-26)

* 功能
** 给Yex传AdType字段标识广告位是：0-只可投效果广告，1-只可投品牌广告，2-品效共用

== 2.53.0(2019-03-04)

* 功能
** 如果品牌广告是从SDK(Gorgon)投放出去的广告，点击上报不需要302跳转到落地页，而是返回"thanks"。目前由Gorgon在投出品牌广告时
在点击上报tracker中添加标识是否需要302跳转。

== 2.54.0(2019-03-15)

* 功能
** API对接的激励视频SDK endCard页面点击上报不进行SDK的bug兼容处理

== 2.55.0(2019-03-18)

* 功能
** 品牌广告展示和点击增加设备号用于统计

== 2.55.1(2019-03-18)

* 功能
** 针对一些特殊的广告位，仍然使得点击上报tracker能够302跳转

== 2.55.2(2019-03-18)

* 修复
** 改正不抛出 NOT_IN_CACHE 的方式

== 2.55.3(2019-03-20)

* 修复
** 修复品牌广告点击中未写入设备号。

== 2.56.0(2019-04-12)

* 优化
** exchange.maxTotal:400 –&gt; 600。

== 2.57.0(2019-05-13)

* 功能
** 增加PC端广告位的cookie mapping相关功能；
** 增加字段区分PC端广告位和非PC端广告位；
** 收集JSSDK的cookie并传给YEX；
** 针对API对接的PC广告位，收集cookie并传给YEX；
** wiki: http://confluence.inner.youdao.com/pages/viewpage.action?pageId=2551881

== 2.58.0(2019-05-13)

* Bugfix
** 修复http://jira.corp.youdao.com/browse/BIDSYSTEM-2274[小写设备号无法拉取自测物料问题]

== 2.59.0(2019-05-14)

* 功能
** rst文档增加对PC广告位clktrackers的说明
** gorgon针对pc广告位添加developerId和siteCategoryIds,默认isrd=1
** 更新测试库地址

== 2.60.0(2019-05-21)

* 功能
** webmail banner广告位尺寸修改
** 修复新版本ios sdk品牌广告上报异常的问题

== 2.61.0(2019-05-22)

* 功能
** 增加第三种测试设备类型：既是品牌广告测试设备也是效果广告测试设备
** pc广告位传cookie给bs

== 2.62.0(2019-05-30)

* 功能
** 数据服务层使用新版监控框架。

== 2.63.0(2019-06-04)

* 功能
** YEX：从Dsp返回的广告支持dpTrackers/dpFailedTrackers

== 2.64.0(2019-06-12)

* 功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2207[更多广告位clktracker字段支持302跳]。

== 2.65.0(2019-06-13)

* wiki：http://confluence.inner.youdao.com/pages/viewpage.action?pageId=8308591[场景定向v1.1]；
* 功能
** SlotId2SdkSlotVFinal数据域中SdkAdSlot增加广告位对应的场景信息；

== 2.65.1(2019-06-19)

* 功能
** 内存不够用，调大jvm启动参数xmx

== 2.66.0

* 功能
** 收集 android sdk 采集的用户电池，电量，内存，传感器等信息

== 2.66.1

* bugfix
** 升级 ead 库到 1.2

== 2.67.0

* 功能
** dataserv2升级到3.0.0版本，其消息队列从RabbitMQ切换到Kafka。

== 2.68.0

* 功能
** Kafka client升级并统一到2.11_0.11.0.3。

== 2.69.0

* wiki：http://confluence.inner.youdao.com/pages/viewpage.action?pageId=11537794[场景定向V1.2.1]
* 功能
** 某些slot udid不属于对应的scenario id，即使其对应的app id属于对应的scenario id

== 2.70.0

* 功能
** 动态词包中地域信息更改为使用修复库 + GPS + 标准 IP 库

== 2.70.1

* Bugfix
** 增加直辖市的判断

== 2.71.0

* 功能
** 给bs传输的AdSlot中赋值场景ID

== 2.72.0

* 功能
** 支持设置某些流量只返回品牌广告，不返回效果广告

== 2.73.0

* 功能
** 给yex发送是否支持deeplink调起的字段

== 2.74.0

* 功能
** 品牌广告的渲染结果中添加`mmaImpUrl`字段用于客户端展示上报

== 2.75.0

* 功能
** imprTracker和clkTracker新增tid(模板id)参数

== 2.76.0

* 功能
** 新增oaid字段作为imei的补充字段

== 2.76.1

* bugfix
** 【品牌广告】测试设备下，将测试广告调到候选广告列表最前面，防止线上轮播数满导致刷不出测试广告

== 2.77.0

* 功能
** 同一创意多样式优先级排序 http://jira.corp.youdao.com/browse/BIDSYSTEM-2451[JIRA]

== 2.78.0

* 变更
** venus2-client依赖版本从3.0.3升级到4.2.3

== 2.79.0

* bugfix
** 与eadd包同步bean配置文件，以解决有品牌广告投放时的NPE问题
* 变更
** 去除无用的数据域读取以及相应的类
* 变更
** 由于tcpcopy方式会将流量打给线上bs，删除ant build-tcpcopy命令及相关配置

== 2.79.1

* bugfix
** 解决 netty 相关包的依赖问题

== 2.80.0

* 功能
** 支持JsSDK新增oaid作为imei的补充字段

== 2.80.1

* 功能
** centraldogma 改用 corp 域名
** CI/CD 镜像使用 centos7-jdk1.8.0_66

== 2.81.0

* 改进
** eadd停用SponsorAgentId，删除过期配置

== 2.81.1

* bugfix
** 互动广告过滤掉不正常的广告ID

== 2.82.0

* 功能
** 构造BidRequest时，设置app channel参数

== 2.83.0

* 功能
** 访问YEX时提供精确超时时间控制参数exchange.maxRequestTimeout
** 监控从yex请求广告时的http请求耗时及超时频率

== 2.84.0

* 功能
** 禁止定向广告的请求只打给智选，并在请求中设置禁止定向的标识

== 2.85.0

* 功能
** 轮播的品牌广告支持访问yex指定dsp获取实时品牌广告素材
** 更新MachineUtils,获取服务相关信息使用java自带支持的api

== 2.86.0

* 功能
** BidRequest增加max_cpc字段，广告位cpc上限，用于模板定向cpc广告组调价

== 2.87.0

* bugfix
** 修复yex返回的信息流视频广告adCat字段不正确的问题

== 2.88.0

* 功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2503[支持指定广告位在指定时间段内返回的广告增加指定监测链接数据]

== 2.89.0

* 功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2508[品牌广告补量小工具支持宏参数替换]

== 2.89.1

* fix
** 解析图片元素名称失败时，log里输出图片元素名称。

== 2.90.0

* 功能
** 轮播品牌广告支持dsp配置改从mfs_ead上读取
** 增加LocalFileWatchDog用于后续WatchDog的统一管理

== 2.90.1

* 改进
** 完善接口文档中设备id相关参数的说明

== 2.91.0

* 功能
** sdk广告位生产者添加是否支持平滑消费的字段
** 发送给智选的请求中设置广告位是否支持平滑消费

== 2.92.0

* 功能
** 发送给yex的openrtb请求携带oaid参数
** 将imei、androidId单独放到扩展字段中

== 2.93.0

* 功能
** 添加品牌广告预取接口
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=19371106[wiki]

== 3.0.0

* 功能
** 转用 maven 构建
** 优化gitlab ci 与 回测沙盒环境

== 3.0.1

* fix
** 修改 readme 描述
** 修改启动脚本

== 3.1.0

* 功能
** 出打底品牌广告素材时，干掉标题中的dsp信息
** 删除ivy文件

== 3.2.0

* 功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2554[更新gorgon对接文档]

== 3.2.1

* fix
** ci 环境迁移到 k8s-dev2

== 3.3.0

* 改进
** 激励视频广告endcard样式适配优化
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=24392509[wiki]

== 3.4.0

* fix
** 加载ScenarioApp表读不到第一行，bugfix

== 3.5.0

* 改进
** SDK producer使用G1GC。

== 3.6.0

* 功能
** 点击上报tracker能够302跳转的白名单新增2个广告位

== 3.7.0

* 迁移
** 将eadd服务全部功能迁移至gorgon
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=22014584[wiki]
* 功能
** 品牌广告支持vender参数指定提取dsp的广告

== 3.7.1

* bugfix
** 修复数据转换导致的dsp品牌广告统计信息缺失的问题

== 3.8.0

* 改进
** 支持对dsp的信息流视频播放事件做上报
** 支持对dsp的下载开始和完成事件做上报

== 3.8.1

* fix
** 修复品牌广告对接dsp中由于修改尺寸导致的问题

== 3.9.0

* 改进
** 品牌广告指定dsp配置移动到centraldogma上
** 删除过期的rabbitmq配置

== 3.10.0

* 功能
** 对秒针监测链接做宏替换
** 对于sdk对接的品牌广告位增加monitorImprUrls字段和monitorClickUrls字段返回以支持多条上报

== 3.10.1

* bugfix
** 品牌广告直投广告（非dsp对接）漏掉了单条上报链接字段
** api对接品牌广告链接没有实现宏替换

== 3.10.2

* bugfix
** 修复oaid被大写的问题

== 3.10.3

* 功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2615[品牌广告落地页链接宏替换]

== 3.11.0

* 功能
** bs无广告返回时区分具体情况，超时，异常，还是返回空广告

== 3.11.1

* fix
** 品牌广告构造请求时设置请求的广告数量

== 3.12.0

* 功能
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=32791056[品牌广告支持PD模式及设备号抓取替换]

== 3.12.1

* 更新
** 更新ead.jar的版本，以支持cpm关键词品牌广告

== 3.13.0

* 功能
** https://logging.apache.org/log4j/log4j-2.3/manual/async.html#Performance[日志框架由logback改成log4j 2]
** log4j.FileWatchDog改用LocalFileWatchDog

== 3.14.0

* bugfix
** 品牌广告发给yex的请求中secure默认置为true以获取https连接
* 功能
** 品牌广告对接dsp支持预取功能

== 3.14.1

* bugfix
** 修复两个bidRequest hashCode相同导致的bug
** `BidResponse.parseFrom`解析字节数组，加上异常捕获

== 3.14.2

* fix
** text元素（基本只有邮箱大师在用）和title一样使用动态词包替换；

== 3.14.3

* fix
** 更新ead.jar：品牌cpm广告在提名时设置是否全屏的标识用于客户端渲染

== 3.14.4

* 功能
** sdk品牌预取接口增加参数
** 

更新eadd依赖以支持翻译官开屏视频广告位

== 3.15.0

* 功能
** 更新ead.jar：eadd品牌广告新增iconimage和appName字段
** 

品牌广告结果渲染增加可选字段：appName和iconimage

== 3.15.1

* fix
** SDK API对接文档中说明展示上报支持的 Content-type。

== 3.15.2

* 功能
** 品牌广告从通过yex获取素材增加appName、video、iconimage三个元素

== 3.15.3

* 功能
** 低版本ios慕课不返回合成链接，直接返回deeplink
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2674[jira]

== 3.15.4

* bugfix
** 修复信息流视频广告不支持vender_id的bug

== 3.16.0

* 功能
** 品牌广告增加渲染brandClkType字段

== 3.16.1

* 更新
** 更新依赖eadd的版本，品牌广告cpm支持配置轮播数
* bugfix
** 修复无法解析X-Forwarded-For中包含多个ip情况的bug

== 3.16.2

* 功能
** 新增中国大学mooc开屏广告位尺寸映射
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=62366256[wiki]

== 3.16.3

* bugfix
** 修复一个sdk广告位对应多个品牌广告位时，cpm广告可能会排在cpd广告之前的bug
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2664[jira]

== 3.16.4

* bugfix
** 将补量工具的宏替换方式修改为秒针的宏替换逻辑
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2684[jira]

== 3.16.5

* 功能
** 去掉设备号替换逻辑，宏替换时通过操作系统类型区分应替换的设备号字段
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2684[jira]

== 3.17.0
 * 功能
 - 词典android端支持效果轮播功能
 - http://confluence.inner.youdao.com/pages/viewpage.action?pageId=64937053[wiki]

== 3.17.1
 * bugfix
 - 新加的轮播逻辑放在request null check之后，不然日志会刷NPE；

== 3.18.0

* 功能
** 新增有道云笔记mooc开屏广告位尺寸映射
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=64936053[wiki]

== 3.18.1

* 功能
** 在响应中增加几种打点上报链接
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2686[jira]

== 3.18.2

* 功能
** 针对品牌广告增加deeplink字段，方便词典应用内跳转
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=67436001[wiki]
* fix
** 针对品牌广告预取接口，删除title中用于程序化对接的无用前缀

== 3.18.3

* 功能
** 品牌广告程序化对接支持deeplink功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2715[jira]

== 3.19.0

* 功能
** 品牌广告预取接口点击上报链接修改为https
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2726[jira]

== 3.20.0

* 功能
** 增加CAID字段(中国广告协会互联网广告标识），作为IOS 14获取不到IDFA后的替补字段。
** 子网络类型加入5G值。

== 3.21.0

* 功能
** 品牌广告程序化对接增加deeplink字段
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2728[jira]

== 3.21.1

* BugFix
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2735[EADD服务中获取数据网络不通时无法启动]

== 3.22.0

* 功能
** 品牌广告预取接口prebrand.s增加对cpm广告的支持

== 3.22.1

* 功能
** 出错时输出更多日志。

== 3.23.0

* 功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2740[品牌广告发送给yex的请求，title默认为required=true]
* bugfix
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2742[预取接口eadd/request.s 修复落地页链接未进行宏替换的问题]

== 3.23.1

* bugfix
** 品牌广告发送给yex的请求，title修改为若为视频广告required=true，否则为false

== 3.24.0

* 功能
** http://jira.corp.youdao.com/browse/BIDSYSTEM-2745[支持aaid（阿里巴巴广告设备号）和caid（中国广告协会设备号）]

== 3.25.0

* 功能
** 品牌广告投放增加wxMiniProgram字段，包含跳转小程序所需的全部字段
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=77931921[wiki]
* 功能
** 升级uap-java依赖的版本为0.7.0

== 3.25.1

* 更新
** 更新依赖eadd的版本 将iplocation工具更新为advancedIptool

== 3.25.2

* 更新
** 更新依赖eadd的版本到2.2.3 修改了eadd内部逻辑 将tuple5的使用更新为了LocationInfo

== 3.25.3

* bugfix
** 修复取设备号不区分操作系统类型的bug，增加对auid和oaid的支持
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2751[jira]

== 3.25.4

* bugfix
** 修复判断oaid是否为测试设备号时大小写对不上的问题
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2754[jira]

== 3.26.0

* 功能
** 更新eadd版本，支持预取接口投放带有firstshot标记的广告
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2752[jira]

== 3.26.1

* 修复
** 更新eadd版本，词典预取接口增加对aaid的支持
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2763[jira]

== 3.26.2

* 修复
** 更新eadd版本，修复计算ios全屏广告位高宽比例时，额外考虑了logo_height的bug

== 3.27.0

* 功能
** 广告位走YEX的情况下，广告返回packagename和appname
** http://jira.corp.youdao.com/browse/YEX-220[jira]

== 3.28.0

* 功能
** eadd接口和prebrand接口新增渲染字段fullScreenClick，标识开屏广告是否允许全屏跳转
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=93405265[wiki]

== 3.29.0

* 功能
** eadd和prebrand接口增加对视频类型广告程序化对接的支持
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2768[jira]

== 3.30.0

* 功能
** 实时接口增加abtest字段
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2777[jira]

== 3.31.0

* 功能
** 效果广告增加downloadAppInfo字段，表示下载型广告将要下载应用的相关信息
** http://confluence.inner.youdao.com/pages/viewpage.action?pageId=98019767[wiki]

== 3.32.0

* 功能
** 品牌广告eadd接口支持根据版本号控制广告对象下发
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2807[wiki]

== 3.32.1

* 功能
** 更新 API 对接文档，添加 iOS 获取 device name 的代码样例。

== 3.32.2

* 功能
** 增加eadd 接口请求统计日志

== 3.33.0

* 功能
** 效果广告新增用于微信小程序跳转的三个字段。
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2808[jira]

== 3.34.0

* 功能

** 支持词典焦点图位置的视差图类型相关字段。
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2810[jira]
* 功能

** 支持在centraldogma中配置信息流视频包含的元素id
** 支持在指定广告位的视频url上拼接视频的宽高信息
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2809[jira]

== 3.34.1

* bugfix
** 修复展示、点击上报 URL 中只有图片在广告样式指定了映射方式时才拼接图片信息的问题。此 bug 会导致 oimage_stat 中的图片展示、点击信息
 不准确。目前不指定映射方法是使用outfox.ead.gorgon.render.RenderUtils.ImageSizeMapMethod.DIRECT，因为图片 URL 中一定会拼接上
 `&w=` 和 `&h=`，可以理解为按此尺寸进行拉伸。

== 3.34.2

* 优化
** ead-ip-core 升级到1.15.2。

== 3.34.3

* 优化
** 重置无效的设备 ID，将无效的设备ID替换为&quot;&quot;，无效设备ID列表获取自central dogma
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2827[jira]

== 3.34.4

* 优化
** 批量请求品牌广告逻辑支持处理信息流视频物料。
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2844[jira]

== 3.34.5

* 优化
** 批量请求品牌广告根据adcount限制广告数量。
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2844[jira]

== 3.34.6

* bugfix
** 修复词典版本号参数为空时的npe问题

== 3.34.7

* bugfix
** 修复预取cpm广告程序化对接无法投出广告的问题
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2852[jira1]
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2813[jira2]

== 3.35.0

* 功能
** bidrequest新增trafficSource字段，用于标识此请求来自于哪个系统（YEX、GORGON、BD），默认值为BD
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2853[jira]

== 3.36.0

* 功能
** 预取接口prebrand支持vender_id指定广告
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=26586752[wiki]

== 3.37.0

* 功能
** 词典开屏支持摇一摇，增加shakable字段
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2857[jira])

== 3.38.0

* 功能
** 词典信息流视频广告位支持品牌广告程序化对接
** openrtb协议视频尺寸采用对应封面图片的尺寸
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2858[jira1]
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2860[jira2]

== 3.39.0

* 功能
** 品牌点击上报接口兼容优选广告位上报到brand_impr_v2 topic

== 3.40.0

* 功能
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2861[支持智慧学习开屏尺寸适配]

== 3.41.0

* 功能
** http://jira.inner.youdao.com/browse/YEX-232[新增设备相关参数]

== 3.41.1

* bugfix
** 兼容已有字节单位，减少error日志数量

== 3.42.0

* 功能
* 增加支持图片等比缩放映射
* http://jira.inner.youdao.com/browse/ZHIXUAN-4812[jira]

== 3.43.0

* 功能
** 支持通过vender_id直接指定广告
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2865[jira]

== 3.44.0

* 功能
** yex的openRTB协议更新字段,添加了下载应用信息
** http://jira.inner.youdao.com/browse/BIDSYSTEM-2863[jira]

== 3.45.0

* 功能
** eadd接口支持tanx反作弊参数

== 3.45.1

* bugfix
** 修复了实时接口通过vender直接指定程序化对接广告时，可能返回打底广告的问题

== 3.45.2

* bugfix
** 修复了实时接口品牌广告程序化对接中，当yex不返回广告时，不去请求效果广告的问题

== 3.45.3

* 功能
** 升级ead-ip-core版本，https://confluence.inner.youdao.com/pages/viewpage.action?pageId=121912021[wiki]

== 3.46.0

* 功能
** 优选投放重构
** http://jira.inner.youdao.com/browse/YOUXUAN-4[jira]
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=115909193[wiki]

== 3.46.1

* fix
** 升级log4j版本以修复远程代码执行漏洞。
** https://help.aliyun.com/noticelist/articleid/1060971232.html?spm=a2c4g.789213612.n2.6.3a7d4c07DML4Vz[漏洞详情]

== 3.46.2

* fix
** 修复程序化对接中获取不到封面图片的bug。
-http://jira.inner.youdao.com/browse/YOUXUAN-290[jira]

== 3.46.3

* fix
** 词典开屏展示上报记录keyFrom

== 3.46.4

* fix
** 词典开屏pv bid

== 3.46.5

* fix
** update log4j 2.15.1.SNAPSHOT -&gt; 2.16.0

== 3.46.6

* update
** Gorgon –&gt; YEX 超时从250ms 到 260ms。

== 3.46.7

* bugfix
** 新增对预取品牌广告到yex的流量限制：当链接池中pending线程数大于0时，nominateAsync方法2秒内不再给yex发送请求

== 3.46.8

* bugfix
** 修复品牌广告生产者创意状态未过滤问题

== 3.47.0

* 功能
** 品牌广告数据由本地生产，暂停对品牌广告生产者的使用

== 3.47.1

* 功能
** 增加Panda平板广告拦截器的功能
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2880[jira]

== 3.47.2

* 功能
** 修改prebrand vender对接请求，渲染广告落地页为空问题

== 3.48.0

* 功能
** 品牌广告支持青少年模式

== 3.48.1

* 功能
** 支持读取idfv和词典定义的dimei，并将其处理成deviceid
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2900[jira]

== 3.48.2

* fix
** 修复无效设备ID过滤器导致的deviceId异常的问题
** Before: 过滤器针对deviceId进行过滤，例如有A、B、C三个ID，A是非法ID，B是有效ID，针对deviceId过滤会导致有效的B ID不会被使用，而非法的A ID被当作deviceId后被过滤器过滤掉。
** After: 过滤器针对所有的设备ID进行过滤，非法的ID被替换为&quot;"，在deviceId的处理过程中则不会使用，最终生成的deviceId将是合法ID或为"&quot;。

== 3.48.3

* 功能
** 屏蔽聚合广告位需求新增的样式，cd config配置样式id

== 3.49.0

* 功能
** 修改品牌微信小程序id获取逻辑
** 修改原生开屏、开屏回收、开屏首刷字段查询sql

== 3.49.1

* 功能
** 修复processNominatedAds 传入了immutable List 的问题

== 3.49.2

* 功能
** 修复品牌请求竞价上报设备id问题

== 3.49.3

* 功能
** 统计gorgon host字段

== 3.50.0

* 功能
** 优选合并广告位需求
** https://jira.inner.youdao.com/browse/YOUXUAN-249[jira]

== 3.50.1

* 功能
** 支持定向查词
** https://jira.inner.youdao.com/browse/YOUXUAN-350[jira]

== 3.50.2

* 功能
* 修改解析屏幕宽高参数异常问题
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2923[jira]
* 移除yex接口限流，修改连接池监控
* 提供 gitlab merge request template。
* 开机首刷深拷贝问题修改，OOM

== 3.51.0

* 功能
** 安卓下载型广告新增字段 appVersion
** https://jira.inner.youdao.com/browse/ZHIXUAN-4826[jira]

== 3.51.1

* bugfix
** 修复 rtd 中 DownloadAppInfo 新增字段导致显示异常

== 3.52.0

* 功能
** 升级使用jdk17

== 3.53.0

* 功能
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2922[支持品牌广告过滤器]

== 3.53.1

* 功能
** 修改词典开屏创意多样式视频渲染问题
** 增加品牌广告提名的全局开关，用于在线上因品牌投放导致紧急故障出现时，快速恢复服务
** https://jira.inner.youdao.com/browse/YOUXUAN-433[jira]

== 3.54.0

* 功能
** https://jira.inner.youdao.com/browse/YEX-261[支持穿山甲协议更新，新增字段]

== 3.55.0

* 功能
** 词典开屏pv增加用户id

== 3.56.0

* 功能
** 更新词典敏感词

== 3.56.1

* bugfix
** 修复eadd接口无法宏替换oaid字段的问题

== 3.57.0

* 功能
* 实时接口支持品牌下发mainimage1 等元素
* https://jira.inner.youdao.com/browse/YOUXUAN-698[jira]

== 3.58.0

* 功能
** 从venus获取性别特征，并记录到品牌的pv日志中
** 增加request.s 和 prebrand.s 的brand pv bid 日志记录
** 品牌展示 点击日志增加vender_id 字段
** 收集已安装列表接口，记录并落地 oaid,uuid,dimei 等字段
** https://jira.inner.youdao.com/browse/YOUXUAN-691[jira]
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2946[jira]

== 3.59.0

* 功能
** 支持下发`dictPostId`
** https://jira.inner.youdao.com/browse/ZHIXUAN-4836[jira]

== 3.60.0

* 功能
** 品牌性别定向
** https://jira.inner.youdao.com/browse/YOUXUAN-691[jira]

== 3.61.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-717[V1.3-vender的投放和统计与自然量拆分]

== 3.61.1

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-735[修复dsp video 物料问题 ]

== 3.61.2

* 功能
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2956[修改视频参数配置]

== 3.61.3

* bugfix
** https://jira.inner.youdao.com/browse/YOUXUAN-737[vender id校验方式改为完整匹配]

== 3.61.4

* 0day fix
** 修复fastjson漏洞(https://security.netease.com/post/detail/?id=77)。
** 升级ead-ip-core到1.16.4。
** 升级ead-ip-core的fastjson版本到1.2.83。

== 3.62.0

* fix
** 修改dockerfile，指定java进程的时区

== 3.63.0

* 功能
** https://jira.inner.youdao.com/browse/YEX-270[效果广告响应结果新增ecpm字段]

== 3.63.1

* 功能
** 修改品牌生产广告周期为3天

== 3.64.0

* 功能
** 生产者使用jdk17

== 3.64.1

* 功能
** android 应用市场下载方式增加是否为安卓sdk流量的判断，api等其他对接方式暂不支持
** https://jira.inner.youdao.com/browse/ZHIXUAN-4972[jira]

== 3.65.0

* 功能
** gorgon -&gt; bs 客户端联通状态状态新增监控

== 3.66.0

* 功能
** 词典视频详情页广告
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2959[jira]

== 3.66.1

* 功能
** 修改下发的cpm价格
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2980[jira]

== 3.66.2

* 功能
** 升级ead-ip-core版本号，去掉advance ip库逻辑。

== 3.67.0

* 功能
** 1、支持yex返回的小程序字段 2、将给可以发送给dsp的字段迁移到OpenRtbYDExtForDsp中
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2979[jira]

== 3.68.0

* 功能
** 1、品牌广告新增空设备号过滤逻辑
** 2、vender请求广告不受cpm展示量上限限制
** https://jira.inner.youdao.com/browse/YOUXUAN-803[jira]

== 3.68.1

* 功能
** 修改sdk 参数校验日志级别

== 3.68.2

* 功能
** 升级ead-ip-core版本号，加上advanced ip库逻辑。

== 3.69.0

* 功能
** 品牌广告支持开屏广告全屏点击可跳转白名单配置
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2987[jira]

== 3.70.0

* 功能
** 词典品牌广告预取接口重构
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=200306217[wiki]

== 3.70.1

* bugfix
** 品牌广告开屏可点击 bugfix

== 3.71.0

* 功能
** 优选摇一摇修改
** https://jira.inner.youdao.com/browse/YOUXUAN-907[jira]

== 3.72.0

* 功能
** 广告请求新增 appInstalled 字段

== 3.72.1

* bugfix
** 品牌广告宏替换当字段内容为空时，保留原始宏

== 3.73.0

* 功能
** 优选投放后台增加年龄、身份定向&amp;性别定向优化
** https://jira.inner.youdao.com/browse/YOUXUAN-916[jira]

== 3.74.0

* 功能
** 删除eadd接口的效果广告逻辑，实时接口针对词典开屏广告位不出品牌广告
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3020[jira]

== 3.74.1

* bugfix
** 修复gauge重复初始化问题

== 3.75.0

* 功能
** 针对预取接口prefetch.s，达到当日展示量上限的广告可以出现在非当日的投放计划中
** https://jira.inner.youdao.com/browse/YOUXUAN-975[jira]

== 3.75.1

* bugfix
** 修改bs dispatch cluster gauge初始化位置

== 3.76.0

* 功能
** 优选开屏和信息流广告位增加滑动互动类型
** https://jira.inner.youdao.com/browse/YOUXUAN-969[jira]

== 3.77.0

* 功能
** 品牌广告支持暗夜模式主图元素darkmainimage

== 3.77.1

* 优化
** 升级`simplenet`至`2.3.1`

== 3.78.0

* 功能
** openrtb 请求传递 provinceName 和 cityName

== 3.79.0

* 功能
** 品牌广告支持精准定向，接入词典-杭研标签
** https://jira.inner.youdao.com/browse/YOUXUAN-991[jira]

== 3.79.1

* bugfix
** 修复brandDeviceId中deviceId可能为null的bug

== 3.80.0

* 功能
** 品牌广告支持基于应用版本号过滤
** https://jira.inner.youdao.com/browse/YOUXUAN-1002[jira]

== 3.81.0

* 功能
** 实时接口效果及rtb 广告支持开启全屏点击
** https://jira.inner.youdao.com/browse/YOUXUAN-1014[jira]

== 3.82.0

* 优化
** 修复gorgon生产者druid 查询超时问题
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3029[jira]

== 3.82.1

* 优化
** 应用下载信息中的图片地址改为https
** https://jira.inner.youdao.com/browse/ZHIXUAN-5044[jira]

== 3.83.0

* 需求
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=226263770[prd]

== 3.84.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1008[V1.5-品牌广告支持设备号的md5类型宏参数替换 &amp; 优选后台增加推送“无设备信息”类流量开关]
* 文档
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=223976715
* 功能
** 宏替换支持md5参数替换

== 3.84.1

* bugfix
** brandPv w/h 整形解析出错

== 3.84.2

* refactor
** 代码优化

== 3.84.3

* 功能
** apk下载上报链接增加广告位id，便于排查api对接问题

== 3.85.0

* 功能
** 优选广告样式支持多视频元素

== 3.86.0

* 功能
** 移除对/global mfs配置文件的依赖，迁到/mfs_ead

== 3.86.1

* bugfix
** prefetch.s接口当品牌广告无投放计划时，若可以填充效果广告，则全部填充效果广告
** https://jira.inner.youdao.com/browse/YOUXUAN-1021[jira]
** 移除对/global mfs配置文件的依赖，迁到/mfs_ead

== 3.87.0

* 功能
** 新增设备相关请求参数
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3046[jira]

== 3.87.1

* 功能
** apk下载上报链接增加上报所需的参数
** https://jira.inner.youdao.com/browse/ZHIXUAN-5054[jira]

== 3.88.0

* 功能
** 支持在ecpm字段下发yex中竞价获胜的价格
** https://jira.inner.youdao.com/browse/ZHIXUAN-5108[jira]
** https://jira.inner.youdao.com/browse/ZHIXUAN-5054[jira]

== 3.88.1

* 功能
** 添加 brandPv 统计上报数据

== 3.89.0

* 需求
** BidRequest 请求中的 App 新增字段 isYdMedia
** jira：https://jira.inner.youdao.com/browse/BIDSYSTEM-3047

== 3.89.1

* 功能
* 添加 brandPv 统计上报数据

== 3.90.0

* fix
** https://jira.inner.youdao.com/browse/YOUXUAN-1052[修复`实时接口无法召回开屏广告位下的品牌广告`的问题]

== 3.90.1

* fix
** 修改品牌广告版本号过滤，jsSdk 获取不到keyFrom 版本号问题

== 3.90.2

* fix
** 修改品牌广告版本号过滤，优化可识别的appVersion的格式，识别不出的默认版本号为0.0.0

== 3.91.0

* 需求
** sdk开屏交互方式字段标准化
** https://jira.inner.youdao.com/browse/YOUXUAN-1046[jira]

== 3.92.0

* 功能
* 摇一摇+滑动互动
* prd: https://confluence.inner.youdao.com/pages/viewpage.action?pageId=235357913
* jira: https://jira.inner.youdao.com/browse/YOUXUAN-1062

== 3.92.1

* 需求
** 修复多主图元素不支持开屏多尺寸的问题
** https://jira.inner.youdao.com/browse/YOUXUAN-1077[jira]

== 3.92.2

* bug fix
** pv数据 ip 取值错误

== 3.92.3

* 功能
** 品牌日志添加补充设备号相关字段

== 3.93.0

* 功能
** 品牌广告支持从设备库补充设备号
** https://jira.inner.youdao.com/browse/YOUXUAN-1034[jira]

== 3.93.1

* fix
** 修复access log记录的请求url过长被截断的问题

== 3.94.0

* 需求
** 推广组支持配置第三方监测链接不替换宏参数
** https://jira.inner.youdao.com/browse/ZHIXUAN-5156[jira]

== 3.95.0

* 功能
** https://jira.inner.youdao.com/browse/YEX-294[yex响应结果支持摇一摇和滑动互动]
** 删除程序化对接中对视频广告title物料的强制要求
** prefetch.s接口下发的realTimeout参数支持可配置

== 3.96.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1063[品牌广告支持频控]

== 3.97.0

* 需求
** 新增开屏文案参数支持接口下发
** https://jira.inner.youdao.com/browse/YOUXUAN-1092[jira]

== 3.98.0

* 需求
** 新增spt_mkt 参数，支持dsp请求厂商应用市场类广告；下发apk 开始下载事件上报链接
** https://jira.inner.youdao.com/browse/YEX-295[jira]

== 3.99.0

* 需求
** 支持__model__宏替换
** https://jira.inner.youdao.com/browse/ZHIXUAN-5186[jira]

== 3.100.0

* 需求
** 品牌广告支持人群包定向及排除
** https://jira.inner.youdao.com/browse/YOUXUAN-1114[jira]

== 3.100.1

* 需求
** 向bid-server 上报real ip

== 3.101.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1113[新增配置点击交互类型功能，并加入到统计]

== 3.102.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1125[品牌广告点击事件日志增加bid_id、设备号等信息]

== 3.102.1

* bug fix
** 属性设置错误修正

== 3.103.0

* 需求
** 品牌程序化开屏广告支持开屏文案下发
** https://jira.inner.youdao.com/browse/YOUXUAN-1126[jira]

== 3.104.0

* 需求
** yex请求从venus获取异常流量标签数据，并添加到yex请求中

== 3.105.0

* 需求
** 将通过v4.2.4之后安卓SDK版本发起的请求secure值改为默认为true
** https://jira.inner.youdao.com/browse/YOUXUAN-1124[jira]

== 3.106.0

* 需求
** 新增 preCaid 等字段
** jira：https://jira.inner.youdao.com/browse/BIDSYSTEM-3127

== 3.107.0

* 需求
** 更新 gorgon 文档，添加 YEX 展示上报竞胜价格宏参数替换说明

== 3.108.0

* 需求
** 品牌广告根据配置替换下发元素字段名称
** https://jira.inner.youdao.com/browse/YOUXUAN-1140[jira]

== 3.108.1

* 修改
** 将 caid 列表传递给 YEX

== 3.109.0

* 需求
** 支持ios apps 接口数据上报
** https://jira.inner.youdao.com/browse/YOUXUAN-1118[jira]

== 3.110.0

* 修改
** https://jira.inner.youdao.com/browse/BIDSYSTEM-2887[bs新增设备号参数idfv]
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3134[删除品牌广告点击上报中的无效字段]
** https://jira.inner.youdao.com/browse/YOUXUAN-1151[eadd接口实现针对VD广告场景，只下发一个广告]

== 3.111.0

* 需求
** 品牌广告新增开屏多link类型，把优选数据库推广组摇一摇，滑动互动字段合并
** https://jira.inner.youdao.com/browse/YOUXUAN-1137[jira]

== 3.112.0

* 需求
** V1.1-支持有道内部接收deeplink吊起结果和应用安装结果转化事件上报
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=254062677[jira]
* fix
** eadd接口 isSecure 参数不生效

== 3.112.1

* 修改
** 升级dsp-base-ext版本

== 3.113.0

* 修改
** 修改bs返回广告的ecpm系数，0.5 -&gt; 0.4

== 3.113.1

* bug fix
** fix npe

== 3.114.0

* 需求
** 修改品牌广告过滤逻辑，需先过滤后执行程序化替换，解决程序化广告展示率低的问题
** https://jira.inner.youdao.com/browse/YOUXUAN-1156[jira]

== 3.115.0

* 需求
** gorgon/request.s接口轮播广告逻辑优化
** https://jira.inner.youdao.com/browse/YOUXUAN-1155[jira]

== 3.115.1

* 修改
** gorgon/request.s接口轮播广告逻辑优化后，添加耗时的监控

== 3.116.0

----
- 从venus下载yex用户的异常流量分，用于异常监测和流量屏蔽
----

== 3.116.1

----
- bugfix：品牌广告AdItem深拷贝问题
----

== 3.117.0

* 需求
** vender使用的gorgon和yex已经容器化部署并配置了自动扩容缩容，删除之前应对vender流量保稳定的限流配置。
** https://jira.inner.youdao.com/browse/YOUXUAN-1170[jira]
** 修改request.s接口，当品牌广告在yex无响应或者出现异常，且推广组配置了可以出打底广告时，就出打底广告：适用于vender请求，多广告请求，信息流视频广告位请求，轮播请求，

== 3.117.1

----
- 回退修改request.s接口打底逻辑改动，分开等下次上线
----

== 3.118.0

* 需求
** gorgon请求参数增加isu，并支持yex ulink的下发功能
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=262735934[jira]

== 3.118.1

* fix
** gorgon发送给yex的请求id修改为随机生成的uuid

== 3.118.2

* fix
** 修改gorgon eadd接口针对vender的逻辑，确保提名出的广告当前是在计划投放时间内

== 3.119.0

* 需求
** SdkAdSlot 生产者新增字段

== 3.120.0

* 功能
** https://jira.inner.youdao.com/browse/YEX-343[gorgon和YEX支持针对流量和DSP个性化设置超时时长阈值]

== 3.121.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1152[品牌广告支持指定app定向投放]

== 3.122.0

* 功能
** 需要更长超时时间的广告位，修改为访问提供给vender使用的yex（支持对dsp更长的超时时间）

== 3.123.0

* 需求
** request.s 品牌广告响应根据客户下发字段 `unique_report`，表示展示、点击是否仅允许上报一次
** jira：https://jira.inner.youdao.com/browse/YOUXUAN-1182

== 3.124.0

* 需求
** 海外流量定投，优选品牌广告海外地域定向逻辑修改
** https://jira.inner.youdao.com/browse/YOUXUAN-1173[jira]
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=264692396[海外流量定投到国家下一级行政区划技术文档]

== 3.125.0

* 需求
** 针对指定广告位，在响应中新增 winNotice
** jira：https://jira.inner.youdao.com/browse/YEX-352

== 3.125.1

* 功能
** 添加 YEX 响应结果渲染 Timer

== 3.126.0

* 优化
** 优化扩展字段

== 3.126.1

* 优化
** 删除冗余代码

== 3.127.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1186[request接口广告召回优化二期]

== 3.128.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1121[品牌广告请求分析功能]

== 3.129.0

* 需求
** BS 响应竞胜上报链接
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5255

== 3.130.0

* 需求
** 优选品牌广告bid，impr，click日志中增加ip定位的国家id
** jira：https://jira.inner.youdao.com/browse/YOUXUAN-1221

== 3.130.1

* fix
** 品牌广告trace，判断内网时过滤掉ipv6

== 3.131.0

* 需求
** bs返回广告的ecpm系数支持安装广告位和appid配置

== 3.131.1

* fix
** 品牌广告brand_click_v2日志数据，在构造点击上报link时增加国家id。

== 3.132.0

* 功能
** 支持生成caid
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3147[jira]
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=264699671[开发文档]

== 3.133.0

* 优化
** https://jira.inner.youdao.com/browse/YOUXUAN-1207[在品牌广告提名流程中，删除已过期的广告]

== 3.133.1

* bug fix
** npe fix

== 3.134.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1187[品牌广告流量转换器V1]

== 3.134.1

* fix
** 修复品牌广告开屏尺寸配置错误的问题

== 3.134.2

* fix
** 修复品牌程序化流程中，多尺寸元素尺寸适配有误的问题

== 3.134.3

* fix
** prefetch接口修复无cpd广告时，cpm广告无法投放的问题

== 3.134.4

* 功能
** 词典上报phone_mask，去重参数两边的单引号

== 3.134.5

* bugfix
** 修复开屏尺寸配置问题

== 3.135.0

* 功能
** https://jira.inner.youdao.com/browse/YEX-357[gorgon支持媒体传拼多多paid及其版本号]
** https://jira.inner.youdao.com/browse/YEX-358[gorgon支持生成paid]

== 3.136.0

* 功能
** 针对指定广告位，响应下发图片的尺寸
** 将请求参数 rstyleID 透传到 BS
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5264

== 3.137.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1219[品牌广告下发CPM价格]
* 文档
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=268678699[品牌广告下发CPM价格]

== 3.138.0

* 功能
** yex反作弊二期规则, 从venus获取新的异常指标: tm_ivt_data
** 补充trace log
** https://jira.inner.youdao.com/browse/YEX-346[jira]

== 3.138.1

* fix
** 在响应下发 icon、images 的同时，保持原来 iconimage、mainimage 等的下发，保证联调时能兼容之前的方式获取图片

== 3.139.0

* 优化
** 迁移venus

== 3.140.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1224[品牌广告点击和展示统计补充keyfrom字段，用于记录app版本]

== 3.141.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1225[优选支持微信小程序定投]
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=270879580[开发文档]

== 3.141.1

* fix
** 品牌广告展示keyfrom字段使用客户端传入的app version填充

== 3.142.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1218[品牌广告年龄、性别定向添加中国大学mooc特征数据]

== 3.142.1

* fix
** bs client 链接偶发丢失bugfix

== 3.142.2

* fix
** 海外地域定向：当定向大洲时，ip定位结果只有国家缺少省份信息时，被地域定向过滤的问题

== 3.143.0

* 功能
** caid生成入参兼容

== 3.144.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1233[品牌广告支持测试设备直达]

== 3.144.1

* bugfix
** 使用IdentityHashMap替代hashmap；修复品牌广告多adItem生成bidRequest相同时，response被覆盖的bug

== 3.144.2

* bugfix
** 修复测试直达广告中，未设置开屏相关字段的bug

== 3.145.0

* 更新
** https://jira.inner.youdao.com/browse/YOUXUAN-1276[品牌广告新增web端需302调跳转广告位]

== 3.146.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1256[app.s收集的iOS安装信息中只对指定应用安装数据进行更新]

== 3.146.1

* 功能
** deeplink上报链接增加上报所需的参数
** https://jira.inner.youdao.com/browse/ZHIXUAN-5295[jira]

== 3.147.0

* fix
** 秒针caid宏替换修正

== 3.147.1

* fix
** 增加广告位校验异常时的日志信息

== 3.148.0

* 功能
** 安卓请求解析 brand、model 时，若 brand 为空，尝试从配置文件中获取

== 3.149.0

* 功能
** https://jira.inner.youdao.com/browse/YEX-377[增加动态底价参数，只用于yex效果广告]

== 3.150.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1255[品牌频控支持md5类型设备号]

== 3.151.0

* 功能
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=281828651[人群画像分析-杭研数据] ：品牌广告展点日志添加字段。

== 3.152.0

* 功能
** https://jira.inner.youdao.com/browse/ZHIXUAN-5300[安卓下载型广告响应结果中新增应用描述链接字段]

== 3.152.1

* npe fix
** eadd 接口npe修复

== 3.153.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1283[开屏新增扭一扭类型广告]

== 3.154.0

* 需求
** 品牌请求展示点击记录添加caids
** 品牌展示、点击记录新增用户特征字段

== 3.154.1

* fix
** 品牌点击记录添加brand和dimei

== 3.155.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1290[优选支持自定义人群包投放]

== 3.156.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1332[品牌程序化新增人群定向类型参数和推广组id参数]
** 品牌广告pv新增dimei字段

== 3.157.0

* 需求
** 请求参数 rstyleID 支持传递多个样式 ID
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5321
** 响应增加视频类元素宽高尺寸信息下发
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5322
** 为方便迁移 Kafka，Gorgon 异常 Request 暂时不发送 abnormal_sdk_request

== 3.157.1

* bug fix
** NPE修复

== 3.157.2

* bugfix
** REQUEST_STYLE_NAME 未传递导致打印太多 "can't find style by preferred styleName" 日志 fix

== 3.158.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1318[优选支持多视频样式]
* 文档
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=283888018[优选支持多视频样式]

== 3.159.0

* bugfix
** eadd接口丢失orientationMode字段bugfix

== 3.160.0

* 功能
** https://jira.inner.youdao.com/browse/ZHIXUAN-5326[针对广告位配置开屏交互样式]

== 3.161.0

* 功能
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=286001016[品牌精准定向支持caid]

== 3.162.0

* 功能
** eadd 支持ivt
* 需求
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=288217719[V1.2.1YEX角色可见性拆分&amp;筛选逻辑优化]

== 3.163.0

* 优化
** caid 参数接收优化，必须同时传递值和版本号
** youdao BidRequest add caids

== 3.164.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1351[品牌新增随机定向模式]

== 3.165.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1353[品牌频控支持caid]
** https://jira.inner.youdao.com/browse/YOUXUAN-1354[品牌设备号过滤支持caid]

== 3.165.1

* fix
** 品牌广告展示上报caid解析问题fix

== 3.166.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1366[品牌广告实时接口支持下发打底广告]

== 3.166.1

* fix
** ci脚本删除check阶段

== 3.167.0

* 功能
** 响应字段添加样式 ID
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5378

== 3.168.0

* 需求
** https://jira.inner.youdao.com/browse/ZHIXUAN-5350?filter=-1[V1.1-智选支持关键词广告投放]

== 3.168.1

* fix
** gorgon 响应 videos 中视频的宽高直接取封面的宽高尺寸

== 3.168.2

* fix
** randomAge类型Integer -&gt; int，避免出现NPE

== 3.168.3

* fix
** 解析 Video Vast 设置 clk 时，如果 Vast 里面的 ClickThrough 为 deeplink，设置为备用落地页

== 3.168.4

* 优化
** VideoInfo 生产者取消全量查询数据库，新增 STATUS 字段，防止数量太多，datacache 同步太慢

== 3.169.0

* 功能
** https://jira.inner.youdao.com/browse/YOUXUAN-1377[小程序来源优化一期]

== 3.170.0

* 功能
** 品牌广告新增推广组有效性验证接口
** https://jira.inner.youdao.com/browse/YOUXUAN-1371[jira]
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=298857890[wiki]

== 3.171.0

* 功能
** Id2VideoInfo 移到DspAdVaraition
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3230[jira]

== 3.172.0

* 功能
** 删除品牌广告有效设备号过滤器中对caid的认定

== 3.172.1

* 优化
** kafka换为新域名

== 3.172.2

* 更新
** logDB/unionDB/eadb10 端口使用 3328
** 数据库连接使用 FQDN

== 3.173.0

* 优化
** [热启动开屏间隔时间修改为可配置]（https://jira.inner.youdao.com/browse/YOUXUAN-1388）

== 3.173.1

* 优化
** 视频请求物料时长改为：3s ~ 30s
** jira：https://jira.inner.youdao.com/browse/YEX-430

== 3.174.0

* 需求
** https://jira.inner.youdao.com/browse/ZHIXUAN-5410[接入有道传媒]
** https://confluence.inner.youdao.com/pages/viewpage.action?pageId=304786699[开发文档]

== 3.175.0

* 更新
** brand pv新增对品牌广告人群包id列表字段的记录

== 3.176.0

* 需求
** 动态底价作用到 bs 中
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5420
** 出价系数调整到 bs 中
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5419

== 3.177.0

* 更新
** 品牌广告频控redis改为使用域名
** 更新venus版本

== 3.177.1

* 回滚
** 去除出价系数回滚

== 3.178.0

* 需求
** 去除 ecpm 出价系数，使用 bs 的系数，保证统计中的 bidPrice 是乘了系数后的
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5419

== 3.179.0

* 需求
** 品牌广告开屏优化2.1（https://jira.inner.youdao.com/browse/YOUXUAN-1389）

== 3.180.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1406?filter=-1[阿里京东caid宏参替换逻辑优化]

== 3.181.0

* 需求
** https://jira.inner.youdao.com/browse/YEX-436[华为dsp参数对齐]

== 3.181.1

* fix
** brand bid int值转换异常修复

== 3.181.2

* 需求
** 动态底价参数传递给 bs
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5420

== 3.182.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1405[支持过滤无设备号流量功能中排除caid]

== 3.182.1

* 优化
** gorgon文档修正

== 3.183.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1420[优选新增地域定向生效范围控制开关]
* 优化
** 品牌广告将地域定向的逻辑统一放到过滤器，key改为广告位id
** ci cache修正

== 3.184.0

* 优化
** 加密请求支持兼容明文传入的参数(若加密参数和明文参数中有相同字段，优先使用加密参数中的该字段值)

== 3.185.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1427[预取接口下发的效果占位符支持按广告位维度配置]

== 3.185.1

* 需求
** 品牌程序化错误日志新增记录出错deal id

== 3.186.0

* 优化
** eadd相关统计支持记录接口类型字段interfaceType
** 品牌特征新增从杭研field获取app安装列表

== 3.187.0

* 需求
** yex 请求网络类型支持5g
** (doc)[https://confluence.inner.youdao.com/display/~huati/yex+support+5g%2C+add+youdao+open+rtb]

== 3.188.0

* 需求
** 品牌广告展示上报添加env，env为test不发送kafka

== 3.188.1

* bug fix
** 品牌展示上报修正

== 3.188.2

* 优化
** 经纬度解析失败日志改为warn级别

== 3.188.3

* fix
** 临时修复品牌广告多尺寸下mainimage字段可能为空的问题

== 3.188.4

* fix
** 应用市场下载链接支持在API流量场景下发
** https://jira.inner.youdao.com/browse/ZHIXUAN-5568[jira]

== 3.189.0

* 需求
** 品牌广告新增响应字段brandOrderType，标识此广告所属的结算类型，用于存在竞价展示逻辑的媒体判断此广告是否可以直接展示
** https://jira.inner.youdao.com/browse/YOUXUAN-1438[jira]

== 3.190.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1429?filter=-1[【服务端】全链路营销广告]

== 3.191.0

* 需求
** https://confluence.inner.youdao.com/display/ead/Brand-track[迁移品牌广告展示、点击接收功能至独立服务]

== 3.191.1

* fix
** 修复品牌展示上报服务的路径错误

== 3.191.2

* fix
** 修复eadd接口在adItem和adText转换过程中丢失字段的问题

== 3.191.3

* fix
** 修复从YEX请求的安卓下载广告没有取appDescUrl的问题.

== 3.191.4

* 更新
** caid licence更新

== 3.192.0

* 需求
** https://jira.inner.youdao.com/browse/ZHIXUAN-5609[V1.13 优选增加不下发第三方曝光监测的开关]

== 3.192.1

* 需求
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3231[jira]
** 替换docker镜像改为openjdk

== 3.193.0

* 需求
** brand impr 增加 cpm 字段
** jira：https://jira.inner.youdao.com/browse/YOUXUAN-1446

== 3.194.0

* 需求
** https://jira.inner.youdao.com/browse/ZHIXUAN-5646[转化跟踪联调广告支持通过caid、caidMD5、oaidMD5设备号下发测试广告]

== 3.195.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1448[支持全球发音广告]

== 3.195.1

* 优化
** https://jira.inner.youdao.com/browse/YOUXUAN-1458[优化gorgon高频率慢查询sql]

== 3.195.2

* 优化
** openRtb 请求 Geo 填充 counrty

== 3.196.0

* 需求
** yex 增加设备初始化时间字段：deviceStartDuringTime

== 3.196.1

* 优化
** openRtb 请求 Geo 填充的 country 改为 ISO 国家编码

== 3.197.0

* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1465[品牌广告支持ip黑名单]
** https://jira.inner.youdao.com/browse/YOUXUAN-1468[品牌广告视频物料使用https链接下发]

== 3.198.0

* 需求
** yex app 配置是否支持快应用广告开关
** jira：https://jira.inner.youdao.com/browse/YEX-459

== 3.198.1

* bugfix
** 修复randomSelectAdItemFromWeightMap方法空指针问题

== 3.199.0

* 需求
** https://jira.inner.youdao.com/browse/ZHIXUAN-5678[智选支持PO圈和非PO圈流量有同的CPC底价属性（CPC双底价）]

== 3.199.1

* bugfix
* 修复品牌广告设备号传入异常值

== 3.200.0

* 需求
** 品牌广告接入新杭研性别、年龄标签数据
** jira：https://jira.inner.youdao.com/browse/YOUXUAN-1475

== 3.200.1

* 需求
** 更换连接池dbcp-&gt;hikari
** jira：https://jira.inner.youdao.com/browse/BIDSYSTEM-3279

== 3.200.2

* 需求
** 将邮箱安卓端传递的 dToken 记录到 strategyInfo 中，以便记录到 pv 中

== 3.200.3

* bugfix
** Hikari 配置添加 driverClassName

== 3.201.0

* 需求
** 品牌广告AdItem 对象拷贝问题重构，引入Candidate对象，投放过程中不直接修改AdItem的属性值，最大可能减少对象复制，优化内存占用
** jira: https://jira.inner.youdao.com/browse/YOUXUAN-1452

== 3.202.0

* bugfix
** 连接超时时间改为27000000毫秒
** 优化Hikari连接池并配置连接池监控 https://github.com/brettwooldridge/HikariCP/wiki/Dropwizard-Metrics[指标参考]

== 3.203.0

* 需求
** 开屏位置效果广告下发fullScreen参数并默认全屏
** jira: https://jira.inner.youdao.com/browse/ZHIXUAN-5757

== 3.204.0

* 需求
** 支持YDID
** jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5828

== 3.205.0

* 需求
** 

品牌广告第三方检测支持RTBAsia CAID值

----
  iOS 系统国内广告标识，格式为：版本号_值，如20220111_d49569b6998ba9769d38573c62162f0。对应宏：__CAID__
----

----
Morus(王榕盛) | rsw 9/20 14:33
1、具体要求的格式是：最新版本号_caid值；这个是他们的要求；
2、域名为https://trace.rtbasia.com，一级域名不会变；
----

== 3.205.1
* 优化
** 将多个需要在 app side log 中打印的日志迁移到其中
** changelog 使用 asciiDoc，快且支持更多功能

== 3.206.0
* 需求
** 支持优选平台配置全屏点击
** jira：https://jira.inner.youdao.com/browse/YOUXUAN-1489

== 3.207.0
* 需求
** 修复品牌广告md5设备号默认值问题
** jira：https://jira.inner.youdao.com/browse/YOUXUAN-1496


== 3.208.0
* 需求
** 支持inmobi dsp响应banner形式广告
** jira：https://jira.inner.youdao.com/browse/YEX-467

== 3.209.0
* 需求
** 优化prefetch.s接口的日志输出

== 3.210.0
* 需求
** https://jira.inner.youdao.com/browse/YOUXUAN-1501[支持miaozhen媒体参数宏替换]

== 3.210.1
* 优化
** should_log_cd_conf 可以配置是否在指定 hosts上生效

== 3.210.2
* bugfix
** 品牌广告依次处理单个第三方监测的宏替换

== 3.210.3
* bugfix
** OpenRTB BidRequest的 Imp 可能为空，如样式有 app 版本号要求被过滤或者样式被删除
** 提供异常打印异常类型

== 3.211.0
* 需求
** 流量分布需求
** https://jira.inner.youdao.com/browse/ZHIXUAN-5980

== 3.211.1
* BugFix
** 轮播广告总权重大于广告位总权重报警，等于时没问题。

== 3.212.0
* 需求
** 增加品牌广告生产延迟的监控
** 优化生产者代码，selectivityMap的生产使用独立线程，不阻塞广告数据更新的线程

== 3.213.0
* 需求
** 增加vender-gz部署

== 3.214.0
* 需求
** 对vender流量，使用Sentinel对其进行限流
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3306[jira]

== 3.215.0
* 需求
** 对品牌api流量，下发展示上报链接
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3314[jira]

== 3.216.0
* 需求
** gorgon请求增加游戏地图ID等扩展参数
** https://jira.inner.youdao.com/browse/ZHIXUAN-6098[jira]

== 3.217.0
* 需求
** 补齐brand_pv等日志的设备号md5字段
** https://jira.inner.youdao.com/browse/YOUXUAN-1508[jira]

== 3.218.0
* 需求
** 支持渠道使用groupid 请求品牌直投广告
** https://jira.inner.youdao.com/browse/YOUXUAN-1512[jira]

== 3.219.0
* 需求
** 调整 gorgon-to-yex.youdao.com 域名上的超时时间
** 新配置 zk_config_path_bid_client_configs 替代老配置 zk_config_path_bid_client_max_load
** LoadBalanceClient isValid()中, 新增可以再次向服务端发送请求的时间判断逻辑

== 3.219.1
* 优化
** rst 文档里增加 adCat 字段的描述，版本号从 1.3.5 升到 1.3.6

== 3.219.2
* bugfix
** 更改

== 3.220.0
* bugfix
** 修改品牌广告clk字段没有下发原始落地页链接的问题
** [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1521)

== 3.220.1
* bugfix
** 修复品牌程序化广告落地页链接未替换ori_dest_url问题

== 3.221.0
* 需求
** 请求广告时将手机品牌标准化;
* 技术文档：https://confluence.inner.youdao.com/pages/viewpage.action?pageId=370082302
** Jira: https://jira.inner.youdao.com/browse/ZHIXUAN-6134
** PRD: https://confluence.inner.youdao.com/pages/viewpage.action?pageId=367650073

== 3.221.1
* 需求
** 增加手机品牌关键词匹配策略(由于部分流量传参不符合规范. e.g. dn/ua等传错,所以增加此策略保证手机品牌定向时尽可能命中)
** 技术文档：https://confluence.inner.youdao.com/pages/viewpage.action?pageId=370082302
** Jira: https://jira.inner.youdao.com/browse/ZHIXUAN-6134
** PRD: https://confluence.inner.youdao.com/pages/viewpage.action?pageId=367650073
* 优化
** 继续调大等待 YEX 的超时时间（300ms --> 330ms）

== 3.221.2
* 优化
** 修复云笔记开屏广告clickType兼容问题
** [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1535)

== 3.222.0
* 需求
** gorgon协议支持传包名作为扩展参数
** [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6219)

== 3.223.0
* 需求
** 品牌广告支持动态配置程序化对接的元素
** [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1525)
** [doc](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=375653826)

== 3.223.1
* bugfix
** 从OpenRTB提名的效果广告返回的styleId应为Long类型

== 3.224.0
* 需求
** 京东dsp合成链接投放vender, 不下发deeplink
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3329

== 3.225.0
* 需求
** 支持按abtest id屏蔽广告
** https://jira.inner.youdao.com/browse/BIDSYSTEM-3331

== 3.225.1
* 需求
** 更新 CAID licence，有效期至25年7月31日

== 3.226.0
* 需求
** 品牌程序化支持gifimage元素投放(https://jira.inner.youdao.com/browse/YOUXUAN-1537)
** rollback 3.224.0 code

== 3.227.0
* 需求
** JIRA[CAID版本升级](https://jira.inner.youdao.com/browse/ZHIXUAN-6281)
** 本次 CAID 最新版算法包（返回20230330和20250325值）的版本。其中新算法license密钥请替换为：31373835343237323030，有效期至26年7月31日。

== 3.228.0
* 需求
- 优选支持联合频控
- [Jira](https://jira.inner.youdao.com/browse/YOUXUAN-1533)
- [PRD](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=373140068)
- [相关文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=373159819)

== 3.229.0
* 优化
** gorgon_hostname 优化，提供更短的名称；
** [jira](https://jira.inner.youdao.com/browse/EADSYS-146)

== 3.230.0
* 优化
** 优选支持三合一交互
** [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1557)

== 3.231.0
* 需求
- 下线 gorgon 中品牌广告上报展示代码
- [Jira](https://jira.inner.youdao.com/browse/YOUXUAN-1523)
- [相关文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=369461485)

== 3.232.0
* 需求
** 搭建贵州 Gorgon-YEX 集群；
** [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3339)

== 3.233.0
* 优化
** 更新sdk传参中需要过滤的异常设备号类型

== 3.234.0
* bugfix
** 修改oaidMd5等设备取值问题
** [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1587)

== 3.235.0
* 需求
** 优选支持开屏灵敏度控制
** [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1568)
** [技术方案](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=379942230)