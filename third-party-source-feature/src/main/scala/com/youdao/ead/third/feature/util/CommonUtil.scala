package com.youdao.ead.third.feature.util

import com.youdao.ead.third.feature.HyFeatureToVenus.writeErrorCount
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.apache.spark.sql.DataFrame
import org.slf4j.LoggerFactory
import outfox.venus.client2.VenusClient
import outfox.venus.client2.data.{BasicData, Data, Id}

import collection.JavaConverters._
import java.time.LocalDate
import java.time.format.DateTimeFormatter

object CommonUtil {
  private val logger = LoggerFactory.getLogger(CommonUtil.getClass)

  // 杭研中存储的app名称与app双端包名的映射
  private val pkgNameMap = Map(
    "京东" -> "com.jingdong.app.mall,com.360buy.jdmobile",
    "淘宝" -> "com.taobao.taobao,com.taobao.taobao4iphone",
    "天猫" -> "com.tmall.wireless",
    "抖音" -> "com.ss.iphone.ugc.Aweme,com.ss.android.ugc.aweme",
    "抖音极速版" -> "com.ss.android.ugc.aweme.lite,com.ss.iphone.ugc.aweme.lite"
  )

  val dateTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

  val eadHdfsTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("/yyyy/MM/dd")
  /**
   * 当前的年份
   */
  val currentYear: Integer = Integer.parseInt(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy")))

  /**
   * 更新增量数据
   */
  val INCREMENTAL_MODE = "incremental"
  /**
   * 更新全量数据
   */
  val FULL_AMOUNT_MODE = "fullAmount"

  /**
   * 无效设备号集合
   */
  val INVALID_DEVICE_ID_SEQ = Set("UNKNOWN", "00000000-0000-0000-0000-000000000000", "00000000000000000000000000000000")

  /**
   * 全量数据hdfs路径
   */
  val FULL_AMOUNT_HDFS = "hdfs://eadhadoop/user/da_luna/hive_db/dw_luna_wow.db/dw_dict_netease_label"

  /**
   * 增量数据hdfs路径
   */
  val INCREMENTAL_HDFS = "hdfs://eadhadoop/user/da_luna/hive_db/dw_luna_wow.db/dw_dict_netease_label_day"

  /**
   * 中国大学mooc特征数据存储的hdfs路径
   */
  val MOOC_HIVE_HDFS = "hdfs://eadhadoop/user/da_cr/hive_db/edw_ads.db/ads_moc_device_detail_d"

  /**
   * 将dataframe中数据写入venus
   *
   * @param df          源数据dataframe，venus中的key是deviceId
   * @param featCols    特征字段名列表
   * @param ageFeatCols 年龄特征字段名列表，用于单独校验年龄特征的有效性
   * @param field       写入venus的field名称
   * @param dataPrefix  要写入的data前缀，若为空则表示不需要前缀
   */
  def writeToVenus(df: DataFrame, featCols: Seq[String], ageFeatCols: Seq[String], field: String, appsColName: String, dataPrefix: String): Unit = {
    df.repartition(Config.venusClients).foreachPartition(partition => {
      val venusClient = new VenusClient(Config.venusAddress, Config.venusRedisUri, Config.venusTimeout)
      val prefix = if (StringUtils.isBlank(dataPrefix)) "" else dataPrefix + "_"
      partition.foreach(row => {
        val deviceId = row.getAs[String]("deviceId")

        if (StringUtils.isNotBlank(deviceId) && !INVALID_DEVICE_ID_SEQ.contains(deviceId)) {
          val deviceIdUpper = StringUtils.upperCase(deviceId)
          try {
            // 避免多次与venus通信，一次性更新完毕该field的内容
            val oldDataCollection = venusClient.getAllData(deviceIdUpper, field)
            var dataMap = if (CollectionUtils.isEmpty(oldDataCollection)) {
              Map.empty[String, Data]
            } else {
              oldDataCollection.asScala.map(c => c.getId.getDataId -> c).toMap
            }
            for (colname: String <- featCols) {
              val featAndValid =
                if (StringUtils.equalsAny(colname, ageFeatCols: _*)) {
                  val feat = row.getAs[Int](colname)
                  (feat, feat < 100 && feat > 0)
                } else if (StringUtils.equalsIgnoreCase(colname, appsColName)) {
                  // 将杭研数据库中存储的应用名映射为对应的包名
                  val appList = row.getAs[Seq[String]](colname)
                  if (appList == null || appList.isEmpty) {
                    ("", false)
                  } else {
                    val feat = appList.map(c => pkgNameMap.getOrElse(c, "")).filter(StringUtils.isNotBlank(_)).mkString(",")
                    (feat, StringUtils.isNotBlank(feat))
                  }
                } else {
                  val feat = row.getAs[String](colname)
                  (feat, StringUtils.isNotBlank(feat))
                }
              if (featAndValid._2) {
                val dataId = prefix + s"$colname"
                val stringFeatData = new BasicData(new Id(deviceIdUpper, field, dataId),
                  String.valueOf(featAndValid._1))
                dataMap += (dataId -> stringFeatData)
              }
            }
            if(dataMap.nonEmpty){
              venusClient.replaceByField(deviceIdUpper, field, dataMap.asJava)
            }
          } catch {
            case ex: Throwable => {
              writeErrorCount.addAndGet(1)
              logger.error("write to venus error: ", ex)
            }
          }

        }
      })
      venusClient.close()
    })
  }

  /**
   * 拼接多个路径字符串
   *
   * @param paths 路径
   * @return
   */
  def concatPathString(root: String, paths: String*): String = {
    paths.foldLeft(root) {
      (origin, path) => {
        if (origin.endsWith("/")) {
          origin + path
        } else {
          origin + "/" + path
        }
      }
    }
  }

}
