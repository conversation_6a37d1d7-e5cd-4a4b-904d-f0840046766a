package models

import org.joda.time.Period

/**
 * <AUTHOR>
 */
case class SimpleQuery(broker: String, dataSource: String, interval: Interval, granularity: String, var filter:
List[Filter], groupBy: List[String], having: List[Filter], aggregation: List[String], limit: Long, orderBy: List[OrderBy],
                       dropZeros: Boolean, relativeInterval: String, timeoutSeconds: Int)

case class Query(dataSource: String, interval: Interval, granularity: Period, filter: Filter, groupBy:
Array[String], having: Having, limit: Limit)

case class Limit(limit: Int, orderBys: Array[OrderBy])

case class OrderBy(dimension: String, ascending: Boolean)

case class Interval(start: String, end: String)

abstract class Filter

case class SelectFilter(dimension: String, value: String) extends Filter
case class InFilter(dimension: String, values: String) extends Filter
case class EqualToFilter(dimension: String, value: String) extends Filter
case class GreaterThanFilter(dimension: String, value: String) extends Filter
case class LessThanFilter(dimension: String, value: String) extends Filter
case class IsNullFilter(dimension: String) extends Filter
case class NotNullFilter(dimension: String) extends Filter
case class LikeFilter(dimension: String, value: String) extends Filter
case class RegexFilter(dimension: String, value: String) extends Filter

case class OrFilter(filters: Array[Filter]) extends Filter

case class AndFilter(filters: Array[Filter]) extends Filter

case class NotFilter(filter: Filter) extends Filter

abstract class Having

case class EqualHaving(metric: String, value: Double) extends Having

case class GreaterThanHaving(metric: String, value: Double) extends Having

case class LessThanHaving(metric: String, value: Double) extends Having

case class AndHaving(havings: Array[Having]) extends Having

case class OrHaving(havings: Array[Having]) extends Having

case class NotHaving(having: Having) extends Having