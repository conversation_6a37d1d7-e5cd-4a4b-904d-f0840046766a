@(user: String)(message: String)(userTable: Set[String])

<!DOCTYPE html>
<html>
    <head>
        @head("Quipu Camayos")
        <link rel="stylesheet" media="screen" href="@routes.Assets.at("stylesheets/simple-query.css")">
        <script src="@routes.Assets.at("javascripts/simple-query.js")" type="text/javascript"></script>
    </head>
    <style>
        .main-content::after {
            display: block;
            content: '';
            position: fixed;
            left: 0;
            top: 0;
            height: 100%;
            width: 100%;
            pointer-events: none;
            opacity: 0.1;
            background-color: grey;
            background-image: url(@routes.WaterMarkAction.waterMark());
        }
    </style>
    <body class="main-content">
        @navbar(user)(userTable)
        <div class="container-fluid">
            <div class="row-fluid">
                <h1 style="font-style: italic; text-align: center; position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%)">
                @message
                </h1>
            </div>
        </div>
        <footer id="footer" class="footer">
            <div class="container">
            </div>
        </footer>
    </body>
</html>