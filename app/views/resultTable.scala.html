@(columns: List[(String, String)])(result: Option[(Long, play.api.libs.json.JsValue, String)])(msg: String)
    @import play.api.libs.json.JsArray

    <div id="table-rtt">
        @result match {
            case Some(content) => {
                <label onclick="navigator.clipboard.writeText('@result.get._3')">Used @result.get._1 ms.</label>
            }
            case _ => {}
        }
        @if(msg != null && msg.nonEmpty) {
            <span style="color: red; margin-left: 400px;">@msg</span>
        }
    </div>
    <div class="table-responsive">
        <script>
            $(function () {
                var $table = $('#table');
                $('#toolbar').find('select').change(function () {
                $table.bootstrapTable('refreshOptions', {
                  exportDataType: $(this).val()
                });
              });
            });
            function enhancedSorter(a, b) {
                a = parseFloat(a);
                b = parseFloat(b);
                if (a < b) return -1;
                if (a > b) return 1;
                return 0;
            }

            /*
            * 更改image size,点击image图片size变大,再次点击变小
            * */
            function imgResize(e) {
                if (e.tagName === "IMG")
                {
                    var rate = 5 - Math.min(Math.floor(e.width / e.height), 3);
                    if (e.height <= 80)
                        e.style.height = (e.height * rate) + "px";
                    else
                        e.style.height = (e.height / rate) + "px";
                }
            }

            function copyQueryJson() {

            }
        </script>
    @result match {
        case Some(content) => {
            <div class="bs-bars pull-left">
                <div id="toolbar">
                    <select class="form-control">
                        <option value="">Export Basic</option>
                        <option value="all">Export All</option>
                    </select>
                </div>
            </div>
            <table id="table" data-toggle="table" data-classes="table table-hover table-condensed" data-pagination="true"
            data-search="true" data-show-refresh="true" data-page-size="50" data-page-list="[20,50,100,200,1000,10000,100000,1000000]" data-striped="true"
            data-toolbar="#toolbar" data-show-export="true" data-show-columns="true" data-sticky-header="true">
                <thead>
                    <tr>
                        <th data-sortable="true">时间</th>
                        @columns.map { column =>
                            <th data-sortable="true" data-sorter="enhancedSorter">@column._2</th>
                        }
                    </tr>
                </thead>
                <tbody>
                @for(obj <- result.get._2.as[JsArray].value) {
                    <tr>
                        <td>@{
                            (obj \ "timestamp").as[String]
                        }</td>
                        @columns.map { column =>
                            <td>@Html(
                                Application.toString(obj \ "result" \ column._1)
                            )</td>
                        }
                    </tr>
                }
                </tbody>
            </table>
        }
        case None => {
        }
    }
    </div>