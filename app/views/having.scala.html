@(form: Form[SimpleQuery])(metrics: List[(String, String)])
@implicitField = @{
    helper.FieldConstructor(bootstrapFieldConstructorTemplate_noLabel.f)
}
<div class="addable-wrapper">
    <label for="select2-single-input-sm" class="control-label">having</label>
    @helper.repeat(form("having"), min = form("having").indexes.length) { having =>
        <div class="row @if(having.hasErrors) {has-error}">
            <div class="col-xs-5 col-sm-5 col-md-5 col-lg-5 col-first">
            @helper.input(form(having.name + ".dimension")) { (id, name, value, args) =>
                <select id="@id" name="@name" class="form-control select2 addable-value">
                    <option></option>
                    @metrics.map { option =>
                        @defining(if(option._1.equals(value.get)) {
                            "selected=\"selected"
                        }) { selected =>
                            <option @selected value="@option._1">@option._2</option>
                        }
                    }
                </select>
            }
            </div>
            <div class="col-xs-3 col-sm-3 col-md-2 col-lg-2 col">
            @helper.select(form(having.name + ".type"), List(("eq", "eq"), ("gt", "gt"), ("lt", "lt")),
                'class -> "form-control select2 addable-value", 'value -> form.data.get(having.name + ".type"))
            </div>
            <div class="col-xs-4 col-sm-4 col-md-5 col-lg-5 col-last">
            @helper.input(form(having.name + ".value")) { (id, name, value, args) =>
                <div class="input-group select2-bootstrap-append">
                    <input type="text" name="@name" class="form-control addable-value"
                    value="@{
                        form.data.get(name)
                    }">
                    <span class="input-group-btn">
                    @if(having.label.endsWith("." + (form("having").indexes.length - 1))) {
                        <button class="btn btn-info query-add-btn" type="button"> + </button>
                    } else {
                        <button class="btn btn-danger query-remove-btn" type="button">-</button>
                    }
                    </span>
                </div>
            }
            </div>
            <span class="help-block">@having.errors.map(_.message).mkString(", ")</span>
        </div>
    }
</div>