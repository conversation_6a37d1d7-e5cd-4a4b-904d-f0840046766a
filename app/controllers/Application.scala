package controllers

import controllers.convert.{PortraitByEadDmpUtil, TimeDifferenceUtil}
import data.Location.{cityMap, countryMap}
import dispatch.Defaults._
import dispatch._
import models._
import org.apache.commons.lang.StringEscapeUtils
import org.apache.commons.lang.math.NumberUtils
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import play.api.Logger
import play.api.data.Forms._
import play.api.data.format.Formatter
import play.api.data.{Form, FormError, Forms}
import play.api.libs.json._
import play.api.mvc.{Action, AnyContent, Controller}

import java.text.DecimalFormat
import java.util.Objects
import java.util.concurrent.{ExecutorService, Executors}
import scala.collection.mutable.ListBuffer
import scala.concurrent.Await
import scala.concurrent.duration._
import scala.language.postfixOps

object Application extends Controller {
  private val log: Logger = Logger(this.getClass)

  val THIRD_JUMP_IMPR: String = "3rd_jump_impr"
  val THIRD_JUMP_CLICK: String = "3rd_jump_click"
  val THIRD_JUMP_CHARGE: String = "3rd_jump_charge"

  // post-aggregations量
  val POST_AGGREGATIONS: Set[String] = Set(THIRD_JUMP_CHARGE, THIRD_JUMP_CLICK, THIRD_JUMP_IMPR)
  val hypers: Set[String] = Set("ub", "ui", "uc")
  val USER_NAME: String = "userName"
  val IS_LDAP: String = "isLdap"

  // 1元 = 100分
  val CURRENCY_RATIO_YUAN_FEN = 100

  def toString(json: JsValue): String = {
    json match {
      //      case j: JsNumber => if (j.value.isDecimalDouble && j.value.doubleValue() <= 1) new DecimalFormat("#.##").format(j.value.doubleValue()) else j.as[Long] + ""
      case j: JsNumber => new DecimalFormat("#.######").format(j.as[Double])
      case j: JsUndefined => "null"
      case JsNull => "null"
      case j: JsValue => j.as[String]
      case _ => "undefined"
    }
  }

  //  private val NEW_BROKER: String = "http://qt105x.corp.youdao.com:9080/druid/v2"
  private val NEW_BROKER: String = "http://druid-broker-yy.inner.youdao.com/druid/v2"
  implicit val filterFormatter = new Formatter[Filter] {
    override def bind(key: String, data: Map[String, String]): Either[Seq[FormError], Filter] = {
      val dimension: String = data(key + ".dimension")
      val value: String = data(key + ".value")
      val filterType: String = data(key + ".type")
      filterType match {
        case "eq" => Right(SelectFilter(dimension, value))
        case "ne" => Right(NotFilter(SelectFilter(dimension, value)))
        case "in" => Right(InFilter(dimension, value))
        case "notIn" => Right(NotFilter(InFilter(dimension, value)))
        case "nn" => Right(NotNullFilter(dimension))
        case "isnull" => Right(IsNullFilter(dimension))
        case "gt" => Right(GreaterThanFilter(dimension, value))
        case "lt" => Right(LessThanFilter(dimension, value))
        case "like" => Right(LikeFilter(dimension, value))
        case "regex" => Right(RegexFilter(dimension, value))
      }
    }

    override def unbind(key: String, filter: Filter): Map[String, String] = {
      filter match {
        case SelectFilter(dimension, value) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> value, key + ".type" -> "eq", key -> "")
        case InFilter(dimension, value) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> value, key + ".type" -> "in", key -> "")
        case GreaterThanFilter(dimension, value) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> value, key + ".type" -> "gt", key -> "")
        case LessThanFilter(dimension, value) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> value, key + ".type" -> "lt", key -> "")
        case NotFilter(SelectFilter(dimension, value)) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> value, key + ".type" -> "ne", key -> "")
        case NotFilter(InFilter(dimension, value)) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> value, key + ".type" -> "notIn", key -> "")
        case IsNullFilter(dimension) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> "", key + ".type" -> "isnull", key -> "")
        case NotNullFilter(dimension) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> "", key + ".type" -> "nn", key -> "")
        case LikeFilter(dimension, value) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> value, key + ".type" -> "like", key -> "")
        case RegexFilter(dimension, value) =>
          Map(key + ".dimension" -> dimension, key + ".value" -> value, key + ".type" -> "regex", key -> "")
      }
    }
  }


  private val simpleQueryMapping = mapping(
    "broker" -> default(text, NEW_BROKER),
    "dataSource" -> text,
    "interval" -> mapping("start" -> text, "end" -> text)(Interval.apply)(Interval.unapply).verifying("Interval is " +
      "invalid!", interval => try {
      interval.start.compareTo(interval.end) < 0 &&
        org.joda.time.Interval.parse(interval.start + "/" + interval.end) != null
    } catch {
      case _: Throwable => false
    }),
    "granularity" -> default(text, "day"),
    "filter" -> list(Forms.of(filterFormatter)),
    "groupBy" -> list(text),
    "having" -> list(Forms.of(filterFormatter)),
    "aggregation" -> list(text).verifying("Must has at least one aggregation!", list => list.nonEmpty),
    "limit" -> longNumber,
    "orderBy" -> list(mapping("dimension" -> text, "ascending" -> boolean)(OrderBy.apply)(OrderBy.unapply)),
    "dropZeros" -> default(boolean, true),
    "relativeInterval" -> default(text, ""),
    "timeoutSeconds" -> default(number, 300)
  )(SimpleQuery.apply)(SimpleQuery.unapply)

  val form = Form(simpleQueryMapping)

  def buildRequest(query: SimpleQuery): Either[String, JsObject] = {
    query.groupBy.size match {
      case 0 => buildTimeseriesQuery(query)
      case _ => buildGroupByRequest(query)
    }
  }

  private val doubleSumMetrics: Set[String] = Set(
    "bid_predict_cost",
    "ctr",
    "pr_cvr",
    "ctr_of_impr",
    "pr_cvr_of_impr",
    "original_cur_impr_price",
    "original_cur_bid_price"
  )

  def buildTimeseriesQuery(query: SimpleQuery): Either[String, JsObject] = {
    var fields: List[(String, JsValue)] = "queryType" -> JsString("timeseries") ::
      "dataSource" -> JsString({
        if (query.dataSource.endsWith("_new")) {
          query.dataSource.dropRight(4)
        } else {
          query.dataSource
        }
      }) ::
      buildGranularity(query) ::
      "intervals" -> Json.arr(
        org.joda.time.Interval.parse(query.interval.start + "/" + query.interval.end).toString
      ) ::
      "aggregations" -> JsArray(query.aggregation.map { aggregation =>
        if (aggregation.endsWith("uv") && !query.dataSource.equals("ead_dmp_stat") || hypers.contains(aggregation)) {
          Json.obj(
            "type" -> "hyperUnique", "fieldName" -> aggregation, "name" -> (aggregation + "NEED_POST_AGG")
          )
        } else if (doubleSumMetrics.contains(aggregation)) {
          Json.obj(
            "type" -> "doubleSum", "fieldName" -> aggregation, "name" -> aggregation
          )
        } else if (aggregation.equals("iad_click")) {
          // 入口点击数由iad_click和iad_outer_face_click加和而成
          buildJsAddAggregation("iad_click", "iad_outer_face_click")
        } else if (aggregation.equals("click") && ("iad_stat").equals(query.dataSource)) {
          // 三跳点击 = 总点击 - 入口点击。入口点击多加了一列，总点击也得多加一列
          buildJsAddAggregation("click", "iad_outer_face_click")
        }  else {
          Json.obj(
            "type" -> "longSum", "fieldName" -> aggregation, "name" -> aggregation
          )
        }
      }) ::
      "context" -> Json.obj(
        "timeout" -> JsNumber(1000 * query.timeoutSeconds)) :: Nil
    fields ++= buildFilter(query.filter)
    fields ++= buildHavingFilter(query.having, query.aggregation, query.dropZeros)
    fields ++= buildLimit(query.orderBy, query.groupBy, query.aggregation, query.limit)
    fields ++= buildPostAggs(query.dataSource, query.aggregation)
    Right(JsObject(
      fields
    ))
  }

  def buildGroupByRequest(query: SimpleQuery): Either[String, JsObject] = {
    var fields: List[(String, JsValue)] = "queryType" -> JsString("groupBy") ::
      "dataSource" -> JsString({
        if (query.dataSource.endsWith("_new")) {
          query.dataSource.dropRight(4)
        } else {
          query.dataSource
        }
      }) ::
      buildGranularity(query) ::
      "intervals" -> Json.arr(org.joda.time.Interval.parse(query.interval.start + "/" + query.interval.end).toString) ::
      "aggregations" -> JsArray(query.aggregation.map {
        aggregation =>
          if (aggregation.endsWith("uv") && !query.dataSource.equals("ead_dmp_stat") || hypers.contains(aggregation)) {
            Json.obj(
              "type" -> "hyperUnique", "fieldName" -> aggregation, "name" -> (aggregation + "NEED_POST_AGG")
            )
          } else if (doubleSumMetrics.contains(aggregation)) {
            Json.obj(
              "type" -> "doubleSum", "fieldName" -> aggregation, "name" -> aggregation
            )
          } else if (aggregation.equals("iad_click")) {
            // 入口点击数由iad_click和iad_outer_face_click加和而成
            buildJsAddAggregation("iad_click", "iad_outer_face_click")
          } else if (aggregation.equals("click") && ("iad_stat").equals(query.dataSource)) {
            // 三跳点击 = 总点击 - 入口点击。入口点击多加了一列，总点击也得多加一列
            buildJsAddAggregation("click", "iad_outer_face_click")
          } else {
            Json.obj(
              "type" -> "longSum", "fieldName" -> aggregation, "name" -> aggregation
            )
          }
      }) ::
      "context" -> Json.obj(
        "timeout" -> JsNumber(1000 * query.timeoutSeconds),
        "applyLimitPushDown" -> false) :: Nil
    fields ++= buildDimension(query)
    fields ++= buildFilter(query.filter)
    fields ++= buildLimit(query.orderBy, query.groupBy, query.aggregation, query.limit)
    fields ++= buildHavingFilter(query.having, query.aggregation, query.dropZeros)
    fields ++= buildPostAggs(query.dataSource, query.aggregation)
    Right(JsObject(
      fields
    ))
  }

  /**
    * 使用javascript type，将两个aggregation的和作为一个aggregation的值。
    * <p>即 a = a + b
    *
    * @param a
    * @param b
    * @return
    */
  def buildJsAddAggregation(a: String, b: String) : JsObject = {
    Json.obj(
      "type" -> "javascript",
      "name" -> a,
      "fieldNames" -> Json.arr(a, b),
      "fnAggregate" -> "function(current, a, b) { return current + a + b; }",
      "fnCombine" -> "function(a, b) { return a + b; }",
      "fnReset" -> "function() { return 0; }"
    )
  }

  def buildDimension(query: SimpleQuery) = {
    val dimension = query.groupBy
    val dataSource = query.dataSource
    var array = new JsArray()
    val abxTestMarksIndices = getAbxTestMarksBitFlag(dimension)
    val isBrandStatV2 = "brand_stat_v2".equals(dataSource)

    dimension.filter(x => !x.startsWith("abx_test_marks_")).foreach { x =>
      if (x.endsWith("*")) {
        array = array :+ JsString(x.substring(0, x.length - 1))
      }
      else if ("abx_test_marks" == x) {
        if (abxTestMarksIndices.size == 0) {
          array = array :+ JsString(x)
        } else {
          array = array :+ buildJsExtractionFn(x, abxTestMarksIndices)
        }
      }
      else if(isBrandStatV2 && "keyFrom" == x){
        array = array :+ buildJsReplaceFn(x, "iphonepro", "iphone")
      }
      else {
        array = array :+ JsString(x)
      }
      if (isBrandStatV2) {
        array = array ++ buildLookUpForBrandStatV2(x)
      } else if ("sales_activity_event_log".equals(query.dataSource)) {
        array = array ++ buildLookUpForSalesActivityEventLog(x)
      } else if ("course_stat".equals(query.dataSource)) {

      } else {
        array = array ++ buildLookUp(x)
      }
    }
    Some("dimensions" -> array)
  }

  def buildLookUpForBrandStatV2(groupBy: String): JsArray = {
    var array = JsArray()
    if ("sponsorId" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "sponsorName", "youxuan_customerId_name")
    } else if ("adVariationId" == groupBy) {
      array = array :+ buildLookUpExtensionMissValue(groupBy, "youxuanTextContent", "youxuan_contentId_text", "[]")
      array = array :+ buildLookUpExtensionMissValue(groupBy, "youxuanImageContent", "youxuan_contentId_image", "[]")
      array = array :+ buildLookUpExtensionMissValue(groupBy, "youxuanVideoContent", "youxuan_contentId_video", "[]")
    } else if ("campaignId" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "campaignName", "youxuan_campaignId_name")
    } else if ("imprPos" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "imprPosName", "youxuan_imprPos_name")
    } else if ("mediaId" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "mediaName", "youxuan_mediaId_name")
    } else if ("adGroupId" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "adGroupName", "youxuan_groupId_name")
    } else if ("styleId" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "styleName", "youxuan_styleId_name")
    }

    array
  }

  def buildLookUpForSalesActivityEventLog(groupBy: String): JsArray = {
    var array = JsArray()
    if ("stage_id".equals(groupBy)) {
      array = array :+ buildLookUpExtension(groupBy, "stage_name", "sale_activity_stage_id_name")
    } else if ("profit_id".equals(groupBy)) {
      array = array :+ buildLookUpExtension(groupBy, "profit_name", "sale_activity_profit_id_name")
    }

    array
  }

  def buildLookUp(groupBy: String): JsArray = {
    var array = JsArray()
    if ("sponsor_id" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "sponsor_name", "sponsorId_name")
      array = array :+ buildLookUpExtension(groupBy, "sponsor_userName", "sponsorId_userName")
    } else if ("campaign_id" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "campaign_name", "AdCampaignId_Name")
    } else if ("sid" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "schema_name", "schema_id_name")
    } else if ("group_id" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "group_name", "AdGroupId_Name")
    } else if ("variation_id" == groupBy) {
      //        array = array :+ buildLookUpExtension(groupBy, "variation_url", "adContentId_mimeSrc")
      array = array :+ buildLookUpExtension(groupBy, "sdk_data", "adContentId_data")
      array = array :+ buildLookUpExtension(groupBy, "variation_title", "AdContentId_Title")
    } else if ("site_id" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "site_name", "siteId_name")
    } else if ("slot_id" == groupBy || "iad_face_slot_id" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "slot_name", "sdkSlotId_name")
    } else if ("dc_id" == groupBy || "app_id" == groupBy || "ext.app_id" == groupBy || "iad_face_app_id" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "app_name", "sdkAppId_name")
    } else if ("sponsorId" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "sponsorName", "brand_sponsorId_name")
    } else if ("adVariationId" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "adVariationTitle", "brand_adVariationId_title")
      array = array :+ buildLookUpExtension(groupBy, "adVariationMimeSrc", "brand_adVariationId_mimeSrc")
      array = array :+ buildLookUpExtension(groupBy, "adVariationLink", "brand_adVariationId_link")
    } else if ("campaignId" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "campaignName", "brand_campaignId_name")
    } else if ("imprPos" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "imprPosName", "brand_imprPos_name")
    } else if ("dsp_id" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "dsp_name", "yex_dspId_username")
    } else if ("channel_did" == groupBy) {
      array = array :+ buildLookUpExtension(groupBy, "channel_code", "channelDid_code")
    }

    array
  }

  /**
    * Get abx_test_marks_X bit marks from dimensions.
    *
    * @param dimension
    * @return bit marks list
    */
  def getAbxTestMarksBitFlag(dimension: List[String]): List[Int] = {
    var list = List[Int]()
    dimension.foreach(x => {
      if (x.startsWith("abx_test_marks_"))
        list = list :+ x.substring(x.lastIndexOf("_") + 1).toInt
    })
    return list
  }

  /**
    * Generate JS Extraction Function for DimensionSpec.
    *
    * @param name    name for the dimension
    * @param indices list for bit marks
    * @return
    */
  def buildJsExtractionFn(name: String, indices: List[Int]) = {
    Json.obj("type" -> "extraction", "dimension" -> name,
      "extractionFn" -> Json.obj("type" -> "javascript",
        "function" -> buildJsSubstrFn(indices)
      )
    )
  }

  /**
    * Generate substring js statement according to bit marks.
    *
    * @param indices bit marks
    * @return
    */
  def buildJsSubstrFn(indices: List[Int]): String = {
    var branch = ""
    indices.sortWith(_ < _)
    indices.foreach(x => {
      if (branch != "")
        branch += " + "
      // ALERT: substr in javascript is different from java
      branch += "str.substr(" + x + ", 1)"
    })
    return "function(str) {if (str == null) {return str;} else {return " + branch + ";}}"
  }

  /**
   * replace dimension str value by javascript
   * @param dimension
   * @param regexp
   * @param replacement
   * @return
   */
  def buildJsReplaceFn(dimension: String, regexp: String, replacement: String) = {
    val fn = "function(str) { return !str ? str :str.replace('" + regexp + "','" + replacement + "') }"
    Json.obj("type" -> "extraction", "dimension" -> dimension,
      "extractionFn" -> Json.obj("type" -> "javascript",
        "function" -> fn
      )
    )
  }

  def buildLookUpExtension(name: String, lookupName: String, namespace: String) = {
    Json.obj("type" -> "extraction", "dimension" -> name, "outputName" -> lookupName,
      "extractionFn" ->
        Json.obj("type" -> "registeredLookup", "lookup" -> namespace, "retainMissingValue" -> true, "injective" -> true))
  }

  def buildLookUpExtensionMissValue(name: String, lookupName: String, namespace: String, missingValue: String) = {
    Json.obj("type" -> "extraction", "dimension" -> name, "outputName" -> lookupName,
      "extractionFn" ->
        Json.obj("type" -> "registeredLookup", "lookup" -> namespace, "retainMissingValue" -> false, "injective" -> true, "replaceMissingValueWith" -> missingValue))
  }

  def buildPostAggs(dataSource: String, aggregations: List[String]): Some[(String, JsArray)] = {
    Some("postAggregations" -> JsArray(aggregations.filter(x => x.endsWith("uv") && !dataSource.equals("ead_dmp_stat") || hypers.contains(x)).map(agg =>
      Json.obj("type" -> "arithmetic", "name" -> agg, "fn" -> "+", "fields" -> JsArray(
        Seq(Json.obj("type" -> "hyperUniqueCardinality", "fieldName" -> (agg + "NEED_POST_AGG")),
          Json.obj("type" -> "constant", "name" -> "zero", "value" -> 0))
      ))
    )))
  }

  def buildFilter(filters: List[Filter]): Option[(String, JsValue)] = {
    buildFilter(filters, "and")
  }

  def buildFilter(filters: List[Filter], filterType: String): Option[(String, JsValue)] = {
    val validFilters = filters.filterNot {
      case SelectFilter(dim, value) => dim.isEmpty || value.isEmpty
      case InFilter(dim, value) => dim.isEmpty || value.isEmpty
      case NotFilter(SelectFilter(dim, value)) => dim.isEmpty || value.isEmpty
      case NotFilter(InFilter(dim, value)) => dim.isEmpty || value.isEmpty
      case IsNullFilter(dim) => dim.isEmpty
      case NotNullFilter(dim) => dim.isEmpty
      case LikeFilter(dim, value) => dim.isEmpty || value.isEmpty
      case RegexFilter(dim, value) => dim.isEmpty || value.isEmpty
    }
    if (validFilters.isEmpty) {
      None
    } else if (validFilters.size == 1) {
      validFilters.head match {
        case f: SelectFilter => Some("filter" -> Json.obj("type" -> "selector", "dimension" -> f.dimension, "value" -> f.value.trim))
        case f: InFilter => {
          if (f.values.contains(","))
            buildFilter(f.values.split(",").map(x => new SelectFilter(f.dimension, x)).toList, "or")
          else if (f.values.contains(" "))
            buildFilter(f.values.split(" ").map(x => new SelectFilter(f.dimension, x)).toList, "or")
          else
            Some("filter" -> Json.obj("type" -> "selector", "dimension" -> f.dimension, "value" -> f.values.trim))
        }
        case f: IsNullFilter => Some("filter" -> Json.obj("type" -> "selector", "dimension" -> f.dimension, "value" -> ""))
        case f: NotFilter => Some("filter" -> Json.obj("type" -> "not", "field" -> buildFilter(List(f.filter)).get._2))
        case f: NotNullFilter => Some("filter" -> Json.obj("type" -> "not", "field" -> Json.obj(
          "type" -> "selector", "dimension" -> f.dimension, "value" -> "")))
        case f: LikeFilter => Some("filter" -> Json.obj("type" -> "like", "dimension" -> f.dimension, "pattern" -> f.value.trim))
        case f: RegexFilter => Some("filter" -> Json.obj("type" -> "regex", "dimension" -> f.dimension, "pattern" -> f.value.trim))
      }
    } else {
      Some("filter" -> Json.obj(
        "type" -> filterType,
        "fields" -> {
          var fields: JsArray = new JsArray()
          validFilters.map({
            _ match {
              case f: SelectFilter => fields = fields :+ Json.obj("type" -> "selector", "dimension" -> f.dimension, "value" -> f.value.trim)
              case f: InFilter => {
                if (f.values.contains(",")) {
                  buildFilter(f.values.split(",").map(x => new SelectFilter(f.dimension, x)).toList, "or").foreach(x => fields = fields :+ x._2)
                } else if (f.values.contains(" ")) {
                  buildFilter(f.values.split(" ").map(x => new SelectFilter(f.dimension, x)).toList, "or").foreach(x => fields = fields :+ x._2)
                } else
                  fields = fields :+ Json.obj("type" -> "selector", "dimension" -> f.dimension, "value" -> f.values.trim)
              }
              case f: IsNullFilter => fields = fields :+ Json.obj("type" -> "selector", "dimension" -> f.dimension, "value" -> "")
              case f: NotFilter => fields = fields :+ Json.obj("type" -> "not", "field" -> buildFilter(List(f.filter)).get._2)
              case f: NotNullFilter => fields = fields :+ Json.obj("type" -> "not", "field" -> Json.obj("type" -> "selector", "dimension" -> f.dimension, "value" -> ""))
              case f: LikeFilter => fields = fields :+ Json.obj("type" -> "like", "dimension" -> f.dimension, "pattern" -> f.value.trim)
              case f: RegexFilter => fields = fields :+ Json.obj("type" -> "regex", "dimension" -> f.dimension, "pattern" -> f.value.trim)
            }
          })
          fields
        }
      ))
    }
  }

  def buildExtractionFilter(name: String, lookupName: String, namespace: String) = {
    Json.obj("type" -> "extraction", "dimension" -> name, "outputName" -> name,
      "extractionFn" -> Json.obj("type" -> "in", "lookup" -> Json.obj("type" -> "registeredLookup", "lookup" -> namespace), "retainMissingValue" -> true, "injective" -> true))
  }

  def buildHavingFilter(filters: List[Filter], aggregations: List[String], dropZeros: Boolean): Option[(String, JsValue)] = {
    val neFilters = filters.filterNot {
      case SelectFilter(dim, value) => dim.isEmpty || value.isEmpty
      case GreaterThanFilter(dim, value) => dim.isEmpty || value.isEmpty
      case LessThanFilter(dim, value) => dim.isEmpty || value.isEmpty
    }
    //    if (dropZeros) {
    //      if (neFilters.isEmpty) {
    //        Some("having" -> Json.obj(
    //          "type" -> "or",
    //          "havingSpecs" -> JsArray(aggregations.map({ agg =>
    //            Json.obj("type" -> "greaterThan", "aggregation" -> agg, "value" -> 0)
    //          }))
    //        ))
    //      }else{
    //        Some("having" -> Json.obj(
    //          "type" -> "and",
    //          "havingSpecs" -> JsArray(neFilters.map({
    //            _ match {
    //              case f: SelectFilter => Json.obj("type" -> "equalTo", "aggregation" -> f.dimension, "value" -> f.value)
    //              case f: GreaterThanFilter => Json.obj("type" -> "greaterThan", "aggregation" -> f.dimension, "value" -> f.value)
    //              case f: LessThanFilter => Json.obj("type" -> "lessThan", "aggregation" -> f.dimension, "value" -> f.value)
    //            }
    //          }) ++ aggregations.map({ agg =>
    //            Json.obj("type" -> "greaterThan", "aggregation" -> agg, "value" -> 0)
    //          }) )
    //        ))
    //      }
    //    } else {
    if (neFilters.isEmpty) {
      None
    } else if (neFilters.size == 1) {
      neFilters.head match {
        case f: SelectFilter => Some("having" -> Json.obj("type" -> "equalTo", "aggregation" -> f.dimension, "value" -> f.value))
        case f: GreaterThanFilter => Some("having" -> Json.obj("type" -> "greaterThan", "aggregation" -> f.dimension, "value" -> f.value))
        case f: LessThanFilter => Some("having" -> Json.obj("type" -> "lessThan", "aggregation" -> f.dimension, "value" -> f.value))
      }
    } else {
      Some("having" -> Json.obj(
        "type" -> "and",
        "havingSpecs" -> JsArray(neFilters.map({
          _ match {
            case f: SelectFilter => Json.obj("type" -> "equalTo", "aggregation" -> f.dimension, "value" -> f.value)
            case f: GreaterThanFilter => Json.obj("type" -> "greaterThan", "aggregation" -> f.dimension, "value" -> f.value)
            case f: LessThanFilter => Json.obj("type" -> "lessThan", "aggregation" -> f.dimension, "value" -> f.value)
          }
        }))
      ))
    }
  }

  //  }

  def buildLimit(orderBys: List[OrderBy], dimension: List[String], aggregation: List[String], limit: Long): Option[
    (String, JsValue)] = {
    val neOrderBysFilters = orderBys.filter(o => !o.dimension.isEmpty && (aggregation.contains(o.dimension)
      || dimension.contains(o.dimension)))
    if (neOrderBysFilters.nonEmpty) {
      Some(
        "limitSpec" -> Json.obj(
          "type" -> "default",
          "limit" -> limit,
          "columns" -> JsArray(neOrderBysFilters.map({ o =>
            val direction = if (o.ascending) "ASCENDING" else "DESCENDING"
            Json.obj("dimension" -> o.dimension, "direction" -> direction)
          }))
        ))
    } else {
      Some(
        "limitSpec" -> Json.obj(
          "type" -> "default",
          "limit" -> limit
        ))
    }
  }

  def buildGranularity(query: SimpleQuery): (String, JsValue) = {
    query.granularity match {
      case "all" => "granularity" -> JsString("all")
      case _ => "granularity" -> Json.obj(
        "type" -> "period",
        "timeZone" -> "Asia/Shanghai",
        query.granularity match {
          case "quarter" => "period" -> "P3M"
          case "month" => "period" -> "P1M"
          case "week" => "period" -> "P1W"
          case "hour" => "period" -> "PT1H"
          case "day" => "period" -> "P1D"
          case "minute" => "period" -> "PT1M"
        }
      )
    }
  }

  private def normalizeResult(query: JsObject, result: JsValue, dropZeros: Boolean): JsValue = {
    (query \ "queryType").as[String] match {
      case "groupBy" => JsArray(result.as[JsArray].value.map {
        obj =>
          val event = obj.as[JsObject] \ "event"
          val enrichedEvent = event.asOpt[JsObject].fold(event)(x => enrichResult(query, x))
          obj.as[JsObject] + ("result" -> enrichedEvent)
      })
      case "timeseries" => JsArray(result.as[JsArray].value.map { obj =>
        val event = obj.as[JsObject] \ "result"
        val enrichedEvent = event.asOpt[JsObject].fold(event)(x => enrichResult(query, x))
        obj.as[JsObject] + ("result" -> enrichedEvent)
      })
    }
  }

  private def ydbc2identify1(perfix: String): String = {
    if (perfix.equals("ydbc1")) {
      "pv_sdk"
    } else if (perfix.equals("ydbc3")) {
      "brand_pv"
    } else if (perfix.equals("ydbc4")) {
      "click_third_party"
    } else if (perfix.equals("ydbc5")) {
      "brand_pv_vender"
    } else {
      ""
    }
  }

  /**
    * 生成指向 查查查 的链接
    *
    * 例如： https://cha.corp.youdao.com/appId/1002/default/
    *
    * field 为 appId, value 为 1002
    *
    * @param field 数据路径
    * @param value 数值
    * @return 查查查数据查询链接，例如： https://cha.corp.youdao.com/appId/1002/default/
    */
  private def chaLink(field: String, value: String) : JsString = {
    JsString (s"""<a href="https://cha.corp.youdao.com/$field/$value/default/" target="_blank">$value</a>""")
  }

  /**
    * 返回指向包含全部信息的链接
    */
  private def chaLinkAll(field: String, value: String) : JsString = {
    JsString (s"""<a href="https://cha.corp.youdao.com/$field/$value/default/all" target="_blank">$value</a>""")
  }

  /**
    * 返回指向包含部分信息的链接
    */
  private def chaLinkSome(field: String, value: String) : JsString = {
    JsString (s"""<a href="https://cha.corp.youdao.com/$field/$value/default/some" target="_blank">$value</a>""")
  }

  /**
   * @param d     原值
   * @param scale 小数点后保留的位数
   * @return      小数点后保留 scale 位的 double 值
   */
  private def nDecimal(d: Double, scale: Int): Double = {
    if (d == 0) {
      0
    } else {
      new DecimalFormat("#." + "#" * scale).format(d).toDouble
    }
  }

  private def twoDecimalDivide(x: Long, y: Long): Double = {
    nDecimalDivide(x, y, 2)
  }

  /**
   * 注意，这个还需要留意 {@link controllers.Application.toString} 方法中 JsNumber类型设置的保留小数位数
   *
   * @param scale 保留小数位数
   */
  private def nDecimalDivide(x: Long, y: Long, scale: Int): Double = {
    if (y == 0) {
      0
    } else {
      nDecimal(x / y.toDouble, scale)
    }
  }

  def enrichResult(query: JsObject, result: JsObject): JsObject = {
    var enriched = result
    val dataSource = (query \ "dataSource").as[String]
    dataSource match {
      case "ead_stat" | "ssp_log" => {
        if ((query \ "filter" \\ "value").seq.exists(x => x.asInstanceOf[JsString].value == "IMAGE") &&
          (query \ "filter" \\ "dimension").seq.exists(x => x.asInstanceOf[JsString].value == "ad_type")) {
        }
      }
      case "sdk_stat" | "iad_stat" => {
        (result \ "ext.image0").asOpt[String].foreach(x => enriched += "ext.image0" -> JsString( s"""<img src="$x">"""))
        (result \ "ext.image1").asOpt[String].foreach(x => enriched += "ext.image1" -> JsString( s"""<img src="$x">"""))
        (result \ "ext.image2").asOpt[String].foreach(x => enriched += "ext.image2" -> JsString( s"""<img src="$x">"""))
        (result \ "ext.image3").asOpt[String].foreach(x => enriched += "ext.image3" -> JsString( s"""<img src="$x""""))
        (result \ "uv").asOpt[Long].foreach(x => enriched += "uv" -> JsString(x.toString))
        (result \ "ub").asOpt[Long].foreach(x => enriched += "ub" -> JsString(x.toString))
        (result \ "ui").asOpt[Long].foreach(x => enriched += "ui" -> JsString(x.toString))
        (result \ "uc").asOpt[Long].foreach(x => enriched += "uc" -> JsString(x.toString))
        (result \ "country").asOpt[String].foreach(x => enriched += "country" -> JsString(countryMap.getOrElse(NumberUtils.toInt(x, -1), x)))
        (result \ "province").asOpt[String].foreach(x => enriched += "province" -> JsString(cityMap.getOrElse(NumberUtils.toInt(x, -1), x)))
        (result \ "city").asOpt[String].foreach(x => enriched += "city" -> JsString(cityMap.getOrElse(NumberUtils.toInt(x, -1), x)))
        (result \ "network_type").asOpt[String].foreach(x => enriched += "network_type" -> JsString(networkMap.getOrElse(x, x)))
        (result \ "ext.networktype").asOpt[String].foreach(x => enriched += "ext.networktype" -> JsString(networkMap.getOrElse(x, x)))
        (result \ "developer_id").asOpt[String].foreach(x => enriched += "developer_id" -> chaLink("developerId", x))
        (result \ "app_id").asOpt[String].foreach(x => enriched += "app_id" -> chaLink("appId", x))
        (result \ "iad_face_app_id").asOpt[String].foreach(x => enriched += "iad_face_app_id" -> chaLink("appId", x))
        (result \ "slot_id").asOpt[String].foreach(x => enriched += "slot_id" -> chaLink("slotId", x))
        (result \ "iad_face_slot_id").asOpt[String].foreach(x => enriched += "iad_face_slot_id" -> chaLink("slotId", x))
        (result \ "sponsor_id").asOpt[String].foreach(x => enriched += "sponsor_id" -> chaLinkSome("sponsorId", x))
        (result \ "campaign_id").asOpt[String].foreach(x => enriched += "campaign_id" -> chaLinkSome("adCampaignId", x))
        (result \ "group_id").asOpt[String].foreach(x => enriched += "group_id" -> chaLinkAll("groupId", x))
        (result \ "variation_id").asOpt[String].foreach(x => enriched += "variation_id" -> chaLinkAll("adContentId", x))
        (result \ "bid_predict_cost").asOpt[Double].foreach(x => enriched += "bid_predict_cost" -> JsNumber(x / 100.0))
        (result \ "bid_time_charge").asOpt[Long].foreach(x => enriched += "bid_time_charge" -> JsNumber(x / 100.0))
        (result \ "bid").asOpt[Long].foreach(x => {
          (result \ "ctr").asOpt[Double].foreach(y => enriched += ("avg_ctr", gimmeRatio(y, x)))
          (result \ "pr_cvr").asOpt[Double].foreach(y => enriched += ("avg_pr_cvr", gimmeRatio(y, x)))
          (result \ "bid_price").asOpt[Long].foreach(y => enriched += ("bid_ecpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
          (result \ "win_count").asOpt[Long].foreach(y => enriched += ("win_rate", gimmeRatio(y, x)))
        })
        (result \ "win_count").asOpt[Long].foreach(x => {
          (result \ "adx_win_price").asOpt[Long].foreach(y => enriched += ("adx_win_ecpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
          (result \ "impr").asOpt[Long].foreach(y => enriched += ("adx_impr_rate", gimmeRatio(y, x)))
        })
        (result \ "win_price").asOpt[Long].foreach(x => {
          enriched += ("adx_consume", JsNumber(x.toDouble / 100 / 1000))
          // win_price 是 1000 次消费，所以这里 win_price 应除 1000，但精度有可能丢失，所以给 charge 乘 1000
          (result \ "charge").asOpt[Long].foreach(y => enriched += ("adx_consume_roi", JsNumber(twoDecimalDivide(y * 1000, x))))
          (result \ "conv").asOpt[Long].foreach(y => enriched += ("adx_cpa", JsNumber(twoDecimalDivide(x, y) / 100 / 1000)))
          (result \ "loc_conv").asOpt[Long].foreach(y => enriched += ("adx_loc_cpa", JsNumber(twoDecimalDivide(x, y) / 100 / 1000)))
        })
        (result \ "impr").asOpt[Long].foreach(x => {
          (result \ "win_price").asOpt[Long].foreach(y => enriched += ("impr_ecpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
          (result \ "ctr_of_impr").asOpt[Double].foreach(y => enriched += ("avg_ctr_of_impr", gimmeRatio(y, x)))
          (result \ "pr_cvr_of_impr").asOpt[Double].foreach(y => enriched += ("avg_pr_cvr_of_impr", gimmeRatio(y, x)))
        })
        (result \ "impr_dedup").asOpt[Long].foreach(x => {
          (result \ "win_price_dedup").asOpt[Long].foreach(y => enriched += ("impr_ecpm_dedup", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
        })
        (result \ "win_price_dedup").asOpt[Long].foreach(x => {
          enriched += ("adx_consume_dedup", JsNumber(x.toDouble / 100 / 1000))
          // win_price 是 1000 次消费，所以这里 win_price 应除 1000，但精度有可能丢失，所以给 charge 乘 1000
          (result \ "charge").asOpt[Long].foreach(y => enriched += ("adx_consume_roi_dedup", JsNumber(twoDecimalDivide(y * 1000, x))))
        })
        (result \ "conv_touchpoint_time_difference").asOpt[String]
          .foreach(x => enriched += ("conv_touchpoint_time_difference_desc", JsString(TimeDifferenceUtil.convClickTimeDifference(x))))
        (result \ "conv_activate").asOpt[Long].foreach(x => {
          // 激活成本：消费/激活数，单位：元。charge为消费，单位：分，conv_activate，单位：个。
          (result \ "charge").asOpt[Long].foreach(y => enriched += ("activate_cost", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
          // adx激活成本：adx消费/激活数，单位：元。win_price为adx消费*1000，单位：分，conv_activate，单位：个
          (result \ "win_price").asOpt[Long].foreach(y => enriched += ("adx_activate_cost", JsNumber(twoDecimalDivide(y, x * 1000 * CURRENCY_RATIO_YUAN_FEN))))
          (result \ "win_price_dedup").asOpt[Long].foreach(y => enriched += ("adx_activate_cost_dedup", JsNumber(twoDecimalDivide(y, x * 1000 * CURRENCY_RATIO_YUAN_FEN))))
        })
      }
      case "sdk_stat_v2" => {
        (result \ "uv").asOpt[Long].foreach(x => enriched += "uv" -> JsString(x.toString))
        (result \ "developer_id").asOpt[String].foreach(x => enriched += "developer_id" -> chaLink("developerId", x))
        (result \ "app_id").asOpt[String].foreach(x => enriched += "app_id" -> chaLink("appId", x))
        (result \ "slot_id").asOpt[String].foreach(x => enriched += "slot_id" -> chaLink("slotId", x))
        (result \ "sponsor_id").asOpt[String].foreach(x => enriched += "sponsor_id" -> chaLinkSome("sponsorId", x))
        (result \ "campaign_id").asOpt[String].foreach(x => enriched += "campaign_id" -> chaLinkSome("adCampaignId", x))
        (result \ "group_id").asOpt[String].foreach(x => enriched += "group_id" -> chaLinkAll("groupId", x))
        (result \ "variation_id").asOpt[String].foreach(x => enriched += "variation_id" -> chaLinkAll("adContentId", x))
      }
      case "third_party_stat" => {
        (result \ "sponsor_id").asOpt[String].foreach(x => enriched += "sponsor_id" -> chaLinkSome("sponsorId", x))
        (result \ "campaign_id").asOpt[String].foreach(x => enriched += "campaign_id" -> chaLinkSome("adCampaignId", x))
        (result \ "group_id").asOpt[String].foreach(x => enriched += "group_id" -> chaLinkAll("groupId", x))
        (result \ "variation_id").asOpt[String].foreach(x => enriched += "variation_id" -> chaLinkAll("adContentId", x))
        (result \ "app_id").asOpt[String].foreach(x => enriched += "app_id" -> chaLink("appId", x))
        (result \ "tdp_platform").asOpt[String].foreach(x => enriched += "tdp_platform" -> JsString(Objects.toString(tdpPlatformDict.getOrElse(x, x),"")))
        (result \ "province").asOpt[String].foreach(x => enriched += "province" -> JsString(cityMap.getOrElse(x.toInt, x)))
        (result \ "city").asOpt[String].foreach(x => enriched += "city" -> JsString(cityMap.getOrElse(x.toInt, x)))
      }
      case "third_party_stat_v2" => {
        (result \ "sponsor_id").asOpt[String].foreach(x => enriched += "sponsor_id" -> chaLinkSome("sponsorId", x))
        (result \ "campaign_id").asOpt[String].foreach(x => enriched += "campaign_id" -> chaLinkSome("adCampaignId", x))
        (result \ "group_id").asOpt[String].foreach(x => enriched += "group_id" -> chaLinkAll("groupId", x))
        (result \ "variation_id").asOpt[String].foreach(x => enriched += "variation_id" -> chaLinkAll("adContentId", x))
      }
      case "dsp_stat" |"dsp_stat_v2" | "brand_abnor_url_stat" => {
        (result \ "city").asOpt[String].foreach(x => enriched += "city" -> JsString(cityMap.getOrElse(x.toInt, x)))
        (result \ "province").asOpt[String].foreach(x => enriched += "province" -> JsString(cityMap.getOrElse(x.toInt, x)))
      }
      case "yex" => {
        (result \ "impr_price").asOpt[Long].foreach(x=> enriched += "impr_price" -> JsNumber(x/100000.0))
        (result \ "original_cur_impr_price").asOpt[Long].foreach(x=> enriched += "original_cur_impr_price" -> JsNumber(x/1000.0))
        (result \ "original_cur_bid_price").asOpt[Long].foreach(x=> enriched += "original_cur_bid_price" -> JsNumber(x/1000.0))
        (result \ "impr_count").asOpt[Long].foreach(x => {
          (result \ "impr_price").asOpt[Long].foreach(y => enriched += ("ecpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / 1000 / x.toDouble * 1000 / 100)))
          (result \ "bid_count_win").asOpt[Long].foreach(y => enriched += ("yex_impr_ratio", JsNumber(twoDecimalDivide(x, y))))
          (result \ "adx_impr_price").asOpt[Long].foreach(y => enriched += ("adx_impr_ecpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
        })
        (result \ "bid_count_win").asOpt[Long].foreach(x => {
          (result \ "bid_price_of_win").asOpt[Long].foreach(y => enriched += ("bid_cpm_of_win", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
          (result \ "win_price").asOpt[Long].foreach(y => enriched += ("win_cpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
          (result \ "bid_count").asOpt[Long].foreach(y => enriched += ("yex_bid_success_ratio", JsNumber(twoDecimalDivide(x, y))))
          (result \ "adx_bid_price").asOpt[Long].foreach(y => enriched += ("adx_bid_ecpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
          (result \ "adx_bid_count_win").asOpt[Long].foreach(y => enriched += ("adx_bid_win_rate", gimmeRatio(y, x)))
        })
        (result \ "bid_count").asOpt[Long].foreach(x => {
          (result \ "received_ad_request_count").asOpt[Long].foreach(y => enriched += ("yex_bid_ratio", JsNumber(twoDecimalDivide(x, y))))
        })
        (result \ "adx_bid_count_win").asOpt[Long].foreach(x => {
          (result \ "adx_win_price").asOpt[Long].foreach(y => enriched += ("adx_win_ecpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100)))
        })
        (result \ "adx_impr_price").asOpt[Long].foreach(x => enriched += ("adx_consume", JsNumber(x.toDouble / 100 / 1000)))
        (result \ "sponsor_id").asOpt[String].foreach(x => enriched += "sponsor_id" -> chaLinkSome("sponsorId", x))
        (result \ "bid_response_count_no_bid").asOpt[Long].foreach(x => {
          (result \ "bid_count").asOpt[Long].foreach(y => enriched += ("bid_rate", gimmeRatio(y, y + x)))
        })
      }
      case "brand_stat" => {
        (result \ "uc").asOpt[Long].foreach(x => enriched += "uc" -> JsString(x.toString))
        (result \ "ui").asOpt[Long].foreach(x => enriched += "ui" -> JsString(x.toString))
      }
      case "brand_stat_v2" => {
        (result \ "billingType").asOpt[String].foreach(x => enriched += "billingType" -> JsString(billingTypeDict.getOrElse(x, x)))
        (result \ "deliveryType").asOpt[String].foreach(x => enriched += "deliveryType" -> JsString(deliveryTypeDict.getOrElse(x, x)))
        (result \ "brandClkType").asOpt[String].foreach(x => enriched += "brandClkType" -> JsString(brandClkTypeDict.getOrElse(x, x)))
        (result \ "uc").asOpt[Long].foreach(x => enriched += "uc" -> JsString(x.toString))
        (result \ "ui").asOpt[Long].foreach(x => enriched += "ui" -> JsString(x.toString))
      }
      case _ =>
    }
    var sb = new StringBuilder
    (result \ "sdk_data").asOpt[String].foreach(x =>
      try {
        Json.parse(x).as[Map[String, String]].foreach(y => {
          if (y._2.matches("http://oimage.*")) {
            val a = y._2.trim
            sb.append("Img:").append( s"""<img height="60" src="$a">""").append("<br>")
            sb.append("URL:").append( s"""$a""").append("<br>")
          } else if (!y._2.isEmpty) {
            sb.append(y._2).append("<br>")
          }
        })
      } catch {
        case _: Throwable => println("Not validated Json.")
      }
    )
    enriched += "sdk_data" -> JsString(sb.toString())

    //优选广告内容
    sb = new StringBuilder
    val textOptional = (result \ "youxuanTextContent").asOpt[String]
    if (textOptional nonEmpty) {
      val textContent = Json.parse(textOptional.get)
      sb.append("文本元素：").append("<br/>")
      textContent.as[JsArray].value.foreach{
        obj =>
        sb.append(obj.as[JsObject] \ "elementKey").append(":").append(obj.as[JsObject] \ "name").append("<br/>")
        val properties = obj.as[JsObject] \ "properties"
        properties.as[JsArray].value.map {
          p => sb.append(p.as[JsObject] \ "text").append(";")
        }
      }
    }

    val imageOptional = (result \ "youxuanImageContent").asOpt[String]
    if (imageOptional nonEmpty) {
      val imageContent = Json.parse(imageOptional.get)
      sb.append("图片元素：").append("<br/>")
      imageContent.as[JsArray].value.foreach {
        obj =>
          sb.append(obj.as[JsObject] \ "elementKey").append(":").append(obj.as[JsObject] \ "name").append("<br/>")
          val properties = obj.as[JsObject] \ "properties"
          properties.as[JsArray].value.foreach {
            p => {
              val url = (p.as[JsObject] \ "url").as[String]
              if (url.matches("http://oimage.*")) {
                val a = url.replaceFirst("http","https")
                sb.append("Img:").append(s"""<img height="60" src="$a">""").append("<br>")
                sb.append("URL:").append(s"""$a""").append("<br>")
              } else if (url.nonEmpty) {
                sb.append(url).append("<br>")
              }
            }
          }
      }
    }
    val videoOptional = (result \ "youxuanVideoContent").asOpt[String]
    if (videoOptional nonEmpty) {
      val videoContent = Json.parse(videoOptional.get)
      sb.append("视频元素：").append("<br/>")
      videoContent.as[JsArray].value.foreach {
        obj =>
          sb.append(obj.as[JsObject] \ "elementKey").append(":").append(obj.as[JsObject] \ "name").append("<br/>")
          val properties = obj.as[JsObject] \ "properties"
          properties.as[JsArray].value.foreach {
            p => {
              val url = (p.as[JsObject] \ "url").as[String]
              if (url.nonEmpty) {
                sb.append(url).append("<br>")
              }
            }
          }
      }
    }
    if (sb.nonEmpty) {
      enriched += "adContent" -> JsString(sb.toString())
    }
    //merge品牌广告内容
    sb = new StringBuilder
    val titleValidate = (result \ "adVariationTitle").validate[String]
    if (titleValidate.isInstanceOf[JsSuccess[String]]) {
      sb.append("标题：").append(titleValidate.get).append("<br/>")
    }
    val mimeSrcValidate = (result \ "adVariationMimeSrc").validate[String]
    if (mimeSrcValidate.isInstanceOf[JsSuccess[String]]) {
      val mimeSrc = mimeSrcValidate.get
      if (mimeSrc.matches("http://oimage.*")) {
        sb.append("素材：").append( s"""<img height="60" src="$mimeSrc" >""").append("<br>")
      }
    }
    val destinationLinkValidate = (result \ "adVariationLink").validate[String]
    if (destinationLinkValidate.isInstanceOf[JsSuccess[String]]) {
      val destinationLink = destinationLinkValidate.get
      if (destinationLink.matches("http.*")) {
        sb.append("落地页：").append(s"""<a target="_blank" href="$destinationLink" />落地页链接</a>""").append("<br/>")
      }
    }
    if (!sb.isEmpty) {
      enriched += "adContent" -> JsString(sb.toString())
    }

    dataSource match {
      case "ead_stat" | "dsp_stat" | "dsp_stat_v2" | "sdk_stat"| "sdk_stat_v2" | "readease_stat" | "iad_stat" | "mediation_sdk_report" =>
        (result \ "click").asOpt[Long].foreach(x => {
          (result \ "impr").asOpt[Long].foreach(y =>
            enriched +=("actual_ctr", gimmeRatio(x, y)))
          (result \ "charge").asOpt[Long].foreach(y =>
            enriched +=("cpc", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
          (result \ "conv").asOpt[Long].foreach(y =>
            enriched +=("ccr", gimmeRatio(y, x)))
          (result \ "loc_conv").asOpt[Long].foreach(y =>
            enriched +=("loc_ccr", gimmeRatio(y, x)))
          (result \ "opt_conv").asOpt[Long].foreach(y =>
            enriched +=("opt_ccr", gimmeRatio(y, x)))
          (result \ "loc_opt_conv").asOpt[Long].foreach(y =>
            enriched +=("loc_opt_ccr", gimmeRatio(y, x)))
        })
        (result \ "charge").asOpt[Long].foreach(y =>
          enriched +=("charge", JsNumber(twoDecimalDivide(y, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "conv_cost").asOpt[Long].foreach(y =>
          enriched +=("conv_cost", JsNumber(twoDecimalDivide(y, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "loc_conv_cost").asOpt[Long].foreach(y =>
          enriched +=("loc_conv_cost", JsNumber(twoDecimalDivide(y, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "discarded_charge").asOpt[Long].foreach(y =>
          enriched +=("discarded_charge", JsNumber(twoDecimalDivide(y, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "fraud_charge").asOpt[Long].foreach(y =>
          enriched +=("fraud_charge", JsNumber(twoDecimalDivide(y, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "impr").asOpt[Long].foreach(x => {
          (result \ "bid").asOpt[Long].foreach(y => enriched +=("ir", JsNumber(twoDecimalDivide(x, y))))
          (result \ "charge").asOpt[Long].foreach(y => enriched +=("ecpm", JsNumber(twoDecimalDivide(y * 1000, x * CURRENCY_RATIO_YUAN_FEN))))
          (result \ "impr_conv").asOpt[Long].foreach(y => enriched +=("icr", gimmeRatio(y, x)))
        })
        (result \ "bid").asOpt[Long].foreach(x => (result \ "request_ad_num").asOpt[Long].foreach(y =>
          enriched +=("br", JsNumber(twoDecimalDivide(x, y)))))
        (result \ "bid").asOpt[Long].foreach(x => (result \ "req_ad_num").asOpt[Long].foreach(y =>
          enriched +=("br", JsNumber(twoDecimalDivide(x, y)))))
        (result \ "conv").asOpt[Long].foreach(x => {
          (result \ "charge").asOpt[Long].foreach(y => enriched +=("cpa", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
        })
        (result \ "loc_conv").asOpt[Long].foreach(x => {
          (result \ "charge").asOpt[Long].foreach(y => enriched +=("loc_cpa", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
        })
        (result \ "opt_conv").asOpt[Long].foreach(x => {
          (result \ "charge").asOpt[Long].foreach(y => enriched +=("opt_cpa", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
        })
        (result \ "loc_opt_conv").asOpt[Long].foreach(x => {
          (result \ "charge").asOpt[Long].foreach(y => enriched +=("loc_opt_cpa", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
        })
        // 互动广告
        if ("iad_stat".equals(dataSource)) {
          (result \ "iad_click").asOpt[Long].foreach(x => {
            // 入口CTR
            (result \ "iad_impr").asOpt[Long].foreach(y =>
              enriched += ("entry_ctr", gimmeRatio(x, y)))
            // 入口CPC:元
            (result \ "iad_charge").asOpt[Long].foreach(y =>
              enriched += ("entry_cpc", JsNumber(twoDecimalDivide(y, CURRENCY_RATIO_YUAN_FEN * x))))
            // 落地页到达率
            (result \ "iad_page").asOpt[Long].foreach(y =>
              enriched += ("lp_arrival_rate", gimmeRatio(y, x)))
            // 三跳点击数
            (result \ "click").asOpt[Long].foreach(y =>
              enriched += ("3rd_jump_click", JsNumber(y - x)))
            // 三跳转化率
            (result \ "click").asOpt[Long].foreach(y => {
              (result \ "conv").asOpt[Long].foreach(z => {
                enriched += ("3rd_jump_conv_rate", gimmeRatio(z, y - x))
              })
            })
            // 垫底广告点击占比
            (result \ "click").asOpt[Long].foreach(y => {
              (result \ "iad_backup_click").asOpt[Long].foreach(z => {
                enriched += ("backup_click_rate", gimmeRatio(z, z + y - x))
              })
            })
          })
          (result \ "iad_impr").asOpt[Long].foreach(x => {
            // 入口CPM:元
            (result \ "iad_charge").asOpt[Long].foreach(y =>
              enriched += ("entry_cpm", JsNumber(twoDecimalDivide(1000 * y, x * CURRENCY_RATIO_YUAN_FEN))))
            // 三跳展示数
            (result \ "impr").asOpt[Long].foreach(y =>
              enriched += ("3rd_jump_impr", JsNumber(y - x)))
            // 三跳点击率
            (result \ "impr").asOpt[Long].foreach(y => {
              (result \ "click").asOpt[Long].foreach(m => {
                (result \ "iad_click").asOpt[Long].foreach(n => {
                  enriched += ("3rd_jump_ctr", gimmeRatio(m - n, y - x))
                })
              })
            })
            // 垫底广告展示占比
            (result \ "impr").asOpt[Long].foreach(y => {
              (result \ "iad_backup_impr").asOpt[Long].foreach(z => {
                enriched += ("backup_impr_rate", gimmeRatio(z, z + y - x))
              })
            })
          })
          (result \ "iad_user_click").asOpt[Long].foreach(x => {
            // 二跳点击率
            (result \ "iad_page").asOpt[Long].foreach(y =>
              enriched += ("2nd_jump_ctr", gimmeRatio(x, y)))
            // 游戏参与率
            (result \ "iad_click").asOpt[Long].foreach(y =>
              enriched += ("game_in_rate", gimmeRatio(x, y)))
          })
          (result \ "iad_charge").asOpt[Long].foreach(x => {
            enriched += ("iad_charge", JsNumber(twoDecimalDivide(x, CURRENCY_RATIO_YUAN_FEN)))
            // 三跳消费:元
            (result \ "charge").asOpt[Long].foreach(y =>
              enriched += ("3rd_jump_charge", JsNumber(twoDecimalDivide(y - x, CURRENCY_RATIO_YUAN_FEN))))
            // 三跳CPC:元
            (result \ "charge").asOpt[Long].foreach(y => {
              (result \ "click").asOpt[Long].foreach(m => {
                (result \ "iad_click").asOpt[Long].foreach(n => {
                  enriched += ("3rd_jump_cpc", JsNumber(twoDecimalDivide(y - x, (m - n) * CURRENCY_RATIO_YUAN_FEN)))
                })
              })
            })
            // 三跳CPM:元
            (result \ "charge").asOpt[Long].foreach(y => {
              (result \ "impr").asOpt[Long].foreach(m => {
                (result \ "iad_impr").asOpt[Long].foreach(n => {
                  enriched += ("3rd_jump_cpm", JsNumber(twoDecimalDivide((y - x) * 1000, (m - n) * CURRENCY_RATIO_YUAN_FEN)))
                })
              })
            })
            // 三跳CPA:元
            (result \ "charge").asOpt[Long].foreach(y => {
              (result \ "conv").asOpt[Long].foreach(m => {
                enriched += ("3rd_jump_cpa", JsNumber(twoDecimalDivide(y - x, m * CURRENCY_RATIO_YUAN_FEN)))
              })
            })
          })
        }

      case "luna_article_stat" =>
        (result \ "click_uv").asOpt[Long].foreach(x => {
          (result \ "comment_uv").asOpt[Long].foreach(y =>
            enriched +=("cpp", gimmeRatio(y, x)))
          (result \ "share_uv").asOpt[Long].foreach(y =>
            enriched +=("spp", gimmeRatio(y, x)))
          (result \ "like_uv").asOpt[Long].foreach(y =>
            enriched +=("lpp", gimmeRatio(y, x)))
          (result \ "dislike_uv").asOpt[Long].foreach(y =>
            enriched +=("dpp", gimmeRatio(y, x)))
          (result \ "favorites_uv").asOpt[Long].foreach(y =>
            enriched +=("fpp", gimmeRatio(y, x)))
          (result \ "pv_uv").asOpt[Long].foreach(y =>
            enriched +=("cpc", gimmeRatio(x, y)))
        })
        (result \ "playTimes").asOpt[Long].foreach(x => {
          (result \ "playDuration").asOpt[Long].foreach(y => enriched +=("playAvg", gimmeRatio(x, y)))
        })
      case "readease_stat_join" =>
        (result \ "uv").asOpt[Long].foreach(x => enriched += "uv" -> JsString(x.toString))
        (result \ "pv-uv").asOpt[Long].foreach(x => enriched += "pv-uv" -> JsString(x.toString))
      case "sdk_analyzer" =>
        (result \ "lt_sum").asOpt[Long].foreach(x => {
          (result \ "count").asOpt[Long].foreach(y => enriched +=("lt_avg", JsNumber(if (y.toDouble == 0) 0 else x / y.toDouble)))
        })
        (result \ "pt_sum").asOpt[Long].foreach(x => {
          (result \ "count").asOpt[Long].foreach(y => enriched +=("pt_avg", JsNumber(if (y.toDouble == 0) 0 else x / y.toDouble)))
        })
        (result \ "fd_lt_sum").asOpt[Long].foreach(x => {
          (result \ "fd_count").asOpt[Long].foreach(y => enriched +=("fd_lt_avg", JsNumber(if (y.toDouble == 0) 0 else x / y.toDouble)))
        })
        (result \ "fd_pt_sum").asOpt[Long].foreach(x => {
          (result \ "fd_count").asOpt[Long].foreach(y => enriched +=("fd_pt_avg", JsNumber(if (y.toDouble == 0) 0 else x / y.toDouble)))
        })
        (result \ "fd_ht_sum").asOpt[Long].foreach(x => {
          (result \ "fd_count").asOpt[Long].foreach(y => enriched +=("fd_ht_avg", JsNumber(if (y.toDouble == 0) 0 else x / y.toDouble)))
        })
      case "yex_new" | "yex" | "yex_flat" => {
        val sb = new StringBuilder
        sb.append("<a>")
        //解析之前必须先定义一个format
        implicit val assetContentFormat = Json.format[AssetContent]
        var assetsFlatOption = (result \ "assets_flat").asOpt[String]
        if (assetsFlatOption.nonEmpty) {
          if (assetsFlatOption.get.startsWith("[")) {
            //旧的asset_flat: [Text:xxxx, http://xxxx]
            (result \ "assets_flat").asOpt[String].filter(x => x.startsWith("[") && x.endsWith("]")).foreach(x => x.substring(1, x.length - 1).split(",").foreach(y => {
              if (y.startsWith("Text")) {
                sb.append(y).append("<br> ")
              } else if (y.trim.startsWith("lp:")) {
                val url = y.trim.substring(3, y.trim.length)
                sb.append("Lp:").append( s"""<a href="$url" target="_blank">LandingPageUrl</a>""").append("<br>")
              } else if (!y.isEmpty) {
                val a = y.trim
                sb.append(s"""<a href="$a" target="_blank">""").append("Img:").append( s"""<img  onclick="imgResize(this)" height="60" src="$a">""").append("</a>").append("<br>")
              }
            }))
          } else {
            val assetContentOption = Json.parse(assetsFlatOption.get).asOpt[AssetContent]
            if (assetContentOption.nonEmpty) {
              val assetContent = assetContentOption.get
              //若有多个text、image只取第一个
              if (assetContent.texts.size >= 1) {
                var text = StringEscapeUtils.escapeHtml(assetContent.texts(0))
                if (text.length > 2000) {
                  text = text.substring(0, 2000)
                }
                sb.append("Text:").append(text).append("<br> ")
              }
              if (assetContent.imageUrls.size >=1) {
                var imageUrl = StringEscapeUtils.escapeHtml(assetContent.imageUrls(0))
                if (imageUrl.length > 2000) {
                  imageUrl = imageUrl.substring(0, 2000)
                }
                sb.append(s"""<a href="$imageUrl" target="_blank">""").append("Img:").append( s"""<img  onclick="imgResize(this)" height="60" src="$imageUrl">""").append("</a>").append("<br>")
              }
              var url = StringEscapeUtils.escapeHtml(assetContent.lpUrl)
              if (url.length > 2000) {
                url = url.substring(0, 2000)
              }
              if (url.nonEmpty && url.length > 0) {
                sb.append("Lp:").append( s"""<a href="$url" target="_blank">LandingPageUrl</a>""").append("<br>")
              }
            }
          }
        }
        sb.append("</a>")
        enriched += "assets_flat" -> JsString(sb.toString())
        (result \ "uv").asOpt[Long].foreach(x => enriched += "uv" -> JsString(x.toString))

        (result \ "click_count").asOpt[Long].foreach(x => {
          (result \ "impr_count").asOpt[Long].foreach(y =>
            enriched +=("actual_ctr", gimmeRatio(x, y)))
        })

        (result \ "click_count").asOpt[Long].foreach(x => {
          (result \ "impr_price").asOpt[Long].foreach(y =>
            enriched +=("unit_price", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / 1000 / x.toDouble / 100)))
        })
      }
      case "third_party_stat" => {
        (result \ "click").asOpt[Long].foreach(x => {
          (result \ "impr").asOpt[Long].foreach(y =>
            enriched +=("actual_ctr", gimmeRatio(x, y)))
          (result \ "conv").asOpt[Long].foreach(y =>
            enriched +=("ccr", gimmeRatio(y, x)))
          (result \ "loc_conv").asOpt[Long].foreach(y =>
            enriched +=("loc_ccr", gimmeRatio(y, x)))
          (result \ "rta_bid_click").asOpt[Long].foreach(y =>
            enriched +=("rta_click_br", gimmeRatio(y, x)))
          (result \ "repeat_click").asOpt[Long].foreach(y =>
            enriched +=("repeat_ctr", gimmeRatio(y, x)))
          (result \ "channel_did_filled").asOpt[Long].foreach(y =>
            enriched += ("channel_did_filled_rate", gimmeRatio(y, x)))
          (result \ "ua_filled").asOpt[Long].foreach(y =>
            enriched += ("ua_filled_rate", gimmeRatio(y, x)))
          (result \ "ip_filled").asOpt[Long].foreach(y =>
            enriched += ("ip_filled_rate", gimmeRatio(y, x)))
          (result \ "ts_filled").asOpt[Long].foreach(y =>
            enriched += ("ts_filled_rate", gimmeRatio(y, x)))
          (result \ "req_id_filled").asOpt[Long].foreach(y =>
            enriched += ("req_id_filled_rate", gimmeRatio(y, x)))
          (result \ "activity_id_filled").asOpt[Long].foreach(y =>
            enriched += ("activity_id_filled_rate", gimmeRatio(y, x)))
          (result \ "device_model_filled").asOpt[Long].foreach(y =>
            enriched += ("device_model_filled_rate", gimmeRatio(y, x)))
          (result \ "imei_filled").asOpt[Long].foreach(y =>
            enriched += ("imei_filled_rate", gimmeRatio(y, x)))
          (result \ "oaid_filled").asOpt[Long].foreach(y =>
            enriched += ("oaid_filled_rate", gimmeRatio(y, x)))
          (result \ "oaid_md5_filled").asOpt[Long].foreach(y =>
            enriched += ("oaid_md5_filled_rate", gimmeRatio(y, x)))
          (result \ "idfa_filled").asOpt[Long].foreach(y =>
            enriched += ("idfa_filled_rate", gimmeRatio(y, x)))
          (result \ "idfa_md5_filled").asOpt[Long].foreach(y =>
            enriched += ("idfa_md5_filled_rate", gimmeRatio(y, x)))
          (result \ "caid_filled").asOpt[Long].foreach(y =>
            enriched += ("caid_filled_rate", gimmeRatio(y, x)))
          (result \ "caid_md5_filled").asOpt[Long].foreach(y =>
            enriched += ("caid_md5_filled_rate", gimmeRatio(y, x)))
          (result \ "alid_filled").asOpt[Long].foreach(y =>
            enriched += ("alid_filled_rate", gimmeRatio(y, x)))
          (result \ "android_id_filled").asOpt[Long].foreach(y =>
            enriched += ("android_id_filled_rate", gimmeRatio(y, x)))
          (result \ "android_id_md5_filled").asOpt[Long].foreach(y =>
            enriched += ("android_id_md5_filled_rate", gimmeRatio(y, x)))
          (result \ "unexpected_ua_click").asOpt[Long].foreach(y =>
            enriched += ("unexpected_ua_rate", gimmeRatio(y, x)))
          (result \ "unexpected_device_click").asOpt[Long].foreach(y =>
            enriched += ("unexpected_device_rate", gimmeRatio(y, x)))
          (result \ "unexpected_os_click").asOpt[Long].foreach(y =>
            enriched += ("unexpected_os_rate", gimmeRatio(y, x)))
          (result \ "fraud_click_filtered").asOpt[Long].foreach(y => {
            enriched += ("fraud_click_filtered_rate", gimmeRatio(y, x + y))
            (result \ "value_error_dark_device_id_mark").asOpt[Long].foreach(z =>
              enriched += ("value_error_dark_device_id_mark_rate", gimmeRatio(z, x + y)))
            (result \ "value_error_dark_ip_mark").asOpt[Long].foreach(z =>
              enriched += ("value_error_dark_ip_mark_rate", gimmeRatio(z, x + y)))
            (result \ "value_error_intranet_ip_mark").asOpt[Long].foreach(z =>
              enriched += ("value_error_intranet_ip_mark_rate", gimmeRatio(z, x + y)))
            (result \ "action_rate_click_mark").asOpt[Long].foreach(z =>
              enriched += ("action_rate_click_mark_rate", gimmeRatio(z, x + y)))
            (result \ "repeat_click_mark").asOpt[Long].foreach(z =>
              enriched += ("repeat_click_mark_rate", gimmeRatio(z, x + y)))
            (result \ "unexpected_os_mark").asOpt[Long].foreach(z =>
              enriched += ("unexpected_os_mark_rate", gimmeRatio(z, x + y)))
            (result \ "unexpected_device_mark").asOpt[Long].foreach(z =>
              enriched += ("unexpected_device_mark_rate", gimmeRatio(z, x + y)))
            (result \ "unexpected_ua_mark").asOpt[Long].foreach(z =>
              enriched += ("unexpected_ua_mark_rate", gimmeRatio(z, x + y)))
            (result \ "unexpected_model_mark").asOpt[Long].foreach(z =>
              enriched += ("unexpected_model_mark_rate", gimmeRatio(z, x + y)))
            (result \ "related_multi_ua_mark").asOpt[Long].foreach(z =>
              enriched += ("related_multi_ua_mark_rate", gimmeRatio(z, x + y)))
            (result \ "action_rate_device_ip_mark").asOpt[Long].foreach(z =>
              enriched += ("action_rate_device_ip_mark_rate", gimmeRatio(z, x + y)))
          })
        })
        (result \ "android_click").asOpt[Long].foreach(x => {
          (result \ "android_os_filled").asOpt[Long].foreach(y =>
            enriched += ("android_os_filled_rate", gimmeRatio(y, x)))
        })
        (result \ "ios_click").asOpt[Long].foreach(x => {
          (result \ "ios_os_filled").asOpt[Long].foreach(y =>
            enriched += ("ios_os_filled_rate", gimmeRatio(y, x)))
        })
        (result \ "conv").asOpt[Long].foreach(x => {
          (result \ "rta_bid_conv").asOpt[Long].foreach(y =>
            enriched +=("rta_conv_br", gimmeRatio(y, x)))
          (result \ "conv_time_follow_closely").asOpt[Long].foreach(y =>
            enriched +=("conv_time_follow_closely_rate", gimmeRatio(y, x)))
        })
        (result \ "rta_request_count").asOpt[Long].foreach(x => {
          (result \ "rta_bid_count").asOpt[Long].foreach(y =>
            enriched +=("rta_br", gimmeRatio(y, x)))
          (result \ "rta_exception_count").asOpt[Long].foreach(y =>
            enriched +=("rta_er", gimmeRatio(y, x)))
        })
        (result \ "conv_cost").asOpt[Long].foreach(y =>
          enriched +=("conv_cost", JsNumber(twoDecimalDivide(y, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "loc_conv_cost").asOpt[Long].foreach(y =>
          enriched +=("loc_conv_cost", JsNumber(twoDecimalDivide(y, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "conv_click_time_difference").asOpt[String]
          .foreach(x => enriched += ("conv_click_time_difference_desc", JsString(TimeDifferenceUtil.convClickTimeDifference(x))))
        (result \ "diff_a_click_time").asOpt[String]
          .foreach(x => enriched += ("diff_a_click_time_desc", JsString(TimeDifferenceUtil.diffClickTimeDesc(x))))
        (result \ "diff_c_click_time").asOpt[String]
          .foreach(x => enriched += ("diff_c_click_time_desc", JsString(TimeDifferenceUtil.diffClickTimeDesc(x))))
        (result \ "diff_s_click_time").asOpt[String]
          .foreach(x => enriched += ("diff_s_click_time_desc", JsString(TimeDifferenceUtil.diffClickTimeDesc(x))))
        (result \ "diff_p_click_time").asOpt[String]
          .foreach(x => enriched += ("diff_p_click_time_desc", JsString(TimeDifferenceUtil.diffClickTimeDesc(x))))
      }
      case "third_party_stat_v2" => {
        (result \ "click").asOpt[Long].foreach(x => {
          (result \ "impr").asOpt[Long].foreach(y =>
            enriched +=("actual_ctr", gimmeRatio(x, y)))
          (result \ "conv").asOpt[Long].foreach(y =>
            enriched +=("ccr", gimmeRatio(y, x)))
        })
      }
      case "course_stat" => {
        (result \ "click").asOpt[Long].foreach(x => {
          (result \ "impr").asOpt[Long].foreach(y =>
            enriched += ("actual_ctr", gimmeRatio(x, y)))
        })
      }
      case "brand_stat" => {
        (result \ "click").asOpt[Long].foreach(x => {
          (result \ "impr").asOpt[Long].foreach(y =>
            enriched += ("actual_ctr", gimmeRatio(x, y)))
        })
      }
      case "brand_stat_v2" =>
        (result \ "click").asOpt[Long].foreach(x => {
          (result \ "impr").asOpt[Long].foreach(y =>
            enriched += ("actual_ctr", gimmeRatio(x, y)))
        })
        (result \ "impr").asOpt[Long].foreach(x => {
          (result \ "bid").asOpt[Long].foreach(y =>
            enriched += ("ir", gimmeRatio(x, y)))
          (result \ "cpm").asOpt[Long].foreach(y => {
            enriched += ("ecpm", JsNumber(if (x.toDouble == 0) 0 else y.toDouble / x.toDouble / 100))
          })
        })
        (result \ "bid").asOpt[Long].foreach(x => {
          (result \ "pv").asOpt[Long].foreach(y =>
            enriched += ("br", gimmeRatio(x, y)))
        })
        (result \ "cpm").asOpt[Long].foreach(x => {
          enriched += ("cpm", JsNumber(x / 1000.0 / 100))
        })
      case "wow_outlog" =>
        (result \ "pv").asOpt[Long].foreach(x => {
          (result \ "uv").asOpt[Long].foreach(y =>
            enriched += ("pv_avg", JsString(
              (if (y.toDouble == 0) 0 else x.toDouble / y.toDouble).toString
            )))
        })
        (result \ "duration_efficient_ms").asOpt[Long].foreach(x => {
          (result \ "uv").asOpt[Long].foreach(y =>
            enriched += ("duration_efficient_ms_avg", JsString(
              (if (y.toDouble == 0) 0 else x.toDouble / y.toDouble).toString
            )))
        })
      case "sales_activity_event_log" =>
        (result \ "uv").asOpt[Long].foreach(x => enriched += "uv" -> JsString(x.toString))
      case "sdk_tdp_together" =>
        (result \ "app_id").asOpt[String].foreach(x => enriched += "app_id" -> chaLink("appId", x))
        (result \ "slot_id").asOpt[String].foreach(x => enriched += "slot_id" -> chaLink("slotId", x))
        (result \ "sponsor_id").asOpt[String].foreach(x => enriched += "sponsor_id" -> chaLinkSome("sponsorId", x))
        (result \ "campaign_id").asOpt[String].foreach(x => enriched += "campaign_id" -> chaLinkSome("adCampaignId", x))
        (result \ "group_id").asOpt[String].foreach(x => enriched += "group_id" -> chaLinkAll("groupId", x))
        (result \ "variation_id").asOpt[String].foreach(x => enriched += "variation_id" -> chaLinkAll("adContentId", x))
        (result \ "win_price").asOpt[Long]
          .foreach(x => enriched += ("adx_consume", JsNumber(x.toDouble / 100 / 1000)))
        (result \ "sdk_impr").asOpt[Long].foreach(x => {
          (result \ "win_price").asOpt[Long]
            .foreach(y => enriched += ("impr_ecpm", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
          (result \ "charge").asOpt[Long]
            .foreach(y => enriched += ("ecpm", JsNumber(twoDecimalDivide(y * 1000, x * CURRENCY_RATIO_YUAN_FEN))))
        })
        (result \ "sdk_click").asOpt[Long].foreach(x => {
          (result \ "sdk_impr").asOpt[Long]
            .foreach(y => enriched +=("actual_ctr", gimmeRatio(x, y)))
          (result \ "charge").asOpt[Long]
            .foreach(y => enriched +=("cpc", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
        })
        (result \ "tdp_click").asOpt[Long].foreach(x => {
          (result \ "tdp_click_uv").asOpt[Long]
            .foreach(y => enriched +=("tdp_click_uv_frequency", JsNumber(twoDecimalDivide(x, y))))
          (result \ "tdp_activate").asOpt[Long]
            .foreach(y => enriched +=("tdp_activate_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_register").asOpt[Long]
            .foreach(y => enriched +=("tdp_register_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_purchase").asOpt[Long]
            .foreach(y => enriched +=("tdp_purchase_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_custom").asOpt[Long]
            .foreach(y => enriched +=("tdp_custom_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_loc_custom").asOpt[Long]
            .foreach(y => enriched +=("tdp_loc_custom_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_day1retention").asOpt[Long]
            .foreach(y => enriched +=("tdp_day1retention_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_loc_activate").asOpt[Long]
            .foreach(y => enriched +=("tdp_loc_activate_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_loc_register").asOpt[Long]
            .foreach(y => enriched +=("tdp_loc_register_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_loc_purchase").asOpt[Long]
            .foreach(y => enriched +=("tdp_loc_purchase_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
          (result \ "tdp_loc_day1retention").asOpt[Long]
            .foreach(y => enriched +=("tdp_loc_day1retention_rate", JsNumber(nDecimalDivide(y * 10000, x, 8))))
        })
        (result \ "charge").asOpt[Long]
          .foreach(x => enriched +=("charge", JsNumber(twoDecimalDivide(x, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "tdp_order_amount").asOpt[Long]
          .foreach(x => enriched +=("tdp_order_amount", JsNumber(twoDecimalDivide(x, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "tdp_first_day_order_amount").asOpt[Long]
          .foreach(x => enriched +=("tdp_first_day_order_amount", JsNumber(twoDecimalDivide(x, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "tdp_loc_order_amount").asOpt[Long]
          .foreach(x => enriched +=("tdp_loc_order_amount", JsNumber(twoDecimalDivide(x, CURRENCY_RATIO_YUAN_FEN))))
        (result \ "tdp_click_uv").asOpt[Long]
          .foreach(x => enriched += "tdp_click_uv" -> JsString(x.toString))
        (result \ "tdp_conv_uv").asOpt[Long]
          .foreach(x => enriched += "tdp_conv_uv" -> JsString(x.toString))
        (result \ "channel_id_prefix").asOpt[String]
          .foreach(x => enriched += ("identify1", JsString(ydbc2identify1(x))))
      case "ead_dmp_stat" =>
        (result \ "uv").asOpt[Long].foreach(x => enriched += "uv" -> JsString(x.toString))
      case "portrait_by_ead_dmp" =>
        (result \ "birth_y").asOpt[String].foreach(x => enriched += "age" -> JsNumber(PortraitByEadDmpUtil.calculateAge(x)))
        (result \ "app_id").asOpt[String].foreach(x => enriched += "app_id" -> chaLink("appId", x))
        (result \ "slot_id").asOpt[String].foreach(x => enriched += "slot_id" -> chaLink("slotId", x))
        (result \ "sponsor_id").asOpt[String].foreach(x => enriched += "sponsor_id" -> chaLinkSome("sponsorId", x))
        (result \ "campaign_id").asOpt[String].foreach(x => enriched += "campaign_id" -> chaLinkSome("adCampaignId", x))
        (result \ "group_id").asOpt[String].foreach(x => enriched += "group_id" -> chaLinkAll("groupId", x))
        (result \ "variation_id").asOpt[String].foreach(x => enriched += "variation_id" -> chaLinkAll("adContentId", x))
        (result \ "win_price").asOpt[Long]
          .foreach(x => enriched += ("adx_consume", JsNumber(x.toDouble / 100 / 1000)))
        (result \ "impr").asOpt[Long].foreach(x => {
          (result \ "win_price").asOpt[Long]
            .foreach(y => enriched += ("impr_ecpm", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
          (result \ "charge").asOpt[Long]
            .foreach(y => enriched += ("ecpm", JsNumber(twoDecimalDivide(y * 1000, x * CURRENCY_RATIO_YUAN_FEN))))
        })
        (result \ "clk").asOpt[Long].foreach(x => {
          (result \ "impr").asOpt[Long]
            .foreach(y => enriched +=("actual_ctr", gimmeRatio(x, y)))
          (result \ "charge").asOpt[Long]
            .foreach(y => enriched +=("cpc", JsNumber(twoDecimalDivide(y, x * CURRENCY_RATIO_YUAN_FEN))))
        })
        (result \ "clk").asOpt[Long].foreach(x => {
          (result \ "conv_activate").asOpt[Long]
            .foreach(y => enriched +=("conv_activate_rate", JsNumber(twoDecimalDivide(y * 10000, x))))
          (result \ "conv_register").asOpt[Long]
            .foreach(y => enriched +=("conv_register_rate", JsNumber(twoDecimalDivide(y * 10000, x))))
          (result \ "conv_purchase").asOpt[Long]
            .foreach(y => enriched +=("conv_purchase_rate", JsNumber(twoDecimalDivide(y * 10000, x))))
          (result \ "conv_day1retention").asOpt[Long]
            .foreach(y => enriched +=("conv_day1retention_rate", JsNumber(twoDecimalDivide(y * 10000, x))))
        })
        (result \ "charge").asOpt[Long]
          .foreach(x => enriched += "charge" -> JsNumber(twoDecimalDivide(x, CURRENCY_RATIO_YUAN_FEN)))
        (result \ "order_amount").asOpt[Long]
          .foreach(x => enriched +="order_amount" -> JsNumber(twoDecimalDivide(x, CURRENCY_RATIO_YUAN_FEN)))
        (result \ "impr_uv").asOpt[Long]
          .foreach(x => enriched += "impr_uv" -> JsString(x.toString))
        (result \ "click_uv").asOpt[Long]
          .foreach(x => enriched += "click_uv" -> JsString(x.toString))
        (result \ "conv_uv").asOpt[Long]
          .foreach(x => enriched += "conv_uv" -> JsString(x.toString))
        (result \ "type").asOpt[String]
          .foreach(x => enriched += ("type_name", JsString(PortraitByEadDmpUtil.enumTypeZhixuanOrCpa(x))))
        (result \ "cpa_source").asOpt[String]
          .foreach(x => enriched += ("cpa_source_name", JsString(PortraitByEadDmpUtil.enumSourceChannelOrConvAccelerator(x))))
        (result \ "gender").asOpt[String]
          .foreach(x => enriched += ("gender_name", JsString(PortraitByEadDmpUtil.nameByKey("gender", x))))
        (result \ "city_grade").asOpt[String]
          .foreach(x => enriched += ("city_grade_name", JsString(PortraitByEadDmpUtil.nameByKey("city_grade", x))))
        (result \ "province").asOpt[String]
          .foreach(x => enriched += ("province_name", JsString(PortraitByEadDmpUtil.nameByKey("province", x))))
        (result \ "reside").asOpt[String]
          .foreach(x => enriched += ("reside_name", JsString(PortraitByEadDmpUtil.nameByKey("reside", x))))
        (result \ "reside_estate_price").asOpt[String]
          .foreach(x => enriched += ("reside_estate_price_name", JsString(PortraitByEadDmpUtil.nameByKey("reside_estate_price", x))))
        (result \ "level1_car_brand_180d").asOpt[String]
          .foreach(x => enriched += ("level1_car_brand_180d_name", JsString(PortraitByEadDmpUtil.nameByKey("level1_car_brand_180d", x))))
        (result \ "phone_brand_list").asOpt[String]
          .foreach(x => enriched += ("phone_brand_list_name", JsString(PortraitByEadDmpUtil.nameByKey("phone_brand_list", x))))
        (result \ "phone_price_list").asOpt[String]
          .foreach(x => enriched += ("phone_price_list_name", JsString(PortraitByEadDmpUtil.nameByKey("phone_price_list", x))))
        (result \ "edu_degree").asOpt[String]
          .foreach(x => enriched += ("edu_degree_name", JsString(PortraitByEadDmpUtil.nameByKey("edu_degree", x))))
        (result \ "school_type").asOpt[String]
          .foreach(x => enriched += ("school_type_name", JsString(PortraitByEadDmpUtil.nameByKey("school_type", x))))
        (result \ "profession").asOpt[String]
          .foreach(x => enriched += ("profession_name", JsString(PortraitByEadDmpUtil.nameByKey("profession", x))))
        (result \ "income").asOpt[String]
          .foreach(x => enriched += ("income_name", JsString(PortraitByEadDmpUtil.nameByKey("income", x))))
        (result \ "pay_monetary_all").asOpt[String]
          .foreach(x => enriched += ("pay_monetary_all_name", JsString(PortraitByEadDmpUtil.nameByKey("pay_monetary_all", x))))
        (result \ "interest_all").asOpt[String]
          .foreach(x => enriched += ("interest_all_name", JsString(PortraitByEadDmpUtil.nameByKey("interest_all", x))))
        (result \ "pay_will").asOpt[String]
          .foreach(x => enriched += ("pay_will_name", JsString(PortraitByEadDmpUtil.nameByKey("pay_will", x))))
      case _ =>
    }

    enriched
  }


  def gimmeRatio(x: Double, y: Long): JsString = {
    JsString(if (y.toDouble == 0) "0%"
    else {
      val ctr = x / y.toDouble
      if (ctr < 1)
        new DecimalFormat("#.####%").format(ctr)
      else {
        new DecimalFormat("#.###%").format(ctr)
      }
    })
  }

  private val pool: ExecutorService = Executors.newFixedThreadPool(100)
  val client: Http = Http.configure(_.setExecutorService(pool).setRequestTimeoutInMs(15 * 60 * 1000).setIdleConnectionTimeoutInMs(15 * 60 * 1000))


  def doRequest(dataSource: String, broker: String, query: JsObject, dropZeros: Boolean, user: String): (Long, JsValue, String) = {
    val realBroker = NEW_BROKER
    val start = System.currentTimeMillis

    var realQuery = query

    // 互动广告需要去sdk_stat表查询
    if ("iad_stat".equals(dataSource)) {
      realQuery = query.copy() ++ Json.obj("dataSource" -> "sdk_stat")
    }

    // 查询指标包含点击转化、点击转化成本时需要使用union dataSource
    if (queryLocConv(query)) {
      if (dataSource.equals("third_party_stat")) {
        realQuery = realQuery ++ Json.obj("dataSource" -> druidUnionDatasource(dataSource, "third_party_stat_loc_conv"))
      } else if (dataSource.equals("sdk_tdp_together")) {
        realQuery = realQuery ++ Json.obj("dataSource" -> druidUnionDatasource(dataSource, "sdk_tdp_together_loc_conv"))
      } else {
        var ds = dataSource
        if ("sdk_stat_new".equals(dataSource)) {
          ds = "sdk_stat"
        }
        realQuery = realQuery ++ Json.obj("dataSource" -> druidUnionDatasource("click_time_conv", ds))
      }
    }

    log.info(s"user: ${user} query conditions: " + realQuery.toString())

    val brokerService = url(realBroker).POST.setContentType("application/json", "UTF-8") << realQuery.toString
    val result = client(brokerService.OK(as.String.utf8))
    Await.result(result, 15 minutes)
    val normalizedResult = normalizeResult(query, Json.parse(result()), dropZeros)
    Tuple3(System.currentTimeMillis - start, normalizedResult, realQuery.toString())
  }

  def queryLocConv(query: JsObject): Boolean = {
    for (agg <- query.\("aggregations").asInstanceOf[JsArray].value) {
      // filedName不一定存在，也有可能是fieldNames
        /*
            {
              "type":"javascript",
              "name":"iad_click",
              "fieldNames":[
                "iad_click",
                "iad_outer_face_click"
              ],
              "fnAggregate":"function(current, a, b) { return current + a + b; }",
              "fnCombine":"function(a, b) { return a + b; }",
              "fnReset":"function() { return 0; }"
            },
        */
      if (!agg.\("fieldName").isInstanceOf[JsUndefined]) {
        val metric = agg.\("fieldName").asInstanceOf[JsString].value
        if (metric.equals("loc_conv") || metric.equals("loc_conv_cost")) {
          return true
        }
        if (metric.equals("tdp_loc_conv_uv") || metric.equals("tdp_loc_conv") || metric.equals("tdp_loc_download")
          || metric.equals("tdp_loc_activate") || metric.equals("tdp_loc_register") || metric.equals("tdp_loc_addtocart")
          || metric.equals("tdp_loc_purchase") || metric.equals("tdp_loc_day1retention") || metric.equals("tdp_loc_credit")
          || metric.equals("tdp_loc_custom") || metric.equals("tdp_loc_other") || metric.equals("tdp_loc_order_amount")) {
          return true
        }
      }
    }
    false
  }

  def druidUnionDatasource(ds1: String, ds2: String): JsObject = {
    Json.obj ("type" -> "union").+("dataSources" -> Json.arr(ds1, ds2))
  }

  val metrics = Map(
    "yex_new" -> List(
      "bid_request_count" -> "bid_request_count",
      "imp_count" -> "imp_count",
      "ad_request_count" -> "ad_request_count",
      "bid_response_count" -> "bid_response_count",
      "bid_response_count_timeout" -> "bid_response_count_timeout",
      "bid_response_asset_replace_filter" -> "bid_response_asset_replace_filter",
      "bid_response_count_parse_error" -> "bid_response_count_parse_error",
      "bid_response_count_unknown_error" -> "bid_response_count_unknown_error",
      "bid_response_count_no_bid" -> "bid_response_count_no_bid",
      "bid_response_count_no_bd_client" -> "bid_response_count_no_bd_client",
      "bid_response_count_connection_failed" -> "bid_response_count_connection_failed",
      "bid_response_count_request_building_error" -> "bid_response_count_request_building_error",
      "seat_bid_count" -> "seat_bid_count",
      "bid_count" -> "bid_count",
      "bid_count_win" -> "bid_count_win",
      "bid_count_invalid" -> "bid_count_invalid",
      "bid_count_lose" -> "bid_count_lose",
      "bid_count_test" -> "bid_count_test",
      "received_imp_count" -> "received_imp_count",
      "received_ad_request_count" -> "received_ad_request_count",
      "win_price" -> "win_price",
      "bid_price_of_win" -> "bid_price_of_win",
      "original_cur_bid_price" -> "bid_price(原币种)",
      "impr_count" -> "impr_count",
      "impr_price" -> "impr_price",
      "original_cur_impr_price" -> "impr_price(原币种)",
      "adx_bid_price" -> "adx出价价格",
      "adx_bid_count_win" -> "adx竞胜数",
      "adx_win_price" -> "adx竞胜winprice",
      "adx_impr_price" -> "adx展示winprice",
      "click_count" -> "click_count",
      "bid_count_valid" -> "竞价响应有效数",
      "bid_count_invalid_imp_id" -> "竞价响应impId无效数",
      "bid_count_invalid_asset_id" -> " 竞价响应assetId无效数",
      "bid_count_invalid_price" -> "竞价响应价格无效数",
      "bid_count_invalid_attri" -> "竞价响应attri无效数",
      "bid_count_no_attri" -> "竞价响应无attri数",
      "bid_count_invalid_url" -> "竞价响应url无效数",
      "bid_count_no_landing_page" -> "竞价响应无落地页数",
      "bid_count_illegal_asset" -> "竞价响应物料非法数",
      "bid_count_no_vast_tag" -> "竞价响应无vast数",
      "bid_count_invalid_vast_tag" -> "竞价响应vast格式非法数",
      "bid_count_invalid_image_url" -> "竞价响应图片url无效数",
      "bid_count_invalid_landing_page" -> "竞价响应落地页无效数",
      "bid_count_empty_white_list" -> "app对应url白名单为空数",
      "bid_count_invalid_youdao_initiative" -> "主动配置响应无效数",
      "bid_count_invalid_download_app_info" -> "竞价响应安卓七要素无效数",
      "bid_count_quick_app_ad_blocked" -> "竞价响应快应用广告无效数",
      "apk_download_start" -> "开始下载数",
      "apk_download_complete" -> "下载完成数",
      "apk_install_start" -> "开始安装数",
      "apk_install_complete" -> "安装完成数",
      "wechat_call_up" -> "小程序尝试吊起量",
      "wechat_success" -> "小程序吊起成功量",
      "wechat_fail" -> "小程序吊起失败量",
      "dp_call_up" -> "deeplink尝试吊起量",
      "dp_installed" -> "deeplink当前投放应用已安装量",
      "dp_success" -> "deeplink成功吊起量",
      "dp_not_install" -> "deeplink未安装吊起失败量",
      "dp_installed_fail" -> "deeplink已安装吊起失败量"
    ),
    "yex_flat_new" -> List(
      "bid_request_count" -> "bid_request_count",
      "imp_count" -> "imp_count",
      "ad_request_count" -> "ad_request_count",
      "bid_response_count" -> "bid_response_count",
      "bid_response_count_timeout" -> "bid_response_count_timeout",
      "bid_response_asset_replace_filter" -> "bid_response_asset_replace_filter",
      "bid_response_count_parse_error" -> "bid_response_count_parse_error",
      "bid_response_count_no_bid" -> "bid_response_count_no_bid",
      "seat_bid_count" -> "seat_bid_count",
      "bid_count" -> "bid_count",
      "bid_count_win" -> "bid_count_win",
      "bid_count_invalid" -> "bid_count_invalid",
      "bid_count_lose" -> "bid_count_lose",
      "bid_count_test" -> "bid_count_test",
      "received_imp_count" -> "received_imp_count",
      "received_ad_request_count" -> "received_ad_request_count",
      "win_price" -> "win_price",
      "bid_price_of_win" -> "bid_price_of_win",
      "impr_count" -> "impr_count",
      "impr_price" -> "impr_price",
      "click_count" -> "click_count"
    ),
    "ead_stat_new" -> List(
      "pv" -> "pv",
      "impr" -> "展示",
      "click" -> "点击",
      "charge" -> "消费"),
    "sdk_stat_new" -> List(
      "pv" -> "请求数",
      "request_ad_num" -> "请求广告数",
      "uv" -> "请求独立设备数",
      "bid" -> "竞价数",
      "ub" -> "竞价独立设备数",
      "bid_price" -> "adx出价价格",
      "win_count" -> "adx竞胜数",
      "adx_win_price" -> "adx竞胜winprice",
      "impr" -> "展示",
      "impr_dedup" -> "展示去重",
      "ui" -> "展示独立设备数",
      "win_price" -> "adx展示winprice",
      "win_price_dedup" -> "adx展示winprice(展示去重)",
      "click" -> "点击",
      "discarded_click" -> "过耗点击",
      "fraud_click" -> "作弊点击",
      "uc" -> "点击独立设备数",
      "charge" -> "消费",
      "discarded_charge" -> "过耗消费",
      "fraud_charge" -> "作弊消费",
      "conv" -> "当日转化",
      "impr_conv" -> "曝光转化",
      "opt_conv" -> "oCPC优化目标当日转化",
      "loc_conv_cost" -> "预期转化消费",
      "loc_conv" -> "点击转化",
      "loc_opt_conv" -> "oCPC优化目标点击转化",
      "order_amount" -> "转化订单金额",
      "first_day_order_amount" -> "当日付费",
      "in_24_hours_order_amount" -> "24小时内付费",
      "pcnt0" -> "pcnt0",
      "pcnt25" -> "pcnt25",
      "pcnt50" -> "pcnt50",
      "pcnt75" -> "pcnt75",
      "pcnt100" -> "pcnt100",
      "mute" -> "mute",
      "unmute" -> "unmute",
      "close" -> "close",
      "loaded" -> "loaded",
      "referer_count" -> "带refere请求数",
      "charged_and_discarded_click_delay_seconds" -> "计费和丢弃点击竞价延迟时间:秒",
      "bid_predict_cost" -> "竞价预估消费",
      "bid_time_charge" -> "竞价实际消费",
      "ctr" -> "参竞预估点击率",
      "pr_cvr" -> "参竞预估转化率",
      "ctr_of_impr" -> "展示预估点击率",
      "pr_cvr_of_impr" -> "展示预估转化率",
      "apk_download_start" -> "开始下载数",
      "apk_download_complete" -> "下载完成数",
      "apk_install_start" -> "开始安装数",
      "apk_install_complete" -> "安装完成数",
      "wechat_call_up" -> "小程序尝试吊起量",
      "wechat_success" -> "小程序吊起成功量",
      "wechat_fail" -> "小程序吊起失败量",
      "dp_call_up" -> "deeplink尝试吊起量",
      "dp_installed" -> "deeplink当前投放应用已安装量",
      "dp_success" -> "deeplink成功吊起量",
      "dp_not_install" -> "deeplink未安装吊起失败量",
      "dp_installed_fail" -> "deeplink已安装吊起失败量",
      "conv_activate" -> "激活数",
      "conv_register" -> "注册数",
      "conv_purchase" -> "购买数",
      "conv_custom" -> "自定义事件数"
    ),
    "iad_stat" -> List(
      "iad_bid" -> "互动广告竞价数",
      "impr" -> "展示数",
      "iad_impr" -> "入口展示数",
      "click" -> "点击数",
      "iad_click" -> "入口点击数",
      "charge" -> "消费数",
      "iad_charge" -> "入口消费",
      "iad_page" -> "落地页pv数",
      "iad_user_click" -> "二跳点击数",
      "pv" -> "二跳请求数",
      "3rd_jump_impr" -> "三跳展示数",
      "3rd_jump_click" -> "三跳点击数",
      "conv" -> "三跳转化数",
      "3rd_jump_charge" -> "三跳消费:元",
      "iad_backup_impr" -> "垫底广告展示数",
      "iad_backup_click" -> "垫底广告点击数"
    ),
    "impr_sdk_joined_new" -> List(
      "impr" -> "展示",
      "ui" -> "展示独立设备数"
    ),
    "sdk_stat" -> List(
      "pv" -> "请求数",
      "req_ad_num" -> "请求广告数",
      "uv" -> "请求独立设备数",
      "bid" -> "竞价数",
      "ub" -> "竞价独立设备数",
      "impr" -> "展示",
      "ui" -> "展示独立设备数",
      "click" -> "点击",
      "uc" -> "点击独立设备数",
      "charge" -> "消费",
      "conv" -> "转化",
      "opt_conv" -> "oCPC优化目标当日转化"
    ),
    "sdk_stat_v2" -> List(
      "pv" -> "请求数",
      "request_ad_num" -> "请求广告数",
      "uv" -> "请求独立设备数",
      "bid" -> "竞价数",
      "impr" -> "展示",
      "click" -> "点击",
      "charge" -> "消费",
      "discarded_click" -> "过耗点击",
      "discarded_charge" -> "过耗消费",
      "conv" -> "当日转化",
      "impr_conv" -> "曝光转化",
      "opt_conv" -> "oCPC优化目标当日转化",
      "loc_conv_cost" -> "预期转化消费",
      "loc_conv" -> "点击转化",
      "loc_opt_conv" -> "oCPC优化目标点击转化",
      "order_amount" -> "转化订单金额",
      "first_day_order_amount" -> "当日付费",
      "in_24_hours_order_amount" -> "24小时内付费",
      "pcnt0" -> "pcnt0",
      "pcnt25" -> "pcnt25",
      "pcnt50" -> "pcnt50",
      "pcnt75" -> "pcnt75",
      "pcnt100" -> "pcnt100",
      "mute" -> "mute",
      "unmute" -> "unmute",
      "close" -> "close",
      "loaded" -> "loaded"
    ),
    "third_party_stat" -> List(
      "rta_request_count" -> "rta请求数",
      "rta_bid_count" -> "rta参竞数",
      "rta_exception_count" -> "rta异常数",
      "impr" -> "展示",
      "click" -> "点击",
      "discard_click" -> "无效点击(discard)",
      "fraud_click_unfiltered" -> "未被过滤的作弊点击数",
      "fraud_click_filtered" -> "被过滤的作弊点击数",
      "repeat_click" -> "重复点击",
      "rta_bid_click" -> "参竞点击",
      "conv" -> "当日转化",
      "loc_conv" -> "点击转化",
      "rta_bid_conv" -> "参竞转化",
      "order_amount" -> "转化订单金额",
      "first_day_order_amount" -> "当日付费",
      "in_24_hours_order_amount" -> "24小时内付费",
      "loc_order_amount" -> "点击转化订单金额",
      "android_click" -> "android点击数",
      "ios_click" -> "ios点击数",
      "channel_did_filled" -> "渠道DID携带数",
      "ua_filled" -> "ua携带数",
      "ip_filled" -> "ip携带数",
      "ts_filled" -> "点击时间携带数",
      "req_id_filled" -> "rta请求id携带数",
      "activity_id_filled" -> "推广活动id携带数",
      "android_os_filled" -> "android操作系统携带数",
      "ios_os_filled" -> "ios操作系统携带数",
      "device_model_filled" -> "手机机型携带数",
      "imei_filled" -> "imei携带数",
      "oaid_filled" -> "oaid携带数",
      "oaid_md5_filled" -> "oaidMd5携带数",
      "idfa_filled" -> "idfa携带数",
      "idfa_md5_filled" -> "idfaMd5携带数",
      "caid_filled" -> "caid携带数",
      "caid_md5_filled" -> "caidMd5携带数",
      "alid_filled" -> "aaid携带数",
      "android_id_filled" -> "androidId携带数",
      "android_id_md5_filled" -> "androidIdMd5携带数",
      "unexpected_ua_click" -> "ua与推广类型不一致点击数",
      "unexpected_device_click" -> "设备类型与推广类型不一致点击数",
      "unexpected_os_click" -> "操作系统与推广类型不一致点击数",
      "blank_param_filtered" -> "参数为空过滤数",
      "model_filtered" -> "机型过滤数",
      "value_error_dark_device_id_mark" -> "点击设备号黑名单识别数",
      "value_error_dark_ip_mark" -> "点击IP黑名单识别数",
      "value_error_intranet_ip_mark" -> "点击内网IP识别数",
      "action_rate_click_mark" -> "设备号日点击量级过大识别数",
      "repeat_click_mark" -> "重复点击识别数",
      "unexpected_os_mark" -> "点击操作系统与应用推广类型不一致识别数",
      "unexpected_device_mark" -> "点击设备号与应用推广类型不一致识别数",
      "unexpected_ua_mark" -> "点击UA与应用推广类型不一致识别数",
      "unexpected_model_mark" -> "点击手机机型与应用推广类型不一致识别数",
      "related_multi_ua_mark" -> "点击设备号关联多个UA识别数",
      "action_rate_device_ip_mark" -> "点击IP高频变化识别数",
      "conv_time_follow_closely" -> "转化劫持数"
    ),
    "third_party_stat_v2" -> List(
      "impr" -> "展示",
      "click" -> "点击",
      "conv" -> "当日转化",
      "order_amount" -> "转化订单金额",
      "first_day_order_amount" -> "当日付费",
      "in_24_hours_order_amount" -> "24小时内付费"
    ),
    "course_stat" -> List(
      "impr" -> "展示",
      "click" -> "点击"
    ),
    "click_ead_fraud" -> List(
      "pv" -> "pv",
      "impr" -> "展示",
      "click" -> "点击",
      "charge" -> "消费"),
    "click_ead_discarded" -> List(
      "pv" -> "pv",
      "impr" -> "展示",
      "click" -> "点击",
      "charge" -> "消费"),
    "univ_ads" -> List(
      "count" -> "数量", "pv" -> "pv"),
    "eadv_mobile_log" -> List(
      "impr" -> "impr", "click" -> "click", "impr_uv" -> "impr_uv", "click_uv" -> "click_uv"),
    "ssp_log" -> List(
      "count" -> "数量"),
    "dsp_stat_v2" -> List(
      "pv" -> "pv", "bid" -> "bid", "impr" -> "impr", "win_price" -> "win_price", "click" -> "click", "charge" -> "charge"),
    "dsp_stat" -> List(
      "pv" -> "pv", "bid" -> "bid", "impr" -> "impr", "win_price" -> "win_price", "click" -> "click", "charge" -> "charge"),
    "dsp_stat_new" -> List(
      "pv" -> "pv",
      "bid" -> "bid",
      "impr" -> "impr",
      "win_price" -> "win_price",
      "click" -> "click",
      "charge" -> "charge",
      "bid_price" -> "bid_price"
    ),
    "xuetang_course" -> List(
      "count" -> "count", "uv" -> "uv"),
    "temp" -> List(
      "count" -> "count", "longSum" -> "longSum"),
    "infoline_article" -> List(
      "count" -> "count", "uv" -> "uv"),
    "readease_stat" -> List(
      "count" -> "count",
      "pv" -> "pv",
      "impr" -> "impr",
      "click" -> "click",
      "content" -> "content",
      "duration" -> "duration_count",
      "duration_total" -> "duration_total",
      "uv" -> "uv"),
    "readease_stat_join" -> List(
      "pv-uv" -> "pv",
      "total_impr" -> "impr",
      "total_click" -> "click",
      "duration_total" -> "duration_total",
      "uv" -> "uv"),
    "sdk_analyzer" -> List(
      "fd_lt_sum" -> "fd_lt_sum",
      "fd_pt_sum" -> "fd_pt_sum",
      "fd_ht_sum" -> "fd_ht_sum",
      "fd_count" -> "fd_count",
      "ht_sum" -> "ht_sum",
      "ht_max" -> "ht_max",
      "lt_sum" -> "lt_sum",
      "pt_sum" -> "pt_sum",
      "count" -> "count"),
    "luna_article_stat" -> List(
      "pv" -> "cardDisplay",
      "click" -> "pageView",
      "comment_uv" -> "commentUv",
      "share_uv" -> "shareUv",
      "like_uv" -> "likeUv",
      "dislike_uv" -> "dislikeUv",
      "favorites_uv" -> "favoritesUv",
      "comment" -> "comment",
      "share" -> "share",
      "like" -> "like",
      "dislike" -> "dislike",
      "favorites" -> "favorites",
      "pv_uv" -> "cardUV",
      "playDuration" -> "playDuration",
      "playTimes" -> "playTimes",
      "click_uv" -> "pageUV"
    ),
    "brand_stat" -> List(
      "click" -> "点击数",
      "impr" -> "展示数",
      "uc" -> "点击独立设备数",
      "ui" -> "展示独立设备数"
    ),
    "brand_stat_v2" -> List(
      "pv" -> "请求数",
      "bid" -> "竞价数",
      "impr" -> "展示数",
      "click" -> "点击数",
      "ui" -> "展示独立设备数",
      "uc" -> "点击独立设备数",
      "cpm" -> "消耗:元"
    ),
    "bid_failed_stat" -> List(
      "pv" -> "次数"
    ),
    "failed_send_click_stat" -> List(
      "pv" -> "次数"
    ),
    "yex_invalid_traffic_stat" -> List(
      "pv" -> "请求数"
    ),
    "sdk_report_stat" -> List(
      "deep_link_invoke_count" -> "DeepLink调起数",
      "app_download_count" -> "APP下载数"
    ),
    "tiny_url_visit" -> List(
      "impr" -> "访问数"
    ),
    "mediation_sdk_report" -> List(
      "app_req" -> "app请求聚合sdk数",
      "sdk_req" -> "聚合sdk请求广告数",
      "sdk_res" -> "聚合sdk接收广告数",
      "impr" -> "聚合sdk展示数",
      "click" -> "聚合sdk点击数",

      "pull_config_success" -> "配置拉取成功数",
      "pull_config_fail" -> "配置拉取失败数",
      "sd_app_fill_group_req" -> "APP缓存请求广告组数",
      "sd_app_fill_group_rsp" -> "APP缓存返回广告组数",
      "sd_group_real_time_load" -> "实时请求广告组数",
      "sd_group_real_time_loaded" -> "实时请求返回广告组数",
      "sd_sdk_fill_group_req" -> "SDK缓存请求广告组数",
      "sd_sdk_fill_group_rsp" -> "SDK缓存广告返回组数",

      "sd_response_timeout" -> "DSP返回超时数",
      "sc_load_empty" -> "SDK缓存池为空数",
      "sc_load_out_date" -> "SDK缓存池广告过期数",
      "app_ad_show" -> "总广告展示数",
      "app_ad_click" -> "总广告点击数",
      "as_load_fail_timeout" -> "SDK返回给客户端超时数",
      "as_load_fail_no_network" -> "SDK返回给客户端无网数",
      "as_load_fail_no_rsp" -> "SDK返回给客户端无填充数",
      "as_rsp_fail_to_app" -> "SDK已拉取广告未返回给客户端数",
      "ad_out_date" -> "广告过期数"
    ),
    "wow_outlog" -> List(
      "pv" -> "pv",
      "uv" -> "uv",
      "duration_efficient_ms" -> "有效播放时长ms",
      "duration_actually_ms" -> "实际播放时长ms",
      "interactive" -> "互动类行为"
    ),
    "sales_activity_event_log" -> List(
      "show_count" -> "展示数",
      "click_count" -> "点击数",
      "leave_count" -> "离开数",
      "count" -> "pv",
      "uv" -> "uv"
    ),
    "brand_abnor_url_stat" -> List(
      "count" -> "上报次数"
    ),
    "sdk_tdp_together" -> List(
      "sdk_impr" -> "展示",
      "sdk_impr_uv" -> "展示设备数",
      "win_price" -> "adx展示winprice",
      "sdk_click" -> "点击",
      "charge" -> "消费:元",
      "tdp_click" -> "tdp点击",
      "tdp_click_uv" -> "tdp点击设备数",
      "tdp_conv" -> "tdp转化",
      "tdp_conv_uv" -> "tdp转化设备数",
      "tdp_download" -> "tdp下载",
      "tdp_activate" -> "tdp激活",
      "tdp_register" -> "tdp注册",
      "tdp_addtocart" -> "tdp加入购物车",
      "tdp_purchase" -> "tdp购买",
      "tdp_day1retention" -> "tdp次留",
      "tdp_credit" -> "tdp授信",
      "tdp_custom" -> "tdp自定义转化行为",
      "tdp_other" -> "tdp其他转化行为",
      "tdp_order_amount" -> "tdp转化订单金额:元",
      "tdp_first_day_order_amount" -> "tdp激活日付费数:元",
      "tdp_loc_conv" -> "tdp_loc转化",
      "tdp_loc_conv_uv" -> "tdp_loc转化设备数",
      "tdp_loc_download" -> "tdp_loc下载",
      "tdp_loc_activate" -> "tdp_loc激活",
      "tdp_loc_register" -> "tdp_loc注册",
      "tdp_loc_addtocart" -> "tdp_loc加入购物车",
      "tdp_loc_purchase" -> "tdp_loc购买",
      "tdp_loc_day1retention" -> "tdp_loc次留",
      "tdp_loc_credit" -> "tdp_loc授信",
      "tdp_loc_custom" -> "tdp_loc自定义转化行为",
      "tdp_loc_other" -> "tdp_loc其他转化行为",
      "tdp_loc_order_amount" -> "tdp_loc转化订单金额:元"
    ),
    "ead_dmp_stat" -> List(
      "uv" -> "独立设备数"
    ),
    "portrait_by_ead_dmp" -> List(
      "impr" -> "展示",
      "impr_uv" -> "展示设备数",
      "win_price" -> "adx展示winprice",
      "clk" -> "点击",
      "click_uv" -> "点击设备数",
      "charge" -> "消费:元",
      "conv" -> "转化",
      "conv_uv" -> "转化设备数",
      "conv_download" -> "下载",
      "conv_activate" -> "激活",
      "conv_register" -> "注册",
      "conv_addtocart" -> "加入购物车",
      "conv_purchase" -> "购买",
      "conv_day1retention" -> "次留",
      "conv_credit" -> "授信",
      "conv_custom" -> "自定义转化行为",
      "order_amount" -> "转化订单金额:元"
    )
  )
  val dimensions = Map(
    "yex_new" -> List("" -> List(
      "dc_type" -> "dc_type",
      "dc_id" -> "dc_id",
      "app_version" -> "应用版本",
      "req_bundle" -> "流量方应用包名",
      "slot_id" -> "slot_id",
      "hostname" -> "hostname",
      "vender_id" -> "vender_id",
      "vender_source" -> "vender_source",
      "gorgon_hostname" -> "gorgon_hostname",
      "bid_response_time_step" -> "响应时长区间",
      "country_name" -> "国家",
      "assets_flat" -> "assets_flat",
      "imp_type" -> "imp_type",
      "dsp_id" -> "dsp_id",
      "cur" -> "币种",
      "dsp_ad_type" -> "dsp_ad_type",
      "seat_id" -> "seat_id",
      "mac" -> "mac",
      "nbr" -> "nbr",
      "ssid" -> "ssid",
      "ssids" -> "模板IDs",
      "deal_id" -> "deal_id",
      "sid" -> "样式ID",
      "dict_post_id" -> "词典帖子id",
      "click_interact_type" -> "点击互动类型",
      "brand_source_slot_id" -> "品牌流量原始广告位id",
      "brand_orientation_type" -> "品牌广告定向类型",
      "crid" -> "广告变体ID",
      "ad_group_id" -> "推广组ID",
      "cid" -> "广告系列ID",
      "sponsor_id" -> "广告商ID",
      "gorgon_interface_type" -> "gorgon接口类型"
    )),
    "yex_flat_new" -> List("" -> List(
      "dc_type" -> "dc_type",
      "dc_id" -> "dc_id",
      "slot_id" -> "slot_id",
      "assets_flat" -> "assets_flat",
      "imp_type" -> "imp_type",
      "dsp_id" -> "dsp_id",
      "seat_id" -> "seat_id",
      "cid" -> "cid",
      "crid" -> "crid",
      "nbr" -> "nbr"
    )),
    "ead_stat_new" -> List(
      "广告" -> List(
        "agent_id" -> "代理商",
        "sponsor_id" -> "广告商",
        "campaign_id" -> "广告系列",
        "group_id" -> "广告组",
        "variation_id" -> "广告变体",
        "keyword_id" -> "关键字",
        "ad_type" -> "广告类型"
      ), "流量" -> List(
        "channel_id" -> "频道",
        "synd_id" -> "联盟",
        "site_id" -> "站点",
        "slot_id" -> "广告位",
        "impr_pos" -> "展示位置",
        "impr_province" -> "省份",
        "impr_city" -> "城市",
        "slot_size" -> "广告位尺寸",
        "ad_size" -> "广告尺寸"
      ), "策略" -> List(
        "match_id" -> "算法",
        "style_id" -> "样式")),
    "click_ead_fraud" -> List(
      "广告" -> List(
        "agent_id" -> "代理商",
        "sponsor_id" -> "广告商",
        "campaign_id" -> "广告系列",
        "group_id" -> "广告组",
        "variation_id" -> "广告变体",
        "keyword_id" -> "关键字",
        "ad_type" -> "广告类型"
      ), "流量" -> List(
        "channel_id" -> "频道",
        "synd_id" -> "联盟",
        "site_id" -> "站点",
        "slot_id" -> "广告位",
        "impr_pos" -> "展示位置",
        "impr_province" -> "省份",
        "impr_city" -> "城市",
        "slot_size" -> "广告位尺寸",
        "ad_size" -> "广告尺寸"
      ), "策略" -> List(
        "match_id" -> "算法",
        "style_id" -> "样式")),
    "click_ead_discarded" -> List(
      "广告" -> List(
        "agent_id" -> "代理商",
        "sponsor_id" -> "广告商",
        "campaign_id" -> "广告系列",
        "group_id" -> "广告组",
        "variation_id" -> "广告变体",
        "keyword_id" -> "关键字",
        "ad_type" -> "广告类型"
      ), "流量" -> List(
        "channel_id" -> "频道",
        "synd_id" -> "联盟",
        "site_id" -> "站点",
        "slot_id" -> "广告位",
        "impr_pos" -> "展示位置",
        "impr_province" -> "省份",
        "impr_city" -> "城市",
        "slot_size" -> "广告位尺寸",
        "ad_size" -> "广告尺寸"
      ), "策略" -> List(
        "match_id" -> "算法",
        "style_id" -> "样式")),
    "univ_ads" -> List("" -> List(
      "ad_size" -> "广告尺寸",
      "ad_type" -> "广告类型",
      "cause" -> "原因",
      "host" -> "服务主机",
      "impr_area" -> "省份",
      "impr_host" -> "impr主机",
      "impr_port" -> "impr端口",
      "port" -> "服务端口",
      "site_id" -> "站点"
    )),
    "impr_sdk_joined_new" -> List("广告" -> List(
      "agent_id" -> "代理商",
      "sponsor_id" -> "广告商",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "广告变体"
    ), "渠道" -> List(
      "app_id" -> "应用",
      "slot_id" -> "广告位",
      "conv_type" -> "转化类型"
    ), "用户" -> List(
      "province" -> "省份",
      "city" -> "城市",
      "synd_id" -> "联盟",
      "os" -> "操作系统",
      "device_name" -> "设备名",
      "carrier_name" -> "运营商名",
      "network_type" -> "网络类型"
    )),
    "sdk_stat_new" -> List("广告" -> List(
      "agent_id" -> "代理商",
      "sponsor_id" -> "广告商",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "广告变体",
      "sid" -> "样式",
      "template_id" -> "模板",
      "variation_id*" -> "广告变体*",
      "match_id" -> "算法",
      "strategy_id" -> "算法策略",
      "bs_host" -> "机器实例",
      "gorgon_host" -> "gorgon实例",
      "cvr_alg_id" -> "转化算法ID",
      "enable_ocpc" -> "oCPC启用",
      "enable_deep_link" -> "deeplink启用",
      "scenario_ids" -> "场景ID",
      "abx_test_marks" -> "ABxTest",
      "min_relevancy" -> "相关性定向阈值",
      "dict_post_id" -> "词典帖子id",
      "click_interact_type" -> "点击互动类型",
      "enable_convert_tracking" -> "enable_convert_tracking",
      "rta_enabled" -> "rta投放启用",
      "gorgon_interface_type" -> "gorgon接口类型",
      "attribution_type" -> "归因方式"
    ), "AbxTest实验位（从左往右数）" -> List(
      "abx_test_marks_0" -> "第0位",
      "abx_test_marks_1" -> "第1位",
      "abx_test_marks_2" -> "第2位",
      "abx_test_marks_3" -> "第3位",
      "abx_test_marks_4" -> "第4位"
    ), "渠道" -> List(
      "developer_id" -> "开发者ID",
      "media_type" -> "媒体类型",
      "app_id" -> "应用",
      "package_name" -> "流量方应用包名",
      "slot_style_type" -> "广告位大类",
      "slot_id" -> "广告位",
      "conv_parent_type" -> "转化父类型",
      "conv_type" -> "转化类型",
      "sub_type" -> "作弊类型",
      "is_secure" -> "isSecure"
    ), "用户" -> List(
      "country" -> "国家",
      "province" -> "省份",
      "city" -> "城市",
      "synd_id" -> "联盟",
      "os" -> "操作系统",
      "brand" -> "手机品牌",
      "carrier_name" -> "运营商名",
      "av" -> "AppVersion",
      "nsv" -> "SdkVersion",
      "osv" -> "OSVersion",
      "network_type" -> "网络类型",
      "crowd_ids" -> "自定义人群id"
    ), "转化" -> List(
      "ct_id" -> "ct_id",
      "ct_type" -> "ct_type",
      "dest_link" -> "dest_link",
      "ct_action" -> "ct_action",
      "retention_days" -> "留存天数",
      "active_purchase_interval_days" -> "激活付费间隔天数",
      "conv_touchpoint_time_difference" -> "转化触点时间差id"
    )),
    "iad_stat" -> List("广告" -> List(
      "agent_id" -> "代理商",
      "sponsor_id" -> "广告商",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "广告变体"
    ), "互动广告" -> List(
      "iad_id" -> "页面id",
      "iad_type" -> "页面类型",
      "iad_face_app_id" -> "入口应用id",
      "iad_face_slot_id" -> "入口广告位id"
    ), "转化" -> List(
      "ct_id" -> "ct_id",
      "ct_type" -> "ct_type",
      "dest_link" -> "dest_link",
      "ct_action" -> "ct_action"
    )),
    "sdk_stat_v2" -> List("广告" -> List(
      "agent_id" -> "代理商",
      "sponsor_id" -> "广告商",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "广告变体",
      "click_interact_type" -> "点击互动类型",
      "rta_enabled" -> "rta投放启用",
      "attribution_type" -> "归因方式"
    ), "渠道" -> List(
      "developer_id" -> "开发者ID",
      "app_id" -> "应用",
      "slot_id" -> "广告位",
      "sid" -> "样式",
      "conv_parent_type" -> "转化父类型",
      "conv_type" -> "转化类型",
      "wechat_origin_id" -> "微信小程序原始id",
      "dict_post_id" -> "词典帖子id"
    ), "用户" -> List(
      "synd_id" -> "联盟"
    ), "转化" -> List(
      "ct_id" -> "ct_id",
      "ct_type" -> "ct_type",
      "dest_link" -> "dest_link",
      "ct_action" -> "ct_action",
      "retention_days" -> "留存天数",
      "active_purchase_interval_days" -> "激活付费间隔天数"
    )),
    "mediation_sdk_report" -> List("" -> List(
      "gaid" -> "设备号",
      "config_id" -> "配置id",
      "dsp_name" -> "dsp名称",
      "dsp_slot" -> "dsp广告位",
      "duration" -> "时长",
      "status_code" -> "sdk返回码",
      "sdkv" -> "sdk版本",
      "sd_rsp_fail_code" -> "DSP返回空数错误码"
    )),
    "third_party_stat" -> List("广告" -> List(
      "sponsor_id" -> "广告商ID",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "广告变体",
      "rta_name" -> "rta接口",
      "discard_reason" -> "点击丢弃原因",
      "rta_enabled" -> "rta投放启用",
      "api_mode" -> "api模式",
      "filter_no_bid_conv" -> "被过滤的兜底转化",
      "fraud_tags" -> "反作弊标签",
      "fraud_rule_id" -> "点击屏蔽规则id",
      "fraud_stat_type" -> "反作弊统计分类",
      "brand" -> "设备品牌",
      "model" -> "设备机型",
      "province" -> "省",
      "city" -> "市"
    ), "渠道" -> List(
      "media_type" -> "媒体类型",
      "app_id" -> "应用",
      "slot_style_type" -> "广告位大类",
      "slot_id" -> "广告位id",
      "tdp_platform" -> "推广平台",
      "activity_id" -> "推广活动ID",
      "channel_id" -> "子渠道",
      "conv_parent_type" -> "转化父类型",
      "conv_type" -> "转化类型",
      "channel_did" -> "渠道DID",
      "os" -> "操作系统"
    ), "转化" -> List(
      "ct_id" -> "ct_id",
      "ct_type" -> "ct_type",
      "dest_link" -> "dest_link",
      "ct_action" -> "ct_action",
      "retention_days" -> "留存天数",
      "conv_click_time_difference" -> "转点时间差id",
      "active_purchase_interval_days" -> "激活付费间隔天数"
    ), "反作弊" -> List(
      "diff_a_click_time" -> "跨aid点击上报时间分布差id",
      "diff_c_click_time" -> "跨渠道did点击上报时间分布差id",
      "diff_s_click_time" -> "跨广告商id点击上报时间分布差id",
      "diff_p_click_time" -> "跨产品id点击上报时间分布差id"
    )),
    "third_party_stat_v2" -> List("广告" -> List(
      "sponsor_id" -> "广告商ID",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "广告变体"
    ), "渠道" -> List(
      "activity_id" -> "推广活动ID",
      "conv_parent_type" -> "转化父类型",
      "conv_type" -> "转化类型",
      "channel_did" -> "渠道DID"
    ), "转化" -> List(
      "ct_id" -> "ct_id",
      "ct_action" -> "ct_action",
      "retention_days" -> "留存天数",
      "active_purchase_interval_days" -> "激活付费间隔天数"
    )),
    "course_stat" -> List("" -> List(
      "source" -> "投放平台",
      "sponsor_id" -> "广告商ID",
      "campaign_id" -> "广告系列ID",
      "campaign_name" -> "广告系列名",
      "creative_id" -> "广告创意ID",
      "group_id" -> "广告组ID",
      "group_name" -> "广告组名",
      "app_id" -> "应用ID"
    )),
    "eadv_mobile_log" -> List("" -> List(
      "category" -> "category",
      "infoid" -> "infoid",
      "style" -> "style",
      "type" -> "type",
      "keyfrom" -> "keyfrom",
      "abtest" -> "abtest",
      "province" -> "province",
      "city" -> "city"
    )),
    "ssp_log" -> List("" -> List(
      "ad_type" -> "广告类型",
      "channel_id" -> "频道",
      "impr_city" -> "城市",
      "impr_province" -> "省份",
      "site_id" -> "站点",
      "slot_id" -> "广告位",
      "slot_size" -> "广告位尺寸",
      "ad_size" -> "广告尺寸",
      "style_id" -> "样式",
      "synd_id" -> "联盟",
      "more_info.ssptype" -> "ssptype",
      "more_info.sspid" -> "sspid"
    )),
    "xuetang_course" -> List("" -> List(
      "clientid" -> "clientid", "msgtype" -> "msgtype", "parameters.userid" -> "parameters.userid", "parameters.useragent" -> "parameters.useragent",
      "parameters.referer" -> "parameters.referer", "parameters.ip" -> "parameters.ip", "parameters.requrl" -> "parameters.requrl",
      "parameters.groupid" -> "parameters.groupid", "parameters.version" -> "parameters.version", "parameters.content" -> "parameters.content",
      "parameters.courseid" -> "parameters.courseid", "parameters.lesson" -> "parameters.lesson", "parameters.lessonid" -> "parameters.lessonid",
      "parameters.sublesson" -> "parameters.sublesson", "parameters.questionid" -> "parameters.questionid",
      "parameters.answer" -> "parameters.answer", "parameters.targetid" -> "parameters.targetid",
      "parameters.question" -> "parameters.question", "parameters.log.action" -> "parameters.log.action",
      "parameters.log.client" -> "parameters.log.client", "parameters.log.crashlog" -> "parameters.log.crashlog",
      "parameters.log.loadtime" -> "parameters.log.loadtime", "parameters.log" -> "parameters.log",
      "parameters.log.pageloadtime" -> "parameters.log.pageloadtime", "parameters.log.playerloadtime" -> "parameters.log.playerloadtime",
      "parameters.log.logtime" -> "parameters.log.logtime"
    )),
    "infoline_article" -> List("" -> List(
      "show" -> "show", "action" -> "action", "keyfrom" -> "keyfrom", "infoid" -> "infoid", "abtest" -> "abtest", "style" -> "style", "os" -> "os", "vendor" -> "vendor"
    )),
    "temp" -> List("" -> List("d0" -> "d0", "d1" -> "d1")),
    "dsp_stat_v2" -> List("" -> List(
      "ad_size" -> "ad_size",
      "agent_id" -> "agent_id",
      "synd_id" -> "synd_id",
      "global_pid" -> "global_pid",
      "campaign_id" -> "campaign_id",
      "match_id" -> "match_id",
      "sponsor_id" -> "sponsor_id",
      "site_id" -> "site_id",
      "variation_id" -> "variation_id",
      "city" -> "city",
      "province" -> "province",
      "domain" -> "domain",
      "group_id" -> "group_id"
    )),
    "dsp_stat_new" -> List("" -> List(
      "ad_size" -> "ad_size",
      "agent_id" -> "agent_id",
      "synd_id" -> "synd_id",
      "global_pid" -> "global_pid",
      "campaign_id" -> "campaign_id",
      "match_id" -> "match_id",
      "sponsor_id" -> "sponsor_id",
      "site_id" -> "site_id",
      "variation_id" -> "variation_id",
      "city" -> "city",
      "province" -> "province",
      "domain" -> "domain",
      "group_id" -> "group_id",
      "enable_deep_link" -> "deeplink启用",
      "scenario_ids" -> "场景ID",
      "min_relevancy" -> "相关性定向阈值"
    )),
    "dsp_stat" -> List("" -> List(
      "ad_size" -> "ad_size",
      "agent_id" -> "agent_id",
      "synd_id" -> "synd_id",
      "campaign_id" -> "campaign_id",
      "match_id" -> "match_id",
      "site_id" -> "site_id",
      "sponsor_id" -> "sponsor_id",
      "variation_id" -> "variation_id",
      "city" -> "city",
      "province" -> "province",
      "domain" -> "domain",
      "group_id" -> "group_id"
    )),
    "readease_stat" -> List("" -> List(
      "ctId" -> "contentId",
      "ctType" -> "contentType",
      "sour" -> "sour",
      "level" -> "level",
      "level2" -> "level2",
      "secDuration" -> "secDuration",
      "category" -> "category",
      "grade" -> "grade",
      "type" -> "type",
      "nom" -> "提名器",
      "sc" -> "文章打分",
      "pos" -> "pos",
      "province" -> "省份",
      "city" -> "城市",
      "dayOfWeek" -> "星期",
      "hourOfWeek" -> "小时",
      "OSType" -> "OSType",
      "actionType" -> "actionType",
      "vendor" -> "vendor",
      "nId" -> "nId",
      "subtype" -> "subtype",
      "resp" -> "resp",
      "srcHost" -> "srcHost"
    )),
    "readease_stat_join" -> List("" -> List(
      "ctId" -> "contentId",
      "aType" -> "aType",
      "aLevel" -> "aLevel",
      "uLevel" -> "uLevel",
      "nom" -> "提名器",
      "sour" -> "sour",
      "pos" -> "pos",
      "aGrade" -> "aGrade",
      "host" -> "host",
      "hour" -> "小时",
      "sdk_dn" -> "sdk_dn",
      "sdk_cn" -> "sdk_cn",
      "country" -> "country",
      "userPartition" -> "userPartition",
      "ctType" -> "contentType",
      "click" -> "isclick",
      "impr" -> "isimpr",
      "dura" -> "dura"
    )),
    "sdk_analyzer" -> List("" -> List(
      "sponsor_id" -> "sponsorId",
      "os" -> "os",
      "variation_id" -> "variationId",
      "slot_id" -> "slotId",
      "sdk_version" -> "sdkVersion"
    )),
    "luna_article_stat" -> List("" -> List(
      "os" -> "os",
      "useragent" -> "browser",
      "cardId" -> "cardId",
      "cardTitle" -> "cardTitle",
      "editor" -> "cardEditor",
      "articleUrl" -> "pageURL",
      "articleTitle" -> "pageTitle",
      "style" -> "style",
      "abtest" -> "abtest",
      "model" -> "model",
      "mid" -> "mid",
      "vendor" -> "vendor",
      "keyfrom" -> "keyfrom"
    )),
    "brand_stat" -> List("" -> List(
      "sponsorId" -> "广告主",
      "campaignId" -> "广告订单",
      "adVariationId" -> "广告",
      "imprPos" -> "广告位",
      "adType" -> "广告类型"
    )),
    "brand_stat_v2" -> List("" -> List(
      "sponsorId" -> "客户",
      "campaignId" -> "推广计划",
      "adGroupId" -> "推广组",
      "adVariationId" -> "推广创意",
      "brandClkType" -> "推广目标",
      "billingType" -> "计费方式",
      "deliveryType" -> "投放方式",
      "mediaId" -> "媒体",
      "imprPos" -> "广告位",
      "styleId" -> "样式",
      "keyFrom" -> "版本号",
      "hostname" -> "gorgon实例",
      "venderId" -> "venderId",
      "vender_source" -> "vender_source",
      "click_interact_type" -> "点击互动类型",
      "mappingPosId" -> "流量映射后广告位id",
      "interface_type" -> "流量来源接口名",
      "dark_ip" -> "IP黑名单",
      "dark_device" -> "设备黑名单"
    )),
    "bid_failed_stat" -> List("" -> List(
      "slot_id" -> "广告位",
      "app_id" -> "应用",
      "package_name" -> "流量方应用包名",
      "sponsor_id" -> "广告商",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "广告变体",
      "filter_reason" -> "过滤原因",
      "filter_sub_reason" -> "过滤子原因",
      "rule_filter_rule_id" -> "过滤RuleId",
      "bs_host" -> "bs实例"
    )),
    "failed_send_click_stat" -> List("" -> List(
      "url_domain" -> "URL域名",
      "sponsor_id" -> "广告商",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "广告变体",
      "http_status_code" -> "http状态码",
      "activity_id" -> "活动ID"
    )),
    "yex_invalid_traffic_stat" -> List("" -> List(
        "dc_id" -> "应用ID",
        "slot_id" -> "广告位id",
        "dsp_id" -> "dsp id",
        "ivt_tags" -> "异常流量标签",
        "ivt_rule_id" -> "过滤规则id",
        "connection_type" -> "网络类型",
        "os" -> "操作系统",
        "os_version" -> "操作系统版本",
        "model" -> "机型",
        "manufacturer" -> "制造商",
        "carrier" -> "运营商"
      )),
    "sdk_report_stat" -> List("" -> List(
        "slot_id" -> "广告位",
        "variation_id" -> "广告创意",
        "group_id" -> "广告组",
        "campaign_id" -> "广告系列",
        "sponsor_id" -> "广告主",
        "dsp_id" -> "Dsp",
        "synd_id" -> "联盟",
        "agent_id" -> "代理商",
        "app_id" -> "应用",
        "sid" -> "样式",
        "download_success" -> "APP下载结果",
        "download_url" -> "APP下载链接",
        "enable_deep_link" -> "是否启用应用直达",
        "deep_link_url" -> "应用直达链接",
        "invoke_success" -> "DeepLink调起结果",
        "province" -> "省份",
        "city" -> "城市",
        "os" -> "操作系统",
        "network_type" -> "网络类型",
        "carrier_name" -> "运营商",
        "av" -> "AppVersion",
        "nsv" -> "SdkVersion",
        "osv" -> "OsVersion",
        "device_name" -> "设备名"
    )),
    "tiny_url_visit" -> List("" -> List(
        "tiny_url" -> "短链ID",
        "mng_id" -> "运营账号ID",
        "kol_id" -> "KOL ID",
        "task_id" -> "任务ID",
        "dest_url" -> "目标链接"
    )),
    "wow_outlog" -> List("" -> List(
      "postid" -> "postid",
      "show" -> "show",
      "action" -> "action",
      "shape" -> "shape",
      "client" -> "client",
      "abtest" -> "abtest",
      "rank_ab_test1" -> "rank_ab_test1",
      "rank_ab_test2" -> "rank_ab_test2",
      "other" -> "other",
      "score" -> "score",
      "datafrom" -> "词典datafrom",
      "post_duration" -> "视频时长",
      "post_duration_bucket" -> "视频时长分桶id",
      "is_duration_efficient" -> "is_duration_efficient",
      "is_related" -> "is_related"
    )),
    "sales_activity_event_log" -> List("" -> List(
      "campaign_id" -> "活动ID",
      "page_domain_id" -> "页面域",
      "event_id" -> "事件ID",
      "stage_id" -> "打卡阶段ID",
      "profit_id" -> "权益奖励ID",
      "province_id" -> "省份ID",
      "city_id" -> "城市ID"
    )),
    "brand_abnor_url_stat" -> List("" -> List(
      "slot_id" -> "广告位ID",
      "adv_id" -> "创意id",
      "province" -> "省份",
      "city" -> "城市",
      "app_version" -> "应用版本号",
      "sdk_version" -> "sdk版本号",
      "os_version" -> "操作系统版本",
      "model" -> "设备机型",
      "abnormal_type" -> "错误类型"
    )),
    "sdk_tdp_together" -> List("广告" -> List(
      "os" -> "操作系统",
      "app_id" -> "媒体",
      "slot_id" -> "广告位",
      "sponsor_id" -> "广告商",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "创意"
    ),"渠道" -> List(
      "activity_id" -> "推广活动id",
      "channel_id" -> "渠道id",
      "from_ydbc" -> "是否转化加速器策略",
      "channel_id_prefix" -> "渠道id前缀",
      "identify" -> "流量二级id",
      "media_pkg_id" -> "媒体包id",
      "reyun_camp_id" -> "人群包id",
      "alg_bucket_id" -> "算法分桶id",
      "retriever" -> "召回模型",
      "retrieve_alg_id" -> "召回算法id",
      "product_id" -> "推广应用id",
      "channel_did" -> "渠道did"
    )),
    "ead_dmp_stat" -> List("人口属性" -> List(
      "birth_y" -> "出生年份",
      "gender" -> "性别",
      "married" -> "是否已婚",
      "has_chd" -> "是否已育",
      "chd_birth_y" -> "小孩年龄"
    ),"地域属性" -> List(
      "city_grade" -> "常驻市等级",
      "province" -> "常驻省",
      "reside" -> "常驻市",
      "reside_estate_price" -> "小区价格"
    ),"设备属性" -> List(
      "level1_car_brand_180d" -> "汽车品牌",
      "os" -> "操作系统",
      "phone_brand_list" -> "手机品牌",
      "phone_price_list" -> "手机价格"
    ),"社会属性" -> List(
      "edu_degree" -> "文化程度",
      "school_type" -> "大学类型",
      "profession" -> "职业"
    ),"资产属性" -> List(
      "has_car" -> "是否有车",
      "has_house" -> "是否有房",
      "income" -> "收入水平",
      "pay_monetary_all" -> "付费水平"
    ),"行为标签" -> List(
      "industry_conv" -> "行业转化"
    ),"行业标签" -> List(
      "interest_all" -> "兴趣类目偏好",
      "pay_will" -> "产品付费偏好"
    )),
    "portrait_by_ead_dmp" -> List("公共维度" -> List(
      "os" -> "操作系统",
      "type" -> "类型id",
      "sponsor_id" -> "广告商",
      "campaign_id" -> "广告系列",
      "group_id" -> "广告组",
      "variation_id" -> "创意"
    ),"智选" -> List(
      "app_id" -> "媒体",
      "slot_id" -> "广告位",
      "crowd_ids" -> "人群包id"
    ),"第三方" -> List(
      "activity_id" -> "推广活动id",
      "cpa_source" -> "来源id",
      "media_pkg_id" -> "媒体包id",
      "dmp_pkg_id" -> "数据包id"
    ),"人口属性" -> List(
      "birth_y" -> "出生年份",
      "gender" -> "性别",
      "married" -> "是否已婚",
      "has_chd" -> "是否已育",
      "chd_birth_y" -> "小孩年龄"
    ),"地域属性" -> List(
      "city_grade" -> "常驻市等级",
      "province" -> "常驻省",
      "reside" -> "常驻市",
      "reside_estate_price" -> "小区价格"
    ),"设备属性" -> List(
      "level1_car_brand_180d" -> "汽车品牌",
      "phone_brand_list" -> "手机品牌",
      "phone_price_list" -> "手机价格"
    ),"社会属性" -> List(
      "edu_degree" -> "文化程度",
      "school_type" -> "大学类型",
      "profession" -> "职业"
    ),"资产属性" -> List(
      "has_car" -> "是否有车",
      "has_house" -> "是否有房",
      "income" -> "收入水平",
      "pay_monetary_all" -> "付费水平"
    ),"行业标签" -> List(
      "interest_all" -> "兴趣类目偏好",
      "pay_will" -> "产品付费偏好"
    ))
  )
  val granularities = List("minute", "hour", "day", "week", "month", "quarter", "all")

  val dateFormatter = DateTimeFormat.forPattern("yyyy-MM-dd")

  def main = Action {
    implicit request => {
      val user = request.session.get(USER_NAME).getOrElse("")
      if ("".equals(user)) {
        Ok(views.html.login(User.loginForm, "请先登录!!"))
      } else {
        Ok(views.html.main(user)("Welcome to Quipu Camayos")(User.getUserTable(user)))
      }
    }
  }

  def simpleQuery(ds: String) = Action {
    implicit request => {
      request.session.get(USER_NAME).map {
        user =>
          val userTable = User.getUserTable(user)
          val dataSource = if (ds == "ead_stat") "ead_stat_new" else ds
          if (!validAuth(user, dataSource)) {
            Ok(views.html.main(user)(dataSource + "表信息权限受限，请联系管理员!!")(userTable))
          } else {
            val dimension = dimensions(dataSource)
            val metric = metrics(dataSource)

            val today = org.joda.time.DateTime.now()
            val tomorrow = today.plusDays(1)

            val defaultQuery = new SimpleQuery(NEW_BROKER, dataSource, Interval(dateFormatter.print(today),
              dateFormatter.print(tomorrow)), "day", List(SelectFilter("", "")), List(), List(SelectFilter("", "")), List(), 1000, List(OrderBy("",
              false)), true, "", 300)
            Ok(views.html.simpleQuery(form.fill(defaultQuery))(dataSource)(dimension)(metric)(List())(granularities)(None)(user)("")(userTable))
          }
      }.getOrElse {
        Ok(views.html.login(User.loginForm, "请先登录!!"))
      }
    }
  }

  def insert(input: List[(String, String)], name: String, insert: List[(String, String)]): List[(String, String)] = {
    val i = input.indexWhere(_._1 == name)
    if (i >= 0) {
      val p = input.splitAt(i + 1)
      p._1 ::: insert ::: p._2
    } else {
      input
    }
  }

  def replace(input: List[(String, String)], name: String, insert: (String, String)): List[(String, String)] = {
    val i = input.indexWhere(_._1 == name)
    if (i >= 0) {
      val newList = ListBuffer(input: _*)
      newList(i) = insert
      newList.toList
    } else {
      input
    }
  }

  def insert(input: List[(String, String)], name1: String, name2: String, insert: List[(String, String)]): List[(String, String)] = {
    val i = input.indexWhere(_._1 == name1)
    val j = input.indexWhere(_._1 == name2)
    if (i >= 0 && j >= 0) {
      val index = Math.max(i, j)
      val p = input.splitAt(index + 1)
      p._1 ::: insert ::: p._2
    } else {
      input
    }
  }

  def insert(input: List[(String, String)], name1: String, name2: String, name3: String, insert: List[(String, String)]): List[(String, String)] = {
    val i = input.indexWhere(_._1 == name1)
    val j = input.indexWhere(_._1 == name2)
    val k = input.indexWhere(_._1 == name3)
    if (i >= 0 && j >= 0 && k >= 0) {
      val index = Math.max(Math.max(i, j), k)
      val p = input.splitAt(index + 1)
      p._1 ::: insert ::: p._2
    } else {
      input
    }
  }

  /**
    * 为原始列表增加新的内容。
    * <p>增加规则：
    * <ul>
    *   <li>原始列表中必须有required列表中所有的内容，新插入的内容必须在required中所有列的内容之后；</li>
    *   <li>原始列表中可以有optional列表中的内容，如果有，新插入的内容在optional中所有列的内容之后；</li>
    * </ul>
    *
    * 总体而言起到两个作用：根据required列决定是否插入新的列，根据optional列进一步控制新插入列的位置。
    *
    * @param input    原始列表
    * @param required 必备列表：只有原始列表拥有此列表对应的所有列，才能插入insert列
    * @param optional 可选列表：如果原始列表拥有此列表对应的任意列，insert必须插入到这些列的后面
    * @param insert   待插入的列
    * @return 含有新插入列的列表或原始列表
    */
  def insert(input: List[(String, String)], required: List[String], optional: List[String], insert: List[(String, String)]): List[(String, String)] = {
    var max = 0
    required.foreach(name => {
      val index = input.indexWhere(_._1 == name)
      if (index < 0) {
        return input
      } else if (index > max) {
        max = index
      }
    })
    optional.foreach(name => {
      val index = input.indexWhere(_._1 == name)
      if (index > max) {
        max = index
      }
    })
    val p = input.splitAt(max + 1)
    p._1 ::: insert ::: p._2
  }

  /**
    * 截取list在element前的部分，不含element。如果element不存在于list，会返回空列表。
    *
    * @param list    原始列表
    * @param element 分界元素
    * @return 分界元素前的列表
    */
  def truncateBefore(list: List[String], element: String): List[String] = {
    val p = list.splitAt(list.indexWhere(_ == element))
    p._1
  }

  def enrichTableHeader(query: SimpleQuery, tuples: List[(String, String)]): List[(String, String)] = {
    if("course_stat".equals(query.dataSource)) {
      return tuples
    }

    var result: List[(String, String)] = insert(tuples, "sponsor_id", ("sponsor_userName" -> "广告商邮箱") :: ("sponsor_name" -> "广告商名称") :: Nil)
    result = insert(result, "site_id", ("site_name" -> "站点名") :: Nil)
    result = insert(result, "ext.app_id", ("app_name" -> "应用名") :: Nil)
    result = insert(result, "dc_id", ("app_name" -> "应用名") :: Nil)
    if (!"sales_activity_event_log".equals(query.dataSource)) {
      result = insert(result, "campaign_id", ("campaign_name" -> "广告系列名称") :: Nil)
    }
    result = insert(result, "group_id", ("group_name" -> "广告组名称") :: Nil)
    result = insert(result, "variation_id", ("variation_title" -> "广告变体标题") :: Nil)
    result = insert(result, "slot_id", ("slot_name" -> "广告位名") :: Nil)

    result = insert(result, "variation_id", ("sdk_data" -> "sdk_data") :: Nil)
    result = replace(result, "variation_id*", ("variation_id" -> "广告变体"))
    result = insert(result, "app_id", ("app_name" -> "应用名") :: Nil)
    result = insert(result, "sid", ("schema_name" -> "样式名") :: Nil)
    result = replace(result, "charge", "charge" -> "消费:元")
    result = replace(result, "bid_predict_cost", "bid_predict_cost" -> "竞价预估消费:元")
    result = replace(result, "bid_time_charge", "bid_time_charge" -> "竞价实际消费:元")
    result = replace(result, "fraud_charge", "fraud_charge" -> "作弊消费:元")
    result = replace(result, "discarded_charge", "discarded_charge" -> "过耗消费:元")
    result = insert(result, "click", "impr", ("actual_ctr" -> "点击率") :: Nil)
    result = insert(result, "conv", "click", ("ccr" -> "当日转化率") :: Nil)
    result = insert(result, "impr_conv", "impr", ("icr" -> "曝光转化率") :: Nil)
    result = insert(result, "opt_conv", "click", ("opt_ccr" -> "oCPC优化目标当日转化率") :: Nil)
    result = insert(result, "loc_conv", "click", ("loc_ccr" -> "点击转化率") :: Nil)
    result = insert(result, "loc_opt_conv", "click", ("loc_opt_ccr" -> "oCPC优化目标点击转化率") :: Nil)
    result = insert(result, "conv", "charge", ("cpa" -> "当日转化成本:元") :: Nil)
    result = insert(result, "opt_conv", "charge", ("opt_cpa" -> "oCPC优化目标当日转化成本:元") :: Nil)
    result = insert(result, "loc_conv", "charge", ("loc_cpa" -> "点击转化成本:元") :: Nil)
    result = insert(result, "loc_opt_conv", "charge", ("loc_opt_cpa" -> "oCPC优化目标点击转化成本:元") :: Nil)
    result = insert(result, "comment", "click", ("cpp" -> "cpp") :: Nil)
    result = insert(result, "comment_uv", "click_uv", ("cpp" -> "cpp") :: Nil)
    result = insert(result, "share_uv", "click_uv", ("spp" -> "spp") :: Nil)
    result = insert(result, "like_uv", "click_uv", ("lpp" -> "lpp") :: Nil)
    result = insert(result, "dislike_uv", "click_uv", ("dpp" -> "dpp") :: Nil)
    result = insert(result, "favorites_uv", "click_uv", ("fpp" -> "fpp") :: Nil)
    result = insert(result, "pv_uv", "click_uv", ("cpc" -> "cpc") :: Nil)
    result = insert(result, "playDuration", "playTimes", ("playAvg" -> "playAvg") :: Nil)
    result = insert(result, "favorites", "click", ("fpp" -> "fpp") :: Nil)
    result = insert(result, "share", "click", ("spp" -> "spp") :: Nil)
    result = insert(result, "like", "click", ("lpp" -> "lpp") :: Nil)
    result = insert(result, "dislike", "click", ("dpp" -> "dpp") :: Nil)
    result = insert(result, "impr", "bid", ("ir" -> "展示率") :: Nil)
    result = insert(result, "impr", "cpm", ("ecpm" -> "ecpm:元") :: Nil)
    result = insert(result, "impr", "charge", ("ecpm" -> "ecpm:元") :: Nil)
    result = insert(result, "impr_count", "impr_price", ("ecpm" -> "ecpm:元") :: Nil)
    result = insert(result, "bid", "req_ad_num", ("br" -> "竞价率") :: Nil)
    result = insert(result, "bid", "request_ad_num", ("br" -> "竞价率") :: Nil)
    result = insert(result, "charge", "click", ("cpc" -> "单价:元") :: Nil)
    result = insert(result, "pt_sum", "count", ("pt_avg" -> "pt_avg") :: Nil)
    result = insert(result, "lt_sum", "count", ("lt_avg" -> "lt_avg") :: Nil)
    result = insert(result, "fd_pt_sum", "fd_count", ("fd_pt_avg" -> "fd_pt_avg") :: Nil)
    result = insert(result, "fd_lt_sum", "fd_count", ("fd_lt_avg" -> "fd_lt_avg") :: Nil)
    result = insert(result, "fd_ht_sum", "fd_count", ("fd_ht_avg" -> "fd_ht_avg") :: Nil)
    result = insert(result, "impr_count", "click_count", ("actual_ctr" -> "点击率") :: Nil)
    result = insert(result, "bid_response_count_no_bid", "bid_count", ("bid_rate" -> "参竞率") :: Nil)
    result = insert(result, "impr_price", "click_count", ("unit_price" -> "点击单价") :: Nil)
    result = insert(result, "bid_price_of_win", "bid_count_win", ("bid_cpm_of_win" -> "bid_cpm_of_win:元") :: Nil)
    result = insert(result, "win_price", "bid_count_win", ("win_cpm" -> "win_cpm:元") :: Nil)
    result = replace(result, "ctr", "avg_ctr" -> "参竞预估点击率")
    result = replace(result, "pr_cvr", "avg_pr_cvr" -> "参竞预估转化率")
    result = insert(result, "bid_count_win", "adx_bid_price", ("adx_bid_ecpm" -> "adx出价ecpm:元") :: Nil)
    result = insert(result, "bid_count_win", "adx_bid_count_win", ("adx_bid_win_rate" -> "adx竞胜率") :: Nil)
    result = insert(result, "adx_bid_count_win", "adx_win_price", ("adx_win_ecpm" -> "adx竞胜ecpm:元") :: Nil)
    result = insert(result, "adx_impr_price", ("adx_consume" -> "adx消费:元") :: Nil)
    result = insert(result, "impr_count", "adx_impr_price", ("adx_impr_ecpm" -> "adx展示ecpm:元") :: Nil)
    result = insert(result, "rta_request_count", "rta_bid_count", ("rta_br" -> "rta参竞率") :: Nil)
    result = insert(result, "rta_request_count", "rta_exception_count", ("rta_er" -> "rta异常请求比例") :: Nil)
    result = insert(result, "click", "rta_bid_click", ("rta_click_br" -> "rta参竞点击比例") :: Nil)
    result = insert(result, "conv", "rta_bid_conv", ("rta_conv_br" -> "rta参竞转化比例") :: Nil)

    if ("third_party_stat".equals(query.dataSource)) {
      result = insert(result, "click", "repeat_click", ("repeat_ctr" -> "重复点击率") :: Nil)
      result = insert(result, "click", "channel_did_filled", ("channel_did_filled_rate" -> "渠道DID携带率") :: Nil)
      result = insert(result, "click", "ua_filled", ("ua_filled_rate" -> "ua携带率") :: Nil)
      result = insert(result, "click", "ip_filled", ("ip_filled_rate" -> "ip携带率") :: Nil)
      result = insert(result, "click", "ts_filled", ("ts_filled_rate" -> "点击时间携带率") :: Nil)
      result = insert(result, "click", "req_id_filled", ("req_id_filled_rate" -> "rta请求id携带率") :: Nil)
      result = insert(result, "click", "activity_id_filled" , ("activity_id_filled_rate" -> "推广活动id携带率") :: Nil)
      result = insert(result, "android_click", "android_os_filled", ("android_os_filled_rate"  -> "android操作系统携带率") :: Nil)
      result = insert(result, "ios_click", "ios_os_filled", ("ios_os_filled_rate" -> "ios操作系统携带率") :: Nil)
      result = insert(result, "click", "device_model_filled", ("device_model_filled_rate" -> "手机机型携带率") :: Nil)
      result = insert(result, "click", "imei_filled", ("imei_filled_rate" -> "imei携带率") :: Nil)
      result = insert(result, "click", "oaid_filled", ("oaid_filled_rate" -> "oaid携带率") :: Nil)
      result = insert(result, "click", "oaid_md5_filled", ("oaid_md5_filled_rate" -> "oaidMd5携带率") :: Nil)
      result = insert(result, "click", "idfa_filled", ("idfa_filled_rate" -> "idfa携带率") :: Nil)
      result = insert(result, "click", "idfa_md5_filled", ("idfa_md5_filled_rate" -> "idfaMd5携带率") :: Nil)
      result = insert(result, "click", "caid_filled", ("caid_filled_rate" -> "caid携带率") :: Nil)
      result = insert(result, "click", "caid_md5_filled", ("caid_md5_filled_rate" -> "caidMd5携带率") :: Nil)
      result = insert(result, "click", "alid_filled", ("alid_filled_rate" -> "aaid携带率") :: Nil)
      result = insert(result, "click", "android_id_filled", ("android_id_filled_rate" -> "androidId携带率") :: Nil)
      result = insert(result, "click", "unexpected_ua_click", ("unexpected_ua_rate" -> "ua与推广类型不一致点击比例") :: Nil)
      result = insert(result, "click", "unexpected_device_click", ("unexpected_device_rate" -> "设备与推广类型不一致点击比例") :: Nil)
      result = insert(result, "click", "unexpected_os_click", ("unexpected_os_rate" -> "操作系统与推广类型不一致点击比例") :: Nil)
      result = insert(result, "channel_did", ("channel_code" -> "渠道代号") :: Nil)
      result = insert(result, "conv_click_time_difference", ("conv_click_time_difference_desc" -> "转点时间差") :: Nil)
      result = insert(result, "diff_a_click_time", ("diff_a_click_time_desc" -> "跨aid点击上报时间分布差id") :: Nil)
      result = insert(result, "diff_c_click_time", ("diff_c_click_time_desc" -> "跨渠道did点击上报时间分布差id") :: Nil)
      result = insert(result, "diff_s_click_time", ("diff_s_click_time_desc" -> "跨广告商id点击上报时间分布差id") :: Nil)
      result = insert(result, "diff_p_click_time", ("diff_p_click_time_desc" -> "跨产品id点击上报时间分布差") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", ("fraud_click_filtered_rate" -> "异常点击总过滤率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "value_error_dark_device_id_mark", ("value_error_dark_device_id_mark_rate" -> "点击设备号黑名单识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "value_error_dark_ip_mark", ("value_error_dark_ip_mark_rate" -> "点击IP黑名单识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "value_error_intranet_ip_mark", ("value_error_intranet_ip_mark_rate" -> "点击内网IP识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "action_rate_click_mark", ("action_rate_click_mark_rate" -> "设备号日点击量级过大识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "repeat_click_mark", ("repeat_click_mark_rate" -> "重复点击识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "unexpected_os_mark", ("unexpected_os_mark_rate" -> "点击操作系统与应用推广类型不一致识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "unexpected_device_mark", ("unexpected_device_mark_rate" -> "点击设备号与应用推广类型不一致识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "unexpected_ua_mark", ("unexpected_ua_mark_rate" -> "点击UA与应用推广类型不一致识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "unexpected_model_mark", ("unexpected_model_mark_rate" -> "点击手机机型与应用推广类型不一致识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "related_multi_ua_mark", ("related_multi_ua_mark_rate" -> "点击设备号关联多个UA识别率") :: Nil)
      result = insert(result, "click", "fraud_click_filtered", "action_rate_device_ip_mark", ("action_rate_device_ip_mark_rate" -> "点击IP高频变化识别率") :: Nil)
      result = insert(result, "conv", "conv_time_follow_closely", ("conv_time_follow_closely_rate" -> "转化劫持率") :: Nil)
    }

    if ("third_party_stat_v2".equals(query.dataSource)) {
      result = insert(result, "channel_did", ("channel_code" -> "渠道代号") :: Nil)
    }

    if ("sdk_stat_new".equals(query.dataSource)) {
      result = insert(result, "bid", "bid_price", ("bid_ecpm" -> "adx出价ecpm:元") :: Nil)
      result = insert(result, "bid", "win_count", ("win_rate" -> "adx竞胜率") :: Nil)
      result = insert(result, "win_count", "adx_win_price", ("adx_win_ecpm" -> "adx竞胜ecpm:元") :: Nil)
      result = insert(result, "win_price", ("adx_consume" -> "adx消费:元") :: Nil)
      result = insert(result, "win_price_dedup", ("adx_consume_dedup" -> "adx消费(展示去重):元") :: Nil)
      result = insert(result, "conv", "win_price", ("adx_cpa" -> "adx当日转化成本:元") :: Nil)
      result = insert(result, "loc_conv", "win_price", ("adx_loc_cpa" -> "adx点击转化成本:元") :: Nil)
      result = insert(result, "charge", "win_price", ("adx_consume_roi" -> "adx消费ROI") :: Nil)
      result = insert(result, "charge", "win_price_dedup", ("adx_consume_roi_dedup" -> "adx消费ROI(展示去重)") :: Nil)
      result = insert(result, "impr", "win_price", ("impr_ecpm" -> "adx展示ecpm:元") :: Nil)
      result = insert(result, "impr_dedup", "win_price_dedup", ("impr_ecpm_dedup" -> "adx展示ecpm(展示去重):元") :: Nil)
      result = replace(result, "ctr_of_impr", "avg_ctr_of_impr" -> "展示预估点击率")
      result = replace(result, "pr_cvr_of_impr", "avg_pr_cvr_of_impr" -> "展示预估转化率")
      result = insert(result, "win_count", "impr", ("adx_impr_rate" -> "adx竞胜展示率") :: Nil)
      result = insert(result, "conv_touchpoint_time_difference", ("conv_touchpoint_time_difference_desc" -> "转化触点时间差") :: Nil)
      result = insert(result, "conv_activate", "charge", ("activate_cost" -> "激活成本:元") :: Nil)
      result = insert(result, "conv_activate", "win_price", ("adx_activate_cost" -> "adx激活成本:元") :: Nil)
      result = insert(result, "conv_activate", "win_price_dedup", ("adx_activate_cost_dedup" -> "adx激活成本(展示去重):元") :: Nil)

    }

    if("brand_stat_v2".equals(query.dataSource)){
      result = insert(result, "sponsorId", ("sponsorName" -> "客户名称") :: Nil)
      result = insert(result, "campaignId", ("campaignName" -> "推广计划名称") :: Nil)
      result = insert(result, "imprPos", ("imprPosName" -> "广告位名称") :: Nil)
      result = insert(result, "adVariationId", ("adContent" -> "广告内容") :: Nil)
      result = insert(result, "mediaId", ("mediaName" -> "媒体名称") :: Nil)
      result = insert(result, "styleId", ("styleName" -> "样式名称") :: Nil)
      result = insert(result, "adGroupId", ("adGroupName" -> "推广组名称") :: Nil)
      result = insert(result, "bid", "pv", ("br" -> "竞价率") :: Nil)
    }else {
      result = insert(result, "sponsorId", ("sponsorName" -> "客户名称") :: Nil)
      result = insert(result, "campaignId", ("campaignName" -> "订单名称") :: Nil)
      result = insert(result, "imprPos", ("imprPosName" -> "广告位名称") :: Nil)
      result = insert(result, "adVariationId", ("adContent" -> "广告内容") :: Nil)
    }
    result = insert(result, "impr_count", "bid_count_win", ("yex_impr_ratio" -> "展示率") :: Nil)
    result = insert(result, "bid_count_win", "bid_count", ("yex_bid_success_ratio" -> "竞价成功率") :: Nil)
    result = insert(result, "bid_count", "received_ad_request_count", ("yex_bid_ratio" -> "竞价率") :: Nil)
    result = insert(result, "dsp_id", ("dsp_name" -> "DSP名称") :: Nil)
    // 互动广告
    if ("iad_stat".equals(query.dataSource)) {
      // 互动广告metrics列出现的顺序
      var iadSequence = List(
        "impr", "iad_impr", "click", "iad_click", "entry_ctr", "charge", "cpc", "iad_charge", "entry_cpc", "entry_cpm",
        "iad_page", "lp_arrival_rate",
        "iad_user_click", "pv", "2nd_jump_ctr",
        "game_in_rate",
        "3rd_jump_impr", "3rd_jump_click", "3rd_jump_ctr", "conv", "3rd_jump_conv_rate", "3rd_jump_charge", "3rd_jump_cpc", "3rd_jump_cpm", "3rd_jump_cpa",
        "backup_impr_rate", "backup_click_rate"
      )
      result = replace(result, "iad_charge", "iad_charge" -> "入口消费:元")
      result = insert(result, "iad_face_slot_id", ("slot_name" -> "广告位名") :: Nil)
      result = insert(result, "iad_face_app_id", ("app_name" -> "应用名") :: Nil)
      result = insert(result, List("iad_impr", "iad_click"), truncateBefore(iadSequence, "entry_ctr"), ("entry_ctr" -> "入口点击率") :: Nil)
      result = insert(result, List("iad_click", "iad_charge"), truncateBefore(iadSequence, "entry_cpc"), ("entry_cpc" -> "入口CPC:元") :: Nil)
      result = insert(result, List("iad_impr", "iad_charge"), truncateBefore(iadSequence, "entry_cpm"), ("entry_cpm" -> "入口CPM:元") :: Nil)
      result = insert(result, List("iad_page", "iad_click"), truncateBefore(iadSequence, "lp_arrival_rate"), ("lp_arrival_rate" -> "落地页到达率") :: Nil)
      result = insert(result, List("iad_user_click", "iad_page"), truncateBefore(iadSequence, "2nd_jump_ctr"), ("2nd_jump_ctr" -> "二跳点击率") :: Nil)
      result = insert(result, List("pv", "iad_click"), truncateBefore(iadSequence, "game_in_rate"), ("game_in_rate" -> "游戏参与率") :: Nil)
      result = insert(result, List("impr", "iad_impr"), truncateBefore(iadSequence, "3rd_jump_impr"), ("3rd_jump_impr" -> "三跳展示数") :: Nil)
      result = insert(result, List("click", "iad_click"), truncateBefore(iadSequence, "3rd_jump_click"), ("3rd_jump_click" -> "三跳点击数") :: Nil)
      result = insert(result, List("impr", "iad_impr", "click", "iad_click"), truncateBefore(iadSequence, "3rd_jump_ctr"), ("3rd_jump_ctr" -> "三跳点击率") :: Nil)
      result = insert(result, List("conv", "click", "iad_click"), truncateBefore(iadSequence, "3rd_jump_conv_rate"), ("3rd_jump_conv_rate" -> "三跳转化率") :: Nil)
      result = insert(result, List("charge", "iad_charge"), truncateBefore(iadSequence, "3rd_jump_charge"), ("3rd_jump_charge" -> "三跳消费:元") :: Nil)
      result = insert(result, List("charge", "iad_charge", "click", "iad_click"), truncateBefore(iadSequence, "3rd_jump_cpc"), ("3rd_jump_cpc" -> "三跳CPC:元") :: Nil)
      result = insert(result, List("charge", "iad_charge", "impr", "iad_impr"), truncateBefore(iadSequence, "3rd_jump_cpm"), ("3rd_jump_cpm" -> "三跳CPM:元") :: Nil)
      result = insert(result, List("charge", "iad_charge", "conv"), truncateBefore(iadSequence, "3rd_jump_cpa"), ("3rd_jump_cpa" -> "三跳CPA:元") :: Nil)
      result = insert(result, List("iad_backup_impr", "impr", "iad_impr"), truncateBefore(iadSequence, "backup_impr_rate"), ("backup_impr_rate" -> "垫底广告展示占比") :: Nil)
      result = insert(result, List("iad_backup_click", "click", "iad_click"), truncateBefore(iadSequence, "backup_click_rate"), ("backup_click_rate" -> "垫底广告点击占比") :: Nil)
    }

    if (query.filter.exists(x => x.isInstanceOf[SelectFilter] && x.asInstanceOf[SelectFilter].dimension == "ad_type" &&
      x.asInstanceOf[SelectFilter].value == "IMAGE")) {
      result = insert(result, "variation_id", ("variation_url" -> "图片url") :: Nil)
    }
    if (query.dataSource == "luna_article_stat") {
      result = result.sortBy(x => {
        val name = Seq("os", "browser", "cardId", "cardTitle", "cardDisplay", "cardEditor", "cardUV",
          "pageURL", "pageTitle", "pageView", "cpc", "pageUV", "comment", "cpp", "share", "spp", "like", "lpp",
          "dislike", "dpp", "favorites", "fpp")
        name.indexOf(x._2)
      })
    }
    if (query.dataSource == "sdk_analyzer") {
      result = result.sortBy(x => {
        val name = Seq("fd_ht_sum", "fd_ht_avg", "fd_lt_sum", "fd_lt_avg", "fd_pt_sum", "fd_pt_avg", "fd_count", "ht_sum", "ht_max", "lt_sum", "lt_avg", "pt_sum", "pt_avg", "count")
        name.indexOf(x._2)
      })
    }
    if("wow_outlog".equals(query.dataSource)){
      result = insert(result, "pv", "uv", ("pv_avg" -> "人均pv") :: Nil)
      result = insert(result, "duration_efficient_ms", "uv", ("duration_efficient_ms_avg" -> "人均视频有效播放时长ms") :: Nil)
    }
    if ("sales_activity_event_log".equals(query.dataSource)) {
      result = insert(result, "stage_id", ("stage_name" -> "打卡阶段名称") :: Nil)
      result = insert(result, "profit_id", ("profit_name" -> "权益奖励名称") :: Nil)
    }
    if ("sdk_tdp_together".equals(query.dataSource)) {
      result = insert(result, "win_price", ("adx_consume" -> "adx消费:元") :: Nil)
      result = insert(result, "channel_did", ("channel_code" -> "渠道代号") :: Nil)
      result = insert(result, "channel_id_prefix", ("identify1" -> "流量一级id") :: Nil)
      result = insert(result, "sdk_impr", "win_price", ("impr_ecpm" -> "adx展示ecpm:元") :: Nil)
      result = insert(result, "sdk_impr", "charge", ("ecpm" -> "ecpm:元") :: Nil)
      result = insert(result, "sdk_click", "sdk_impr", ("actual_ctr" -> "点击率") :: Nil)
      result = insert(result, "sdk_click", "charge", ("cpc" -> "单价:元") :: Nil)
      result = insert(result, "tdp_click", "tdp_click_uv", ("tdp_click_uv_frequency" -> "tdp点击/设备数") :: Nil)
      result = insert(result, "tdp_click", "tdp_activate", ("tdp_activate_rate" -> "tdp激活率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_register", ("tdp_register_rate" -> "tdp注册率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_purchase", ("tdp_purchase_rate" -> "tdp购买率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_custom", ("tdp_custom_rate" -> "tdp自定义转化行为率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_loc_custom", ("tdp_loc_custom_rate" -> "tdp_loc自定义转化行为率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_day1retention", ("tdp_day1retention_rate" -> "tdp次留率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_loc_activate", ("tdp_loc_activate_rate" -> "tdp_loc激活率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_loc_register", ("tdp_loc_register_rate" -> "tdp_loc注册率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_loc_purchase", ("tdp_loc_purchase_rate" -> "tdp_loc购买率%%") :: Nil)
      result = insert(result, "tdp_click", "tdp_loc_day1retention", ("tdp_loc_day1retention_rate" -> "tdp_loc次留率%%") :: Nil)
    }
    if ("portrait_by_ead_dmp".equals(query.dataSource)) {
      result = insert(result, "birth_y", ("age" -> "年龄") :: Nil)
      result = insert(result, "win_price", ("adx_consume" -> "adx消费:元") :: Nil)
      result = insert(result, "type", ("type_name" -> "类型名称") :: Nil)
      result = insert(result, "cpa_source", ("cpa_source_name" -> "来源名称") :: Nil)
      result = insert(result, "gender", ("gender_name" -> "性别名称") :: Nil)
      result = insert(result, "city_grade", ("city_grade_name" -> "常驻市等级名称") :: Nil)
      result = insert(result, "province", ("province_name" -> "常驻省名称") :: Nil)
      result = insert(result, "reside", ("reside_name" -> "常驻市名称") :: Nil)
      result = insert(result, "reside_estate_price", ("reside_estate_price_name" -> "小区价格名称") :: Nil)
      result = insert(result, "level1_car_brand_180d", ("level1_car_brand_180d_name" -> "汽车品牌名称") :: Nil)
      result = insert(result, "phone_brand_list", ("phone_brand_list_name" -> "手机品牌名称") :: Nil)
      result = insert(result, "phone_price_list", ("phone_price_list_name" -> "手机价格名称") :: Nil)
      result = insert(result, "edu_degree", ("edu_degree_name" -> "文化程度名称") :: Nil)
      result = insert(result, "school_type", ("school_type_name" -> "大学类型名称") :: Nil)
      result = insert(result, "profession", ("profession_name" -> "职业名称") :: Nil)
      result = insert(result, "income", ("income_name" -> "收入水平名称") :: Nil)
      result = insert(result, "pay_monetary_all", ("pay_monetary_all_name" -> "付费水平名称") :: Nil)
      result = insert(result, "interest_all", ("interest_all_name" -> "兴趣类目偏好名称") :: Nil)
      result = insert(result, "pay_will", ("pay_will_name" -> "产品付费偏好名称") :: Nil)
      result = insert(result, "impr", "win_price", ("impr_ecpm" -> "adx展示ecpm:元") :: Nil)
      result = insert(result, "impr", "charge", ("ecpm" -> "ecpm:元") :: Nil)
      result = insert(result, "clk", "impr", ("actual_ctr" -> "点击率") :: Nil)
      result = insert(result, "clk", "charge", ("cpc" -> "单价:元") :: Nil)
      result = insert(result, "clk", "conv_activate", ("conv_activate_rate" -> "激活率%%") :: Nil)
      result = insert(result, "clk", "conv_register", ("conv_register_rate" -> "注册率%%") :: Nil)
      result = insert(result, "clk", "conv_purchase", ("conv_purchase_rate" -> "购买率%%") :: Nil)
      result = insert(result, "clk", "conv_day1retention", ("conv_day1retention_rate" -> "次留率%%") :: Nil)
    }
    result
  }

  private def transformQuery(query: SimpleQuery): SimpleQuery = {
    query.relativeInterval match {
      case "-1D" => SimpleQuery(query.broker, query.dataSource,
        Interval(new DateTime().withMillisOfDay(0).minusDays(1).toString("yyyy-MM-dd"), new DateTime().withMillisOfDay(0).toString("yyyy-MM-dd")),
        query.granularity, query.filter, query.groupBy, query.having
        , query.aggregation, query.limit, query.orderBy, query.dropZeros, query.relativeInterval, query.timeoutSeconds)

      case _ => query
    }

    // modify metrics
    addMoreMetrics(query)
  }

  /**
    * 根据输入修改一些metrics。
    *
    * @param query query
    * @return new query with enriched metrics
    */
  def addMoreMetrics(query: SimpleQuery): SimpleQuery = {
    var aggregation = query.aggregation
    var dataSource = query.dataSource
    var aggSet = aggregation.filter(x => !POST_AGGREGATIONS.contains(x)).toSet
    if (aggregation.contains(THIRD_JUMP_IMPR)) {
      // 这里不要用 aggSet += xxx + yyy，+=的优先级低于+
      aggSet = aggSet + "impr" + "iad_impr"
    }
    if (aggregation.contains(THIRD_JUMP_CLICK)) {
      aggSet = aggSet + "click" + "iad_click"
    }
    if (aggregation.contains(THIRD_JUMP_CHARGE)) {
      aggSet = aggSet + "charge" + "iad_charge"
    }
    query.copy(aggregation = reorderMetricsList(aggSet.toList, dataSource))
  }

  /**
    * 按照对应的dataSource中定义的metrics的顺序，返回新的metrics。
    *
    * @param list new metrics
    * @param dataSource data source
    * @return ordered new metrics
    */
  def reorderMetricsList(list: List[String], dataSource: String): List[String] = {
    val realDataSource = if (dataSource == "ead_stat") "ead_stat_new" else dataSource
    val metric = metrics(realDataSource).map(_._1)
    list.sortWith((l, r) => metric.indexOf(l) < metric.indexOf(r))
  }

  private def getSponsorIdOfInFilter(inFilter: InFilter): Set[String] = {
    (if (inFilter.values.contains(",")) inFilter.values.split(",") else inFilter.values.split(" "))
      .filter(x => x.nonEmpty)
      .map(_.trim)
      .toSet
  }

  /**
   * 如果 datasource 中有维度 sponsor_id，则添加包含所有白名单中的 sponsor_id 的 InFilter 以限制只能访问相应的广告主ID
   * 不会重复添加此 InFilter
   **/
  private def enrichQueryWithSponsorIds(user: String, query: SimpleQuery): Unit = {
    val sponsorIdConfig: Set[String] = User.userSponsorMap.getOrElse(user, List.empty).toSet
    if (sponsorIdConfig.isEmpty) {
      return
    }

    // 包含所有白名单 sponsor_id 的 InFilter
    val inFilters = query.filter.filter(f => f.isInstanceOf[InFilter]
      && f.asInstanceOf[InFilter].dimension.equals("sponsor_id")
      && sponsorIdConfig == getSponsorIdOfInFilter(f.asInstanceOf[InFilter])
    )

    if (inFilters.isEmpty) {
      var tempQueryFilter: List[Filter] = query.filter
      tempQueryFilter = tempQueryFilter :+ InFilter("sponsor_id", sponsorIdConfig.mkString(","))
      query.filter = tempQueryFilter
    }
  }

  private def validAuth(user: String, dataSource: String): Boolean = {
    if (User.userWhiteList.contains(user)) {
      // 用户在白名单中
      log.info(s"User ${user} is on user white list")
      val roles = User.userToRoleMap.getOrElse(user, Set.empty[String])
      if (roles.isEmpty) {
        // 没有配置角色，不需要收紧权限
        return true
      }
      if (roles.exists(role => User.roleToTableMap.getOrElse(role, Set.empty[String]).contains(dataSource))) {
        // 存在角色，且角色具备当前要访问的表权限
        return true
      }
    }
    // 不在白名单中 OR 角色不具备当前要访问的表权限
    false
  }


  def doSimpleQuery(dataSource: String): Action[AnyContent] = Action {
    implicit request =>
      val realDataSource = if (dataSource == "ead_stat") "ead_stat_new" else dataSource
      val dimension = dimensions(realDataSource)
      val pureDimension = dimension.flatMap(_._2)
      val metric = metrics(realDataSource)
      request.session.get(USER_NAME).map {
        user =>
          val userTable = User.getUserTable(user)
          form.bindFromRequest.fold(
            formWithErrors => BadRequest(views.html.simpleQuery(formWithErrors)(realDataSource)(dimension)(metric)(List())
            (granularities)(None)(user)("")(userTable)),
            query => {
              val newQuery = transformQuery(query)
              if (!validAuth(user, newQuery.dataSource)) {
                Ok(views.html.main(user)(newQuery.dataSource + "表信息权限受限，请联系管理员!!")(userTable))
              } else {
                var msg = ""
                if (User.userSponsorMap.getOrElse(user, List.empty).toSet.nonEmpty
                  && dimensions(query.dataSource).flatMap(_._2).map(_._1).contains("sponsor_id")) {
                  enrichQueryWithSponsorIds(user, newQuery)
                  msg = "请求中部分广告商 id 权限受限，已过滤(自动添加 InFilter)"
                }

                buildRequest(newQuery).fold(
                  error => Ok(views.html.simpleQuery(form.fill(newQuery))(realDataSource)(dimension)(metric)(List())
                  (granularities)(None)(user)("")(userTable)),
                  request => {
                    val columns: List[(String, String)] = newQuery.groupBy.filter(x => !x.startsWith("abx_test_marks_")).map {
                      g => pureDimension.filter(_._1.equals(g)).head
                    } :::
                      newQuery.aggregation.map {
                        a => metric.filter(_._1.equals(a)).head
                      }
                    val result: Option[(Long, JsValue, String)] =
                      try {
                        Some(doRequest(newQuery.dataSource, newQuery.broker, request, newQuery.dropZeros, user))
                      } catch {
                        case e: Throwable =>
                          e.printStackTrace()
                          None
                      }
                    Ok(views.html.simpleQuery(form.fill(newQuery))(realDataSource)(dimension)(metric)(enrichTableHeader(newQuery, columns))(granularities)(result)(user)(msg)(userTable))
                  }
                )
              }
            }
          )
      }.getOrElse {
        Ok(views.html.login(User.loginForm, "请登录之后再进行操作!!"))
      }
  }

  val tdpPlatformDict: Map[String, String] = Map("0" -> "默认(0)", "1" -> "小米(1)", "2" -> "VIVO(2)", "3" -> "华为(3)", "4" -> "广点通(4)", "5" -> "WIFI万能钥匙(5)", "6" -> "华为荣耀(6)", "7" -> "OPPO(7)", "8" -> "百度(8)")
  val brandClkTypeDict: Map[String, String] = Map("0" -> "落地页", "2" -> "应用直达", "3" -> "小程序")
  val billingTypeDict: Map[String, String] = Map("0" -> "CPT", "1" -> "CPM")
  val deliveryTypeDict: Map[String, String] = Map("0" -> "标准投放", "1" -> "PD", "2" -> "PDB")
  val networkMap: Map[String, String] = Map("1" -> "WIFI", "2" -> "其他", "3" -> "3G", "4" -> "4G")
}
