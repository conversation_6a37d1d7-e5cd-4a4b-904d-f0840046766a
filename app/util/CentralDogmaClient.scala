package util

import com.linecorp.centraldogma.client.CentralDogma
import com.linecorp.centraldogma.client.armeria.legacy.LegacyCentralDogmaBuilder
import com.linecorp.centraldogma.common.{Query, Revision}
import play.api.Logger

import java.util.function.BiConsumer

/**
 * <AUTHOR>
 */
object CentralDogmaClient {
  private val log: Logger = Logger(this.getClass)

  /**
   * CentralDogma Hosts
   */
  val CENTRAL_DOGMA_HOSTS = "centraldogma1.corp.youdao.com,centraldogma2.corp.youdao.com,centraldogma3.corp.youdao.com"

  /**
   * CentralDogma Port
   */
  val CENTRAL_DOGMA_PORT = 80

  /**
   * CentralDogma 客户端
   */
  var centralDogma: CentralDogma = _

  /**
   * 初始化 CentralDogma 客户端
   */
  def init(): Unit = {
    try {
      val centralDogmaBuilder = new LegacyCentralDogmaBuilder()
      val hosts = CENTRAL_DOGMA_HOSTS.split(",")
      for (host <- hosts) {
        centralDogmaBuilder.host(host, CENTRAL_DOGMA_PORT)
      }
      centralDogma = centralDogmaBuilder.build()
      log.info("Init centralDogma")
    } catch {
      case e: Exception =>
        log.error("Init centralDogma failed", e)
    }
  }
  init()

  /**
   * 监听文件变化，执行回调
   *
   * @param project    文件所在项目
   * @param repository 文件所在仓库
   * @param filename   文件名称
   * @param call       文件出现变化执行的回调
   */
  def watchConfig[T](project: String, repository: String, query: Query[T], call: BiConsumer[Revision, T]): Unit = {
    try {
      if (centralDogma != null) {
        log.info(s"Watch ${repository}'s ${query.path()} of ${project}")
        val watcher = centralDogma.fileWatcher(project, repository, query)
        watcher.watch(call)
        watcher.awaitInitialValue()
      } else {
        log.error(s"CentralDogma is null, can not watch ${repository}'s ${query.path()} of ${project}")
      }
    } catch {
      case e: Exception =>
        log.error("Register file watcher failed", e)
    }
  }
}
