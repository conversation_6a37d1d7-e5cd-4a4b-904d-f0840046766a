package outfox.ead.druid.task.guard.jdbc;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;

/**
 * <AUTHOR>
 */
public class ConnectionPool {

    private final HikariDataSource ds;

    public ConnectionPool() throws IOException {
        Properties properties = new Properties();
        try(InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("jdbc.properties")){
            properties.load(inputStream);
        }

        HikariConfig config = new HikariConfig(properties);
        ds = new HikariDataSource(config);
    }

    public Connection getConnection() throws SQLException {
        return ds.getConnection();
    }
}
