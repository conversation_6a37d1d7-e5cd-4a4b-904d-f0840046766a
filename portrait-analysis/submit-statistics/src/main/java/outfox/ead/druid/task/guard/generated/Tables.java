/*
 * This file is generated by jOOQ.
*/
package outfox.ead.druid.task.guard.generated;


import outfox.ead.druid.task.guard.generated.tables.DruidTask;

import javax.annotation.Generated;


/**
 * Convenience access to all tables in apolo
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.10.7"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * The table <code>apolo.druid_task</code>.
     */
    public static final DruidTask DRUID_TASK = DruidTask.DRUID_TASK;
}
