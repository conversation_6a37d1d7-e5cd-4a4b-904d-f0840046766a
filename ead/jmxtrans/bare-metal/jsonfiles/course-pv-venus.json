{"clusters": [{"servers": [{"host": "service1-backend-ad.gz.ynode.cn", "alias": "service1-backend-ad_gz_ynode.cn-8559", "port": 8559}, {"host": "service2-backend-ad.gz.ynode.cn", "alias": "service2-backend-ad_gz.ynode_cn-8559", "port": 8559}], "queries": [{"obj": "com.youdao.ead:type=MetricsVenusClient,name=sendMeter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "course-kafka-pv"}}]}, {"obj": "com.youdao.ead:type=MetricsVenusClient,name=dropMeter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "course-kafka-pv"}}]}, {"obj": "com.youdao.ead:type=CoursePvConsumer,name=coursePvMeter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "course-kafka-pv"}}]}]}]}