{"clusters": [{"servers": [{"host": "web04-ead.gz.ynode.cn", "alias": "web04-ead-gz-ynode-cn-8569", "port": 8569}], "queries": [{"obj": "com.youdao.ead:type=MetricsVenusClient,name=sendMeter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "luna-pv"}}]}, {"obj": "com.youdao.ead:type=MetricsVenusClient,name=dropMeter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "luna-pv"}}]}, {"obj": "com.youdao.ead:type=HzPortraitConsumer,name=pvDropMeter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "luna-pv"}}]}, {"obj": "com.youdao.ead:type=HzPortraitConsumer,name=pvTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "luna-pv"}}]}, {"obj": "com.youdao.ead:type=LunaLabelService,name=lunaTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "luna-pv"}}]}]}]}