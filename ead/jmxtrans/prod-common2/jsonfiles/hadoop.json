{"clusters": [{"servers": [{"host": "th020.corp.yodao.com", "alias": "th020", "port": 8004}, {"host": "web09-ead.gz.ynode.cn", "alias": "web09", "port": 8004}], "queries": [{"obj": "Hadoop:service=NameNode,name=JvmMetrics", "allowDottedKeys": true, "attr": ["MemHeapUsedM"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["service", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "had<PERSON>.eadata"}}]}, {"obj": "Hadoop:service=NameNode,name=NameNodeInfo", "allowDottedKeys": true, "attr": ["Threads", "Total", "Used", "NonDfsUsedSpace", "PercentUsed", "BlockPoolUsedSpace", "PercentBlockPoolUsed", "PercentRemaining", "TotalBlocks", "TotalFiles", "NumberOfMissingBlocks", "NumberOfMissingBlocksWithReplicationFactorOne"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["service", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "had<PERSON>.eadata"}}]}, {"obj": "Hadoop:service=NameNode,name=FSNamesystem", "allowDottedKeys": true, "attr": ["MissingBlocks", "MissingReplOneBlocks", "CapacityTotal", "CapacityTotalGB", "CapacityUsed", "CapacityUsedGB", "CapacityRemaining", "CapacityRemainingGB", "CapacityUsedNonDFS", "TotalLoad", "BlocksTotal", "FilesTotal"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["service", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "had<PERSON>.eadata"}}]}, {"obj": "Hadoop:service=NameNode,name=FSNamesystemState", "allowDottedKeys": true, "attr": ["NumLiveDataNodes", "NumDeadDataNodes", "NumDecomLiveDataNodes", "NumDecomDeadDataNodes", "NumDecommissioningDataNodes", "NumStaleDataNodes"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["service", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "had<PERSON>.eadata"}}]}, {"obj": "Hadoop:service=NameNode,name=RpcActivityForPort8000", "allowDottedKeys": true, "attr": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RpcProcessingTimeAvgTime"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["service", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "had<PERSON>.eadata"}}]}, {"obj": "Hadoop:service=NameNode,name=RpcDetailedActivityForPort8000", "allowDottedKeys": true, "attr": ["MonitorHealthAvgTime", "TransitionToActiveAvgTime", "RegisterDatanodeAvgTime", "BlockReportAvgTime", "AddBlockAvgTime", "CompleteAvgTime", "RollEditLogAvgTime", "SendHeartbeatAvgTime"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["service", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "had<PERSON>.eadata"}}]}]}]}