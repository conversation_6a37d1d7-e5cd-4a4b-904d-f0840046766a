{"clusters": [{"servers": [{"host": "web01-ead.sg.ynode.cn", "alias": "gorgon-sg-web01", "port": 8889}, {"host": "web02-ead.sg.ynode.cn", "alias": "gorgon-sg-web02", "port": 8889}], "queries": [{"obj": "outfox.ead.dataserv2.monitor.v2:type=dataReadProxyStatus,name=*", "resultAlias": "status", "allowDottedKeys": true, "attr": ["Size", "TimeStamp", "UpdateLag"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "dataserv2.readproxy.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=null-bid-client-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EadController,name=sdk-bid-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EadController,name=second-brand-ad-slot-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EaddController,name=eadd-ad-request-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EadmController,name=eadm-ad-request-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=GatherAppInstallInfoController,name=gather-installed-app-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EadController,name=yex-bid-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRTBNominator,name=yex-http-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EadController,name=bd-bid-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "bd.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=BrandAdScheduleController,name=sdk-prereq-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EadmController,name=eadm-ad-request-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=gorgon-error-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=zhixuan-timeout-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=yex-timeout-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=zhixuan-vacant-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=yex-vacant-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=gorgon-failtag-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=closeable-http-async-client-pending-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRtbNominator,name=yex-pool-max-gauge-default", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRtbNominator,name=yex-pool-available-gauge-default", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRtbNominator,name=yex-pool-pending-gauge-default", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRtbNominator,name=yex-pool-leased-gauge-default", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "java.lang:type=Threading", "resultAlias": "jvm", "allowDottedKeys": true, "attr": ["DaemonThreadCount", "PeakThreadCount", "ThreadCount", "TotalStartedThreadCount"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "java.lang:type=OperatingSystem", "resultAlias": "sys", "allowDottedKeys": true, "attr": ["AvailableProcessors", "TotalPhysicalMemorySize", "FreePhysicalMemorySize", "TotalSwapSpaceSize", "FreeSwapSpaceSize", "ProcessCpuLoad", "SystemCpuLoad", "SystemLoadAverage"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "java.lang:type=GarbageCollector,name=ZGC Cycles", "resultAlias": "gc", "allowDottedKeys": true, "attr": ["CollectionCount", "CollectionTime"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "java.lang:type=GarbageCollector,name=ZGC Pauses", "resultAlias": "gc", "allowDottedKeys": true, "attr": ["CollectionCount", "CollectionTime"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "java.lang:type=Memory", "resultAlias": "heap", "allowDottedKeys": true, "attr": ["HeapMemoryUsage", "NonHeapMemoryUsage"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=response-X-Adstate-meter.*", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=preBrandResponse-prebrand-meter.*", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=adResponse-eadd-ad-count-meter.*", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "\"outfox.ead.gorgon.utils\":type=\"MetricsVenusClient\",name=\"venusReadTimeout\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "\"outfox.ead.gorgon.utils\":type=\"MetricsVenusClient\",name=\"venusWriteTimeout\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "\"outfox.ead.gorgon.utils\":type=\"MetricsVenusClient\",name=\"venusReadTime\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=LoadBalanceClientCluster,name=main-cluster-bs-client-open-gauge", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=LoadBalanceClientCluster,name=main-cluster-bs-client-close-gauge", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=BrandAdScheduleControllerV2,name=prefetch-brand-request-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=BrandAdScheduleController,name=sdk-prebrand-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=SupplyDeviceRedisClient,name=vdDeviceReadTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=SupplyDeviceRedisClient,name=vdDeviceHitMeter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=SupplyDeviceRedisClient,name=vdDeviceReadTimeout", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=ImprRedisService,name=brand-impr-redis-update-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=DeviceId,name=generate-caid-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=DeviceId,name=generate-caid-blank-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=ImprRedisService,name=brand-impr-redis-get-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=ImprRedisService,name=brand-impr-redis-fail-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EadController,name=cpc-yex-long-timeout-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "bd.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=BrandDspAdService,name=brand-yex-long-timeout-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "bd.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=BrandDspAdService,name=yex-brand-bid-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "bd.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRtbNominator,name=yex-pool-max-gauge-longTimeout", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRtbNominator,name=yex-pool-available-gauge-longTimeout", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRtbNominator,name=yex-pool-pending-gauge-longTimeout", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=OpenRtbNominator,name=yex-pool-leased-gauge-longTimeout", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=ImprRedisService,name=device-frequency-get-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=ImprRedisService,name=brand-impr-executor-reject-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=EadController,name=gorgon-bid-v2-timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Mean", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}, {"obj": "youdao.ead.gorgon:type=MetricMeters,name=gorgon-bid-v2-error-meter", "resultAlias": "meter", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "sdk.gorgon"}}]}]}]}