{"clusters": [{"servers": [{"host": "bid-0.bid", "alias": "bs-tj-0", "port": 8721}, {"host": "bid-1.bid", "alias": "bs-tj-1", "port": 8721}, {"host": "bid-2.bid", "alias": "bs-tj-2", "port": 8721}, {"host": "bid-3.bid", "alias": "bs-tj-3", "port": 8721}, {"host": "bid-4.bid", "alias": "bs-tj-4", "port": 8721}, {"host": "bid-5.bid", "alias": "bs-tj-5", "port": 8721}, {"host": "bid-6.bid", "alias": "bs-tj-6", "port": 8721}, {"host": "bid-7.bid", "alias": "bs-tj-7", "port": 8721}, {"host": "bid-8.bid", "alias": "bs-tj-8", "port": 8721}, {"host": "bid-9.bid", "alias": "bs-tj-9", "port": 8721}, {"host": "bid-10.bid", "alias": "bs-tj-10", "port": 8721}, {"host": "bid-11.bid", "alias": "bs-tj-11", "port": 8721}, {"host": "bid-12.bid", "alias": "bs-tj-12", "port": 8721}, {"host": "bid-13.bid", "alias": "bs-tj-13", "port": 8721}, {"host": "bid-14.bid", "alias": "bs-tj-14", "port": 8721}, {"host": "bid-15.bid", "alias": "bs-tj-15", "port": 8721}, {"host": "bid-16.bid", "alias": "bs-tj-16", "port": 8721}, {"host": "bid-17.bid", "alias": "bs-tj-17", "port": 8721}, {"host": "bid-18.bid", "alias": "bs-tj-18", "port": 8721}, {"host": "bid-19.bid", "alias": "bs-tj-19", "port": 8721}, {"host": "bid-20.bid", "alias": "bs-tj-20", "port": 8721}, {"host": "bid-21.bid", "alias": "bs-tj-21", "port": 8721}, {"host": "bid-22.bid", "alias": "bs-tj-22", "port": 8721}, {"host": "bid-23.bid", "alias": "bs-tj-23", "port": 8721}, {"host": "bid-24.bid", "alias": "bs-tj-24", "port": 8721}, {"host": "bid-25.bid", "alias": "bs-tj-25", "port": 8721}, {"host": "bid-26.bid", "alias": "bs-tj-26", "port": 8721}, {"host": "bid-27.bid", "alias": "bs-tj-27", "port": 8721}, {"host": "bid-28.bid", "alias": "bs-tj-28", "port": 8721}, {"host": "bid-29.bid", "alias": "bs-tj-29", "port": 8721}, {"host": "bid-30.bid", "alias": "bs-tj-30", "port": 8721}, {"host": "bid-31.bid", "alias": "bs-tj-31", "port": 8721}, {"host": "bid-32.bid", "alias": "bs-tj-32", "port": 8721}, {"host": "bid-33.bid", "alias": "bs-tj-33", "port": 8721}, {"host": "bid-34.bid", "alias": "bs-tj-34", "port": 8721}, {"host": "bid-35.bid", "alias": "bs-tj-35", "port": 8721}, {"host": "bid-36.bid", "alias": "bs-tj-36", "port": 8721}, {"host": "bid-37.bid", "alias": "bs-tj-37", "port": 8721}, {"host": "bid-38.bid", "alias": "bs-tj-38", "port": 8721}, {"host": "bid-39.bid", "alias": "bs-tj-39", "port": 8721}, {"host": "bid-40.bid", "alias": "bs-tj-40", "port": 8721}, {"host": "bid-41.bid", "alias": "bs-tj-41", "port": 8721}, {"host": "bid-42.bid", "alias": "bs-tj-42", "port": 8721}, {"host": "bid-43.bid", "alias": "bs-tj-43", "port": 8721}, {"host": "bid-44.bid", "alias": "bs-tj-44", "port": 8721}, {"host": "bid-45.bid", "alias": "bs-tj-45", "port": 8721}, {"host": "bid-46.bid", "alias": "bs-tj-46", "port": 8721}, {"host": "bid-47.bid", "alias": "bs-tj-47", "port": 8721}, {"host": "bid-48.bid", "alias": "bs-tj-48", "port": 8721}, {"host": "bid-49.bid", "alias": "bs-tj-49", "port": 8721}, {"host": "bid-50.bid", "alias": "bs-tj-50", "port": 8721}, {"host": "bid-51.bid", "alias": "bs-tj-51", "port": 8721}, {"host": "bid-52.bid", "alias": "bs-tj-52", "port": 8721}, {"host": "bid-53.bid", "alias": "bs-tj-53", "port": 8721}, {"host": "bid-54.bid", "alias": "bs-tj-54", "port": 8721}, {"host": "bid-55.bid", "alias": "bs-tj-55", "port": 8721}, {"host": "bid-56.bid", "alias": "bs-tj-56", "port": 8721}, {"host": "bid-57.bid", "alias": "bs-tj-57", "port": 8721}, {"host": "bid-58.bid", "alias": "bs-tj-58", "port": 8721}, {"host": "bid-59.bid", "alias": "bs-tj-59", "port": 8721}, {"host": "bid-60.bid", "alias": "bs-tj-60", "port": 8721}, {"host": "bid-61.bid", "alias": "bs-tj-61", "port": 8721}, {"host": "bid-62.bid", "alias": "bs-tj-62", "port": 8721}, {"host": "bid-63.bid", "alias": "bs-tj-63", "port": 8721}, {"host": "bid-64.bid", "alias": "bs-tj-64", "port": 8721}, {"host": "************", "alias": "bs-tj-staging", "port": 8721}, {"host": "bids-topon-0.bids-topon", "alias": "bs-topon-0", "port": 8721}, {"host": "bids-topon-1.bids-topon", "alias": "bs-topon-1", "port": 8721}, {"host": "bids-topon-2.bids-topon", "alias": "bs-topon-2", "port": 8721}, {"host": "bids-topon-3.bids-topon", "alias": "bs-topon-3", "port": 8721}, {"host": "bids-topon-4.bids-topon", "alias": "bs-topon-4", "port": 8721}, {"host": "bids-topon-5.bids-topon", "alias": "bs-topon-5", "port": 8721}, {"host": "bids-topon-6.bids-topon", "alias": "bs-topon-6", "port": 8721}, {"host": "bids-topon-7.bids-topon", "alias": "bs-topon-7", "port": 8721}, {"host": "bids-taptap-0.bids-taptap", "alias": "bs-taptap-0", "port": 8721}, {"host": "bids-taptap-1.bids-taptap", "alias": "bs-taptap-1", "port": 8721}, {"host": "bids-taptap-2.bids-taptap", "alias": "bs-taptap-2", "port": 8721}, {"host": "bids-taptap-3.bids-taptap", "alias": "bs-taptap-3", "port": 8721}, {"host": "bids-lenovo-0.bids-lenovo", "alias": "bs-lenovo-0", "port": 8721}, {"host": "bids-lenovo-1.bids-lenovo", "alias": "bs-lenovo-1", "port": 8721}], "queries": [{"obj": "outfox.ead.dataserv2.monitor.v2:type=dataReadProxyStatus,name=*", "resultAlias": "status", "allowDottedKeys": true, "attr": ["Size", "TimeStamp", "UpdateLag"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "dataserv2.readproxy.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.server\":type=\"BidServer\",name=\"bsTimer\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.bidder\":type=\"Bidder\",name=\"queryAnaTimer\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=Bidder,name=analyzeTimer.*", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.bidder\":type=\"Bidder\",name=\"retrievalTimer\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.bidder\":type=\"Bidder\",name=\"mergeTimer\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.bidder\":type=\"Bidder\",name=\"reorderTimer\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.reorder.ctr.predict\":type=\"RmtCtrPredictor\",name=\"remoteCtrPredictTimer\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=FFMPredictor,name=ffmCtrPredictTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RmtDNNPredictor,name=dnnCtrPredictorTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RmtDNNPredictor,name=rmtDNNCtrPredictTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RmtDNNV2Predictor,name=dnn2CtrPredictorTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RmtDNNV2Predictor,name=rmtDNN2CtrPredictTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=FFMPredictor,name=ffmCvrPredictTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.bidder\":type=\"Bidder\",name=\"candidates\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "Mean", "Max", "Min", "50thPercentile", "75thPercentile", "95thPercentile", "98thPercentile", "99thPercentile", "999thPercentile", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs\":type=\"BidHandler\",name=\"emptyResponse\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs\":type=\"BidHandler\",name=\"emptyResForAllSlotMappingFailed\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs\":type=\"BidHandler\",name=\"validResponse\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid\":type=\"AdRequest\",name=\"timeout\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.server\":type=\"BidServer\",name=\"bid-waiting-size\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "java.lang:type=Threading", "resultAlias": "jvm", "allowDottedKeys": true, "attr": ["DaemonThreadCount", "PeakThreadCount", "ThreadCount", "TotalStartedThreadCount"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type"], "rootPrefix": "prod.bs"}}]}, {"obj": "java.lang:type=OperatingSystem", "resultAlias": "sys", "allowDottedKeys": true, "attr": ["AvailableProcessors", "TotalPhysicalMemorySize", "FreePhysicalMemorySize", "TotalSwapSpaceSize", "FreeSwapSpaceSize", "ProcessCpuLoad", "SystemCpuLoad", "SystemLoadAverage"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type"], "rootPrefix": "prod.bs"}}]}, {"obj": "java.lang:type=GarbageCollector,name=ZGC Cycles", "resultAlias": "gc", "allowDottedKeys": true, "attr": ["CollectionCount", "CollectionTime"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "java.lang:type=GarbageCollector,name=ZGC Pauses", "resultAlias": "gc", "allowDottedKeys": true, "attr": ["CollectionCount", "CollectionTime"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "java.lang:type=Memory", "resultAlias": "heap", "allowDottedKeys": true, "attr": ["HeapMemoryUsage", "NonHeapMemoryUsage"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=AdGroupNominator,name=ad_group_nominator_filter_timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=AdGroupNominator,name=ad_group_nominator_search_timer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "kafka.producer:type=ProducerTopicMetrics,name=MessagesPerSec,topic=pv_dsp", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "MeanRate", "OneMinuteRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name", "topic"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name", "topic"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.log.util\":type=\"BidLogLogger\",name=\"bidMeter\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.log.util\":type=\"ElectorRankingLogger\",name=\"electorRankingMeter\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.log.util\":type=\"BidFailLogLogger\",name=\"bidFailMeter\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.util\":type=\"MetricsVenusClient\",name=\"venusReadTimeout\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.util\":type=\"MetricsVenusClient\",name=\"venusWriteTimeout\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.util\":type=\"MetricsVenusClient\",name=\"venusReadTime\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=TanxRtaAcquirer,name=tanxRtaCacheHit", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=TanxRtaService,name=tanxRtaTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=TanxRtaService,name=tanxRtaRefuseMeter", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=TanxRtaService,name=tanxRtaErrorMeter", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RtaService,name=*.rtaTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RtaService,name=*.rtaBidMeter", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RtaService,name=*.rtaExceptionMeter", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RtaService,name=*.rtaSlotMeter", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.qa.feature.dictLabel\":type=\"LunaDictLabelService\",name=\"lunaTimer\"", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.qa.feature.dictLabel\":type=\"LunaDictLabelService\",name=\"lunaQuery\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.qa.feature.dictLabel\":type=\"LunaDictLabelService\",name=\"lunaHit\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"outfox.ead.dsp.bs.bid.qa.feature.dictLabel\":type=\"LunaDictLabelService\",name=\"lunaQueryFailed\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"com.youdao.quipu.kafka.producer\":type=\"LocalFileWriterWorkerThread\",name=\"serializedFailedMessages\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"com.youdao.quipu.kafka.producer\":type=\"LocalFileWriterWorkerThread\",name=\"landedMessages\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"com.youdao.quipu.kafka.producer\":type=\"LocalFileWriterWorkerThread\",name=\"flushFailedTimes\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"com.youdao.quipu.kafka.producer\":type=\"AtLeastOnceKafkaProducer\",name=\"droppedMessages\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"com.youdao.quipu.kafka.producer\":type=\"LocalFileReaderThread\",name=\"recoveredMessages\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:name=thunder_rsync_budget_cost_timer,type=ThunderService", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:name=thunder_rsync_offline_timer,type=ThunderService", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:name=ip_tool_timer,type=AdRequest", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=SimplenetRpcServer,name=rejectTaskMeter", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=ServerHandler,name=totalTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "\"com.youdao.quipu.kafka.producer\":type=\"LocalFileWriterWorkerThread\",name=\"localFileSizeGauge\"", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.tfs:type=LoadBalancedCtrClientCluster,name=tfs-available-gauge", "resultAlias": "gauge", "allowDottedKeys": true, "attr": ["Number", "Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.tfs:type=LoadBalancedCtrClientCluster,name=tfs-unavailable-gauge", "resultAlias": "gauge", "allowDottedKeys": true, "attr": ["Number", "Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=ReyunDmpService,name=reyunDmpTimer", "resultAlias": "timer", "allowDottedKeys": true, "attr": ["50thPercentile", "75thPercentile", "95thPercentile", "99thPercentile", "98thPercentile", "999thPercentile", "Max", "Min", "Mean", "MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "<PERSON><PERSON><PERSON><PERSON>"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=ReyunReporterUtils,name=reyunReporterException", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=ReyunReporterUtils,name=reyunReporterSubmitHttpRequestException", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=*,name=*.revisionGauge", "resultAlias": "gauge", "allowDottedKeys": true, "attr": ["Number", "Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RtaAcquirer,name=rta-acquirer.*", "resultAlias": "gauge", "allowDottedKeys": true, "attr": ["Number", "Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=TanxRtaAcquirer,name=tanx-feature-acquirer.*", "resultAlias": "gauge", "allowDottedKeys": true, "attr": ["Number", "Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=RtaService,name=*.rtaRejectedMeter", "resultAlias": "count", "allowDottedKeys": true, "attr": ["Count", "FifteenMinuteRate", "FiveMinuteRate", "OneMinuteRate", "MeanRate"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}, {"obj": "youdao.ead.bs:type=*,name=rta-pool-*", "resultAlias": "gauge", "allowDottedKeys": true, "attr": ["Number", "Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "prod.bs"}}]}]}]}