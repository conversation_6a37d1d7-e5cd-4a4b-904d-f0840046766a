{"clusters": [{"servers": [{"host": "*************", "alias": "yex-tracker-zw", "port": 28234, "numQueryThreads": 10}], "queries": [{"obj": "metrics:name=com.util.threadpool.*", "resultAlias": "yex", "allowDottedKeys": true, "attr": ["MeanRate", "OneMinuteRate", "FiveMinuteRate", "FifteenMinuteRate", "Count", "Value"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.tracker"}}]}, {"obj": "metrics:name=com.youdao.yex.routing.Routing$.imp*", "resultAlias": "yex", "allowDottedKeys": true, "attr": ["999thPercentile", "OneMinuteRate", "Mean", "Max", "Min"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.tracker"}}]}, {"obj": "metrics:name=com.youdao.yex.routing.Routing$.imp*", "resultAlias": "yex", "allowDottedKeys": true, "attr": ["75thPercentile", "95thPercentile", "99thPercentile"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.tracker"}}]}, {"obj": "metrics:name=com.youdao.yex.routing.Routing$.clk*", "resultAlias": "yex", "allowDottedKeys": true, "attr": ["999thPercentile", "OneMinuteRate", "Mean", "Max", "Min"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.tracker"}}]}, {"obj": "metrics:name=com.youdao.yex.routing.Routing$.clk*", "resultAlias": "yex", "allowDottedKeys": true, "attr": ["75thPercentile", "95thPercentile", "99thPercentile"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.tracker"}}]}, {"obj": "java.lang:type=Threading", "resultAlias": "jvm", "allowDottedKeys": true, "attr": ["DaemonThreadCount", "PeakThreadCount", "ThreadCount", "TotalStartedThreadCount"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type"], "rootPrefix": "yex.tracker"}}]}, {"obj": "java.lang:type=OperatingSystem", "resultAlias": "sys", "allowDottedKeys": true, "attr": ["AvailableProcessors", "TotalPhysicalMemorySize", "FreePhysicalMemorySize", "TotalSwapSpaceSize", "FreeSwapSpaceSize", "ProcessCpuLoad", "SystemCpuLoad", "SystemLoadAverage"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type"], "rootPrefix": "yex.tracker"}}]}, {"obj": "java.lang:type=GarbageCollector,name=G1 Young Generation", "resultAlias": "gc", "allowDottedKeys": true, "attr": ["CollectionCount", "CollectionTime"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.tracker"}}]}, {"obj": "java.lang:type=GarbageCollector,name=G1 Old Generation", "resultAlias": "gc", "allowDottedKeys": true, "attr": ["CollectionCount", "CollectionTime"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["type", "name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["type", "name"], "rootPrefix": "yex.tracker"}}]}, {"obj": "java.lang:type=Memory", "resultAlias": "heap", "allowDottedKeys": true, "attr": ["HeapMemoryUsage", "NonHeapMemoryUsage"], "outputWriters": [{"@class": "com.googlecode.jmxtrans.model.output.KeyOutWriter", "settings": {"outputFile": "./jmxtrans.out", "typeNames": ["name"]}}, {"@class": "com.googlecode.jmxtrans.model.output.GraphiteWriter", "settings": {"host": "quipu-graphite.inner.youdao.com", "port": 2003, "typeNames": ["name"], "rootPrefix": "yex.tracker"}}]}]}]}