plugins {
    id 'org.springframework.boot' version '3.1.2'
    id 'io.spring.dependency-management' version '1.0.14.RELEASE'
    id 'java'
}

group = 'com.youdao.ead.pacioli'
version = '1.22.0'
sourceCompatibility = '17'
targetCompatibility = '17'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
    configureEach {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
        exclude group: 'ch.qos.logback', module: 'logback-classic'
    }
}

repositories {
    maven { url 'https://nexus3.corp.youdao.com/repository/maven-public' }
    maven { url 'https://repo.spring.io/milestone' }
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-log4j2'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'com.xxl.job.core.autoconfig:spring-boot-starter-xxl-job-core:0.0.1-SNAPSHOT'
    implementation 'commons-io:commons-io:2.11.0'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'commons-validator:commons-validator:1.7'
    implementation 'com.alibaba:easyexcel:3.1.2'
    implementation 'in.zapr.druid:druidry:3.1'
    implementation 'org.mapstruct:mapstruct:1.5.3.Final'
    implementation 'io.jsonwebtoken:jjwt:0.9.1'
    implementation 'com.google.guava:guava:31.1-jre'
    implementation 'com.github.ben-manes.caffeine:caffeine:3.1.5'
    implementation 'org.apache.httpcomponents.client5:httpclient5:5.1.3'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0'
    // Why import javax stuff? See: https://stackoverflow.com/questions/43574426/how-to-resolve-java-lang-noclassdeffounderror-javax-xml-bind-jaxbexception
    implementation 'javax.xml.bind:jaxb-api:2.3.1'
    implementation 'com.belerweb:pinyin4j:2.5.1'
    implementation 'com.lmax:disruptor:3.4.2'
    implementation 'io.github.mouzt:bizlog-sdk:3.0.2'
    implementation 'io.sentry:sentry-spring-boot-starter-jakarta:6.28.0'
    implementation 'com.google.guava:guava:31.1-jre'
    implementation 'org.apache.httpcomponents.client5:httpclient5:5.2.1'
    implementation 'com.youdao:quipu-avro:0.219.3'

    implementation 'org.apache.commons:commons-text:1.12.0'
    compileOnly 'org.projectlombok:lombok'
    compileOnly 'org.jetbrains:annotations:23.0.0'

    runtimeOnly 'com.mysql:mysql-connector-j'

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
    annotationProcessor 'org.hibernate:hibernate-jpamodelgen:6.1.5.Final'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
}

tasks.named('test') {
    useJUnitPlatform()
}

bootJar {
    archiveName("pacioli.jar")
}