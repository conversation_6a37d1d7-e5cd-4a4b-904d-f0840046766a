=============================================
Gorgon错误返回码说明文档
=============================================
请求成功返回码 单个/批量广告请求
=================================
21000
    请求单条广告返回成功。
     
22001
    请求多条广告返回成功。

22002
    请求多条广告，部分返回成功。

非有效广告请求
=====================
20001
    广告非有道提供，转发流量到其它平台。

20002
    失败的广告请求，当服务端返回的广告因客户端解析出错时便会触发FAIL请求到服务端，服务端返回此状态。

20003
    广告位当前不存在有效的样式（样式被删除或者暂停了）。

不合法的广告请求
==================
40001
    广告请求构建失败。

40002
    广告位id为空导致广告请求构建失败。

40003
    请求广告数目非法（<=0）导致广告请求构建失败。

http请求转换为sdk请求异常
===========================
40101
     加密方式字段（ydet）非法。

40102
     广告请求中指定的样式名，在广告位的样式缓存里找不到。请检查广告位id是否正确传入，若正确，可能为缓存未同步，同步时间不超过15分钟。

40103
    检查广告位状态失败，不出广告。

    可能原因：

    1. 开发者将广告位的状态设为暂停或者删除状态，导致广告位本身不存在、不可用等。

    2. 广告位未审核通过。

40104
    广告位信息不存在，同步此数据到服务中最多需要180秒，可稍后重试。

40105
    无法取到加密后的请求字段，请求‘s’字段为空。

40106
     在解析Http请求的参数时发生错误。
     
     如：不合法的加密类型；加密类型存在但加密内容为空；对密文解密失败等。

40107
     根据Http请求构建有道移动广告失败。

     以上40102、40103、40104等错误均会导致构建不成功。

返回广告异常
================
41001～41014
    内部系统返回广告异常。

请求批量广告失败
==================
42001
    请求批量广告失败。

超时
=======
43001
    超时, 目前nginx最多等待后端服务900ms。

未定义异常
===========
-1
  
未定义异常。
