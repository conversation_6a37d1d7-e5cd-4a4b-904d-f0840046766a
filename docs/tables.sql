create table advisor_seller_relation
(
    id                 bigint auto_increment comment '自增ID'
        primary key,
    create_time        timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id         bigint                              null comment '创建者ID',
    last_modifier_id   bigint                              null comment '最后一次修改者ID',
    advisor_user_id    bigint                              null comment '顾问用户ID',
    seller_user_id     bigint                              null comment '销售用户ID'
)
    comment '顾问与销售关系';

create table channel
(
    id                   bigint auto_increment comment '自增ID'
        primary key,
    create_time              timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time       timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id               bigint                              null comment '创建者ID',
    last_modifier_id         bigint                              null comment '最后一次修改者ID',
    name                     varchar(128)                        not null comment '渠道名',
    state                    int                                 null comment '状态；0-已启用；1-未启用',
    bd_user_id               bigint                              null comment 'BD ID',
    disable_sub_task_ids     text                                null comment '渠道停用时，联动暂停的子任务Id，json格式：eg：[100001,100003]',
    annual_income_scale      text                                null comment '年收入规模',
    core_resource            text                                null comment '核心资源组成列举',
    history_cooperation_case text                                null comment '过往合作过的优势客户以及规模',
    hope_cooperation_type    text                                null comment '希望合作类型',
    grade                    tinyint   default 0                 not null comment '评级',
    reason                   text                                null comment '原因',
    constraint channel_name_uindex
        unique (name)
)
    auto_increment = 100000 comment '渠道';

create table channel_allocate_log
(
    id                       bigint auto_increment comment '自增ID'
        primary key,
    create_time              timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time       timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id               bigint                              null comment '创建者ID',
    last_modifier_id         bigint                              null comment '最后一次修改者ID',
    allocation_count         int                                 null comment '量级',
    allocation_name          varchar(16)                         null comment '分配指标',
    alternative_channel_json text                                null comment '渠道信息Json',
    assessment_item_json     text                                null comment '考核指标信息Json',
    min_allocation_rate      decimal(10, 4)                      null comment '渠道分配保底比例',
    allocate_result_map      text                                null comment '预测结果Json：各渠道分配数和各考核指标预测的转化率'
)
    comment '渠道任务分配工具使用记录';

create table conversion_action
(
    id                 bigint auto_increment comment '自增ID'
        primary key,
    create_time        timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id         bigint                              null comment '创建者ID',
    last_modifier_id   bigint                              null comment '最后一次修改者ID',
    name               varchar(255)                        null comment '转化类型名称',
    reflect_field      varchar(255)                        not null comment '下载数据对应的反射字段',
    type tinyint default 0 not null comment '数据类型，0-普通，1-比率，2-其他',
    `rank` smallint default 0 not null comment '展示顺序',
    constraint UK_hhdc97nnn2hgp02812789xpx6
        unique (reflect_field)
)
    comment '转化类型';

create table customer
(
    id                 bigint auto_increment comment '自增ID'
        primary key,
    create_time        timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id         bigint                              null comment '创建者ID',
    last_modifier_id   bigint                              null comment '最后一次修改者ID',
    name               varchar(255)                        not null comment '客户名称',
    operator_user_id   bigint                              null comment '所属运营用户ID',
    zhixuan_slot_ids   text                                null comment '智选广告位ID，以JSON ARRAY存储',
    zhixuan_username   text                                null comment '智选账号，以JSON ARRAY存储'
)
    auto_increment = 100000 comment '客户';

create table data_report
(
    id                             bigint auto_increment comment '自增ID'
        primary key,
    create_time                    timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time             timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id                     bigint                              null comment '创建者ID',
    last_modifier_id               bigint                              null comment '最后一次修改者ID',
    active_num                     bigint                              not null comment '激活数',
    advisor_feedback               varchar(255)                        null comment '顾问反馈',
    date                           date                                not null comment '日期',
    extend_conversion_data         varchar(1000)                        null comment '扩展转化类型列，以JSON Object存储',
    operator_feedback              varchar(255)                        null comment '运营反馈',
    sub_task_id                    bigint                              not null comment '子任务ID',
    task_id                        bigint                              not null comment '任务ID',
    advisor_id                     bigint                              null comment '顾问ID',
    channel_cpa_unit_price         bigint                              null comment '当日的渠道CPA单价，单位分',
    channel_id                     bigint                              null comment '当日所属的渠道ID',
    effective_conversion_action_id bigint                              null comment '当日有效转化列',
    constraint data_report_sub_task_id_date_uindex
        unique (sub_task_id, date)
)
    comment '数据报表';

create table disable_time_interval
(
    id                 bigint auto_increment comment '自增ID'
        primary key,
    create_time        datetime(6) null comment '创建时间',
    creator_id         bigint      null comment '创建者ID',
    last_modified_time datetime(6) null comment '最后一次修改时间',
    last_modifier_id   bigint      null comment '最后一次修改者ID',
    end                date        null comment '结束日期（含）',
    start              date        not null comment '开始日期（含）',
    sub_task_id        bigint      not null comment '子任务ID'
)
    comment '子任务不可写入数据的时间区间';

create table sub_task
(
    id                             bigint auto_increment comment '自增ID'
        primary key,
    creator_id                     bigint                                 null comment '创建者ID',
    create_time                    timestamp    default CURRENT_TIMESTAMP not null,
    last_modified_time             timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    last_modifier_id               bigint                                 null comment '最后一次修改者ID',
    bd_feedback                    varchar(200)                           null comment 'BD反馈信息',
    bd_user_id                     bigint                                 null comment 'BD人员ID',
    channel_cpa_unit_price         bigint                                 null comment '渠道CPA单价(分)',
    channel_id                     bigint                                 null comment '渠道ID',
    effective_conversion_action_id tinyint                                null comment '有效转化列 0-激活 1-注册 2-次日留存 3-加入购物车 4-购买 5-完件 6-预授信 7-授信 8-自定义',
    effective_date                 date                                   null comment '生效日期',
    extend_conversion_action_ids   varchar(500) default '[]'              null comment '除激活外新增的转化列，json格式，eg，[0, 1, 2]',
    promotion_link                 text                                   null comment '推广链接',
    state                          tinyint                                null comment '状态 0-已开启 1-已暂停',
    task_amount                    bigint                                 null comment '任务量',
    task_id                        bigint                                 not null comment '任务ID',
    advisor_id                     bigint                                 null comment '顾问ID',
    name                           varchar(100)                           not null comment '子任务名称',
    role_key_pause                 varchar(32) default 'bd'               null comment '暂停子任务的角色值',
    activity_id                    bigint default 0                       not null comment '活动id，0为无效的活动id',
    constraint UK_jsinbsbsx29e0h6g1rf8sh49s
        unique (name)
) auto_increment = 100000 comment '子任务';

create index sub_task_channel_id_index
    on sub_task (channel_id);

create table sub_task_channel_log
(
    id                     bigint auto_increment comment '自增ID'
        primary key,
    create_time            timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time     timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id             bigint                              null comment '创建者ID',
    last_modifier_id       bigint                              null comment '最后一次修改者ID',
    channel_cpa_unit_price bigint                              null comment '渠道CPA单价(分)',
    channel_id             bigint                              null comment '渠道ID',
    sub_task_id            bigint                              null comment '子任务ID',
    task_id                bigint                              null comment '任务ID',
    duty_operator_id       bigint                              null comment '责任运营ID'
) comment '子任务关联渠道修改历史';

create table task
(
    id                     bigint auto_increment comment '自增ID'
        primary key,
    create_time            timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time     timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id             bigint                              null comment '创建者ID',
    last_modifier_id       bigint                              null comment '最后一次修改者ID',
    audit_time             datetime(6)                         null comment '审核时间（仅记录不展示）',
    audit_user_id          bigint                              null comment '审核人员ID（运营ID）（仅记录不展示）',
    conversion_type        varchar(100)                        null comment '转化类型',
    customer_cpa           bigint                              null comment '客户CPA（RMB）,单位分',
    customer_payment       varchar(200)                        null comment '客户款项',
    customer_id            bigint                              null comment '客户名称（可关联到责任运营）',
    effective_date         date                                null comment '生效日期',
    kpi                    varchar(200)                        null comment '后端KPI',
    name                   varchar(100)                        not null comment '任务名称',
    orientation_info       varchar(200)                        null comment '定向信息',
    os                     tinyint                             null comment '客户端 0-安卓 1-IOS',
    preview_link           text                                null comment '预览链接',
    promotion_product_name varchar(100)                        null comment '推广产品名称',
    rebate                 decimal(10, 2)                      null comment '返点',
    regional_orientation   bit                                 null comment '地域定向',
    sales_id               bigint                              null comment '销售ID',
    state                  int                                 null comment '任务状态 0-待发布 1-待审核 2-已发布 4-审核不通过 >300-已下架',
    view_zhixuan           bit                                 null comment '查看智选后台',
    zhixuan_username       varchar(100)                        null comment 'Noah用户名',
    advisor_id             bigint    default -1                null comment '顾问ID',
    effective_conversion_action_id bigint default 0            not null comment '有效转化行为id',
    integration_method     json                                not null comment '对接方式，json array格式，eg，["API", "APK", "RTA_API", "CUSTOM"]',
    integration_method_custom_text varchar(100)                comment '对接方式自定义内容',
    audit_fail_reason      varchar(500)                        comment '审核不通过原因',
    promotion_category_id  int default 0                       not null comment '推广类别',
    product_category_id    int default 33                      not null comment '产品类别id'
) auto_increment = 100000 comment '任务';

create table user
(
    id                 bigint auto_increment comment '自增ID'
        primary key,
    create_time        timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id         bigint                              null comment '创建者ID',
    last_modifier_id   bigint                              null comment '最后一次修改者ID',
    corp_email         varchar(255)                        null comment '集团邮箱',
    deleted            bit       default b'0'              not null comment '逻辑删除;0-未删除；1-已删除',
    name               varchar(255)                        not null comment '用户名',
    password           varchar(255)                        not null comment '密码',
    phone              varchar(255)                        not null comment '手机号(RSA)',
    role_key           varchar(255)                        not null comment '角色值',
    state              tinyint   default 0                 not null comment '用户状态；0-启用；1-禁用'
) auto_increment = 100000 comment '用户';

create table user_leader_relation
(
    id                 bigint auto_increment comment '自增ID'
        primary key,
    create_time        timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    creator_id         bigint                              null comment '创建者ID',
    last_modifier_id   bigint                              null comment '最后一次修改者ID',
    user_id            bigint                              not null comment '用户ID,主管也可能会有主管的leader',
    leader_id          bigint                              not null comment '用户主管ID'
) comment 'user与leader关系';


insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (1, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '注册', 'register', 0, 20);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (2, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '注册率', 'registerRate', 1, 22);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (3, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '次日留存', 'morrowRetention', 0, 30);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (4, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '次日留存率', 'morrowRetentionRate', 1, 32);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (5, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '加入购物车', 'addCart', 0, 60);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (6, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '加入购物车率', 'addCartRate', 1, 62);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (7, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '购买', 'buy', 0, 80);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (8, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '购买率', 'buyRate', 1, 82);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (9, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '完件', 'complete', 0, 90);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (10, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '完件率', 'completeRate', 1, 92);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (11, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '预授信', 'preCredit', 0, 100);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (12, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '预授信率', 'preCreditRate', 1, 102);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (13, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '授信', 'credit', 0, 110);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (14, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '授信率', 'creditRate', 1, 112);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (15, '2022-11-07 16:10:06', '2022-12-19 14:38:41', null, null, '自定义行为1', 'customAction', 0, 310);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (17, '2022-12-12 16:10:06', '2022-12-19 14:38:41', null, null, '自定义行为2', 'customAction2', 0, 320);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (19, '2022-12-13 13:30:00', '2022-12-19 14:38:41', null, null, '创角', 'characterCreate', 0, 120);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (20, '2022-12-13 13:30:00', '2022-12-19 14:38:41', null, null, '创角率', 'characterCreateRate', 1, 122);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (21, '2022-12-13 13:30:00', '2022-12-19 14:38:41', null, null, '充值数', 'rechargeAmount', 0, 130);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (22, '2022-12-13 13:30:00', '2022-12-19 14:38:41', null, null, '充值率', 'rechargeAmountRate', 1, 132);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (23, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '首日充值金额', 'firstDayRechargeAmount', 2, 140);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (25, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '首日ROI', 'firstDayRoi', 2, 150);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (27, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '3日ROI', 'threeDaysRoi', 2, 160);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (29, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '7日ROI', 'sevenDaysRoi', 2, 170);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (31, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '播放数', 'playbackAmount', 0, 180);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (32, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '播放率', 'playbackAmountRate', 1, 182);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (33, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '目标付费数', 'targetPaymentAmount', 0, 190);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (34, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '目标付费率', 'targetPaymentAmountRate', 1, 192);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (35, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '首购', 'firstTimeBrought', 0, 200);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (36, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '首购率', 'firstTimeBroughtRate', 1, 202);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (37, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '唤端', 'wakeUpClient', 0, 210);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (38, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '唤端率', 'wakeUpClientRate', 1, 212);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (39, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '首唤', 'firstTimeWakeUpClient', 0, 220);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (40, '2022-12-13 13:30:01', '2022-12-19 14:38:42', null, null, '首唤率', 'firstTimeWakeUpClientRate', 1, 222);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (41, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, 'LTV1', 'ltv1', 2, 240);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (43, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, 'LTV15', 'ltv15', 2, 250);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (45, '2022-12-13 13:30:01', '2022-12-19 14:38:42', null, null, 'LTV30', 'ltv30', 2, 260);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (47, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, 'LTV15累计充值额', 'ltv15AddUpRechargeAmount', 2, 270);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (49, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, 'LTV30累计充值额', 'ltv30AddUpRechargeAmount', 2, 280);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (51, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '安装', 'install', 0, 10);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (52, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '安装率', 'installRate', 1, 12);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (53, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '3日留存', 'threeDayRetention', 0, 40);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (54, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '3日留存率', 'threeDayRetentionRate', 1, 42);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (55, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '7日留存', 'sevenDayRetention', 0, 50);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (56, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '7日留存率', 'sevenDayRetentionRate', 1, 52);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (57, '2022-12-13 13:30:01', '2022-12-19 14:38:42', null, null, '下单', 'placeOrder', 0, 70);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (58, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '下单率', 'placeOrderRate', 1, 72);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (59, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '作弊', 'cheating', 2, 230);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (61, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '结算单价', 'settlementUnit', 2, 290);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (63, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '结算金额', 'settlementAmount', 2, 300);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, rank)
values (65, '2022-12-13 13:30:01', '2022-12-19 14:38:41', null, null, '激活率', 'activeRate', 1, 2);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, `rank`)
VALUES (67, '2023-03-05 13:30:01', '2023-03-05 13:30:01', null, null, '回传激活', 'activeCallBack', 3, 4);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, `rank`)
VALUES (69, '2023-03-05 13:30:01', '2023-03-05 13:30:01', null, null, '回传注册', 'registerCallBack', 3, 24);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, `rank`)
VALUES (71, '2023-03-05 13:30:01', '2023-03-05 13:30:01', null, null, '回传次日留存', 'morrowRetentionCallBack', 3, 34);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, `rank`)
VALUES (73, '2023-03-05 13:30:01', '2023-03-05 13:30:01', null, null, '回传加入购物车', 'addCartCallBack', 3, 64);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, `rank`)
VALUES (75, '2023-03-05 13:30:01', '2023-03-05 13:30:01', null, null, '回传购买', 'buyCallBack', 3, 84);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, `rank`)
VALUES (77, '2023-03-05 13:30:01', '2023-03-05 13:30:01', null, null, '回传授信', 'creditCallBack', 3, 114);
insert into pacioli.conversion_action (id, create_time, last_modified_time, creator_id, last_modifier_id, name,
                                       reflect_field, type, `rank`)
VALUES (79, '2023-03-05 13:30:01', '2023-03-05 13:30:01', null, null, '回传自定义行为', 'customActionCallBack', 3, 314);

# 渠道流量监控 V1.4 (https://confluence.inner.youdao.com/pages/viewpage.action?pageId=346312371)

# 新增用户和角色关联关系表
create table user_role_relation
(
    id               bigint auto_increment
        primary key,
    create_time      timestamp default CURRENT_TIMESTAMP not null,
    last_modified_time    timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    user_id          bigint                              not null comment '用户id',
    role_key         varchar(255)                        not null comment '角色',
    creator_id       bigint                              not null comment '创建者id',
    last_modifier_id bigint                              null comment '最后一次修改者id',
    deleted          bit       default b'0'              not null comment '逻辑删除;0-未删除；1-已删除',
    state            tinyint   default 0                 not null comment '角色状态；0-启用；1-禁用'
)
    comment '用户角色绑定表';

# 用户表新增字段 163邮箱
alter table user
    add email varchar(255) null comment '163邮箱';

# 初始化用户角色关系表的数据
INSERT INTO user_role_relation
(
		id,
    create_time,
    last_modified_time,
    user_id,
    role_key,
    state,
    creator_id,
    last_modifier_id
)
SELECT
    id,
    create_time,
    last_modified_time,
    id AS user_id,
    role_key,
    state,
    id AS creator_id,
    last_modifier_id
FROM
    user;

# 任务表的智选用户名字段类型变更
ALTER TABLE task
    MODIFY COLUMN zhixuan_username varchar(1024) NULL COMMENT 'Noah用户名';

# 任务表的智选用户名初始化
UPDATE task
SET zhixuan_username = CONCAT('[', zhixuan_username, ']')
WHERE zhixuan_username IS NOT NULL;

# 屏蔽规则表新增字段
alter table anti_fraud_config
    add relate_channel_refresh_mode varchar(32) not null comment '关联渠道刷新模式 NORMAL-常规 AUTO-自动';
alter table anti_fraud_config
    add relate_channel_dids longtext null comment '关联的渠道did';
# 屏蔽规则表字段初始化
update anti_fraud_config set relate_channel_dids = '[]', relate_channel_refresh_mode='AUTO' ;

# 修改表的creator_id和last_modifier_id的字段名以及含义
ALTER TABLE advisor_seller_relation
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE anti_fraud_config
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NOT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NOT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE anti_fraud_rule
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NOT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NOT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE channel
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE channel_allocate_log
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE conversion_action
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE customer
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE data_report
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE disable_time_interval
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE sub_task
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE sub_task_channel_log
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE task
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE `user`
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE user_leader_relation
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';
ALTER TABLE user_role_relation
    CHANGE COLUMN creator_id creator_user_role_relation_id BIGINT NULL COMMENT '创建者-用户角色ID',
    CHANGE COLUMN last_modifier_id last_modifier_user_role_relation_id BIGINT NULL COMMENT '最后一次修改者-用户角色ID';

-- # 道流量监控 V1.5 新增一期反作弊规则
INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num) VALUES (83, 1, 1, '2024-11-26 11:43:31', '2024-11-26 12:15:49', '手机机型与推广类型不一致', 0, '参数异常', 'unexpected_model_click', '', 20004);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num) VALUES (76, 1, 1, '2024-11-26 11:43:31', '2024-11-26 12:15:49', 'UA格式错误', 0, '参数异常', 'format_error_ua', '', 20005);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num) VALUES (77, 1, 1, '2024-11-26 11:43:31', '2024-11-26 12:15:49', '设备号格式错误', 0, '参数异常', 'format_error_device_id', '', 20006);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num) VALUES (78, 1, 1, '2024-11-26 11:43:31', '2024-11-26 12:15:49', '内网IP', 0, '参数异常', 'value_error_intranet_ip', '', 20010);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num) VALUES (79, 1, 1, '2024-11-26 11:43:31', '2024-11-26 12:15:49', '黑IP', 0, '参数异常', 'value_error_dark_ip', '', 20008);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num) VALUES (80, 1, 1, '2024-11-26 11:43:31', '2024-11-26 12:15:49', '黑设备id', 0, '参数异常', 'value_error_dark_ip', '', 20009);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num) VALUES (81, 1, 1, '2024-11-26 11:43:31', '2024-11-26 12:15:49', 'IP取值异常', 0, '参数异常', 'value_error_ip', '', 20007);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num) VALUES (82, 1, 1, '2024-11-26 11:43:31', '2024-11-26 12:15:49', 'UA取值异常', 0, '参数异常', 'value_error_ua', '', 20011);

-- # 道流量监控 V1.5 新增二期反作弊规则

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (18, 1, 1, DEFAULT, DEFAULT, '未携带操作系统版本号', 0, '参数为空过滤', 'param_filter', '', 30014)

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (84, 1, 1, DEFAULT, DEFAULT, '请求和点击-设备号不一致', 0, '参数间关联异常', 'related_rta_device', DEFAULT, 50012) ;

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (85, 1, 1, DEFAULT, DEFAULT, '请求和点击-UA不一致', 0, '参数间关联异常', 'related_rta_ua', DEFAULT, 50013) ;

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (86, 1, 1, DEFAULT, DEFAULT, '请求和点击-机型不一致', 0, '参数间关联异常', 'related_rta_model', DEFAULT, 50014) ;

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (87, 1, 1, DEFAULT, DEFAULT, '请求和点击-IP不一致', 0, '参数间关联异常', 'related_rta_ip', DEFAULT, 50015) ;

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (88, 1, 1, DEFAULT, DEFAULT, '请求和点击-操作系统版本号不一致', 0, '参数间关联异常', 'related_rta_osv', DEFAULT, 50016) ;

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (89, 1, 1, DEFAULT, DEFAULT, '同一设备号关联多个UA', 0, '参数间关联异常', 'related_multi_ua', '', 50009);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (90, 1, 1, DEFAULT, DEFAULT, '同一设备号关联多个机型', 0, '参数间关联异常', 'related_multi_model', '', 50010);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (91, 1, 1, DEFAULT, DEFAULT, '同一设备关联多个osv', 0, '参数间关联异常', 'related_multi_osv', '', 50011);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (93, 1, 1, DEFAULT, DEFAULT, 'IP高频变化', 0, '行为异常', 'action_rate_device_ip', '', 10002);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (94, 1, 1, DEFAULT, DEFAULT, '设备号日点击量级过大', 0, '行为异常', 'action_rate_click', '', 10003);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (95, 1, 1, DEFAULT, DEFAULT, 'RTA点击时间异常', 0, '行为异常', 'rta_click_time_error', '', 10004);

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id, create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (96, 1, 1, DEFAULT, DEFAULT, '点击跨渠道上报', 0, '行为异常', 'action_features_diff_source', '', 10005);

UPDATE pacioli.anti_fraud_rule t SET t.type = '参数间关联异常', t.sort_num = 50001 WHERE t.id = 2;

UPDATE pacioli.anti_fraud_rule t SET t.type = '参数间关联异常', t.sort_num = 50002 WHERE t.id = 3;

UPDATE pacioli.anti_fraud_rule t SET t.type = '参数间关联异常', t.sort_num = 50003 WHERE t.id = 20;

UPDATE pacioli.anti_fraud_rule t SET t.type = '参数间关联异常', t.sort_num = 50004 WHERE t.id = 83;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 16;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 7;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 14;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 6;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 17;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 15;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 4;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 13;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 12;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 10;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 9;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 11;

UPDATE pacioli.anti_fraud_rule t SET t.extend_info = ''
WHERE t.id = 8;

-- # 道流量监控 V1.5 新增三期反作弊规则

UPDATE pacioli.anti_fraud_rule t
SET t.sort_num = 10005
WHERE t.id = 95;

UPDATE pacioli.anti_fraud_rule t
SET t.sort_num = 10006
WHERE t.id = 96;

UPDATE pacioli.anti_fraud_rule t
SET t.sort_num = 10003
WHERE t.id = 93;

UPDATE pacioli.anti_fraud_rule t
SET t.sort_num = 10004
WHERE t.id = 94;

INSERT INTO pacioli.anti_fraud_rule (id, creator_user_role_relation_id, last_modifier_user_role_relation_id,
                                     create_time, last_modified_time, name, status, type, tags, extend_info, sort_num)
VALUES (92, 1, 1, '2025-01-13 16:37:25', '2025-01-13 16:37:25', 'IP分钟级点击量级过大', 0, '行为异常', 'action_rate_ip',
        '', 10002);
