package outfox.ead.stat;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeZone;
import org.joda.time.Interval;
import org.joda.time.Period;
import org.joda.time.format.DateTimeFormat;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultConfiguration;
import outfox.ead.druid.task.guard.generated.tables.records.DruidTaskRecord;
import outfox.ead.druid.task.guard.jdbc.ConnectionPool;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static outfox.ead.druid.task.guard.generated.Tables.DRUID_TASK;
import static outfox.ead.stat.MergeDeviceCrowdTag.DRUID_INPUT_HDFS_PREFIX;

/**
 * <AUTHOR>
 */
@Slf4j
public class DruidClient {
    private static String druidOverloadHost = "http://eadata-druid-overlord.corp.youdao.com/";
    private static String druidOverlordUrl = druidOverloadHost + "druid/indexer/v1/task";
    private static final String MACRO_INTERVALS = "@INTERVALS@";
    private static final String MACRO_STATIC_FILE_PATHS = "@STATIC_FILE_PATHS@";
    private static final org.joda.time.format.DateTimeFormatter HDFS_PATH_HOURLY_FORMATTER = DateTimeFormat.forPattern("yyyy/MM/dd/HH");

    private final HdfsClient HDFS_CLIENT = new HdfsClient();

    private static DSLContext dslContext;

    static {
        try {
            dslContext = DSL.using(new DefaultConfiguration()
                    .set(new ConnectionPool().getConnection())
                    .set(SQLDialect.MYSQL));
        } catch (Exception e) {
            log.error("init jooq failed!", e);
        }
    }

    /**
     * 提交相应时间段的任务到druid。
     * @param interval 时间段
     * @param rawPayload 使用的druid tasks配置，包含需要替换的宏。
     */
    public void submitDruidTask(Interval interval, String rawPayload, boolean rollUp) {
        log.info("prepare to submit druid ingest task of interval {}", interval);
        try {
            String payload = StringUtils.replaceAll(rawPayload, MACRO_INTERVALS, interval.toString());
            if (!rollUp) {
                payload = StringUtils.replaceAll(payload, MACRO_STATIC_FILE_PATHS,
                        StringUtils.join(getStaticFilePathsHourly(interval), ","));
            }
            log.info("prepare to submit payload {}", payload);
            String taskId = HttpClient.post(druidOverlordUrl, payload);
            if (StringUtils.isNotBlank(taskId)) {
                write2ApoloDruidTask(taskId, payload);
                // !!!! 这里的日志关键字不能改，在check_job.sh中有用到
                log.info("success submit druid task, id {}", taskId);
            } else {
                log.error("failed submit druid task, payload {}", payload);
            }
        } catch (Exception e) {
            log.error("failed to submit druid task.", e);
        }
    }

    private void write2ApoloDruidTask(String taskId, String payload) {
        DruidTaskRecord task = dslContext.newRecord(DRUID_TASK);
        task.setTaskId(taskId);
        task.setPalyload(payload);
        task.setRetriedTimes(0);
        task.setStatus("RUNNING");
        dslContext.executeInsert(task);
        log.info("insert new druid task to apolo.\n{}", task);
    }

    private Collection<String> getStaticFilePathsHourly(Interval interval) throws IOException {
        return getHourlyHdfsPath(interval, false);
    }

    private Collection<String> getHourlyHdfsPath(@NotNull Interval interval, Boolean ignoreMissPath) throws IOException {
        Collection<Interval> intervals = getHourlyIntervals(interval);
        List<String> paths = new ArrayList<>();
        for (Interval hourlyInterval : intervals) {
            String path = String.format("%s/%s", DRUID_INPUT_HDFS_PREFIX,
                    hourlyInterval.getStart().toLocalDateTime().toString(HDFS_PATH_HOURLY_FORMATTER));
            if (HDFS_CLIENT.isNonEmptyDirectory(path)) {
                paths.add(path);
            } else {
                if (ignoreMissPath) {
                    log.error("not find path or path is empty, path is {}", path);
                } else {
                    throw new IOException("not find path or path is empty, path:" + path);
                }
            }
        }
        return paths;
    }

    private Collection<Interval> getHourlyIntervals(@javax.validation.constraints.NotNull Interval interval) {
        long start = interval.getStartMillis();
        long end = interval.getEndMillis();
        Period granularity = Period.hours(1);

        if (start > end) {
            return Collections.emptyList();
        }
        long chunkSize = granularity.toStandardDuration().getMillis();
        long left = getFloor(start, granularity);
        long right = getCeil(end, granularity);
        if (left == right && start == left) {
            // start & end must be on the boarder of granularity
            right = left + granularity.toStandardDuration().getMillis();
        }
        List<Interval> list = new ArrayList<>();
        while (left < right) {
            list.add(new Interval(left, (left = left + chunkSize)));
        }
        return list;
    }

    private long getFloor(long timestamp, Period granularity) {
        org.joda.time.LocalDateTime localDateTime = new org.joda.time.LocalDateTime(timestamp, DateTimeZone.getDefault());
        localDateTime = localDateTime.minusMillis((int) (localDateTime.toDateTime(DateTimeZone.UTC).getMillis() %
                (granularity.toStandardDuration().getMillis())));
        return localDateTime.toDateTime().toDateTime().getMillis();
    }

    private long getCeil(long timestamp, Period granularity) {
        long floor = getFloor(timestamp, granularity);
        return timestamp == floor ? floor : floor + granularity.toStandardDuration().getMillis();
    }


}
