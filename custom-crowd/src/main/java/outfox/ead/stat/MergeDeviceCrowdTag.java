package outfox.ead.stat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.PosixParser;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.spark.launcher.SparkAppHandle;
import org.apache.spark.launcher.SparkLauncher;
import org.joda.time.Interval;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static java.time.ZoneId.SHORT_IDS;

/**
 * 完成interest_pv join上自定义人群id的功能。详细信息参考readme.md。
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class MergeDeviceCrowdTag {

    private static final DateTimeFormatter FORMATTER_HOUR = DateTimeFormatter.ofPattern("yyyy/MM/dd/HH");
    private static final ThreadFactory JOIN_THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("join-pool-%d").build();
    private static final int MAX_WAITING_HOURS = 20;

    static final String DRUID_INPUT_HDFS_PREFIX = "/user/eadata/custom-crowd/hourly";
    private static final String ZONE_ID_SHANGHAI = "Asia/Shanghai";

    private static final HdfsClient HDFS_CLIENT = new HdfsClient();
    private static final DruidClient DRUID_CLIENT = new DruidClient();

    static final String CUSTOM_CROWD_IMEI_MD5_HDFS = "/user/eadata/custom-crowd/dc_imei_md5";
    static final String CUSTOM_CROWD_IDFA_MD5_HDFS = "/user/eadata/custom-crowd/dc_idfa_md5";
    /**
     * 这些天没有修改过的文件的词包以前应该已经跑过，不需要重跑。
     */
    private static int crowdIgnoreDay = 1;

    /**
     * 是否在提交完一次job后对其进行按天的 rollup 。
     */
    private static boolean doRollup = false;

    /**
     * 默认需要join上7天以前的pv数据。
     */
    private static int joinWindow = 7;

    /**
     * 同时提交join任务数 默认为20
     */
    private static int joinPoolSize = 20;

    private static boolean runInDefaultInterval = true;
    private static LocalDateTime startTime;
    private static LocalDateTime endTime;
    private static String javaHome;
    private static String sparkHome;
    private static String appResource;
    private static String joinClass;
    private static String unionClass;
    private static String propertiesFile;

    /**
     * 此schema中intervals和paths使用宏替换的方法更新，所以需要单独的文件。
     */
    private static String druidSchemaPath = "/mfs_ead/eadata/ead-conf/eadata/druid/online/batch-ingestion-schema/hadoop_index_interest_stat_updated.json";

    /**
     * 现在是7天的数据分7*24小时跑148次，最后整个按天rollup一次，以下schema是按天rollup配置。
     */
    private static String druidRollupSchemaPath = "/mfs_ead/eadata/ead-conf/eadata/druid/online/batch-ingestion-schema/hadoop_index_interest_stat_updated_rollup.json";

    public static void main(String[] args) throws Exception {

        tryDeleteOldData(-8);

        parseArgs(args);
        printParameters();
        System.setProperty("java.util.logging.SimpleFormatter.format", "%5$s%6$s%n");

        final LocalDateTime todayMidNight = LocalDateTime.of(LocalDate.now(ZoneId.of(SHORT_IDS.get("CTT"))), LocalTime.MIDNIGHT);

        List<CrowdInfo> allCrowdInfo = loadAllCrowdInfo();
        final AtomicReference<LocalDateTime> newestFileModTime = new AtomicReference<>();
        List<CrowdInfo> crowdInfos = allCrowdInfo
                .stream()
                .filter(c -> StringUtils.isNotBlank(c.getFilePath()))
                .filter(c -> HDFS_CLIENT.isFileExist(c.getFilePath()))
                // 如果昨天没有提交新的自定义人群包，则不必跑
                .filter(c -> {
                    // 得到当前最新文件修改时间，方便打log
                    LocalDateTime fileModTime = HDFS_CLIENT.getModifiedTime(c.getFilePath());
                    if (newestFileModTime.get() == null || newestFileModTime.get().isBefore(fileModTime)) {
                        newestFileModTime.set(fileModTime);
                    }
                    return fileModTime.plusDays(1).isAfter(todayMidNight);
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crowdInfos)) {
            log.info("{} has no more new custom crowd, current new file modify time is {}.", todayMidNight.minusDays(1), newestFileModTime.get());
            return;
        }

        final List<CrowdInfo> crowdInfoList = allCrowdInfo
                .stream()
                .filter(c -> StringUtils.isNotBlank(c.getFilePath()))
                .filter(c -> HDFS_CLIENT.isFileExist(c.getFilePath()))
                //过滤掉N天前上传的文件。
                .filter(c -> HDFS_CLIENT.getModifiedTime(c.getFilePath()).plusDays(crowdIgnoreDay).isAfter(todayMidNight))
                .collect(Collectors.toList());
        log.info("+++++++++++++++++++++++++++++++++++++++");
        if (crowdInfoList.isEmpty()) {
            log.info("no custom crowd need add to pv, job done.");
            return;
        } else {
            crowdInfoList.forEach(crowdInfo -> {
                // !!!! 这里的日志关键字不能改，在check_job.sh中有用到
                log.info("will merge custom crowd:" + crowdInfo.toString());
            });
        }
        log.info("+++++++++++++++++++++++++++++++++++++++");

        if (!unionCustomCrowdInSubProcess(crowdInfoList)) {
            log.info("failed unionCustomCrowdInSubProcess, job failed");
            return;
        }

        if (runInDefaultInterval) {
            runWithDefaultInterval();
        } else {
            runInterval();
        }
    }

    /**
     * 删除{@code countOfDay}天以前的数据。
     * <p>删除时目录尾不能是/*，否则不会删除亦不会报错。</p>
     */
    private static void tryDeleteOldData(long countOfDay) {
        String path = DRUID_INPUT_HDFS_PREFIX +
                DateTimeFormatter.ofPattern("/yyyy/MM/dd")
                        .format(LocalDateTime.now().plusDays(countOfDay));
        if (HDFS_CLIENT.deletePath(path, true)) {
            log.info("delete hdfs path {}", path);
        } else {
            log.error("failed delete hdfs path {}", path);
        }
    }

    private static String getDruidSchemaPath(String schemaPath) throws IOException {
        StringBuilder stringBuilder = new StringBuilder();
        log.info("schemaPath = {}", schemaPath);
        for (String s : Files.readAllLines(Paths.get(schemaPath))) {
            stringBuilder.append(s);
        }
        return stringBuilder.toString();
    }

    private static void printParameters() {
        log.info("+++++++++++++++++++++++++++++++++++++++");
        log.info("run with parameters:\n" +
                "startTime:{}\n" +
                "endTime:{}\n" +
                "joinWindow:{}\n" +
                "druidSchemaPath:{}\n" +
                "doRollup:{}\n" +
                "crowdIgnoreDay:{}\n", startTime, endTime, joinWindow, druidSchemaPath, doRollup, crowdIgnoreDay);
        log.info("+++++++++++++++++++++++++++++++++++++++");
    }

    /**
     * 默认按从当前时间向前走7天来重跑数据。
     */
    private static void runWithDefaultInterval() throws InterruptedException, IOException {
        endTime = LocalDateTime.now(ZoneId.of(ZONE_ID_SHANGHAI)).truncatedTo(ChronoUnit.DAYS);
        startTime = endTime.minusDays(joinWindow);
        runInterval();
    }

    /**
     * 重跑指定时间内的数据，提交一次 druid 任务更新 interest_stat 表。
     */
    private static void runInterval() throws InterruptedException, IOException {
        log.info("run with time parameter, startTime {} endTime {}", startTime, endTime);
        String druidSchema = getDruidSchemaPath(druidSchemaPath);
        LocalDateTime tempTime = startTime;
        int duration = (int) Duration.between(startTime, endTime).toHours();
        CountDownLatch latch = new CountDownLatch(duration);
        ExecutorService executor = new ThreadPoolExecutor(joinPoolSize, 200,
                0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(1024),
                JOIN_THREAD_FACTORY, new ThreadPoolExecutor.AbortPolicy());

        while (endTime.isAfter(tempTime)) {
            LocalDateTime curHour = tempTime;
            executor.execute(() -> {
                log.info("+++++++++++++++++++ join data in {} started +++++++++++++++++++", curHour);
                try {
                    joinByHourInSubProcess(curHour.format(FORMATTER_HOUR));
                    DRUID_CLIENT.submitDruidTask(new Interval(curHour.toInstant(ZoneOffset.ofHours(8)).toEpochMilli(),
                            curHour.plusHours(1).toInstant(ZoneOffset.ofHours(8)).toEpochMilli()), druidSchema, false);
                } catch (Exception e) {
                    log.error("+++++++++++++++++++ join data in {} failed +++++++++++++++++++", curHour, e);
                    return;
                } finally {
                    log.info("+++++++++++++++++++ join data in {} finished +++++++++++++++++++", curHour);
                }
                latch.countDown();
            });
            tempTime = tempTime.plusHours(1);
            Thread.sleep(1000);
        }

        // 当全部 join 任务成功完成时，提交 rollup druid 任务
        if (latch.await(MAX_WAITING_HOURS, TimeUnit.HOURS)) {
            //最后的druid 任务提交后，等待10min，避免网卡引起 rollup先于其他任务提交
            Thread.sleep(600000);
            String druidRollupSchema = getDruidSchemaPath(druidRollupSchemaPath);
            DRUID_CLIENT.submitDruidTask(new Interval(startTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli(),
                    endTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()), druidRollupSchema, true);
        } else {
            sendAlertSms("13051570228","some data-join jobs in custom-crowd, not submit druid task!!");
            sendAlertSms("15210646461","some data-join jobs in custom-crowd, not submit druid task!!");
        }
        executor.shutdown();
    }

    /**
     * 创建一个新进程，依次按IMEI、IMEI_MD5、IDFA顺序union上自定义词包id。
     */
    private static boolean unionCustomCrowdInSubProcess(List<CrowdInfo> crowdInfoList) throws IOException {

        String crowds = new ObjectMapper().writeValueAsString(crowdInfoList);
        log.info("+++++++++++++++++++ unionCustomCrowdInSubProcess started +++++++++++++++++++");
        try {
            final String[] appArgs = new String[]{"--crowds", crowds};
            SparkAppHandle handler = new SparkLauncher()
                    .setAppName("UnionCustomCrowdByDeviceType")
                    .setVerbose(true)
                    .setJavaHome(javaHome)
                    .setSparkHome(sparkHome)
                    .setAppResource(appResource)
                    .setMainClass(unionClass)
                    .setPropertiesFile(propertiesFile)
                    .setMaster("yarn")
                    .setDeployMode("client")
                    .setConf("spark.executor.instances", "8")
                    .setConf(SparkLauncher.DRIVER_MEMORY, "4g")
                    .setConf(SparkLauncher.EXECUTOR_MEMORY, "32g")
                    .setConf(SparkLauncher.EXECUTOR_CORES, "4")
                    .setConf("spark.network.timeout", "200")
                    .addAppArgs(appArgs)
                    .startApplication();

            while (!"FINISHED".equalsIgnoreCase(handler.getState().toString())
                    && !"FAILED".equalsIgnoreCase(handler.getState().toString())) {
                log.info("id:{}, state:{}" + handler.getAppId(), handler.getState());
                Thread.sleep(60000);
            }
        } catch (Exception e) {
            log.error("+++++++++++++++++++ unionCustomCrowdInSubProcess failed +++++++++++++++++++", e);
            return false;
        }
        log.info("+++++++++++++++++++ unionCustomCrowdInSubProcess finished +++++++++++++++++++");
        return true;
    }

    /**
     * 创建一个新进程，spark 将兴趣pv日志依次按IMEI、IMEI_MD5、IDFA顺序join上自定义词包id。
     */
    private static void joinByHourInSubProcess(String curHour) throws IOException, InterruptedException {
        final String[] appArgs = new String[]{"--time", curHour};
        SparkAppHandle handler = new SparkLauncher()
                .setAppName("MergeDeviceCrowdTag-" + curHour)
                .setVerbose(true)
                .setJavaHome(javaHome)
                .setSparkHome(sparkHome)
                .setAppResource(appResource)
                .setMainClass(joinClass)
                .setPropertiesFile(propertiesFile)
                .setMaster("yarn")
                .setDeployMode("client")
                .setConf("spark.executor.instances", "4")
                .setConf(SparkLauncher.DRIVER_MEMORY, "4g")
                .setConf(SparkLauncher.EXECUTOR_MEMORY, "16g")
                .setConf(SparkLauncher.EXECUTOR_CORES, "4")
                .setConf("spark.network.timeout", "200")
                .addAppArgs(appArgs)
                .startApplication();

        while (!"FINISHED".equalsIgnoreCase(handler.getState().toString())
                && !"FAILED".equalsIgnoreCase(handler.getState().toString())) {
            log.info("id:{}, state:{}" + handler.getAppId(), handler.getState());
            Thread.sleep(60000);
        }
    }

    private static void parseArgs(String[] args) throws Exception {
        Options options = new Options();
        options.addOption("startTime", true, "开始时间，格式为yyyy/MM/dd/HH");
        options.addOption("endTime", true, "结束时间，格式为yyyy/MM/dd/HH");
        options.addOption("window", true, "需要重跑的pv天数");
        options.addOption("joinPoolSize", true, "重跑数据的spark任务并发数");
        options.addOption("crowdIgnoreDay", true, "人群包HDFS文件最后修改时间比当天时间早此天数不再重跑相应的词包数据");
        options.addOption("druidSchemaPath", true, "druid提交任务的task schema的文件路径");
        options.addOption("doRollup", true, "完成统计后是否需要提交rollup，粒度为天");
        options.addOption("javaHome", true, "子进程 spark 配置 JAVA_HOME");
        options.addOption("sparkHome", true, "子进程 spark 配置 SPARK_HOME");
        options.addOption("appResource", true, "子进程 spark 配置 appResource");
        options.addOption("joinClass", true, "子进程 spark 配置 joinClass");
        options.addOption("unionClass", true, "子进程 spark 配置 unionClass");
        options.addOption("propertiesFile", true, "子进程 spark 配置 propertiesFile");

        CommandLineParser parser = new PosixParser();
        CommandLine cmd = parser.parse(options, args);

        if (cmd.hasOption("window")) {
            joinWindow = Integer.parseInt(cmd.getOptionValue("window"));
        }
        if (cmd.hasOption("joinPoolSize")) {
            joinPoolSize = Integer.parseInt(cmd.getOptionValue("joinPoolSize"));
        }

        if (cmd.hasOption("startTime") && cmd.hasOption("endTime")) {
            runInDefaultInterval = false;
            startTime = LocalDateTime.parse(cmd.getOptionValue("startTime"), FORMATTER_HOUR);
            endTime = LocalDateTime.parse(cmd.getOptionValue("endTime"), FORMATTER_HOUR);
            if (startTime.isAfter(endTime)) {
                throw new Exception("start time should before end time.");
            }
        }

        if (cmd.hasOption("crowdIgnoreDay")) {
            crowdIgnoreDay = Integer.parseInt(cmd.getOptionValue("crowdIgnoreDay"));
        }

        if (cmd.hasOption("druidSchemaPath")) {
            druidSchemaPath = cmd.getOptionValue("druidSchemaPath");
        }
        if (cmd.hasOption("doRollup")) {
            doRollup = Boolean.parseBoolean(cmd.getOptionValue("doRollup"));
        }
        if (cmd.hasOption("javaHome")) {
            javaHome = cmd.getOptionValue("javaHome");
        }
        if (cmd.hasOption("sparkHome")) {
            sparkHome = cmd.getOptionValue("sparkHome");
        }
        if (cmd.hasOption("appResource")) {
            appResource = cmd.getOptionValue("appResource");
        }
        if (cmd.hasOption("joinClass")) {
            joinClass = cmd.getOptionValue("joinClass");
        }
        if (cmd.hasOption("unionClass")) {
            unionClass = cmd.getOptionValue("unionClass");
        }
        if (cmd.hasOption("propertiesFile")) {
            propertiesFile = cmd.getOptionValue("propertiesFile");
        }
    }

    @Data
    @AllArgsConstructor
    public static class DeviceCrowd implements Serializable {

        /**
         * 设备id。
         */
        private String deviceId;

        /**
         * 设备对应的所有的自定义人群词包id列表。
         */
        private List<Integer> crowdIds;
    }

    /**
     * 从商业库中查询出所有自定义人群词包数据。
     *
     * @return 自定义人群词包集合。
     */
    private static List<CrowdInfo> loadAllCrowdInfo() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource("******************************************************************", "eadonline4nb", "new1ife4Th1sAugust");
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        List<CrowdInfo> crowdInfoList = new ArrayList<>();
        jdbcTemplate.queryForList("SELECT ID, PATH, DEVICE_ID_TYPE FROM CustomAudiencePackage")
                .forEach(
                        row -> crowdInfoList.add(
                                new CrowdInfo(Integer.valueOf(row.get("ID").toString()), (String) row.get("PATH"),
                                        DeviceType.parse(((Long) row.get("DEVICE_ID_TYPE")).intValue()))
                        )
                );
        log.info("load crowd info total count:{}", crowdInfoList.size());
        for (CrowdInfo ci : crowdInfoList) {
            log.info("load crowd info: {}", ci);
        }

        return crowdInfoList;
    }

    private static void sendAlertSms(String number, String message) {
        try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet("http://sms.corp.yodao.com:8080/sms?phonenumber=" + number +
                    "&message=" + URLEncoder.encode(message, "UTF-8"));
            httpclient.execute(httpGet);
        } catch (Exception e) {
            log.error("send sms message({}) error", message, e);
        }
    }
}

@AllArgsConstructor
@NoArgsConstructor
@ToString
class CrowdInfo implements Serializable {

    /**
     * 自定义人群词包在业务库中的id。
     */
    @Getter
    private int id;

    /**
     * 自定义人群词包对应的设备id列表所在的文件路径。
     */
    @Getter
    private String filePath;

    /**
     * 设备id的类型。
     */
    @Getter
    private DeviceType deviceType;
}

enum DeviceType {
    /**
     * 设备imei号
     */
    IMEI,

    /**
     * 设备imei号MD5值，统一使用大写
     */
    IMEI_MD5,

    /**
     * iOS设备IDFA
     */
    IDFA,

    /**
     * iOS设备IDFA md5值。
     */
    IDFA_MD5,

    /**
     * 不应该出现。
     * 目前只支持按以上几种类型join。
     */
    UNKNOWN;

    public static DeviceType parse(int index) {
        switch (index) {
            case 0:
                return IMEI;
            case 1:
                return IMEI_MD5;
            case 2:
                return IDFA;
            case 3:
                return IDFA_MD5;
            default:
                return UNKNOWN;
        }
    }
}
