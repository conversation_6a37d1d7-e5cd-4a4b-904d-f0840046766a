package com.youdao.quipu.kafka.formatter;

import com.linkedin.camus.coders.CamusWrapper;
import com.linkedin.camus.etl.kafka.coders.JsonStringMessageDecoder;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageDecoder;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageEncoder;
import com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry;
import kafka.common.MessageFormatter;
import org.apache.avro.generic.GenericData;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.io.PrintStream;
import java.util.Arrays;
import java.util.Date;
import java.util.Properties;

/**
 * <AUTHOR>
 */
public class AvroMessageFormatter implements MessageFormatter {
    private String timestampField;
    private KafkaAvroMessageDecoder decoder;

    @Override
    public void init(Properties props) {
        String topic = props.getProperty("topic");
        String avroRepoUrl = props.getProperty("avro.repo.url");
        timestampField = props.getProperty("timestamp.field");
        decoder = new KafkaAvroMessageDecoder();
        Properties properties = new Properties();
        properties.put(KafkaAvroMessageEncoder.KAFKA_MESSAGE_CODER_SCHEMA_REGISTRY_CLASS,
                AvroRestSchemaRegistry.class.getCanonicalName());
        properties.put(AvroRestSchemaRegistry.ETL_SCHEMA_REGISTRY_URL, avroRepoUrl);
        if (timestampField != null) {
            properties.put(JsonStringMessageDecoder.CAMUS_MESSAGE_TIMESTAMP_FIELD, timestampField);
        }
        decoder.init(properties, topic);
    }

    @Override
    public void writeTo(ConsumerRecord<byte[], byte[]> consumerRecord, PrintStream output) {
        try {
            CamusWrapper<GenericData.Record> decode = decoder.decode(consumerRecord.value());
            if (timestampField != null) {
                output.println(new Date(decode.getTimestamp()) + "\t" + decode.getRecord().toString());
            } else {
                output.println(decode.getRecord().toString());
            }
        } catch (Exception e) {
            output.print("Error while decoding avro message, key:" + Arrays.toString(consumerRecord.key())
                    + ", raw value: " + Arrays.toString(consumerRecord.value()));
            e.printStackTrace(output);
        }
    }

    @Override
    public void close() {
    }
}
