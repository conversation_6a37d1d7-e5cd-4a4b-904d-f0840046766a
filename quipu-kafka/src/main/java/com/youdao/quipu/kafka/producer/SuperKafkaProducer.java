package com.youdao.quipu.kafka.producer;

import org.apache.avro.generic.IndexedRecord;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.util.Map;

/**
 * Created by zhaown on 15/12/12.
 */
public abstract class SuperKafkaProducer {

    private static final String KAFKA_CONFIG_PRODUCER_TYPE_UNWRAPPED = "super.producer.type";
    public static final String KAFKA_CONFIG_PRODUCER_TYPE_NOOP = "noop";

    public static SuperKafkaProducer getInstance(Map<String, String> properties) {
        if (KAFKA_CONFIG_PRODUCER_TYPE_NOOP.equalsIgnoreCase(properties.get(KAFKA_CONFIG_PRODUCER_TYPE_UNWRAPPED))) {
            return new NoopSuperKafkaProducer();
        } else {
            return AtLeastOnceKafkaProducer.getInstance(properties);
        }
    }

    public boolean send(String topic, String message) {
        return send(topic, null, message);
    }

    public abstract boolean send(String topic, String key, String message);

    public boolean send(String topic, byte[] message) {

        return send(topic, null, message);
    }

    public abstract boolean send(String topic, byte[] key, byte[] message);

    public boolean send(String topic, IndexedRecord message) {
        return send(topic, null, message);

    }

    public boolean send(String topic, byte[] key, IndexedRecord message) {
        return send(new ProducerRecord<byte[], IndexedRecord>(topic, key, message));
    }

    public abstract boolean send(ProducerRecord<byte[], IndexedRecord> record);
}

