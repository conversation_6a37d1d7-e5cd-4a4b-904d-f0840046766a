/*
 * @(#)LocalFileWriterWorkerThread.java, 2014-12-05 Copyright 2014 Youdao, Inc.
 *                                       All rights reserved. YOUDAO
 *                                       PROPRIETARY/CONFIDENTIAL. Use is
 *                                       subject to license terms.
 */
package com.youdao.quipu.kafka.producer;

import com.google.common.io.Files;
import com.yammer.metrics.core.Counter;
import com.yammer.metrics.core.Gauge;
import com.youdao.quipu.avro.schema.EncodedMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.avro.file.DataFileWriter;
import org.apache.avro.generic.IndexedRecord;
import org.apache.avro.io.DatumWriter;
import org.apache.avro.specific.SpecificDatumWriter;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.BlockingQueue;

import static com.yammer.metrics.Metrics.newCounter;
import static com.yammer.metrics.Metrics.newGauge;

/**
 * <AUTHOR>
 */
@Slf4j
public class LocalFileWriterWorkerThread extends WorkerThread<ProducerRecord<byte[], IndexedRecord>> {
    static final String LOCAL_FILE_DIRECTORY = "logs" + File.separator + "kafkaBackupAvro";
    static final String LOCAL_FILE_NAME = "kafkaProducerBackup";

    private final AvroSerde converter;
    private final Counter landedMessages;
    private final Counter serializedFailedMessages;
    private final Counter flushFailedTimes;
    private final Gauge<Long> localFileSizeGauge;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
    private final DatumWriter<EncodedMessage> datumWriter = new SpecificDatumWriter<EncodedMessage>(EncodedMessage.class);

    private DataFileWriter<EncodedMessage> localFileWriter = new DataFileWriter<EncodedMessage>(datumWriter);

    private long rollTime;

    private boolean currentLocalFileIsEmpty = true;

    public LocalFileWriterWorkerThread(String name, BlockingQueue<ProducerRecord<byte[], IndexedRecord>> queue,
                                       int numBricks, long timeout, AvroSerde converter) {
        super(name, queue, numBricks, timeout);
        this.converter = converter;
        landedMessages = newCounter(this.getClass(), "landedMessages");
        serializedFailedMessages = newCounter(this.getClass(), "serializedFailedMessages");
        flushFailedTimes = newCounter(this.getClass(), "flushFailedTimes");
        localFileSizeGauge = newGauge(this.getClass(), "localFileSizeGauge", new Gauge<Long>() {
            @Override
            public Long value() {
                if (FileUtils.getFile(LOCAL_FILE_DIRECTORY).exists()) {
                    return FileUtils.sizeOfDirectory(FileUtils.getFile(LOCAL_FILE_DIRECTORY));
                }
                return 0L;
            }
        });

    }

    @Override
    protected void moveBricks(List<ProducerRecord<byte[], IndexedRecord>> bricks) {
        checkRollup();
        if (bricks.size() > 0) {
            writeToLocal(bricks);
        }
    }

    @Override
    protected void moveRemainBricks(List<ProducerRecord<byte[], IndexedRecord>> bricks) {
        moveBricks(bricks);
    }

    @Override
    protected void close() {

    }

    private void checkRollup() {
        File localFile = FileUtils.getFile(LOCAL_FILE_DIRECTORY, LOCAL_FILE_NAME);
        // in the start of running
        if (rollTime == 0) {
            // with legacy
            if (localFile.exists()) {
                rollTime = getMinuteCeil(localFile.lastModified());
                if (System.currentTimeMillis() > rollTime) {
                    rollAndCreate();
                } else {
                    reopenLocalFile();
                }
            } else {
                // without legacy
                createLocalFile();
            }
        } else {
            // in the middle of running
            if (System.currentTimeMillis() > rollTime) {
                if (currentLocalFileIsEmpty) {
                    rollTime = getMinuteCeil(System.currentTimeMillis());
                } else {
                    IOUtils.closeQuietly(localFileWriter);
                    rollAndCreate();
                }
            }
        }
    }

    private void rollAndCreate() {
        rollLocalFile();
        createLocalFile();
    }

    private void rollLocalFile() {
        while (true) {
            log.info("Trying to roll up file: {}", LOCAL_FILE_DIRECTORY + File.separator + LOCAL_FILE_NAME);
            try {
                File fromFile = FileUtils.getFile(LOCAL_FILE_DIRECTORY, LOCAL_FILE_NAME);
                if (fromFile.exists()) {
                    File toFile = FileUtils.getFile(LOCAL_FILE_DIRECTORY, String.format("%s.%s", LOCAL_FILE_NAME,
                                    dateFormat.format(new Date(rollTime))));
                    while (toFile.exists()) {
                        sleep(1000);
                        toFile = FileUtils.getFile(LOCAL_FILE_DIRECTORY, String
                                .format("%s.%s", LOCAL_FILE_NAME, dateFormat.format(new Date(System.currentTimeMillis()))));
                    }
                    // fromFile.renameTo(toFile);
                    FileUtils.moveFile(fromFile, toFile);
                }
                return;
            } catch (IOException e) {
                log.error("IOException", e);
            } catch (InterruptedException ignored) {
            }
        }
    }

    private void flushLocalFileWithReopen(int size) {
        try {
            localFileWriter.flush();
            landedMessages.inc(size);
        } catch (Exception e) {
            flushFailedTimes.inc();
            IOUtils.closeQuietly(localFileWriter);
            reopenLocalFile();
            log.error("Flush local file error", e);
        }
    }

    private void createLocalFile() {
        try {
            rollTime = getMinuteCeil(System.currentTimeMillis());
            File file = FileUtils.getFile(LOCAL_FILE_DIRECTORY, LOCAL_FILE_NAME);
            Files.createParentDirs(file);
            localFileWriter.create(EncodedMessage.getClassSchema(), file);
            currentLocalFileIsEmpty = true;
        } catch (IOException e) {
            log.error("create local file error", e);
        }
    }

    private void reopenLocalFile() {
        try {
            localFileWriter.appendTo(FileUtils.getFile(LOCAL_FILE_DIRECTORY, LOCAL_FILE_NAME));
            currentLocalFileIsEmpty = false;
        } catch (IOException e) {
            log.error("reopen local file error", e);
        }
    }

    private long getMinuteCeil(long time) {
        return DateUtils.ceiling(new Date(time), Calendar.MINUTE).getTime();
    }

    private void writeToLocal(List<ProducerRecord<byte[], IndexedRecord>> bricks) {
        int serializedFailedCounter = 0;
        for (ProducerRecord<byte[], IndexedRecord> brick : bricks) {
            try {
                EncodedMessage encodedMessage;
                if (brick.value() instanceof EncodedMessage) {
                    encodedMessage = (EncodedMessage) brick.value();
                } else {
                    ProducerRecord<byte[], byte[]> message = converter.getSerializedFunction().apply(brick);
                    Schema.Field timestampField = brick.value().getSchema().getField(KafkaSenderWorkerThread.TIMESTAMP_FIELD_NAME);
                    long timestamp = 0;
                    if (timestampField != null) {
                        Object timestampObject = brick.value().get(timestampField.pos());
                        if (timestampObject instanceof Long) {
                            timestamp = (Long) timestampObject;
                        }
                    }
                    encodedMessage = new EncodedMessage(message.topic(), message.key() == null ? null : ByteBuffer
                            .wrap(message.key()), timestamp, ByteBuffer.wrap(message.value()));
                }
                localFileWriter.append(encodedMessage);
                currentLocalFileIsEmpty = false;
            } catch (Exception e) {
                serializedFailedCounter++;
                log.error("write to local error", e);
            }
        }
        serializedFailedMessages.inc(serializedFailedCounter);
        flushLocalFileWithReopen(bricks.size() - serializedFailedCounter);
    }
}
