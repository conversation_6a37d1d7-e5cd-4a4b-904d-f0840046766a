# 1.290.0
* [jmxtrans]
    - gorgon-tj新增实时接口v2的监控配置

# 1.284.0
* [jmxtrans]
    - bid-server，yex，gorgon贵州集群部署服务的监控

# 1.283.0
* [eadata-druid]
- sdk_tdp_together统计表里增加第三方的“激活日付费数”指标
- [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6319)

# 1.282.0
* [eadata-druid]
- sdk_stat 表增加激活数、注册数、购买事件数、自定义事件数等指标
- [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6277)

# 1.281.0
* [eadata-druid]
- sdk_stat 实时流程添加 package_name

# 1.280.0
* [eadata-druid]
- sdk_stat 表增加转化触点时间差维度

# 1.279.0
* [eadata-druid]
- 添加 task.warmingPeriod 防止 overlord压力大时 tranqulity 1min 之类无法将数据发送到 druid 面 drop 数据
- sdk_stat_v2 表加大并行度和延长窗口时间，防止 pv_sdk lag 过大
- sdk_stat_v2 添加 tranquility.lingerMillis 防止小流量的数据不能及时发出而drop

# 1.278.0
* [eadata-druid]
- YEX 表去掉统计字段: province_name, city_name，app_version

# 1.277.0
* [eadata-druid]
- interest_stat 批量流程添加 package_name
- sdk_stat 批量流程添加 package_name
- bid_failed_stat 实时流程添加 package_name

# 1.276.0
* [druid]
  - sdk_stat/sdk_stat_v2/third_party_stat/third_party_stat_v2增加激活付费时间差

# 1.275.0
* [druid]
  - yex 添加响应时间指标为向上取整时间
  - [jira](https://jira.inner.youdao.com/browse/YEX-481)

# 1.274.0
* [druid]
  - gorgon协议支持传包名作为扩展参数
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6219)

# 1.273.0
* [jmxtrans]
  - bid-dispatcher 增加sigmob adx的监控

# 1.272.0
* [druid]
  - third_party_stat表新增反作弊设备清洗池相关指标。

# 1.271.0
* [druid]
  - interest_stat / interest_stat_updated / interest_stat_update_rollup 增加手机品牌维度(mobile_brand)

# 1.270.0
* [druid]
  - 修改海外短链 UV 统计规则

# 1.269.1
* [druid]
  - third_party_stat表补充四期增加的反作弊指标 v2

# 1.269.0
* [druid]
  - third_party_stat表补充四期增加的反作弊指标

# 1.268.0
* [jmxtrans]
  - 添加consumer-click的http-timer的监控

# 1.268.0
* [druid]
  - third_party_stat表补充三期增加的反作弊指标

# 1.268.0
* [druid]
  - sdk_tdp_together表增加算法召回实验相关参数，

# 1.267.1
* [jmxtrans]
  - lenovo的bs添加一个pod的监控

# 1.267.0
* [jmxtrans]
  - 添加gorgon-vender-gz和yex-vender-gz的监控

# 1.266.0
* [jmxtrans]
  - 添加lenovo的bd和bs监控

# 1.265.0
* [jmxtrans]
  - gorgon监控更新，增加品牌广告生产延迟指标

# 1.264.0
* [druid]
  - third_party_stat/third_party_stat_v2/sdk_stat/sdk_stat_v2增加当日付费和24小时内付费指标.

# 1.263.0
* [druid]
  - third_party_stat、sdk_stat 增加media_type、slot_style_type统计维度
* [jmxtrans]
  - bs添加rta广告位请求监控 

# 1.262.3
* [druid]
  - third_party_stat 批量统计内存调大，
  - sdk_tdp_together_loc_conv 批量统计优先级调高，

# 1.262.2
* [druid]
  - third_party_stat 去掉channel_did维度，这个是转化加速器在使用的字段，维度值太大了不在这个表统计了

# 1.262.1
* [druid]
  - sdk_tdp_together 批量统计调大reduce内存
  
# 1.262.0
* [druid]
  - sdk_tdp_together 添加算法分桶id
  
# 1.261.0
* [druid]
  - sdk_stat 删除没用到的批量统计_rollup配置文件，避免歧义，rollup任务用的配置文件和普通的统计任务是同一个配置文件

# 1.261.0
* [druid]
  - sdk_stat实时统计,批量统计添加统计去重展示量

# 1.260.0
* [jmxtrans]
  - 添加topon，taptap的bd和bs监控

# 1.259.0
* [druid]
  - 增加统计表：sdk_tdp_together_loc_conv

# 1.258.0
* [druid]
  - ead_dmp_stat 增加个推数据和广告商转化数据标签维度

# 1.257.0
* [jmxtrans]
  - 修改生产者监控host

# 1.256.0
* [druid]
  - oimage_stat 添加场景相关维度

# 1.255.0
* [jmxtrans]
  - 增加bs监控指标

# 1.254.0
* [druid]
  - sdk_stat 增加国家统计维度

# 1.254.0
* [druid]
  - third_party_stat 增加省市统计维度

# 1.253.0
* [druid]
  - third_party_stat 增加流量监控

# 1.252.0
* [druid]
  - oimage_stat 添加转化相关维度和指标

# 1.251.0
* [druid]
  - interest_stat 添加新杭研数据定向维度

# 1.250.0
* [jmxtrans]
  - add gorgon-staging-tj jmx host

# 1.249.0
* [druid]
  - sdk_stat_v2 添加过耗点击
  - sdk_tdp_together 添加过耗渠道did

# 1.248.0
* [druid]
  - brand_stat_v2 添加 status 维度
  - 实时统计中如果需要使用维度过滤出特定的指标，需要将维度添加到维度列表中

# 1.247.0
* [druid]
  - third_party_stat 统计 channel_did 填充数

# 1.246.0
* [druid]
  - third_party_stat 增加 channel_did 唯一标识维度

# 1.245.0
* [druid]
  - bid_failed_stat 增加 bs_host 维度

# 1.244.0
* [druid]
  - sdk_stat、third_party_stat 增加retention_days等字段

# 1.243.0
* [jmxtrans]
  - 添加消费brand_pv的监控

# 1.242.1
* [druid]
  - 修复 https://confluence.inner.youdao.com/pages/viewpage.action?pageId=254062677 实时统计schema 中没有添加相应 topic 的问题

# 1.242.0
* [druid]
  - yex 统计表增加竞价响应安卓七要素无效数、竞价响应快应用广告无效数

# 1.241.0
* [druid]
  - brand统计表增加 country 维度字段。

# 1.240.0
* [jmxtrans]
  - 增加ergate-stage 监控地址

# 1.239.0
* [druid]
  - 修改sdk_stat 增加曝光归因统计字段

# 1.238.0
* [jmxtrans]
  - 修改bid-server 监控地址
  
# 1.237.0
* [druid]
  - brand stat 统计 cpm 指标

# 1.236.0
* [druid]
    - yex 统计海外 DSP 原始币种价格

# 1.235.0
* [jmxtrans]
  - 添加新加坡阿里云 gorgon、yex 服务监控

# 1.234.0
* [jmxtrans]
  - 更新third-party-reporter的jmx监控配置

# 1.233.1
* [druid]
  - click_time_conv 统计表添加 strategy_id。

# 1.233.0
* [druid]
  - rocket_gz service3/4/5 节点使用了 3T和 新加的 700G 两块 SSD, 但目前的分配策略是优先使用剩余空间大的磁盘，导致新加的 700G 磁盘空间无法被利用。
  目前旧的 3T 盘已经使用 2T,所以优化下配置，使其使用上新加的 700G 磁盘，加大磁盘并发性能。

# 1.232.0
* [druid]
  - 统计表 sdk_stat 添加 strategy_id 维度
  - bid_failed_stat 添加 filter_sub_reason,app_id 维度

# 1.232.0
* [druid]
  - 新增统计表 portrait_by_ead_dmp 批量摄入数据的配置

# 1.231.0
* [druid]
  - third-party-stat 增加filter_no_bid_conv 字段

# 1.230.0
* [druid]
  - yex/bs新增gorgon_interface_type字段，用于标明流量来源于gorgon哪个接口
  
# 1.229.0
* [druid]
  - third_party_stat 增加 os、device_model 维度
  - third_party_stat 增加 android_click、ios_click、android_id_filled 指标
  
# 1.228.0
* [druid]
  - 渠道点击增加一系列流量质量相关指标

# 1.227.0
* [druid]
  - 添加dmp统计表定义

# 1.226.0
* [druid]
  - bs 竞价失败日志增加slot_id 维度

# 1.225.0
* [jmxtrans]
  - 添加贵州 yex-tracker 和 yex-noticer 监控

# 1.224.0
* [jmxtrans]
  - 添加天津 bs-oppo 监控

# 1.223.0
* [jmxtrans]
  - bd 监控添加传媒
  - 天津 jmxtrans 添加传媒 bd 监控

# 1.222.0
* [jmxtrans]
  - 记录天津的监控配置，（实际上天津的没有用mfs，更新到/mfs_ead/eadata/ead-conf目录也不会对天津的jmxtrans生效了）
  - 修改ergate和bs-impr的监控

# 1.221.0
* [jmxtrans]
  - 新增gorgon 实时接口v2的监控
  - 品牌统计新增interface_type字段，用于标明流量来源于gorgon哪个接口

# 1.220.0
* [druid]
  - yex 统计增加 dsp_ad_type 维度
  - jira：https://jira.inner.youdao.com/browse/YEX-433

# 1.219.0
* [jmxtrans]
  - 修改click-third-party监控host

# 1.218.0
* [jmxtrans]
  - 修改antifrauder监控host

# 1.217.0
* [druid]
  - druid批量任务新增统计表brand_feature_stat，用于记录品牌广告pv中的定向信息

# 1.216.0
* [druid]
  - 修改 tranquility kafka 配置，配合 tranquility kafka consumer api 升级

# 1.215.0
* [jmxtrans]
  - 修改click-consumer监控host

# 1.214.0
* [druid]
  - yex_stat 增加广告商 ID 维度

# 1.213.0
* [druid]
  - third_party_stat 拆分rta实时统计任务

# 1.212.0
* [druid]
  - third_party_stat 统计重复点击数
  - jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5405

# 1.211.0
* [druid]
  - yex添加bundle dimension

# 1.210.0
* [jmx-trans]
- fix third reporter host

# 1.209.0
* [druid]
  - [YEX素材转换器](https://jira.inner.youdao.com/browse/YEX-417)

# 1.208.0
* [druid]
  - 更新sdk_tdp_together，增加tdp转化设备数统计字段配置

# 1.207.0
* [druid]
- 提交druid lookup 更新脚本，修改mysql长域名

# 1.206.0
* [jmxtrans]
- brand-tracker监控新增zw节点

# 1.205.0
* [druid]
  - 迁移 tranquility kafka consumer offset 到 kafka 上，方便 tranquility kafka consumer api 升级

# 1.204.0
* [druid]
    - yex_request新增指标

# 1.203.0
* [druid]
  - 更新sdk_tdp_together，增加媒体包id的统计字段配置

# 1.202.0
* [jmxtrans]
- gorgon增加频控相关监控指标device-frequency-get-timer、brand-impr-executor-reject-meter

# 1.201.0
* [jmxtrans]
- third-party-reporter 增加智选转化加速器策略上报的点击监控

# 1.200.0
* [jmxtrans]
    - k8s-zw添加yoyo的bd和bs监控

# 1.199.0
* [druid]
  - 更新sdk_tdp_together，增加统计字段配置

# 1.198.0
* [druid]
  - 更新sdk_stat、third_party_stat 等统计配置

# 1.197.0
* [jmxtrans]
- 新增brand-tracker的监控指标抓取

# 1.196.0
* [druid]
  - /home目录迁移至/mfs_ead/home

# 1.195.1
* [kafka-lag-exporter]
- 添加配置reyun_record的consumer offset lag监控

# 1.195.0
* [druid]
- yex表新增品牌程序化相关维度brand_orientation_type、ad_group_id

# 1.194.1
* [druid]
- 表portrait_analysis uv cardinality -> hyperUnique
- 表 reyun_record、sdk_tdp_together 存储分段条件修改

# 1.194.0
* [jmxtrans]
- 更新consumer-third-party 监控

# 1.193.0
* [druid]
- 新增表portrait_analysis

# 1.192.0
* [druid]
 - third-party-stat tranqulity partition 修改为5

# 1.191.0
* [jmxtrans]
 - third-party-reporter 增加stage监控

# 1.190.0
* [druid]
 - 新增表sdk_tdp_together

# 1.189.0
* [druid]
 - third-party-stat 增加无效点击数统计

# 1.188.0
* [jmxtrans]
  - third-party-reporter 监控：增加redis监控

# 1.187.0
* [druid]
  - 拆分loc_conv_third_party_avro数据源，单独存储到third_party_stat_loc_conv
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3176?filter=-1)
  - [文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=277781112)

# 1.186.0
* [druid]
  - yex 新增app_version维度。

# 1.185.0
* [druid]
  - third-party-stat 增加rta相关统计字段

# 1.184.0
* [jmxtrans]
  - 更新third-party-reporter 监控：增加oppo点击上报监控指标

# 1.183.0
* [jmxtrans]
  - 更新third-party-reporter 监控：增加wifi点击上报监控指标

# 1.182.0
* [jmxtrans]
  - bs中的reyun监控指标修改

# 1.181.0
* [jmxtrans]
  - kafka-portrait-venus 服务增加 RTA 结果缓存的监控

# 1.180.0
* [jmxtrans]
  - bs和kafka-portrait-venus服务增加热云相关的监控。
* [druid]
  - 新增reyun_record表，实时和批量摄入任务schema。

# 1.179.0
* [jmxtrans]
  - 补充third-party-reporter:zjy,zw的jmx配置文件
  
# 1.178.0
* [druid]
  - brand_stat新增mappingPosId维度，yex_stat新增brand_source_slot_id字段

# 1.177.0
* [jmxtrans]
  - 增加generate-caid-blank-meter，generate-caid-timer指标
  
# 1.176.0
* [druid]
  - sdk_stat 新增 adx 出价价格、adx 竞胜数等统计指标

# 1.175.0
* [jmxtrans]
  - 移除物理机上或者已下线机器上的的bs-ng/bs-ng-tcpcopy/consumer-third-click/mediation-sdk/mkt-2-db/producer/video-callback/witake-kol-platform配置。

# 1.174.0
* [jmxtrans]
  - 移除物理机上的gorgon/yex/yex-tracker/yex-winnoticer/bs-ng的配置。

# 1.173.0
* [druid]
  - 增加brand_abnor_url_stat

# 1.172.0
* [druid]
  - yex 增加 dsp 在 adx 的参竞价格统计指标

# 1.171.0
* [jmxtrans]
  - 增加consumer-click timer指标

# 1.170.0
* [druid]
  - sdk_stat 新增点击率统计指标

# 1.169.1
* [jmxtrans]
  - gorgon补充对gorgon -> yex更长超时时间的连接池监控指标
  
# 1.169.0
* [jmxtrans]
  - gorgon新增对gorgon -> yex更长超时时间的连接池监控指标

# 1.168.0
* [jmxtrans]
  - bs-xiaomi 监控去除，bs-oppo 添加监控实例

# 1.167.0
* [druid]
  - yex 增加 adx 竞价数、竞胜数统计
  - jira：https://jira.inner.youdao.com/browse/YEX-335

# 1.166.0
* [druid]
  - 添加统计 yex_invalid_traffic 表

# 1.165.0
* [jmxtrans]
  - gorgon 监控更新：对开启了二次品牌广告提名的广告位增加接口耗时监控

# 1.164.0
* [jmxtrans]
  - 更新 consumer-third-party 服务监控
  
* [kafka-lag-exporter]
  - 增加click_third_party lag 监控

# 1.163.0
* [druid]
  - 添加统计 failed_send_click_stat 表

# 1.162.0
* [druid]
  - 添加事件上报
  - [jira](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=254062677)

# 1.161.0

* [jmxtrans]
  - Thunder迁移机器从th017/th018 --> zj044/zj045

# 1.160.0

* [jmxtrans]
  - yex-tracker & yex-noticer 迁移到容器上，将监控配置到容器上

# 1.159.2
* [druid]
  - flatten yex ivt metrics

# 1.159.1
* [druid]
  - fix yex ivt stat

# 1.159.0
* [kafka-lag-exporter]
  - 迁移到 ti057

# 1.158.0
* [druid]
  - yex 增加异常流量标签统计

# 1.157.0
* [druid]
  - 新增 bid_failed_stat 统计竞价失败原因。
  
# 1.157.0
* [druid]
  - [新增点击互动类型维度](https://jira.inner.youdao.com/browse/YOUXUAN-1113)
  
# 1.156.0
* [jmxtrans]
  - 更新third-party-reporter 监控：增加广点通点击上报监控指标

# 1.155.0
* [druid]
  - [V1.0-流量质量数据分析和管理看板](https://jira.inner.youdao.com/browse/YEX-301)
  - 指标修正

# 1.54.0
* [jmxtrans]
  - bs 添加针对具体 QueryAnalyzer 的 timer 指标

# 1.153.0
* [druid]
  - 优化 Druid 批量统计YARN资源使用量。

# 1.152.0
* [jmxtrans]
  - gorgon 监控更新

# 1.151.0
* [druid]
  - [V1.0-流量质量数据分析和管理看板](https://jira.inner.youdao.com/browse/YEX-301)

# 1.150.0
* [jmxtrans]
  - gorgon 监控更新

# 1.149.0
* [druid]
  - 新增海外归因平台AF对接的聚合统计数据schema

# 1.148.0
* [jmxtrans]
  - 新增容器云音乐 bd 监控

# 1.147.1
* [jmxtrans]
  - oppo bs 添加实例

# 1.147.0
* [kafka-lag-exporter]
  - 更新third-party-reporter 监控

# 1.146.0
* [kafka-lag-exporter]
  - click-consumer topic lag 报警配置
  
# 1.145.0
* [jmxtrans]
  - 小米 bs 容器监控配置

# 1.144.0
* [Druid]
  - 新增历史 default 节点 zj300。
  
# 1.143.0
* [jmxtrans]
  - 新增 bd 操作系统 cpu 相关监控信息

# 1.142.1
* [jmxtrans]
  - 新增小米 bs 监控机器

# 1.142.0
* [jmxtrans]
  - BS添加远程DNN_v2的推理监控
# 1.141.0
* [jmxtrans]
  - 更新gorgon 设备库redis 监控

# 1.140.0
* [jmxtrans]
  - 更新third-party-reporter 监控

# 1.139.0
* [jmxtrans]
  - 新增 bs jmx 监控机器
  - 新增 oppo bd jmx 监控

# 1.138.0
* [jmxtrans]
  - 更新Thunder Kafka消费者吞吐量指标。

# 1.137.0
* [druid]
  - 删除旧有的小Druid集群配置。

# 1.136.0
* [jmxtrans]
  - k8s-zw:bid-dispatcher 新增 bd 在容器上的监控

# 1.135.2
* [jmxtrans]
  - 更新third-party-reporter 监控

# 1.135.1
* [jmxtrans]
  - k8s-zjy:bs-impr 新建k8s集群上的bs-impr，ergate配置文件

# 1.135.0
* [jmxtrans]
  - k8s-zw:bs-impr 新建k8s集群上的bs-impr，ergate配置文件

# 1.134.1
* [jmxtrans]
  - third-party-reporter 添加convClickMeter指标
  
# 1.134.0
* [jmxtrans]
  - 更新third-party-reporter 监控

# 1.133.0
* [jmxtrans]
  - vendor服务新增实例

# 1.132.0
* [jmxtrans]
  - bs-impr和ergate支持监控localFileSizeGauge指标

# 1.131.0
* [jmxtrans]
  - 容器上的yex和bs支持监控localFileSizeGauge指标
  
# 1.131.0
* [druid]
  - 将`realtime_index_yex_stat-yex_bid_response_flat.json`拆分为两组tranquility，分别负责`yex_bid_request_flat`和`yex_bid_response_flat`

# 1.130.3
* [druid]
  - 海外短链访问 新增 create_date 维度, 例如: 20220906

# 1.130.2
* [druid]
  - 海外短链访问 新增 creator_id 维度

# 1.130.1
* [druid]
  - sales_activity_event_log 新增 uv 统计指标

# 1.130.0
* [druid]
  - 新增海外短链访问的统计schema

# 1.129.0
* [jmxtrans]
  - 新增BrandAdScheduleControllerV2的监控timer

# 1.128.0
* [druid]
  - 新增效果营销活动 H5 页面埋点数据批量统计 schema

# 1.127.2
* [jmxtrans]
  - 移除容器化的机器
  - zj053 zj054 zj299 zw235 zw236 zj297 zj298

# 1.127.1
* [jmxtrans]
  - 新增bs/yex本地kafka缓存文件大小监控
  
# 1.127.1
* [jmxtrans]
  - 修改 tfs 可用情况 metrics domain

# 1.127.0
* [jmxtrans]
  - bs 新增 tfs 可用情况的统计

# 1.126.0
* [druid]
  - bugfix：NumberFormatException，修改词典社区日志帖子统计数据schema，过滤掉空字符串的数据。

# 1.126.0
* [druid]
  - 创建词典社区日志帖子统计数据schema，用于abtests数据做统计视图。
  - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-18)

# 1.125.0
* [jmxtrans]
  - vg服务 从 zj054 迁移到 ns001
# 1.125.0
* [jmxtrans]
  - vg服务 从 zj297 zj298 迁移到 zj053 zj054
# 1.124.0
* [jmxtrans]
  - 添加gorgon yex pool的监控
# 1.123.0
* [jmxtrans]
  - 添加kafka-portrait-venus wow-outlog-label和wow-outlog-stat的监控

# 1.123.0
* [druid]
  - 修改批量、实时统计的schema。[jira](https://jira.inner.youdao.com/browse/YEX-255)
* [jmxtrans]
  - 增加bid-dispatch的server
  
# 1.122.0
* [eadata]
  - 批量和实时统计新增gorgon host字段的统计

# 1.121.0
* [kafka-lag-exporter]
  - 新增 kafka consumer lag 的监控
  - 新增对 yex winnotice consumer lag 的监控

# 1.120.0
* [jmxtrans]
  - 增加对阿里 Rta 的服务监控
  - 删除淘宝相关的监控配置

# 1.110.0
* [jmxtrans]
  - bs 新增流量广告位样式映射失败次数监控

# 1.109.0
* [jmxtrans]
  - yex 新增竞败上报监控
  - gorgon 中下掉 ns002 和 ns003

#1.108.0
* [eadata]
 - 批量和实时统计新增小程序原始id字段的统计

# 1.107.0
* [jmxtrans]
 - BS添加远程DNN推理监控

# 1.106.0
* [jmxtrans]
  - BS 添加 Thunder 同步数据监控。

# 1.105.0
* [jmxtrans]
  - 添加kafka-portrait-venus(luna标签监控)
  
# 1.104.0
* [eadata-druid]
  - 增加对yex-bid中竞价响应无效原因的统计
  - 增加对yex-bid中deal_id的统计

# 1.103.0
* [jmxtrans]
  - 添加 witake-kol-platform 监控

# 1.102.0
* [jmxtrans]
  - 删除eadd-impr的监控

# 1.101.0
* [jmxtrans]
  - 添加 third-party-reporter 监控
  - 添加 consumer-third-click 监控

# 1.100.0
* [jmxtrans]
  - gorgon添加zj300。

# 1.99.0
* [jmxtrans]
  - 增加对mkt-canal-2-db的监控

# 1.98.0
* [eadata-druid]
  - [hadoop_index_mediation_sdk_report.json](eadata/druid/online/batch-ingestion-schema/hadoop_index_mediation_sdk_report.json)添加内存，防止 OOM。

# 1.97.0
* [jmxtrans]
  - jmxtrans从 ns机器迁移到其它机器.

# 1.96.1
* [jmxtrans]
  - fix: luna组词典标签查询监控count的bug修复

# 1.96.0
* [jmxtrans]
  - 增加luna组词典标签查询监控。
  
# 1.95.0
* [jmxtrans]
  - BS 从 ns003迁移到 ns005以减轻 ns003压力。

# 1.94.0
* [jmxtrans]
  - 添加淘宝访问限制监控

# 1.93.0
* [jmxtrans]
  - 添加淘宝 cache 命中监控

# 1.92.0
* [eadata-druid]
  - historical ws004/ws005/ws008日志按小时切割，最大保留2天或300G日志。

# 1.91.0
* [jmxtrans]
  - 移除 bs hypers 和 reyun 监控

# 1.90.0
* [jmxtrans]
  - 增加聚合sdk新实例zj043监控；

# 1.89.0
* [eadata-druid]
  - [聚合SDK 1.3.2版本打点需求](http://confluence.inner.youdao.com/pages/viewpage.action?pageId=22009911) 。

# 1.88.0
* [jmxtrans]
  - 增加精品课出勤数据消费监控

# 1.87.0
* [jmxtrans]
  - yex监控添加对头条竞价失败上报的监控
  
# 1.86.0
* [jmxtrans]
  - 干掉oimage服务的jmx抓取配置，改由服务直接上报给graphite

# 1.85.0
* [eadata-druid]
  - 添加 third_party_stat 表 增加更多维度和指标

# 1.84.0
* [eadata-druid]
  - 添加 course_stat 表 统计精品课外部投放情况

# 1.83.0
* [eadata-druid]
  - Druid Peon降低参数processing.buffer.sizeBytes / processing.numThreads值。
  - sdk_stat实时统计为防止Peon进程FullGC导致task失败且无法终止，降低maxRowsInMemory。
  - sdk_stat实时统计扩大Kafka consumer数量到50且多部署一台tranquility加速消费pv_sdk。
  
# 1.82.0
* [jmxtrans]
  - 增加精品课RTA的监控

# 1.81.0
* [jmxtrans]
  - 增加精品课外部监测服务的监控

# 1.80.0
* [jmxtrans]
  - bs 增加 rta 接口访问延迟监控

# 1.79.0
* [jmxtrans]
  - bs 增加热云前置机访问监控

# 1.78.0
* [jmxtrans]
  - 增加精品课浏览、订单数据消费监控

# 1.77.0
* [eadata-druid]
  - druid增加tiny_url_visit表统计链接访问次数 
  
# 1.76.0
* [eadata-druid]
  - yex表增加adx_impr_price查询列用于查询adx对yex的扣费
  
# 1.75.0
* [jmxtrans]
  - 干掉不用的avro-sdk-conv-click-join.json和kafka-extractor.json

# 1.74.0
* [jmxtrans]
  - ns006有点儿不堪重负，在zj040新增jmxtrans，迁移bid-dispatcher和mediation-sdk的抓取配置；

# 1.73.1
* [jmxtrans]
  - fix 杭研标签名称

# 1.73.0
* [jmxtrans]
  - bs 增加 taobao api 询问指标
  - 增加淘宝标签请求服务监控 
  - 杭研标签机器调整

# 1.72.0
* [eadata-druid]
  - yex表查询增加hostname作为查询维度

# 1.71.0
* [jmxtrans]
  - 抓取gorgon请求yex http耗时

# 1.70.0
* [eadata-druid]
  - sdk_stat实时统计作弊点击和作弊消费

# 1.69.0
* [jmxtrans]
  - bd th025 取代 th017
  - bs 增加 hyper 询问指标
  - 增加杭研标签请求服务监控

# 1.68.1
* [eadata-druid]
  - tranquility使用的druid不支持druid-histogram，聚合sdk实时配置
  不再使用approxHistogramFold，只给批量任务加上approxHistogramFold；

# 1.68.0
* [jmxtrans]
  - 干掉universe-parser服务的jmx抓取配置
  
# 1.67.0
* [eadata-druid]
  - 增加聚合sdk缓存池版本统计；
  - 修正聚合sdk的统计维度，duration应该作为metric histogram存在；
  - 修改mediation-sdk-report落地hdfs时申请的yarn container内存；

# 1.66.0
* [eadata-druid]
  - 为yex_flat添加实时job配置

# 1.65.0
* [jmxtrans]
  - 取消抓取hdfs-monitor jmx数据，改由项目自己直接发送到graphite
  
# 1.64.0
* [jmxtrans]
  - 抓取oImage相关信息
  
# 1.63.0
* [jxmtrans]
  - Add yexTimeoutMeter for bid-dispatcher;

# 1.62.0
* [eadata-druid]
  - 统计当日转化数、转化成本。

# 1.61.1
* [eadata-druid]
  - 修正in_flight_spend中bid_predict_cost对应的fieldName

# 1.61.0
* [eadata-druid]
  - 竞价实际消费单独拎出来一个datasource

# 1.60.0
* [eadata-druid]
  - sdk_stat统计竞价时间为时间戳的实际消费

# 1.59.0
* [eadata-druid]
  - sdk_stat统计竞价预估消费

# 1.58.1
* [eadata-druid]
  - sdk_stat统计修复计费点击和丢弃点击的竞价平均延迟

# 1.58.0
* [jmxtrans]
  - hadoop nn ws031 -> zj066

# 1.57.0
* [eadata-druid]
  - sdk_stat统计 计费点击和丢弃点击的竞价平均延迟

# 1.56.0
* [eadata-druid]
  - sdk_stat和sdk_stat_v2统计转化订单金额字段

# 1.55.0
* [eadata-druid]
  - sdk_stat添加template_id维度

# 1.54.0
* [jmxtrans]
  - bs增加读取venus时间监控
  
# 1.53.0
* [jmxtrans]
  - 修复 FFM CTR/CVR 预测时间监控

# 1.51.0
* [jmxtrans]
  - Hadoop nn服务从ws030迁移到zj065 zj066。
  - Hbase rs下线ws030
* [eadata-druid]
  - realtime_index_dsp_stat_v2和realtime_index_sdk_stat_v2消费线程数从8 --> 24

# 1.52.0
* [eadata-druid]
  - 拆分dsp_stat_v2和sdk_stat_v2中的计费相关topic

# 1.51.0
* [jmxtrans]
  - HBase master服务迁移到zj065 zj066。

# 1.50.1
* [eadata-druid]
  - 使用新版的HLLSketchBuild需要加载druid-datasketches拓展，
    tranquility加载0.10.1版druid的该拓展有bug，弃用HLLSketchBuild，使用旧版cardinality；

# 1.50.0
* [eadata-druid]
  - 聚合sdk删除设备号维度，添加设备号的cardinality；

# 1.49.0
* [eadata-druid]
  - Tranquility添加elector_ranking重启脚本。
  - Tranquility升级到0.8.4。

# 1.48.0
* [jmxtrans]
  - eadd服务迁移到ns001-ns006

# 1.47.0
* [jmxtrans]
  - viper监控迁移至zj044
  
# 1.46.0

* [eadata-druid]
  - 添加广告位排序情况统计表 elector_ranking_stat；

# 1.45.0
* [jmxtrans]
  - bs-ng 增加 electorRankingMeter 监控广告位排序记录数量;
  - bs-ng-tcpcopy 更新;

# 1.44.0
* [jmxtrans]
  - bid-dispatcher hs044迁移到zj044；

# 1.44.0
* [eadata-druid]
  - 添加聚合sdk上报信息统计表mediation_sdk_report；

# 1.43.0
* [jmxtrans]
  - 数据服务层DataCache/Producer/ReadProxy监控数据抓取。

# 1.42.0
* [eadata-druid]
  - interest_stat 全部落地后再更新

# 1.41.0
* [eadata-druid]
  - yex表添加ssids

# 1.40.1
* [jmxtrans]
  - 新增一个jmxtrans实例到ns005上

# 1.40.0
* [jmxtrans]
  - click、impr consumer从qt、hs迁移到zj
  
# 1.39.0
* [jmxtrans]
  - 抓取thunder/canal数据。
  
# 1.38.0
* [jmxtrans]
  - 抓取hbase jmx数据

# 1.37.0
* [jmxtrans]
  - 更新hdfs-monitor jmx抓取数据
  
# 1.36.0
* [jmxtrans]
  - 抓取hadoop rm/dm/dn/nn 数据

# 1.35.0
* [eadata-druid]
  - 历史节点添加zj061-zj063实例。

# 1.34.0
* [jmxtrans]
  - 抓取hdfs-monitor jmx数据

# 1.33.0
* [eadata-druid]
  - yex表添加样式id维度。

# 1.32.0
* [jmxtrans]
  - 聚合sdk抓取基本请求指标；

# 1.31.0
* [jmxtrans]
  - 更新universe-parser服务的抓取地址
  - 更新druid配置

# 1.30.0
* [jmxtrans]
  - 抓取展示bs的相关指标

# 1.29.0
* [jmxtrans]
  - 竞价bs从hs迁移到zj机群

# 1.28.0
* [jmxtrans]
  - 更新转化点击JOIN服务的抓取的数据

# 1.27.0
* [jmxtrans]
  - 抓取thunder jmx数据。
* [eadata-druid]
  - sdk_stat实时数据统计丢弃点击、消费。
  - historical日志保留3天。
  
# 1.26.0
* [jmxtrans]
  - eadd 展示切换到新机器，并添加resin指标

# 1.25.0
* [eadata-druid]
  - tranqulity为YEX表添加更多实例以减少dropmessagecount。

# 1.24.0
* [jmxtrans]
  - 抓取转化点击JOIN服务数据

# 1.23.0
* [jmxtrans]
  - 添加eadata hadoop jmx抓取。
  
# 1.22.0
* [eadata-druid]
  - brand_stat 添加展示/点击独立设备数

# 1.21.1
* [eadata-druid]
  - 修改启动脚本中错误的配置名称

# 1.21.0
* [eadata-druid]
  - 抓取deep_link_invoke_status_joined和app_download_status_joined

# 1.20.0
* [jmxtrans]
  - YEX抓取winNoticeMessageProcessingDelayHistogram。
  
# 1.19.0
* [eadata-druid]
  - dsp_stat和sdk_stat加上相关性定向阈值维度min_relevancy
  - 补上漏掉的场景ID维度

# 1.18.0
* [eadata-druid]
  - sdk_stat_v2添加样式维度。

# 1.17.0
* [eadata-druid]
  - interest_stat "targetPartitionSize": 1000000->5000000。
  - druid.broker.balancer.type=random。
* [jmxtrans]
  - YEX添加实例th079-th081。

# 1.16.0
* [eadata-druid]
 - interest_stat 批量流程添加 scenario_ids
 - sdk_stat 批量流程，实时流程添加 scenario_ids

# 1.15.0
* [eadata-druid]
  - topic pv_sdk在对应的Avro schema上更新了doc字段，但avro中Schema并不以doc字段作为区分，导致tranquility处理schema时挂掉。
  现在是将tranquility的注入schema中临时加上"kafka.auto.offset.reset":"largest"并将kafka.group.id永久换掉，重启服务。
  
# 1.14.0
* [eadata-druid]
  - 为实时的dsp_stat/sdk_stat增加enable_deep_link属性

# 1.13.0
* [jmxtrans]
    - 添加 conv-sponsor-venus.json 配置

# 1.12.1
* [jmxtrans]
    - bs-ng.json加实例。
    - 排序各实例。

# 1.12.0
* [jmxtrans]
    - [course-kafka-producer.json](./ead/jmxtrans/ns003/jsonfiles/course-kafka-producer.json)抓取courseKafkaProducer中处理精品课数据计数

# 1.11.0
* [eadata-druid]
  - 为sdk_stat增加abx_test_marks字段

# 1.10.0
* [eadata-druid]
  - 为 sdk_stat, sdk_stat_v2 增加 conv_parent_type 字段

# 1.9.0
* [jmxtrans]
    - [bs-ng.json](./ead/jmxtrans/ns003/jsonfiles/bs-ng.json)抓取bs中读写venus失败计数

# 1.8.1
* [eadata-druid]
    - 修改druid tranquility批量启动脚本
    - bugfix: 实时sdk_material_stat消费的组错误，和原有组重名，导致原有组的kafka消息被错误消费；

# 1.8.0
* [eadata-druid]
    - wiki: [新e-greedy实现文档](http://confluence.inner.youdao.com/pages/viewpage.action?pageId=2530513)
    - 新增广告素材id统计信息表；

# 1.7.1
* [eadata-druid]
    - dsp_stat/sdk_stat增加enable_deep_link属性

# 1.7.0
* [jmxtrans] 添加jsonfiles文件。

# 1.6.0
* [eadata-druid]
    - wiki: [用户点击数 vs. 广告请求数、第三方入口点击上报](https://dev.corp.youdao.com/outfoxwiki/liuhaibo/iad#A.2BhD1XMJh1-pv_vs._.2BdShiN3C5Ufs-)
    - 为互动广告新增用户真实点击pv、外部媒体上报的广告位入口点击

# 1.5.2
* [eadata-druid]
    - 为realtime_index_sdk_stat增加is_secure属性

# 1.5.1
* [eadata-druid]
    - 性能调优。

# 1.5.0
* [eadata-druid]
    - historical节点rocket tier取消以下监控："com.metamx.metrics.JvmMonitor"，"com.metamx.metrics.SysMonitor"
    - middleManager节点processing.buffer.sizeBytes,druid.processing.numThreads调整。
    - tranquility landingpage_stat/third_party_stat/dsp_stat_v2的intermediatePersistPeriod调整。
    - startAllTranquilityXXX.sh 添加third_party_stat、brand_stat。
    - dataSource sdk_stat 添加 referer_count。

# 1.4.0
* [eadata-druid]
    - 增加 ocpc 数据域批量，实时配置

# 1.3.0
* [eadata-druid]
    - 增加 third-party-stat 批量、实时配置

# 1.2.0
* [eadata-druid]
    - 添加品牌广告统计的配置和lookups

# 1.1.0
* [eadata-druid]
    - druid增加互动广告统计到sdk_stat，实时+批量。

# 1.0.0
* 初始化工程 。
* [eadata-druid]
    - 新加druid的批量、实时、各node配置。

