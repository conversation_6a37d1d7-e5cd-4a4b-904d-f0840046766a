package com.youdao.ead.util

import java.time.LocalDateTime

import com.youdao.ead.util.CommonUtil.dateTimeFormatter
import org.apache.hadoop.shaded.org.apache.commons.cli.{BasicParser, CommandLine, Options}

class CommandOptions {
  /**
   * 获取增量数据的日期，默认是前一天
   */
  var beginDate: String = LocalDateTime.now().minusDays(1).format(dateTimeFormatter)
  var endDate: String = LocalDateTime.now().format(dateTimeFormatter)
  var hour: String = "*"
  var limit: Int = -1
}

object CommandOptions{
  val BEGIN_DATE = "beginDate"
  val END_DATE = "endDate"
  val HOUR = "hour"
  val LIMIT = "limit"

  def parseOpts(args: Array[String]): CommandOptions = {
    val options = new Options()
    val parser: BasicParser = new BasicParser()
    options.addOption("b", BEGIN_DATE, true, "incremental date")
    options.addOption("e", END_DATE, true, "incremental date")
    options.addOption("h", HOUR, true, "incremental hour")
    options.addOption("l", LIMIT, true, "incremental hour")
    val commandLine: CommandLine = parser.parse(options, args)
    val opts = new CommandOptions()
    if (commandLine.hasOption(BEGIN_DATE)) {
      opts.beginDate = commandLine.getOptionValue(BEGIN_DATE)
    }
    if (commandLine.hasOption(BEGIN_DATE)) {
      opts.endDate = commandLine.getOptionValue(END_DATE)
    }
    if (commandLine.hasOption(HOUR)) {
      opts.hour = commandLine.getOptionValue(HOUR)
    }
    if (commandLine.hasOption(LIMIT)) {
      opts.limit = commandLine.getOptionValue(LIMIT).toInt
    }
    opts
  }
}
