package com.youdao.ead

import com.youdao.ead.util.CommonUtil.{dateTimeFormatter, hdfsTimeFormatter}
import com.youdao.ead.util.{CommandOptions, Config}
import io.lettuce.core.Range
import io.lettuce.core.cluster.RedisClusterClient
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection
import io.lettuce.core.cluster.api.async.RedisAdvancedClusterAsyncCommands
import io.lettuce.core.codec.{ByteArrayCodec, RedisCodec, StringCodec}
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.lang3.StringUtils
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}
import org.msgpack.MessagePack
import org.msgpack.packer.Packer
import org.msgpack.unpacker.Unpacker
import outfox.ead.ip.location.entity.AdvancedLocation
import outfox.ead.ip.location.tools.AdvancedIpTool

import java.io.{ByteArrayInputStream, ByteArrayOutputStream}
import java.time.LocalDate


case object DeviceInfoToRedis {
  private val basePath = "hdfs://eadata-hdfs/quipu/camus/data/brand_pv/hourly/__date__/__hour__"

  private val ONE_DAY: Long = 60 * 60 * 24
  private val EXPIRE_DAY: Int = 15

  def main(args: Array[String]): Unit = {
    val opts: CommandOptions = CommandOptions.parseOpts(args)
    val beginDate: LocalDate = LocalDate.parse(opts.beginDate, dateTimeFormatter)
    val endDate: LocalDate = LocalDate.parse(opts.endDate, dateTimeFormatter)
    val hour: String = opts.hour
    val limit: Int = opts.limit
    print(beginDate, endDate, hour, limit)
    var date: LocalDate = beginDate
    while (date.isBefore(endDate)) {
      etl(date, hour, limit)
      date = date.plusDays(1)
    }
  }

  def etl(date: LocalDate, hour: String, limit: Int): Unit = {
    val spark: SparkSession = SparkSession
      .builder()
      .appName("DeviceInfoToRedis - " + date)
      .getOrCreate()
    val dataSet: Dataset[Params] = doExtract(spark, date, hour, limit)
    val expireTimes: Long = getExpireTime(date)
    transformAndLoad(dataSet, date, expireTimes)
    spark.close()
  }

  private def getExpireTime(date: LocalDate): Long = {
    val now: LocalDate = LocalDate.now()
    if (now.isBefore(date)) {
      throw new RuntimeException("所选日期不能大于现在")
    }
    val expireTimes: Long = (date.toEpochDay - now.toEpochDay + EXPIRE_DAY) * ONE_DAY
    if (expireTimes <= 0) {
      throw new RuntimeException("数据已过期")
    }
    expireTimes
  }

  /**
   * 从hdfs拉取指定数据
   */
  private def doExtract(spark: SparkSession, date: LocalDate, hour: String, limit: Int): Dataset[Params] = {
    import spark.implicits._

    var df: DataFrame = spark.read
      .format("avro")
      .load(basePath
        .replace("__date__", date.format(hdfsTimeFormatter))
        .replace("__hour__", hour)
      )
    if (limit > 0) {
      df.limit(limit)
    }
    if (!df.columns.contains("age_v2")) {
      df = df.withColumn("age_v2", lit(List.empty))
    }
    df
      .as[Params]
      .filter(_.model != "")
      .filter(_.mid != "")
      .filter(_.os != "")
      .filter(_.imprIp != null)
      .filter(_.ua != "")
  }


  private def transformAndLoad(dataset: Dataset[Params], date: LocalDate, expireTime: Long): Unit = {
    dataset
      .repartition(400)
      .rdd
      .foreachPartition(partition => {
        val redisClient: RedisClusterClient = RedisClusterClient.create(Config.redisUrl)

        val connection: StatefulRedisClusterConnection[String, Array[Byte]] = redisClient
          .connect(RedisCodec.of(new StringCodec, new ByteArrayCodec()))
        val bytesCommands: RedisAdvancedClusterAsyncCommands[String, Array[Byte]] = connection
          .async()

        val messagePack = new MessagePack()
        messagePack.register(new DeviceInfo().getClass)

        partition.foreach(a => {
          import scala.collection.JavaConverters._
          val queryParams: QueryParams = new QueryParams(
            a.model,
            a.mid,
            a.os,
            a.imprIp,
            a.ua,
            a.oaid,
            a.imei,
            a.idfa,
            a.gender.asJava,
            a.age.asJava,
            a.interests.asJava,
            a.dictStates.asJava,
            a.age_v2.asJava)
          if (queryParams.isValid) {
            val location: AdvancedLocation = AdvancedIpTool.getInstance().locate(queryParams.getIp)
            queryParams.setCountry(location.getCountry)
            queryParams.setProvince(location.getProvince)
            queryParams.setCity(location.getCity)
            doLoad(queryParams, bytesCommands, date, expireTime, messagePack)
          }
        })

        connection.close()
        redisClient.close()
      })
  }

  private def doLoad(queryParams: QueryParams, bytesCommands: RedisAdvancedClusterAsyncCommands[String, Array[Byte]], date: LocalDate, expireTime: Long, messagePack: MessagePack): Unit = {
    val score: Long = date.toEpochDay
    val deviceInfo = new DeviceInfo()

    deviceInfo.setAge(queryParams.getAge)
    deviceInfo.setAgeV2(queryParams.getAgeV2)
    deviceInfo.setGender(queryParams.getGender)
    deviceInfo.setInterests(queryParams.getInterests)
    deviceInfo.setDictStates(queryParams.getDictStates)

    val zsetKey: String = DigestUtils.md5Hex(queryParams.getUid)
    if (StringUtils.isNotBlank(queryParams.getIdfa)) {
      val key: String = "idfa_" + queryParams.getIdfa
      deviceInfo.setIdfa(queryParams.getIdfa)
      val zsetValue: Array[Byte] = messagePack.write(deviceInfo)
      val value: Array[Byte] = getValue(messagePack, zsetKey, zsetValue)
      insertOrUpdateDeviceInfo(bytesCommands, score, key, value, zsetKey, zsetValue, expireTime, messagePack)
      deviceInfo.setIdfa(null)
    }

    if (StringUtils.isNotBlank(queryParams.getOaid)) {
      val key: String = "oaid_" + queryParams.getOaid
      deviceInfo.setOaid(queryParams.getOaid)
      val zsetValue: Array[Byte] = messagePack.write(deviceInfo)
      val value: Array[Byte] = getValue(messagePack, zsetKey, zsetValue)
      insertOrUpdateDeviceInfo(bytesCommands, score, key, value, zsetKey, zsetValue, expireTime, messagePack)
      deviceInfo.setOaid(null)
    }

    if (StringUtils.isNotBlank(queryParams.getImei)) {
      deviceInfo.setImei(queryParams.getImei)
      val key: String = "imei_" + queryParams.getImei
      val zsetValue: Array[Byte] = messagePack.write(deviceInfo)
      val value: Array[Byte] = getValue(messagePack, zsetKey, zsetValue)
      insertOrUpdateDeviceInfo(bytesCommands, score, key, value, zsetKey, zsetValue, expireTime, messagePack)
    }
  }

  private def getValue(messagePack: MessagePack, zsetKey: String, zsetValue: Array[Byte]) = {
    val os = new ByteArrayOutputStream()
    val packer: Packer = messagePack.createPacker(os)
    packer.write(zsetKey)
    packer.write(zsetValue)
    os.toByteArray
  }

  private def insertOrUpdateDeviceInfo(bytesCommands: RedisAdvancedClusterAsyncCommands[String, Array[Byte]],
                                       score: Long,
                                       key: String,
                                       value: Array[Byte],
                                       zsetKey: String,
                                       zsetValue: Array[Byte],
                                       expireTime: Long,
                                       messagePack: MessagePack) = {
    if (bytesCommands.exists(key).get() > 0) {
      update(score, key, value, zsetKey, zsetValue, bytesCommands, expireTime, messagePack)
    } else {
      insert(score, key, value, zsetKey, zsetValue, bytesCommands, expireTime)
    }
    bytesCommands.zremrangebyscore(zsetKey, Range.create(0, LocalDate.now.minusDays(EXPIRE_DAY).toEpochDay))
  }

  private def insert(score: Long, key: String, value: Array[Byte], zsetKey: String, zsetValue: Array[Byte], bytesCommands: RedisAdvancedClusterAsyncCommands[String, Array[Byte]], expireTime: Long): Any = {
    bytesCommands.setex(key, expireTime, value)
    bytesCommands.zadd(zsetKey, score, zsetValue)
    bytesCommands.expire(zsetKey, expireTime)
  }

  private def update(score: Long, key: String, value: Array[Byte], zsetKey: String, zsetValue: Array[Byte], bytesCommands: RedisAdvancedClusterAsyncCommands[String, Array[Byte]], expireTime: Long, messagePack: MessagePack): Any = {
    val oldValue: Array[Byte] = bytesCommands.get(key).get()
    if (value.sameElements(oldValue)) {
      bytesCommands.expire(key, expireTime)
      bytesCommands.zadd(zsetKey, score, zsetValue)
      bytesCommands.expire(zsetKey, expireTime)
    } else {
      val is = new ByteArrayInputStream(oldValue)
      val unpacker: Unpacker = messagePack.createUnpacker(is)
      val oldZsetKey: String = unpacker.readString()
      val oldZsetValue: Array[Byte] = unpacker.readByteArray()

      bytesCommands.setex(key, expireTime, value)
      bytesCommands.zrem(oldZsetKey, oldZsetValue)
      bytesCommands.zadd(zsetKey, score, zsetValue)
      bytesCommands.expire(zsetKey, expireTime)
    }
  }

  case class Params(model: String, mid: String, os: String, imprIp: String, ua: String, oaid: String, imei: String, idfa: String, gender: List[Integer], age: List[Integer], interests: List[Integer], dictStates: List[String], age_v2: List[Integer])
}
