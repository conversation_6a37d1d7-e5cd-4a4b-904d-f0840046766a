type=java
job.class=outfox.ead.apolo.job.math.DomesticCpaMktDataJdRealtimeJob
method.run=run
classpath=lib/*

# 存储数据库
eadb1.url=**************************************************************************************************************************************************************************************
#eadb1.url=*************************************************************************************************************************************************************************************
eadb1.username=eadonline4nb
eadb1.password=new1ife4Th1sAugust

# 分销系统订单入口url
dcpamkt.url=https://unionback.youdao.com/v2.0.0/order/jpk/inner
#dcpamkt.url=http://domestic-cpa-test.youdao.com/v2.0.0/order/jpk/inner

# 分布式锁
zk.connection.string=zk1:2181,zk2:2181,zk3:2181
zk.distributed.lock.path=/ead/apolo/domestic_mkt_data_jd_job
zk.distributed.lock.acquire.max.wait.ms=600000
zk.last.update.time.path=/ead/apolo/domestic_mkt_data_jd_job/last_update_time

# 京东联盟
jd.union.appKey=0e28024bd18a4927e82bd0908a422027
jd.union.appSecret=19f0a61f8a1949c6b472fa9042de5755
jd.union.serverUrl=https://api.jd.com/routerjson