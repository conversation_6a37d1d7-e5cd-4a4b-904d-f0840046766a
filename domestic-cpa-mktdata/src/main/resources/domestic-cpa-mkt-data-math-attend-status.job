type=java
job.class=outfox.ead.apolo.job.math.DomesticCpaMktDataMathAttendStatusJob
method.run=run
classpath=lib/*
Xmx=4G

# mathUrl
math.attend.status.url=https://shuxue.youdao.com/ydc/internal/user/course/get_user_study_info_by_time?
#math.attend.status.url=https://offline-shuxue.youdao.com/ydc/internal/user/course/get_user_study_info_by_time?

# 存储数据库
eadb1.url=**************************************************************************************************************************************************************************************
#eadb1.url=*************************************************************************************************************************************************************************************
eadb1.username=eadonline4nb
eadb1.password=new1ife4Th1sAugust

# 分销系统订单入口url
dcpamkt.url=https://unionback.youdao.com/v2.0.0/order/jpk/inner
#dcpamkt.url=http://th077x.corp.youdao.com:16669/v2.0.0/order/jpk/inner

# 分布式锁
zk.connection.string=zk1:2181,zk2:2181,zk3:2181
#zk.connection.string=zj131x.corp.youdao.com:2181
zk.distributed.lock.path=/ead/apolo/domestic_mkt_data_math_job
zk.distributed.lock.acquire.max.wait.ms=600000
zk.last.update.attend.status.time.path=/ead/apolo/domestic_mkt_data_math_attend_status_job/last_update_time