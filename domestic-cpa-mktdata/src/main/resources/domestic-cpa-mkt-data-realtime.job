type=java
job.class=outfox.ead.apolo.job.realtime.DomesticCpaMktDataRealtimeJob
method.run=run
classpath=lib/*

# 精品课数据库
dictdb.url=******************************************************************************************************************************************************************************
dictdb.username=dict
dictdb.password=dict123outfox

# 存储数据库
eadb1.url=**************************************************************************************************************************************************************************************
eadb1.username=eadonline4nb
eadb1.password=new1ife4Th1sAugust

# 分布式锁
zk.connection.string=zk1:2181,zk2:2181,zk3:2181
zk.distributed.lock.path=/ead/apolo/domestic_mkt_data_job
zk.distributed.lock.acquire.max.wait.ms=60000

# 分销系统订单入口url
dcpamkt.url=https://unionback.youdao.com/v2.0.0/order/jpk/inner