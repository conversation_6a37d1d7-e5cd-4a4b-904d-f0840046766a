package outfox.ead.apolo.job.jd;

import lombok.*;

import java.math.BigDecimal;

import static outfox.ead.apolo.job.Constants.POSITIVE_ORDER;
import static outfox.ead.apolo.job.Constants.TRIAL_ORDER;

/**
 * 京东联盟数据打通
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class MktDataJd {

    private Long id;
    private String vendor;
    private String orderId;
    private String productId;
    private String productName;
    private Integer orderStatus;
    private Integer returnNum;
    private Integer orderNum;
    private BigDecimal orderAmount;
    private BigDecimal maxCosAmount;
    private BigDecimal refundAmount;
    private BigDecimal price;
    private String orderCreateTime;
    private String orderUpdateTime;

    /**
     * 订单类型: 1-试听课 2-正价课
     */
    private Integer orderType;

    /**
     * 是否为裡job写入的数据。
     * 0(false)：批量
     * 1(true)：实时
     */
    private boolean byRealtimeJob;

    /**
     * 原始订单id
     */
    private Long taskId;

    /**
     * 子任务id
     */
    private Long subTaskId;

    /**
     * 一级下游id
     */
    private Long downstreamId1;

    /**
     * 二级下游id
     */
    private Long downstreamId2;

    /**
     * 三级下游id
     */
    private Long downstreamId3;

    public boolean isPositiveOrder() {
        return orderType != null && orderType.equals(POSITIVE_ORDER);
    }
}
