package outfox.ead.apolo.job.math;

import azkaban.utils.Props;
import com.facebook.presto.jdbc.internal.jackson.core.JsonGenerator;
import com.facebook.presto.jdbc.internal.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

import static outfox.ead.apolo.job.Constants.*;

/**
 * 将分销系统中有道数学底表数据同步上报给分销系统通用线索订单体系
 * <p>
 */
@Slf4j
public class DomesticCpaMktDataMathReporter {

    private final static String SOURCE_MATH = "math";

    /**
     * 访问分销系统http接口
     */
    private static final CloseableHttpClient HTTP_CLIENT = HttpClients.createDefault();

    /**
     * json解析
     */
    private final static ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 同步上报新增订单数据
     *
     * @param mktDataMath 订单数据
     * @param props       配置
     */
    public static boolean insertMktDataMath(MktDataMath mktDataMath, Props props) {
        boolean result = false;

        CloseableHttpResponse res = null;

        try {
            HttpPost post = new HttpPost(props.getString("dcpamkt.url"));

            StringEntity s = new StringEntity(generateInsertJson(mktDataMath), "UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");
            post.setEntity(s);

            res = HTTP_CLIENT.execute(post);

            result = parseResponse(props, res, "insertMktData");
        } catch (Exception e) {
            log.error("Error for insertMktData", e);
        } finally {
            try {
                if (res != null) {
                    res.close();
                }
            } catch (IOException e) {
                log.error("Error for close InputStream", e);
            }
        }

        return result;
    }


    /**
     * 同步上报更新订单数据
     *
     * @param mktDataMath 订单数据
     * @param props       配置信息
     */
    public static boolean updateMktDataMath(MktDataMath mktDataMath, Props props) {
        boolean result = false;

        CloseableHttpResponse res = null;

        try {
            HttpPut put = new HttpPut(props.getString("dcpamkt.url"));

            StringEntity s = new StringEntity(generateUpdateJson(mktDataMath), "UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");
            put.setEntity(s);

            res = HTTP_CLIENT.execute(put);

            result = parseResponse(props, res, "updateMktData");
        } catch (Exception e) {
            log.error("Error for updateMktData", e);
        } finally {
            try {
                if (res != null) {
                    res.close();
                }
            } catch (IOException e) {
                log.error("Error for close InputStream", e);
            }
        }

        return result;
    }

    /**
     * 同步上报更新订单出勤状态
     *
     * @param mktDataMath
     * @param props
     */
    public static boolean updateMktDataMathForAttendStatus(MktDataMath mktDataMath, Props props) {
        boolean result = false;

        CloseableHttpResponse res = null;

        try {
            HttpPut put = new HttpPut(props.getString("dcpamkt.url"));

            StringEntity s = new StringEntity(generateUpdateJsonForAttendStatus(mktDataMath), "UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");
            put.setEntity(s);

            res = HTTP_CLIENT.execute(put);

            result = parseResponse(props, res, "updateMktDataMathForAttendStatus");
        } catch (Exception e) {
            log.error("Error for updateMktDataMathForAttendStatus", e);
        } finally {
            try {
                if (res != null) {
                    res.close();
                }
            } catch (IOException e) {
                log.error("Error for close CloseableHttpResponse", e);
            }
        }

        return result;
    }

    /**
     * 解析分销系统订单上报响应
     *
     * @param props 配置信息
     * @param res   response
     * @return 上报是否成功
     * @throws IOException io
     */
    private static boolean parseResponse(Props props, CloseableHttpResponse res, String resource) throws IOException {
        boolean result = false;

        int statusCode = res.getStatusLine().getStatusCode();
        if (statusCode == 200) {
            Map<String, Object> respBody = OBJECT_MAPPER.readValue(res.getEntity().getContent(), HashMap.class);

            if ((Integer) respBody.get("error_code") == 0) {
                result = true;
            } else {
                log.error("{} access {} failed, error_code:{}, error_message:{}", resource,
                        props.getString("dcpamkt.url"), respBody.get("error_code"), respBody.get("error_message"));
            }
        } else {
            log.error("{} access {} failed, statusCode:{}", resource, props.getString("dcpamkt.url"), statusCode);
        }

        return result;
    }

    private static String generateInsertJson(MktDataMath mktDataMath) throws Exception {
        String json = "";

        Map<String, Object> result = new HashMap<>(32);
        result.put("source", SOURCE_MATH);
        result.put("orderId", mktDataMath.getOrderId());
        result.put("orderType", mktDataMath.getOrderType());
        result.put("attendStatus", NOT_ATTEND_STATUS_VALID);
        result.put("categoryName", "math");
        result.put("courseTitle", mktDataMath.getProductName());
        result.put("mobile", mktDataMath.getUserMobile());
        result.put("sourceUserId", mktDataMath.getUserMobile());
        result.put("orderAmount", mktDataMath.getOrderAmount());
        result.put("orderCreateTime", mktDataMath.getOrderCreateTime());
        result.put("outvendor", mktDataMath.getVendor());
        result.put("productId", mktDataMath.getProductId());
        result.put("refundAmount", mktDataMath.getRefundAmount());
        result.put("silentTransDetail", mktDataMath.getTransDetail());
        result.put("userValidity", USER_VALIDITY_VALID);
        result.put("byRealtimeJob", mktDataMath.isByRealtimeJob());

        StringWriter stringWriter = new StringWriter();
        JsonGenerator generator = OBJECT_MAPPER.getFactory().createGenerator(stringWriter);
        generator.writeObject(result);
        json = stringWriter.toString();

        return json;
    }

    private static String generateUpdateJson(MktDataMath mktDataMath) throws Exception {
        String json = "";

        Map<String, Object> result = new HashMap<>(16);
        result.put("source", SOURCE_MATH);
        result.put("orderId", mktDataMath.getOrderId());
        result.put("refundAmount", mktDataMath.getRefundAmount());
        result.put("silentTransDetail", mktDataMath.getTransDetail());

        StringWriter stringWriter = new StringWriter();
        JsonGenerator generator = OBJECT_MAPPER.getFactory().createGenerator(stringWriter);
        generator.writeObject(result);
        json = stringWriter.toString();

        return json;
    }

    private static String generateUpdateJsonForAttendStatus(MktDataMath mktDataMath) throws Exception {
        String json = "";

        Map<String, Object> result = new HashMap<String, Object>();
        result.put("source", SOURCE_MATH);
        result.put("orderId", mktDataMath.getOrderId());
        result.put("attendStatus", mktDataMath.getAttendStatus());

        StringWriter stringWriter = new StringWriter();
        JsonGenerator generator = OBJECT_MAPPER.getFactory().createGenerator(stringWriter);
        generator.writeObject(result);
        json = stringWriter.toString();

        return json;
    }

}
