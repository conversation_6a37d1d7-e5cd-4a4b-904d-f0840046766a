package outfox.ead.apolo.job.jd;

import azkaban.utils.Props;
import com.facebook.presto.jdbc.internal.guava.collect.Lists;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.kplunion.OrderService.request.query.OrderRowReq;
import com.jd.open.api.sdk.domain.kplunion.OrderService.response.query.OrderRowResp;
import com.jd.open.api.sdk.request.kplunion.UnionOpenOrderRowQueryRequest;
import com.jd.open.api.sdk.response.kplunion.UnionOpenOrderRowQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.BeanPropertySqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.apolo.job.DataUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.apolo.job.Constants.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class DomesticCpaMktDataJdCommon {
    private static final Set<Integer> POSITIVE_ORDER_STATUS_SET = new HashSet<>(Arrays.asList(16, 17));
    private static final Set<Integer> INVALID_ORDER_STATUS_SET = new HashSet<>(Arrays.asList(3, 15));

    /**
     * 批量插入或更新分销系统数据库的条数
     */
    private final static int BATCH_SIZE = 2000;

    /**
     * map或者list初始化容量，防止频繁自动扩容 订单数量较多
     */
    private final static Integer INITIAL_MORE_CAPACITY = 30000;


    protected static boolean processMktDataJd(List<MktDataJd> mktDataJdList, List<DataUtils.OutvendorFromLink> vendorList, JdbcTemplate eadb1JdbcTemplate, Props props) {
        Map<String, DataUtils.OutvendorFromLink> vendorToTaskInfo = vendorList.stream()
                .collect(Collectors.toMap(DataUtils.OutvendorFromLink::getOutvendor, Function.identity()));
        // 组装MktDataJd
        assembleMktDataJd(mktDataJdList, vendorToTaskInfo);

        // 获取分销系统中现有的所有MktDataJd
        List<MktDataJd> mktDataJdListFromDomesticCpa = getAllDomesticCpaMktDataJdList(eadb1JdbcTemplate);

        Map<String, MktDataJd> orderId2MktDataJd = mktDataJdListFromDomesticCpa.stream()
                .collect(Collectors.toMap(MktDataJd::getOrderId, Function.identity()));
        log.info("mktDataJdListFromDomesticCpa size:{}", mktDataJdListFromDomesticCpa.size());

        // 找出分销系统中不存在的新订单
        List<MktDataJd> mktDataJdListForInsert = mktDataJdList.stream()
                .filter(value -> !orderId2MktDataJd.containsKey(value.getOrderId())).collect(Collectors.toList());
        log.info("mktDataJdListForInsert size:{}", mktDataJdListForInsert.size());
        int needInsert = mktDataJdListForInsert.size(), succeedInsert = 0;

        // 找出分销系统中已存在的，且对应信息发生变化的老订单
        List<MktDataJd> mktDataJdListForUpdate = mktDataJdList.stream()
                .filter(value -> (orderId2MktDataJd.containsKey(value.getOrderId())
                        && isDifferent(orderId2MktDataJd.get(value.getOrderId()), value)))
                .collect(Collectors.toList());
        log.info("mktDataJdListForUpdate size:{}", mktDataJdListForUpdate.size());
        int needUpdate = mktDataJdListForUpdate.size(), succeedUpdate = 0;

        // 将分销系统中京东联盟底表数据同步上报给分销系统通用线索订单体系
        List<MktDataJd> finalMktDataJdListForInsert = mktDataJdListForInsert.stream()
                .filter(value -> DomesticCpaMktDataJdReporter.insertMktDataJd(value, props))
                .collect(Collectors.toList());
        log.info("finalMktDataJdListForInsert size:{}", finalMktDataJdListForInsert.size());

        List<MktDataJd> finalMktDataJdListForUpdate = mktDataJdListForUpdate.stream()
                .filter(value -> DomesticCpaMktDataJdReporter.updateMktDataJd(value, props))
                .collect(Collectors.toList());
        log.info("finalMktDataJdListForUpdate size:{}", finalMktDataJdListForUpdate.size());

        // 存入新数据
        if (finalMktDataJdListForInsert.size() > 0) {
            succeedInsert = insertDomesticCpaMktDataJd(finalMktDataJdListForInsert, eadb1JdbcTemplate);
            log.info("succeed insert to database {}", succeedInsert);
        }

        // 更新已存在数据
        if (finalMktDataJdListForUpdate.size() > 0) {
            succeedUpdate = updateDomesticCpaMktDataJd(finalMktDataJdListForUpdate, eadb1JdbcTemplate);
            log.info("succeed update to database {}", succeedUpdate);
        }
        return needInsert == succeedInsert && needUpdate == succeedUpdate;
    }

    /**
     * 添加 subTaskId, transDetail 等
     *
     * @param mktDataJdList  订单数据
     * @param vendorToTaskInfo vendor 信息
     */
    protected static void assembleMktDataJd(List<MktDataJd> mktDataJdList, Map<String, DataUtils.OutvendorFromLink> vendorToTaskInfo) {
        // 补充信息
        for (MktDataJd mktDataJd : mktDataJdList) {
            if (StringUtils.isNotBlank(mktDataJd.getVendor())) {
                DataUtils.OutvendorFromLink vendorFromLink = vendorToTaskInfo.get(mktDataJd.getVendor());
                if (vendorFromLink != null) {
                    mktDataJd.setTaskId(vendorFromLink.getTaskId());
                    mktDataJd.setSubTaskId(vendorFromLink.getSubTaskId());
                    mktDataJd.setDownstreamId1(vendorFromLink.getDownstreamId1());
                    mktDataJd.setDownstreamId2(vendorFromLink.getDownstreamId2());
                    mktDataJd.setDownstreamId3(vendorFromLink.getDownstreamId3());
                }
            }
            mktDataJd.setByRealtimeJob(false);
        }
    }

    /**
     * 根据间隔时间，遍历调取京东联盟API，获取订单数据
     *
     * @param startTime      查询开始时间
     * @param intervalMinute 间隔时间
     * @param mktDataJdList  查询到的所有订单数据
     * @param jdClient       京东联盟API客户端
     */
    protected static void loadMktDataJd(LocalDateTime startTime, Long intervalMinute, List<MktDataJd> mktDataJdList, JdClient jdClient) {
        LocalDateTime now = LocalDateTime.now();
        Set<String> jdIds = new LinkedHashSet<>(INITIAL_MORE_CAPACITY);
        LocalDateTime endTime;
        while (startTime.isBefore(now)) {
            endTime = startTime.plusMinutes(intervalMinute);
            if (endTime.isAfter(now)) {
                endTime = now;
            }
            getJdOrderByApi(startTime, endTime, jdClient, mktDataJdList, jdIds);
            startTime = endTime;
        }
    }

    /**
     * 分页获取京东联盟订单
     * API每次最多获取500个订单
     *
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param jdClient      客户端
     * @param mktDataJdList 所有订单信息
     * @param jdIds         jdID set
     */
    private static void getJdOrderByApi(LocalDateTime startTime, LocalDateTime endTime, JdClient jdClient, List<MktDataJd> mktDataJdList, Set<String> jdIds) {
        boolean hasMore = true;
        int startPage = 1;
        while (hasMore) {
            try {
                OrderRowReq orderReq = new OrderRowReq();
                orderReq.setStartTime(startTime.format(DATE_TIME_FORMATTER));
                orderReq.setEndTime(endTime.format(DATE_TIME_FORMATTER));
                orderReq.setPageSize(DEFAULT_PAGE_SIZE_FOR_JD);
                orderReq.setPageIndex(startPage);
                orderReq.setType(UPDATE_TIME_TYPE);

                UnionOpenOrderRowQueryRequest request = new UnionOpenOrderRowQueryRequest();
                request.setOrderReq(orderReq);
                request.setVersion("1.0");
                request.setTimestamp(LocalDateTime.now().format(DATE_TIME_FORMATTER));

                UnionOpenOrderRowQueryResponse response = jdClient.execute(request);
                if (response.getQueryResult().getData() != null) {
                    convertJdDate(mktDataJdList, jdIds, response.getQueryResult().getData());
                }
                hasMore = response.getQueryResult().getHasMore() != null && response.getQueryResult().getHasMore();
                startPage++;
            } catch (Exception e) {
                hasMore = false;
                log.error("get jd union order api error", e);
            }
        }
    }

    /**
     * 数据处理，将API获取到的结果进行处理封装
     *
     * @param mktDataJdList 京东订单信息
     * @param jdIds         jdID Set
     * @param orderRowResps 获取的订单信息
     */
    private static void convertJdDate(List<MktDataJd> mktDataJdList, Set<String> jdIds, OrderRowResp[] orderRowResps) {
        try {
            for (OrderRowResp order : orderRowResps) {
                String jdId = order.getId();
                // jd 唯一ID, 过滤
                if (jdIds.contains(jdId)) {
                    continue;
                }
                String orderId = order.getOrderId().toString();
                Integer orderStatus = order.getValidCode();
                // 过滤未支付订单
                if (INVALID_ORDER_STATUS_SET.contains(orderStatus)) {
                    continue;
                }

                String vendor = order.getSubUnionId();
//                todo 测试使用，将所有订单设置成固定vendor, 上线前删除
//                if (StringUtils.isEmpty(vendor)) {
//                    vendor = "dingtest";
//                }
                String productId = order.getSkuId().toString();
                String productName = order.getSkuName();

                Integer returnNum = order.getSkuReturnNum();
                Integer orderNum = order.getSkuNum();

                BigDecimal price = BigDecimal.valueOf(order.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
                BigDecimal orderAmount = BigDecimal.valueOf(order.getActualCosPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
                // 已付款和已完成的才能计算金额，否则重置为0
                if (!POSITIVE_ORDER_STATUS_SET.contains(orderStatus)) {
                    orderAmount = BigDecimal.ZERO;
                }

                String orderCreateTime = order.getOrderTime();
                String orderUpdateTime = order.getModifyTime();

                MktDataJd mktDataJd = MktDataJd.builder()
                        .orderId(orderId + "_" + productId)
                        .vendor(vendor)
                        .productId(productId)
                        .productName(productName)
                        .orderAmount(orderAmount)
                        .maxCosAmount(orderAmount)
                        .price(price)
                        .orderCreateTime(orderCreateTime)
                        .orderUpdateTime(orderUpdateTime)
                        .orderType(GOODS_ORDER)
                        .orderStatus(orderStatus)
                        .orderNum(orderNum)
                        .returnNum(returnNum)
                        .byRealtimeJob(false)
                        .build();

                if (returnNum > 0) {
                    // 认为已退款，退款金额未知，默认为0
                    BigDecimal refundAmount = BigDecimal.ZERO;
                    mktDataJd.setRefundAmount(refundAmount);
                }

                jdIds.add(jdId);
                mktDataJdList.add(mktDataJd);
                // TODO: 2020/11/16 上线前删除
                log.info("mktDataJd is {}", mktDataJd.toString());
            }
        } catch (Exception e) {
            log.error("convert data error", e);
        }
    }

    /**
     * 判断订单某些信息是否发生变化，后期用来实时通知分销下游渠道任务订单具体的变化
     *
     * @param o 已存在订单信息
     * @param n 新获取订单信息
     * @return 是否有变化
     */
    protected static boolean isDifferent(MktDataJd o, MktDataJd n) {
        // 如果最大消费值变少了，则不更新该字段（默认更新，重置）, 需更新退款值字段
        if (o.getMaxCosAmount().compareTo(n.getMaxCosAmount()) > 0) {
            n.setRefundAmount(o.getMaxCosAmount().subtract(n.getMaxCosAmount()));
            n.setMaxCosAmount(o.getMaxCosAmount());
        }

        boolean result = ObjectUtils.notEqual(o.getOrderStatus(), n.getOrderStatus())
                || ObjectUtils.notEqual(o.getRefundAmount(), n.getRefundAmount())
                || ObjectUtils.notEqual(o.getOrderAmount(), n.getOrderAmount());
        if (result) {
            // TODO: 2020/11/13 上线前注释掉
            log.info("old mkd is {}", o.toString());
            log.info("new mkd is {}", n.toString());
        }
        return result;
    }

    /**
     * 将新的MktDataJd存入分销系统数据库
     *
     * @param finalMktDataJdListForInsert 需要新加入的订单
     * @param eadb1JdbcTemplate             jdbc
     * @return 插入个数
     */
    @Transactional(rollbackFor = Exception.class)
    protected static int insertDomesticCpaMktDataJd(List<MktDataJd> finalMktDataJdListForInsert, JdbcTemplate eadb1JdbcTemplate) {
        String sql = "INSERT INTO mkt_order_jd (outvendor, order_id, product_id, product_name, order_create_time, order_update_time, order_amount, max_cos_amount, refund_amount, " +
                "price, order_status, order_type, order_num, return_num, by_realtime_job, task_id, sub_task_id, downstream_id1, downstream_id2, downstream_id3) " +
                "VALUES (:vendor,:orderId,:productId,:productName,:orderCreateTime,:orderUpdateTime,:orderAmount,:maxCosAmount,:refundAmount," +
                ":price,:orderStatus,:orderType,:orderNum,:returnNum,:byRealtimeJob,:taskId,:subTaskId,:downstreamId1,:downstreamId2,:downstreamId3);";

        NamedParameterJdbcTemplate namedParameterJdbcTemplate =
                new NamedParameterJdbcTemplate(Objects.requireNonNull(eadb1JdbcTemplate.getDataSource()));

        return batchUpdate(finalMktDataJdListForInsert, namedParameterJdbcTemplate, sql);
    }


    /**
     * 更新已存在的 MktDataJd 数据到分销系统数据库
     *
     * @param finalMktDataJdListForUpdate 需修改的订单
     * @param eadb1JdbcTemplate             jdbc
     * @return 更新个数
     */
    @Transactional(rollbackFor = Exception.class)
    protected static int updateDomesticCpaMktDataJd(List<MktDataJd> finalMktDataJdListForUpdate, JdbcTemplate eadb1JdbcTemplate) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate =
                new NamedParameterJdbcTemplate(Objects.requireNonNull(eadb1JdbcTemplate.getDataSource()));

        String sql = "UPDATE mkt_order_jd SET order_amount=:orderAmount, max_cos_amount=:maxCosAmount, refund_amount=:refundAmount, " +
                "order_status=:orderStatus, order_update_time=:orderUpdateTime, return_num=:returnNum WHERE order_id = :orderId";

        return batchUpdate(finalMktDataJdListForUpdate, namedParameterJdbcTemplate, sql);
    }

    private static int batchUpdate(List<MktDataJd> finalMktDataJdListForUpdate, NamedParameterJdbcTemplate namedParameterJdbcTemplate, String sql) {
        int updateRows = 0;
        for (List<MktDataJd> list : Lists.partition(finalMktDataJdListForUpdate, BATCH_SIZE)) {
            int[] rows = namedParameterJdbcTemplate.batchUpdate(sql, list.stream()
                    .map(BeanPropertySqlParameterSource::new).toArray(SqlParameterSource[]::new));
            for (int c : rows) {
                updateRows += c;
            }
        }
        return updateRows;
    }

    /**
     * 获取分销系统中所有的MktDataJd数据
     *
     * @return
     * @param jdbcTemplate
     */
    @Transactional(rollbackFor = Exception.class)
    protected static List<MktDataJd> getAllDomesticCpaMktDataJdList(JdbcTemplate jdbcTemplate) {
        String sql = "select * from mkt_order_jd";

        return jdbcTemplate.query(sql, (rs, rowNum) -> MktDataJd.builder()
                .vendor(rs.getString("outvendor"))
                .id(rs.getLong("id"))
                .orderAmount(rs.getBigDecimal("order_amount"))
                .refundAmount(rs.getBigDecimal("refund_amount"))
                .maxCosAmount(rs.getBigDecimal("max_cos_amount"))
                .orderStatus(rs.getInt("order_status"))
                .orderNum(rs.getInt("order_num"))
                .returnNum(rs.getInt("return_num"))
                .orderType(rs.getInt("order_type"))
                .productName(rs.getString("product_name"))
                .productId(rs.getString("product_id"))
                .orderId(rs.getString("order_id"))
                .byRealtimeJob(rs.getBoolean("by_realtime_job"))
                .orderCreateTime(rs.getString("order_create_time"))
                .orderUpdateTime(rs.getString("order_update_time"))
                .build());

    }

}
