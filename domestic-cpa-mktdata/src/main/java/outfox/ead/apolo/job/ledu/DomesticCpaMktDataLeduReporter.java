package outfox.ead.apolo.job.ledu;

import azkaban.utils.Props;
import com.facebook.presto.jdbc.internal.jackson.core.JsonGenerator;
import com.facebook.presto.jdbc.internal.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

import static outfox.ead.apolo.job.Constants.ATTEND_STATUS_VALID;
import static outfox.ead.apolo.job.Constants.DATE_TIME_FORMATTER;
import static outfox.ead.apolo.job.Constants.NOT_ATTEND_STATUS_VALID;

/**
 * 将分销系统中有道乐读底表数据同步上报给分销系统通用线索订单体系
 *
 * Created by yangyu on 2020/10/26.
 */
@Slf4j
public class DomesticCpaMktDataLeduReporter {

    private final static String SOURCE_LEDU = "ledu";

    /**
     * 访问分销系统http接口
     */
    private static final CloseableHttpClient HTTP_CLIENT = HttpClients.createDefault();

    /**
     * json解析
     */
    private final static ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 同步上报新增订单数据
     *
     * @param mktDataLedu 订单数据
     * @param props       配置
     */
    public static boolean insertMktDataLedu(MktDataLedu mktDataLedu, Props props) {
        boolean result = false;

        CloseableHttpResponse res = null;

        try {
            HttpPost post = new HttpPost(props.getString("dcpamkt.url"));

            StringEntity s = new StringEntity(generateInsertJson(mktDataLedu), "UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");
            post.setEntity(s);

            res = HTTP_CLIENT.execute(post);

            result = parseResponse(props, res, "insertMktData");
        } catch (Exception e) {
            log.error("Error for insertMktData", e);
        } finally {
            try {
                if (res != null) {
                    res.close();
                }
            } catch (IOException e) {
                log.error("Error for close InputStream", e);
            }
        }

        return result;
    }


    /**
     * 同步上报更新订单数据
     * 更新出勤信息，退款信息等
     *
     * @param mktDataLedu 订单数据
     * @param props       配置信息
     */
    public static boolean updateMktDataLedu(MktDataLedu mktDataLedu, Props props) {
        boolean result = false;

        CloseableHttpResponse res = null;

        try {
            HttpPut put = new HttpPut(props.getString("dcpamkt.url"));

            StringEntity s = new StringEntity(generateUpdateJson(mktDataLedu), "UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");
            put.setEntity(s);

            res = HTTP_CLIENT.execute(put);

            result = parseResponse(props, res, "updateMktData");
        } catch (Exception e) {
            log.error("Error for updateMktData", e);
        } finally {
            try {
                if (res != null) {
                    res.close();
                }
            } catch (IOException e) {
                log.error("Error for close InputStream", e);
            }
        }

        return result;
    }


    /**
     * 实时订单任务同步上报更新订单数据
     * 更新关联信息，用户有效性
     *
     * @param mktDataLedu 订单数据
     * @param props       配置信息
     */
    public static boolean updateMktDataLeduForRealtime(MktDataLedu mktDataLedu, Props props) {
        boolean result = false;

        CloseableHttpResponse res = null;

        try {
            HttpPut put = new HttpPut(props.getString("dcpamkt.url"));

            StringEntity s = new StringEntity(generateUpdateJsonForRealtime(mktDataLedu), "UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");
            put.setEntity(s);

            res = HTTP_CLIENT.execute(put);

            result = parseResponse(props, res, "updateMktData");
        } catch (Exception e) {
            log.error("Error for updateMktData", e);
        } finally {
            try {
                if (res != null) {
                    res.close();
                }
            } catch (IOException e) {
                log.error("Error for close InputStream", e);
            }
        }

        return result;
    }

    /**
     * 解析分销系统订单上报响应
     *
     * @param props 配置信息
     * @param res   response
     * @return 上报是否成功
     * @throws IOException io
     */
    private static boolean parseResponse(Props props, CloseableHttpResponse res, String resource) throws IOException {
        boolean result = false;

        int statusCode = res.getStatusLine().getStatusCode();
        if (statusCode == 200) {
            Map<String, Object> respBody = OBJECT_MAPPER.readValue(res.getEntity().getContent(), HashMap.class);

            if ((Integer) respBody.get("error_code") == 0) {
                result = true;
            } else {
                log.error("{} access {} failed, error_code:{}, error_message:{}", resource,
                        props.getString("dcpamkt.url"), respBody.get("error_code"), respBody.get("error_message"));
            }
        } else {
            log.error("{} access {} failed, statusCode:{}", resource, props.getString("dcpamkt.url"), statusCode);
        }

        return result;
    }

    private static String generateInsertJson(MktDataLedu mktDataLedu) throws Exception {
        String json = "";

        Map<String, Object> result = new HashMap<>(32);
        result.put("source", SOURCE_LEDU);
        result.put("orderId", mktDataLedu.getOrderId());
        result.put("orderType", mktDataLedu.getOrderType());
        result.put("attendStatus", NOT_ATTEND_STATUS_VALID);
        result.put("categoryName", mktDataLedu.getCategoryName());
        result.put("courseTitle", mktDataLedu.getProductName());
        result.put("mobile", mktDataLedu.getMobile());
        result.put("sourceUserId", mktDataLedu.getUserId().toString());
        result.put("orderAmount", mktDataLedu.getOrderAmount());
        result.put("orderCreateTime", mktDataLedu.getOrderCreateTime().format(DATE_TIME_FORMATTER));
        result.put("outvendor", mktDataLedu.getOutvendor());
        result.put("productId", mktDataLedu.getProductId());
        result.put("refundAmount", mktDataLedu.getRefundAmount());
        result.put("silentTransDetail", mktDataLedu.getTransDetail());
        result.put("userValidity", mktDataLedu.getEffect() ? 1 : 0);

        StringWriter stringWriter = new StringWriter();
        JsonGenerator generator = OBJECT_MAPPER.getFactory().createGenerator(stringWriter);
        generator.writeObject(result);
        json = stringWriter.toString();

        return json;
    }

    private static String generateUpdateJson(MktDataLedu mktDataLedu) throws Exception {
        String json = "";

        Map<String, Object> result = new HashMap<>(16);
        result.put("source", SOURCE_LEDU);
        result.put("orderId", mktDataLedu.getOrderId());
        result.put("refundAmount", mktDataLedu.getRefundAmount());
        result.put("attendStatus", mktDataLedu.getAttendStatus() ? ATTEND_STATUS_VALID : NOT_ATTEND_STATUS_VALID);
        result.put("addTeacher", mktDataLedu.getAddTeacher());
        result.put("attendNumber", mktDataLedu.getAttendNumber());

        StringWriter stringWriter = new StringWriter();
        JsonGenerator generator = OBJECT_MAPPER.getFactory().createGenerator(stringWriter);
        generator.writeObject(result);
        json = stringWriter.toString();

        return json;
    }

    /**
     * notes: 如果发送 `silentTransDetail` 字段，数据为空，也会更新
     *
     * @param mktDataLedu
     * @return
     * @throws Exception
     */
    private static String generateUpdateJsonForRealtime(MktDataLedu mktDataLedu) throws Exception {
        String json = "";

        Map<String, Object> result = new HashMap<>(16);
        result.put("source", SOURCE_LEDU);
        result.put("orderId", mktDataLedu.getOrderId());
        result.put("userValidity", mktDataLedu.getEffect() ? 1 : 0);
        result.put("silentTransDetail", mktDataLedu.getTransDetail());

        StringWriter stringWriter = new StringWriter();
        JsonGenerator generator = OBJECT_MAPPER.getFactory().createGenerator(stringWriter);
        generator.writeObject(result);
        json = stringWriter.toString();

        return json;
    }
}
