package outfox.ead.apolo.job.ledu;

import azkaban.utils.Props;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.locks.InterProcessLock;
import org.apache.curator.framework.recipes.locks.InterProcessMutex;
import org.apache.zookeeper.CreateMode;
import org.springframework.jdbc.core.JdbcTemplate;
import outfox.ead.apolo.job.DataUtils;
import outfox.ead.apolo.job.DbUtil;
import outfox.ead.apolo.job.DistributedLock;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.apolo.job.Constants.YOU_DAO_LEDU;

/**
 * 分钟级别更新一次截止上次成功更新数据最大id之后的trade.trade_order表中数据
 * 上次成功更新数据最大id，存放在zookeeper中
 *
 * Created by yangyu on 2020/10/22.
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class DomesticCpaMktDataLeduRealtimeJob {
    private final String name;
    private final Props props;

    /**
     * map或者list初始化容量，防止频繁自动扩容 订单数量较多
     */
    private final static Integer INITIAL_MORE_CAPACITY = 30000;

    private final static String DEFAULT_ID = "0";

    private final static String PROPS_FILE_NAME = "domestic-cpa-mkt-data-ledu-realtime.job";

    public void run() throws Exception {
        DistributedLock.init(props.getString("zk.connection.string"));

        String lockPathInZk = props.getString("zk.distributed.lock.path");
        int lockAcquireMaxWaitMs = props.getInt("zk.distributed.lock.acquire.max.wait.ms", 600000);
        InterProcessLock globalLock = new InterProcessMutex(
                DistributedLock.getClient(), lockPathInZk);

        try {
            if (!globalLock.acquire(lockAcquireMaxWaitMs, TimeUnit.MILLISECONDS)) {
                log.error("acquire distributed lock failed in {} ms.", lockAcquireMaxWaitMs);
                return;
            }
        } catch (Exception e) {
            log.error("acquire distributed lock got error in {} ms.", lockAcquireMaxWaitMs, e);
            throw e;
        }

        try {
            log.info("got distributed lock under path {} and start to do job.", lockPathInZk);
            CuratorFramework curatorClient = DistributedLock.getClient();

            String lastUpdateIdInZk = props.getString("zk.last.update.id.path");
            if (curatorClient.checkExists().forPath(lastUpdateIdInZk) == null) {
                curatorClient.create().creatingParentsIfNeeded().withMode(CreateMode.PERSISTENT).forPath(lastUpdateIdInZk, DEFAULT_ID.getBytes());
            }
            String lastUpdateId = new String(curatorClient.getData().forPath(lastUpdateIdInZk));
            log.info("got lastUpdateId from zk: {}", lastUpdateId);

            log.info("============start============");

            Pair<Boolean, String> result = doWork(lastUpdateId);
            if (result.getLeft()) {
                curatorClient.setData().forPath(lastUpdateIdInZk, result.getRight().getBytes());
                log.info("set lastUpdateIdInZk succeed to {}", result.getRight());
            }

            log.info("============end============");
        } catch (Exception e) {
            log.error("error message:", e);
        } finally {
            globalLock.release();
            log.info("release distributed lock under path {}.", lockPathInZk);
            DistributedLock.destroy();
        }
    }

    private Pair<Boolean, String> doWork(String lastUpdateId) {
        Pair<Boolean, String> result = Pair.of(false, "");
        DomesticCpaMktDataLeduCommon common = new DomesticCpaMktDataLeduCommon();
        JdbcTemplate eadb1JdbcTemplate = DbUtil.getEadJdbcTemplate(PROPS_FILE_NAME);
        JdbcTemplate leduJdbcTemplate = DbUtil.getLeduJdbcTemplate(PROPS_FILE_NAME);

        List<DataUtils.OutvendorFromLink> vendorList = DataUtils.getOutvendorListFromDomesticCpa(eadb1JdbcTemplate, YOU_DAO_LEDU);
        Set<String> vendorSet = vendorList.stream().map(DataUtils.OutvendorFromLink::getOutvendor).collect(Collectors.toSet());
        log.info("vendorSet size:{}", vendorSet.size());

        if (!vendorSet.isEmpty()) {
            // 查找上次更新 orderId 之后新订单的 userIdSet
            Set<Long> userIdSet = common.getUserIdSet(vendorSet, lastUpdateId, leduJdbcTemplate);
            if (CollectionUtils.isEmpty(userIdSet)) {
                return result;
            }

            // 根据 userIdSet 查找对应的所有订单（属于分销系统vendor的订单或者正价课订单），结果根据 orderId 升序
            List<MktDataLedu> mktDataLeduList = common.getMktDataLeduListFromLeduByUserId(vendorSet, userIdSet, leduJdbcTemplate, common);
            log.info("mktDataLeduList size:{}, after id: {}", mktDataLeduList.size(), lastUpdateId);
            if (CollectionUtils.isEmpty(mktDataLeduList)) {
                return result;
            }
            String newId = mktDataLeduList.get(mktDataLeduList.size() - 1).getOrderId();

            // 组装MktDataLedu
            common.assembleMktDataLedu(mktDataLeduList, vendorList, vendorSet);

            // 获取分销系统中本次乐读新来的订单用户之前下过的所有订单
            List<MktDataLedu> mktDataLeduListFromDomesticCpa = common.getMktDataLeduListFromDomesticCpaByUserId(userIdSet, eadb1JdbcTemplate, common);
            Map<String, MktDataLedu> orderId2MktDataLedu = mktDataLeduListFromDomesticCpa.stream()
                    .collect(Collectors.toMap(MktDataLedu::getOrderId, Function.identity()));
            log.info("mktDataMathListFromDomesticCpa size:{}", mktDataLeduListFromDomesticCpa.size());

            // 处理旧订单有 transDetail 的情况
            processTransDetailForOldOrder(mktDataLeduList, eadb1JdbcTemplate, common);

            // 找出分销系统中不存在的新订单
            List<MktDataLedu> mktDataLeduListForInsert = mktDataLeduList.stream()
                    .filter(value -> !orderId2MktDataLedu.containsKey(value.getOrderId())).collect(Collectors.toList());
            log.info("mktDataLeduListForInsert size:{}", mktDataLeduListForInsert.size());
            int needInsert = mktDataLeduListForInsert.size(), succeedInsert = 0;

            // 找出分销系统中已存在的，且对应信息发生变化的老订单
            List<MktDataLedu> mktDataLeduListForUpdate = mktDataLeduList.stream()
                    .filter(value -> (orderId2MktDataLedu.containsKey(value.getOrderId())
                            && isDifferent(orderId2MktDataLedu.get(value.getOrderId()), value)))
                    .collect(Collectors.toList());
            log.info("mktDataLeduListForUpdate size:{}", mktDataLeduListForUpdate.size());
            int needUpdate = mktDataLeduListForUpdate.size(), succeedUpdate = 0;

            // 将分销系统中有道乐读底表数据同步上报给分销系统通用线索订单体系
            List<MktDataLedu> finalMktDataLeduListForInsert = mktDataLeduListForInsert.stream()
                    .filter(value -> DomesticCpaMktDataLeduReporter.insertMktDataLedu(value, props))
                    .collect(Collectors.toList());
            log.info("finalMktDataLeduListForInsert size:{}", finalMktDataLeduListForInsert.size());

            List<MktDataLedu> finalMktDataLeduListForUpdate = mktDataLeduListForUpdate.stream()
                    .filter(value -> DomesticCpaMktDataLeduReporter.updateMktDataLeduForRealtime(value, props))
                    .collect(Collectors.toList());
            log.info("finalMktDataLeduListForUpdate size:{}", finalMktDataLeduListForUpdate.size());

            // 存入新数据
            if (finalMktDataLeduListForInsert.size() > 0) {
                succeedInsert = common.insertDomesticCpaMktDataLedu(finalMktDataLeduListForInsert, eadb1JdbcTemplate);
                log.info("succeed insert to database {}", succeedInsert);
            }

            // 更新已存在数据
            if (finalMktDataLeduListForUpdate.size() > 0) {
                succeedUpdate = common.updateDomesticCpaMktDataLeduForRealtime(finalMktDataLeduListForUpdate, eadb1JdbcTemplate);
                log.info("succeed update to database {}", succeedUpdate);
            }

            boolean isAllDone = needInsert == succeedInsert && needUpdate == succeedUpdate;
            return Pair.of(isAllDone, newId);
        }

        return result;
    }

    private void processTransDetailForOldOrder(List<MktDataLedu> mktDataLeduList, JdbcTemplate eadb1JdbcTemplate, DomesticCpaMktDataLeduCommon common) {
        Map<String, String> orderId2TransDetailMap = common.getOrderId2TransDetailMap(eadb1JdbcTemplate);
        // 如果之前旧订单有 transDetail, 则不变更对应的 transDetail 信息
        mktDataLeduList.forEach(mktDataLedu -> {
            if (orderId2TransDetailMap.containsKey(mktDataLedu.getOrderId())) {
                mktDataLedu.setTransDetail(orderId2TransDetailMap.get(mktDataLedu.getOrderId()));
            }
        });
    }

    private static boolean isDifferent(MktDataLedu o, MktDataLedu n) {
        boolean result = ObjectUtils.notEqual(o.getTransDetail(), n.getTransDetail())
                || ObjectUtils.notEqual(o.getEffect(), n.getEffect());
        if (result) {
            log.info("old mkd is {}", o.toString());
            log.info("new mkd is {}", n.toString());
        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        Path path = Paths.get(Objects.requireNonNull(DomesticCpaMktDataLeduRealtimeJob.class.getClassLoader()
                .getResource(PROPS_FILE_NAME)).getPath());
        Props props = new Props(null, path.toFile());
        DomesticCpaMktDataLeduRealtimeJob job = new DomesticCpaMktDataLeduRealtimeJob("DomesticCpaMktDataLeduRealtimeJob", props);
        job.run();
    }
}
