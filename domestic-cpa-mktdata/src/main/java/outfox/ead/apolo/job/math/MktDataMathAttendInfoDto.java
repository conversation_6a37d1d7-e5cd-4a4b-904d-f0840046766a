package outfox.ead.apolo.job.math;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * 有道数学数据打通
 * 出勤信息
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class MktDataMathAttendInfoDto {

    /**
     * arrive_num : 4
     * class_type : 512
     * finish_item_num : 5
     * finish_num : 0
     * user_id : yd.353c47f1ee7d41c7a
     */

    @JsonProperty("arrive_num")
    private Integer arriveNum;

    @JsonProperty("class_type")
    private Integer classType;

    @JsonProperty("finish_item_num")
    private Integer finishItemNum;

    @JsonProperty("finish_num")
    private Integer finishNum;

    @JsonProperty("user_id")
    private String userId;
}
