# 广告位消费数据监控

 - 打包上传到[azkaban](http://ws014:9081)
    ```
    mvn package -pl ead-monitor -DskipTests
    ```
 - 手动运行
需要先修改pom.xml中的<mainClass>为相应的类。
    ```
    cd ead-monitor
    mvn compile exec:java -Dexec.mainClass="outfox.ead.apolo.stat.AppCostMonitor"
    ```
# 视频广告消费数据报告

 - 功能：
 查询昨日视频广告变体在广告位上的展示、点击消费数据，并发送到
  email.sendto 指定的收件人。
 - TODO
    - 目前只认为有一个视频URL，所以找到一个就返回。

 - 打包上传到[azkaban](http://ws014:9081)
    ```
    mvn package -pl ead-monitor -DskipTests
    ```
 - 手动运行
 需要先修改pom.xml中的<mainClass>为相应的类
    ```
    cd  ead-monitor
    mvn compile exec:java -Dexec.mainClass="outfox.ead.apolo.stat.VideoAdConsumptionReporter"
    ```
