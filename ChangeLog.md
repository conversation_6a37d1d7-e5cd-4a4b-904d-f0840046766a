CHANGELOG
=========
v1.43.0
* 需求
  - 渠道点击增加“渠道流量来源”识别与拦截功能
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6330)

v1.42.0
* 需求
  - 修改荣耀回传接口转化时间字段取值错误问题
  - 支持直投平台按比例回传转化数据
  - [JIRA](https://jira.inner.youdao.com/browse/ZHIXUAN-6307)

v1.41.0
* 需求
    - 快手 转化跟踪 事件映射调整
    - [JIRA](https://jira.inner.youdao.com/browse/BIDSYSTEM-3335)

v1.40.0 - 2025-05-15
* 需求
  - 广点通点击-转化回传协议升级
  - [广点通升级文档](https://datanexus.qq.com/doc/develop/guider/interface/conversion/trackingcgi_to_mktapi)
  - [广点通点击上报对接文档](https://datanexus.qq.com/doc/develop/guider/interface/conversion/ad_track_click)
  - [广点通转化回传对接文档](https://datanexus.qq.com/doc/develop/guider/interface/conversion/trackingcgi_api_app)
  - [JIRA](https://jira.inner.youdao.com/browse/BIDSYSTEM-3332)

v1.39.0
* 需求
    - 曝光归因路线为conv_sdk_joined添加conv_touchpoint_time_difference
    - [JIRA](https://jira.inner.youdao.com/browse/ZHIXUAN-6280)

v1.38.0
* 需求
   - adjust支持回传多事件
   - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6245)

v1.37.0
* 需求
    - 支持计算激活和付费的时间差
    - [JIRA](https://jira.inner.youdao.com/browse/ZHIXUAN-6143)

v1.36.0 - 2025-04-27
* 需求
    - 快手 转化跟踪对接
    - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6230)

v1.35.0 - 2025-04-24
* 需求
    - 曝光转化中增加竞价流量app包名: package name
    - [JIRA](https://jira.inner.youdao.com/browse/ZHIXUAN-6223)

v1.34.1 - 2025-04-24
* 需求
  - sdk流量的曝光归因、点击归因使用BID_ID+SPONSOR_ID

v1.34.0 - 2025-04-24
* 需求
  - 支持智选sdk转化数据去重
  - [JIRA](https://jira.inner.youdao.com/browse/ZHIXUAN-6222)

v1.33.0 - 2025-04-17
* 需求
  - 新增设备清洗池
  - [JIRA](https://jira.inner.youdao.com/browse/ZHIXUAN-6121)

v1.32.0 - 2025-03-27
* 需求
  - 推广活动转化行为映射支持组上的全部转化行为
  - [JIRA](https://jira.inner.youdao.com/browse/ZHIXUAN-6173)

v1.31.4 - 2025-03-14
* 需求
    - 添加ThirdPartyConv gaid字段

v1.31.2 - 2025-03-05
* 优化
  - 升级支持更多转化劫持-时间枚举值

v1.31.1 - 2025-03-03
* 优化
  - 优化dsp test conv设置debug flag的条件

v1.31.0 - 2025-02-28
* 需求
  - 联调阶段的转化跟踪支持标记待检查状态
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6034)

v1.30.0 - 2025-02-18
* 需求
  - 付费类型的转化如果没有上报orderId则不去重
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6034)

v1.29.0 - 2025-02-08
* 需求
  - 支持soul广告主转化跟踪协议对接
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3312)

v1.28.0 - 2025-01-13
* 需求
  - 渠道流量监控 V1.5 三期：增加转化指标
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6071)

v1.27.0 - 2024-12-18
* 需求
    - taptap 转化跟踪对接
    - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6032)

v1.26.0 - 2024-12-25
* 需求
  - 渠道流量监控 V1.5 二期：新增转化与点击的时间差 的维度与指标
  - [jira](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=349476047)

v1.25.0 - 2024-12-23
* 需求
- 智选app下载-新增付费指标
- [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6036)

v1.24.2 - 2024-12-23
* 优化
  - 记录点击转化维度的付费金额

v1.24.1 - 2024-12-03
* fix
  - 广点通转化行为同时支持商品详情页浏览和关键页面浏览

v1.24.0 - 2024-11-29
* 需求
  - 流量分配需求
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5980)

v1.23.0 - 2024-11-19
* 需求
    - moonshot月之暗面(广告商) 转化跟踪协议对接
    - [jira](https://jira.inner.youdao.com/browse/ZHIXUANDEV-239)
    - [doc](https://moonshot.feishu.cn/wiki/JFYDwITq0iJ2etk73hQcRpZrn3c)

V1.22.0 - 2024-11-19

* 需求
   - [曝光归因中添加算法 match_id](https://jira.inner.youdao.com/browse/EADPLATFOR-12)

V1.21.0 - 2024-11-06

* 需求
  - SpiderV3.1 增加推广活动级别的转化跟踪映射配置项
  - [PRD](https://confluence.inner.youdao.com/display/ead/SpiderV3.1)
  - [技术文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=351196271)

v1.20.1 - 2024-11-13
* fix
  - appsflyer转化跟踪对接升级：支持回传conv_type原始值为转化类型

v1.20.0 - 2024-11-04
* 需求
  - 更新广点通转化事件映射
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5943)

v1.19.0 - 2024-11-01
* 需求
  - 转化日志增加转化加速器的算法数据分桶id
  - [需求文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=351192116)

v1.18.0 - 2024-10-22
* 需求
  - 更新华为&荣耀事件映射
    - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3300)
  - bugfix: req conv add timestamp

v1.17.0 - 2024-10-21
* 需求
  - 使用dp吊起app或者投放小程序时，支持客户使用req_id归因并回传
  - [开发文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=346312563)

v1.16.0 - 2024-10-14
* 需求
  - 更新小米转化事件映射 
    - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5900)
  - 响应增加process_code字段，区分异常类型
    - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5903)

v1.15.0 - 2024-09-14
* 需求
  - 曝光归因转化回传中，增加scenario ids场景信息

v1.14.1- 2024-09-09
* 需求
  - camayos sdk_stat统计添加区分国家查看
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5857)

v1.14.0 - 2024-08-21
* 需求
  - 优化设备机型、品牌维度的获取逻辑

v1.13.0 - 2024-08-19
* 需求
  - 曝光归因转化回传中，增加iid图片素材信息

v1.12.1 - 2024-08-15
* 需求
  - appsflyer【客户】转化跟踪协议对接, 支持订单金额字段的请求参数格式

v1.12.0 - 2024-08-14
* 需求
  - appsflyer【客户】转化跟踪协议对接
  - [doc](https://confluence.inner.youdao.com/display/ead/AppsFlyer)

v1.11.0 - 2024-08-12
* 需求
  - 优化转化回传逻辑，校验callbackMd5是否为空

v1.10.0 - 2024-07-21
* 需求
  - 转化加速器增加app_id

v1.9.0 - 2024-07-11
* 需求
  - 渠道转化增加渠道唯一 did
  - jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5708

v1.8.0 - 2024-07-11
* 需求
  - 更新转化跟踪回传协议，支持回传留存天数字段
  - [jira]](https://jira.inner.youdao.com/browse/ZHIXUAN-5686)  
  
v1.7.0 - 2024-06-25
* 需求
  - access log打印来源ip地址
  - 放宽callback 回传的超时时间限制

v1.6.0 - 2024-06-19
* 需求
  - third-party转换跟踪日志guid改为uuid，新增字段click_guid用于记录关联点击日志的guid。便于数据join。

v1.5.0 - 2024-05-28
* 需求
  - 支持曝光归因数据回转
  - [doc](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=331096120)

v1.4.1 - 2024-04-19
* bugfix
  - 修改唯品会接口回传响应格式问题

v1.4.0 - 2024-04-02
* 需求
  - 修改api-rta兜底转化回传逻辑，支持开关配置
  - [doc](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=323369159)

v1.3.0 - 2024-03-29
* 需求
  - 唯品会【客户】转化跟踪协议对接
  - [doc](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=323369159)

v1.2.1 - 2024-03-19
* bugfix
  - 小米回传转化跟踪回传接口typo问题修复

v1.2.0 - 2024-02-27
* 功能
  - 小米转化跟踪协议更新
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5448)

v1.1.0 - 2024-01-09
* 优化
    - callback url 支持根据key 从不同的hbase table 查询数据

v1.0.0 - 2024-01-08
* 优化
    - redis使用集群模式

v0.0.55 - 2023-12-26
* 优化
- add androidId md5

v0.0.54 - 2023-12-25
* 优化
 - kafka使用新域名.

v0.0.53 - 2023-12-13
* 功能
  -  对接百度转化跟踪
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5390)

v0.0.52 - 2023-12-07
* 优化
  -  数据库链接串使用FQDN

v0.0.51 - 2023-11-14
* 功能
  - 转化新增智选转化加速器相关字段

v0.0.50 - 2023-11-06
* 功能
  - 禁用actuator health 端口
  - 修改cd config with json format

v0.0.49 - 2023-10-24
* 功能
  - 转化新增 caids

v0.0.48 - 2023-10-07
* 功能
- 统计增加api-mode、rta_enabled 等字段

v0.0.47 - 2023-09-28
* 功能
  - 修改hbase客户端超时时间设置，优化接口响应速度

v0.0.46 - 2023-09-06
* 功能
  - 修改打包插件配置
  - 修改转化时间校验为14天
  - 修改mapdb自动更新依赖问题

v0.0.45 - 2023-08-31
* 功能
  - 增加转化时间校验，15天的外的不统计点击转化

v0.0.44 - 2023-08-07
* bugfix
  - 修复lombok升级导致的jackson 反序列化失败问题

v0.0.43 - 2023-07-21
* 功能
  - oppo 转化跟踪对接
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3174)

v0.0.42 - 2023-08-03
* 功能
  - 华为荣耀转化跟踪对接
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3173)

v0.0.41 - 2023-07-03
* 功能
  - wifi万能钥匙转化跟踪对接
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3172)

v0.0.40 - 2023-05-19
* 功能
  - 增加oaid md5等设备字段

v0.0.39 - 2023-05-16
* 功能
  - track 接口增加order_id 字段，区分不同的购买转化事件
  - 修改回传去重逻辑，购买转化时间增加订单id作为去重key
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5247)

v0.0.38 - 2023-04-17
* 功能
  - redis迁移至百川

v0.0.37 - 2023-03-27
* 功能
  - 广点通 转化跟踪对接
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5201)

v0.0.36 - 2023-03-24
* 功能
  - push接口支持过滤第三方转化事件
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5211)
  
v0.0.35 - 2023-02-06
* 功能
  - 华为 转化跟踪对接
  - [jira]( https://jira.inner.youdao.com/browse/ZHIXUAN-5154)

v0.0.34 - 2023-01-18
* 功能
  - 小红书第三方转化，修复操作系统获取错误的bug

v0.0.33 - 2023-01-17
* 功能
  - 小红书 转化跟踪对接
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5150)

v0.0.32 - 2023-01-12
* 功能
  - vivo 转化跟踪对接
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3074)
  
v0.0.31 - 2023-01-03
* bugfix
  - 处理第三方转化数据中，设备厂商模型字段解析为null的问题

v0.0.30 - 2023-01-03
* 功能
  - 第三方转化增加省市，设备厂商模型字段
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5121)

v0.0.29 - 2022-11-30
* 功能
  - 增加http明细日志
  
v0.0.28 - 2022-11-18
* 功能
  - 第三方转化按配置的比例回传
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3038)

v0.0.27 - 2022-11-24
* 功能
  - 优化callback 日志
  - 添加监控
  - 修复小米回传设备号空的问题
v0.0.26 - 2022-10-24
* 功能
  - 对接小米转化回传api

v0.0.25 - 2022.09-16
-------------------
* bugfix
  - 修复callback service没有注入的问题。

v0.0.24 - 2022.09-16
-------------------
* 功能
  - add health check endpoint

v0.0.23 - 2022.03.30
-------------------
* 功能
  - redis to hbase

v0.0.22 - 2021.07.15
-------------------
* 功能
  - 支持根据“点击+转化行为“去重CPA转化数据
  - 支持上报转化数据给渠道方时，上报具体的转化行为并且筛选渠道方所需行为事件

v0.0.21 - 2021.03.18
-------------------
* 功能
    - 第三方监测转化支持 caid, alid

v0.0.20 - 2020.10.28
-------------------
* 功能
    - 淘宝rta更新转化事件对应关系

v0.0.19 - 2020.09.02
-------------------
* 功能
    - 大航海转化行为重新对接

v0.0.18 - 2020.07.29
-------------------
* 功能
    - 转化跟踪支持对接大航海事件

v0.0.17 - 2020.07.07
-------------------
* 功能
    - 第三方监测渠道上报支持转化事件

v0.0.16 - 2020.05.29
-------------------
* bugfix
    - 第三方监测老转化支持 活动id

v0.0.15 - 2020.05.29
-------------------
* 第三方监测支持老转化(api/v2/push)上报

v0.0.14 - 2020.05.19
-------------------
* 从缓存获取 callback url 缩短转化跟踪链接长度

v0.0.13 - 2020.04.27
-------------------
* 支持第三方转化监测支持上报设备号信息

v0.0.12 - 2020.04.22
-------------------
* 支持第三方转化监测支持更多字段和点击转化
* 支持第三方转化回调链接

v0.0.11 - 2019.10.31
-------------------
* 支持标准转化跟踪上报时，添加conv_time转化发生时间字段，hotfix

v0.0.10 - 2019.10.31
-------------------
* 支持标准转化跟踪上报时，添加conv_time转化发生时间字段

v0.0.9 - 2019.09.12
-------------------
* 支持标准转化跟踪上报时，添加order_amount转化订单金额字段

v0.0.8 - 2019.07.15
-------------------
* 迁移老转化文档

v0.0.7 - 2019.07.11
-------------------
* kafka client 升级到kafka_2.11-********，通过quipu-kafka 1.4.0

v0.0.6 - 2018.12.24
-------------------
* 变更接口路径 api/push 为 api/v2/push
* 添加 accessLog
* 接受 youdao_time 作为时间参考

v0.0.5 - 2018.12.24
-------------------
* 支持 DSP api 上报转化，并添加转化事件

v0.0.4 - 2018.12.24
-------------------
* 向 conv_dsp_avro 发送数据时，设置 conv_type 和 conv_parent_type.

v0.0.3 - 2018.11.01
-------------------
* 向conv_dsp_avro发送数据时设置conv_type字段到ext中

v0.0.2 - 2018.07.23
-------------------
* 支持第三方监测标准转化跟踪上报

v0.0.1 - 2018.03.03
-------------------
* 支持标准转化跟踪上报
