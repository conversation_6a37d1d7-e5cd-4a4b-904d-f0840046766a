#!/bin/sh
# Show usage
echo "--------------Welcome to use CourseKafkaProducer--------------" >/dev/stdout
echo "-- Usage:"  >/dev/stdout
echo "-- Start timed task: sh bin/start_course_kafka_producer.sh"  >/dev/stdout
echo "-- Start send by id: sh bin/start_course_kafka_producer sendById startId endId. Note: Don't contain data Id == startId or endId"  >/dev/stdout
echo "--------------------------------------------------------------"  >/dev/stdout

# Set appropriate paths and args
service_name="CourseKafkaProducer"
proj_path=$(dirname $0)/..
pid_file="$proj_path/$service_name.pid"
stdout_file="$proj_path/logs/stdout"
stderr_file="$proj_path/logs/stderr"

# Check pid file
if [[ -f ${pid_file} ]]; then
    echo "Error: Service $service_name may be already started. Check the status first." > /dev/stderr
    exit 1;
fi

# create logs dir if not exists
if [[ ! -d "$proj_path/logs" ]]
then
    mkdir "$proj_path/logs"
fi

# backup stdout_file and stderr_file if they are not empty
[[ -s ${stdout_file} ]] && mv ${stdout_file} ${stdout_file}.$(date +%Y-%m-%d.%H%M)
[[ -s ${stderr_file} ]] && mv ${stderr_file} ${stderr_file}.$(date +%Y-%m-%d.%H%M)

# invoke the server.
memory_options="-Xms500m -Xmx2g -XX:MaxMetaspaceSize=256M -XX:MetaspaceSize=256M";
gc_options="-server -verbose:gc -XX:+PrintGCDetails -XX:+PrintAdaptiveSizePolicy -XX:+PrintTenuringDistribution -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=3 -XX:GCLogFileSize=200M -XX:+PrintGCDateStamps -Xloggc:logs/gc.log -XX:ParallelGCThreads=13 -XX:ConcGCThreads=3 -XX:+UseG1GC -XX:MaxGCPauseMillis=10 -XX:-OmitStackTraceInFastThrow -XX:+ParallelRefProcEnabled";
jmx_options=" -Dcom.sun.management.jmxremote.port=9011 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false"
service_vm_args="-Dserver=CourseKafkaProducer $memory_options $gc_options $jmx_options"
service_jar="target/quipu-examples.jar"
if [[ $# == 3 ]]; then
    if [[ $1 == "sendById" ]]; then
        nohup java -jar ${service_vm_args} ${service_jar} $1 $2 $3 >${stdout_file} 2>${stderr_file} &
        echo "Start CourseKafkaProducer $1 $2 $3 success."
        exit 0
    fi
else
    nohup java -jar ${service_vm_args} ${service_jar} >${stdout_file} 2>${stderr_file} &
fi

# write down the pid
pid=$!;
if [[ -z ${pid} ]]; then
    echo "Error: cannot start the service." > /dev/stderr;
    exit 1;
fi
echo ${pid} > ${pid_file};
echo "Start CourseKafkaProducer success.";