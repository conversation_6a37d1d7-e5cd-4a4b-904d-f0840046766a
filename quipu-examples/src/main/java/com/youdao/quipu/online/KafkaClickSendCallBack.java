package com.youdao.quipu.online;

import com.codahale.metrics.Meter;
import com.codahale.metrics.SharedMetricRegistries;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.errors.OutOfOrderSequenceException;

import static com.youdao.quipu.online.ClickKafkaProducer.CLICK_PRODUCER_METRIC_REGISTRY;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
@RequiredArgsConstructor
public class KafkaClickSendCallBack implements Callback {
    private static Meter errorCallbackMeter = SharedMetricRegistries.getOrCreate(CLICK_PRODUCER_METRIC_REGISTRY).meter("send-to-kafka-callback-meter");
    private final KafkaProducer<byte[], byte[]> kafkaProducer;
    private final ClickRecordWrapper recordWrapper;


    @Override
    public void onCompletion(RecordMetadata metadata, Exception e) {
        if (e != null) {
            errorCallbackMeter.mark();

            log.warn("send record to kafka got error.topic is {}, click id is {}.",
                    recordWrapper.getTopic(), recordWrapper.getClickId(), e);

            log.info("retry send click {}.", recordWrapper.getClickId());
            this.kafkaProducer.send(recordWrapper.getRecord(), new KafkaClickSendCallBack(kafkaProducer, recordWrapper));
            if (e instanceof OutOfOrderSequenceException) {
                log.error("this exception should not happen since max.in.flight.requests.per.connection=1.");
            }
        }
    }
}
