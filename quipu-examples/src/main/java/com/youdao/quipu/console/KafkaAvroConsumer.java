package com.youdao.quipu.console;

import com.linkedin.camus.coders.CamusWrapper;
import com.linkedin.camus.etl.kafka.coders.JsonStringMessageDecoder;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageDecoder;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageEncoder;
import com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry;
import kafka.api.FetchRequest;
import kafka.api.FetchRequestBuilder;
import kafka.api.PartitionOffsetRequestInfo;
import kafka.cluster.BrokerEndPoint;
import kafka.common.TopicAndPartition;
import kafka.javaapi.*;
import kafka.javaapi.consumer.SimpleConsumer;
import kafka.message.MessageAndOffset;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.avro.generic.GenericData;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.HelpFormatter;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.PosixParser;
import org.joda.time.DateTime;

import java.nio.ByteBuffer;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by liuhongliang on 2015/1/28.
 */
@CommonsLog
public class KafkaAvroConsumer {

    private long time;//not null
    private String timeField = "date";//not null
    long count = 100;

    private volatile SimpleConsumer consumer;

    private final String topic;

    private final Map<String, Integer> brokers = new HashMap<String, Integer>();

    private final String clientName = "testClientName";

    private final int timeout = 100000;

    private DateFormat formatter;

    Map<Integer, BrokerEndPoint> brokerMap = new HashMap<>();
    Map<Integer, SimpleConsumer> consumerMap = new HashMap<>();
    Map<Integer, Long> offsetMap = new HashMap<>();
    ArrayList<LinkedList<GenericData.Record>> dataArray = new ArrayList<LinkedList<GenericData.Record>>();

    KafkaAvroMessageDecoder decoder;

    public KafkaAvroConsumer(String topic, String brokerStr, String timeField, String timeFormat, String time) {
        this.topic = topic;
        String[] brokerList = brokerStr.split(",");
        for (String broker : brokerList) {
            String[] host = broker.split(":");
            if (host.length != 2) {
                continue;
            }
            brokers.put(host[0], Integer.parseInt(host[1]));
        }
        formatter = new SimpleDateFormat(timeFormat);
        try {
            this.time = formatter.parse(time).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        this.timeField = timeField;
        initDecoder();
        initWork();
    }

    public void initWork() {
        initLeaders();
        initConsumers();
    }

    public void initDecoder() {
        decoder = new KafkaAvroMessageDecoder();
        String avroRepoUrl = "http://quipu-schema-repo.inner.youdao.com/";
        Properties properties = new Properties();
        properties.put(KafkaAvroMessageEncoder.KAFKA_MESSAGE_CODER_SCHEMA_REGISTRY_CLASS,
                AvroRestSchemaRegistry.class.getCanonicalName());
        properties.put(AvroRestSchemaRegistry.ETL_SCHEMA_REGISTRY_URL, avroRepoUrl);
        properties.put(JsonStringMessageDecoder.CAMUS_MESSAGE_TIMESTAMP_FIELD, timeField);
        decoder.init(properties, topic);
    }

    public void doWork() {
        initOffsets();
        while (true) {
            initDatas();
            if (dataArray.isEmpty()) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                mergeData();
            }
        }
    }

    public void mergeData() {
        LinkedList<GenericData.Record> head;
        buildMinHeap(dataArray);
        while (dataArray.size() != 0) {
            head = dataArray.get(0);
            GenericData.Record pop = head.pop();
            System.out.println(String.format("%s\t%s", new DateTime(pop.get(timeField)), pop.toString()));
            if (head.isEmpty()) {
                dataArray.remove(0);
                buildMinHeap(dataArray);
            } else {
                fixMinHeap(dataArray, 0);
            }
        }
    }

    public void fixMinHeap(ArrayList<LinkedList<GenericData.Record>> heap, int index) {
        int position = index;
        int left = 2 * index + 1;
        int right = 2 * index + 2;

        long positionData = (Long) heap.get(position).peek().get(timeField);
        if (left < heap.size()) {
            long leftData = (Long) heap.get(left).peek().get(timeField);
            if (left < heap.size() && leftData < positionData) {
                position = left;
            }

        }
        if (right < heap.size()) {
            long rightData = (Long) heap.get(right).peek().get(timeField);
            if (right < heap.size() && rightData < positionData) {
                position = right;
            }
        }

        if (position != index) {
            LinkedList<GenericData.Record> temp = heap.get(position);
            heap.set(position, heap.get(index));
            heap.set(index, temp);
            fixMinHeap(heap, position);
        }
    }

    public void buildMinHeap(ArrayList<LinkedList<GenericData.Record>> heap) {
        for (int i = heap.size() / 2 - 1; i >= 0; i--) {
            fixMinHeap(heap, i);
        }
    }

    public void initLeaders() {
        for (String host : brokers.keySet()) {
            SimpleConsumer consumer = null;
            try {
                consumer = new SimpleConsumer(host, brokers.get(host), 100000, 64 * 1024, "leaderLookup");
                List<String> topics = new ArrayList<String>();
                topics.add(topic);
                TopicMetadataRequest req = new TopicMetadataRequest(topics);
                TopicMetadataResponse resp = consumer.send(req);

                List<TopicMetadata> metaData = resp.topicsMetadata();
                for (TopicMetadata item : metaData) {
                    for (PartitionMetadata part : item.partitionsMetadata()) {
                        if (part != null) {
                            brokerMap.put(part.partitionId(), part.leader());
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e);
            } finally {
                if (consumer != null) {
                    consumer.close();
                }
            }
        }
    }

    public void initConsumers() {
        for (Integer key : brokerMap.keySet()) {
            BrokerEndPoint leader = brokerMap.get(key);
            consumerMap.put(key, new SimpleConsumer(leader.host(), leader.port(), timeout, (int) (count * 1024), clientName));
        }
    }

    public void initOffsets() {
        for (Integer key : consumerMap.keySet()) {
            offsetMap.put(key, getOffsetAt(key, consumerMap.get(key)));
        }
    }

    public void initDatas() {
        for (Integer key : offsetMap.keySet()) {
            LinkedList<GenericData.Record> data = fetchData(consumerMap.get(key), offsetMap.get(key), key);
            if (!data.isEmpty()) {
                dataArray.add(data);
                offsetMap.put(key, offsetMap.get(key) + data.size());
            }
        }
    }

    public long getOffsetAt(int partition, SimpleConsumer consumer) {
        long startOffset = getStartOffset(partition, consumer);
        long begin, end;
        long step = count;
        boolean forward = false;
        boolean returned = false;
        while (step > 0) {
            List<GenericData.Record> list = fetchData(consumer, startOffset, partition);
            int size = list.size();
            if (size == 0) {
                if (forward) {
                    step /= 2;
                    startOffset -= step;
                    forward = false;
                    returned = true;
                } else {
                    step /= 2;
                    startOffset += step;
                    forward = true;
                    returned = true;
                }
                continue;
            }
            begin = (Long) list.get(0).get(timeField);
            end = (Long) list.get(size - 1).get(timeField);
            if (begin > time) { // need backward
                if (forward) {
                    step /= 2;
                    returned = true;
                } else {
                    if (!returned) {
                        step *= 2;
                    } else {
                        returned = false;
                    }
                }
                startOffset -= step;
                forward = false;
            } else if (end < time) { // need forward
                if (forward) {
                    if (!returned) {
                        step *= 2;
                    } else {
                        returned = false;
                    }
                } else {
                    step /= 2;
                    returned = true;
                }
                startOffset += step;
                forward = true;
            } else {
                int add = 0;
                for (GenericData.Record record : list) {
                    if ((Long) record.get(timeField) < time) {
                        add++;
                    } else {
                        break;
                    }
                }
                return startOffset + add;
            }
        }
        return startOffset - 1;
    }

    public long getStartOffset(int partition, SimpleConsumer consumer) {
        TopicAndPartition topicAndPartition = new TopicAndPartition(topic, partition);
        Map<TopicAndPartition, PartitionOffsetRequestInfo> requestInfo =
                new HashMap<TopicAndPartition, PartitionOffsetRequestInfo>();
        requestInfo.put(topicAndPartition, new PartitionOffsetRequestInfo(
                kafka.api.OffsetRequest.EarliestTime(), 1));
        OffsetRequest request = new OffsetRequest(requestInfo,
                kafka.api.OffsetRequest.CurrentVersion(), clientName);
        OffsetResponse response;
        try {
            response = consumer.getOffsetsBefore(request);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return 0;
        }
        if (response.hasError()) {
            log.error("Error fetching data Offset Data the Broker. Reason: "
                    + response.errorCode(topic, partition));
            System.out.println("Error fetching data Offset Data the Broker. Reason: "
                    + response.errorCode(topic, partition));
            return 0;
        }
        long[] offsets = response.offsets(topic, partition);
        return offsets[0];
    }

    public LinkedList<GenericData.Record> fetchData(SimpleConsumer consumer, long startOffset, int partition) {
        int sizeInBytes = 1024 * 1024;
        FetchRequest req = new FetchRequestBuilder().clientId(clientName)
                .addFetch(topic, partition, startOffset, sizeInBytes).build();
        FetchResponse fetchResponse;
        try {
            fetchResponse = consumer.fetch(req);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
//        if (fetchResponse.hasError()) {
//            log.error("Error fetching data from the Broker: Reason: " + fetchResponse.errorCode(topic, partition));
//        }
        LinkedList<GenericData.Record> list = new LinkedList<GenericData.Record>();
        long numRead = 0;
        for (MessageAndOffset messageAndOffset : fetchResponse.messageSet(topic, partition)) {
            ByteBuffer payload = messageAndOffset.message().payload();
            byte[] bytes = new byte[payload.limit()];
            payload.get(bytes);
            CamusWrapper<GenericData.Record> decode = decoder.decode(bytes);
            list.add(decode.getRecord());

            numRead++;
            if (numRead >= count) {
                break;
            }
        }
        return list;
    }

    public static void main(String[] args) throws org.apache.commons.cli.ParseException {
        Options options = new Options();
        options.addOption("t", true, "topic");
        options.addOption("b", true, "broker");
        options.addOption("time", true, "the begin time to get data");
        options.addOption("m", true, "time's format");
        options.addOption("f", true, "the field of time in avro");
        options.addOption("h", false, "help");

        PosixParser parser = new PosixParser();
        CommandLine cmd = parser.parse(options, args);

        if (cmd.hasOption("h")) {
            HelpFormatter hf = new HelpFormatter();
            hf.printHelp("help:", options);
            return;
        }

//        String topic = KafkaAvroTest.topic;
//        String brokerStr = "nc110x.corp.youdao.com:9092,nc110x.corp.youdao.com:9094,nc110x.corp.youdao.com:9095";
//        String time = "2015-01-29 17:38:26";
//        String timeFormat = KafkaAvroTest.timeFormat;
//        String timeField = "date";
        String topic = cmd.getOptionValue("t");
        String brokerStr = cmd.getOptionValue("b");
        String time = cmd.getOptionValue("time");
        String timeFormat = cmd.getOptionValue("m");
        String timeField = cmd.getOptionValue("f");
        KafkaAvroConsumer client = new KafkaAvroConsumer(topic, brokerStr, timeField, timeFormat, time);
        client.doWork();
    }
}