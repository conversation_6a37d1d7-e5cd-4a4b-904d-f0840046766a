//package com.youdao.quipu.console;
//
//import com.youdao.quipu.avro.schema.Datum;
//import io.druid.data.input.schemarepo.Avro1124SubjectAndIdConverter;
//import io.druid.data.input.schemarepo.SubjectAndIdConverter;
//import kafka.javaapi.producer.Producer;
//import kafka.producer.KeyedMessage;
//import kafka.producer.ProducerConfig;
//import lombok.extern.apachecommons.CommonsLog;
//import org.apache.avro.Schema;
//import org.apache.avro.io.DatumWriter;
//import org.apache.avro.io.EncoderFactory;
//import org.apache.avro.specific.SpecificDatumWriter;
//import org.schemarepo.SchemaValidationException;
//import org.schemarepo.api.TypedSchemaRepository;
//import org.schemarepo.api.converter.AvroSchemaConverter;
//import org.schemarepo.api.converter.IdentityConverter;
//import org.schemarepo.api.converter.IntegerConverter;
//import org.schemarepo.client.RESTRepositoryClient;
//import org.schemarepo.json.GsonJsonUtil;
//
//import java.io.ByteArrayOutputStream;
//import java.io.IOException;
//import java.nio.ByteBuffer;
//import java.util.Arrays;
//import java.util.Properties;
//import java.util.Random;
//
//@CommonsLog
//public class KafkaAvroProducer
//{
//  public static void main(String[] args)
//      throws org.apache.commons.cli.ParseException, IOException, SchemaValidationException
//  {
//    RESTRepositoryClient client = new RESTRepositoryClient(
//        "http://quipu-schema-repo.inner.youdao.com",
//        new GsonJsonUtil(),
//        false
//    );
//    TypedSchemaRepository<Integer, Schema, String> repo = new TypedSchemaRepository<Integer, Schema, String>(
//        client, new IntegerConverter(), new AvroSchemaConverter(true), new IdentityConverter()
//    );
//    SubjectAndIdConverter c = new Avro1124SubjectAndIdConverter("temp");
//
//    Datum datum = Datum.newBuilder().setD0(Boolean.toString(new Random().nextBoolean()))
//                       .setD1(new Random().nextInt())
//                       .setTimestamp(System.currentTimeMillis())
//                       .setLong$(1L).build();
//    DatumWriter<Datum> writer = new SpecificDatumWriter<Datum>(datum.getSchema());
//
//    String subject = "temp";
//    Integer id = repo.registerSchema(subject, Datum.getClassSchema());
//    System.out.println(String.format("Id = %d", id));
//    ByteBuffer bf = ByteBuffer.allocate(5);
//    c.fromSubjectAndId(subject, id, bf);
//    ByteArrayOutputStream out = new ByteArrayOutputStream();
////    out.write(0x0);
////    out.write(ByteBuffer.allocate(4).putInt(id).array());
//    out.write(bf.array());
//    writer.write(datum, EncoderFactory.get().directBinaryEncoder(out, null));
//    out.flush();
//    Properties props = new Properties();
//
//
//    props.setProperty("metadata.broker.list", "ws143:6666");
//    props.setProperty("request.required.acks", "0");
//    props.setProperty("producer.type", "sync");
//    props.setProperty("serializer.class", "kafka.serializer.DefaultEncoder");
//
//    ProducerConfig config = new ProducerConfig(props);
//    Producer<byte[], byte[]> syncProducer = new Producer<byte[], byte[]>(config);
//    syncProducer.send(new KeyedMessage<byte[], byte[]>("temp", out.toByteArray()));
//    System.out.println(String.format("Bytes = %s", Arrays.toString(out.toByteArray())));
//    System.exit(0);
//  }
//}