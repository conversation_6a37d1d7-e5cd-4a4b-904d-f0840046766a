package com.youdao.quipu.examples.course;

import com.alibaba.fastjson.JSONObject;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageEncoder;
import com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry;
import com.yammer.metrics.Metrics;
import com.yammer.metrics.core.Meter;
import com.youdao.quipu.avro.schema.DspConv;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 将精品课转化数据从mysql发到kafka
 */
@Slf4j
public class CourseKafkaProducer {
    private static final String SEND_BY_ID = "sendById";
    /**
     * 系统配置文件
     */
    private static final String SYSTEM_PROP_FILE_NAME = "course_kafka_producer.properties";
    /**
     * Kafka生产者配置文件
     */
    private static final String KAFKA_PRODUCER_PROP_FILE_NAME = "kafka_producer.properties";
    private static final String SQL_PREFIX = "SELECT Id, createTime, vendor, platform, detail, product_Id, userId, detail_encrypt FROM course_orders ";
    private final String[] args;
    private CourseOrderDao courseOrderDao;
    private KafkaProducer<byte[], byte[]> kafkaProducer;
    private KafkaAvroMessageEncoder encoder;
    private String dspConvTopic;
    private String courseOrderTopic;
    private long interval;
    private String startIdFileName;
    /**
     * 从数据库中一次查询最多拿出的数据条数
     */
    private long dbMaxLoadData;
    /**
     * 每次获取转化数据的起点,不含起点
     */
    private long startId = -1;
    private static final Meter PROCESS_COURSE_ORDER_METER = Metrics.newMeter(CourseKafkaProducer.class, "processCourseOrderData",
            "count", TimeUnit.SECONDS);

    private CourseKafkaProducer(String[] args) throws Exception {
        this.args = args;
        init();
    }

    private void init() throws Exception {
        Properties systemProp = loadProperties(SYSTEM_PROP_FILE_NAME);
        dspConvTopic = systemProp.getProperty(CoursePropName.dspConvTopic);
        courseOrderTopic = systemProp.getProperty(CoursePropName.courseOrderTopic);
        interval = Long.parseLong(systemProp.getProperty(CoursePropName.producerInterval));
        dbMaxLoadData = Integer.parseInt(systemProp.getProperty(CoursePropName.courseDbMaxLoadData));
        startIdFileName = systemProp.getProperty(CoursePropName.startIdFile);

        File file = new File(startIdFileName);
        try (Scanner sc = new Scanner(new BufferedInputStream(new FileInputStream(file)))){
            startId = sc.nextLong();
            log.info("Get startId from {} success, startId = {}", startIdFileName, startId);
        }

        String jdbcUrl = systemProp.getProperty(CoursePropName.courseDbUrl);
        if (null != jdbcUrl) {
            courseOrderDao = new CourseOrderDao(jdbcUrl);
        } else {
            throw new Exception("Prop not set jdbcUrl!");
        }

        kafkaProducer = new KafkaProducer<>(loadProperties(KAFKA_PRODUCER_PROP_FILE_NAME));

        Properties encodeProp = new Properties();
        encodeProp.put(KafkaAvroMessageEncoder.KAFKA_MESSAGE_CODER_SCHEMA_REGISTRY_CLASS, AvroRestSchemaRegistry.class.getCanonicalName());
        encodeProp.put(AvroRestSchemaRegistry.ETL_SCHEMA_REGISTRY_URL, systemProp.getProperty(CoursePropName.avroRepositoryUrl));
        encoder = new KafkaAvroMessageEncoder(dspConvTopic, new Configuration());
        encoder.init(encodeProp, dspConvTopic);
    }

    private Properties loadProperties(String propFileName) throws Exception {
        InputStream inputStream = ClassLoader.getSystemResourceAsStream(propFileName);
        if (null == inputStream) {
            inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(propFileName);
        }

        if (null == inputStream) {
            throw new Exception("Can't find properties file " + propFileName);
        }
        Properties props = new Properties();
        props.load(inputStream);
        inputStream.close();
        return props;
    }

    private synchronized void updateStartIdToFile() {
        File file = new File(startIdFileName);
        if (!file.exists()) {
            try {
                if (file.createNewFile()) {
                    log.info("Create file {} success.", startIdFileName);
                } else {
                    log.error("Create file {} failed, startId = {}", startIdFileName, startId);
                    return;
                }
            } catch (IOException e) {
                log.error("Create file {} failed, startId = {}", startIdFileName, startId, e);
                return;
            }
        }

        try (BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(file, false))) {
            bufferedWriter.write(startId + "");
            log.info("Write startId = {} to {} success.", startId, startIdFileName);
        } catch (Exception e) {
            log.error("Write startId = {} to {} error.", startId, startIdFileName, e);
        }
    }

    /**
     * 根据id查找精品课订单记录并发送数据到Kafka上
     */
    private void sendById(long startId, long endId) {
        String sql = SQL_PREFIX + " WHERE Id > " + startId + " and Id < " + endId + " ORDER BY ASC";
        List<CourseOrder> courseOrderList = courseOrderDao.getData(sql);
        sendCourseOrderListToKafka(courseOrderList);
    }

    private void produce() {
        if (args.length == 0) {
            // 未传任务参数，执行正常任务
            runScheduleTask();
        } else {
            // 特殊任务 之 重发指定ID数据
            if (SEND_BY_ID.equals(args[0])) {
                log.info("Start sendById from {} to {}.", args[1], args[2]);
                sendById(Long.parseLong(args[1]), Long.parseLong(args[2]));
                log.info("sendById from {} to {} over.", args[1], args[2]);
                shutdown();
            } else {
                // 用户有传参，但是找不到对应的特殊任务，退出
                log.error("Unknown command, please read use info.");
            }
        }
    }

    private void runScheduleTask() {
        // 执行正常任务，从数据库中拿到转化数据，然后发送给conv_dsp_avro
        log.info("CourseKafkaProducer timed task started, dspConvTopic = {}, courseOrderTopic = {}, startId = {}, interval = {}", dspConvTopic, courseOrderTopic, startId, interval);
        ScheduledThreadPoolExecutor threadPool = new ScheduledThreadPoolExecutor(1,
                new BasicThreadFactory.Builder()
                        .daemon(false)
                        .namingPattern("course-kafka-timer-producer-%d")
                        .build());
        threadPool.scheduleWithFixedDelay(() -> {
            try {
                List<CourseOrder> courseOrderList = courseOrderDao.getData(genSql());
                sendCourseOrderListToKafka(courseOrderList);
                updateStartIdToFile();
            } catch (Exception e) {
                log.error("Send course order data to kafka error, startId = {}", startId, e);
            }
        }, 0, interval, TimeUnit.MILLISECONDS);

        // 添加shutdown hook关闭任务
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Shutdown hook was invoked, start shutdown courseKafkaProducer task...");
            threadPool.shutdownNow();
            while (threadPool.getActiveCount() != 0) {
                log.info("CourseKafkaProducer is waiting for last task...");
                sleep1sQuietly();
            }
            log.info("Last task end, start shutdown producer and db connection...");
            shutdown();
            log.info("Shutdown over~");
        }, "CourseKafkaProducerShutdownHook"));

    }

    private void sleep1sQuietly() {
        try {
            Thread.sleep(1000);
        } catch (Exception ignored) {}
    }

    private void sendCourseOrderListToKafka(List<CourseOrder> courseOrderList) {
        if (CollectionUtils.isEmpty(courseOrderList)) {
            log.info("No new data for startId = {}, stop", startId);
            return;
        }

        // 统计转化数据数量
        int count = 0;

        for (CourseOrder courseOrder : courseOrderList) {
            if (Thread.currentThread().isInterrupted()) {
                break;
            }

            PROCESS_COURSE_ORDER_METER.mark();
            if (null == courseOrder) {
                continue;
            }
            // 把所有订单都发送到courseOrderTopic中，给精品课重定向用
            sendAllCourseOrderToKafka(courseOrder);
            startId = courseOrder.getId();
            if (!courseOrder.isYoudaoConvert()) {
                log.info("Data that id = {} is not conv data, ignored.", courseOrder.getId());
                continue;
            }
            if (!sendCourseOrderToKafka(courseOrder)) {
                // 如果没有发送成功,回滚startId
                startId--;
            } else {
                count++;
            }
        }

        if (count != 0) {
            log.info("{} DspConv data has been sent to dspConvTopic = {}", count, dspConvTopic);
        }
    }

    private void sendAllCourseOrderToKafka(CourseOrder courseOrder) {
        String courseOrderJson = JSONObject.toJSONString(courseOrder);
        kafkaProducer.send(new ProducerRecord<>(courseOrderTopic, courseOrderJson.getBytes(StandardCharsets.UTF_8)));
    }

    private boolean sendCourseOrderToKafka(CourseOrder courseOrder) {
        boolean sendSuccess = false;
        while (!Thread.currentThread().isInterrupted() && !sendSuccess) {
            try {
                DspConv dspConv = buildDspConvByCourseOrder(courseOrder);
                Future<RecordMetadata> future = kafkaProducer.send(new ProducerRecord<>(dspConvTopic, encoder.toBytes(dspConv)));
                future.get();
                sendSuccess = true;
                log.info("Send order = {} to server.", courseOrder);
            } catch (Exception e) {
                log.error("Send course order data error ,course order = {}", courseOrder, e);
                sleep1sQuietly();
            }
        }

        return sendSuccess;
    }

    private String genSql() {
        return SQL_PREFIX + " WHERE Id > " + startId + " ORDER BY Id ASC LIMIT " + dbMaxLoadData;
    }

    private DspConv buildDspConvByCourseOrder(CourseOrder courseOrder) {
        DspConv.Builder dspConvBuilder = DspConv.newBuilder();
        dspConvBuilder.setBid(courseOrder.getBidId())
                .setSponsorId(NumberUtils.toLong(courseOrder.getSponsorId()))
                .setTimestamp(courseOrder.getCreateTime())
                .setGuid(UUID.randomUUID().toString())
                .setConvParentType("db")
                .setConvType("course_order")
                .setCtAction("landingpage_course")
                .setUrl("")
                .setDomain("")
                .setAgent("")
                .setContext(0)
                .setAdSize("")
                .setMatchId(0)
                // 这里设置成0或1均可，druid会从多个topic中拿数据，可能拿到同一个转化数据多次，druid只会对一条数据设置成1
                .setConv(0)
                .setConvCost(0)
                .setLocConv(0)
                .setLocConvCost(0)
                .setQualityScore(-1)
                .setClickIp("")
                .setCtId("")
                .setCtType(-1)
                .setDestLink("");
        Map<CharSequence, CharSequence> ext = new HashMap<>(16);
        ext.put("platform", courseOrder.getPlatform());
        dspConvBuilder.setExt(ext);

        return dspConvBuilder.build();
    }

    private void shutdown() {
        courseOrderDao.close();
        kafkaProducer.close();
    }

    public static void main(String[] args) throws Exception {
        CourseKafkaProducer courseKafkaProducer = new CourseKafkaProducer(args);
        courseKafkaProducer.produce();
    }
}
