/**
 * @(#)KafkaSimpleConsumer.java, 2014-12-05
 *
 * Copyright 2014 Youdao, Inc. All rights reserved.
 * YOUDAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.youdao.quipu.examples;

import java.util.ArrayList;
import java.util.List;

import kafka.javaapi.PartitionMetadata;
import kafka.javaapi.TopicMetadata;
import kafka.javaapi.TopicMetadataRequest;
import kafka.javaapi.TopicMetadataResponse;
import kafka.javaapi.consumer.SimpleConsumer;

public class KafkaSimpleConsumer {

    public static void main(String[] args) {
        SimpleConsumer consumer = null;
        consumer = new SimpleConsumer(args[0], Integer.parseInt(args[1]),
                100000, 64 * 1024, "leaderLookup");
        List<String> topics = new ArrayList<String>();
        topics.add(args[2]);
        TopicMetadataRequest req = new TopicMetadataRequest(topics);
        TopicMetadataResponse resp = consumer.send(req);
        for (TopicMetadata meta : resp.topicsMetadata()) {
            for (PartitionMetadata part: meta.partitionsMetadata()) {
                System.out.println(part.leader()+"-"+part.isr()+"-"+part.replicas());
            }
        }
        System.out.println(resp.topicsMetadata());
    }
}
