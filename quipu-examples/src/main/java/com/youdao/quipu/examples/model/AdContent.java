package com.youdao.quipu.examples.model;

import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@DatabaseTable(tableName = "AdContent")
public class AdContent {
    @DatabaseField(id = true)
    private long AD_CONTENT_ID;

    @DatabaseField
    private int MIME_WIDTH;

    @DatabaseField
    private int MIME_HEIGHT;

    @DatabaseField
    private int TYPE;
}
