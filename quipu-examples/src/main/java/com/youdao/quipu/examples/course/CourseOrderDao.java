package com.youdao.quipu.examples.course;

import com.alibaba.fastjson.JSONObject;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import outfox.course.enigma.client.Enigma;
import outfox.course.enigma.client.EnigmaConfig;
import outfox.course.enigma.client.transport.EnigmaClient;

import java.sql.ResultSet;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

/**
 * 跟精品课转化数据相关的数据表名称
 * <AUTHOR>
 */
@Slf4j
class CourseOrderDao {
    private HikariDataSource hikariDataSource;
    private JdbcTemplate jdbcTemplate;
    private CourseOrderMapper courseOrderMapper = new CourseOrderMapper();
    private static final Enigma enigma = new Enigma(EnigmaClientConfig.getEnigmaConfig());
    private static final String OUT_VENDOR = "outVendor";
    private static final String IMEI = "imei";

    CourseOrderDao(String jdbcUrl) {
        hikariDataSource = initHikariDataSouce(jdbcUrl);
        jdbcTemplate = new JdbcTemplate(hikariDataSource);
    }

    private HikariDataSource initHikariDataSouce(String jdbcUrl) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(jdbcUrl);
        hikariConfig.setMaximumPoolSize(1);
        hikariConfig.setConnectionTimeout(30000);
        // 7.5小时生命周期，因为mysql的生命周期是8小时
        hikariConfig.setMaxLifetime(27000000);
        Properties dataSourceProperties = new Properties();
        dataSourceProperties.setProperty("cachePrepStmts", "true");
        dataSourceProperties.setProperty("prepStmtCacheSize", "250");
        dataSourceProperties.setProperty("prepStmtCacheSqlLimit", "2048");
        dataSourceProperties.setProperty("useServerPrepStmts", "true");
        dataSourceProperties.setProperty("useLocalSessionState", "true");
        dataSourceProperties.setProperty("rewriteBatchedStatements", "true");
        dataSourceProperties.setProperty("cacheResultSetMetadata", "true");
        dataSourceProperties.setProperty("elideSetAutoCommits", "true");
        dataSourceProperties.setProperty("maintainTimeStats", "false");
        hikariConfig.setDataSourceProperties(dataSourceProperties);
        return new HikariDataSource(hikariConfig);
    }

    /**
     * 获得昨天的精品课订单数据
     */
    List<CourseOrder> getData(String sql) {
        if (hikariDataSource.isClosed()) {
            return Collections.emptyList();
        }
        List<CourseOrder> courseOrderList = jdbcTemplate.query(sql, courseOrderMapper);
        if (null == courseOrderList) {
            return Collections.emptyList();
        }
        return courseOrderList;
    }

    void close() {
        hikariDataSource.close();
    }



    private static class CourseOrderMapper implements RowMapper<CourseOrder> {


        @Override
        public CourseOrder mapRow(ResultSet rs, int rowNum) {
            CourseOrder courseOrder = new CourseOrder();
            try {
                courseOrder.setId(rs.getLong("Id"));
                courseOrder.setCreateTime(rs.getLong("createTime"));
                courseOrder.setVendor(rs.getString("vendor"));
                courseOrder.setPlatform(Objects.toString(rs.getString("platform"), ""));
                courseOrder.setDetail(rs.getString("detail"));
                courseOrder.setDetailEncrypt(rs.getString("detail_encrypt"));
                courseOrder.setDetail(this.getRowDetail(courseOrder.getDetail(), courseOrder.getDetailEncrypt()));
                JSONObject detailJson =  JSONObject.parseObject(courseOrder.getDetail());
                String imei = detailJson.getString(IMEI);
                courseOrder.setImei(imei);
                String outVendor = detailJson.getString(OUT_VENDOR);
                courseOrder.setOutVendor(outVendor);
                CourseOrder.BidIdAndSponsorId bidIdAndSponsorId = CourseOrder.extractBidIdAndSponsorIdFromOutVendor(outVendor);
                courseOrder.setBidId(bidIdAndSponsorId.getBidId());
                courseOrder.setSponsorId(bidIdAndSponsorId.getSponsorId());
                courseOrder.setProductId(rs.getLong("product_Id"));
                courseOrder.setUserId(rs.getString("userId"));
                return courseOrder;
            } catch (Exception e) {
                log.error("Table course_orders has wrong data, skip, rowNum = {}", rowNum, e);
                return courseOrder;
            }
        }

        /**
         * 判断是否需要解密detail字段并解密
         * @return 明文的detail内容
         */
        private String getRowDetail(String detail, String detailEncrypt) {
            try {
                if (StringUtils.isNotBlank(detail) &&
                        !StringUtils.startsWith(detail, EnigmaClientConfig.getENCRYPT_TEXT_PREFIX())) {
                    // 原始数据非空，且不是加密数据，可以直接用
                    return detail;
                } else {
                    // 需要解密
                    if (StringUtils.startsWith(detailEncrypt, EnigmaClientConfig.getENCRYPT_TEXT_PREFIX())) {
                        return enigma.decrypt(detailEncrypt);
                    } else {
                        log.error("Detail and detailEncrypt both empty or wrong, check datasource!!!, detail:{}, detail_encrypt:{}.", detail, detailEncrypt);
                        return StringUtils.EMPTY;
                    }
                }
            } catch (Exception e) {
                log.error("Encrypt data error, detail:{}, detail_encrypt:{}.",detail, detailEncrypt, e);
                return StringUtils.EMPTY;
            }
        }
    }
}
