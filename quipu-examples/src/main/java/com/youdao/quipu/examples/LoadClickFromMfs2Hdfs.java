package com.youdao.quipu.examples;

import com.youdao.quipu.avro.schema.SdkClick;
import com.youdao.quipu.examples.model.Click;
import com.youdao.quipu.online.ClickKafkaProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.file.CodecFactory;
import org.apache.avro.file.DataFileWriter;
import org.apache.avro.io.DatumWriter;
import org.apache.avro.specific.SpecificDatumWriter;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import outfox.venus.client.VenusException;

import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.youdao.quipu.online.ClickKafkaProducer.CLICK_TYPE_CHARGED;

/**
 * 从ODFS导入Click日志到HDFS：odis.sh dump点击 -> 本地生成avro文件 -> hdfs dfs put。目的是导入HDFS上未保存（2015年以前，或各种原因丢失）的点击数据
 * 使用方法：
 *  env JAVA_HOME=/global/share/jdk1.8.0_131 PATH=$JAVA_HOME/bin:$PATH MAVEN_OPTS='-Xmx10G -XX:+UseG1GC' mvn exec:java -Dexec.mainClass="com.youdao.quipu.examples.LoadClickFromMfs2Hdfs" -Dexec.args="2018/08/16 2018/09/01 mfs"
 */
@Slf4j
public class LoadClickFromMfs2Hdfs {
    private static final String mfsBasePath = "/disk3/mfs/logs/click/";
    private static final String odisDataBasePath = "/disk2/zhaown/m6.6.8.10/";
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
    private static final SimpleDateFormat dateFormat_ = new SimpleDateFormat("yyyyMMdd");

    private static final FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyyMMddHHmmss");
    private static final FastDateFormat filePathDateFormat = FastDateFormat.getInstance("yyyy/MM/dd/HH");
    private static ClickKafkaProducer clickKafkaProducer;
    /**
     *
     * @param args args[0]:startDate as yyyy/MM/dd, args[1]:endDate as yyyy/MM/dd args[2]: file type, so far only support mfs|odis.
     */
    public static void main(String[] args) throws Exception {
        Date startDate = dateFormat.parse(args[0]);
        Date endDate = dateFormat.parse(args[1]);
        String fileType = args[2];

        clickKafkaProducer = new ClickKafkaProducer();
        clickKafkaProducer.loadPropertiesConfiguration();
        clickKafkaProducer.initAll();
        clickKafkaProducer.registerShutdownHook();

        log.info("start load data from {} to {}", startDate, endDate);

        doJob(startDate, endDate, fileType);

        log.info("job done!");

        clickKafkaProducer.close();
        // 目前依赖的venus-client释放资源有问题，导致进程不会退出。
        System.exit(0);
    }

    private static void doJob(Date startDate, Date endDate, String fileType) {
        Date tmpDate = new Date(startDate.getTime());
        while (tmpDate.before(endDate)) {
            try {
                List<Click> clicks;
                if ("mfs".equals(fileType)) {
                    clicks = loadOneDayClick(tmpDate);
                } else if ("odis".equals(fileType)) {
                    clicks = loadOneDayClickForOdis(tmpDate);
                } else {
                    throw new IllegalArgumentException("file type only support mfs|odis");
                }

                List<SdkClick> clicksAvro = convert2Avro(clicks);

                writeOneDayToFile(clicksAvro);

                log.info("task done with date " + fastDateFormat.format(tmpDate));
            } catch (Exception e) {
                log.error("got error with date {}", tmpDate, e);
            }
            tmpDate = DateUtils.addDays(tmpDate, 1);
        }
    }

    private static void writeOneDayToFile(List<SdkClick> clicks) throws IOException {
        HashMap<String, List<SdkClick>> map = new HashMap<>();
        for (SdkClick click : clicks) {
            if (null != click) {
                List<SdkClick> list = map.computeIfAbsent(filePathDateFormat.format(click.getTimestamp()) + "/fromfs.avro", k -> new LinkedList<>());
                list.add(click);
            } else {
                log.error("null click!!!");
            }
        }

        for (Map.Entry<String, List<SdkClick>> entry : map.entrySet()) {
            writeOneHourToFile(entry.getValue(), entry.getKey());
        }
    }

    private static void writeOneHourToFile(List<SdkClick> clicks, String path) throws IOException {

        log.info(String.format("Start to write to file: %s, size: %d.", path, clicks.size()));

        File file = new File(path);
        file.getParentFile().mkdirs();
        DatumWriter<SdkClick> userDatumWriter = new SpecificDatumWriter<>(SdkClick.class);
        DataFileWriter<SdkClick> dataFileWriter = new DataFileWriter<>(userDatumWriter);
        dataFileWriter.setCodec(CodecFactory.deflateCodec(6));
        dataFileWriter.create(SdkClick.getClassSchema(), file);

        for (SdkClick click : clicks) {
            dataFileWriter.append(click);
        }
        dataFileWriter.close();

        log.info(String.format("Finish write to file: %s, size: %d.", path, clicks.size()));
    }

    private static List<SdkClick> convert2Avro(@NotNull List<Click> clicks) throws SQLException {

        log.info("Start to convert clicks, size: " + clicks.size());

        AtomicInteger i = new AtomicInteger(0);
        List<SdkClick> clicksInAvro = clicks.parallelStream().map(c-> {
            try {
                SdkClick clickInAvro = (SdkClick)clickKafkaProducer.transformSdkClick(c, CLICK_TYPE_CHARGED);
                log.info("add click " + i.addAndGet(1) + "/" + clicks.size());
                return clickInAvro;
            } catch (SQLException e) {
                log.error("", e);
                return null;
            }
        }).collect(Collectors.toList());

        log.info("Finish convert clicks, click size: " + clicks.size() + " in avro with size: " + clicksInAvro.size());

        return clicksInAvro;
    }

    private static List<Click> loadOneDayClickForOdis(Date date) {
        List<Click> clicks = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        File file = new File(odisDataBasePath + calendar.get(Calendar.YEAR) +"/" +  dateFormat_.format(date) + ".clk");
        log.info("Reading one day's file : " + file.getAbsolutePath());

        try {
            log.info("Reading one file : " + file.getAbsolutePath());
            Files.readAllLines(file.toPath()).forEach(line -> {
                if (!line.startsWith("Running tool dump")) {
                    line = StringUtils.removeStart(line, "0 ^ ");
                    String[] field = line.split(",");
                    if (field.length != 20) {
                        log.error(String.format("Wrong click: %s, file: %s", line, file.getAbsolutePath()));
                        System.exit(-1);
                    }
                    Click click = null;
                    try {
                        click = parseClick(field);
                    } catch (ParseException e) {
                        log.error(e.toString(), e);
                    }
                    clicks.add(click);
                }
            });
        } catch (IOException e) {
            log.error(e.toString(), e);
        }

        log.info("Read clicks size: " + clicks.size());

        return clicks;
    }

    private static List<Click> loadOneDayClick(Date date) throws IOException {
        List<Click> clicks = new ArrayList<>();

        File directory = new File(mfsBasePath + dateFormat.format(date));
        log.info("Reading one day's file in: " + directory.getAbsolutePath());

        FileUtils.iterateFiles(directory, null, false)
                .forEachRemaining(file -> {
                    try {
                        log.info("Reading one file : " + file.getAbsolutePath());
                        Files.readAllLines(file.toPath()).forEach(line -> {

                            String[] field = line.split(",");
                            if (field.length != 20) {
                                log.error(String.format("Wrong click: %s, file: %s", line, file.getAbsolutePath()));
                                System.exit(-1);
                            }
                            Click click = null;
                            try {
                                click = parseClick(field);
                            } catch (ParseException e) {
                                log.error(e.toString(), e);
                            }
                            clicks.add(click);
                        });
                    } catch (IOException e) {
                        log.error(e.toString(), e);
                    }
                });

        log.info("Read clicks size: " + clicks.size());

        return clicks;
    }

    private static Click parseClick(String[] fields) throws ParseException {
        long id = 0;
        long sponsorId = Long.parseLong(fields[1]);
        long campaignId = Long.parseLong(fields[2]);
        long adgroupId = Long.parseLong(fields[3]);
        long advariationId = Long.parseLong(fields[4]);
        long keywordId = Long.parseLong(fields[5]);
        long syndId = Long.parseLong(fields[6]);
        long siteId = Long.parseLong(fields[7]);
        int imprPos = Integer.parseInt(fields[8]);
        String imprIp = fields[9];
        String imprReq = fields[10];
        int origCost = Integer.parseInt(fields[11]);
        int actuCost = Integer.parseInt(fields[12]);
        float qualityScore = Float.parseFloat(fields[13]);
        float rank = Float.parseFloat(fields[14]);
        long imprTime = fastDateFormat.parse(fields[15]).getTime();
        long clickerId = Long.parseLong(fields[16]);
        String clickerIp = fields[17];
        String moreinfo = fields[18];
        String reffer = "";
        // 第一列存储的是commit time，mfs中目前并没有存储click time。
        long commitTime = fastDateFormat.parse(fields[0]).getTime();
        long clickTime = commitTime;
        long codeid = Long.parseLong(fields[19]);
        long memberid = 0;
        return new Click(
                id, sponsorId, campaignId, adgroupId, advariationId, keywordId, syndId, siteId, imprPos, qualityScore,
                rank, origCost, actuCost, imprIp, clickerIp, clickerId, imprTime, clickTime, commitTime, imprReq,
                reffer, moreinfo, codeid, memberid
        );
    }
}
