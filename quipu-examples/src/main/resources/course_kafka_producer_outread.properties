# 转化数据发往的topic
dsp_conv_topic=conv_dsp_avro
course_order_topic=course_order
# avro.repository.url
avro.repository.url=http://eadata-schema-repo.inner.youdao.com/schema-repo
# 发送转化数据的时间间隔，单位ms
producer_interval=60000
# 精品课数据库
course_db_url=****************************************************************************************
# 一次从db中拿出的最大记录数
course_db_max_load_data=2000
# 存储startId的文件的所在位置
start_id_file=/mfs_ead/eadata/quipu/quipu-examples/course-kafka-producer/start-id-outread.txt
