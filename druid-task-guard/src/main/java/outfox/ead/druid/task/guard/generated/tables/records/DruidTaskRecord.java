/*
 * This file is generated by jOOQ.
*/
package outfox.ead.druid.task.guard.generated.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;

import outfox.ead.druid.task.guard.generated.tables.DruidTask;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.10.7"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DruidTaskRecord extends UpdatableRecordImpl<DruidTaskRecord> implements Record6<Long, String, String, String, Integer, Timestamp> {

    private static final long serialVersionUID = 249431667;

    /**
     * Setter for <code>apolo.druid_task.id</code>.
     */
    public void setId(Long value) {
        set(0, value);
    }

    /**
     * Getter for <code>apolo.druid_task.id</code>.
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for <code>apolo.druid_task.task_id</code>.
     */
    public void setTaskId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>apolo.druid_task.task_id</code>.
     */
    public String getTaskId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>apolo.druid_task.status</code>.
     */
    public void setStatus(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>apolo.druid_task.status</code>.
     */
    public String getStatus() {
        return (String) get(2);
    }

    /**
     * Setter for <code>apolo.druid_task.palyload</code>.
     */
    public void setPalyload(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>apolo.druid_task.palyload</code>.
     */
    public String getPalyload() {
        return (String) get(3);
    }

    /**
     * Setter for <code>apolo.druid_task.retried_times</code>.
     */
    public void setRetriedTimes(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>apolo.druid_task.retried_times</code>.
     */
    public Integer getRetriedTimes() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>apolo.druid_task.create_time</code>.
     */
    public void setCreateTime(Timestamp value) {
        set(5, value);
    }

    /**
     * Getter for <code>apolo.druid_task.create_time</code>.
     */
    public Timestamp getCreateTime() {
        return (Timestamp) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<Long, String, String, String, Integer, Timestamp> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row6<Long, String, String, String, Integer, Timestamp> valuesRow() {
        return (Row6) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field1() {
        return DruidTask.DRUID_TASK.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return DruidTask.DRUID_TASK.TASK_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return DruidTask.DRUID_TASK.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return DruidTask.DRUID_TASK.PALYLOAD;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return DruidTask.DRUID_TASK.RETRIED_TIMES;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field6() {
        return DruidTask.DRUID_TASK.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long component1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String component2() {
        return getTaskId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String component3() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String component4() {
        return getPalyload();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer component5() {
        return getRetriedTimes();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp component6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getTaskId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getPalyload();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getRetriedTimes();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value6() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DruidTaskRecord value1(Long value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DruidTaskRecord value2(String value) {
        setTaskId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DruidTaskRecord value3(String value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DruidTaskRecord value4(String value) {
        setPalyload(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DruidTaskRecord value5(Integer value) {
        setRetriedTimes(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DruidTaskRecord value6(Timestamp value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DruidTaskRecord values(Long value1, String value2, String value3, String value4, Integer value5, Timestamp value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DruidTaskRecord
     */
    public DruidTaskRecord() {
        super(DruidTask.DRUID_TASK);
    }

    /**
     * Create a detached, initialised DruidTaskRecord
     */
    public DruidTaskRecord(Long id, String taskId, String status, String palyload, Integer retriedTimes, Timestamp createTime) {
        super(DruidTask.DRUID_TASK);

        set(0, id);
        set(1, taskId);
        set(2, status);
        set(3, palyload);
        set(4, retriedTimes);
        set(5, createTime);
    }
}
