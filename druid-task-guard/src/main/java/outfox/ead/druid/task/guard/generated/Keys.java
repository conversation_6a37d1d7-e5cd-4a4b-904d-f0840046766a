/*
 * This file is generated by jOOQ.
*/
package outfox.ead.druid.task.guard.generated;


import javax.annotation.Generated;

import org.jooq.Identity;
import org.jooq.UniqueKey;
import org.jooq.impl.Internal;

import outfox.ead.druid.task.guard.generated.tables.DruidTask;
import outfox.ead.druid.task.guard.generated.tables.records.DruidTaskRecord;


/**
 * A class modelling foreign key relationships and constraints of tables of 
 * the <code>apolo</code> schema.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.10.7"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // IDENTITY definitions
    // -------------------------------------------------------------------------

    public static final Identity<DruidTaskRecord, Long> IDENTITY_DRUID_TASK = Identities0.IDENTITY_DRUID_TASK;

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<DruidTaskRecord> KEY_DRUID_TASK_PRIMARY = UniqueKeys0.KEY_DRUID_TASK_PRIMARY;

    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------


    // -------------------------------------------------------------------------
    // [#1459] distribute members to avoid static initialisers > 64kb
    // -------------------------------------------------------------------------

    private static class Identities0 {
        public static Identity<DruidTaskRecord, Long> IDENTITY_DRUID_TASK = Internal.createIdentity(DruidTask.DRUID_TASK, DruidTask.DRUID_TASK.ID);
    }

    private static class UniqueKeys0 {
        public static final UniqueKey<DruidTaskRecord> KEY_DRUID_TASK_PRIMARY = Internal.createUniqueKey(DruidTask.DRUID_TASK, "KEY_druid_task_PRIMARY", DruidTask.DRUID_TASK.ID);
    }
}
