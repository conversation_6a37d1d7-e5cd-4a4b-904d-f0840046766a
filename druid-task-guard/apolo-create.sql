CREATE DATABASE `apolo` /*!40100 DEFAULT CHARACTER SET utf8 */;
CREATE TABLE `druid_task` (
  `id` bigint(64) NOT NULL AUTO_INCREMENT,
  `task_id` varchar(1024) NOT NULL,
  `status` enum('RUNNING','FAILED','<PERSON>UCCE<PERSON>','RETRI<PERSON>','GIVEUP','<PERSON><PERSON><PERSON>OW<PERSON>') NOT NULL DEFAULT 'UNKNOWN',
  `palyload` text NOT NULL,
  `retried_times` int(11) NOT NULL DEFAULT '0',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8;