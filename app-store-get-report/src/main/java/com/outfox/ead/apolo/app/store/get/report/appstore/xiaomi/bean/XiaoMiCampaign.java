package com.outfox.ead.apolo.app.store.get.report.appstore.xiaomi.bean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 小米广告计划
 *
 * <AUTHOR>
 * @date 2021/5/21 14:29
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class XiaoMiCampaign {
    /**
     * 激活量
     */
    private String activeSumFormat;
    /**
     * 点击激活率
     */
    private String clickActiveRatioFormat;
    /**
     * 点击注册率
     */
    private String clickRegisterRatioFormat;
    /**
     * 点击留存率
     */
    private String clickRetentRatioFormat;
    /**
     * 点击量
     */
    private String clickSumFormat;
    /**
     * 花费
     */
    private String costFormat;
    /**
     * 激活均价
     */
    private String costPerActiveFormat;
    /**
     * 下载均价
     */
    private String costPerDownloadFormat;
    /**
     * 注册均价
     */
    private String costPerRegisterFormat;
    /**
     * 留存均价
     */
    private String costPerRetentFormat;
    /**
     * 点击均价
     */
    private String cpcFormat;
    /**
     * 点击率
     */
    private String ctrFormat;
    /**
     * 下载激活率
     */
    private String downActiveRatioFormat;
    /**
     * 下载注册率
     */
    private String downRegisterRatioFormat;
    /**
     * 下载留存率
     */
    private String downRetentRatioFormat;
    /**
     * 下载率
     */
    private String downloadRatioFormat;
    /**
     * 下载量
     */
    private String downloadSumFormat;
    /**
     * ECPM
     */
    private String ecpmFormat;
    /**
     * 曝光激活率
     */
    private String imprActiveRatioFormat;
    /**
     * 曝光注册率
     */
    private String imprRegisterRatioFormat;
    /**
     * 曝光留存率
     */
    private String imprRetentRatioFormat;
    /**
     * 注册量
     */
    private String registerSumFormat;
    /**
     * 留存量
     */
    private String retentSumFormat;
    /**
     * 曝光量
     */
    private String viewSumFormat;
    /**
     * ⼴告计划投放预算
     */
    private String budget;
    /**
     * ⼴告计划投放结束时间
     */
    private String endDate;
    /**
     * ⼴告计划ID
     */
    private String id;
    /**
     * ⼴告计划名称
     */
    private String name;
    /**
     * ⼴告计划投放开始时间
     */
    private String startDate;
    /**
     * ⼴告计划类型
     */
    private String type;
    /**
     * 表单提交量
     */
    private String formSubmitSumFormat;
}
