package com.outfox.ead.apolo.app.store.get.report;

import com.outfox.ead.apolo.app.store.get.report.appstore.huawei.HuaWeiAppStoreService;
import com.outfox.ead.apolo.app.store.get.report.base.TaskParams;
import com.outfox.ead.apolo.app.store.get.report.persistent.dao.base.BaseRepositoryImpl;
import com.outfox.ead.apolo.app.store.get.report.task.GetHuaWeiAdTaskStatisticReportTask;
import com.outfox.ead.apolo.app.store.get.report.task.GetHuaWeiDownloadExportTask;
import com.outfox.ead.apolo.app.store.get.report.task.Task;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 实时更新广告报表
 *
 * <AUTHOR>
 * @date 2021/5/6 14:29
 */
@Slf4j
@EnableTransactionManagement
@EnableJpaRepositories(repositoryBaseClass = BaseRepositoryImpl.class)
@EnableJpaAuditing
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScans(value = {
        @ComponentScan(basePackageClasses = HuaWeiAppStoreService.class),
        @ComponentScan(basePackages = "com.outfox.ead.apolo.app.store.get.report.task",
                useDefaultFilters = false, includeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                        classes = {GetHuaWeiAdTaskStatisticReportTask.class, GetHuaWeiDownloadExportTask.class})
        }),
        @ComponentScan(basePackages = "com.outfox.ead.apolo.app.store.get.report.persistent")
})
public class PullAppStoreAdReportRealTimeJob extends AzkabanSpringBootBaseJob {
    private static final String JOB_FILE = "classpath:pull-appstore_ad_report_realtime.job";

    public static void main(String[] args) {
        try {
            ApplicationContext context = startUp(JOB_FILE, PullAppStoreAdReportRealTimeJob.class);
            Task task = context.getBean("task_report", Task.class);
            String[] appIds = context.getEnvironment().getRequiredProperty("huawei.app_ids").split(",");
            LocalDateTime now = LocalDateTime.now();
            TaskParams params = new TaskParams();
            params.setStartDatetime(now);
            params.setEndDatetime(now);
            params.setAppIdList(Arrays.asList(appIds));
            task.run(params);
        } catch (Exception e) {
            log.error("任务执行异常。", e);
            // 确认异常，将异常情况反馈给azkaban
            System.exit(1);
        }
    }
}