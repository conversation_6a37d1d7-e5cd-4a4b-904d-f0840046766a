package com.outfox.ead.apolo.app.store.get.report.appstore.vivo.bean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.outfox.ead.apolo.app.store.get.report.base.AppStoreType;
import com.outfox.ead.apolo.app.store.get.report.persistent.dto.AdTaskStatisticDTO;
import lombok.Data;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * 指标后的（Z）代表该指标以转化时间归因统计，即某个转化在11日产生，则该口径下，转化会被记在11日；转化事件口径的指标时效性高，方便实时决策。且较为稳定，一般T+1数据就不再变动；指标后的（J）代表该指标以计费时间归因统计，即某个转化在11日产生，但带来这个转化的点击发生在10日，则当次转化会被记在10日。计费口径数据能够更准确衡量广告ROI，但数据回流慢，一般需要T+7，统计数据才趋于稳定
 *
 * <AUTHOR>
 * @date 2021/5/11 15:22
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdStatement {
    /**
     * 广告主
     */
    private String advertiserId;

    /**
     * 日期:20190812
     */
    private Integer reportDate;

    /**
     * 可能为空
     */
    private Date reportTime;

    /**
     * 创意
     */
    private Long creativeId;
    /**
     * 广告ID
     */
    private Long advertisementId;

    /**
     * 广告名称
     */
    private String advertisementName;
    /**
     * 广告组ID
     */
    private Long groupId;
    /**
     * 广告组
     */
    private String groupName;
    /**
     * 广告计划
     */
    private Long campaignId;
    /**
     * 广告计划
     */
    private String campaignName;
    /**
     * 包名
     */
    private String appPackage;
    /**
     * 广告位类型：1-信息流；2-开屏；3-banner；4-插屏广告；5-原生广告；9-激励视频；20-推荐列表;21-搜索提词;22-搜索拓词;23-装机必备;24-积分下载;25-运营活动;26-升版推荐;27-红包激励；28-优选流量；29-激励广告；
     */
    private Integer placeType;
    /**
     * 展示量
     */
    private Long showCount;
    /**
     * 点击量
     */
    private Long clickCount;
    /**
     * 下载量（Z）
     */
    private Long downloadCount;
    /**
     * 花费
     */
    private Double spent;
    /**
     * 转化目标：1-下载；2-游戏注册；3-新增激活；4-表单提交；5-自定义激活；6-自定义注册；7-添加到桌面8-自定义留存9-游戏付费10-自定义付费13-自定义拉活14-网页购买100- 普通激活(商店广告)；101- 新增激活(商店广告)；102-自定义激活(商店广告)；103-自定义注册(商店广告)104-自定义次留(商店广告)105-游戏注册(商店广告)
     */
    private Integer cvType;

    /**
     * 自定义激活数（Z）
     */
    private  Long backActivateCount;

    /**
     * 游戏注册数（Z）
     */
    private  Long registerCount;
    /**
     * 新增激活数（Z）
     */
    private  Long activateCount;
    /**
     * 普通激活数（Z）
     */
    private  Long normalActivateCount;
    /**
     * 加桌数（Z）
     */
    private  Long addDesktopCount;
    /**
     * 自定义注册数（Z）
     */
    private  Long backRegisterCount;
    /**
     * 自定义次留数（Z）
     */
    private  Long customRetainCount;
    /**
     * 自定义付费数（Z）
     */
    private  Long customPayCount;
    /**
     * 游戏付费数（Z）
     */
    private  Long gamePayCount;
    /**
     * 自定义拉活（Z）
     */
    private  Long reactivation;
    /**
     * 网页购买（Z）
     */
    private  Long webPay;
    /**
     * 自定义激活数(J)
     */
    private  Long backActivateC;
    /**
     * 新增激活数(J)
     */
    private  Long activateC;
    /**
     * 游戏注册数(J)
     */
    private  Long registerC;
    /**
     * 自定义注册数(J)
     */
    private  Long backRegisterC;
    /**
     * 加桌数(J)
     */
    private  Long addDesktopC;
    /**
     * 自定义次留数(J)
     */
    private  Long customRetainC;
    /**
     * 游戏付费数(J)
     */
    private  Long gamePayC;
    /**
     * 自定义付费数(J)
     */
    private  Long customPayC;
    /**
     * 下载数(J)
     */
    private  Long cDownloadCount;
    /**
     * 自定义拉活(J)
     */
    private  Long reactivationC;
    /**
     * 网页购买（J）
     */
    private  Long webPayC;
    /**
     * 表单提交数
     */
    private  Long formSubmitCount;

    public AdTaskStatisticDTO getAdTaskStatisticDTO() {
        return AdTaskStatisticDTO.builder()
                .date(getConvertDateFormat())
                .appPackage(appPackage)
                .appStoreType(AppStoreType.VIVO)
                .adId(String.valueOf(advertisementId))
                .adName(advertisementName)
                .channel(Objects.nonNull(placeType) && Objects.nonNull(VivoPlaceType.valueOf(placeType)) ?
                        VivoPlaceType.valueOf(placeType).getChannel() : null)
                .advertiserId(advertiserId)
                .click(clickCount)
                .cost(spent.doubleValue())
                .download(downloadCount)
                .exposure(showCount)
                .mediaChannel(Objects.nonNull(placeType) && Objects.nonNull(VivoPlaceType.valueOf(placeType)) ?
                        VivoPlaceType.valueOf(placeType).getName(): null)
                .creativeId(String.valueOf(creativeId))
                .groupId(String.valueOf(groupId))
                .groupName(groupName)
                .planId(String.valueOf(campaignId))
                .planName(campaignName)
                .build();
    }

    private String getConvertDateFormat() {
        if (Objects.nonNull(reportDate)) {
            LocalDate date = LocalDate.parse(String.valueOf(reportDate), DateTimeFormatter.BASIC_ISO_DATE);
            return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
        }
        return null;
    }
}
