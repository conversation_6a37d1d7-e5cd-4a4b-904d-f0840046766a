package com.outfox.ead.apolo.app.store.get.report.persistent.dto;

import com.outfox.ead.apolo.app.store.get.report.base.AppStoreType;
import com.outfox.ead.apolo.app.store.get.report.persistent.po.DownloadInstallReport;
import com.outfox.ead.apolo.app.store.get.report.persistent.po.DownloadInstallReportPrimaryKey;
import lombok.Builder;
import lombok.Data;

/**
 * 下载安装报表DTO
 * <AUTHOR>
 * @date 2021/4/27 12:01
 */
@Data
@Builder
public class DownloadInstallReportDTO {
    private String date;
    private String appId;
    private AppStoreType appStoreType;
    private Long impressions;
    private Long detailsPageViews;
    private Long totalDownloads;
    private Long updateDownloads;
    private Long newDownloads;
    private Long unloads;
    private String ctr;
    private String cvrDetailPage;
    private String installDownloadsRate;

    public DownloadInstallReport getDownloadInstallReport() {
        DownloadInstallReportPrimaryKey primaryKey = DownloadInstallReportPrimaryKey.builder()
                .date(date)
                .appId(appId)
                .appStoreType(appStoreType)
                .build();
        return DownloadInstallReport.builder()
                .id(primaryKey)
                .impressions(impressions)
                .detailsPageViews(detailsPageViews)
                .totalDownloads(totalDownloads)
                .updateDownloads(updateDownloads)
                .newDownloads(newDownloads)
                .unloads(unloads)
                .ctr(ctr)
                .cvrDetailPage(cvrDetailPage)
                .installDownloadsRate(installDownloadsRate)
                .build();
    }
}
