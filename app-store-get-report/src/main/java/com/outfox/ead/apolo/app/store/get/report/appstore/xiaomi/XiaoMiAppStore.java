package com.outfox.ead.apolo.app.store.get.report.appstore.xiaomi;

import com.outfox.ead.apolo.app.store.get.report.appstore.xiaomi.bean.*;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;

/**
 * 小米http接口定义
 *
 * <AUTHOR>
 * @date 2021/5/20 14:56
 */
public interface XiaoMiAppStore {

    /**
     * 获取广告创意列表
     * @param queryMap
     * @return
     */
    @GET("/creative/list")
    @Headers(value = {
            "Content-Type: application/json;charset=utf-8"
    })
    Call<XiaoMiResponse<XiaoMiResult<XiaoMiCreative>>> getCreativeList(@QueryMap Map<String, Object> queryMap);

    /**
     * 获取商城搜索报表
     * @param queryMap
     * @return
     */
    @GET("/tools/searchKey/report")
    Call<XiaoMiResponse<XiaoMiResult<XiaoMiSearchKey>>> getSearchKeyReport(@QueryMap Map<String, Object> queryMap);
}
