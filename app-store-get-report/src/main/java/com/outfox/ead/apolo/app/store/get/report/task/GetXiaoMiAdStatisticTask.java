package com.outfox.ead.apolo.app.store.get.report.task;

import com.outfox.ead.apolo.app.store.get.report.appstore.xiaomi.XiaoMiAppStoreService;
import com.outfox.ead.apolo.app.store.get.report.appstore.xiaomi.bean.XiaoMiCreative;
import com.outfox.ead.apolo.app.store.get.report.appstore.xiaomi.bean.XiaoMiSearchKey;
import com.outfox.ead.apolo.app.store.get.report.base.TaskParams;
import com.outfox.ead.apolo.app.store.get.report.persistent.service.AdTaskStatisticService;
import com.outfox.ead.apolo.app.store.get.report.persistent.service.SearchAdTaskStatisticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 同步xiaomi广告统计和关键词统计报表
 * <p>
 * 广告计划 -> 广告组 -> 广告创意
 *
 * <AUTHOR>
 * @date 2021-05-21
 */
@Service("task_xiaomi")
@Slf4j
public class GetXiaoMiAdStatisticTask implements Task {

    private final XiaoMiAppStoreService xiaoMiAppStoreService;
    private final AdTaskStatisticService adTaskStatisticService;
    private final SearchAdTaskStatisticService searchAdTaskStatisticService;

    @Autowired
    GetXiaoMiAdStatisticTask(XiaoMiAppStoreService xiaoMiAppStoreService,
                             AdTaskStatisticService adTaskStatisticService,
                             SearchAdTaskStatisticService searchAdTaskStatisticService) {
        this.xiaoMiAppStoreService = xiaoMiAppStoreService;
        this.adTaskStatisticService = adTaskStatisticService;
        this.searchAdTaskStatisticService = searchAdTaskStatisticService;
    }

    @Override
    public void run(TaskParams taskParams) throws Exception {
        LocalDate endDate = taskParams.getEndDatetime().toLocalDate();
        LocalDate startDate = taskParams.getStartDatetime().toLocalDate();

        while (startDate.toEpochDay() <= endDate.toEpochDay()) {
            // 搜索词接口不支持查询当天内容
            processTask(startDate, (!startDate.isEqual(LocalDate.now())));
            startDate = startDate.plusDays(1);
        }
    }

    private void processTask(LocalDate date, boolean searchKey) throws Exception {
        List<XiaoMiCreative> creativeList = xiaoMiAppStoreService.getCreativeList(date);

        Map<String, XiaoMiCreative> creativeMap = creativeList.stream()
                .collect(Collectors.toMap(XiaoMiCreative::getId, Function.identity()));
        String dateStr = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
//        将相同adId 和搜索词的key 聚合， xiaomi不同的cpd会单列出来。
        if (searchKey) {
            List<XiaoMiSearchKey> searchCreativeList = xiaoMiAppStoreService.getSearchKey(date)
                    .stream()
                    .collect(Collectors.groupingBy(xiaoMiSearchKey -> xiaoMiSearchKey.getSearchKeyTypeFormat() +
                            xiaoMiSearchKey.getSearchKey() + xiaoMiSearchKey.getAdCreativeId()))
                    .values()
                    .stream()
                    .map(list -> {
                        XiaoMiSearchKey result = list.get(0);
                        for (int i = 1; i < list.size(); i++) {
                            XiaoMiSearchKey tempKey = list.get(i);
                            result.setCost(BigDecimal.valueOf(result.getCost())
                                    .add(BigDecimal.valueOf(tempKey.getCost())).doubleValue());
                            result.setDownloadNum(result.getDownloadNum() + tempKey.getDownloadNum());
                        }
                        return result;
                    }).collect(Collectors.toList());

            // 保存搜索广告报表
            searchAdTaskStatisticService.batchInsertOrUpdateOppoXiaoMiStatistic(searchCreativeList
                    .stream()
                    .map(sc -> sc.getSearchAdTaskStatistic(date, creativeMap))
                    .collect(Collectors.toList()));
            log.info("{}, 获取小米搜索广告报表{}条。", dateStr, searchCreativeList.size());
        }

        // 保存创意报表
        adTaskStatisticService.batchInsertOrUpdateXiaoMiStatistic(creativeList.stream().map(creative -> creative
                .getAdTaskStatisticDTO(date))
                .collect(Collectors.toList()));
        log.info("{}, 获取小米广告报表{}条。", dateStr, creativeList.size());
    }
}
