package com.outfox.ead.apolo.app.store.get.report;

import com.outfox.ead.apolo.app.store.get.report.appstore.huawei.HuaWeiAppStoreService;
import com.outfox.ead.apolo.app.store.get.report.base.TaskParams;
import com.outfox.ead.apolo.app.store.get.report.persistent.dao.base.BaseRepositoryImpl;
import com.outfox.ead.apolo.app.store.get.report.task.GetHuaWeiAdTaskStatisticReportTask;
import com.outfox.ead.apolo.app.store.get.report.task.GetHuaWeiDownloadExportTask;
import com.outfox.ead.apolo.app.store.get.report.task.Task;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * 每日一次更新上一日的广告报表数据
 *
 * <AUTHOR>
 * @date 2021/5/6 14:29
 */
@Slf4j
@EnableTransactionManagement
@EnableJpaRepositories(repositoryBaseClass = BaseRepositoryImpl.class)
@EnableJpaAuditing
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScans(value = {
        @ComponentScan(basePackageClasses = HuaWeiAppStoreService.class),
        @ComponentScan(basePackages = "com.outfox.ead.apolo.app.store.get.report.task",
                useDefaultFilters = false, includeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                        classes = {GetHuaWeiAdTaskStatisticReportTask.class, GetHuaWeiDownloadExportTask.class})
        }),
        @ComponentScan(basePackages = "com.outfox.ead.apolo.app.store.get.report.persistent")
})
public class PullAppStoreAdReportJob extends AzkabanSpringBootBaseJob {

    private static final String JOB_CONFIG = "classpath:pull-appstore_ad_report.job";

    public static void main(String[] args) {
        try {
            ApplicationContext context = startUp(JOB_CONFIG, PullAppStoreAdReportJob.class);
            Task task = context.getBean(GetHuaWeiAdTaskStatisticReportTask.class);
            String startDate = context.getEnvironment().getProperty("huawei.ad.report.job.startDate");
            String endDate = context.getEnvironment().getProperty("huawei.ad.report.job.endDate");
            String[] appIds = context.getEnvironment().getRequiredProperty("huawei.app_ids").split(",");
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime start = now.minusDays(1);
            LocalDateTime end = now.minusDays(1);
            if (!StringUtils.isEmpty(startDate)) {
                start = convertLocalDateTime(startDate);
            }
            if (!StringUtils.isEmpty(endDate)) {
                end = convertLocalDateTime(endDate);
            }
            TaskParams params = new TaskParams();
            params.setStartDatetime(start);
            params.setEndDatetime(end);
            params.setAppIdList(Arrays.asList(appIds));
            task.run(params);
        } catch (Exception e) {
            log.error("任务执行异常。", e);
            // 确认异常，将异常情况反馈给azkaban
            System.exit(1);
        }
    }
}
