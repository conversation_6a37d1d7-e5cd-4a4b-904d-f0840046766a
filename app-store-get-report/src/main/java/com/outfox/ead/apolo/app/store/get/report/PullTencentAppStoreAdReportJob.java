package com.outfox.ead.apolo.app.store.get.report;

import com.outfox.ead.apolo.app.store.get.report.appstore.tencent.TencentAppStore;
import com.outfox.ead.apolo.app.store.get.report.base.TaskParams;
import com.outfox.ead.apolo.app.store.get.report.persistent.dao.base.BaseRepositoryImpl;
import com.outfox.ead.apolo.app.store.get.report.task.GetTencentAdStatementTask;
import com.outfox.ead.apolo.app.store.get.report.task.Task;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;


/**
 * 5:00PM 以后才能拉取
 * 更新应用宝报表
 *
 * <AUTHOR>
 * @date 2021-05-21
 */
@Slf4j
@EnableTransactionManagement
@EnableJpaRepositories(repositoryBaseClass = BaseRepositoryImpl.class)
@EnableJpaAuditing
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScans(value = {
        @ComponentScan(basePackageClasses = TencentAppStore.class),
        @ComponentScan(basePackages = "com.outfox.ead.apolo.app.store.get.report.task",
                useDefaultFilters = false, includeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = GetTencentAdStatementTask.class)
        }),
        @ComponentScan(basePackages = "com.outfox.ead.apolo.app.store.get.report.persistent")
})
public class PullTencentAppStoreAdReportJob extends AzkabanSpringBootBaseJob {

    private static final String JOB_CONFIG = "classpath:pull-tencent_appstore_ad_report.job";

    public static void main(String[] args) {
        try {
            ApplicationContext context = startUp(JOB_CONFIG, PullTencentAppStoreAdReportJob.class);
            Task task = context.getBean(GetTencentAdStatementTask.class);
            String startDate = context.getEnvironment().getProperty("tencent.ad.report.job.startDate");
            String endDate = context.getEnvironment().getProperty("tencent.ad.report.job.endDate");
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime start = now.minusDays(1);
            LocalDateTime end = now.minusDays(1);
            if (!StringUtils.isEmpty(startDate)) {
                start = convertLocalDateTime(startDate);
            }
            if (!StringUtils.isEmpty(endDate)) {
                end = convertLocalDateTime(endDate);
            }
            TaskParams params = new TaskParams();
            params.setStartDatetime(start);
            params.setEndDatetime(end);
            task.run(params);
        } catch (Exception e) {
            log.error("任务执行异常。", e);
            // 确认异常，将异常情况反馈给azkaban
            System.exit(1);
        }
    }
}
