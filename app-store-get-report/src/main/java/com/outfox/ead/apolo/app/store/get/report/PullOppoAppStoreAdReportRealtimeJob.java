package com.outfox.ead.apolo.app.store.get.report;

import com.outfox.ead.apolo.app.store.get.report.appstore.oppo.OppoAppStoreService;
import com.outfox.ead.apolo.app.store.get.report.base.TaskParams;
import com.outfox.ead.apolo.app.store.get.report.persistent.dao.base.BaseRepositoryImpl;
import com.outfox.ead.apolo.app.store.get.report.task.GetOppoAdStatisticTask;
import com.outfox.ead.apolo.app.store.get.report.task.Task;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.LocalDateTime;

/**
 * 实时更新上一日的OPPO广告报表数据
 *
 * <AUTHOR>
 * @date 2021/5/6 14:29
 */
@Slf4j
@EnableTransactionManagement
@EnableJpaRepositories(repositoryBaseClass = BaseRepositoryImpl.class)
@EnableJpaAuditing
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScans(value = {
        @ComponentScan(basePackageClasses = OppoAppStoreService.class),
        @ComponentScan(basePackages = "com.outfox.ead.apolo.app.store.get.report.task",
                useDefaultFilters = false, includeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = GetOppoAdStatisticTask.class)
        }),
        @ComponentScan(basePackages = "com.outfox.ead.apolo.app.store.get.report.persistent")
})
public class PullOppoAppStoreAdReportRealtimeJob extends AzkabanSpringBootBaseJob {

    private static final String JOB_CONFIG = "classpath:pull-oppo_appstore_ad_report_realtime.job";

    public static void main(String[] args) {
        try {
            ApplicationContext context = startUp(JOB_CONFIG, PullOppoAppStoreAdReportRealtimeJob.class);
            Task task = context.getBean(GetOppoAdStatisticTask.class);
            LocalDateTime now = LocalDateTime.now();
            TaskParams params = new TaskParams();
            params.setStartDatetime(now);
            params.setEndDatetime(now);
            task.run(params);
        } catch (Exception e) {
            log.error("任务执行异常。", e);
            // 确认异常，将异常情况反馈给azkaban
            System.exit(1);
        }
    }
}
