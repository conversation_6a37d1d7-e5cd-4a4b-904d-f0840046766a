package com.outfox.ead.apolo.app.store.get.report.appstore.huawei;

import com.google.common.collect.Lists;
import com.outfox.ead.apolo.app.store.get.report.appstore.AbstractBaseAppStoreServiceImpl;
import com.outfox.ead.apolo.app.store.get.report.appstore.huawei.bean.*;
import com.outfox.ead.apolo.app.store.get.report.base.AppStoreType;
import com.outfox.ead.apolo.app.store.get.report.base.HuaWeiAdTask;
import com.outfox.ead.apolo.app.store.get.report.persistent.dto.AdTaskStatisticDTO;
import com.outfox.ead.apolo.app.store.get.report.persistent.dto.DownloadInstallReportDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import retrofit2.Call;
import retrofit2.Response;

import java.io.BufferedReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/22 18:01
 */
@Slf4j
@Service("huawei")
public class HuaWeiAppStoreServiceImpl extends AbstractBaseAppStoreServiceImpl implements HuaWeiAppStoreService {

    public static final int SUCCESS_CODE = 20770001;
    private static final int MAX_PAGE_SIZE = 1000;
    private final HuaWeiAppStore huaWeiAppStore;
    private final String clientId;
    private final String clientSecret;
    private volatile String token;
    private volatile long expireTimestamp;

    @Autowired
    HuaWeiAppStoreServiceImpl(@Value("${huawei.clientUrl}") String huaweiUrl,
                              @Value("${huawei.client_secret}") String clientSecret,
                              @Value("${huawei.clientId}") String clientId) {
        this.clientSecret = clientSecret;
        this.clientId = clientId;
        huaWeiAppStore = getRetrofit(huaweiUrl).create(HuaWeiAppStore.class);
    }

    @Override
    public List<DownloadInstallReportDTO> getAppDownloadExport(HuaWeiAppDownloadExportRequest request) {
        try {
            String appId = request.getAppId();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String startDay = request.getStartTime().atZone(ZoneId.systemDefault())
                    .withZoneSameInstant(ZoneOffset.UTC).format(formatter);
            String endDay = request.getEndTime().atZone(ZoneId.systemDefault())
                    .withZoneSameInstant(ZoneOffset.UTC).format(formatter);

            Call<HuaWeiAppDownloadExportResponse> call = huaWeiAppStore.getDownloadExport(clientId,
                    getToken(),
                    request.getAppId(),
                    request.getLanguage(),
                    startDay,
                    endDay);
            HuaWeiAppDownloadExportResponse huaWeiAppDownloadExportResponse = call.execute().body();
            if (Objects.isNull(huaWeiAppDownloadExportResponse)) {
                log.error("调用华为下载安装报表数据接口失败");
                return Collections.emptyList();
            }
            Call<ResponseBody> report = huaWeiAppStore.downloadReportFile(huaWeiAppDownloadExportResponse.getFileUrl());

            try (ResponseBody responseBody = report.execute().body()) {
                assert responseBody != null;
                try (BufferedReader bufferedReader = new BufferedReader(responseBody.charStream())) {
                    return bufferedReader.lines().skip(1).map(line -> {
                        String[] row = line.split(",");
                        return DownloadInstallReportDTO
                                .builder()
                                .appId(appId)
                                .date(row[0])
                                .appStoreType(AppStoreType.HUAWEI)
                                .impressions(longValueOf(row[1]))
                                .detailsPageViews(longValueOf(row[2]))
                                .totalDownloads(longValueOf(row[3]))
                                .updateDownloads(longValueOf(row[4]))
                                .newDownloads(longValueOf(row[5]))
                                .unloads(longValueOf(row[6]))
                                .ctr(row[7])
                                .cvrDetailPage(row[8])
                                .installDownloadsRate(row[9])
                                .build();
                    }).collect(Collectors.toList());
                }
            } catch (IOException e) {
                log.error("解析报表文件异常。", e);
            }
        } catch (Exception e) {
            log.error("get huawei download_install_reports error.", e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<HuaWeiAdTask> getAdTaskList() {
        try {
            Call<HuaWeiResponse<HuaWeiAdTask>> call = huaWeiAppStore.getAdTaskInfo(clientId, getToken(),
                    HuaWeiAdTaskRequest.builder().page(1).pageSize(MAX_PAGE_SIZE).build());
            HuaWeiResponse<HuaWeiAdTask> response = call.execute().body();
            assert response != null;
            if (response.getCode() != SUCCESS_CODE) {
                log.error("查询华为任务列表接口失败." + response.getMsg());
                return Collections.emptyList();
            }
            return response.getDatas();
        } catch (Exception e) {
            log.error("调用接口异常", e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<HuaWeiAdTaskStatistic> getAdTaskStatisticList(LocalDateTime startDate,
                                                              LocalDateTime endDate) throws Exception {
        int page = 1;
        List<HuaWeiAdTaskStatistic> result = Lists.newArrayList();

        Pair<Integer, List<HuaWeiAdTaskStatistic>> pair = getAdTaskStatisticListByPage(startDate,
                endDate, page);
        result.addAll(pair.getSecond());
        int total = pair.getFirst();
        while (total > page * MAX_PAGE_SIZE) {
            page++;
            Pair<Integer, List<HuaWeiAdTaskStatistic>> rePair = getAdTaskStatisticListByPage(startDate,
                    endDate, page);
            result.addAll(rePair.getSecond());
        }
        return result;
    }

    private Pair<Integer, List<HuaWeiAdTaskStatistic>> getAdTaskStatisticListByPage(LocalDateTime startDate,
                                                                                    LocalDateTime endDate,
                                                                                    int page) throws Exception {
        HuaWeiAdTaskReportRequest<AdTaskReportFiltering> request = new HuaWeiAdTaskReportRequest<>();
        request.setPage(page);
        request.setPageSize(HuaWeiAppStoreServiceImpl.MAX_PAGE_SIZE);
        request.setStartDate(startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        request.setEndDate(endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        request.setGroupby(Lists.newArrayList("statDate", "taskId", "region", "channel"));
        Call<HuaWeiResponse<HuaWeiAdTaskStatistic>> call = huaWeiAppStore.getAdTaskReport(clientId,
                getToken(), request);
        HuaWeiResponse<HuaWeiAdTaskStatistic> response = call.execute().body();

        if (Objects.isNull(response) || response.getCode() != SUCCESS_CODE) {
            throw new Exception("调用外部接口失败");
        }
        log.info("【query huawei ad task report】  数据total：{}, 当前执行第{}页，pageSize:{}, 获得数据{}条",
                response.getTotal(), page, MAX_PAGE_SIZE, response.getDatas().size());
        return Pair.of(response.getTotal(), response.getDatas());
    }

    @Override
    public List<HuaWeiSearchAdTaskStatistic> getSearchAdTaskStatisticList(LocalDateTime startDate,
                                                                          LocalDateTime endDate) throws Exception {
        int page = 1;
        List<HuaWeiSearchAdTaskStatistic> result = Lists.newArrayList();

        Pair<Integer, List<HuaWeiSearchAdTaskStatistic>> pair = getSearchAdTaskStatisticListByPage(startDate,
                endDate, page);
        result.addAll(pair.getSecond());
        int total = pair.getFirst();
        while (total > page * MAX_PAGE_SIZE) {
            page++;
            Pair<Integer, List<HuaWeiSearchAdTaskStatistic>> rePair = getSearchAdTaskStatisticListByPage(startDate,
                    endDate, page);
            result.addAll(rePair.getSecond());
        }
        return result;
    }

    private Pair<Integer, List<HuaWeiSearchAdTaskStatistic>> getSearchAdTaskStatisticListByPage(LocalDateTime startDate,
                                                                                                LocalDateTime endDate,
                                                                                                int page) throws Exception {
        HuaWeiAdTaskReportRequest<HuaWeiSearchAdTaskReportFiltering> request = new HuaWeiAdTaskReportRequest<>();
        request.setPage(page);
        request.setPageSize(HuaWeiAppStoreServiceImpl.MAX_PAGE_SIZE);
        request.setStartDate(startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        request.setEndDate(endDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
        request.setGroupby(Lists.newArrayList("statDate", "taskId",
                "region", "searchkey", "matchType", "matchKey"));
        Call<HuaWeiResponse<HuaWeiSearchAdTaskStatistic>> call = huaWeiAppStore.getSearchAdTaskReport(clientId,
                getToken(), request);
        HuaWeiResponse<HuaWeiSearchAdTaskStatistic> response = call.execute().body();

        if (Objects.isNull(response) || response.getCode() != SUCCESS_CODE) {
            throw new Exception("调用外部接口失败");
        }
        log.info("【query huawei search ad task】  数据total：{}, 当前执行第{}页，pageSize:{}, 获得数据{}条",
                response.getTotal(), page, MAX_PAGE_SIZE, response.getDatas().size());
        return Pair.of(response.getTotal(), response.getDatas());
    }

    private Long longValueOf(String num) {
        if (StringUtils.isEmpty(num)) {
            return 0L;
        }
        return Long.valueOf(num);
    }

    private String getToken() throws Exception {
        long now = System.currentTimeMillis() / 1000;
        if ((!StringUtils.isEmpty(token)) && now < expireTimestamp) {
            return token;
        }
        int maxRetryCount = 2;
        int count = 0;
        Response<HuaWeiToken> call = null;
        while (count < maxRetryCount) {
            count++;
            Call<HuaWeiToken> tokenResp = huaWeiAppStore.getToken(new HuaWeiTokenRequest(clientId, clientSecret));
            call = tokenResp.execute();
            if (call.isSuccessful()) {
                break;
            }
        }
        if (!call.isSuccessful()) {
            throw new Exception("访问华为token接口失败");
        }
        HuaWeiToken huaWeiToken = call.body();
        assert huaWeiToken != null;
        token = "Bearer " + huaWeiToken.getAccessToken();
        // 1 秒误差
        expireTimestamp = now + huaWeiToken.getExpiresIn() - 1;
        return token;
    }

    @Override
    public List<AdTaskStatisticDTO> getAdTaskStatisticDTOList(LocalDateTime dateTime) throws Exception {
        return getAdTaskStatisticList(dateTime, dateTime)
                .stream()
                .map(huaWeiAdTaskStatistic -> huaWeiAdTaskStatistic.getAdTaskStatisticDTO(null))
                .collect(Collectors.toList());
    }
}
