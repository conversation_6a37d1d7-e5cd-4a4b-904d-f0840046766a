package com.outfox.ead.apolo.app.store.get.report.persistent.service.impl;

import com.outfox.ead.apolo.app.store.get.report.base.AppStoreType;
import com.outfox.ead.apolo.app.store.get.report.persistent.dao.AdTaskStatisticRepository;
import com.outfox.ead.apolo.app.store.get.report.persistent.dto.AdTaskStatisticDTO;
import com.outfox.ead.apolo.app.store.get.report.persistent.dto.DailyReportDTO;
import com.outfox.ead.apolo.app.store.get.report.persistent.po.AdTaskStatistic;
import com.outfox.ead.apolo.app.store.get.report.persistent.service.AdTaskStatisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 更新数据报表
 *
 * <AUTHOR>
 * @date 2021/4/27 16:38
 */
@Service
public class AdTaskStatisticServiceImpl implements AdTaskStatisticService {

    @Autowired
    private AdTaskStatisticRepository adTaskStatisticRepository;

    @Transactional(rollbackOn = Exception.class)
    @Override
    public void batchInsertOrUpdateHuaWeiStatistic(List<AdTaskStatisticDTO> list) {
        List<AdTaskStatistic> statistics = list.stream().map(AdTaskStatisticDTO::getAdTaskStatistic)
                .peek(adTaskStatistic -> {
                    AdTaskStatistic ad = adTaskStatisticRepository.findOne(Example.of(AdTaskStatistic.builder()
                            .date(adTaskStatistic.getDate())
                            .appStoreType(adTaskStatistic.getAppStoreType())
                            .taskId(adTaskStatistic.getTaskId())
                            .channel(adTaskStatistic.getChannel())
                            .region(adTaskStatistic.getRegion())
                            .build()
                    )).orElse(null);
                    if (Objects.nonNull(ad)) {
                        adTaskStatistic.setId(ad.getId());
                        adTaskStatistic.setCreateTime(ad.getCreateTime());
                    }
                })
                .collect(Collectors.toList());
        adTaskStatisticRepository.saveAll(statistics);
    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public void batchInsertOrUpdateVivoStatistic(List<AdTaskStatisticDTO> list) {
        Map<Boolean, List<AdTaskStatistic>> statistics = list.stream()
                .map(AdTaskStatisticDTO::getAdTaskStatistic)
                .peek(adTaskStatistic -> {
                    AdTaskStatistic ad = adTaskStatisticRepository.findOne(Example.of(AdTaskStatistic.builder()
                            .date(adTaskStatistic.getDate())
                            .appStoreType(adTaskStatistic.getAppStoreType())
                            .mediaChannel(adTaskStatistic.getMediaChannel())
                            .adId(adTaskStatistic.getAdId())
                            .groupId(adTaskStatistic.getGroupId())
                            .planId(adTaskStatistic.getPlanId())
                            .creativeId(adTaskStatistic.getCreativeId())
                            .build()
                    )).orElse(null);
                    if (Objects.nonNull(ad)) {
                        adTaskStatistic.setId(ad.getId());
                        adTaskStatistic.setCreateTime(ad.getCreateTime());
                    }
                })
                .collect(Collectors.groupingBy(s -> Objects.isNull(s.getId())));

        persistentStatistic(statistics);
    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public void batchInsertOrUpdateOppoStatistic(List<AdTaskStatisticDTO> list) {
        Map<Boolean, List<AdTaskStatistic>> statistics = list.stream()
                .map(AdTaskStatisticDTO::getAdTaskStatistic)
                .peek(adTaskStatistic -> {
                    AdTaskStatistic ad = adTaskStatisticRepository.findOne(Example.of(AdTaskStatistic.builder()
                            .date(adTaskStatistic.getDate())
                            .appStoreType(adTaskStatistic.getAppStoreType())
                            .advertiserId(adTaskStatistic.getAdvertiserId())
                            .adId(adTaskStatistic.getAdId())
                            .groupId(adTaskStatistic.getGroupId())
                            .planId(adTaskStatistic.getPlanId())
                            .build()
                    )).orElse(null);
                    if (Objects.nonNull(ad)) {
                        adTaskStatistic.setId(ad.getId());
                        adTaskStatistic.setCreateTime(ad.getCreateTime());
                    }
                })
                .collect(Collectors.groupingBy(s -> Objects.isNull(s.getId())));

        persistentStatistic(statistics);
    }

    @Transactional(rollbackOn = Exception.class)
    @Override
    public void batchInsertOrUpdateXiaoMiStatistic(List<AdTaskStatisticDTO> list) {
        Map<Boolean, List<AdTaskStatistic>> statistics = list.stream()
                .map(AdTaskStatisticDTO::getAdTaskStatistic)
                .peek(adTaskStatistic -> {
                    AdTaskStatistic ad = adTaskStatisticRepository.findOne(Example.of(AdTaskStatistic.builder()
                            .date(adTaskStatistic.getDate())
                            .appStoreType(adTaskStatistic.getAppStoreType())
                            .advertiserId(adTaskStatistic.getAdvertiserId())
                            .adId(adTaskStatistic.getAdId())
                            .groupId(adTaskStatistic.getGroupId())
                            .planId(adTaskStatistic.getPlanId())
                            .build())).orElse(null);
                    if (Objects.nonNull(ad)) {
                        adTaskStatistic.setId(ad.getId());
                        adTaskStatistic.setCreateTime(ad.getCreateTime());
                    }
                })
                .collect(Collectors.groupingBy(s -> Objects.isNull(s.getId())));

        persistentStatistic(statistics);
    }

    @Override
    public DailyReportDTO queryDailyReport(LocalDateTime date, AppStoreType appStoreType) {
        return adTaskStatisticRepository.findAll(Example.of(AdTaskStatistic.builder()
                .date(date.format(DateTimeFormatter.ISO_LOCAL_DATE))
                .appStoreType(appStoreType)
                .build()
        )).stream()
                .map(AdTaskStatisticDTO::toAdTaskStatisticDTO)
                .map(AdTaskStatisticDTO::getDailyReportDTO)
                .reduce(DailyReportDTO::add)
                .orElse(null);
    }

    @Override
    public void deleteStatistic(LocalDateTime date, AppStoreType appStoreType) {
        List<AdTaskStatistic> list = adTaskStatisticRepository.findAll(Example.of(AdTaskStatistic.builder()
                .date(date.format(DateTimeFormatter.ISO_LOCAL_DATE))
                .appStoreType(appStoreType)
                .build()
        ));
        if (!list.isEmpty()) {
            adTaskStatisticRepository.deleteInBatch(list);
        }

    }

    @Override
    public void batchInsertOrUpdateAdvertiserStatistic(List<AdTaskStatisticDTO> list) {
        Map<Boolean, List<AdTaskStatistic>> statistics = list.stream()
                .map(AdTaskStatisticDTO::getAdTaskStatistic)
                .peek(adTaskStatistic -> {
                    AdTaskStatistic ad = adTaskStatisticRepository.findOne(Example.of(AdTaskStatistic.builder()
                            .date(adTaskStatistic.getDate())
                            .appStoreType(adTaskStatistic.getAppStoreType())
                            .advertiserId(adTaskStatistic.getAdvertiserId())
                            .build())).orElse(null);
                    if (Objects.nonNull(ad)) {
                        adTaskStatistic.setId(ad.getId());
                        adTaskStatistic.setCreateTime(ad.getCreateTime());
                    }
                })
                .collect(Collectors.groupingBy(s -> Objects.isNull(s.getId())));

        persistentStatistic(statistics);
    }

    private void persistentStatistic(Map<Boolean, List<AdTaskStatistic>> statistics) {
        List<AdTaskStatistic> insertList = statistics.get(true);
        if (!CollectionUtils.isEmpty(insertList)) {
            adTaskStatisticRepository.batchInsert(insertList);
        }
        List<AdTaskStatistic> updateList = statistics.get(false);
        if (!CollectionUtils.isEmpty(updateList)) {
            adTaskStatisticRepository.batchUpdate(updateList);
        }
    }

}
