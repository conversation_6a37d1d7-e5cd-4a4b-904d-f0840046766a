package com.outfox.ead.apolo.app.store.get.report.persistent.dao;

import com.outfox.ead.apolo.app.store.get.report.persistent.dao.base.BaseRepository;
import com.outfox.ead.apolo.app.store.get.report.persistent.po.AdTaskStatistic;
import org.springframework.stereotype.Repository;

/**
 * 广告任务报表
 *
 * <AUTHOR>
 * @date 2021/4/27 11:18
 */
@Repository
public interface AdTaskStatisticRepository
        extends BaseRepository<AdTaskStatistic, Long> {

}
