package com.outfox.ead.apolo.app.store.get.report.persistent.po;

import com.outfox.ead.apolo.app.store.get.report.base.AppStoreType;
import com.outfox.ead.apolo.app.store.get.report.persistent.dto.SearchMatchType;
import lombok.*;

import javax.persistence.*;

/**
 * 搜索广告报表实体定义
 * <AUTHOR>
 * @date 2021/4/27 17:49
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "search_ad_task_statistic", indexes = {
        @Index(name = "idx_date_6_etc", columnList = "date" ),
        @Index(name = "idx_date_6_etc", columnList = "appStoreType" ),
        @Index(name = "idx_date_6_etc", columnList = "appId" ),
        @Index(name = "idx_date_6_etc", columnList = "matchType" ),
        @Index(name = "idx_date_6_etc", columnList = "matchKey" ),
        @Index(name = "idx_date_6_etc", columnList = "searchKey" )
})
public class SearchAdTaskStatistic extends BaseEntity {
    @javax.persistence.Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(columnDefinition = "bigint(20) COMMENT '主键'")
    private Long id;
    @Column(columnDefinition = "VARCHAR(20) COMMENT '日期 yyyy-MM-dd'")
    private String date;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '应用ID'")
    private String appId;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '应用名称'")
    private String appName;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '包名'")
    private String appPackage;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '渠道'")
    private String channel;
    @Column(columnDefinition = "VARCHAR(64) COMMENT '广告主ID'")
    private String advertiserId;
    @Column(columnDefinition = "VARCHAR(64) COMMENT '广告ID'")
    private String adId;
    @Column(columnDefinition = "VARCHAR(64) COMMENT '广告名称'")
    private String adName;
    @Column(columnDefinition = "VARCHAR(64) COMMENT '计划ID'")
    private String planId;
    @Column(columnDefinition = "VARCHAR(64) COMMENT '计划名称'")
    private String planName;
    @Column(columnDefinition = "VARCHAR(64) COMMENT '广告组ID'")
    private String groupId;
    @Column(columnDefinition = "VARCHAR(64) COMMENT '广告组名称'")
    private String groupName;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '推广目的'")
    private String extensionType;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '业务类型'")
    private String bizType;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '推广规格'")
    private String adSpec;
    @Column(columnDefinition = "VARCHAR(255) COMMENT '搜索词'")
    private String searchKey;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '华为任务ID'")
    private String taskId;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '华为任务名称'")
    private String taskName;
    @Column(columnDefinition = "VARCHAR(16) COMMENT '区域'")
    private String region;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '关键词'")
    private String matchKey;
    @Column(columnDefinition = "VARCHAR(32) COMMENT '推广样式'")
    private String matchType;
    @Column(length = 32)
    @Enumerated(EnumType.STRING)
    private AppStoreType appStoreType;
    private Double cost;
    @Column(name = "\"show\"", columnDefinition = "bigint(20) COMMENT '服务端展示量'")
    private Long show;
    private Long click;
    private Long exposure;
    private Long download;
    private String rank;
}
