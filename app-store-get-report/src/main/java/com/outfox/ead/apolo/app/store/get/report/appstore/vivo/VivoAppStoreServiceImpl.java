package com.outfox.ead.apolo.app.store.get.report.appstore.vivo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.outfox.ead.apolo.app.store.get.report.appstore.AbstractBaseAppStoreServiceImpl;
import com.outfox.ead.apolo.app.store.get.report.appstore.vivo.bean.*;
import com.outfox.ead.apolo.app.store.get.report.persistent.dto.AdTaskStatisticDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import retrofit2.Call;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/11 15:59
 */
@Slf4j
@Service("vivo")
public class VivoAppStoreServiceImpl extends AbstractBaseAppStoreServiceImpl implements VivoAppStoreService {

    private static final int MAX_PAGE_SIZE = 10000;
    private static final int SUCCESS_CODE = 0;
    private final String apiKey;
    private final String apiUuid;
    private final VivoAppStore vivoAppStore;

    @Autowired
    VivoAppStoreServiceImpl(@Value("${vivo.clientUrl}") String url,
                            @Value("${vivo.apiKey}") String apiKey,
                            @Value("${vivo.apiUuid}") String apiUuid) {
        this.apiKey = apiKey;
        this.apiUuid = apiUuid;
        vivoAppStore = getRetrofit(url).create(VivoAppStore.class);
    }

    @Override
    public List<AdStatement> queryAdStatementByDay(String startDate, String endDate) throws Exception {
        String lastId = null;
        Pair<String, List<AdStatement>> result;
        List<AdStatement> list = new ArrayList<>(MAX_PAGE_SIZE);
        do {
            result = getAdStatement(startDate, endDate, lastId);
            lastId = result.getFirst();
            list.addAll(result.getSecond());
        } while (hasNextPage(result));
        log.info("完成拉取vivo报表任务。startDate: {}, endDate: {}, total: {}", startDate, endDate, list.size());
        return list;
    }

    private boolean hasNextPage(Pair<String, List<AdStatement>> result) {
        if (StringUtils.isEmpty(result.getFirst()) ||
                CollectionUtils.isEmpty(result.getSecond())) {
            return false;
        }
        return result.getSecond().size() >= MAX_PAGE_SIZE;
    }

    private Pair<String, List<AdStatement>> getAdStatement(String startDate, String endDate,
                                                           String lastId) throws Exception {
        AdStatementQueryRequest request = getAdStatementQueryRequest(startDate, endDate, lastId);
        String requestStr = parserJson(request);
        String sign = DigestUtils.sha256Hex(apiKey + requestStr).toUpperCase();
        Call<VivoResponse<AdStatementQuery>> call = vivoAppStore.postVivoAdStatistic(apiUuid, sign, requestStr);
        VivoResponse<AdStatementQuery> response = call.execute().body();
        if (Objects.isNull(response) || response.getCode() != SUCCESS_CODE) {
            throw new Exception(String.format("请求vivo外部接口异常。msg: %s",
                    Objects.nonNull(response) ? response.getMsg() : ""));
        }
        AdStatementQuery queryResult = response.getData();
        return Pair.of(queryResult.getLastId(), queryResult.getList());
    }

    private String parserJson(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error("object parse json failed. ", e);
        }
        return "";
    }

    private AdStatementQueryRequest getAdStatementQueryRequest(String startDate,
                                                               String endDate,
                                                               String lastId) {
        return AdStatementQueryRequest.builder()
                .filterFiledIds(null)
                .startDate(startDate)
                .endDate(endDate)
                .lastId(lastId)
                .summaryType(SummaryTypeEnum.DAY)
                .pageSize(MAX_PAGE_SIZE)
                .build();
    }

    @Override
    public List<AdTaskStatisticDTO> getAdTaskStatisticDTOList(LocalDateTime dateTime) throws Exception {
        String date = dateTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
        return this.queryAdStatementByDay(date, date)
                .stream()
                .map(AdStatement::getAdTaskStatisticDTO)
                .collect(Collectors.toList());
    }
}
