package com.outfox.ead.apolo.app.store.get.report.task;

import com.outfox.ead.apolo.app.store.get.report.appstore.huawei.HuaWeiAppStoreService;
import com.outfox.ead.apolo.app.store.get.report.appstore.huawei.bean.HuaWeiAppDownloadExportRequest;
import com.outfox.ead.apolo.app.store.get.report.base.TaskParams;
import com.outfox.ead.apolo.app.store.get.report.persistent.dto.DownloadInstallReportDTO;
import com.outfox.ead.apolo.app.store.get.report.persistent.service.DownloadInstallReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 获取华为下载安装报表Job
 *
 * <AUTHOR>
 * @date 2021/4/22 16:44
 */
@Slf4j
@Service
public class GetHuaWeiDownloadExportTask implements Task {

    private static final int MAX_QUERY_RANGE_DAYS = 180;

    @Autowired
    private HuaWeiAppStoreService huaWeiAppStoreService;
    @Autowired
    private DownloadInstallReportService downloadInstallReportService;

    @Override
    public void run(TaskParams taskParams) throws Exception {

        if (taskParams.getEndDatetime().minusDays(MAX_QUERY_RANGE_DAYS)
                .compareTo(taskParams.getStartDatetime()) > 0) {
            throw new Exception(String.format("请求华为应用商店下载安装报表接口的时间范围不能超过%d天。", MAX_QUERY_RANGE_DAYS));
        }

        for (String appId : taskParams.getAppIdList()) {
            HuaWeiAppDownloadExportRequest request = HuaWeiAppDownloadExportRequest
                    .builder()
                    .appId(appId)
                    .startTime(taskParams.getStartDatetime())
                    .endTime(taskParams.getEndDatetime())
                    .language("zh-CN")
                    .build();
            List<DownloadInstallReportDTO> result = huaWeiAppStoreService.getAppDownloadExport(request);
            // 持久化
            downloadInstallReportService.batchInsertOrUpdate(result);
        }

    }
}
