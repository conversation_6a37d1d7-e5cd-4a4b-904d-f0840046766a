package com.outfox.ead.apolo.app.store.get.report.appstore.tencent.bean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 请求 youdao 封装 获取 广点通 token
 * 广点通token结构体
 *
 * <AUTHOR>
 * @date 2021/5/25 15:57
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GdtAccessToken {
    private Integer err;
    private AccessToken data;

    public String getToken() {
        return data.getAccessToken();
    }
}

class AccessToken {
    @JsonProperty("access_token")
    private String accessToken;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
}
