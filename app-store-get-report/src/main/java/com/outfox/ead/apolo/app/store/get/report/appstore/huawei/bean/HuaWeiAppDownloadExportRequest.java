package com.outfox.ead.apolo.app.store.get.report.appstore.huawei.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 华为下载安装报表请求参数
 *
 * <AUTHOR>
 * @date 2021/4/22 17:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HuaWeiAppDownloadExportRequest {
    private String appId;
    private String language = "zh-CN";
    /**
     * 查询开始时间，UTC时间，格式为YYYYMMDD。startTime和endTime之间不超过180天。
     */
    private LocalDateTime startTime;
    /**
     * 查询结束时间，UTC时间，格式为YYYYMMDD。startTime和endTime之间不超过180天。
     */
    private LocalDateTime endTime;
}
