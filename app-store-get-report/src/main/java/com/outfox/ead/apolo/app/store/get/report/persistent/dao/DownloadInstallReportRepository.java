package com.outfox.ead.apolo.app.store.get.report.persistent.dao;

import com.outfox.ead.apolo.app.store.get.report.persistent.dao.base.BaseRepository;
import com.outfox.ead.apolo.app.store.get.report.persistent.po.DownloadInstallReport;
import com.outfox.ead.apolo.app.store.get.report.persistent.po.DownloadInstallReportPrimaryKey;
import org.springframework.stereotype.Repository;

/**
 * 应用商店 应用下载安装报表
 *
 * <AUTHOR>
 * @date 2021/4/27 11:18
 */
@Repository
public interface DownloadInstallReportRepository
        extends BaseRepository<DownloadInstallReport, DownloadInstallReportPrimaryKey> {

}
