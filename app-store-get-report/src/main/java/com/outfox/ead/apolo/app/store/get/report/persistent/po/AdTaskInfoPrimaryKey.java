package com.outfox.ead.apolo.app.store.get.report.persistent.po;

import com.outfox.ead.apolo.app.store.get.report.base.AppStoreType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;

/**
 * 广告任务表联合主键
 * <AUTHOR>
 * @date 2021/4/27 15:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class AdTaskInfoPrimaryKey implements Serializable {
    @Column(length = 64)
    private String taskId;
    @Column(length = 32)
    @Enumerated(EnumType.STRING)
    private AppStoreType appStoreType;
}
