package com.outfox.ead.apolo.app.store.get.report;

import com.outfox.ead.apolo.app.store.get.report.base.TaskParams;
import com.outfox.ead.apolo.app.store.get.report.persistent.dao.base.BaseRepositoryImpl;
import com.outfox.ead.apolo.app.store.get.report.sentry.SentryTask;
import com.outfox.ead.apolo.app.store.get.report.task.Task;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.LocalDateTime;

/**
 * 定时检查task任务执行情况
 * 当前仅能解决我方账目条数比远端少，或数据有差异的问题。暂不能处理我方数据比远端多的情况（该类情况少见，且不正常）。
 * 若出现一天数据出现多次处理异常，考虑是该情况。
 * 比如： 28号数据，连续三天告警，均拉取最新数据覆盖，考虑本地条目比远端多导致总账不平
 * <AUTHOR>
 * @date 2021/5/6 14:29
 */
@Slf4j
@EnableTransactionManagement
@EnableJpaRepositories(repositoryBaseClass = BaseRepositoryImpl.class)
@EnableJpaAuditing
@SpringBootApplication
public class SentryJob extends AzkabanSpringBootBaseJob {

    private static final String JOB_CONFIG = "classpath*:*.job";

    public static void main(String[] args) {
        try {
            ApplicationContext context = startUp(JOB_CONFIG, SentryJob.class);
            String sentryDay = context.getEnvironment().getProperty("sentry.day", "7");
            Task task = context.getBean(SentryTask.class);
            LocalDateTime now = LocalDateTime.now();
            task.run(TaskParams.builder()
            .startDatetime(now.minusDays(Long.parseLong(sentryDay) + 1))
            .endDatetime( now.minusDays(1))
            .build());
        } catch (Exception e) {
            log.error("任务执行异常。", e);
            // 确认异常，将异常情况反馈给azkaban
            System.exit(1);
        }
    }
}
