type=javaprocess
java.class=com.outfox.ead.apolo.app.store.get.report.PullXiaoMiAppStoreAdReportJob
classpath=./*

xiaomi.url=http://api.e.mi.com/
xiaomi.signId=2665ec4fc6dd9da44aabad3a13d0bca1
xiaomi.secretKey=MJdNRPaerVtJTNZa
xiaomi.customerId=22252
xiaomi.ad.report.job.startDate
xiaomi.ad.report.job.endDate

#spring.datasource.url=***************************************************************************************************************************************************************************************
spring.datasource.url=****************************************************************************************************************************************************************************************
spring.datasource.username=eadonline4nb
spring.datasource.password=new1ife4Th1sAugust

spring.jpa.database=mysql
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy

logging.level.com.outfox.ead.apolo.app.store.get.report=info
logging.level.org.hibernate.sql=debug
logging.level.org.hibernate.engine=debug