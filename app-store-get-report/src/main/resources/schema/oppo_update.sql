
-- ---------------------------------------
-- ad_task_statistic 表新增部分字段，并更新指标存储类型为bigint
-- -----------------------------------------

ALTER TABLE `ad_task_statistic` ADD `extension_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推广目的';
ALTER TABLE `ad_task_statistic` ADD `biz_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型';
ALTER TABLE `ad_task_statistic` ADD `ad_spec` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推广规格';
ALTER TABLE `search_ad_task_statistic` ADD `ad_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告ID';
ALTER TABLE `search_ad_task_statistic` ADD `ad_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告名称';
ALTER TABLE `search_ad_task_statistic` ADD `advertiser_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告主ID';
ALTER TABLE `search_ad_task_statistic` ADD `group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告组ID';
ALTER TABLE `search_ad_task_statistic` ADD  `group_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告组名称';
ALTER TABLE `search_ad_task_statistic` ADD `plan_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计划ID';
ALTER TABLE `search_ad_task_statistic` ADD `plan_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计划名称';
ALTER TABLE `search_ad_task_statistic` ADD `app_package` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '包名';
ALTER TABLE `search_ad_task_statistic` ADD `extension_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推广目的';
ALTER TABLE `search_ad_task_statistic` ADD `biz_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务类型';
ALTER TABLE `search_ad_task_statistic` ADD `ad_spec` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推广规格';
ALTER TABLE `search_ad_task_statistic` ADD `channel` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推广渠道';
ALTER TABLE `search_ad_task_statistic` ADD `click` bigint(20) DEFAULT NULL COMMENT '点击量';
ALTER TABLE `search_ad_task_statistic` MODIFY `download` bigint(20) DEFAULT NULL COMMENT '下载量';
ALTER TABLE `search_ad_task_statistic` MODIFY  `exposure` bigint(20) DEFAULT NULL COMMENT '展示量';
ALTER TABLE `search_ad_task_statistic` MODIFY `show` bigint(20) DEFAULT NULL COMMENT '服务端展示量';
ALTER TABLE `search_ad_task_statistic` MODIFY `rank` varchar(32) DEFAULT NULL COMMENT '排名';

