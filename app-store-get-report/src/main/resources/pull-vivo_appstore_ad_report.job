type=javaprocess
java.class=com.outfox.ead.apolo.app.store.get.report.PullVivoAppStoreAdReportJob
classpath=./*

vivo.clientUrl=https://ad-market.vivo.com.cn/
vivo.apiKey=eaqyXpIH2jQ=
vivo.apiUuid=g8LbXnnK1rLp6cGqAgV/WPiAi9cpnpr2
vivo.ad.report.job.startDate
vivo.ad.report.job.endDate

#spring.datasource.url=***************************************************************************************************************************************************************************************
spring.datasource.url=****************************************************************************************************************************************************************************************
spring.datasource.username=eadonline4nb
spring.datasource.password=new1ife4Th1sAugust

spring.jpa.database=mysql
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy

logging.level.com.outfox.ead.apolo.app.store.get.report=info
logging.level.org.hibernate.sql=debug
logging.level.org.hibernate.engine=debug