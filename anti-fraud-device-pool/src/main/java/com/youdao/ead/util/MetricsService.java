package com.youdao.ead.util;


import com.codahale.metrics.MetricFilter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.SharedMetricRegistries;
import com.codahale.metrics.Slf4jReporter;
import com.codahale.metrics.graphite.Graphite;
import com.codahale.metrics.graphite.GraphiteReporter;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.net.InetSocketAddress;
import java.util.concurrent.TimeUnit;

public class MetricsService implements Serializable {

    private static final MetricsService INSTANCE = new MetricsService();

    private Slf4jReporter logReporter;
    private GraphiteReporter graphiteReporter;
    public static final String JMX_DOMAIN_NAME = "youdao.ead.apolo-anti-fraud-device-pool";

    private MetricsService() {
        initMetrics();
    }

    public static MetricsService getInstance() {
        return INSTANCE;
    }

    private void initMetrics() {
        try {
            MetricRegistry metricRegistry = SharedMetricRegistries.getOrCreate(MetricUtils.APOLO_ANTI_FRAUD_DEVICE_POOL_METRIC_REGISTRY);

            logReporter = Slf4jReporter.forRegistry(metricRegistry)
                    .outputTo(LoggerFactory.getLogger("metrics_reporter_logger"))
                    .convertRatesTo(TimeUnit.SECONDS)
                    .convertDurationsTo(TimeUnit.MILLISECONDS)
                    .build();
            logReporter.start(1, TimeUnit.MINUTES);

            System.out.println("success init metrics slf4j reporter");

            final Graphite graphite = new Graphite(new InetSocketAddress("quipu-graphite.inner.youdao.com", 2003));

            graphiteReporter = GraphiteReporter.forRegistry(metricRegistry)
                    .prefixedWith(JMX_DOMAIN_NAME + "." + MachineUtils.getHostName())
                    .convertRatesTo(TimeUnit.SECONDS)
                    .convertDurationsTo(TimeUnit.MILLISECONDS)
                    .filter(MetricFilter.ALL)
                    .build(graphite);
            graphiteReporter.start(1, TimeUnit.MINUTES);
        } catch (Exception e) {
            System.err.println("init metrics reporter got error: " + e.getMessage());
        }
    }

    public void destroy() {
        if (logReporter != null) {
            logReporter.close();
        }
        if (graphiteReporter != null) {
            graphiteReporter.close();
        }
    }
}