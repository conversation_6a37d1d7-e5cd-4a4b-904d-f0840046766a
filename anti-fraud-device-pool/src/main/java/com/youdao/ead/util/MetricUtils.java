package com.youdao.ead.util;

import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.SharedMetricRegistries;
import com.codahale.metrics.Timer;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class MetricUtils implements Serializable {
    public static final String APOLO_ANTI_FRAUD_DEVICE_POOL_METRIC_REGISTRY = "apolo-anti-fraud-device-pool-metric-registry";
    private static final MetricRegistry REGISTRY = SharedMetricRegistries.getOrCreate(APOLO_ANTI_FRAUD_DEVICE_POOL_METRIC_REGISTRY);

    private static final Map<String, Timer> saveStandardDeviceKvrocksTimerMap = new ConcurrentHashMap<>();
    private static final Map<String, Meter> saveStandardDeviceKvrocksErrorMeterMap = new ConcurrentHashMap<>();

    public static Timer saveStandardDeviceKvrocksTimer(Class<?> kclass) {
        return saveStandardDeviceKvrocksTimerMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.timer(name(kclass, "STANDARD_KVROCKS")));
    }


    public static Meter saveStandardDeviceKvrocksErrorMeter(Class<?> kclass) {
        return saveStandardDeviceKvrocksErrorMeterMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.meter(name(kclass, "STANDARD_KVROCKS_ERROR")));
    }

    public static String name(@NotNull Class<?> kclass, @NotNull String name) {
        return String.format("%s.%s", kclass.getSimpleName(), name);
    }
}