package com.youdao.ead.config

import io.lettuce.core.RedisURI
import scopt.OptionParser

import java.time.LocalDate
import java.time.format.DateTimeFormatter

case class DailyUpdateDevicePoolConfig(startDate: LocalDate, endDate: LocalDate)

object DailyUpdateDevicePoolParser {
  val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

  implicit val localDateRead: scopt.Read[LocalDate] =
    scopt.Read.reads(LocalDate.parse(_, dateTimeFormatter))

  val parser: OptionParser[DailyUpdateDevicePoolConfig] = new scopt.OptionParser[DailyUpdateDevicePoolConfig]("scopt") {
    // ignore unknown parameter
    override def errorOnUnknownArgument: Boolean = false

    opt[LocalDate]('s', "startDate").action((x, c) =>
      c.copy(startDate = x)).text("the start date, eg. 2017-07-24")

    opt[LocalDate]('e', "endDate").action((x, c) =>
      c.copy(endDate = x)).text("the end date, eg. 2017-07-24")

    checkConfig(c =>
      if (c.endDate.isAfter(c.startDate)) success
      else failure("The end date must be after start date.")
    )
  }
}

object DailyUpdateDevicePoolConfig extends Serializable{
  val PV_SDK_DIR = "hdfs://eadata-hdfs/quipu/camus/data/pv_sdk/hourly/"
  val DEVICE_DATA_POOL_DAILY_DIR = "hdfs://eadata-hdfs/user/eadata/zhouyunting/device_data_pool/daily/"
  val DEVICE_DATA_POOL_HISTORY_DIR = "hdfs://eadata-hdfs/user/eadata/zhouyunting/device_data_pool/history/"

  //kvrocks
  val KVROCKS_URL = List(
    RedisURI.create("redis://************:6166"),
    RedisURI.create("redis://************:6167"),
    RedisURI.create("redis://************:6166"),
    RedisURI.create("redis://************:6167"),
    RedisURI.create("redis://************:6166"),
    RedisURI.create("redis://************:6167")
  )
  val REDIS_EXPIRE_TIME_MONTH = 6

  val PREFIX_KEY = "s_"
}