<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>apolo</artifactId>
    <groupId>outfox.ead</groupId>
    <version>0.1.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>anti-fraud-device-pool</artifactId>
  <build>
    <finalName>anti-fraud-device-pool</finalName>
    <plugins>
      <plugin>
        <groupId>net.alchim31.maven</groupId>
        <artifactId>scala-maven-plugin</artifactId>
        <version>3.4.6</version>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.7</version>
        <configuration>
          <skipTests>false</skipTests>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.1.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <relocations>
                <relocation>
                  <pattern>org.apache.commons.io</pattern>
                  <shadedPattern>youdao.org.apache.commons.io</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>io.netty</pattern>
                  <shadedPattern>youdao.io.netty</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.apache.curator</pattern>
                  <shadedPattern>youdao.org.apache.curator</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.google.common</pattern>
                  <shadedPattern>youdao.com.google.common</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/maven/**</exclude>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>nexus3</id>
      <name>internal nexus3 repository</name>
      <url>https://nexus3.corp.youdao.com/repository/maven-central/</url>
    </repository>
    <repository>
      <id>nexus3-release</id>
      <name>internal nexus3 release repository</name>
      <url>https://nexus3.corp.youdao.com/repository/maven-releases/</url>
    </repository>
    <repository>
      <id>nexus3-snapshot</id>
      <name>internal nexus3 snapshot repository</name>
      <url>https://nexus3.corp.youdao.com/repository/maven-snapshots/</url>
    </repository>
    <repository>
      <releases />
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>ead</id>
      <url>http://nexus.corp.youdao.com/nexus/content/repositories/ead/</url>
    </repository>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots />
      <id>ead_snapshot</id>
      <url>https://nexus.corp.youdao.com/nexus/content/repositories/ead_snapshot/</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-core_2.12</artifactId>
      <version>3.3.2</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>avro</artifactId>
          <groupId>org.apache.avro</groupId>
        </exclusion>
        <exclusion>
          <artifactId>avro-mapred</artifactId>
          <groupId>org.apache.avro</groupId>
        </exclusion>
        <exclusion>
          <artifactId>chill_2.12</artifactId>
          <groupId>com.twitter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>chill-java</artifactId>
          <groupId>com.twitter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xbean-asm9-shaded</artifactId>
          <groupId>org.apache.xbean</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hadoop-client-api</artifactId>
          <groupId>org.apache.hadoop</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hadoop-client-runtime</artifactId>
          <groupId>org.apache.hadoop</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spark-launcher_2.12</artifactId>
          <groupId>org.apache.spark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spark-kvstore_2.12</artifactId>
          <groupId>org.apache.spark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spark-network-common_2.12</artifactId>
          <groupId>org.apache.spark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spark-network-shuffle_2.12</artifactId>
          <groupId>org.apache.spark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spark-unsafe_2.12</artifactId>
          <groupId>org.apache.spark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activation</artifactId>
          <groupId>javax.activation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>curator-recipes</artifactId>
          <groupId>org.apache.curator</groupId>
        </exclusion>
        <exclusion>
          <artifactId>zookeeper</artifactId>
          <groupId>org.apache.zookeeper</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.servlet-api</artifactId>
          <groupId>jakarta.servlet</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-codec</artifactId>
          <groupId>commons-codec</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-math3</artifactId>
          <groupId>org.apache.commons</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-text</artifactId>
          <groupId>org.apache.commons</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-collections</artifactId>
          <groupId>commons-collections</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-collections4</artifactId>
          <groupId>org.apache.commons</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jul-to-slf4j</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jcl-over-slf4j</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>log4j-slf4j-impl</artifactId>
          <groupId>org.apache.logging.log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>log4j-api</artifactId>
          <groupId>org.apache.logging.log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>log4j-core</artifactId>
          <groupId>org.apache.logging.log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>log4j-1.2-api</artifactId>
          <groupId>org.apache.logging.log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>compress-lzf</artifactId>
          <groupId>com.ning</groupId>
        </exclusion>
        <exclusion>
          <artifactId>snappy-java</artifactId>
          <groupId>org.xerial.snappy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>lz4-java</artifactId>
          <groupId>org.lz4</groupId>
        </exclusion>
        <exclusion>
          <artifactId>zstd-jni</artifactId>
          <groupId>com.github.luben</groupId>
        </exclusion>
        <exclusion>
          <artifactId>RoaringBitmap</artifactId>
          <groupId>org.roaringbitmap</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scala-xml_2.12</artifactId>
          <groupId>org.scala-lang.modules</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scala-reflect</artifactId>
          <groupId>org.scala-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>json4s-jackson_2.12</artifactId>
          <groupId>org.json4s</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jersey-client</artifactId>
          <groupId>org.glassfish.jersey.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jersey-common</artifactId>
          <groupId>org.glassfish.jersey.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jersey-server</artifactId>
          <groupId>org.glassfish.jersey.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jersey-container-servlet</artifactId>
          <groupId>org.glassfish.jersey.containers</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jersey-container-servlet-core</artifactId>
          <groupId>org.glassfish.jersey.containers</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jersey-hk2</artifactId>
          <groupId>org.glassfish.jersey.inject</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>stream</artifactId>
          <groupId>com.clearspring.analytics</groupId>
        </exclusion>
        <exclusion>
          <artifactId>metrics-jvm</artifactId>
          <groupId>io.dropwizard.metrics</groupId>
        </exclusion>
        <exclusion>
          <artifactId>metrics-json</artifactId>
          <groupId>io.dropwizard.metrics</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-databind</artifactId>
          <groupId>com.fasterxml.jackson.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-module-scala_2.12</artifactId>
          <groupId>com.fasterxml.jackson.module</groupId>
        </exclusion>
        <exclusion>
          <artifactId>ivy</artifactId>
          <groupId>org.apache.ivy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>oro</artifactId>
          <groupId>oro</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pickle</artifactId>
          <groupId>net.razorvine</groupId>
        </exclusion>
        <exclusion>
          <artifactId>py4j</artifactId>
          <groupId>net.sf.py4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-crypto</artifactId>
          <groupId>org.apache.commons</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-sql_2.12</artifactId>
      <version>3.3.2</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>scala-library</artifactId>
          <groupId>org.scala-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>rocksdbjni</artifactId>
          <groupId>org.rocksdb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>univocity-parsers</artifactId>
          <groupId>com.univocity</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spark-sketch_2.12</artifactId>
          <groupId>org.apache.spark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spark-catalyst_2.12</artifactId>
          <groupId>org.apache.spark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>orc-core</artifactId>
          <groupId>org.apache.orc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>orc-mapreduce</artifactId>
          <groupId>org.apache.orc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hive-storage-api</artifactId>
          <groupId>org.apache.hive</groupId>
        </exclusion>
        <exclusion>
          <artifactId>parquet-column</artifactId>
          <groupId>org.apache.parquet</groupId>
        </exclusion>
        <exclusion>
          <artifactId>parquet-hadoop</artifactId>
          <groupId>org.apache.parquet</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-databind</artifactId>
          <groupId>com.fasterxml.jackson.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xbean-asm9-shaded</artifactId>
          <groupId>org.apache.xbean</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <scala.version>2.12.0</scala.version>
    <scopt.version>3.5.0</scopt.version>
    <maven.compiler.source>8</maven.compiler.source>
    <spark.avro.version>3.3.2</spark.avro.version>
    <spark.scala.version>2.12</spark.scala.version>
    <spark.version>3.3.2</spark.version>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
</project>

