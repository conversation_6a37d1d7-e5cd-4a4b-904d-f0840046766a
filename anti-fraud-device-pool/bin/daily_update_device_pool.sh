#!/bin/bash
set -e

source device-data-pool/env.sh

# 添加环境变量检查
echo "SPARK_HOME=$SPARK_HOME"
if [ -z "$SPARK_HOME" ]; then
    echo "Error: SPARK_HOME is not set"
    exit 1
fi

process_args() {
  while [[ $# -gt 0 ]]; do
    case "$1" in
    --startDate) confOpt="$confOpt $1 $2" && shift && shift ;;
    --endDate) confOpt="$confOpt $1 $2" && shift && shift ;;
    --help | -h) usage && shift ;;
    *) shift ;;
    esac
  done
  if [[ ! "$confOpt" =~ "startDate" ]]; then
    confOpt="$confOpt --startDate $(date -d "1 days ago" +%Y-%m-%d)"
  fi
  if [[ ! "$confOpt" =~ "endDate" ]]; then
    confOpt="$confOpt --endDate $(date +%Y-%m-%d)"
  fi
}

process_args "$@"

# 设置完整jar路径
JAR_PATH="$(pwd)/device-data-pool/anti-fraud-device-pool.jar"

# 检查jar文件是否存在
if [ ! -f "$JAR_PATH" ]; then
    echo "Error: JAR file not found: $JAR_PATH"
    exit 1
fi

echo "Using JAR path: $JAR_PATH"
echo "Full command params: $confOpt"

# 构建完整的spark-submit命令
SPARK_CMD="$SPARK_HOME/bin/spark-submit \
  --class com.youdao.ead.DailyUpdateDevicePool \
  --master yarn \
  --deploy-mode client \
  --conf spark.sql.adaptive.enabled=true \
  --conf spark.sql.adaptive.coalescePartitions.enabled=true \
  --conf spark.sql.adaptive.skewJoin.enabled=true \
  --driver-memory 4G \
  --queue default \
  --executor-memory 20G \
  --executor-cores 10 \
  --num-executors 10 \
  --conf spark.network.timeout=600 \
  --conf spark.yarn.am.memory=2048 \
  --conf spark.executor.heartbeatInterval=60 \
  --conf spark.sql.shuffle.partitions=200 \
  --conf spark.hadoop.yarn.timeline-service.enabled=false \
  --conf spark.executor.extraJavaOptions='-XX:+UseG1GC -XX:+PrintGCDetails -XX:+HeapDumpOnOutOfMemoryError' \
  --conf spark.ui.enabled=false \
  --conf spark.yarn.jars=$SPARK_HOME/jars/*.jar \
  --conf spark.driver.log.level=DEBUG \
  \"$JAR_PATH\" $confOpt"

# 打印完整命令用于调试
echo "Executing command: $SPARK_CMD"

# 执行命令
eval "$SPARK_CMD"