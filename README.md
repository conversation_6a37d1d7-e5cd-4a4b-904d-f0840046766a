## pacioli 智选渠道管理系统

### 域名
~~https://channel-zhixuan.youdao.com/login~~

https://channel-zhixuan.inner.youdao.com/login

### pipeline

master: [![pipeline status](https://gitlab.corp.youdao.com/ead/pacioli/badges/master/pipeline.svg)](https://gitlab.corp.youdao.com/ead/pacioli/commits/master)

dev: [![pipeline status](https://gitlab.corp.youdao.com/ead/pacioli/badges/dev/pipeline.svg)](https://gitlab.corp.youdao.com/ead/pacioli/commits/dev)

test: [![pipeline status](https://gitlab.corp.youdao.com/ead/pacioli/badges/test/pipeline.svg)](https://gitlab.corp.youdao.com/ead/pacioli/commits/test)

sandbox: [![pipeline status](https://gitlab.corp.youdao.com/ead/pacioli/badges/sandbox/pipeline.svg)](https://gitlab.corp.youdao.com/ead/pacioli/commits/sandbox)


### intro

wiki: https://confluence.inner.youdao.com/pages/viewpage.action?pageId=221564820

matrix：

 - 线上：[服务树](https://matrix.corp.youdao.com/service_tree/serviceTree?node=6385af1ff750b4eb89575b34&parent_id=61dce9e8cbb9830e602b7a55&checkedNodeType=4)
 - 测试：[服务树](https://matrix.corp.youdao.com/service_tree/serviceTree?node=635b8a65c5451b476e7d0f3d&parent_id=5f570071a7f9fa000126e1cf&checkedNodeType=4)


### 服务启停：

```shell
sh bin/start.sh (local/dev/test/online)

sh bin/stop.sh
```

### 更新常驻测试环境

向dev/test/sandbox分支推送代码即可部署到对应的环境。

### Notice

Using JDK17 + Gradle 7.5

### xxl-job部署

online: https://ead-xxl-job-admin.inner.youdao.com/xxl-job-admin/jobinfo?jobGroup=5

dev: https://ead-xxl-job-admin-test.inner.youdao.com/xxl-job-admin/jobinfo?jobGroup=17

test: https://ead-xxl-job-admin-test.inner.youdao.com/xxl-job-admin/jobinfo?jobGroup=19
