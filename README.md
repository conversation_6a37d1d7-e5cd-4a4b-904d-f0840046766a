# quipu-camayos

## WIKI
http://confluence.inner.youdao.com/pages/viewpage.action?pageId=2525093

## 使用方法
- 启动线上：`sh bin/start online`
- 启动带debug参数：`sh bin/start debug`
- 关闭：`sh bin/stop`

## 介绍
[quipu-camayos](http://camayos.inner.youdao.com/) 是新统计的查询前端，使用 [Play](https://www.playframework.com/) 框架
编写。前端请求发至 quipu-camayos 后，通过 [druid query api](http://druid.io/docs/latest/querying/querying.html) 获取
统计数据，渲染后，返回前端。

## CI/CD

将代码推送至`test`分支即可触发测试环境的部署。
将代码合入master后会触发线上环境的build，手动执行deploy即可完成上线。

浏览器请求示例：

```
http://camayos.inner.youdao.com/sdk_stat_new/query?dataSource=sdk_stat_new&interval.start=2017-01-26&interval.end=2017-01-27&granularity=day&filter%5B0%5D.dimension=&filter%5B0%5D.type=eq&filter%5B0%5D.value=&groupBy%5B%5D=app_id&having%5B0%5D.dimension=&having%5B0%5D.type=eq&having%5B0%5D.value=&aggregation%5B%5D=pv&orderBy%5B0%5D.ascending=false&orderBy%5B0%5D.dimension=&limit=3&dropZeros=true
```

参数为：

```
http://camayos.inner.youdao.com/sdk_stat_new/query?dataSource=sdk_stat_new
&interval.start=2017-01-26
&interval.end=2017-01-27
&granularity=day
&filter[0].dimension=
&filter[0].type=eq
&filter[0].value=
&groupBy[]=app_id
&having[0].dimension=
&having[0].type=eq
&having[0].value=
&aggregation[]=pv
&orderBy[0].ascending=false
&orderBy[0].dimension=
&limit=3
&dropZeros=true
```

请求到达 quipu-camayos 后，向 druid 发起请求示例：

```
URL: http://eadata-druid-broker.corp.youdao.com/druid/v2
Method: POST
Header:
Content-Type: application/json
Request Body:
{
  "queryType": "groupBy",
  "dataSource": "sdk_stat",
  "granularity": "day",
  "intervals": [ "2017-01-26T00:00:00.000/2017-01-27T00:00:00.000" ],
  "dimensions": ["app_id"],
  "limitSpec": { "type": "default", "limit": 3, "columns": ["app_id"] },
  "aggregations": [
    { "type": "longSum", "name": "pv", "fieldName": "pv" }
  ]
}
```

druid 响应示例:

```
Response Body:
[
  {
    "version": "v1",
    "timestamp": "2017-01-25T00:00:00.000Z",
    "event": {
      "app_id": null,
      "pv": 0
    }
  },
  {
    "version": "v1",
    "timestamp": "2017-01-25T00:00:00.000Z",
    "event": {
      "app_id": "1002",
      "pv": 1037
    }
  },
  {
    "version": "v1",
    "timestamp": "2017-01-25T00:00:00.000Z",
    "event": {
      "app_id": "1004",
      "pv": 88
    }
  }
]
```