package com.youdao.ead

import com.google.gson.Gson
import org.apache.spark.sql.{Row, SparkSession}
import org.slf4j.{Logger, LoggerFactory}
import outfox.venus.client2.VenusClient
import com.youdao.ead.DailyUpdateHyDmp._
import com.youdao.ead.config.DailyUpdateHyDmpConfig._
import com.youdao.ead.config.{HyDmpToVenusConfig, HyDmpToVenusParser}
import com.youdao.ead.util.CommonUtil
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import outfox.ead.dsp.protocol.youdao.EadDmp
import outfox.ead.dsp.protocol.youdao.EadDmp.{FeatureIntDouble, FeatureLongDouble, FeatureStringDouble, HyFeature}
import outfox.venus.client2.data.{BasicData, Id}

import java.time.LocalDate
import java.util.ArrayList
import java.util.concurrent.atomic.AtomicInteger

/**
 * 杭研数据写venus任务
 * <AUTHOR>
 * @date 2024/1/19
 */
object HyDmpToVenus {

  private val logger = LoggerFactory.getLogger(HyDmpToVenus.getClass)

  def main(args: Array[String]): Unit = {
    logger.info(s"args is ${args.mkString("Array(", ", ", ")")}")
    val spark = SparkSession
      .builder()
      .appName("daily-hy-dmp-to-venus")
      .getOrCreate()
    val writeErrorCount = spark.sparkContext.longAccumulator("writeErrorCount")
    val configParser = HyDmpToVenusParser.parser
    val config = configParser.parse(args, HyDmpToVenusConfig("", LocalDate.now.minusDays(1), "orc", clean = true, wformat = "protobuf", remove = false)).get
    val hdfsInputPath = if (StringUtils.isNotBlank(config.path)) {
      config.path
    } else {
      TAG_DAILY_DIR + CommonUtil.hdfsTimeFormatter.format(config.date) + "/*"
    }
    val dataRead = spark.read.format(config.rformat)
      .load(s"$hdfsInputPath")
    val data = if (config.clean) {
      validHyDmpData(spark, dataRead, false, "birth_y", "gender", "has_chd", "chd_birth_y", "married", "province", "reside", "city_grade", "reside_estate", "reside_estate_price", "has_car", "has_house", "income_lvl", "level1_car_brand_180d", "phone_brand_180d", "phone_model_180d", "phone_price_180d", "phone_brand_max", "phone_model_max", "phone_price_max", "phone_brand_last", "phone_model_last", "phone_price_last", "pay_will_mh", "pay_will_date", "pay_will_adultedu", "pay_will_ota", "pay_will_virtuala", "pay_will_virtualb", "pay_will_virtualc", "pay_monetary_all", "edu_degree", "school_name", "school_city_grade", "school_type", "profession", "company_name", "company_industry", "company_property", "company_employee_number", "company_registered_capital", "interest_all", "oneip_list", "income")
    } else {
      dataRead
    }
    val count = data.cache.count()
    logger.info("now we know: after read and clean, data count = {}", count)

    data.repartition(120).foreachPartition(partition => {
      val venusClient = CommonUtil.venusClient()
      val gson = new Gson()
      partition.foreach(row => {
        val user_id = row.getAs[String]("user_id")
        try {
          logger.info("user_id = {}.", user_id)
          val id = if ("json".equals(config.wformat)) {
            new Id(user_id, "hyj", "j1")
          } else {
            new Id(user_id, "hy2", "p1")
          }
          if (StringUtils.isNotBlank(user_id) && !CommonUtil.INVALID_DEVICE_ID_SEQ.contains(user_id)) {
            if (config.remove) {
              venusClient.remove(id)
            } else {
              val hyFeature: HyFeature.Builder = toHyFeature(row, user_id)
              val data = if ("json".equals(config.wformat)) {
                new BasicData(id, gson.toJson(hyFeature.build()))
              } else {
                new BasicData(id, hyFeature.build().toByteArray)
              }
              venusClient.set(data)
            }
          }
        } catch {
          case ex: Throwable => {
            writeErrorCount.add(1)
            logger.error(s"write to venus error, user_id = $user_id: " , ex)
          }
        }
      })
      venusClient.close()
    })

    logger.info("writeErrorCount = {}", writeErrorCount)
  }

  private def toHyFeature(row: Row, user_id: String) = {
    val hyFeature = HyFeature.newBuilder()
    hyFeature.setUserId(user_id)
      .addAllBirthY(toFeatureIntDoubleList(row, "birth_y"))
      .addAllGender(toFeatureIntDoubleList(row, "gender"))
      .addAllHasChd(toFeatureIntDoubleList(row, "has_chd"))
      .addAllChdBirthY(toFeatureIntDoubleList(row, "chd_birth_y"))
      .addAllMarried(toFeatureIntDoubleList(row, "married"))
      .addAllProvince(toFeatureStringDoubleList(row, "province"))
      .addAllReside(toFeatureStringDoubleList(row, "reside"))
      .addAllCityGrade(toFeatureIntDoubleList(row, "city_grade"))
      .addAllResideEstate(toFeatureStringDoubleList(row, "reside_estate"))
      .addAllResideEstatePrice(toFeatureStringDoubleList(row, "reside_estate_price"))
      .addAllHasCar(toFeatureIntDoubleList(row, "has_car"))
      .addAllHasHouse(toFeatureIntDoubleList(row, "has_house"))
      .addAllIncomeLvl(toFeatureIntDoubleList(row, "income_lvl"))
      .addAllIncome(toFeatureIntDoubleList(row, "income"))
      .addAllLevel1CarBrand180D(toFeatureStringDoubleList(row, "level1_car_brand_180d"))
      .addAllPhoneBrand180D(toFeatureStringDoubleList(row, "phone_brand_180d"))
      .addAllPhoneModel180D(toFeatureStringDoubleList(row, "phone_model_180d"))
      .addAllPhoneBrandMax(toFeatureStringDoubleList(row, "phone_brand_max"))
      .addAllPhoneModelMax(toFeatureStringDoubleList(row, "phone_model_max"))
      .addAllPhoneBrandLast(toFeatureStringDoubleList(row, "phone_brand_last"))
      .addAllPhoneModelLast(toFeatureStringDoubleList(row, "phone_model_last"))
      .addAllPhonePrice180D(toFeatureIntDoubleList(row, "phone_price_180d"))
      .addAllPhonePriceMax(toFeatureIntDoubleList(row, "phone_price_max"))
      .addAllPhonePriceLast(toFeatureIntDoubleList(row, "phone_price_last"))
      .addAllPayWillMh(toFeatureIntDoubleList(row, "pay_will_mh"))
      .addAllPayWillDate(toFeatureIntDoubleList(row, "pay_will_date"))
      .addAllPayWillAdultedu(toFeatureIntDoubleList(row, "pay_will_adultedu"))
      .addAllPayWillOta(toFeatureIntDoubleList(row, "pay_will_ota"))
      .addAllPayWillVirtuala(toFeatureIntDoubleList(row, "pay_will_virtuala"))
      .addAllPayWillVirtualb(toFeatureIntDoubleList(row, "pay_will_virtualb"))
      .addAllPayWillVirtualc(toFeatureIntDoubleList(row, "pay_will_virtualc"))
      .addAllPayMonetaryAll(toFeatureIntDoubleList(row, "pay_monetary_all"))
      .addAllEduDegree(toFeatureIntDoubleList(row, "edu_degree"))
      .addAllSchoolName(toFeatureLongDoubleList(row, "school_name"))
      .addAllSchoolCityGrade(toFeatureIntDoubleList(row, "school_city_grade"))
      .addAllSchoolType(toFeatureIntDoubleList(row, "school_type"))
      .addAllProfession(toFeatureIntDoubleList(row, "profession"))
      .addAllCompanyName(toFeatureLongDoubleList(row, "company_name"))
      .addAllCompanyIndustry(toFeatureLongDoubleList(row, "company_industry"))
      .addAllCompanyProperty(toFeatureLongDoubleList(row, "company_property"))
      .addAllCompanyEmployeeNumber(toFeatureIntDoubleList(row, "company_employee_number"))
      .addAllCompanyRegisteredCapital(toFeatureIntDoubleList(row, "company_registered_capital"))
//      .addAllInterestAll(toFeatureLongDoubleList(row, "interest_all"))
//      .addAllOneipList(toFeatureLongDoubleList(row, "oneip_list"))
    hyFeature
  }

  private def toFeatureIntDoubleList(row: Row, fieldName :String): java.lang.Iterable[_ <:EadDmp.FeatureIntDouble] = {
    try {
      if (row.isNullAt(row.fieldIndex(fieldName))) {
        CollectionUtils.emptyCollection()
      } else {
        val list = new ArrayList[FeatureIntDouble]();
        row.getAs[Seq[Row]](fieldName).map(x => {
          val featureIntDouble = FeatureIntDouble.newBuilder()
          featureIntDouble.setID(x.getString(0).toInt)
            .setWT(x.getString(2).toDouble)
            .build()
          list.add(featureIntDouble.build())
        })
        list
      }
    } catch {
      case ex: Throwable => {
        logger.warn(s"convert feature failed, return empty feature, fieldName = $fieldName. ", ex)
        CollectionUtils.emptyCollection()
      }
    }
  }

  private def toFeatureLongDoubleList(row: Row, fieldName :String): java.lang.Iterable[_ <:EadDmp.FeatureLongDouble] = {
    try {
      if (row.isNullAt(row.fieldIndex(fieldName))) {
        CollectionUtils.emptyCollection()
      } else {
        val list = new ArrayList[FeatureLongDouble]();
        row.getAs[Seq[Row]](fieldName).map(x => {
          val featureLongDouble = FeatureLongDouble.newBuilder()
          featureLongDouble.setID(x.getString(0).toLong)
            .setWT(x.getString(2).toDouble)
            .build()
          list.add(featureLongDouble.build())
        })
        list
      }
    } catch {
      case ex: Throwable => {
        logger.warn(s"convert feature failed, return empty feature, fieldName = $fieldName. ", ex)
        CollectionUtils.emptyCollection()
      }
    }
  }

  private def toFeatureStringDoubleList(row: Row, fieldName :String): java.lang.Iterable[_ <:FeatureStringDouble] = {
    try {
      if (row.isNullAt(row.fieldIndex(fieldName))) {
        CollectionUtils.emptyCollection()
      } else {
        val list = new ArrayList[FeatureStringDouble]();
        row.getAs[Seq[Row]](fieldName).foreach(x => {
          val featureStringDouble = FeatureStringDouble.newBuilder()
          featureStringDouble.setID(x.getString(0))
            .setWT(x.getString(2).toDouble)
          list.add(featureStringDouble.build())
        })
        list
      }
    } catch {
      case ex: Throwable => {
        logger.warn(s"convert feature failed, return empty feature, fieldName =  $fieldName. ", ex)
        CollectionUtils.emptyCollection()
      }
    }
  }
}
