package com.youdao.ead

import org.apache.spark.sql.{DataFrame, SparkSession}
import org.slf4j.{Logger, LoggerFactory}

import java.time.{Duration, LocalDate}
import java.time.format.DateTimeFormatter
import scala.util.Try
import com.youdao.ead.config.ProduceRawMaterialsConfig.{CPA_CONV_APP_ID, CPA_CONV_DIR, DEVICE_ID_FILTER_APP_ID, INPUT_DIR, OUTPUT_DEVICE_ID_DIR, OUTPUT_PV_DIR, OUTPUT_PV_YUNEI_DIR, PV_FILTER_APP_ID, PV_RESERVE_RATE, PV_YUNEI_FILTER_APP_ID, ZHIXUAN_CONV_APP_ID, ZHIXUAN_CONV_DIR}
import com.youdao.ead.config.{ProduceRawMaterialsConfig, ProduceRawMaterialsParser}
import com.youdao.ead.util.CommonUtil
import org.apache.spark.sql.functions._


/**
 * <AUTHOR>
 * @date 2023/12/15
 */
object ProduceRawMaterials {
  val outputTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("dd")

  val logger: Logger = LoggerFactory.getLogger(ProduceRawMaterials.getClass)
  def main(args: Array[String]): Unit = {
    logger.info(s"args is ${args.mkString("Array(", ", ", ")")}")
    val spark = SparkSession
      .builder()
      .appName("hy-dmp-porter")
      .getOrCreate()
    val configParser = ProduceRawMaterialsParser.parser
    val config = configParser.parse(args, ProduceRawMaterialsConfig(LocalDate.now.minusDays(1), LocalDate.now, Seq("pv", "device_id"))).get
    logger.info(s"tasks is ${config.tasks.mkString(", ")}")
    val diffDays = Duration.between(config.startDate.atStartOfDay(), config.endDate.atStartOfDay()).toDays
    for (offset <- 0L until diffDays) {
      val date = config.startDate.plusDays(offset)
      produce(spark, date, config.tasks)
    }
  }

  private def produce(spark: SparkSession, date: LocalDate, tasks: Seq[String]): Unit = {
    val yearMonthDay = date.format(CommonUtil.hdfsTimeFormatter)
    val dataLoadPath = INPUT_DIR + yearMonthDay + "/*/*.avro"
    val day = date.format(outputTimeFormatter)
    if (tasks.contains("pv")) {
      producePv(spark, dataLoadPath, day)
    }

    if (tasks.contains("device_id")) {
      produceDeviceId(spark, yearMonthDay, dataLoadPath, day)
    }

    if (tasks.contains("pv_yunei")) {
      producePvYunei(spark, dataLoadPath, day)
    }
  }
  private def producePv(spark: SparkSession, dataLoadPath: String, day: String): Unit = {
    spark.read.format("avro")
      .load(dataLoadPath)
      .filter(PV_FILTER_APP_ID)
      .sample(withReplacement = false, fraction = PV_RESERVE_RATE)
      .coalesce(240)
      .write
      .format("avro")
      .mode("overwrite")
      .save(OUTPUT_PV_DIR + day + "/")
  }

  private def produceDeviceId(spark: SparkSession, yearMonthDay: String, dataLoadPath: String, day: String): Unit = {
    val tmpNeteaseDeviceIdDir = OUTPUT_DEVICE_ID_DIR + "tmp_netease/" + day + "/"
    val dataNetease: DataFrame = readDataNetease(spark, dataLoadPath, tmpNeteaseDeviceIdDir)

    val cpaConvLoadPath = CPA_CONV_DIR + yearMonthDay + "/*/*.avro"
    val tmpCpaDeviceIdDir = OUTPUT_DEVICE_ID_DIR + "tmp_cpa/" + day + "/"
    val dataCpa: DataFrame = readDataCpa(spark, cpaConvLoadPath, tmpCpaDeviceIdDir)

    val zhixuanConvLoadPath = ZHIXUAN_CONV_DIR + yearMonthDay + "/*/*.avro"
    val tmpZhixuanDeviceIdDir = OUTPUT_DEVICE_ID_DIR + "tmp_zhixuan/" + day + "/"
    val dataZhixuan: DataFrame = readDataZhixuan(spark, zhixuanConvLoadPath, tmpZhixuanDeviceIdDir)

    val dataUnion = dataNetease.union(dataCpa).union(dataZhixuan)
      .filter("!((imei is null or imei = '') and (oaid is null or oaid = '') and (idfa is null or idfa = ''))")
      .cache()

    val internalDeviceIdDir = OUTPUT_DEVICE_ID_DIR + "internal/" + yearMonthDay + "/"
    dataUnion.groupBy("pv_device_id")
      .agg(first("imei").as("imei"), first("oaid").as("oaid"),
        first("idfa").as("idfa"), first("caids").as("caids"),
        collect_set("app_id").as("app_ids"))
      .coalesce(2).write.format("avro").mode("overwrite").save(internalDeviceIdDir)
    val toHyDeviceIdDir = OUTPUT_DEVICE_ID_DIR + yearMonthDay + "/"
    dataUnion.dropDuplicates("pv_device_id")
      .coalesce(2).write.format("avro").mode("overwrite").save(toHyDeviceIdDir)
  }

  private def readDataNetease(spark: SparkSession, dataLoadPath: String, tmpNeteaseDeviceIdDir: String) = {
    spark.read.format("avro")
      .load(dataLoadPath)
      .filter(DEVICE_ID_FILTER_APP_ID)
      .select("imei", "oaid", "idfa", "caids", "app_id")
      // fix for : https://zeppelin.corp.youdao.com/#/notebook/2JRC1NQN2
      .withColumn("pv_device_id", when(col("idfa").isNotNull && length(col("idfa")) > 0, upper(col("idfa")))
        .when(col("imei").isNotNull && length(col("imei")) > 0, upper(md5(upper(col("imei")))))
        .otherwise(upper(col("oaid"))))
      .coalesce(240).write.format("avro").mode("overwrite").save(tmpNeteaseDeviceIdDir)
    val dataNetease = spark.read.format("avro")
      .load(tmpNeteaseDeviceIdDir + "*.avro")
      .select("imei", "oaid", "idfa", "caids", "app_id", "pv_device_id")
    dataNetease
  }

  private def readDataCpa(spark: SparkSession, cpaConvLoadPath: String, tmpCpaDeviceIdDir: String) = {
    // cpa日志中imei实际是原值md5 or 原值md5大写 or 原值
    spark.read.format("avro")
      .load(cpaConvLoadPath)
      .withColumn("imei_md5_upper", when(col("imei").isNotNull && length(col("imei")) === 15, upper(md5(upper(col("imei")))))
        .when(col("imei").isNotNull && length(col("imei")) === 32, upper(col("imei")))
        .otherwise(lit("")))
      .withColumn("imei", when(col("imei").isNotNull && length(col("imei")) === 15, upper(col("imei")))
        .otherwise(lit("")))
      // cpa日志的conv_device_id字段生成规则和智选的不同，需要重新生成一下pv_device_id。
      .withColumn("pv_device_id", when(col("idfa").isNotNull && length(col("idfa")) > 0, upper(col("idfa")))
        .when(col("imei_md5_upper").isNotNull && length(col("imei_md5_upper")) > 0, col("imei_md5_upper"))
        .otherwise(upper(col("oaid"))))
      .select("imei", "oaid", "idfa", "caids", "pv_device_id")
      // 正常用 overwrite，补历史数据，可以提前准备好历史数据设备号用 append，
      .write.format("avro").mode("overwrite").save(tmpCpaDeviceIdDir)
    // 由于历史对接原因，cpa的数据要用于查询杭研标签，需要加一个虚构的app_id信息。
    val dataCpa = spark.read.format("avro")
      .load(tmpCpaDeviceIdDir + "*.avro")
      .withColumn("app_id", lit(CPA_CONV_APP_ID))
      .select("imei", "oaid", "idfa", "caids", "app_id", "pv_device_id")
    dataCpa
  }

  private def readDataZhixuan(spark: SparkSession, zhixuanConvLoadPath: String, tmpZhixuanDeviceIdDir: String) = {
    spark.read.format("avro")
      .load(zhixuanConvLoadPath)
      .withColumn("pv_device_id", when(col("idfa").isNotNull && length(col("idfa")) > 0, upper(col("idfa")))
        .when(col("imei").isNotNull && length(col("imei")) > 0, upper(md5(upper(col("imei")))))
        .otherwise(upper(col("oaid"))))
      .select("imei", "oaid", "idfa", "caids", "pv_device_id")
      // 正常用 overwrite，补历史数据，可以提前准备好历史数据设备号用 append，
      .write.format("avro").mode("overwrite").save(tmpZhixuanDeviceIdDir)
    // 由于历史对接原因，cpa的数据要用于查询杭研标签，需要加一个虚构的app_id信息。
    val dataZhixuan = spark.read.format("avro")
      .load(tmpZhixuanDeviceIdDir + "*.avro")
      .withColumn("app_id", lit(ZHIXUAN_CONV_APP_ID))
      .select("imei", "oaid", "idfa", "caids", "app_id", "pv_device_id")
    dataZhixuan
  }

  private def producePvYunei(spark: SparkSession, dataLoadPath: String, day: String): Unit = {
    spark.read.format("avro")
      .load(dataLoadPath)
      .filter(PV_YUNEI_FILTER_APP_ID)
      .coalesce(240)
      .write
      .format("avro")
      .mode("overwrite")
      .save(OUTPUT_PV_YUNEI_DIR + day + "/")
  }


  def convertToInt(str: String): Int = {
    Try(str.toInt).getOrElse(1)
  }

}
