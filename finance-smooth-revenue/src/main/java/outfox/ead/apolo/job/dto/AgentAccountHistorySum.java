package outfox.ead.apolo.job.dto;

import lombok.Data;

/**
 * 描述: 代理商入资、撤资等账户操作历史金额的总计类
 */
@Data
public class AgentAccountHistorySum {
    /** 现金入资总和*/
    private Long actualDepositSum;

    /** 现金撤资总和*/
    private Long actualRefundSum;

    /** 从广告商撤资总和*/
    private Long refundFromSponsorSum;

    /** 为广告商入资总和*/
    private Long depositToSponsorSum;

    /** 虚拟金充值总和*/
    private Long virtualMarkDepositSum;

    /** 虚拟金撤资总和*/
    private Long virtualMarkRefundSum;

    /** 预付返点总和*/
    private Long profitPrepaidSum;

    /** 消费返点总和*/
    private Long profitSettledSum;

    /** 返点撤资总和*/
    private Long profitRefundSum;

    /** 设置折扣率，不会产生金额变动*/
    private Long setDiscountRate;

    /** 额外扣款总和*/
    private Long extraRevenueSum;

    /** 收取服务费总和*/
    private Long serviceChargeSum;

    /**
     * 退还服务费总和
     */
    private Long serviceChargeBackSum;

    public AgentAccountHistorySum() {
        this.actualDepositSum = 0L;
        this.actualRefundSum = 0L;
        this.refundFromSponsorSum = 0L;
        this.depositToSponsorSum = 0L;
        this.virtualMarkDepositSum = 0L;
        this.virtualMarkRefundSum = 0L;
        this.profitPrepaidSum = 0L;
        this.profitRefundSum = 0L;
        this.profitSettledSum = 0L;
        this.setDiscountRate = 0L;
        this.extraRevenueSum = 0L;
        this.serviceChargeSum = 0L;
        this.serviceChargeBackSum = 0L;
    }
}
