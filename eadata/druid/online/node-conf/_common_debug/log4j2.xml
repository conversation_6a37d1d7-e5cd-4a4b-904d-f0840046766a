<?xml version="1.0" encoding="UTF-8" ?>
<Configuration status="WARN" monitorInterval="300">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{ISO8601} %p [%t] %c - %m%n"/>
        </Console>
        <RollingFile name="File" fileName="logs/log" filePattern="logs/log-%d{yyyy-MM-dd}">
            <PatternLayout>
                <Pattern>%d{ISO8601} %p [%t] %c - %m%n"</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <DefaultRolloverStrategy max="3">
		<Delete basePath="logs/" maxDepth="2">
    		    <IfLastModified age="3D"/>
		</Delete>
	    </DefaultRolloverStrategy>
        </RollingFile>
        <Async name="AsyncFile">
            <AppenderRef ref="File"/>
        </Async>
    </Appenders>

    <Loggers>
        <Root level="debug">
            <AppenderRef ref="AsyncFile"/>
        </Root>
    </Loggers>
</Configuration>
