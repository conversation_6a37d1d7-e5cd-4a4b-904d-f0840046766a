#druid.service=druid/broker
druid.port=8080

# picks the node with the fewest number of active connections to
# 如果设置为connectionCount，线上运行时发现会很大概率将各segment的请求发送到同一个历史节点上。
druid.broker.balancer.type=random
# druid.broker.select.tier=custom
druid.broker.select.tier=lowestPriority
druid.broker.select.tier.custom.priorities=[1]

# HTTP server threads
druid.broker.http.numConnections=400
druid.server.http.numThreads=400
#druid.broker.http.readTimeout=PT1H
druid.server.http.maxIdleTime=PT1H

# Processing threads and buffers
druid.processing.buffer.sizeBytes=536870912
#druid.processing.numThreads=15

# Query cache
#druid.broker.cache.useCache=true
#druid.broker.cache.populateCache=true
#druid.cache.type=local
#druid.cache.sizeInBytes=2000000000

druid.request.logging.type=file
druid.request.logging.dir=request_logs/

druid.lookup.lookupTier=eadv3

#druid.monitoring.monitors=["com.metamx.metrics.JvmMonitor", "io.druid.server.metrics.QueryCountStatsMonitor"]
#druid.emitter=composing
#druid.emitter.composing.emitters=["logging","graphite"]
#druid.emitter.composing.emitters=["logging","graphite"]
#druid.emitter.graphite.hostname=quipu-graphite.inner.youdao.com
#druid.emitter.graphite.port=2014
#druid.emitter.graphite.eventConverter={"type":"whiteList", "namespacePrefix": "eadata.druid", "ignoreHostname":false, "ignoreServiceName":false, "mapPath":"/mfs_ead/home/<USER>/online/druid-conf-0.10.1/whiteList"}
#druid.emitter.logging.logLevel=info
druid.monitoring.monitors=["com.metamx.metrics.JvmMonitor","io.druid.server.metrics.QueryCountStatsMonitor","com.metamx.metrics.SysMonitor"]
druid.emitter=composing
druid.emitter.composing.emitters=["logging","graphite"]
druid.emitter.graphite.hostname=quipu-graphite.inner.youdao.com
druid.emitter.graphite.port=2014
druid.emitter.graphite.eventConverter={"type":"whiteList", "namespacePrefix": "druid-graphite", "ignoreHostname":false, "ignoreServiceName":false, "mapPath":"/mfs_ead/home/<USER>/online/druid-conf-0.10.1/whiteList"}
druid.emitter.logging.logLevel=info
