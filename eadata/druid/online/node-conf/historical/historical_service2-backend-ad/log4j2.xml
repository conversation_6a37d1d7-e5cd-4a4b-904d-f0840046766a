<?xml version="1.0" encoding="UTF-8" ?>
<Configuration status="WARN" monitorInterval="60">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{ISO8601} %p [%t] %c - %m%n"/>
        </Console>
        <RollingFile name="File" fileName="logs/log" filePattern="logs/log.%d{yyyy-MM-dd-HH}">
            <PatternLayout>
                <Pattern>%d{ISO8601} %p [%t] %c - %m%n"</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="logs/" maxDepth="2">
                    <!-- 如果没有IfFileName，其它日志也会被删除掉，如GC、stderr等，所以限定文件名 -->
                    <IfFileName regex="log.*"/>
                    <ifAny>
                        <!-- 最大保留2天或300GB日志 -->
                        <IfLastModified age="2D"/>
                        <IfAccumulatedFileSize exceeds="300 GB"/>
                    </ifAny>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <Async name="AsyncFile">
            <AppenderRef ref="File"/>
        </Async>
    </Appenders>

    <Loggers>
        <Root level="info">
            <AppenderRef ref="AsyncFile"/>
        </Root>
    </Loggers>
</Configuration>
