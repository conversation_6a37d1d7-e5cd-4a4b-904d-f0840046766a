druid.service=druid/historical
druid.port=8070
druid.server.tier=rocket_gz

# HTTP server threads
druid.server.http.numThreads=200
#druid.server.http.maxIdleTime=P1D
druid.server.priority=100
druid.extensions.loadList=["graphite-emitter", "druid-hdfs-storage", "druid-avro-extensions","druid-kafka-eight", "mysql-metadata-storage","druid-histogram"]

# Processing threads and buffers
druid.processing.buffer.sizeBytes=1073741824
druid.processing.numThreads=16

# cache
druid.cache.type=local
druid.cache.sizeInBytes=10737418240
druid.historical.cache.useCache=true
druid.historical.cache.populateCache=true
druid.historical.cache.unCacheable=["select"]

druid.segmentCache.locations=[{"path": "/disk2/eadata/druid-historical-data/persistent", "maxSize": 1000000000000},{"path": "/disk3/eadata/druid-historical-data/persistent", "maxSize": ************},{"path": "/disk4/eadata/druid-historical-data/persistent", "maxSize": ************}]

# Segment storage
druid.server.maxSize=2400000000000
# storing segments
druid.segmentCache.numLoadingThreads=25
druid.segmentCache.numBootstrapThreads=20

# If you choose to compress ZK announcements, you must do so for every node type
druid.announcer.type=batch
druid.curator.compress=true
druid.query.groupBy.maxResults=5000000

druid.monitoring.monitors=["com.metamx.metrics.JvmMonitor","io.druid.server.metrics.HistoricalMetricsMonitor","io.druid.server.metrics.QueryCountStatsMonitor","com.metamx.metrics.SysMonitor"]
druid.emitter=composing
druid.emitter.composing.emitters=["logging","graphite"]
druid.emitter.graphite.hostname=quipu-graphite.inner.youdao.com
druid.emitter.graphite.port=2014
druid.emitter.graphite.eventConverter={"type":"whiteList", "namespacePrefix": "eadata.druid", "ignoreHostname":false, "ignoreServiceName":false, "mapPath":"/mfs_ead/eadata/ead-conf/eadata/druid/online/node-conf/whiteList"}
druid.emitter.logging.logLevel=info
