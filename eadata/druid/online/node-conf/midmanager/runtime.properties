druid.service=druid/middleManager
druid.port=8091

# Number of tasks per middleManager
druid.worker.capacity=70

# Task launch parameters
druid.indexer.runner.javaOpts=-server -Duser.timezone=Asia/Shanghai -Dfile.encoding=UTF-8 -Xmx5g -XX:MaxDirectMemorySize=3g -XX:+UseG1GC -XX:MaxGCPauseMillis=50 -XX:+PrintGCDetails -XX:+PrintGCTimeStamps
druid.indexer.task.baseTaskDir=var/druid/task
druid.indexer.runner.javaCommand=/usr/local/youdao/ad/jdk1.8.0_202/bin/java

# HTTP server threads
druid.server.http.numThreads=200

druid.indexer.task.restoreTasksOnRestart=true

#druid.indexer.logs.type=file
#druid.indexer.logs.directory=log
druid.indexer.logs.type=hdfs
druid.indexer.logs.directory=hdfs://eadata-hdfs/quipu/druid/index-log

# Processing threads and buffers on Peons
druid.indexer.fork.property.druid.processing.buffer.sizeBytes=536870912
druid.indexer.fork.property.druid.processing.numThreads=2
druid.indexer.fork.property.druid.server.http.numThreads=200
druid.indexer.fork.property.druid.storage.type=hdfs
druid.indexer.fork.property.druid.peon.mode=remote
druid.indexer.fork.property.druid.indexer.task.defaultHadoopCoordinates=["org.apache.hadoop:hadoop-client:2.7.3"]

druid.emitter=composing
druid.emitter.composing.emitters=["logging"]
druid.emitter.logging.logLevel=info