{"dataSources": [{"spec": {"dataSchema": {"dataSource": "elector_ranking_stat", "parser": {"type": "avro_stream", "avroBytesDecoder": {"type": "schema_repo", "subjectAndIdConverter": {"type": "avro_1124", "topic": "elector_ranking"}, "schemaRepository": {"type": "avro_1124_rest_client", "url": "http://eadata-schema-repo.inner.youdao.com/schema-repo"}}, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["slot_id"]}}}, "metricsSpec": [{"type": "longSum", "name": "elect_count", "fieldName": "elect_count"}, {"type": "longSum", "name": "candidate_number", "fieldName": "candidate_number"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR"}}, "ioConfig": {"type": "realtime"}, "tuningConfig": {"type": "realtime", "maxRowsInMemory": "100000", "intermediatePersistPeriod": "PT1H", "windowPeriod": "PT10M"}}, "properties": {"task.partitions": "1", "task.replicants": "2", "topics": "elector_ranking"}}], "properties": {"zookeeper.connect": "eadata-zk0:2181,eadata-zk1:2181,eadata-zk2:2181/druid", "druid.discovery.curator.path": "/druid/discovery", "druid.selectors.indexing.serviceName": "druid/overlord", "commit.periodMillis": "15000", "consumer.numThreads": "8", "kafka.bootstrap.servers": "es02.nbj01.corp.yodao.com:6666,es03.nbj01.corp.yodao.com:6666,es04.nbj01.corp.yodao.com:6666", "kafka.group.id": "tranquility-kafka-eadata-elector-ranking", "reportDropsAsExceptions": false, "reportParseExceptions": true, "druidBeam.randomizeTaskId": false, "serialization.format": "smile"}}