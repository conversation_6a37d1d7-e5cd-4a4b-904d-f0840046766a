{"dataSources": [{"spec": {"dataSchema": {"dataSource": "yex", "parser": {"type": "avro_stream", "avroBytesDecoder": {"type": "schema_repo", "subjectAndIdConverter": {"type": "avro_1124", "topic": "pv_sdk"}, "schemaRepository": {"type": "avro_1124_rest_client", "url": "http://eadata-schema-repo.inner.youdao.com/schema-repo"}}, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["dc_type", "dc_id", "slot_id", "app_version", "hostname", "os", "mac", "connection_type", "ssid", "imp_type", "dsp_id", "dsp_ad_type", "seat_id", "cid", "sponsor_id", "nbr", "sid", "ssids", "deal_id", "gorgon_hostname", "vender_id", "vender_source", "dict_post_id", "click_interact_type", "brand_source_slot_id", "brand_orientation_type", "ad_group_id", "bundle", "gorgon_interface_type", "cur", "country_name"]}}}, "metricsSpec": [{"type": "longSum", "name": "bid_request_count", "fieldName": "bid_request_count"}, {"type": "longSum", "name": "ad_request_count", "fieldName": "ad_request_count"}, {"type": "longSum", "name": "imp_count", "fieldName": "imp_count"}, {"type": "longSum", "name": "bid_response_count", "fieldName": "bid_response_count"}, {"type": "longSum", "name": "bid_response_count_timeout", "fieldName": "bid_response_count_timeout"}, {"type": "longSum", "name": "bid_response_count_unknown_error", "fieldName": "bid_response_count_unknown_error"}, {"type": "longSum", "name": "bid_response_count_parse_error", "fieldName": "bid_response_count_parse_error"}, {"type": "longSum", "name": "bid_response_count_no_bid", "fieldName": "bid_response_count_no_bid"}, {"type": "longSum", "name": "bid_response_count_no_bd_client", "fieldName": "bid_response_count_no_bd_client"}, {"type": "longSum", "name": "bid_response_count_connection_failed", "fieldName": "bid_response_count_connection_failed"}, {"type": "longSum", "name": "bid_response_count_request_building_error", "fieldName": "bid_response_count_request_building_error"}, {"type": "longSum", "name": "bid_response_asset_replace_filter", "fieldName": "bid_response_asset_replace_filter"}, {"type": "longSum", "name": "received_ad_request_count", "fieldName": "received_ad_request_count"}, {"type": "longSum", "name": "received_imp_count", "fieldName": "received_imp_count"}, {"type": "longSum", "name": "seat_bid_count", "fieldName": "seat_bid_count"}, {"type": "longSum", "name": "bid_count", "fieldName": "bid_count"}, {"type": "longSum", "name": "bid_count_win", "fieldName": "bid_count_win"}, {"type": "longSum", "name": "bid_count_invalid", "fieldName": "bid_count_invalid"}, {"type": "longSum", "name": "bid_count_lose", "fieldName": "bid_count_lose"}, {"type": "longSum", "name": "bid_count_test", "fieldName": "bid_count_test"}, {"type": "longSum", "name": "win_price", "fieldName": "win_price"}, {"type": "doubleSum", "name": "original_cur_bid_price", "fieldName": "original_cur_bid_price"}, {"type": "longSum", "name": "bid_price", "fieldName": "bid_price"}, {"type": "longSum", "name": "bid_price_of_win", "fieldName": "bid_price_of_win"}, {"type": "longSum", "name": "impr_count", "fieldName": "impr_count"}, {"type": "doubleSum", "name": "original_cur_impr_price", "fieldName": "original_cur_impr_price"}, {"type": "longSum", "name": "impr_price", "fieldName": "impr_price"}, {"type": "longSum", "name": "adx_impr_price", "fieldName": "adx_impr_price"}, {"type": "longSum", "name": "click_count", "fieldName": "click_count"}, {"type": "longSum", "name": "bid_count_valid", "fieldName": "bid_count_valid"}, {"type": "longSum", "name": "bid_count_invalid_imp_id", "fieldName": "bid_count_invalid_imp_id"}, {"type": "longSum", "name": "bid_count_invalid_asset_id", "fieldName": "bid_count_invalid_asset_id"}, {"type": "longSum", "name": "bid_count_invalid_price", "fieldName": "bid_count_invalid_price"}, {"type": "longSum", "name": "bid_count_invalid_attri", "fieldName": "bid_count_invalid_attri"}, {"type": "longSum", "name": "bid_count_no_attri", "fieldName": "bid_count_no_attri"}, {"type": "longSum", "name": "bid_count_invalid_url", "fieldName": "bid_count_invalid_url"}, {"type": "longSum", "name": "bid_count_no_landing_page", "fieldName": "bid_count_no_landing_page"}, {"type": "longSum", "name": "bid_count_illegal_asset", "fieldName": "bid_count_illegal_asset"}, {"type": "longSum", "name": "bid_count_no_vast_tag", "fieldName": "bid_count_no_vast_tag"}, {"type": "longSum", "name": "bid_count_invalid_vast_tag", "fieldName": "bid_count_invalid_vast_tag"}, {"type": "longSum", "name": "bid_count_invalid_image_url", "fieldName": "bid_count_invalid_image_url"}, {"type": "longSum", "name": "bid_count_invalid_landing_page", "fieldName": "bid_count_invalid_landing_page"}, {"type": "longSum", "name": "bid_count_empty_white_list", "fieldName": "bid_count_empty_white_list"}, {"type": "longSum", "name": "bid_count_invalid_download_app_info", "fieldName": "bid_count_invalid_download_app_info"}, {"type": "longSum", "name": "bid_count_quick_app_ad_blocked", "fieldName": "bid_count_quick_app_ad_blocked"}, {"type": "longSum", "name": "dp_call_up", "fieldName": "dp_call_up"}, {"type": "longSum", "name": "dp_installed", "fieldName": "dp_installed"}, {"type": "longSum", "name": "dp_success", "fieldName": "dp_success"}, {"type": "longSum", "name": "dp_not_install", "fieldName": "dp_not_install"}, {"type": "longSum", "name": "dp_installed_fail", "fieldName": "dp_installed_fail"}, {"type": "longSum", "name": "dp_unknown_fail", "fieldName": "dp_unknown_fail"}, {"type": "longSum", "name": "apk_download_start", "fieldName": "apk_download_start"}, {"type": "longSum", "name": "apk_download_complete", "fieldName": "apk_download_complete"}, {"type": "longSum", "name": "apk_install_start", "fieldName": "apk_install_start"}, {"type": "longSum", "name": "apk_install_complete", "fieldName": "apk_install_complete"}, {"type": "longSum", "name": "wechat_call_up", "fieldName": "wechat_call_up"}, {"type": "longSum", "name": "wechat_success", "fieldName": "wechat_success"}, {"type": "longSum", "name": "wechat_fail", "fieldName": "wechat_fail"}, {"type": "longSum", "name": "adx_bid_price", "fieldName": "adx_bid_price"}, {"type": "longSum", "name": "adx_bid_price_of_win", "fieldName": "adx_bid_price_of_win"}, {"type": "longSum", "name": "adx_bid_price_of_impr", "fieldName": "adx_bid_price_of_impr"}, {"type": "longSum", "name": "adx_win_price", "fieldName": "adx_win_price"}, {"type": "longSum", "name": "adx_bid_count_win", "fieldName": "adx_bid_count_win"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR"}}, "ioConfig": {"type": "realtime"}, "tuningConfig": {"type": "realtime", "maxRowsInMemory": "500000", "intermediatePersistPeriod": "PT4M", "windowPeriod": "PT50M"}}, "properties": {"task.partitions": "100", "task.replicants": "1", "topics": "yex_imp_flat"}}], "properties": {"zookeeper.connect": "eadata-zk0:2181,eadata-zk1:2181,eadata-zk2:2181/druid", "druid.discovery.curator.path": "/druid/discovery", "druid.selectors.indexing.serviceName": "druid/overlord", "commit.periodMillis": "5000", "consumer.numThreads": "50", "tranquility.maxBatchSize": "2000", "tranquility.maxPendingBatches": "20", "tranquility.blockOnFull": false, "druidBeam.firehoseBufferSize": "200000", "druidBeam.firehoseChunkSize": "10000", "kafka.bootstrap.servers": "es02.nbj01.corp.yodao.com:6666,es03.nbj01.corp.yodao.com:6666,es04.nbj01.corp.yodao.com:6666", "kafka.group.id": "tranquility-kafka-eadata-v2", "kafka.max.partition.fetch.bytes": "10485760", "reportDropsAsExceptions": false, "kafka.max.poll.records": "10000", "reportParseExceptions": true, "druidBeam.randomizeTaskId": false, "task.warmingPeriod": "PT4M", "serialization.format": "smile"}}