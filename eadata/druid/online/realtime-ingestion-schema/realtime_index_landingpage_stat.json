{"dataSources": [{"spec": {"dataSchema": {"dataSource": "landingpage_stat", "parser": {"type": "avro_stream", "avroBytesDecoder": {"type": "schema_repo", "subjectAndIdConverter": {"type": "avro_1124", "topic": "landingpage"}, "schemaRepository": {"type": "avro_1124_rest_client", "url": "http://eadata-schema-repo.inner.youdao.com/schema-repo"}}, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["landingpage_id", "sponsor_id", "group_id", "campaign_id", "variation_id", "app_id", "slot_id"]}}}, "metricsSpec": [{"type": "longSum", "name": "open", "fieldName": "action_open"}, {"type": "longSum", "name": "download", "fieldName": "action_start_download"}, {"type": "longSum", "name": "submitform", "fieldName": "action_submit_form"}, {"type": "longSum", "name": "dial", "fieldName": "action_dial"}, {"type": "longSum", "name": "copyweixin", "fieldName": "action_copy_weixin"}, {"type": "longSum", "name": "advisory", "fieldName": "action_advisory"}, {"type": "longSum", "name": "key_page", "fieldName": "action_key_page"}, {"type": "longSum", "name": "custom", "fieldName": "action_custom"}, {"type": "longSum", "name": "pv", "fieldName": "lp_tracker"}, {"type": "longSum", "name": "click", "fieldName": "click"}, {"type": "cardinality", "name": "uv", "fieldNames": ["device_id"]}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR"}}, "ioConfig": {"type": "realtime"}, "tuningConfig": {"type": "realtime", "maxRowsInMemory": "100000", "intermediatePersistPeriod": "PT1H", "windowPeriod": "PT10M"}}, "properties": {"task.partitions": "1", "task.replicants": "2", "topics": "landingpage,click_sdk_charged,click_ead_fraud,click_ead_discarded"}}], "properties": {"zookeeper.connect": "eadata-zk0:2181,eadata-zk1:2181,eadata-zk2:2181/druid", "druid.discovery.curator.path": "/druid/discovery", "druid.selectors.indexing.serviceName": "druid/overlord", "commit.periodMillis": "15000", "consumer.numThreads": "8", "kafka.bootstrap.servers": "es02.nbj01.corp.yodao.com:6666,es03.nbj01.corp.yodao.com:6666,es04.nbj01.corp.yodao.com:6666", "kafka.group.id": "tranquility-kafka-eadata-landingpage", "reportDropsAsExceptions": false, "reportParseExceptions": true, "druidBeam.randomizeTaskId": false, "serialization.format": "smile", "task.warmingPeriod": "PT5M"}}