{"dataSources": [{"spec": {"dataSchema": {"dataSource": "third_party_stat_v2", "parser": {"type": "avro_stream", "avroBytesDecoder": {"type": "schema_repo", "subjectAndIdConverter": {"type": "avro_1124", "topic": "click_third_party"}, "schemaRepository": {"type": "avro_1124_rest_client", "url": "http://eadata-schema-repo.inner.youdao.com/schema-repo"}}, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["sponsor_id", "campaign_id", "group_id", "variation_id", "conv_type", "conv_parent_type", "activity_id", "ct_id", "ct_action", "retention_days", "channel_did", "active_purchase_interval_days"]}}}, "metricsSpec": [{"type": "longSum", "name": "impr", "fieldName": "impr"}, {"type": "filtered", "filter": {"type": "not", "field": {"type": "in", "dimension": "sponsor_id", "values": ["317274", "317250"]}}, "aggregator": {"type": "longSum", "name": "click", "fieldName": "click"}}, {"type": "filtered", "filter": {"type": "not", "field": {"type": "in", "dimension": "sponsor_id", "values": ["317274", "317250"]}}, "aggregator": {"type": "longSum", "name": "conv", "fieldName": "conv"}}, {"type": "longSum", "name": "order_amount", "fieldName": "order_amount"}, {"type": "longSum", "name": "first_day_order_amount", "fieldName": "first_day_order_amount"}, {"type": "longSum", "name": "in_24_hours_order_amount", "fieldName": "in_24_hours_order_amount"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR"}}, "ioConfig": {"type": "realtime"}, "tuningConfig": {"type": "realtime", "maxRowsInMemory": "5000000", "intermediatePersistPeriod": "PT1H", "windowPeriod": "PT10M"}}, "properties": {"task.partitions": "1", "task.replicants": "2", "topics": "click_third_party,impr_third_party,conv_third_party_avro"}}], "properties": {"zookeeper.connect": "eadata-zk0:2181,eadata-zk1:2181,eadata-zk2:2181/druid", "druid.discovery.curator.path": "/druid/discovery", "druid.selectors.indexing.serviceName": "druid/overlord", "tranquility.lingerMillis": "1000", "commit.periodMillis": "15000", "consumer.numThreads": "10", "kafka.bootstrap.servers": "es02.nbj01.corp.yodao.com:6666,es03.nbj01.corp.yodao.com:6666,es04.nbj01.corp.yodao.com:6666", "kafka.group.id": "tranquility-kafka-eadata-third-party-v2", "reportDropsAsExceptions": false, "reportParseExceptions": true, "druidBeam.randomizeTaskId": false, "task.warmingPeriod": "PT3M", "serialization.format": "smile"}}