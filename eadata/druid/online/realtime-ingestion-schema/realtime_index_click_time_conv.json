{"dataSources": [{"spec": {"dataSchema": {"dataSource": "click_time_conv", "parser": {"type": "avro_stream", "avroBytesDecoder": {"type": "schema_repo", "subjectAndIdConverter": {"type": "avro_1124", "topic": "pv_sdk"}, "schemaRepository": {"type": "avro_1124_rest_client", "url": "http://eadata-schema-repo.inner.youdao.com/schema-repo"}}, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["synd_id", "developer_id", "os", "app_id", "slot_id", "agent_id", "sponsor_id", "campaign_id", "group_id", "variation_id", "conv_type", "conv_parent_type", "ct_id", "ct_type", "dest_link", "ct_action", "sid", "template_id", "enable_ocpc", "match_id", "cvr_alg_id", "strategy_id"]}}}, "metricsSpec": [{"type": "longSum", "name": "loc_conv", "fieldName": "loc_conv"}, {"type": "longSum", "name": "loc_opt_conv", "fieldName": "loc_opt_conv"}, {"type": "longSum", "name": "loc_conv_cost", "fieldName": "loc_conv_cost"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR"}}, "ioConfig": {"type": "realtime"}, "tuningConfig": {"type": "realtime", "maxRowsInMemory": "100000", "intermediatePersistPeriod": "PT10M", "windowPeriod": "PT10M"}}, "properties": {"task.partitions": "1", "task.replicants": "2", "topics": "conv_sdk_joined_click_time"}}], "properties": {"zookeeper.connect": "eadata-zk0:2181,eadata-zk1:2181,eadata-zk2:2181/druid", "druid.discovery.curator.path": "/druid/discovery", "druid.selectors.indexing.serviceName": "druid/overlord", "commit.periodMillis": "15000", "consumer.numThreads": "20", "kafka.bootstrap.servers": "es02.nbj01.corp.yodao.com:6666,es03.nbj01.corp.yodao.com:6666,es04.nbj01.corp.yodao.com:6666", "kafka.group.id": "tranquility-kafka-eadata-conv-click-time", "reportDropsAsExceptions": false, "reportParseExceptions": true, "druidBeam.randomizeTaskId": false, "serialization.format": "smile", "task.warmingPeriod": "PT5M"}}