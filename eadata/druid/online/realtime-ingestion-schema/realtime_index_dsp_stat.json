{"dataSources": [{"spec": {"dataSchema": {"dataSource": "dsp_stat", "parser": {"type": "avro_stream", "avroBytesDecoder": {"type": "schema_repo", "subjectAndIdConverter": {"type": "avro_1124", "topic": "pv_dsp"}, "schemaRepository": {"type": "avro_1124_rest_client", "url": "http://eadata-schema-repo.inner.youdao.com/schema-repo"}}, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["ad_size", "agent_id", "campaign_id", "city", "domain", "group_id", "match_id", "province", "site_id", "sponsor_id", "synd_id", "global_pid", "variation_id", "device", "context", "os", "enable_deep_link", "scenario_ids", "min_relevancy"]}}}, "metricsSpec": [{"type": "longSum", "name": "pv", "fieldName": "pv"}, {"type": "longSum", "name": "bid", "fieldName": "bid_count"}, {"type": "longSum", "name": "bid_price", "fieldName": "bid_price"}, {"type": "longSum", "name": "win_price", "fieldName": "win_price"}, {"type": "longSum", "name": "impr", "fieldName": "impr"}, {"type": "longSum", "name": "charge", "fieldName": "charge"}, {"type": "longSum", "name": "click", "fieldName": "click"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR"}}, "ioConfig": {"type": "realtime"}, "tuningConfig": {"type": "realtime", "maxRowsInMemory": "1000000", "intermediatePersistPeriod": "PT10M", "windowPeriod": "PT10M"}}, "properties": {"task.partitions": "1", "task.replicants": "1", "topics": "pv_dsp,bid_dsp,impr_dsp,click_dsp_charged"}}], "properties": {"zookeeper.connect": "eadata-zk0:2181,eadata-zk1:2181,eadata-zk2:2181/druid", "druid.discovery.curator.path": "/druid/discovery", "druid.selectors.indexing.serviceName": "druid/overlord", "commit.periodMillis": "15000", "consumer.numThreads": "8", "kafka.bootstrap.servers": "es02.nbj01.corp.yodao.com:6666,es03.nbj01.corp.yodao.com:6666,es04.nbj01.corp.yodao.com:6666", "kafka.group.id": "tranquility-kafka-eadata-v2", "reportDropsAsExceptions": false, "reportParseExceptions": true, "druidBeam.randomizeTaskId": false, "serialization.format": "smile"}}