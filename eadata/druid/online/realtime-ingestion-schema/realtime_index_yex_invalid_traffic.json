{"dataSources": [{"spec": {"dataSchema": {"dataSource": "yex_invalid_traffic_stat", "parser": {"type": "avro_stream", "avroBytesDecoder": {"type": "schema_repo", "subjectAndIdConverter": {"type": "avro_1124", "topic": "yex_invalid_traffic"}, "schemaRepository": {"type": "avro_1124_rest_client", "url": "http://eadata-schema-repo.inner.youdao.com/schema-repo"}}, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["dc_id", "slot_id", "dsp_id", "ivt_tags", "ivt_rule_id", "connection_type", "os", "os_version", "model", "manufacturer", "carrier"]}}}, "metricsSpec": [{"type": "longSum", "name": "pv", "fieldName": "pv"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR"}}, "ioConfig": {"type": "realtime"}, "tuningConfig": {"type": "realtime", "maxRowsInMemory": "1000000", "intermediatePersistPeriod": "PT10M", "windowPeriod": "PT10M"}}, "properties": {"task.partitions": "1", "task.replicants": "1", "topics": "yex_invalid_traffic"}}], "properties": {"zookeeper.connect": "eadata-zk0:2181,eadata-zk1:2181,eadata-zk2:2181/druid", "druid.discovery.curator.path": "/druid/discovery", "druid.selectors.indexing.serviceName": "druid/overlord", "commit.periodMillis": "15000", "consumer.numThreads": "8", "kafka.bootstrap.servers": "es02.nbj01.corp.yodao.com:6666,es03.nbj01.corp.yodao.com:6666,es04.nbj01.corp.yodao.com:6666", "kafka.group.id": "tranquility-kafka-eadata-yex_invalid_traffic", "reportDropsAsExceptions": false, "reportParseExceptions": true, "druidBeam.randomizeTaskId": false, "serialization.format": "smile"}}