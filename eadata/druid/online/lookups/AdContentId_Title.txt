curl -i -X POST \
   -H "Content-Type:application/json" \
   -d \
'{
    "version": "v2",
    "lookupExtractorFactory": {
        "type": "cachedNamespace",
        "extractionNamespace": {
            "type": "jdbc",
            "connectorConfig": {
                "createTables": false,
                "host": "localhost",
                "port": 1527,
                "connectURI": "************************************************",
                "user": "ead",
                "password": {
                    "type": "default",
                    "password": "ea89,d24"
                }
            },
            "table": "AdContent",
            "keyColumn": "AD_CONTENT_ID",
            "valueColumn": "TITLE",
            "tsColumn": null,
            "filter": " LAST_MOD_TIME >= unix_timestamp( date_sub(curdate(), interval 730 day)) * 1000 ",
            "pollPeriod": "PT10M"
        },
        "firstCacheTimeout": 0,
        "injective": false
    }
}' \
 'http://service2-backend-ad.gz.ynode.cn:8050/druid/coordinator/v1/lookups/eadv3/AdContentId_Title'