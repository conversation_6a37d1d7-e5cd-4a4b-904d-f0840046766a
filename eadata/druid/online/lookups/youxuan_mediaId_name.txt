curl -i -X POST \
   -H "Content-Type:application/json" \
   -d \
'{
    "version": "v3",
    "lookupExtractorFactory": {
        "type": "cachedNamespace",
        "extractionNamespace": {
            "type": "jdbc",
            "connectorConfig": {
                "createTables": false,
                "host": "localhost",
                "port": 1527,
                "connectURI": "**************************************************************************************************************************************************************************************************************************",
                "user": "youxuan_rw",
                "password": {
                    "type": "default",
                    "password": "knhqmL44Xd2AyEIcui56"
                }
            },
            "table": "Media",
            "keyColumn": "ID",
            "valueColumn": "NAME",
            "tsColumn": null,
            "filter": null,
            "pollPeriod": "PT10M"
        },
        "firstCacheTimeout": 0,
        "injective": false
    }
}' \
 'http://service2-backend-ad.gz.ynode.cn:8050/druid/coordinator/v1/lookups/eadv3/youxuan_mediaId_name'