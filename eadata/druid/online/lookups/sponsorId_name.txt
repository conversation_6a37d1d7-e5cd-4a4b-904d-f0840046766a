curl -i -X POST \
   -H "Content-Type:application/json" \
   -d \
'{
    "version": "v5",
    "lookupExtractorFactory": {
        "type": "cachedNamespace",
        "extractionNamespace": {
            "type": "jdbc",
            "connectorConfig": {
                "createTables": false,
                "host": "localhost",
                "port": 1527,
                "connectURI": "************************************************",
                "user": "eadonline4nb",
                "password": {
                    "type": "default",
                    "password": "new1ife4Th1sAugust"
                }
            },
            "table": "Sponsor",
            "keyColumn": "SPONSOR_ID",
            "valueColumn": "NAME",
            "tsColumn": null,
            "filter": null,
            "pollPeriod": "PT10M"
        },
        "firstCacheTimeout": 0,
        "injective": false
    }
}' \
 'http://service2-backend-ad.gz.ynode.cn:8050/druid/coordinator/v1/lookups/eadv3/sponsorId_name'