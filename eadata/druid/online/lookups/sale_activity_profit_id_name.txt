curl -i -X POST \
   -H "Content-Type:application/json" \
   -d \
'{
    "version": "v2",
    "lookupExtractorFactory": {
        "type": "cachedNamespace",
        "extractionNamespace": {
            "type": "jdbc",
            "connectorConfig": {
                "createTables": false,
                "host": "localhost",
                "port": 1527,
                "connectURI": "**************************************************************",
                "user": "sales_activity_rw",
                "password": {
                    "type": "default",
                    "password": "zdN15yPTV3KB3cprgLio"
                }
            },
            "table": "sign_right",
            "keyColumn": "id",
            "valueColumn": "name",
            "tsColumn": null,
            "filter": null,
            "pollPeriod": "PT10M"
        },
        "firstCacheTimeout": 0,
        "injective": false
    }
}' \
 'http://service2-backend-ad.gz.ynode.cn:8050/druid/coordinator/v1/lookups/eadv3/sale_activity_profit_id_name'