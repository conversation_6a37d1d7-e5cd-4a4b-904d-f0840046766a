curl -i -X POST \
   -H "Content-Type:application/json" \
   -d \
'{
    "version": "v7",
    "lookupExtractorFactory": {
        "type": "cachedNamespace",
        "extractionNamespace": {
            "type": "jdbc",
            "connectorConfig": {
                "createTables": false,
                "host": "localhost",
                "port": 1527,
                "connectURI": "**************************************************",
                "user": "ead",
                "password": {
                    "type": "default",
                    "password": "ea89,d24"
                }
            },
            "table": "adcontent",
            "keyColumn": "AD_CONTENT_ID",
            "valueColumn": "DEST_LINK",
            "tsColumn": null,
            "filter": null,
            "pollPeriod": "PT10M"
        },
        "firstCacheTimeout": 0,
        "injective": false
    }
}' \
 'http://service2-backend-ad.gz.ynode.cn:8050/druid/coordinator/v1/lookups/eadv3/brand_adVariationId_link'