{"type": "index_hadoop", "spec": {"dataSchema": {"dataSource": "in_flight_spend", "parser": {"type": "avro_hadoop", "fromPigAvroStorage": false, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["slot_id", "sponsor_id", "campaign_id", "group_id", "variation_id"]}}}, "metricsSpec": [{"type": "doubleSum", "name": "bid_predict_cost", "fieldName": "cost"}, {"type": "longSum", "name": "bid_time_charge", "fieldName": "bid_time_charge"}], "granularitySpec": {"type": "uniform", "segmentGranularity": {"type": "period", "period": "PT1H", "timeZone": "Asia/Shanghai"}, "queryGranularity": "HOUR", "intervals": []}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "static", "inputFormat": "io.druid.data.input.avro.AvroValueInputFormat", "paths": ""}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "hashed", "targetPartitionSize": 500000}, "shardSpecs": {}, "rowFlushBoundary": 10000, "leaveIntermediate": false, "cleanupOnFailure": true, "overwriteFiles": false, "ignoreInvalidRows": false, "useCombiner": true, "jobProperties": {"mapreduce.map.memory.mb": "1024", "mapreduce.map.java.opts": " -Xmx750m ", "mapreduce.reduce.memory.mb": "3072", "mapreduce.reduce.java.opts": " -Xmx2G ", "mapreduce.map.output.compress": "true", "mapreduce.map.output.compress.codec": "org.apache.hadoop.io.compress.DeflateCodec", "mapreduce.job.user.classpath.first": "true"}, "combineText": false}}}