{"type": "index_hadoop", "spec": {"dataSchema": {"dataSource": "dsp_stat", "parser": {"type": "avro_hadoop", "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["ad_size", "agent_id", "campaign_id", "city", "domain", "group_id", "match_id", "province", "site_id", "sponsor_id", "synd_id", "global_pid", "variation_id", "device", "context", "os", "enable_deep_link", "scenario_ids", "min_relevancy"]}}}, "metricsSpec": [{"type": "longSum", "name": "pv", "fieldName": "pv"}, {"type": "longSum", "name": "bid", "fieldName": "bid_count"}, {"type": "longSum", "name": "bid_price", "fieldName": "bid_price"}, {"type": "longSum", "name": "win_price", "fieldName": "win_price"}, {"type": "longSum", "name": "impr", "fieldName": "impr"}, {"type": "longSum", "name": "charge", "fieldName": "charge"}, {"type": "longSum", "name": "click", "fieldName": "click"}], "granularitySpec": {"type": "uniform", "segmentGranularity": {"type": "period", "period": "PT1H", "timeZone": "Asia/Shanghai"}, "queryGranularity": "HOUR", "intervals": []}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "static", "inputFormat": "io.druid.data.input.avro.AvroValueInputFormat", "paths": ""}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "hashed", "targetPartitionSize": 1000000}, "shardSpecs": {}, "rowFlushBoundary": 1000000, "leaveIntermediate": false, "cleanupOnFailure": true, "overwriteFiles": false, "ignoreInvalidRows": false, "useCombiner": true, "jobProperties": {"mapreduce.map.memory.mb": "1024", "mapreduce.map.java.opts": " -Xmx750m ", "mapreduce.reduce.memory.mb": "2048", "mapreduce.reduce.java.opts": " -Xmx1400m ", "mapreduce.input.fileinputformat.split.minsize": "536870912", "mapreduce.map.output.compress": "true", "mapreduce.map.output.compress.codec": "org.apache.hadoop.io.compress.DeflateCodec", "mapreduce.job.user.classpath.first": "true"}, "combineText": false}}}