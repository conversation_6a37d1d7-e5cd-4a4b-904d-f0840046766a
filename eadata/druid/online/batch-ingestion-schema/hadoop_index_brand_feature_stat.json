{"type": "index_hadoop", "spec": {"dataSchema": {"dataSource": "brand_feature_stat", "parser": {"type": "avro_hadoop", "fromPigAvroStorage": false, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["age_v2", "gender", "dictStates", "interests", "installed_pkg_name", "brand_crowd_package_ids", "city", "country", "imprPos", "venderId"]}}}, "metricsSpec": [{"type": "longSum", "name": "pv", "fieldName": "pv"}], "granularitySpec": {"type": "uniform", "segmentGranularity": {"type": "period", "period": "PT1H", "timeZone": "Asia/Shanghai"}, "queryGranularity": "HOUR", "intervals": []}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "static", "inputFormat": "io.druid.data.input.avro.AvroValueInputFormat", "paths": ""}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "hashed", "targetPartitionSize": 10000000}, "shardSpecs": {}, "rowFlushBoundary": 1000000, "leaveIntermediate": false, "cleanupOnFailure": true, "overwriteFiles": false, "ignoreInvalidRows": false, "useCombiner": true, "jobProperties": {"mapreduce.map.memory.mb": "1024", "mapreduce.map.java.opts": " -Xmx750m ", "mapreduce.reduce.memory.mb": "2048", "mapreduce.reduce.java.opts": " -Xmx1400m ", "mapreduce.input.fileinputformat.split.minsize": "536870912", "mapreduce.map.output.compress": "true", "mapreduce.map.output.compress.codec": "org.apache.hadoop.io.compress.DeflateCodec", "mapreduce.job.user.classpath.first": "true"}, "combineText": false}}}