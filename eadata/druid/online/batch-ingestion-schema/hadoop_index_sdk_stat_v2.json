{"type": "index_hadoop", "spec": {"dataSchema": {"dataSource": "sdk_stat_v2", "parser": {"type": "avro_hadoop", "fromPigAvroStorage": false, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["synd_id", "developer_id", "os", "app_id", "slot_id", "agent_id", "sponsor_id", "campaign_id", "group_id", "variation_id", "conv_parent_type", "conv_type", "ct_id", "ct_type", "dest_link", "ct_action", "sid", "enable_ocpc", "wechat_origin_id", "dict_post_id", "click_interact_type", "rta_enabled", "attribution_type", "retention_days", "active_purchase_interval_days"]}}}, "metricsSpec": [{"type": "longSum", "name": "pcnt0", "fieldName": "pcnt0"}, {"type": "longSum", "name": "pcnt25", "fieldName": "pcnt25"}, {"type": "longSum", "name": "pcnt50", "fieldName": "pcnt50"}, {"type": "longSum", "name": "pcnt75", "fieldName": "pcnt75"}, {"type": "longSum", "name": "pcnt100", "fieldName": "pcnt100"}, {"type": "longSum", "name": "mute", "fieldName": "mute"}, {"type": "longSum", "name": "unmute", "fieldName": "unmute"}, {"type": "longSum", "name": "close", "fieldName": "close"}, {"type": "longSum", "name": "loaded", "fieldName": "loaded"}, {"type": "longSum", "name": "pv", "fieldName": "pv"}, {"type": "longSum", "name": "request_ad_num", "fieldName": "request_ad_num"}, {"type": "cardinality", "name": "uv", "fieldNames": ["pv_device_id"]}, {"type": "longSum", "name": "bid", "fieldName": "bid"}, {"type": "longSum", "name": "impr", "fieldName": "impr"}, {"type": "longSum", "name": "win_price", "fieldName": "win_price"}, {"type": "filtered", "filter": {"type": "or", "fields": [{"type": "and", "fields": [{"type": "selector", "dimension": "type", "value": null}, {"type": "not", "field": {"type": "selector", "dimension": "developer_id", "value": null}}]}, {"type": "selector", "dimension": "type", "value": "charged"}]}, "aggregator": {"type": "longSum", "name": "charge", "fieldName": "charge"}}, {"type": "filtered", "filter": {"type": "or", "fields": [{"type": "and", "fields": [{"type": "selector", "dimension": "type", "value": null}, {"type": "not", "field": {"type": "selector", "dimension": "developer_id", "value": null}}]}, {"type": "selector", "dimension": "type", "value": "charged"}]}, "aggregator": {"type": "longSum", "name": "click", "fieldName": "click"}}, {"type": "filtered", "filter": {"type": "selector", "dimension": "type", "value": "discarded"}, "aggregator": {"type": "longSum", "name": "discarded_charge", "fieldName": "charge"}}, {"type": "filtered", "filter": {"type": "selector", "dimension": "type", "value": "discarded"}, "aggregator": {"type": "longSum", "name": "discarded_click", "fieldName": "click"}}, {"type": "longSum", "name": "conv", "fieldName": "conv"}, {"type": "longSum", "name": "opt_conv", "fieldName": "opt_conv"}, {"type": "longSum", "name": "conv_cost", "fieldName": "conv_cost"}, {"type": "longSum", "name": "loc_conv", "fieldName": "loc_conv"}, {"type": "longSum", "name": "loc_opt_conv", "fieldName": "loc_opt_conv"}, {"type": "longSum", "name": "loc_conv_cost", "fieldName": "loc_conv_cost"}, {"type": "longSum", "name": "impr_conv", "fieldName": "impr_conv"}, {"type": "longSum", "name": "order_amount", "fieldName": "order_amount"}, {"type": "longSum", "name": "first_day_order_amount", "fieldName": "first_day_order_amount"}, {"type": "longSum", "name": "in_24_hours_order_amount", "fieldName": "in_24_hours_order_amount"}], "granularitySpec": {"type": "uniform", "segmentGranularity": {"type": "period", "period": "PT1H", "timeZone": "Asia/Shanghai"}, "queryGranularity": "HOUR", "intervals": []}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "static", "inputFormat": "io.druid.data.input.avro.AvroValueInputFormat", "paths": ""}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "hashed", "targetPartitionSize": 10000000}, "shardSpecs": {}, "rowFlushBoundary": 1000000, "leaveIntermediate": false, "cleanupOnFailure": true, "overwriteFiles": false, "ignoreInvalidRows": false, "useCombiner": true, "jobProperties": {"mapreduce.map.memory.mb": "1024", "mapreduce.map.java.opts": " -Xmx750m ", "mapreduce.reduce.memory.mb": "2048", "mapreduce.reduce.java.opts": " -Xmx1400m ", "mapreduce.input.fileinputformat.split.minsize": "536870912", "mapreduce.map.output.compress": "true", "mapreduce.map.output.compress.codec": "org.apache.hadoop.io.compress.DeflateCodec", "mapreduce.job.user.classpath.first": "true", "mapreduce.job.queuename": "prior", "mapreduce.job.running.map.limit": "50"}, "combineText": false}}}