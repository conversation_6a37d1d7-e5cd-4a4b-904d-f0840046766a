{"type": "index_hadoop", "spec": {"dataSchema": {"dataSource": "interest_stat", "parser": {"type": "avro_hadoop", "fromPigAvroStorage": false, "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["template_ids", "syndication_id", "province", "city", "os", "mobile_brand", "network_type", "installed_apps", "interests", "genders", "ages", "crowd_ids", "slot_id", "scenario_ids", "package_name", "dict_community_ids", "ages2", "edu_degree", "school_type", "profession"]}}}, "metricsSpec": [{"type": "longSum", "name": "pv", "fieldName": "pv"}], "granularitySpec": {"type": "uniform", "segmentGranularity": {"type": "period", "period": "P1D", "timeZone": "Asia/Shanghai"}, "queryGranularity": "ALL", "intervals": ["@INTERVALS@"]}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "dataSource", "ingestionSpec": {"dataSource": "interest_stat", "intervals": ["@INTERVALS@"]}}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "hashed", "targetPartitionSize": 5000000}, "shardSpecs": {}, "rowFlushBoundary": 1000000, "leaveIntermediate": false, "cleanupOnFailure": true, "overwriteFiles": false, "ignoreInvalidRows": false, "useCombiner": true, "jobProperties": {"mapreduce.map.memory.mb": "1024", "mapreduce.map.java.opts": " -Xmx750m ", "mapreduce.reduce.memory.mb": "20000", "mapreduce.reduce.java.opts": " -Xmx16G ", "mapreduce.map.output.compress": "true", "mapreduce.map.output.compress.codec": "org.apache.hadoop.io.compress.DeflateCodec", "mapreduce.job.user.classpath.first": "true"}, "combineText": false}}}