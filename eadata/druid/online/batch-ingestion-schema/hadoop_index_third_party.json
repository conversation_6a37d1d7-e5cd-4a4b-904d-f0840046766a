{"type": "index_hadoop", "spec": {"dataSchema": {"dataSource": "third_party_stat", "parser": {"type": "avro_hadoop", "parseSpec": {"format": "timeAndDims", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["sponsor_id", "campaign_id", "group_id", "variation_id", "app_id", "slot_id", "media_type", "slot_style_type", "conv_parent_type", "conv_type", "activity_id", "ct_id", "ct_type", "ct_action", "rta_name", "rta_enabled", "api_mode", "discard_reason", "tdp_platform", "os", "province", "city", "filter_no_bid_conv", "retention_days", "channel_did", "fraud_tags", "fraud_rule_id", "fraud_stat_type", "brand", "model", "diff_a_click_time", "diff_s_click_time", "diff_c_click_time", "diff_p_click_time", "conv_click_time_difference", "active_purchase_interval_days"]}}}, "metricsSpec": [{"type": "longSum", "name": "rta_request_count", "fieldName": "request_count"}, {"type": "longSum", "name": "rta_fraud_mark", "fieldName": "fraud_mark"}, {"type": "longSum", "name": "rta_fraud_mark_send", "fieldName": "fraud_mark_send"}, {"type": "longSum", "name": "rta_bid_count", "fieldName": "bid_count"}, {"type": "longSum", "name": "rta_exception_count", "fieldName": "exception_count"}, {"type": "longSum", "name": "impr", "fieldName": "impr"}, {"type": "filtered", "filter": {"type": "not", "field": {"type": "in", "dimension": "sponsor_id", "values": ["317274", "317250"]}}, "aggregator": {"type": "longSum", "name": "click", "fieldName": "click"}}, {"type": "filtered", "filter": {"type": "not", "field": {"type": "in", "dimension": "sponsor_id", "values": ["317274", "317250"]}}, "aggregator": {"type": "longSum", "name": "conv", "fieldName": "conv"}}, {"type": "longSum", "name": "rta_bid_conv", "fieldName": "rta_bid_conv"}, {"type": "longSum", "name": "conv_cost", "fieldName": "conv_cost"}, {"type": "longSum", "name": "order_amount", "fieldName": "order_amount"}, {"type": "longSum", "name": "first_day_order_amount", "fieldName": "first_day_order_amount"}, {"type": "longSum", "name": "in_24_hours_order_amount", "fieldName": "in_24_hours_order_amount"}, {"type": "longSum", "name": "rta_bid_click", "fieldName": "rta_bid"}, {"type": "longSum", "name": "discard_click", "fieldName": "discard_count"}, {"type": "longSum", "name": "fraud_click_unfiltered", "fieldName": "fraud_click_unfiltered"}, {"type": "longSum", "name": "fraud_click_filtered", "fieldName": "fraud_click_filtered"}, {"type": "longSum", "name": "fraud_rta_filtered", "fieldName": "fraud_rta_filtered"}, {"type": "longSum", "name": "repeat_click_mark", "fieldName": "repeat_click_mark"}, {"type": "longSum", "name": "repeat_click", "fieldName": "repeat_click"}, {"type": "longSum", "name": "android_click", "fieldName": "android_click"}, {"type": "longSum", "name": "ios_click", "fieldName": "ios_click"}, {"type": "longSum", "name": "ua_filled_mark", "fieldName": "ua_filled_mark"}, {"type": "longSum", "name": "ip_filled_mark", "fieldName": "ip_filled_mark"}, {"type": "longSum", "name": "ts_filled_mark", "fieldName": "ts_filled_mark"}, {"type": "longSum", "name": "activity_id_filled_mark", "fieldName": "activity_id_filled_mark"}, {"type": "longSum", "name": "android_os_filled_mark", "fieldName": "android_os_filled_mark"}, {"type": "longSum", "name": "ios_os_filled_mark", "fieldName": "ios_os_filled_mark"}, {"type": "longSum", "name": "device_model_filled_mark", "fieldName": "device_model_filled_mark"}, {"type": "longSum", "name": "os_version_filled_mark", "fieldName": "os_version_filled_mark"}, {"type": "longSum", "name": "imei_filled_mark", "fieldName": "imei_filled_mark"}, {"type": "longSum", "name": "imei_v2_filled_mark", "fieldName": "imei_v2_filled_mark"}, {"type": "longSum", "name": "oaid_filled_mark", "fieldName": "oaid_filled_mark"}, {"type": "longSum", "name": "oaid_md5_filled_mark", "fieldName": "oaid_md5_filled_mark"}, {"type": "longSum", "name": "idfa_filled_mark", "fieldName": "idfa_filled_mark"}, {"type": "longSum", "name": "idfa_md5_filled_mark", "fieldName": "idfa_md5_filled_mark"}, {"type": "longSum", "name": "caid_filled_mark", "fieldName": "caid_filled_mark"}, {"type": "longSum", "name": "caid_md5_filled_mark", "fieldName": "caid_md5_filled_mark"}, {"type": "longSum", "name": "alid_filled_mark", "fieldName": "alid_filled_mark"}, {"type": "longSum", "name": "android_id_md5_filled_mark", "fieldName": "android_id_md5_filled_mark"}, {"type": "longSum", "name": "android_id_filled_mark", "fieldName": "android_id_filled_mark"}, {"type": "longSum", "name": "ua_unfilled_deal", "fieldName": "ua_unfilled_deal"}, {"type": "longSum", "name": "ip_unfilled_deal", "fieldName": "ip_unfilled_deal"}, {"type": "longSum", "name": "android_os_unfilled_deal", "fieldName": "android_os_unfilled_deal"}, {"type": "longSum", "name": "ios_os_unfilled_deal", "fieldName": "ios_os_unfilled_deal"}, {"type": "longSum", "name": "os_unfilled_deal", "fieldName": "os_unfilled_deal"}, {"type": "longSum", "name": "device_model_unfilled_deal", "fieldName": "device_model_unfilled_deal"}, {"type": "longSum", "name": "os_version_unfilled_deal", "fieldName": "os_version_unfilled_deal"}, {"type": "longSum", "name": "channel_did_filled", "fieldName": "channel_did_filled"}, {"type": "longSum", "name": "ua_filled", "fieldName": "ua_filled"}, {"type": "longSum", "name": "ip_filled", "fieldName": "ip_filled"}, {"type": "longSum", "name": "ts_filled", "fieldName": "ts_filled"}, {"type": "longSum", "name": "req_id_filled", "fieldName": "req_id_filled"}, {"type": "longSum", "name": "activity_id_filled", "fieldName": "activity_id_filled"}, {"type": "longSum", "name": "android_os_filled", "fieldName": "android_os_filled"}, {"type": "longSum", "name": "ios_os_filled", "fieldName": "ios_os_filled"}, {"type": "longSum", "name": "os_version_filled", "fieldName": "os_version_filled"}, {"type": "longSum", "name": "device_model_filled", "fieldName": "device_model_filled"}, {"type": "longSum", "name": "imei_filled", "fieldName": "imei_filled"}, {"type": "longSum", "name": "imei_v2_filled", "fieldName": "imei_v2_filled"}, {"type": "longSum", "name": "oaid_filled", "fieldName": "oaid_filled"}, {"type": "longSum", "name": "oaid_md5_filled", "fieldName": "oaid_md5_filled"}, {"type": "longSum", "name": "idfa_filled", "fieldName": "idfa_filled"}, {"type": "longSum", "name": "idfa_md5_filled", "fieldName": "idfa_md5_filled"}, {"type": "longSum", "name": "caid_filled", "fieldName": "caid_filled"}, {"type": "longSum", "name": "caid_md5_filled", "fieldName": "caid_md5_filled"}, {"type": "longSum", "name": "alid_filled", "fieldName": "alid_filled"}, {"type": "longSum", "name": "android_id_md5_filled", "fieldName": "android_id_md5_filled"}, {"type": "longSum", "name": "android_id_filled", "fieldName": "android_id_filled"}, {"type": "longSum", "name": "exception_mark", "fieldName": "exception_mark"}, {"type": "longSum", "name": "exception_deal", "fieldName": "exception_deal"}, {"type": "longSum", "name": "exception_send", "fieldName": "exception_send"}, {"type": "longSum", "name": "unexpected_device_mark", "fieldName": "unexpected_device_mark"}, {"type": "longSum", "name": "unexpected_device_click", "fieldName": "unexpected_device_click"}, {"type": "longSum", "name": "unexpected_ua_mark", "fieldName": "unexpected_ua_mark"}, {"type": "longSum", "name": "unexpected_ua_deal", "fieldName": "unexpected_ua_deal"}, {"type": "longSum", "name": "unexpected_ua_click", "fieldName": "unexpected_ua_click"}, {"type": "longSum", "name": "unexpected_os_mark", "fieldName": "unexpected_os_mark"}, {"type": "longSum", "name": "unexpected_os_deal", "fieldName": "unexpected_os_deal"}, {"type": "longSum", "name": "unexpected_os_click", "fieldName": "unexpected_os_click"}, {"type": "longSum", "name": "unexpected_model_mark", "fieldName": "unexpected_model_mark"}, {"type": "longSum", "name": "unexpected_model_deal", "fieldName": "unexpected_model_deal"}, {"type": "longSum", "name": "unexpected_model_send", "fieldName": "unexpected_model_send"}, {"type": "longSum", "name": "blank_param_filtered", "fieldName": "blank_param_filtered"}, {"type": "longSum", "name": "model_filtered", "fieldName": "model_filtered"}, {"type": "longSum", "name": "blank_param_rta_filtered", "fieldName": "blank_param_rta_filtered"}, {"type": "longSum", "name": "model_rta_filtered", "fieldName": "model_rta_filtered"}, {"type": "longSum", "name": "format_error_ua_mark", "fieldName": "format_error_ua_mark"}, {"type": "longSum", "name": "format_error_ua_deal", "fieldName": "format_error_ua_deal"}, {"type": "longSum", "name": "format_error_ua_send", "fieldName": "format_error_ua_send"}, {"type": "longSum", "name": "format_error_device_id_mark", "fieldName": "format_error_device_id_mark"}, {"type": "longSum", "name": "format_error_device_id_send", "fieldName": "format_error_device_id_send"}, {"type": "longSum", "name": "value_error_intranet_ip_mark", "fieldName": "value_error_intranet_ip_mark"}, {"type": "longSum", "name": "value_error_intranet_ip_deal", "fieldName": "value_error_intranet_ip_deal"}, {"type": "longSum", "name": "value_error_intranet_ip_send", "fieldName": "value_error_intranet_ip_send"}, {"type": "longSum", "name": "value_error_dark_ip_mark", "fieldName": "value_error_dark_ip_mark"}, {"type": "longSum", "name": "value_error_dark_ip_deal", "fieldName": "value_error_dark_ip_deal"}, {"type": "longSum", "name": "value_error_dark_ip_send", "fieldName": "value_error_dark_ip_send"}, {"type": "longSum", "name": "value_error_dark_device_id_mark", "fieldName": "value_error_dark_device_id_mark"}, {"type": "longSum", "name": "value_error_dark_device_id_send", "fieldName": "value_error_dark_device_id_send"}, {"type": "longSum", "name": "value_error_ip_mark", "fieldName": "value_error_ip_mark"}, {"type": "longSum", "name": "value_error_ip_deal", "fieldName": "value_error_ip_deal"}, {"type": "longSum", "name": "value_error_ip_send", "fieldName": "value_error_ip_send"}, {"type": "longSum", "name": "value_error_ua_mark", "fieldName": "value_error_ua_mark"}, {"type": "longSum", "name": "value_error_ua_deal", "fieldName": "value_error_ua_deal"}, {"type": "longSum", "name": "value_error_ua_send", "fieldName": "value_error_ua_send"}, {"type": "longSum", "name": "standard_diff_ua_mark", "fieldName": "standard_diff_ua_mark"}, {"type": "longSum", "name": "standard_diff_ua_deal", "fieldName": "standard_diff_ua_deal"}, {"type": "longSum", "name": "standard_diff_ua_send", "fieldName": "standard_diff_ua_send"}, {"type": "longSum", "name": "standard_diff_osv_mark", "fieldName": "standard_diff_osv_mark"}, {"type": "longSum", "name": "standard_diff_osv_deal", "fieldName": "standard_diff_osv_deal"}, {"type": "longSum", "name": "standard_diff_osv_send", "fieldName": "standard_diff_osv_send"}, {"type": "longSum", "name": "standard_diff_model_mark", "fieldName": "standard_diff_model_mark"}, {"type": "longSum", "name": "standard_diff_model_deal", "fieldName": "standard_diff_model_deal"}, {"type": "longSum", "name": "standard_diff_model_send", "fieldName": "standard_diff_model_send"}, {"type": "longSum", "name": "related_multi_ua_mark", "fieldName": "related_multi_ua_mark"}, {"type": "longSum", "name": "related_multi_ua_deal", "fieldName": "related_multi_ua_deal"}, {"type": "longSum", "name": "related_multi_ua_send", "fieldName": "related_multi_ua_send"}, {"type": "longSum", "name": "related_multi_model_mark", "fieldName": "related_multi_model_mark"}, {"type": "longSum", "name": "related_multi_model_deal", "fieldName": "related_multi_model_deal"}, {"type": "longSum", "name": "related_multi_model_send", "fieldName": "related_multi_model_send"}, {"type": "longSum", "name": "related_multi_osv_mark", "fieldName": "related_multi_osv_mark"}, {"type": "longSum", "name": "related_multi_osv_deal", "fieldName": "related_multi_osv_deal"}, {"type": "longSum", "name": "related_multi_osv_send", "fieldName": "related_multi_osv_send"}, {"type": "longSum", "name": "related_rta_device_mark", "fieldName": "related_rta_device_mark"}, {"type": "longSum", "name": "related_rta_device_send", "fieldName": "related_rta_device_send"}, {"type": "longSum", "name": "related_rta_ua_mark", "fieldName": "related_rta_ua_mark"}, {"type": "longSum", "name": "related_rta_ua_deal", "fieldName": "related_rta_ua_deal"}, {"type": "longSum", "name": "related_rta_ua_send", "fieldName": "related_rta_ua_send"}, {"type": "longSum", "name": "related_rta_model_mark", "fieldName": "related_rta_model_mark"}, {"type": "longSum", "name": "related_rta_model_deal", "fieldName": "related_rta_model_deal"}, {"type": "longSum", "name": "related_rta_model_send", "fieldName": "related_rta_model_send"}, {"type": "longSum", "name": "related_rta_ip_mark", "fieldName": "related_rta_ip_mark"}, {"type": "longSum", "name": "related_rta_ip_deal", "fieldName": "related_rta_ip_deal"}, {"type": "longSum", "name": "related_rta_ip_send", "fieldName": "related_rta_ip_send"}, {"type": "longSum", "name": "related_rta_osv_mark", "fieldName": "related_rta_osv_mark"}, {"type": "longSum", "name": "related_rta_osv_deal", "fieldName": "related_rta_osv_deal"}, {"type": "longSum", "name": "related_rta_osv_send", "fieldName": "related_rta_osv_send"}, {"type": "longSum", "name": "action_rate_click_mark", "fieldName": "action_rate_click_mark"}, {"type": "longSum", "name": "action_rate_click_send", "fieldName": "action_rate_click_send"}, {"type": "longSum", "name": "action_rate_ip_mark", "fieldName": "action_rate_ip_mark"}, {"type": "longSum", "name": "action_rate_ip_send", "fieldName": "action_rate_ip_send"}, {"type": "longSum", "name": "action_rate_device_ip_mark", "fieldName": "action_rate_device_ip_mark"}, {"type": "longSum", "name": "action_rate_device_ip_deal", "fieldName": "action_rate_device_ip_deal"}, {"type": "longSum", "name": "action_rate_device_ip_send", "fieldName": "action_rate_device_ip_send"}, {"type": "longSum", "name": "rta_click_time_error_mark", "fieldName": "rta_click_time_error_mark"}, {"type": "longSum", "name": "rta_click_time_error_deal", "fieldName": "rta_click_time_error_deal"}, {"type": "longSum", "name": "rta_click_time_error_send", "fieldName": "rta_click_time_error_send"}, {"type": "longSum", "name": "action_features_diff_source_mark", "fieldName": "action_features_diff_source_mark"}, {"type": "longSum", "name": "action_features_diff_source_send", "fieldName": "action_features_diff_source_send"}, {"type": "longSum", "name": "conv_time_follow_closely", "fieldName": "conv_time_follow_closely"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR", "intervals": []}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "static", "inputFormat": "io.druid.data.input.avro.AvroValueInputFormat", "paths": ""}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "hashed", "targetPartitionSize": 20000000}, "shardSpecs": {}, "rowFlushBoundary": 1000000, "leaveIntermediate": false, "cleanupOnFailure": true, "overwriteFiles": false, "ignoreInvalidRows": false, "useCombiner": true, "jobProperties": {"mapreduce.map.memory.mb": "1024", "mapreduce.map.java.opts": " -Xmx750m", "mapreduce.reduce.memory.mb": "12288", "mapreduce.reduce.java.opts": " -Xmx11g", "mapreduce.input.fileinputformat.split.minsize": "536870912", "mapreduce.input.fileinputformat.split.maxsize": "1073741824", "mapreduce.map.output.compress": "true", "mapreduce.map.output.compress.codec": "org.apache.hadoop.io.compress.DeflateCodec", "mapreduce.job.user.classpath.first": "true"}, "combineText": false}}}