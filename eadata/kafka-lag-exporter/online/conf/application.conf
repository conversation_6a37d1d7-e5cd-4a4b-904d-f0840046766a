kafka-lag-exporter {
  reporters {
    graphite {
      host = "quipu-graphite.inner.youdao.com"
      port = 2003
      prefix = "eadata.kafka.lag."
    }
  }
  sinks = ["GraphiteEndpointSink"]
  poll-interval	= 5 seconds
  clusters = [
    {
      name = "yex-noticer"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "yex.win-notice.consumer"
      ]
      topic-whitelist = [
        "yex_win_notice",
        "yex_lose_notice"
      ]
    },
    {
      name = "third_party_reporter"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "ead.third_party_reporter.consumer"
      ]
      topic-whitelist = [
        "third_party_callback"
      ]
    },
    {
      name = "click_consumer"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "click.sdk.send.sponsor"
      ]
      topic-whitelist = [
        "click.sdk.charge.full"
      ]
    },
    {
      name = "impr_consumer"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "impr.sdk.dmp.etl.reporter"
      ]
      topic-whitelist = [
        "impr_sdk"
      ]
    },
    {
      name = "consumer_third_party"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "click.third-party.send.sponsor"
      ]
      topic-whitelist = [
        "click_third_party"
      ]
    },
    {
      name = "reyun_record_to_venus"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "reyun_to_venus"
      ]
      topic-whitelist = [
        "reyun_record"
      ]
    },
    {
      name = "thunder_consume_click"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "thunder.click.group.v2"
      ]
      topic-whitelist = [
        "thunder.click"
      ]
    },
    {
      name = "BS_InFlightSpend_KafkaStreams_Consume"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "bs-producer-in-flight-spend-web06-ead.gz.ynode.cn-eadop"
      ]
      topic-whitelist = [
        "inflight_spend_sdk"
      ]
    },
    {
      name = "ad-action-consume-click"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "click_action_2_venus_v1"
      ]
      topic-whitelist = [
        "click_ead_discarded",
        "click_sdk_charged"
      ]
    },
    {
      name = "device-info-database"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "device-info-database-online"
      ]
      topic-whitelist = [
        "brand_pv"
      ]
    },
    {
      name = "third-party-data-poool-consumer-click"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "clickListener"
      ]
      topic-whitelist = [
        "click_third_party"
      ]
    },
    {
      name = "third-party-data-poool-consumer-rta-guizhou"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "rtaForGuizhouListener"
      ]
      topic-whitelist = [
        "rta_record"
      ]
    },
    {
      name = "third-party-data-poool-consumer-rta-tianjin"
      bootstrap-brokers = "ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666"
      group-whitelist = [
        "rtaForTianjinListener"
      ]
      topic-whitelist = [
        "rta_record"
      ]
    }
  ]
}
