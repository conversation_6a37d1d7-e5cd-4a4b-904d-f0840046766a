package outfox.ead.data;

import lombok.Data;
import outfox.ead.brand.util.ClickTypeEnum;

/**
 * 开屏灵敏度配置
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class AdSensitivityDO {
    private Long id;

    /**
     * 广告点击交互类型，1：摇一摇，2：滑动互动，5：扭一扭, 6:三合一
     *
     * @see ClickTypeEnum
     */
    private Integer clickType;

    /**
     * 广告位ID
     */
    private Long adPositionId;

    /**
     * 广告组ID
     */
    private Long adGroupId;

    /**
     * 灵敏度配置-摇动加速度
     */
    private Integer shakeSpeed;

    /**
     * 灵敏度配置-滑动角度
     */
    private Integer slideAngle;

    /**
     * 灵敏度配置-摇动角度/扭转角度
     */
    private Integer rotationAngle;
}
