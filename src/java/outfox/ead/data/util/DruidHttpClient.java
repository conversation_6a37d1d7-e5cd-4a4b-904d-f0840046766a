package outfox.ead.data.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.zapr.druid.druidry.client.DruidClient;
import in.zapr.druid.druidry.client.exception.ConnectionException;
import in.zapr.druid.druidry.client.exception.QueryException;
import in.zapr.druid.druidry.query.DruidQuery;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Use {@link CloseableHttpAsyncClient} to query druid.
 *
 * <AUTHOR> on 2018/10/09
 */
@Slf4j
public class DruidHttpClient implements DruidClient {

    public static final int MAX_QUERY_RETRY_TIMES = 3;
    private CloseableHttpAsyncClient client;
    private ObjectMapper mapper;
    @Setter
    private String druidAddr;
    @Setter
    private int maxWaitTimeout;
    @Setter
    private int druidConnRequestTimeout;
    @Setter
    private int druidSocketTimeout;
    @Setter
    private int druidConnectTimeout;

    @Override
    public void connect() throws ConnectionException {
        try {
            mapper = new ObjectMapper();
            client = HttpAsyncClients.createDefault();
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(druidConnRequestTimeout)
                    .setSocketTimeout(druidSocketTimeout)
                    .setConnectTimeout(druidConnectTimeout)
                    .build();

            client = HttpAsyncClients.custom()
                    .setDefaultRequestConfig(requestConfig)
                    .build();
            client.start();
        } catch (Exception e) {
            log.error("Error when starting httpClient.", e);
            throw new ConnectionException(e);
        }
    }

    @Override
    public void close() throws ConnectionException {
        try {
            client.close();
        } catch (Exception e) {
            log.error("Error when closing httpClient.", e);
            throw new ConnectionException(e);
        }
    }

    @Override
    public String query(DruidQuery druidQuery) throws QueryException {
        String jsonQuery = convertDruidQuery(druidQuery);
        HttpPost post = new HttpPost(druidAddr);
        post.setEntity(new ByteArrayEntity(jsonQuery.getBytes(StandardCharsets.UTF_8)));
        StopWatch sw = new StopWatch();
        try {
            sw.start();
            Future<HttpResponse> responseFuture = client.execute(post, null);
            HttpResponse response = responseFuture.get(maxWaitTimeout, TimeUnit.MILLISECONDS);
            // valid response
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK && response.getEntity() != null) {
                HttpEntity resEntity = response.getEntity();
                try (InputStream inputStream = resEntity.getContent()) {
                    return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
                } catch (IOException e) {
                    log.error("Error get results when querying druid.", e);
                    throw new QueryException(e);
                }
            }
        } catch (Exception e) {
            log.error("Error when querying druid. jsonQuery = {}.", jsonQuery, e);
            throw new QueryException(e);
        } finally {
            sw.stop();
            log.info("druid jsonQuery cost: {} ms, query:{}", sw.getLastTaskTimeMillis(), jsonQuery);
        }
        return "";
    }

    @Override
    public <T> List<T> query(DruidQuery druidQuery, Class<T> className) throws QueryException {
        int retryTimes = 0;
        String json = "";
        while (retryTimes < MAX_QUERY_RETRY_TIMES) {
            try {
                json = query(druidQuery);
                break;
            } catch (QueryException e) {
                retryTimes++;
                log.warn("druid query failed! has retried {} times", retryTimes);
            }
        }
        if (StringUtils.isBlank(json)) {
            throw new QueryException("query failed! response is empty!");
        }
        try {
            return mapper.readValue(json, mapper.getTypeFactory().constructCollectionType(List.class, className));
        } catch (IOException e) {
            log.error("Error when converting druid query result to objects. json={}", json, e);
            throw new QueryException(e);
        }
    }

    /**
     * Convert druid query to json string.
     *
     * @param druidQuery druid query
     * @return json string
     * @throws QueryException if conversion fails
     */
    private String convertDruidQuery(DruidQuery druidQuery) throws QueryException {
        try {
            String query = mapper.writeValueAsString(druidQuery);
            log.info("Druid query: {}", query);
            return query;
        } catch (JsonProcessingException e) {
            log.error("Parsing druid query to json error. query={}", druidQuery, e);
            throw new QueryException(e);
        }
    }
}
