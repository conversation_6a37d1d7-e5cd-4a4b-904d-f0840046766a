package outfox.ead.data;

import lombok.Data;
import outfox.ead.brand.util.ClickTypeEnum;

import java.util.Date;

/**
 * 优选品牌广告 db 数据
 * <AUTHOR>
 * @date 2021/8/30.
 */
@Data
public class AdBrandDeliveryDO {
    /**
     * 当前投放计划时间段的开始
     */
    private Date startTime;
    /**
     * 当前投放计划时间段的结束
     */
    private Date endTime;
    /**
     * 对应的样式id
     */
    private Long styleId;
    /**
     * 样式名称
     */
    private String styleName;
    /**
     * 样式定义
     */
    private String textStyleContent;
    /**
     * 样式定义
     */
    private String picStyleContent;
    /**
     * 样式定义
     */
    private String videoStyleContent;
    /**
     * 样式支持的最小版本号
     */
    private String minAppVersion;
    /**
     * 广告位id
     */
    private Long positionId;
    /**
     * 广告位类型
     */
    private Integer positionType;
    /**
     * 媒体id
     */
    private Long mediaId;
    /**
     * 平台类型
     */
    private Integer osType;
    /**
     * 是否支持轮播
     */
    private Integer positionDisplayType;
    /**
     * 轮播数
     */
    private Integer positionDisplayTimes;
    /**
     * 开屏回收
     */
    private Boolean openScreenRecycle;
    /**
     * 开机首刷
     */
    private Boolean bootFirstRefresh;
    /**
     * 是否接收开屏广告
     */
    private Boolean receiveOpenScreenRecycle;
    /**
     * 推广标的类型
     */
    private String promotionType;
    /**
     * 样式的轮播数
     */
    private Integer styleDisplayWeight;
    /**
     * 推广组
     */
    private Long groupId;
    /**
     * 推广组是否全链路投放
     */
    private boolean fullChannel;
    private boolean posFullChannel;
    /**
     * 投放计划id
     */
    private Long planId;
    /**
     * 计划投放的开始时间
     */
    private Date planStartTime;
    /**
     * 计划投放的结束时间
     */
    private Date planEndTime;
    /**
     * 计费模式
     */
    private Integer billingType;
    /**
     * 投放方式
     */
    private Integer deliveryType;
    /**
     * 是否开启地域定向
     */
    private Boolean regionalOrientation;
    /**
     * 地域定向流量范围 0-全部 1-内部流量
     */
    private Integer regionalOrientationScope;
    /**
     * 投放区域
     */
    private String cityArea;
    /**
     * 投放国家
     */
    private String countryArea;
    /**
     * 客户名称
     */
    private Long customerId;
    /**
     *
     */
    private Date planCreateTime;
    /**
     *
     */
    private Date planLastModTime;
    /**
     * 总展示量
     */
    private Long sumDisplayCount;
    /**
     * 日展示量
     */
    private Long dailyDisplayLimit;
    /**
     * 投放速度
     */
    private Integer deliverySpeed;
    /**
     * 订单id
     */
    private String dealId;
    /**
     * dsp id
     */
    private String dspId;
    /**
     * 订单备注
     */
    private String dealRemark;
    /**
     * 打底广告
     */
    private Boolean basePadding;
    /**
     * 曝光监测链接
     */
    private String expoDetectLink;
    /**
     * 是否下发第三方曝光监测链接
     */
    private Boolean reportExpoDeteLink;
    /**
     * 点击监测链接
     */
    private String clickDetectLink;
    /**
     * cpm 价格
     */
    private Double cpmPrice;
    /**
     * 开机首刷
     */
    private Integer groupBootFirstRefresh;
    /**
     * 开屏回收
     */
    private Boolean groupOpenScreenRecycle;
    /**
     * 性别定向类型：0-无 1-男 2-女
     */
    private int genderOrientation;
    /**
     * 原生开屏
     */
    private Integer styleType;
    /**
     * 样式是否支持摇一摇
     */
    private Boolean styleShakable;
    /**
     * 广告创意id
     */
    private Long adContentId;
    /**
     * 创意类型
     */
    private Integer adContentType;
    /**
     * 文本元素
     */
    private String creativeContentText;
    /**
     * 图片元素
     */
    private String creativeContentImage;
    /**
     * 视频元素
     */
    private String creativeContentVideo;
    /**
     * 备用落地页
     */
    private String landingPageLink;
    /**
     * deeplink 链接
     */
    private String deeplinkUrl;
    /**
     * 展示时长
     */
    private Integer displayTime;
    /**
     * 跳转类型
     */
    private Integer brandClkType;
    /**
     * 微信小程序id
     */
    private String wechatAppId;
    /**
     * 小程序原始id
     */
    private String wechatOriginalId;
    /**
     * 小程序目标页面
     */
    private String wechatPath;
    /**
     * 测试广告
     */
    private Boolean testTag;
    /**
     * 媒体青少年模式
     */
    private Boolean mediaYouthMode;
    /**
     * 推广组青少年模式
     */
    private Boolean groupYouthMode;
    /**
     * 是否全屏样式
     */
    private boolean fullScreen;

    /**
     * 是否支持查词定向
     */
    private boolean supportAdvertisingByKeywords;

    /**
     * 定向词
     */
    private String keywordList;
    private String audioUrls;
    /**
     * 全球发音广告
     */
    private Boolean isAudio;
    /**
     * 词典的内容ID
     */
    private String dictPostId;
    /**
     * 请求是否必须包含设备号
     */
    private Boolean mustDeviceId;
    /**
     * 监测链接是否需要宏替换
     */
    private Boolean replaceMacro;

    /**
     * 是否开启扩量推广
     */
    private Boolean enableExpansion;
    /**
     * 此广告组是否为兜底广告
     */
    private Boolean isBaseAd;

    /**
     * 年龄区间列表，年龄区间格式为以下两种：
     * 1、[22,34]表示22岁到34岁的闭区间
     * 2、[35]表示35岁
     */
    private String ageOrientationIntervals;

    private String crowdLabelOrientation;

    /**
     * 此广告样式是否支持滑动互动
     */
    private Boolean styleSlideInteract;
    /**
     * 此广告样式是否支持双link
     */
    private Boolean styleDoubleLink;
    /**
     * 此广告样式是否支持三link
     */
    private Boolean styleThreeLink;
    /**
     * 此广告样式是否支持三合一
     */
    private Boolean styleThreeInOne;

    /**
     * 定向模式 0-无 1-扩展人群定向 2-精准人群定向
     */
    private int orientationMode;

    /**
     * 频控周期，0-不频控，1-按天频控，2-按自然周频控，3-按投放周期频控
     */
    private int planFrequencyType;

    /**
     * 展示频次限制数
     */
    private int planFrequencyLimit;

    /**
     * 关联的联合频控id
     */
    private Long validUnionFrequencyRuleId;

    /**
     * 联合频控周期，0-不频控, 1-按天频控，2-按自然周频控，3-按投放周期频控
     */
    private int unionFrequencyRuleType;

    /**
     * 联合展示频次限制数 (1-20)
     */
    private int unionFrequencyRuleLimit;

    /**
     * 联合频控开始时间
     */
    private Date unionFrequencyRuleStartTimestamp;

    /**
     * 人群包定向类型，0-不定项，1-定向，2-排除
     */
    private int crowdPackageType;

    /**
     * 人群包id列表，多个人群包id用 ，分割
     */
    private String crowdPackageIds;

    /**
     * 双链接三链接开屏广告的广告组配置：落地页1
     */
    private String landingPageLink1;
    /**
     * 双链接三链接开屏广告的广告组配置：落地页2
     */
    private String landingPageLink2;
    /**
     * 双链接三链接开屏广告的广告组配置：deeplink1
     */
    private String deeplinkUrl1;
    /**
     * 双链接三链接开屏广告的广告组配置：deeplink2
     */
    private String deeplinkUrl2;
    /**
     * 双链接三链接开屏广告的广告组配置：小程序原始ID1
     */
    private String wechatOriginalId1;
    /**
     * 双链接三链接开屏广告的广告组配置：小程序原始ID2
     */
    private String wechatOriginalId2;
    /**
     * 双链接三链接开屏广告的广告组配置：目标路径1
     */
    private String wechatPath1;
    /**
     * 双链接三链接开屏广告的广告组配置：目标路径2
     */
    private String wechatPath2;
    /**
     * 推广组配置的广告点击交互类型，0：普通点击跳转，1：摇一摇，2：滑动互动，3：双link，4：三link，5：扭一扭; 6: 三合一
     *  warn ！ “普通点击跳转”的值与{@link outfox.ead.data.BrandConstants#BRAND_CLICK_TYPE}不同，优选服务端是0，投放服务里是空字符串
     */
    private int interactionType;

    /**
     * 应用包Id列表，格式为json格式的数组，eg：[1,2,3]
     */
    private String installedAppPackageIds;

    /**
     * 是否开启应用包名定向，0-不开启，1-开启
     */
    private boolean appInstalledOrientation;

    /**
     * 是否是微信小程序定向
     */
    private boolean wechatOrientation;

    /**
     * 此广告是否是需要测试设备直达的广告
     */
    private boolean testDirect;

    /**
     * 此广告可以被测试设备直达的开始时间
     */
    private Date directStartTime;

    /**
     * 此广告可以被测试设备直达的结束时间
     */
    private Date directEndTime;

    /**
     * 样式是否支持扭一扭
     */
    private boolean styleTwistable;

    /**
     * 是否开启自定义人群包
     */
    private boolean openCustomCrowdPack;

    /**
     * true-定向人群投放 false-排除人群投放
     */
    private boolean includeCrowdPack;

    /**
     * 人群包id列表
     */
    private String crowdPackIds;

    /**
     * 是否开启了h5中转链接
     */
    private boolean h5TransitUrl;

    /**
     * 是否开启了deeplink中转链接
     */
    private boolean dpTransitUrl;

    /**
     * 广告主小程序的appid，用于开启了h5中转链接的情况下，由中转小程序跳转至广告主小程序
     */
    private String transitTargetWechatAppId;

    /**
     * 品牌cpm广告消费类型，0-正常消费，1-匀速消费，2-加速消费(默认0)
     */
    private int cpmCostType;

    /**
     * cpm广告加速系数，当广告模式为加速模式时生效，范围为1-100，默认为1
     */
    private int cpmAccelerateRatio;

    /**
     * 是否允许caid作为有效的设备号类型，默认false
     */
    private boolean allowCaid;

    /**
     * 全屏点击：0表示关，1表示开
     */
    private boolean fullClickIsOpen;

    /**
     * 全屏点击支持的城市等级列表
     * 包含"-1"则为不限制地域
     */
    private String fullClickClassCities;

    /**
     * 是否为cpm广告
     * @return
     */
    public boolean isCPM(){
        return this.billingType == 1;
    }
    /**
     * 是否为CPT广告
     * @return
     */
    public boolean isCPT(){
        return this.billingType == 0;
    }

    public boolean isDoubleLink() {
        return styleDoubleLink && ClickTypeEnum.DOUBLE_LINK.equals(ClickTypeEnum.fromCode(this.interactionType));
    }

    public boolean isThreeLink() {
        return styleThreeLink && ClickTypeEnum.THREE_LINK.equals(ClickTypeEnum.fromCode(this.interactionType));
    }


}
