package outfox.ead.data.datamanager;

import com.codahale.metrics.Gauge;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.SharedMetricRegistries;
import com.codahale.metrics.Timer;
import com.google.common.base.Functions;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.google.gson.reflect.TypeToken;
import in.zapr.druid.druidry.Context;
import in.zapr.druid.druidry.Interval;
import in.zapr.druid.druidry.aggregator.DruidAggregator;
import in.zapr.druid.druidry.aggregator.LongSumAggregator;
import in.zapr.druid.druidry.client.DruidClient;
import in.zapr.druid.druidry.dimension.DefaultDimension;
import in.zapr.druid.druidry.dimension.DruidDimension;
import in.zapr.druid.druidry.dimension.enums.OutputType;
import in.zapr.druid.druidry.filter.AndFilter;
import in.zapr.druid.druidry.filter.DruidFilter;
import in.zapr.druid.druidry.filter.InFilter;
import in.zapr.druid.druidry.filter.NotFilter;
import in.zapr.druid.druidry.granularity.Granularity;
import in.zapr.druid.druidry.granularity.PeriodGranularity;
import in.zapr.druid.druidry.granularity.PredefinedGranularity;
import in.zapr.druid.druidry.granularity.SimpleGranularity;
import in.zapr.druid.druidry.query.aggregation.DruidGroupByQuery;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.commons.math3.util.Pair;
import org.codehaus.jackson.map.ObjectMapper;
import org.jetbrains.annotations.Nullable;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import outfox.ead.brand.model.BrandSelectivityFeatureEnum;
import outfox.ead.brand.model.CpmGroupPvStatistic;
import outfox.ead.brand.model.CpmLimit;
import outfox.ead.brand.model.CpmOrderImprStatistic;
import outfox.ead.brand.util.BrandUtil;
import outfox.ead.data.*;
import outfox.ead.data.brand.BrandAdWeight;
import outfox.ead.data.brand.ElementProperty;
import outfox.ead.data.brand.StyleElement;
import outfox.ead.dataserv2.datamanager.DataWriteProxy;
import outfox.ead.gorgon.model.BrandAdType;
import outfox.ead.gorgon.request.userfeature.BrandGenderEnum;
import outfox.ead.gorgon.service.brand.BrandAdFilterService;
import outfox.ead.gorgon.service.scheduleBrand.ScheduledPlan;
import outfox.ead.gorgon.utils.MetricUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.brand.util.BrandUtil.MILLISECONDS_OF_ONE_DAY;
import static outfox.ead.data.BrandConstants.EMPTY_JSON_ARRAY;
import static outfox.ead.data.BrandConstants.OPEN_SCREEN_POS_TYPE;
import static outfox.ead.data.WritableMapHelper.gson;
import static outfox.ead.gorgon.config.MetricsConfig.*;
import static outfox.ead.gorgon.utils.MetricUtils.name;


/**
 * 品牌广告数据生产者
 *
 * <AUTHOR>
 * @date 2021/8/24.
 */
@Log4j2
public class AdBrandProducer{
    @Setter
    private YouxuanAdBrandDao youxuanAdBrandDao;
    @Setter
    private DataWriteProxy adBrandCacheWriteProxy;
    @Setter
    private String adBrandCacheDomain;
    @Setter
    private BrandAdFilterService brandAdFilterService;

    /**
     * 默认轮播总数
     */
    private static final int DEFAULT_TOTAL_WEIGHT = 6;


    @Getter
    private Map<String, List<AdItem>> cpdKey2Ads = new HashMap<>();
    @Getter
    private Map<String, BrandAdWeight> cpdKey2Weight = new HashMap<>();
    /**
     * 记录测试广告的权重，防止按权重概率出广告时权重不对
     */
    @Getter
    private Map<String, BrandAdWeight> testCpdKey2Weight = new HashMap<>();
    @Getter
    private Map<String, List<AdItem>> cpmKey2UnionAds = new HashMap<>();

    /**
     * 已经达到cpm日展示及总展示量上限限制的cpm广告集合
     */
    @Getter
    private Map<String, List<AdItem>> overImprCountLimitCpmKey2UnionAds = new HashMap<>();
    /**
     * 保存品牌广告排期数据
     * 第一级key：posid，第二级key: starttime
     */
    @Getter
    private Map<String, Map<Long, List<AdItem>>> scheduledAdsMap = new HashMap<>();
    /**
     * cpm广告的排期数据
     * 一级key：posid/countryid，二级key：startTime
     */
    @Getter
    private Map<String, Map<Long, List<AdItem>>> scheduleCpmAdsMap = new HashMap<>();

    /**
     * 已经达到cpm日展示及总展示量上限限制的cpm广告的排期数据
     * 一级key：posid，二级key：startTime
     */
    @Getter
    private Map<String, Map<Long, List<AdItem>>> overImprCountLimitScheduleCpmAdsMap = new HashMap<>();


    /**
     * 保存cpd品牌广告排期的权重，用于计算cpm广告的数量
     * 第一级key：posid/countryid，第二级key：startTime
     */
    @Getter
    private Map<String, Map<Long, BrandAdWeight>> scheduleCpdAdsWeight = new HashMap<>();

    /**
     * 品牌广告在广告位定向下的广告数据，包含cpd广告和cpm广告
     * key：posid
     * value: 此key下可以投放的广告对象列表
     */
    @Getter
    private Map<String, List<AdItem>> scheduledAdMapV2 = new HashMap<>();

    /**
     * 包含了已达到展示量上线cpm广告的广告数据
     */
    @Getter
    private Map<String,List<AdItem>> overLimitScheduledAdMapV2 = new HashMap<>();

    /**
     * cpd广告对象排期数据
     * 一级key：posid
     * 二级key：投放时段的开始时间
     * 二级value：排期计划列表，记录了这个时间段下全部的广告及其权重，供提名时使用
     */
    @Getter
    private Map<String, HashBasedTable<Long,Long,List<ScheduledPlan>>> scheduledCpdPlanMapV2 = new HashMap<>();

    /**
     * cpm广告对象排期数据
     * 一级key：posid
     * 二级key：投放时段的开始时间
     * 二级value：排期计划列表，记录了这个时间段下全部的广告及其权重，供提名时使用
     */
    @Getter
    private Map<String,HashBasedTable<Long,Long,List<ScheduledPlan>>> scheduledCpmPlanMapV2 = new HashMap<>();

    @Getter
    private Map<String, Set<AdItem>> cpdAdItemMap = new HashMap<>();

    @Getter
    private Map<String, Set<AdItem>> cpmAdItemMap = new HashMap<>();

    /**
     * 直达广告对象排期数据（跳过地域定向、cpm展示量限制的广告投放表）
     * key：posId
     * value：此广告位上的直达广告
     */
    @Getter
    private Map<Long, List<AdItem>> directAdsMap = new HashMap<>();

    /**
     * 定向词到AdItem的map
     * 用于定向查词结果页
     * Map<关键词,Map<广告物料唯一标识,对应音频文件>>
     */
    @Getter
    private Map<String,Map<String,String>> keyword2AdItemKey = new HashMap<>();

    @Getter
    private Set<String> keywordAds = new HashSet<>();

    @Setter
    private DruidClient druidClient;

    @Setter
    private int druidContextTimeout;

    private static final String DATA_SOURCE_BRAND_STAT = "brand_stat_v2";

    private static final String DATA_SOURCE_BRAND_FEATURE_PV = "brand_feature_stat";

    private static final String QUERY_PERIOD_ONE_DAY = "P1D";

    /**
     * 东八区
     */
    private static final DateTimeZone TIME_ZONE_CHINA = DateTimeZone.forOffsetHours(8);

    private static final String ORDER_ID = "campaignId";
    private static final String AD_GROUP_ID = "adGroupId";

    private static final String POS_ID = "imprPos";

    private static final String PV = "pv";


    private static final String IMPR = "impr";

    /**
     * 获取若干天的排期，默认7天
     */
    @Setter
    @Getter
    private int scheduledDays = 7;

    /**
     * 使用pv数据统计特征选择性数据使用的时间跨度
     */
    @Setter
    private int pvInterval = 7;

    private Set<AdBrandDeliveryDO> cpmAdDataSet = new HashSet<>();

    /**
     * cpm广告总展示次数和当日展示次数统计
     * 一级key：adGroupId，二级key：日期（防止0点左右缓存未更新导致投不出广告），value：(总展示量，当日展示量)
     */
    private Map<Long, Map<Integer, Pair<Long, Long>>> cpmAdsImpressionTimesMap = new HashMap<>();

    private static final Long INTERVAL = 60 * 1000L;

    private final MetricRegistry metrics = SharedMetricRegistries.getOrCreate(GORGON_METRICS);
    private final Timer brandProducer = metrics.timer(name(AdBrandProducer.class, "gorgon-brand-producer-timer"));
    private Map<Long, AppPackage> appPackageMap;
    // groupId_sourceStyleId => 映射后样式元素集合的映射
    private Map<String, BrandAdStyle> posConvertKey2Style = new HashMap<>();
    /**
     * 推广组定向特征在各个广告位的选择性map
     * 一级key：推广组id，二级key：广告位id，value：此推广组定向条件命中的pv数在此广告位总pv中的占比
     */
    private Map<Long,Map<Long,Double>> groupId2Selectivity = new HashMap<>();

    /**
     * 推广组支持的最低版本号
     * Map<groupId, Map<AppId, version>>
     */
    @Getter
    private Map<Long, Map<Long, String>> groupId2VersionFilter = new HashMap<>();

    /**
     * 近七天各个广告位的pv总数，key：广告位id， value：pv数
     */
    private Map<Long,Long> posPvMap = new HashMap<>();

    @Setter
    private int brandUpdateInterval = 10 * 60 * 1000;

    /**
     * cpm选择性map更新时间，默认每一小时更新一次
     */
    @Setter
    private int selectivityUpdateInterval = 60 * 60 * 1000;

    /**
     * 最后一次更新缓存数据的时间戳
     */
    private long brandAdThingsLastUpdateTime = 0L;

    /**
     * SelectivityMap最后一次更新缓存数据的时间戳
     */
    private long selectivityThingsLastUpdateTime = 0L;

    public ScheduledExecutorService fixRateExecutor = Executors.newScheduledThreadPool(1);

    public ScheduledExecutorService fixDelayExecutor = Executors.newScheduledThreadPool(1);


    protected void init() {
        initMetrics();

        fixDelayExecutor.scheduleWithFixedDelay(() -> loadCpmAdSelectivityMap(youxuanAdBrandDao.loadCurBrandAds()), 0, selectivityUpdateInterval, TimeUnit.MILLISECONDS);
        fixRateExecutor.scheduleAtFixedRate(this::doJob, 0, brandUpdateInterval, TimeUnit.MILLISECONDS);
    }

    private void initMetrics() {
        metrics.register(MetricUtils.name(AdBrandProducer.class, SELECTIVITY_MAP_PRODUCER_LAG), (Gauge<Long>) () -> System.currentTimeMillis() - selectivityThingsLastUpdateTime);
        metrics.register(MetricUtils.name(AdBrandProducer.class, BRAND_PRODUCER_LAG), (Gauge<Long>) () -> System.currentTimeMillis() - brandAdThingsLastUpdateTime);
    }

    public void doJob() {
        StopWatch stopWatch = new StopWatch();
        try (Timer.Context ignored = brandProducer.time()) {
            stopWatch.start();
            long startTime = System.currentTimeMillis();
            long endTime = startTime + scheduledDays * MILLISECONDS_OF_ONE_DAY;
            List<AdBrandDeliveryDO> brandScheduledAdList = getScheduleAdBranddDeliveryList(startTime,endTime);
            //读取品牌广告排期
            loadBrandScheduledAd(brandScheduledAdList);
            //读取品牌广告排期，第二版
            loadScheduledAdV2(brandScheduledAdList, startTime, endTime);


            List<AdBrandDeliveryDO> adBrandDeliveryList = youxuanAdBrandDao.loadCurBrandAds();

            loadCpmImpressionTimesMap(adBrandDeliveryList);
            //加载品牌广告
            loadBrandAdData(adBrandDeliveryList);
            loadBrandAdDataV2(adBrandDeliveryList);


            //删除已达到展示量限制的广告数据
            updateCpmImpressionTimes();
            //读取优选中配置的应用包信息
            loadAppPackageMap();
            loadConvertKey2BrandAdMapping();
            generateGroupId2VersionFilter(adBrandDeliveryList);
            brandAdThingsLastUpdateTime = System.currentTimeMillis();
        } catch (Throwable e) {
            log.error("produce brand ads Error:" + e.getMessage(), e);
        } finally {
            stopWatch.stop();
            log.info("produce {} finished: time={}", adBrandCacheDomain, stopWatch.getTime());
        }

    }

    private void loadBrandAdData(List<AdBrandDeliveryDO> brandAdList) {
        log.info("Begin to load brand ad data: ");
        if (CollectionUtils.isEmpty(brandAdList)) {
            // 如果访问DB出错，不做处理
            log.error("access adBrandSource db error, this should not happen");
            return;
        }

        int day = getDateTime().getDayOfMonth();
        Map<String, List<AdItem>> tmpCpdKey2Ads = new HashMap<>();
        Map<String, BrandAdWeight> tmpCpdKey2Weight = new HashMap<>();
        Map<String, BrandAdWeight> tmpTestCpdKey2Weight = new HashMap<>();
        HashMap<String, List<AdItem>> tmpCpmKey2UnionAds = new HashMap<>();
        Map<String,Map<String,String>> tmpKeyword2AdItemKey = new HashMap<>();
        Set<String> tmpKeywordAdKeys = new HashSet<>();
        Set<AdBrandDeliveryDO> tmpCpmAdDataSet = new HashSet<>();
        Map<Long, List<AdItem>> tmpDirectAdMap = new HashMap<>();
        for (AdBrandDeliveryDO brandAdItem : brandAdList) {
            try {
                AdItem adItem = AdBrandDeliveryItemConverter.buildAdItem(brandAdItem);
                if (adItem == null) {
                    log.error("unexpected data, brandAdItem:{}", brandAdItem.toString());
                    continue;
                }

                int posid = adItem.getImprPos();
                // 若此adItem设置了可以用测试设备直达
                if (adItem.isTestDirectTarget()) {
                    tmpDirectAdMap.computeIfAbsent((long) posid, c -> new ArrayList<>()).add(adItem);
                }

                if (brandAdItem.isCPT()) {
                    // CPD
                    int weight = brandAdItem.getStyleDisplayWeight();
                    // 获取广告位总权重
                    int totalWeight = brandAdItem.getPositionDisplayTimes();
                    String key = String.valueOf(posid);
                    List<AdItem> ads = tmpCpdKey2Ads.computeIfAbsent(key, k -> new ArrayList<>());
                    for (int i = 0; i < weight; i++) {
                        ads.add(adItem);
                    }
                    BrandAdWeight adWeight = tmpCpdKey2Weight.get(key);
                    if (adWeight == null) {
                        tmpCpdKey2Weight.put(key, new BrandAdWeight(weight, totalWeight));
                    } else {
                        adWeight.plus(weight);
                    }
                    //set test ads weight
                    if (Boolean.TRUE.equals(brandAdItem.getTestTag())) {
                        BrandAdWeight testAdWeight = tmpTestCpdKey2Weight.get(key);
                        if (testAdWeight == null) {
                            tmpTestCpdKey2Weight.put(key, new BrandAdWeight(weight, totalWeight));
                        } else {
                            testAdWeight.plus(weight);
                        }
                    }
                } else {
                    // CPM
                    adItem.getTextMap().put(BrandUtil.ORDER_TOTAL_SHOW_LIMIT,
                            String.valueOf(brandAdItem.getSumDisplayCount()));
                    adItem.getTextMap().put(BrandUtil.ORDER_DAILY_SHOW_LIMIT,
                            String.valueOf(brandAdItem.getDailyDisplayLimit()));
                    tmpCpmAdDataSet.add(brandAdItem);
                    String weight = String.valueOf(brandAdItem.getStyleDisplayWeight());
                    // 获取广告位总权重
                    String totalWeight = String.valueOf(brandAdItem.getPositionDisplayTimes());
                    adItem.getTextMap().put(BrandUtil.WEIGHT, weight);
                    adItem.getTextMap().put(BrandUtil.TOTAL_WEIGHT, totalWeight);
                    String key = String.valueOf(posid);
                    List<AdItem> ads = tmpCpmKey2UnionAds.computeIfAbsent(key, k -> new ArrayList<>());
                    ads.add(adItem);
                }
                List<String> keywordList = gson.fromJson(brandAdItem.getKeywordList(), new TypeToken<List<String>>() {}.getType());
                List<String> audioUrls = gson.fromJson(brandAdItem.getAudioUrls(), new TypeToken<List<String>>() {}.getType());
                if (CollectionUtils.isNotEmpty(keywordList) && brandAdItem.isSupportAdvertisingByKeywords()) {
                    String uniqueIdentifier = adItem.getBrandAdUniqueIdentifier();
                    tmpKeywordAdKeys.add(uniqueIdentifier);
                    for (int i = 0; i < keywordList.size(); i++) {
                        String keyWord = keywordList.get(i);
                        String audioUrl = "";
                        // 如果是全球发音广告，查词会一一对应相关的音频文件
                        if (adItem.isAudio()) {
                            try {
                                audioUrl = audioUrls.get(i);
                            } catch (Exception e) {
                                log.error("Can not get audio url,adItem is {}", adItem, e);
                            }
                        }
                        tmpKeyword2AdItemKey.computeIfAbsent(keyWord.toLowerCase(), k -> new HashMap<>())
                                .put(uniqueIdentifier, audioUrl);
                    }
                }
            } catch (Exception e) {
                log.error(e, e);
            }
        }
        cpdKey2Ads = tmpCpdKey2Ads;
        cpdKey2Weight = tmpCpdKey2Weight;
        testCpdKey2Weight = tmpTestCpdKey2Weight;
        cpmKey2UnionAds = tmpCpmKey2UnionAds;
        cpmAdDataSet = tmpCpmAdDataSet;
        keyword2AdItemKey = tmpKeyword2AdItemKey;
        keywordAds = tmpKeywordAdKeys;
        directAdsMap = tmpDirectAdMap;
        overImprCountLimitCpmKey2UnionAds.clear();
        tmpCpmKey2UnionAds.forEach((key, adItems) -> overImprCountLimitCpmKey2UnionAds.computeIfAbsent(key, k -> new ArrayList<>(adItems)));
        log.info("Finish load brand ad data");
    }

    /**
     * 构造druid查询请求
     */
    private DruidGroupByQuery buildCpmOrderDruidQuery(Set<AdBrandDeliveryDO> adBrandDeliveryDOSet) {
        Set<String> adGroupSet = new HashSet<>();
        //当前在投cpm订单中最早的投放开始时间
        long startTimestamp = System.currentTimeMillis();
        long orderTime;
        for (AdBrandDeliveryDO cpmAdData : adBrandDeliveryDOSet) {
            adGroupSet.add(String.valueOf(cpmAdData.getGroupId()));
            if (cpmAdData.getPlanStartTime() != null) {
                orderTime = cpmAdData.getPlanStartTime().getTime();
                long orderStartTime = orderTime;
                if (orderStartTime < startTimestamp && orderStartTime > 0) {
                    startTimestamp = orderStartTime;
                }
            }
        }
        //查询的起止时间，取开始投放时刻所在天的00:00到今天24:00
        DateTime endTime = getDateTime();
        DateTime startTime = new DateTime(startTimestamp, TIME_ZONE_CHINA).withTimeAtStartOfDay();
        endTime = endTime.withTimeAtStartOfDay().plusDays(1);
        Interval interval = new Interval(startTime, endTime);
        Granularity granularity = PeriodGranularity.builder()
                .period(QUERY_PERIOD_ONE_DAY)
                .timeZone(TIME_ZONE_CHINA)
                .build();
        //filter
        DruidFilter filter = new InFilter(AD_GROUP_ID, new ArrayList<>(adGroupSet));
        //dimension
        DruidDimension orderIdDimension = DefaultDimension.builder()
                .dimension(AD_GROUP_ID)
                .outputName(AD_GROUP_ID)
                .outputType(OutputType.LONG)
                .build();
        //aggregate
        DruidAggregator impr = new LongSumAggregator(IMPR, IMPR);
        Context context = Context.builder()
                .timeoutInMilliSeconds(druidContextTimeout)
                .build();
        return DruidGroupByQuery.builder()
                .dataSource(DATA_SOURCE_BRAND_STAT)
                .granularity(granularity)
                .intervals(Collections.singletonList(interval))
                .filter(filter)
                .dimensions(Collections.singletonList(orderIdDimension))
                .aggregators(Collections.singletonList(impr))
                .context(context)
                .build();
    }

    private void updateCpmImpressionTimes() {
        if (CollectionUtils.isEmpty(cpmAdDataSet)) {
            return;
        }
        //去除缓存中已经达到展示量的cpm广告
        int day = getDateTime().getDayOfMonth();
        scheduleCpmAdsMap.forEach((key, adsMap) ->
                adsMap.forEach((ms, adItems) -> adItems.removeIf(adItem -> cpmGroupIsOverMaxImpression(CpmLimit.instanceOf(adItem), day).isDayOverLimit() || cpmGroupIsOverMaxImpression(CpmLimit.instanceOf(adItem), day).isTotalOverLimit())));
        cpmKey2UnionAds.forEach((key, adItems) -> adItems.removeIf(adItem -> cpmGroupIsOverMaxImpression(CpmLimit.instanceOf(adItem), day).isDayOverLimit() || cpmGroupIsOverMaxImpression(CpmLimit.instanceOf(adItem), day).isTotalOverLimit()));
        scheduledCpmPlanMapV2.forEach((key, planTable) -> {
            for (Table.Cell<Long, Long, List<ScheduledPlan>> cell : planTable.cellSet()) {
                List<ScheduledPlan> planList = cell.getValue();
                Long endTime = cell.getColumnKey();
                // 过滤总展示量超过上限的计划
                planList.removeIf(plan -> cpmGroupIsOverMaxImpression(plan.getCpmLimit(), day).isTotalOverLimit());
                // 若结束时间小于等于第二天0点，则说明是今天的投放计划
                if (endTime <= LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MIN).toInstant(ZoneOffset.of("+8")).toEpochMilli()) {
                    // 过滤在今日投放且超过日展示量上线的计划
                    planList.removeIf(plan -> cpmGroupIsOverMaxImpression(plan.getCpmLimit(), day).isDayOverLimit());
                }
            }
        });
        cpmAdItemMap.forEach((key, adItems) -> adItems.removeIf(adItem -> cpmGroupIsOverMaxImpression(CpmLimit.instanceOf(adItem), day).isDayOverLimit() || cpmGroupIsOverMaxImpression(CpmLimit.instanceOf(adItem), day).isTotalOverLimit()));
    }

    /**
     * 根据druid查询结果计算cpm广告所在推广组的总展示量和当日展示量
     */
    private Map<Long, Map<Integer, Pair<Long, Long>>> calCpmTotalAndDailyImpr(List<CpmOrderImprStatistic> stat) {
        DateTime now = getDateTime();
        String todayFormat = now.withTimeAtStartOfDay().toDateTimeISO().toString();
        Map<Long, Long> cpmGroupTotalImpr = new HashMap<>();
        Map<Long, Long> cpmGroupDailyImpr = new HashMap<>();
        for (CpmOrderImprStatistic imprStatistic : stat) {
            long adGroupId = imprStatistic.getEvent().getAdGroupId();
            long imprCount = imprStatistic.getEvent().getImpr();
            cpmGroupTotalImpr.put(adGroupId,
                    cpmGroupTotalImpr.getOrDefault(adGroupId, 0L) + imprCount);
            if (todayFormat.equals(imprStatistic.getTimestamp())) {
                cpmGroupDailyImpr.put(adGroupId, imprCount);
            }
        }
        Map<Long, Map<Integer, Pair<Long, Long>>> result = new HashMap<>();
        int day = now.getDayOfMonth();
        for (Long adGroupId : cpmGroupTotalImpr.keySet()) {
            Map<Integer, Pair<Long, Long>> dayImprMap = new HashMap<>();
            dayImprMap.put(day, new Pair<>(cpmGroupTotalImpr.getOrDefault(adGroupId, 0L),
                    cpmGroupDailyImpr.getOrDefault(adGroupId, 0L)));
            result.put(adGroupId, dayImprMap);
        }
        return result;
    }


    /**
     * 从品牌广告数据库中读取正在投放、即将投放的广告，保存到map中
     */
    protected void loadBrandScheduledAd(List<AdBrandDeliveryDO> brandScheduledAdList) {
        if (CollectionUtils.isEmpty(brandScheduledAdList)) {
            // 访问DB出错，不做处理
            log.error("access adBrandSource db error, this should not happen");
            return;
        }
        log.info("brandScheduledAdList size : " + brandScheduledAdList.size());
        Map<String, Map<Long, List<AdItem>>> tempScheduledAdsMap = new HashMap<>();
        Map<String, Map<Long, List<AdItem>>> tempScheduledCpmAdsMap = new HashMap<>();
        HashMap<String, Map<Long, List<AdItem>>> tempScheduledNewCpmAdsMap = new HashMap<>();
        Map<String, Map<Long, BrandAdWeight>> tempScheduleCpdWeightMap = new HashMap<>();
        /**
         * 记录每个广告位的总轮播数
         */
        Map<String, Integer> totalWeightMap = new HashMap<>();
        for (AdBrandDeliveryDO brandAdItem : brandScheduledAdList) {
            try {
                AdItem adItem = AdBrandDeliveryItemConverter.buildAdItem(brandAdItem);
                if (adItem == null) {
                    continue;
                }
                int posid = adItem.getImprPos();

                //set ad weight
                String weight = String.valueOf(brandAdItem.getStyleDisplayWeight());
                Integer totalWeight = brandAdItem.getPositionDisplayTimes();
                adItem.getTextMap().put(BrandUtil.WEIGHT, weight);
                adItem.getTextMap().put(BrandUtil.TOTAL_WEIGHT, String.valueOf(totalWeight));

                if (brandAdItem.isCPM()) {
                    adItem.getTextMap().put(BrandUtil.ORDER_TOTAL_SHOW_LIMIT,
                            String.valueOf(brandAdItem.getSumDisplayCount()));
                    adItem.getTextMap().put(BrandUtil.ORDER_DAILY_SHOW_LIMIT,
                            String.valueOf(brandAdItem.getDailyDisplayLimit()));
                }
                String key = String.valueOf(posid);
                totalWeightMap.putIfAbsent(key, totalWeight);
                Map<Long, List<AdItem>> adsMap;
                if (brandAdItem.isCPM()) {
                    adsMap = tempScheduledCpmAdsMap.computeIfAbsent(key, k -> new HashMap<>());
                } else {
                    adsMap = tempScheduledAdsMap.computeIfAbsent(key, k -> new HashMap<>());
                }
                long adStartTime = adItem.getAdStartTime();
                List<AdItem> adList = adsMap.computeIfAbsent(adStartTime, k -> new ArrayList<>());
                adList.add(adItem);
            } catch (Exception e) {
                log.error(e, e);
            }
        }

        //根据已有的cpd投放计划修改、拆分cpm排期时间，将cpm广告填充到cpd广告排期的空隙中
        for (Map.Entry<String, Map<Long, List<AdItem>>> entry : tempScheduledAdsMap.entrySet()) {
            //key : positionId
            String key = entry.getKey();
            if (tempScheduledCpmAdsMap.containsKey(key)) {
                //记录广告的投放时间段，含义为：startTime~endTime内cpd广告占x轮播
                List<AdDeliveryTimePeriod> timePeriods = new LinkedList<>();
                List<AdItem> cpdAds = new LinkedList<>();
                Map<Long, List<AdItem>> cpdAdsMap = entry.getValue();
                cpdAdsMap.forEach((aLong, adItems) -> cpdAds.addAll(adItems));
                Map<Long, List<AdItem>> cpmAdsMap = tempScheduledCpmAdsMap.get(key);
                Set<Map.Entry<Long, List<AdItem>>> allAdsEntry =
                        new HashSet<>(Math.max((int) (cpdAdsMap.size() + cpmAdsMap.size() / 0.75f) + 1, 16));
                allAdsEntry.addAll(cpdAdsMap.entrySet());
                allAdsEntry.addAll(cpmAdsMap.entrySet());

                //对所有广告的起止时间节点排序，将投放计划分割成小段
                Set<Long> timeNodes = new HashSet<>();
                for (Map.Entry<Long, List<AdItem>> kv : allAdsEntry) {
                    long startTimestamp = kv.getKey();
                    timeNodes.add(startTimestamp);
                    for (AdItem ad : kv.getValue()) {
                        timeNodes.add(ad.getAdEndTime());
                    }
                }
                List<Long> timeNodeList = new ArrayList<>(timeNodes);
                Collections.sort(timeNodeList);

                int totalWeight = Optional.ofNullable(totalWeightMap.get(key)).orElse(DEFAULT_TOTAL_WEIGHT);
                for (int i = 0; i < timeNodeList.size() - 1; i++) {
                    long timeStart = timeNodeList.get(i);
                    long timeEnd = timeNodeList.get(i + 1);
                    int weight = 0;
                    for (AdItem ad : cpdAds) {
                        long adStartTime = ad.getAdStartTime();
                        long adEndTime = ad.getAdEndTime();
                        if (adStartTime <= timeStart && adEndTime >= timeEnd) {
                            weight += Integer.parseInt(ad.getTextMap().get(BrandUtil.WEIGHT));
                        }
                    }
                    timePeriods.add(new AdDeliveryTimePeriod(timeStart, timeEnd, weight));
                }

                //记录重新拆分排期时间后的cpm广告
                Map<Long, List<AdItem>> newCpmAdsMap =
                        tempScheduledNewCpmAdsMap.computeIfAbsent(key, k -> new HashMap<>());
                Map<Long, BrandAdWeight> brandAdWeightMap =
                        tempScheduleCpdWeightMap.computeIfAbsent(key, k -> new HashMap<>());
                for (AdDeliveryTimePeriod timePeriod : timePeriods) {
                    int weight = timePeriod.getWeight();
                    long startTimeMs = timePeriod.getStartTime();
                    long endTimeMs = timePeriod.getEndTime();
                    brandAdWeightMap.put(startTimeMs, new BrandAdWeight(weight, totalWeight));
                    //将cpm广告按照cpd的起止时间划分填充到对应时间段中
                    cpmAdsMap.forEach((startMs, adItems) -> {
                        //如果该cpm广告在timePeriod代表的时间段内，则添加到缓存中
                        if (startMs <= startTimeMs) {
                            adItems.forEach(adItem -> {
                                long endMs = adItem.getAdEndTime();
                                if (endMs >= endTimeMs) {
                                    AdItem newAdItem = copyAdItem(adItem);
                                    //修改cpm广告的投放起止时间，目的是将其放到cpd排期的空余流量中
                                    newAdItem.setAdStartTime(startTimeMs);
                                    newAdItem.setAdEndTime(endTimeMs);
                                    List<AdItem> newAdItemList =
                                            newCpmAdsMap.computeIfAbsent(startTimeMs, k -> new ArrayList<>());
                                    newAdItemList.add(newAdItem);
                                }
                            });
                        }
                    });

                }
            }
        }
        //考虑没有cpd但有cpm的位置，直接缓存所有cpm广告
        tempScheduledCpmAdsMap.forEach((key, map) -> {
            if (!tempScheduledNewCpmAdsMap.containsKey(key)) {
                tempScheduledNewCpmAdsMap.put(key, map);
                Map<Long, BrandAdWeight> brandAdWeightMap =
                        tempScheduleCpdWeightMap.computeIfAbsent(key, k -> new HashMap<>());
                map.forEach((startMs, adItems) ->
                        brandAdWeightMap.put(startMs, new BrandAdWeight(0, Optional.ofNullable(totalWeightMap.get(key)).orElse(DEFAULT_TOTAL_WEIGHT))));
            }
        });
        scheduledAdsMap = tempScheduledAdsMap;
        scheduleCpmAdsMap = tempScheduledNewCpmAdsMap;
        scheduleCpdAdsWeight = tempScheduleCpdWeightMap;
        overImprCountLimitScheduleCpmAdsMap.clear();
        // 深拷贝tempScheduledNewCpmAdsMap，防止删除adItems中对象的时候影响到此map
        tempScheduledNewCpmAdsMap.forEach((key, adsMap) ->
                adsMap.forEach((ms, adItems) -> overImprCountLimitScheduleCpmAdsMap.computeIfAbsent(key, k -> new HashMap<>()).computeIfAbsent(ms, k -> new ArrayList<>(adItems))));
        log.info("Finish load brand scheduled Ads data");
    }

    private List<AdBrandDeliveryDO> getScheduleAdBranddDeliveryList(long startTime, long endTime) {
        //0：投放中，2：即将投放
        List<Integer> statusList = Arrays.asList(0, 2);
        log.info("Begin load brand scheduled Ads data");
        List<AdBrandDeliveryDO> brandScheduledAdList = youxuanAdBrandDao.loadBrandAds(startTime, endTime, statusList);
        return brandScheduledAdList;
    }


    private static DateTime getDateTime() {
        return DateTime.now(TIME_ZONE_CHINA);
    }

    /**
     * copy adItem
     */
    public static AdItem copyAdItem(AdItem item) {
        AdItem adItem = new AdItem(item);
        AdVariation adv = new AdVariation();
        adv.duplicate(item.getAdVariation());
        adItem.setAdVariation(adv);
        adItem.setKeyword(Keyword.EMPTY);
        adItem.setMaxCpc(0);
        Map<String, ElementProperty> elementPropertyMap = item.getElementPropertyMap();
        elementPropertyMap.forEach((key, value) -> adItem.getElementPropertyMap().put(key, value));
        return adItem;
    }

    /**
     * 判断cpm广告是否已经达到最大展示量
     * @param cpmLimit
     * @param day
     */
    private CpmShowOverLimit cpmGroupIsOverMaxImpression(CpmLimit cpmLimit, int day) {
        Pair<Long, Long> cpmImpressionTimes = cpmAdsImpressionTimesMap
                .getOrDefault(cpmLimit.getAdGroupId(), Collections.emptyMap()).get(day);
        if (cpmImpressionTimes != null) {
            long totalShow = cpmImpressionTimes.getFirst();
            long totalShowLimit = cpmLimit.getTotalLimit();
            long dailyShow = cpmImpressionTimes.getSecond();
            long dailyShowLimit = cpmLimit.getDailyLimit();
            return CpmShowOverLimit.of(dailyShow >= dailyShowLimit, totalShow >= totalShowLimit);
        }
        return CpmShowOverLimit.of(false, false);
    }


    public void loadScheduledAdV2(List<AdBrandDeliveryDO> brandScheduledAdList, long startTime, long endTime) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("Begin load brand scheduled Ads data");
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(brandScheduledAdList)) {
            // 访问DB出错，不做处理
            log.error("access adBrandSource db error, this should not happen");
            return;
        }
        log.info("brandScheduledAdList size : " + brandScheduledAdList.size());
        Map<String, List<AdItem>> tmpScheduledAdMap = new HashMap<>();
        Map<String, HashBasedTable<Long, Long, List<ScheduledPlan>>> tmpScheduledCpdPlanMap = new HashMap<>();
        Map<String, HashBasedTable<Long, Long, List<ScheduledPlan>>> tmpScheduledCpmPlanMap = new HashMap<>();
        Map<Long, List<Pair<Long, Long>>> posId2timeTable = genTimeTable(brandScheduledAdList, startTime, endTime);

        for (AdBrandDeliveryDO brandAdItem : brandScheduledAdList) {
            try {
                AdItem adItem = AdBrandDeliveryItemConverter.buildAdItem(brandAdItem);
                if (adItem == null) {
                    continue;
                }
                String adId = adItem.getBrandAdUniqueIdentifier();
                int posid = adItem.getImprPos();
                List<Pair<Long, Long>> timeTable = posId2timeTable.getOrDefault(brandAdItem.getPositionId(), new ArrayList<>());
                BrandAdType brandAdType = BrandUtil.parseBrandAdType(adItem);
                long adStartTime = adItem.getAdStartTime();
                long adEndTime = adItem.getAdEndTime();

                //set ad weight
                Integer weight = brandAdItem.getStyleDisplayWeight();
                Integer totalWeight = brandAdItem.getPositionDisplayTimes();
                adItem.getTextMap().put(BrandUtil.AD_ID, adId);
                adItem.getTextMap().put(BrandUtil.WEIGHT, String.valueOf(weight));
                adItem.getTextMap().put(BrandUtil.TOTAL_WEIGHT, String.valueOf(totalWeight));
                if (brandAdItem.isCPM()) {
                    adItem.getTextMap().put(BrandUtil.ORDER_TOTAL_SHOW_LIMIT,
                            String.valueOf(brandAdItem.getSumDisplayCount()));
                    adItem.getTextMap().put(BrandUtil.ORDER_DAILY_SHOW_LIMIT,
                            String.valueOf(brandAdItem.getDailyDisplayLimit()));
                }
                String key = String.valueOf(posid);
                // 设置广告物料数据
                tmpScheduledAdMap.computeIfAbsent(key, k -> new ArrayList<>()).add(adItem);
                HashBasedTable<Long, Long, List<ScheduledPlan>> cpdScheduledPlanTable = tmpScheduledCpdPlanMap.computeIfAbsent(key, k -> HashBasedTable.create());
                HashBasedTable<Long, Long, List<ScheduledPlan>> cpmScheduledPlanTable = tmpScheduledCpmPlanMap.computeIfAbsent(key, k -> HashBasedTable.create());
                HashBasedTable<Long, Long, List<ScheduledPlan>> scheduledPlanTable;
                if (brandAdItem.isCPM()) {
                    scheduledPlanTable = cpmScheduledPlanTable;
                } else {
                    scheduledPlanTable = cpdScheduledPlanTable;
                }
                for (Pair<Long, Long> period : timeTable) {
                    // 此投放时间段的起止时间
                    Long start = period.getFirst();
                    Long end = period.getSecond();
                    // 此key下的每个时间段都需要设置cpd广告的投放计划，保证cpd广告计划table中包含全部时间段
                    if (!cpdScheduledPlanTable.contains(start, end)) {
                        cpdScheduledPlanTable.put(start, end, new ArrayList<>());
                    }
                    if (!scheduledPlanTable.contains(start, end)) {
                        scheduledPlanTable.put(start, end, new ArrayList<>());
                    }
                    // 检查每个投放时间段是否可以投放此广告
                    if (adStartTime <= period.getFirst() && adEndTime >= period.getSecond()) {
                        ScheduledPlan scheduledPlan = ScheduledPlan
                                .builder()
                                .adId(adId)
                                .cpmLimit(CpmLimit.instanceOf(adItem))
                                .weight(weight)
                                .brandAdType(brandAdType)
                                .build();
                        scheduledPlanTable.get(start, end).add(scheduledPlan);
                    }
                }
            } catch (Exception e) {
                log.error(e, e);
            }
        }
        scheduledAdMapV2 = tmpScheduledAdMap;
        scheduledCpdPlanMapV2 = tmpScheduledCpdPlanMap;
        scheduledCpmPlanMapV2 = tmpScheduledCpmPlanMap;
        overLimitScheduledAdMapV2.clear();
        tmpScheduledAdMap.forEach((key, adItems) -> overLimitScheduledAdMapV2.computeIfAbsent(key, k -> new ArrayList<>(adItems)));
        stopWatch.stop();
        log.info("Finish load brand scheduled Ads v2 data, time cost: {}", stopWatch.getTime());
    }

    /**
     * 根据获取到的广告对象列表，生成每个广告位投放时间段的列表
     * @param adBrandDeliveryDOList
     * @return key:广告位，value：此广告位投放时间段的列表
     */
    private Map<Long, List<Pair<Long, Long>>> genTimeTable(List<AdBrandDeliveryDO> adBrandDeliveryDOList, long startTime, long endTime) {
        Map<Long, Set<Long>> posId2TimeNodes = new HashMap<>();
        Map<Long, List<Pair<Long, Long>>> posId2timeTable = new HashMap<>();
        for (AdBrandDeliveryDO adBrandDeliveryDO : adBrandDeliveryDOList) {
            Set<Long> timeNodeSet = posId2TimeNodes.computeIfAbsent(adBrandDeliveryDO.getPositionId(), c -> new HashSet<>());
            timeNodeSet.add(startTime);
            timeNodeSet.add(endTime);
            timeNodeSet.add(adBrandDeliveryDO.getStartTime().getTime());
            timeNodeSet.add(adBrandDeliveryDO.getEndTime().getTime());
        }
        for (Map.Entry<Long, Set<Long>> entry : posId2TimeNodes.entrySet()) {
            long posId = entry.getKey();
            Set<Long> timeNodeSet = entry.getValue();
            List<Pair<Long, Long>> timeTable = new ArrayList<>();
            for (int i = 1; i <= scheduledDays; i++) {
                // 投放时间段内每天的0点，即00:00:00
                long midnightTime = LocalDateTime.of(LocalDate.now().plusDays(i), LocalTime.MIN).toInstant(ZoneOffset.of("+8")).toEpochMilli();
                // 若已有的时间节点中不存在接近0点一个小时之内的时间节点，则将0点插入时间节点集合中，目的是让最终的时间集合不存在跨天的时间段
                // 用于排除"当日展示量已达到上限，但总展示量未达到上限"的cpm投放计划
                if (timeNodeSet.stream().noneMatch(timeNode -> Math.abs(midnightTime - timeNode) < 1000L * 60 * 60)) {
                    timeNodeSet.add(midnightTime);
                }
            }
            // 删除不在提名范围内的时间点
            timeNodeSet.removeIf(c -> c < startTime || c > endTime);
            List<Long> timeList = new ArrayList<>(timeNodeSet);
            Collections.sort(timeList);
            for (int i = 0; i < timeList.size() - 1; i++) {
                timeTable.add(new Pair<>(timeList.get(i), timeList.get(i + 1)));
            }
            posId2timeTable.put(posId, timeTable);
        }
        return posId2timeTable;
    }

    private void loadBrandAdDataV2(List<AdBrandDeliveryDO> brandAdList) {
        log.info("Begin to load real time brand ad data v2: ");
        if (CollectionUtils.isEmpty(brandAdList)) {
            // 如果访问DB出错，不做处理
            log.error("access adBrandSource db error, this should not happen");
            return;
        }

        int day = getDateTime().getDayOfMonth();
        Map<String, Set<AdItem>> tmpCpdMap = new HashMap<>();
        Map<String, Set<AdItem>> tmpCpmMap = new HashMap<>();
        Map<Long, Long> groupId2WeightedRatio = new HashMap<>();
        for (AdBrandDeliveryDO adBrandDeliveryDO : brandAdList) {
            try {
                AdItem adItem = AdBrandDeliveryItemConverter.buildAdItem(adBrandDeliveryDO);
                if (adItem == null) {
                    log.error("unexpected data, brandAdItem:{}", adBrandDeliveryDO.toString());
                    continue;
                }
                long posid = adBrandDeliveryDO.getPositionId();

                if (adBrandDeliveryDO.isCPT()) {
                    String key = String.valueOf(posid);
                    Set<AdItem> ads = tmpCpdMap.computeIfAbsent(key, k -> new HashSet<>());
                    ads.add(adItem);
                } else {
                    // 根据推广组id和广告位id计算adItem所拥有的选择性，默认为1.0，即没有选择性
                    double selectivity = groupId2Selectivity.getOrDefault(adBrandDeliveryDO.getGroupId(), new HashMap<>())
                            .getOrDefault(adBrandDeliveryDO.getPositionId(), 1.0);
                    adItem.getTextMap().put(BrandUtil.ORDER_TOTAL_SHOW_LIMIT,
                            String.valueOf(adBrandDeliveryDO.getSumDisplayCount()));
                    adItem.getTextMap().put(BrandUtil.ORDER_DAILY_SHOW_LIMIT,
                            String.valueOf(adBrandDeliveryDO.getDailyDisplayLimit()));
                    long basicWeight;
                    if (adItem.getCpmCostType() == 2) {
                        // 若设置了加速模式，那么基本权重一直是当日预期展示量，不会随已展示数降低，确保加速效果
                        basicWeight = adItem.getBrandTargetDailyImprCount() * adBrandDeliveryDO.getCpmAccelerateRatio();
                    } else {
                        basicWeight = adItem.getBrandTargetDailyImprCount() - cpmAdsImpressionTimesMap
                                .getOrDefault(adItem.getAdGroupId(), new HashMap<>())
                                .getOrDefault(day, new Pair<>(0L, 0L)).getSecond();
                        // 若当日已展示量超过了日展示量上限，则权重设置为0
                        basicWeight = Math.max(basicWeight, 0);
                    }
                    adItem.setCpmWeightedRatio((long) (basicWeight / selectivity));
                    if (!groupId2WeightedRatio.containsKey(adItem.getAdGroupId())) {
                        groupId2WeightedRatio.put(adItem.getAdGroupId(), adItem.getCpmWeightedRatio());
                    }
                    String key = String.valueOf(posid);
                    Set<AdItem> ads = tmpCpmMap.computeIfAbsent(key, k -> new HashSet<>());
                    ads.add(adItem);
                }
            } catch (Exception e) {
                log.error(e, e);
            }
        }
        cpdAdItemMap = tmpCpdMap;
        cpmAdItemMap = tmpCpmMap;
        log.info("Finish load real time brand ad data v2, cpm group id weighted ratio : {}", groupId2WeightedRatio);
    }

    private void loadCpmImpressionTimesMap(List<AdBrandDeliveryDO> brandAdList){
        if (CollectionUtils.isEmpty(brandAdList)) {
            // 如果访问DB出错，不做处理
            log.error("access adBrandSource db error, this should not happen");
            return;
        }
        cpmAdDataSet = brandAdList.stream().filter(AdBrandDeliveryDO::isCPM).collect(Collectors.toSet());
        try {
            DruidGroupByQuery query = buildCpmOrderDruidQuery(cpmAdDataSet);
            log.info("druid log, brand_stat query is:{}", new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(query));
            List<CpmOrderImprStatistic> cpmOrderImprStatistic = druidClient.query(query, CpmOrderImprStatistic.class);
            cpmAdsImpressionTimesMap = calCpmTotalAndDailyImpr(cpmOrderImprStatistic);
        } catch (Exception e) {
            log.error("error query druid.", e);
        }
    }


    private void loadAppPackageMap() {
        List<AppPackage> appPackageList = youxuanAdBrandDao.loadValidAppPackage();
        Map<Long, AppPackage> tmpAppPackageMap = appPackageList.stream().collect(Collectors.toMap(AppPackage::getId, Function.identity()));
        if (MapUtils.isEmpty(tmpAppPackageMap)) {
            // 访问DB出错，不做处理
            log.error("load appPackage db error, result is empty,this should not happen");
        } else {
            appPackageMap = tmpAppPackageMap;
        }
    }


    /**
     * 读取优选中配置的广告流量映射规则
     */
    private void loadConvertKey2BrandAdMapping() {
        List<BrandAdMappingDO> brandAdMappingDOList = youxuanAdBrandDao.loadValidBrandAdMapping();
        Map<String, BrandAdStyle> tmpPosConvertKey2Style = brandAdMappingDOList.stream()
                .collect(Collectors.toMap(brandAdMappingDO -> brandAdMappingDO.getGroupId() + "_" + brandAdMappingDO.getSourceStyleId(),
                        brandAdMappingDO -> {
                            List<StyleElement> styleElementList = BrandUtil.brandAdMapping2StyleElementList(brandAdMappingDO);
                            boolean hasMultiSizeElement = checkMultiSizeElementList(styleElementList);
                            // 包含多个同名不同尺寸的元素，且所属广告位为开屏广告位的样式，是需要做尺寸适配的样式
                            return BrandAdStyle.builder()
                                    .fullScreen(brandAdMappingDO.isMappingStyleFullScreen())
                                    .mediaId(brandAdMappingDO.getMappingStyleMediaId())
                                    .posType(brandAdMappingDO.getMappingSlotType())
                                    .osType(brandAdMappingDO.getMappingStyleOsType())
                                    .posId(brandAdMappingDO.getMappingPosId())
                                    .needSuitSize(OPEN_SCREEN_POS_TYPE == brandAdMappingDO.getMappingSlotType() && hasMultiSizeElement)
                                    .styleElementList(styleElementList).build();
                        }
                ));
        if (MapUtils.isEmpty(tmpPosConvertKey2Style)) {
            log.warn("load brandAdMapping db error, result is empty,this should not happen");
        }
        posConvertKey2Style = tmpPosConvertKey2Style;
    }


    private void loadCpmAdSelectivityMap(List<AdBrandDeliveryDO> brandAdList) {
        log.info("start load cpm ad selectivity map");
        if (CollectionUtils.isEmpty(brandAdList)) {
            // 如果访问DB出错，不做处理
            log.error("access adBrandSource db error, this should not happen");
            return;
        }
        // 根据groupId去重，因为同一个推广组的定向条件一定相同，所以一个推广组只需要查询一次druid即可
        cpmAdDataSet = new HashSet<>(brandAdList.stream()
                .filter(AdBrandDeliveryDO::isCPM)
                .collect(Collectors.toMap(AdBrandDeliveryDO::getGroupId, Function.identity(), (oldValue, newValue) -> newValue))
                .values());
        // 当前有效推广组覆盖到的广告位id集合
        Set<Long> posIdSet = brandAdList.stream().map(AdBrandDeliveryDO::getPositionId).collect(Collectors.toSet());
        Map<Long, Long> tmpPosPvMap = new HashMap<>();
        try {
            DruidGroupByQuery pvQuery = buildDruidPvQuery(null, posIdSet);
            log.info("druid log, pos pv query is:{}", new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(pvQuery));
            List<CpmGroupPvStatistic> cpmGroupPvStatistics = druidClient.query(pvQuery, CpmGroupPvStatistic.class);
            tmpPosPvMap = genPosId2PvMap(cpmGroupPvStatistics);
            if (MapUtils.isNotEmpty(tmpPosPvMap)) {
                log.info("load position pv map success!");
                posPvMap = tmpPosPvMap;
            }
        } catch (Exception e) {
            log.error("error query pos pv druid.", e);
        }
        Map<Long, Map<Long, Double>> tmpGroupSeleivityMap = new HashMap<>();
        for (AdBrandDeliveryDO adBrandDeliveryDO : cpmAdDataSet) {
            try {
                DruidGroupByQuery groupQuery = buildDruidPvQuery(adBrandDeliveryDO, posIdSet);
                log.info("druid log, group selectivity query is:{}", new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(groupQuery));
                List<CpmGroupPvStatistic> cpmGroupPvStatistics = druidClient.query(groupQuery, CpmGroupPvStatistic.class);
                if (CollectionUtils.isNotEmpty(cpmGroupPvStatistics)) {
                    Map<Long, Long> map = genPosId2PvMap(cpmGroupPvStatistics);
                    if (MapUtils.isNotEmpty(map)) {
                        Map<Long, Double> selectivityMap = new HashMap<>();
                        for (Map.Entry<Long, Long> entry : map.entrySet()) {
                            long posId = entry.getKey();
                            long posPv = posPvMap.getOrDefault(posId, 0L);
                            long featurePv = entry.getValue();
                            if (posPv == 0 || featurePv == 0) {
                                selectivityMap.put(posId, 1.0);
                            } else {
                                selectivityMap.put(posId, (double) featurePv / posPv);
                            }
                        }
                        tmpGroupSeleivityMap.put(adBrandDeliveryDO.getGroupId(), selectivityMap);
                    }
                }
            } catch (Exception e) {
                log.error("error query group pv druid.", e);
            }
        }
        if (MapUtils.isNotEmpty(tmpGroupSeleivityMap)) {
            groupId2Selectivity = tmpGroupSeleivityMap;
            log.info("load groupId2Selectivity success! content: {}", groupId2Selectivity);
        } else {
            groupId2Selectivity.clear();
            log.error("load groupId2Selectivity fail, selectivity map will be empty!");
        }
        selectivityThingsLastUpdateTime = System.currentTimeMillis();
    }

    /**
     * 检查元素列表中是否有同名元素
     *
     * @return
     */
    public static boolean checkMultiSizeElementList(List<StyleElement> styleElementList) {
        Set<String> elementKeys = new HashSet<>();
        boolean hasMultiSizeElement = false;
        for (StyleElement se : styleElementList) {
            if (elementKeys.contains(se.getElementKey())) {
                hasMultiSizeElement = true;
            }
            elementKeys.add(se.getElementKey());
        }
        return hasMultiSizeElement;
    }

    public AppPackage appPackageById(Long id) {
        return appPackageMap.getOrDefault(id, null);
    }

    public BrandAdStyle mappingBrandAdStyleByKey(String key) {
        return posConvertKey2Style.getOrDefault(key, null);
    }

    public long getAdGroupDailyImprCount(long groupId, int dayOfMonth) {
        return cpmAdsImpressionTimesMap.getOrDefault(groupId, new HashMap<>()).getOrDefault(dayOfMonth, new Pair<>(0L, 0L)).getSecond();
    }

    /**
     * 构建基于推广组定向特征的pv druid查询，用于查询推广组定向特征在过去七天覆盖的总pv
     *
     * @param adBrandDeliveryDO
     * @return
     */
    private DruidGroupByQuery buildDruidPvQuery(@Nullable AdBrandDeliveryDO adBrandDeliveryDO, Set<Long> posIdSet) {
        DateTime endTime = getDateTime();
        //开始时间是7天前
        DateTime startTime = endTime.minusDays(7);
        Interval interval = new Interval(startTime, endTime);
        Granularity granularity = new SimpleGranularity(PredefinedGranularity.ALL);
        //filter
        DruidFilter filter = getFeatureFilter(adBrandDeliveryDO, posIdSet);
        //dimension
        DruidDimension orderIdDimension = DefaultDimension.builder()
                .dimension(POS_ID)
                .outputName(POS_ID)
                .outputType(OutputType.LONG)
                .build();
        //aggregate
        DruidAggregator pv = new LongSumAggregator(PV, PV);
        Context context = Context.builder()
                .timeoutInMilliSeconds(druidContextTimeout)
                .build();
        return DruidGroupByQuery.builder()
                .dataSource(DATA_SOURCE_BRAND_FEATURE_PV)
                .granularity(granularity)
                .intervals(Collections.singletonList(interval))
                .filter(filter)
                .dimensions(Collections.singletonList(orderIdDimension))
                .aggregators(Collections.singletonList(pv))
                .context(context)
                .build();
    }

    private AndFilter getFeatureFilter(AdBrandDeliveryDO adBrandDeliveryDO, Set<Long> posIdset){
        List<DruidFilter> druidFilters = new ArrayList<>();
        // 只获取自然量的pv数据，即venderId = ""的pv
        addInFilter(Collections.singletonList(""), BrandSelectivityFeatureEnum.VENDER_ID.getFiledName(), druidFilters, true);
        if (CollectionUtils.isNotEmpty(posIdset)) {
            addInFilter(posIdset, BrandSelectivityFeatureEnum.POSITION.getFiledName(), druidFilters, true);
        }
        // 若adBrandDeliveryDO为null，视为对总pv的查询
        if (Objects.isNull(adBrandDeliveryDO)) {
            return new AndFilter(druidFilters);
        }
        // 性别过滤
        if (adBrandDeliveryDO.getGenderOrientation() > 0) {
            addInFilter(BrandGenderEnum.getCodeListByGender(adBrandDeliveryDO.getGenderOrientation()), BrandSelectivityFeatureEnum.GENDER.getFiledName(), druidFilters, true);
        }
        // 年龄过滤
        if (StringUtils.isNotBlank(adBrandDeliveryDO.getAgeOrientationIntervals())) {
            addInFilter(BrandUtil.extractAgeSet(adBrandDeliveryDO.getAgeOrientationIntervals()), BrandSelectivityFeatureEnum.AGE.getFiledName(), druidFilters, true);
        }
        Set<String> stateOrientations = gson.fromJson(StringUtils.defaultString(adBrandDeliveryDO.getCrowdLabelOrientation(), EMPTY_JSON_ARRAY),
                new TypeToken<HashSet<String>>() {
                }.getType());
        // 身份过滤
        if(CollectionUtils.isNotEmpty(stateOrientations)){
            addInFilter(stateOrientations, BrandSelectivityFeatureEnum.STATE.getFiledName(), druidFilters, true);
        }
        Set<Long> crowdPackIds = gson.fromJson(StringUtils.defaultString(adBrandDeliveryDO.getCrowdPackIds(), EMPTY_JSON_ARRAY),
                new TypeToken<Set<Long>>() {
                }.getType());
        // 人群包定向
        if(BooleanUtils.toBooleanDefaultIfNull(adBrandDeliveryDO.isOpenCustomCrowdPack(), true)
                && CollectionUtils.isNotEmpty(crowdPackIds)){
            addInFilter(crowdPackIds, BrandSelectivityFeatureEnum.CROWD_PACKAGE_IDS.getFiledName(), druidFilters, adBrandDeliveryDO.isIncludeCrowdPack());
        }
        Set<Long> appInstalledIds = gson.fromJson(StringUtils.defaultString(adBrandDeliveryDO.getInstalledAppPackageIds(), EMPTY_JSON_ARRAY),
                new TypeToken<HashSet<Long>>() {
                }.getType());
        // 应用安装信息
        if (BooleanUtils.toBooleanDefaultIfNull(adBrandDeliveryDO.isAppInstalledOrientation(), false)
                && CollectionUtils.isNotEmpty(appInstalledIds)) {
            addInFilter(crowdPackIds, BrandSelectivityFeatureEnum.CROWD_PACKAGE_IDS.getFiledName(), druidFilters, true);
        }
        List<Integer> areas = gson.fromJson(adBrandDeliveryDO.getCityArea(), new TypeToken<List<Integer>>() {
        }.getType());
        // 地域定向
        if (adBrandDeliveryDO.getRegionalOrientation() && CollectionUtils.isNotEmpty(areas)) {
            addInFilter(areas, BrandSelectivityFeatureEnum.AREA.getFiledName(), druidFilters, true);
        }
        List<Integer> countrys = gson.fromJson(adBrandDeliveryDO.getCountryArea(), new TypeToken<List<Integer>>() {
        }.getType());
        // 国家定向
        if (adBrandDeliveryDO.getRegionalOrientation() && CollectionUtils.isNotEmpty(areas)) {
            addInFilter(countrys, BrandSelectivityFeatureEnum.COUNTRY.getFiledName(), druidFilters, true);
        }
        return new AndFilter(druidFilters);
    }

    /**
     * 将指定特征值和维度名称构建出druidFilter并加入到filter列表中
     * @param values 特征值
     * @param dimension 维度名称
     * @param druidFilters 要加入到的filter列表
     * @param in 是否要构建inFilter,false-构建notin，true-构建in
     * @param <T>
     */
    private <T> void addInFilter(Collection<T> values, String dimension, List<DruidFilter> druidFilters, boolean in) {
        if (CollectionUtils.isNotEmpty(values)) {
            DruidFilter filter = new InFilter(dimension,
                    values.stream()
                            .map(Functions.toStringFunction())
                            .collect(Collectors.toList()));
            if (in) {
                druidFilters.add(filter);
            } else {
                druidFilters.add(new NotFilter(filter));
            }
        }
    }

    private Map<Long, Long> genPosId2PvMap(List<CpmGroupPvStatistic> cpmGroupPvStatistics) {
        Map<Long, Long> res = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cpmGroupPvStatistics)) {
            for (CpmGroupPvStatistic cpmGroupPvStatistic : cpmGroupPvStatistics) {
                if (cpmGroupPvStatistic.getEvent().getPv() > 0) {
                    res.put(cpmGroupPvStatistic.getEvent().getImprPos(), cpmGroupPvStatistic.getEvent().getPv());
                }
            }
        }
        return res;
    }

    public void destroy(){
        fixDelayExecutor.shutdown();
        fixRateExecutor.shutdown();
    }

    /**
     * 记录一个cpm推广组的展示量是否超过设定的限制
     */
    @AllArgsConstructor
    static class CpmShowOverLimit {
        /**
         * 是否已经超过日展示上限
         */
        @Getter
        boolean dayOverLimit;
        /**
         * 是否已经超过总展示上限
         */
        @Getter
        boolean totalOverLimit;

        static CpmShowOverLimit of(boolean dayOverLimit, boolean totalOverLimit) {
            return new CpmShowOverLimit(dayOverLimit, totalOverLimit);
        }
    }

    /**
     * 根据广告投放数据生成推广组-版本过滤配置
     * @param adBrandDeliveryDOList 当前在投广告列表
     */
    private void generateGroupId2VersionFilter(List<AdBrandDeliveryDO> adBrandDeliveryDOList) {
        Map<Long, Map<Long, String>> tmpGroupId2VersionFilter = new HashMap<>();
        for (AdBrandDeliveryDO ad : adBrandDeliveryDOList) {
            if (ad.isVersionFilter() && StringUtils.isNotBlank(ad.getVersionFilterList())) {
                // 如果已经有该groupId的数据，跳过
                if (tmpGroupId2VersionFilter.containsKey(ad.getGroupId())) {
                    continue;
                }
                // versionFilterList格式: [{appId:版本号},{appId:版本号}]
                List<Map<String, String>> filterList = gson.fromJson(ad.getVersionFilterList(), new TypeToken<List<Map<String, String>>>() {
                }.getType());
                if (filterList != null) {
                    Map<Long, String> appVersionMap = new HashMap<>();
                    for (Map<String, String> filter : filterList) {
                        for (Map.Entry<String, String> entry : filter.entrySet()) {
                            try {
                                Long appId = Long.valueOf(entry.getKey());
                                appVersionMap.put(appId, entry.getValue());
                            } catch (Exception ignore) {}
                        }
                    }
                    tmpGroupId2VersionFilter.put(ad.getGroupId(), appVersionMap);
                }
            }
        }
        groupId2VersionFilter = tmpGroupId2VersionFilter;
    }

}

