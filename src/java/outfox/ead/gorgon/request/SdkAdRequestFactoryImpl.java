/*
 * @(#)SDKBidRequestFactory.java, 2014年10月16日. Copyright 2014 Yodao, Inc. All
 *                                rights reserved. YODAO
 *                                PROPRIETARY/CONFIDENTIAL. Use is subject to
 *                                license terms.
 */
package outfox.ead.gorgon.request;

import com.alibaba.fastjson.JSON;
import com.youdao.ead.mobilemodels.util.MobileModelProvider;
import com.youdao.quipu.avro.schema.GorgonInterfaceType;
import io.gromit.uaparser.Parser;
import io.gromit.uaparser.cache.GuavaCache;
import io.gromit.uaparser.model.OS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import outfox.ead.brand.model.DictAdPositionInfo;
import outfox.ead.brand.model.request.AdRequest;
import outfox.ead.brand.service.AdRequestFactory;
import outfox.ead.brand.service.DictAdPositionInfoProvider;
import outfox.ead.brand.util.BrandUtil;
import outfox.ead.brand.util.area.AdvancedIpSearcher;
import outfox.ead.brand.util.http.HttpRequestHelper;
import outfox.ead.data.*;
import outfox.ead.data.datamanager.IDevicePriceService;
import outfox.ead.data.datamanager.ISdkAdSlotService;
import outfox.ead.data.dataserv.ADSlotStyles;
import outfox.ead.data.dataserv.ADStyle;
import outfox.ead.data.protobuf.DataHolder;
import outfox.ead.dataserv.client.DataReadable;
import outfox.ead.dsp.protocol.youdao.Bid;
import outfox.ead.gorgon.exception.AdState;
import outfox.ead.gorgon.exception.AdStateException;
import outfox.ead.gorgon.request.codec.DecodeService;
import outfox.ead.gorgon.request.userfeature.UserAgentDto;
import outfox.ead.gorgon.request.userfeature.UserFeatureProcessor;
import outfox.ead.gorgon.service.AndroidBrandService;
import outfox.ead.gorgon.service.AppleModelService;
import outfox.ead.gorgon.service.DeviceInfoService;
import outfox.ead.gorgon.service.S3DownloaderService;
import outfox.ead.gorgon.service.YdidMappingService;
import outfox.ead.gorgon.service.brand.IBrandStatService;
import outfox.ead.gorgon.service.brand.SupplyDeviceService;
import outfox.ead.gorgon.service.carousel.CarouselServiceImpl;
import outfox.ead.gorgon.service.fix.IRequestFix;
import outfox.ead.gorgon.utils.MetricMeters;
import outfox.ead.gorgon.utils.StringHelper;
import outfox.ead.gorgon.utils.VersionCompareUtil;
import toolbox.misc.net.IpUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static outfox.ead.data.AdStyleType.ALL;
import static outfox.ead.data.AdStyleType.DOWNLOAD;
import static outfox.ead.dsp.protocol.youdao.Bid.BidRequest.Device.OS.*;
import static outfox.ead.gorgon.request.DictAdRequestAttributes.*;
import static outfox.ead.gorgon.request.SdkAdRequestValues.*;
import static outfox.ead.gorgon.utils.LogUtils.uniform;
import static outfox.gorgon.SdkAdRequestConstant.*;
import static outfox.venus.dsp.base.DspConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("sdkAdRequestFactory")
public class SdkAdRequestFactoryImpl implements SdkAdRequestFactory {
    /**
     * only print warning log since lots of Apps have not set properties right.<br>
     * such as {@code NETWORK_TYPE} or {@code DETAIL_NETWORK_TYPE}
     */
    private static final Logger appSideWarnLog = LoggerFactory.getLogger("appSideWarnLog");

    public static final int MAX_AD_COUNT = 20;

    public static final int OTHER_CITY_ID = 581;

    public static final String STRATEGY = "newAd";

    public static final String API_VERSION = "3.0";

    public static final int DEFAULT_AD_NUM = 6;

    /**
     * <a href="https://jira.inner.youdao.com/browse/YOUXUAN-1124">jira</a>, android sdk版本>=v4.2.4时，sdk发起的广告请求未携带secure参数，此时需要赋值为true。
     */
    private static final int[] ANDROID_SECURE_MAX_SDK_VERSION = new int[]{4, 2, 3};

    /**
     * to parse UA, init with cache.
     */
    public static final Parser parser;
    public static final String UA_PARSE_DEFAULT = "Other";

    static {
        try {
            parser = new Parser().cache(new GuavaCache());
            log.info("Init ua-parser successfully.");
        } catch (IOException e) {
            log.error("Init ua-parser failed. ", e);
            throw new ExceptionInInitializerError(e);
        }
    }

    @Resource
    protected DataReadable<ADSlotStyles, String> slot2ADStylesReadProxy;

    @Resource
    protected DataReadable<ADStyle, Long> adSlotStyleReadProxy;

    @Resource
    protected DataReadable<SdkAppInfo, Long> sdkAppInfoReadProxy;

    @Resource
    private DictAdPositionInfoProvider dictAdPositionInfoProvider;

    @Autowired
    private ISdkAdSlotService sdkAdSlotService;

    @Autowired
    private DecodeService decodeService;

    @Autowired
    private List<IRequestFix> requestFixes;

    @Autowired
    IDevicePriceService devicePriceService;

    @Autowired
    AppleModelService appleModelService;

    @Autowired
    private AndroidBrandService androidBrandService;

    @Autowired
    private DeviceInfoService deviceInfoService;

    @Autowired
    private UserFeatureProcessor userFeatureProcessor;

    @Autowired
    private IBrandStatService brandStatService;

    @Autowired
    private SupplyDeviceService supplyDeviceService;

    @Autowired
    private YdidMappingService ydidMappingService;

    @Autowired
    private MobileModelProvider mobileModelProvider;

    @Autowired
    private S3DownloaderService s3DownloaderService;

    @Override
    @Nullable
    public SdkAdRequest build(HttpServletRequest httpServletRequest, GorgonInterfaceType interfaceType) throws Exception {
        Map<String, String> paramMap;
        try {
            paramMap = getRequestParameters(httpServletRequest);
        } catch (IllegalBlockSizeException | BadPaddingException e) {
            throw new AdStateException(AdState.ERROR_DECODE, "DESCipher.doFinal failed");
        }
        if (MapUtils.isEmpty(paramMap)) {
            return null;
        }

        SdkAdRequest sdkAdRequest = new SdkAdRequest();
        putHttpParametersIntoSdkAdRequest(sdkAdRequest, paramMap);
        try {
            // 如果是带有 fail 标识的广告请求直接返回。
            // SDK 解析服务端返回的广告失败时，会触发此请求。
            if (StringUtils.isNotBlank(httpServletRequest.getParameter(FAIL_URL_TAG))) {
                // 上次请求失败，本次请求为带fail tag的报错请求
                MetricMeters.getGorgonFailTagMeter().mark();
                return sdkAdRequest;
            }
            paramMap = sdkAdRequest.getAttributes();
            //在统一设备号大写之前记录原始oaid、imei、idfa值，过滤无效设备号
            sdkAdRequest.setOriginalOaid(deviceInfoService.validOrDefaultId(paramMap.get(OAID)));
            sdkAdRequest.setOriginalImei(deviceInfoService.validOrDefaultId(paramMap.get(IMEI)));
            sdkAdRequest.setOriginalUdid(deviceInfoService.validOrDefaultId(paramMap.get(UDID)));
            if (isVender(httpServletRequest.getHeader("isVender"))) {
                if (StringUtils.isBlank(sdkAdRequest.getAttribute(VENDER_ID))) {
                    sdkAdRequest.setAttribute(VENDER_ID, "-1");
                }
            }
            sdkAdRequest.setVenderId(sdkAdRequest.getAttribute(VENDER_ID));
            sdkAdRequest.setVenderSource(sdkAdRequest.getAttribute(VENDER_SOURCE));
            List<Long> venderGroupIds = Arrays.stream(sdkAdRequest.getAttributes().getOrDefault(VENDER_GROUP_ID, "").split(","))
                    .map(String::trim)
                    .filter(NumberUtils::isDigits)
                    .map(Long::parseLong)
                    .toList();
            sdkAdRequest.setVenderGroupIds(venderGroupIds);
            sdkAdRequest.setVerCodeOfAG(sdkAdRequest.getAttribute(VER_CODE_OF_AG));
            sdkAdRequest.setVerCodeOfHms(sdkAdRequest.getAttribute(VER_CODE_OF_HMS));
            resetIdFormat(sdkAdRequest, paramMap);
            SdkAdSlot sdkSlot = getSlot(paramMap);
            validateSlotStatus(sdkSlot);
            validateStyleInfo(sdkSlot.getSdkSlotUdid());
            //优先设置好slot、app信息方便以后使用
            sdkAdRequest.setAdSlot(sdkSlot);
            setDefaultIsrdForPcSlot(sdkAdRequest);
            sdkAdRequest.setApp(sdkAppInfoReadProxy.getData(sdkSlot.getSdkAppId()));

            sdkAdRequest.setDeviceName(paramMap.getOrDefault(DEVICE_NAME, ""));
            sdkAdRequest.setCarrierName(paramMap.getOrDefault(CARRIER_NAME, ""));
            setMagicNo(sdkAdRequest, paramMap);
            setVersions(sdkAdRequest, paramMap);
            setRequestFrom(sdkAdRequest, paramMap);
            setUA(sdkAdRequest, httpServletRequest);
            setNetworkType(sdkAdRequest, paramMap);
            orderedSetOsTypeAndIsSecure(sdkAdRequest, paramMap, sdkSlot);
            setSyndicationID(sdkAdRequest);
            setBid(sdkAdRequest);
            setMultiRequest(sdkAdRequest, paramMap);
            setRequestCount(sdkAdRequest, paramMap);
            setLocationInfo(sdkAdRequest, httpServletRequest, paramMap);
            setDeviceInfo(sdkAdRequest, paramMap);
            setTest(sdkAdRequest, sdkSlot);
            setBrandTest(sdkAdRequest, sdkSlot);
            setNativeBabeLocation(sdkAdRequest);
            setStrategyInfo(sdkAdRequest, sdkSlot, paramMap);
            setUserAgentDto(sdkAdRequest);
            setBrandAndModel(sdkAdRequest);
            setReferer(sdkAdRequest, httpServletRequest);
            setCookie(sdkAdRequest, httpServletRequest, paramMap);
            setBanTarget(sdkAdRequest);
            sdkAdRequest.setAbTest(paramMap.getOrDefault(AB_TEST, ""));
            setYouthMode(sdkAdRequest, paramMap);
            setKeyword(sdkAdRequest, paramMap);
            setDictFields(sdkAdRequest, paramMap);
            setSupportMarketUrl(sdkAdRequest, paramMap);
            //todo 频控测试用
            if (paramMap.containsKey(AdRequestFactory.MOCK_TIMESTAMP)) {
                sdkAdRequest.setAttribute(AdRequestFactory.MOCK_TIMESTAMP, paramMap.get(AdRequestFactory.MOCK_TIMESTAMP));
            }
            sdkAdRequest.setShouldNominateInZhiXuan(AdProviderService.shouldNominateInZhiXuan(sdkAdRequest));
            // 是否支持ulink，参数传了就按照请求传参，参数没传就按照数据库配置。
            String paramSupportUlink = paramMap.get(IS_SUPPORT_ULINK);
            sdkAdRequest.setSupportUlink(
                    paramSupportUlink != null ?
                            paramSupportUlink.equals(SUPPORT_ULINK_TRUE) :
                            (sdkAdRequest.getApp().isSupportUlink() && sdkAdRequest.getApp().getOsType() == IOS_VALUE)
            );
            userFeatureProcessor.process(sdkAdRequest);
            setDynamicBidFloorPrice(sdkAdRequest, paramMap);
            sdkAdRequest.setInterfaceType(interfaceType);
            if (paramMap.containsKey(BRAND_AD_GROUP_IDS)) {
                sdkAdRequest.setAttribute(BRAND_AD_GROUP_IDS, paramMap.get(BRAND_AD_GROUP_IDS));
            }
            // REALTIME_V1和REALTIME_V2支持全链路请求，且需要判断广告位是否支持全链路请求
            if (interfaceType.equals(GorgonInterfaceType.REALTIME_V1) || interfaceType.equals(GorgonInterfaceType.REALTIME_V2)) {
                sdkAdRequest.setFullChannel(Boolean.parseBoolean(paramMap.get(FULL_CHANNEL)));
                String groupId = paramMap.get(GROUP_ID);
                if (StringUtils.isNotBlank(groupId)) {
                    try {
                        sdkAdRequest.setAdGroupId(Long.parseLong(groupId));
                    } catch (Exception e) {
                        log.error("sdkAdRequest is {},GroupId is not a valid number: {}", sdkAdRequest, groupId);
                    }
                }
            }
        } catch (AdStateException e) {
            throw e;
        } catch (Exception e) {
            String reason = "unexpected error when create ad request, url=" +
                    HttpRequestUtils.getFullURL(httpServletRequest);
            throw new AdStateException(AdState.ERROR_CREATE_ADREQUEST, reason);
        }

        if (CollectionUtils.isNotEmpty(requestFixes)) {
            for (IRequestFix requestFix : requestFixes) {
                sdkAdRequest = requestFix.fix(sdkAdRequest, httpServletRequest);
            }
        }
        return sdkAdRequest;
    }

    private static boolean isVender(String isVender) {
        return "true".equals(isVender);
    }

    private void setKeyword(SdkAdRequest sdkAdRequest, Map<String, String> paramMap) {
        Map<String, String> keywordsMap = extractKeywords(sdkAdRequest);
        sdkAdRequest.setDictQueryKeyword(keywordsMap.get(DICT_QUERY_KEYWORD));
    }

    /**
     * PC广告位默认设置isrd=1，302跳转
     */
    private void setDefaultIsrdForPcSlot(SdkAdRequest sdkAdRequest) {
        SdkAdSlot sdkAdSlot = sdkAdRequest.getAdSlot();
        if (null != sdkAdSlot) {
            if (sdkAdSlot.getInstance().getDeviceType() == DataHolder.SdkSlot.DeviceType.PC &&
                    StringUtils.isBlank(sdkAdRequest.getStrategyAttribute(IS_URL_REDIRECT))) {
                sdkAdRequest.setStrategyAttribute(IS_URL_REDIRECT, "1");
            }
        }
    }

    /**
     * 设置cookie
     */
    private void setCookie(SdkAdRequest sdkAdRequest, HttpServletRequest httpServletRequest, Map<String, String> paramMap) {
        Cookie[] cookies = httpServletRequest.getCookies();
        if (cookies != null && cookies.length > 0) {
            for (Cookie c : cookies) {
                if (StringUtils.equals(c.getName(), COOKIE_FOR_USER_ID) && StringUtils.isNotBlank(c.getValue())) {
                    sdkAdRequest.setCookie(DigestUtils.md5Hex(c.getValue().toUpperCase()).toUpperCase());
                    sdkAdRequest.setLoginUserId(c.getValue());
                    return;
                }
            }
        }
        //若解析不到请求头中的COOKIE，则使用参数里的{#USER_ID_FOR_API}字段作为COOKIE
        String cookieForApi = paramMap.get(USER_ID_FOR_API);
        if (StringUtils.isNotBlank(cookieForApi)) {
            sdkAdRequest.setCookie(DigestUtils.md5Hex(cookieForApi.toUpperCase()).toUpperCase());
            sdkAdRequest.setLoginUserId(cookieForApi);
        }
    }

    private void setBanTarget(SdkAdRequest sdkAdRequest) {
        sdkAdRequest.setBanTarget(NO_TARGET_AD_VALUE.equals(sdkAdRequest.getAttribute(TARGET_AD)));
    }

    private void setYouthMode(SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        sdkAdRequest.setYouthMode(IS_YOUTH_MODE.equals(paramMap.get(YOUTH_MODE)));
    }

    private void setSupportMarketUrl(SdkAdRequest sdkAdRequest, Map<String, String> paramMap) {
        sdkAdRequest.setSupportMarketUrl(NumberUtils.toInt(paramMap.getOrDefault(SUPPORT_MARKET_URL, "0")));
    }

    private void setDictFields(SdkAdRequest sdkAdRequest, Map<String, String> paramMap) {
        sdkAdRequest.setDictChannel(paramMap.get(DICT_CHANNEL_ID));
        sdkAdRequest.setDictCommunity(paramMap.get(DICT_COMMUNITY_IDS));
        sdkAdRequest.setDictPostId(paramMap.get(DICT_POST_ID_REQ));
    }

    /**
     * 若广告位不存在对应的有效样式，或者有效样式列表为空，则抛出{@link AdState#NO_VALID_STYLE}异常。
     */
    private void validateStyleInfo(String slotId) throws AdStateException {
        ADSlotStyles styles = slot2ADStylesReadProxy.getData(slotId);
        if (styles == null || CollectionUtils.isEmpty(styles.getStyleIDs())) {
            throw new AdStateException(AdState.NO_VALID_STYLE, "No valid style for slot=" + slotId);
        }
    }

    private void setReferer(SdkAdRequest sdkAdRequest, HttpServletRequest httpServletRequest) {
        sdkAdRequest.setReferer(Objects.toString(httpServletRequest.getHeader(HttpHeaders.REFERER), ""));
    }

    /**
     * 设置请求是否启用安全链路，现指Https.
     *
     * @param sdkAdRequest
     */
    private void setIsSecure(SdkAdRequest sdkAdRequest, Map<String, String> paramMap) {
        String sdkVersion = paramMap.getOrDefault(SDK_VERSION, "");
        try {
            if (ANDROID == sdkAdRequest.getOs()) {
                if (StringUtils.isNotBlank(sdkVersion)) {
                    if (!VersionCompareUtil.lowerOrEqualsThanVersion(sdkVersion, ANDROID_SECURE_MAX_SDK_VERSION)) {
                        sdkAdRequest.setAttribute(IS_SECURE, IS_SECURE_TRUE);
                    }
                }
            }
        } catch (Exception e) {
            log.error("compare android sdk_version and reset secure param error. sdkVersion={},os={}", sdkVersion, sdkAdRequest.getOs());
        }
        sdkAdRequest.setSecure(IS_SECURE_TRUE.equals(sdkAdRequest.getAttribute(IS_SECURE)));
    }

    /**
     * 设置 user agent 信息，如果没有获取到，设置为空字符串
     *
     * @param sdkAdRequest       移动广告请求
     * @param httpServletRequest http servlet request
     */
    private void setUA(SdkAdRequest sdkAdRequest, HttpServletRequest httpServletRequest) {
        sdkAdRequest.setUa(StringUtils.defaultString(httpServletRequest.getHeader(USER_AGENT)));
    }

    /**
     * 设置 Magic Number
     *
     * @param sdkAdRequest 移动广告请求
     * @param paramMap     请求参数
     */
    private void setMagicNo(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        String magicNoStr = paramMap.get(MAGIC_NO);
        if (StringUtils.isNumeric(magicNoStr)) {
            try {
                int magicNo = Integer.parseInt(magicNoStr);
                sdkAdRequest.setMagicNo(magicNo);
            } catch (Exception e) {
                log.error(uniform(sdkAdRequest.getAdSlot().getSdkSlotUdid(), "bad MAGIC_NO format",
                        paramMap.toString()));
            }
        }
    }

    /**
     * 设置广告请求的来源
     *
     * @param sdkAdRequest 广告请求
     * @param paramMap     请求参数映射
     */
    private void setRequestFrom(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        String sdkVersion = paramMap.get(SDK_VERSION);
        if (StringUtils.isNotBlank(sdkVersion)) {
            if (hasJSSdkFlag(paramMap)) {
                sdkAdRequest.setRequestFrom(RequestFrom.JS_SDK);
            } else {
                SdkAppInfo appInfo = sdkAdRequest.getApp();
                int osType = appInfo.getOsType();
                switch (osType) {
                    case ANDROID_VALUE:
                        sdkAdRequest.setRequestFrom(RequestFrom.ANDROID_SDK);
                        break;
                    case IOS_VALUE:
                        sdkAdRequest.setRequestFrom(RequestFrom.IOS_SDK);
                        break;
                    default:
                        sdkAdRequest.setRequestFrom(RequestFrom.UNKNOWN);
                        log.warn(uniform(sdkAdRequest, "unknown os", appInfo.getId()));
                        break;
                }
            }
        } else {
            sdkAdRequest.setRequestFrom(RequestFrom.API);
        }
    }

    /**
     * 设置策略相关的信息
     *
     * @param sdkAdRequest 移动广告请求
     * @param sdkSlot      广告位
     * @throws Exception 设置信息失败
     */
    private void setStrategyInfo(SdkAdRequest sdkAdRequest, SdkAdSlot sdkSlot, Map<String, String> paramMap) throws Exception {
        setAdStyle(sdkAdRequest, sdkSlot.getSdkSlotUdid(), paramMap);
        setIsAllowDownloadType(sdkAdRequest);
        sdkAdRequest.setStrategyAttribute(FILTER_SIZE, String.valueOf(sdkSlot.getFilterSize()));
    }

    /**
     * 从 HttpServletRequest 请求中抽取出实际请求参数。
     *
     * @param servletRequest
     * @return 广告实际请求参数。
     * @throws Exception
     */
    @NotNull
    private Map<String, String> getRequestParameters(HttpServletRequest servletRequest) throws Exception {
        EncodeType encodeType = extractEncodeType(servletRequest);
        Map<String, String> finalParamMap = extractParamMapWithoutEncode(servletRequest);
        switch (encodeType) {
            case NO_ENCODE:
                break;
            case YD_ENCODE:
                String ydEncodeStr = servletRequest.getParameter(ENCODED_REQUEST_KEY);
                if (StringUtils.isNotBlank(ydEncodeStr)) {
                    String slotId = servletRequest.getParameter(SLOT_ID);
                    String decodeStr = decodeService.decodeYdEncode(ydEncodeStr, slotId);
                    Map<String, String> paramMap = HttpRequestUtils.queryString2Map(decodeStr);

                    //此种加密算法将广告位ID放到加密数据外，需要单独放回Map中。
                    if (StringUtils.isNotBlank(slotId)) {
                        paramMap.put(SLOT_ID, slotId);
                    }
                    // 删除加密后的参数
                    finalParamMap.remove(ENCODED_REQUEST_KEY);
                    // 优先使用加密后的参数
                    finalParamMap.putAll(paramMap);
                } else {
                    throw new AdStateException(AdState.WRONG_KEY, "s is null");
                }
                break;
            case YD_BASE64:
                String base64Str = servletRequest.getParameter(ENCODED_REQUEST_KEY);
                if (StringUtils.isNotBlank(base64Str)) {
                    String decodedStr = decodeService.decodeYdBase64(base64Str);
                    Map<String, String> paramMap = HttpRequestUtils.queryString2Map(decodedStr);
                    finalParamMap.remove(ENCODED_REQUEST_KEY);
                    // 优先使用加密后的参数
                    finalParamMap.putAll(paramMap);
                } else {
                    throw new AdStateException(AdState.WRONG_KEY, "s is null");
                }
                break;
            default:
                String reason = "get puzzled encode type, encodeType:" + encodeType.name();
                throw new AdStateException(AdState.INVALID_ENCODETYPE, reason);
        }
        return finalParamMap;
    }

    /**
     * 从请求中提取参数编码类型
     *
     * @param httpServletRequest http servlet 请求
     * @return 参数编码类型
     */
    private EncodeType extractEncodeType(HttpServletRequest httpServletRequest) {
        String encodeType = httpServletRequest.getParameter(ENCODED_TYPE_KEY);
        if (StringUtils.isBlank(encodeType) || StringUtils.equalsIgnoreCase(encodeType, "null")) {
            return EncodeType.NO_ENCODE;
        } else {
            if (StringUtils.isNumeric(encodeType)) {
                return EncodeType.parse(Integer.parseInt(encodeType));
            } else {
                return EncodeType.UNKNOWN;
            }
        }
    }

    /**
     * <p> 从 {@link HttpServletRequest} 中解析出请求的参数，并转化为 map 形式。 </p>
     *
     * @param servletRequest http servlet 请求
     * @return {paramKey: paramValue}
     */
    @NotNull
    private Map<String, String> extractParamMapWithoutEncode(HttpServletRequest servletRequest) {
        Map<String, String> parameters = new HashMap<>();
        Enumeration keys = servletRequest.getParameterNames();
        while (keys.hasMoreElements()) {
            String key = (String) keys.nextElement();
            String value = servletRequest.getParameter(key);
            if (StringUtils.isNotBlank(value)) {
                parameters.put(key.trim(), value.trim());
            }
        }
        return parameters.isEmpty() ? Collections.emptyMap() : parameters;
    }

    /**
     * 设置 SDK 广告请求的 bid id,用于追踪此次请求
     *
     * @param sdkAdRequest
     */
    private void setBid(SdkAdRequest sdkAdRequest) {
        sdkAdRequest.setBid(UUID.randomUUID().toString());
    }

    /**
     * 在策略信息中设置是否允许下载
     *
     * @param sdkAdRequest 移动广告请求
     * @throws JSONException
     */
    private void setIsAllowDownloadType(SdkAdRequest sdkAdRequest) throws JSONException {
        String slotID = sdkAdRequest.getAttribute(SLOT_ID);
        ADSlotStyles styles = slot2ADStylesReadProxy.getData(slotID);

        if (styles != null) {
            boolean allowDownload = false;

            List<Long> styleIDs = styles.getStyleIDs();
            if (CollectionUtils.isNotEmpty(styleIDs)) {
                for (Long id : styleIDs) {
                    ADStyle style = adSlotStyleReadProxy.getData(id);
                    if (style != null) {
                        AdStyleType styleType = AdStyleType.parse(style.getType());
                        if (styleType == DOWNLOAD || styleType == ALL) {
                            allowDownload = true;
                            break;
                        }
                    }
                }
            }

            if (allowDownload) {
                sdkAdRequest.setStrategyAttribute(IS_ALLOW_DOWNLOAD, "true");
            } else {
                sdkAdRequest.setStrategyAttribute(IS_ALLOW_DOWNLOAD, "false");
            }
        } else {
            log.error(uniform(sdkAdRequest, "slot has no styles"));
        }
    }

    /**
     * 如果请求中有参数 REQUEST_STYLE_NAME 或 REQUEST_STYLE_ID,那么广告只请求此类型的广告
     * 如果请求中没有 REQUEST_STYLE_NAME 和 REQUEST_STYLE_ID，则查看广告位中是否设置了fix position对应的广告位样式，以后的repeat位置依次重复以前样式
     *
     * @param sdkAdRequest 移动广告请求
     */
    private void setAdStyle(SdkAdRequest sdkAdRequest, String slotId, Map<String, String> paramMap) throws Exception {
        // first check request
        String preferredStyleIds = paramMap.getOrDefault(REQUEST_STYLE_ID, "");
        String preferredStyleName = paramMap.getOrDefault(REQUEST_STYLE_NAME, "");
        if (StringUtils.isNotBlank(preferredStyleIds) || StringUtils.isNotBlank(preferredStyleName)) {
            boolean findStyle = false;
            ADSlotStyles styles = slot2ADStylesReadProxy.getData(slotId);
            List<Long> styleIds = styles.getStyleIDs();

            if (StringUtils.isNotBlank(preferredStyleIds)) {
                String[] reqStyleIds = preferredStyleIds.split(",");
                for (String psi : reqStyleIds) {
                    long styleId = NumberUtils.toLong(psi.trim(), 0L);
                    if (styleIds.contains(styleId)) {
                        ADStyle style = adSlotStyleReadProxy.getData(styleId);
                        if (style != null) {
                            findStyle = true;
                            sdkAdRequest.getPreferredSchemaIds().add(style.getId());
                        } else {
                            log.error(uniform(sdkAdRequest, "can't find style by id", styleId));
                        }
                    } else {
                        appSideWarnLog.warn("slot {} can't find style by id {} in {}", slotId, styleId, styleIds);
                    }
                }
            }
            if (StringUtils.isNotBlank(preferredStyleName)) {
                for (Long styleId : styleIds) {
                    ADStyle style = adSlotStyleReadProxy.getData(styleId);
                    if (style != null && StringUtils.equals(style.getName(), preferredStyleName)) {
                        findStyle = true;
                        sdkAdRequest.getPreferredSchemaIds().add(style.getId());
                        break;
                    } else {
                        log.error(uniform(sdkAdRequest, "can't find style by preferred styleName", preferredStyleName));
                    }
                }
            }

            if (!findStyle) {
                String reason = uniform(sdkAdRequest, "can't find requested style name in slot-styles", sdkAdRequest.getIp());
                throw new AdStateException(AdState.DESIGNATED_STYLE_NAME_NOT_FOUND, reason);
            }
        } else {//check DB set
            SdkAdSlot slot = this.sdkAdSlotService.getByUdid(slotId);
            List<Long> styleIDs = slot.getStyleIDs();
            //如果设置了此项样式和广告位置必须一一对应
            if (CollectionUtils.isNotEmpty(styleIDs)) {
                if (sdkAdRequest.hasMagicNo()) {
                    int adReqSequence = sdkAdRequest.getMagicNo();
                    long styleId = styleIDs.get(adReqSequence % styleIDs.size());
                    sdkAdRequest.getPreferredSchemaIds().add(styleId);
                }
            }
        }
    }

    /**
     * 如果是本地宝经纬度转为SDK统一经纬度
     *
     * @param sdkAdRequest 移动广告请求
     */
    private void setNativeBabeLocation(SdkAdRequest sdkAdRequest) {
        String nativeBabeLongitude = sdkAdRequest.getAttribute(NATIVEBABE_LONGITUDE);
        String nativeBabeLatitude = sdkAdRequest.getAttribute(NATIVEBABE_LATITUDE);
        if (StringUtils.isNotBlank(nativeBabeLongitude) && StringUtils.isNotBlank(nativeBabeLatitude)) {
            sdkAdRequest.setAttribute(LOCATION, nativeBabeLongitude + "," + nativeBabeLatitude);
        }
    }

    /**
     * 设置设备信息，设置前 requestFrom, os 信息必须已经设置好
     *
     * @param sdkAdRequest 移动广告请求
     */
    private void setDeviceInfo(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        Bid.BidRequest.Device.OS os = sdkAdRequest.getOs();
        RequestFrom requestFrom = sdkAdRequest.getRequestFrom();
        // 清空无效的设备号id
        deviceInfoService.checkAndResetDeviceIds(paramMap);
        sdkAdRequest.setDeviceId(setUniversalDeviceId(paramMap, os, requestFrom));
        String imei = DeviceIdUtils.getIMEI(paramMap);
        sdkAdRequest.setImei(imei);
        sdkAdRequest.setImeimd5(DeviceIdUtils.getDeviceIdOrBlank(DeviceIdUtils.xidToMd5Upper(sdkAdRequest.getOriginalImei(), true), paramMap.getOrDefault(IMEI_MD5, "")));
        String originalIdfa = DeviceIdUtils.getIDFA(sdkAdRequest.getOriginalUdid(), os, requestFrom);
        sdkAdRequest.setIdfa(StringUtils.upperCase(originalIdfa));
        sdkAdRequest.setIdfaMd5(DeviceIdUtils.getDeviceIdOrBlank(DeviceIdUtils.xidToMd5Upper(originalIdfa, true), paramMap.getOrDefault(IDFA_MD5, "")));
        String androidId = DeviceIdUtils.getAUID(paramMap, os, requestFrom);
        sdkAdRequest.setAuid(androidId);
        sdkAdRequest.setAuidmd5(DeviceIdUtils.getDeviceIdOrBlank(DeviceIdUtils.xidToMd5Upper(androidId, false), paramMap.getOrDefault(AUID_MD5, "")));
        sdkAdRequest.setDimei(DeviceIdUtils.getDimei(paramMap));
        if (os == IOS) {
            sdkAdRequest.setCaids(DeviceIdUtils.getCaidList(paramMap));
            sdkAdRequest.setPaids(DeviceIdUtils.getPaids(paramMap));
            // osType=ios时aaid为阿里广告设备标识，android时为android advertising id
            sdkAdRequest.setAlid(paramMap.getOrDefault(AAID, ""));
            // 清空之前添加到Strategy中的aaid信息
            sdkAdRequest.getStrategyReqInfo().remove(AAID);
        } else {
            sdkAdRequest.setAaid(DeviceIdUtils.getAAID(paramMap, os, requestFrom));
        }
        sdkAdRequest.setWxInstalled(getWxInstalled(paramMap.getOrDefault(WX_INSTALLED, ""), os));
        setWxApiVer(sdkAdRequest, paramMap);
        sdkAdRequest.setOpenSdkVer(paramMap.getOrDefault(OPENSDK_VER, ""));
        sdkAdRequest.setOaid(DeviceIdUtils.getOAID(paramMap, os, requestFrom));
        // 根据第三方监测的要求，oaid需用原值进行MD5
        sdkAdRequest.setOaidMd5(DeviceIdUtils.getDeviceIdOrBlank(DeviceIdUtils.xidToMd5Upper(sdkAdRequest.getOriginalOaid(), true), paramMap.getOrDefault(OAID_MD5, "")));
        sdkAdRequest.setYdid(ydidMappingService.getYdid(paramMap));
        sdkAdRequest.setMobileDeviceType(paramMap.getOrDefault(MOBLIE_DEVICE_TYPE, ""));
        sdkAdRequest.setDeviceStartDuringTime(NumberUtils.toDouble(paramMap.getOrDefault(DEVICE_START_DURING_TIME, "-1.0")));
        sdkAdRequest.setDeviceStartTime(NumberUtils.toDouble(paramMap.getOrDefault(DEVICE_START_TIMESTAMP, "-1.0")));
        sdkAdRequest.setDeviceLanguage(paramMap.getOrDefault(DEVICE_LANGUAGE, ""));
        sdkAdRequest.setDeviceMachine(paramMap.getOrDefault(DEVICE_MACHINE, ""));
        sdkAdRequest.setDeviceUpdateTime(NumberUtils.toDouble(paramMap.getOrDefault(DEVICE_UPDATE_TIMESTAMP, "-1.0")));
        sdkAdRequest.setIdfv(paramMap.getOrDefault(IDFV, ""));
        sdkAdRequest.setIosAuth(NumberUtils.toInt(paramMap.getOrDefault(IOS_AUTH, "-1")));
        sdkAdRequest.setDeviceCpuNumber(NumberUtils.toInt(paramMap.getOrDefault(DEVICE_CPU_NUMBER, "-1")));
        sdkAdRequest.setBootMark(paramMap.getOrDefault(BOOT_MARK, ""));
        sdkAdRequest.setUpdateMark(paramMap.getOrDefault(UPDATE_MARK, ""));
        String appInstalled = paramMap.getOrDefault(APP_INSTALLED, "[]");
        List<String> appInstalledList = JSON.parseArray(appInstalled, String.class);
        sdkAdRequest.getAppInstalled().addAll(appInstalledList);
        setBrandDeviceId(sdkAdRequest, paramMap);
        sdkAdRequest.setMediaAge(NumberUtils.toInt(paramMap.getOrDefault(MEDIA_AGE, "-1")));
        sdkAdRequest.setMediaGender(NumberUtils.toInt(paramMap.getOrDefault(MEDIA_GENDER, "0")));
        sdkAdRequest.setMediaPortraitKeywords(paramMap.getOrDefault(MEDIA_PORTRAIT_KEYWORDS, ""));
        sdkAdRequest.setDsid(paramMap.getOrDefault(DSID, ""));
        sdkAdRequest.setPowerOnTime(NumberUtils.toLong(paramMap.getOrDefault(POWER_ONE_TIME, "-1")));
        sdkAdRequest.setImsi(paramMap.getOrDefault(IMSI, ""));
        sdkAdRequest.setRomVersion(paramMap.getOrDefault(ROM_VERSION, ""));
        sdkAdRequest.setSystemCompilingTime(NumberUtils.toDouble(paramMap.getOrDefault(SYSTEM_COMPILING_TIME, "-1")));
        supplyDeviceService.supplyDevice(sdkAdRequest);
    }

    private void setWxApiVer(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        String wxApiVer = paramMap.getOrDefault(WX_API_VER, null);
        if (wxApiVer != null) {
            try {
                sdkAdRequest.setWxApiVer(Long.parseLong(wxApiVer));
            } catch (NumberFormatException e) {
                log.warn(uniform(paramMap.get(SLOT_ID), "can not convert wxApiVer to long. wxApiVer str={}", wxApiVer));
            }
        }
    }

    private Boolean getWxInstalled(String wxInstalled, Bid.BidRequest.Device.OS os) {
        if (os == IOS) {
            return switch (wxInstalled.toLowerCase()) {
                case "1" -> Boolean.TRUE;
                case "0" -> Boolean.FALSE;
                default -> null;
            };
        } else {
            return switch (wxInstalled.toLowerCase()) {
                case "true" -> Boolean.TRUE;
                case "false" -> Boolean.FALSE;
                default -> null;
            };
        }
    }

    /**
     * 设定 YOUDAO DSP内部统一 deviceId
     *
     * @param requestFrom 请求来源
     * @param paramMap    请求参数
     * @return 统一设备 ID
     */
    private String setUniversalDeviceId(@NotNull Map<String, String> paramMap,
                                        @NotNull Bid.BidRequest.Device.OS os,
                                        @NotNull RequestFrom requestFrom) {
        Caid caid;
        String ydid = ydidMappingService.getYdid(paramMap);
        switch (requestFrom) {
            case API:
                caid = os == IOS ? DeviceIdUtils.getLatestCaid(paramMap) : new Caid();
                return DeviceIdUtils.getApiDeviceId(
                        paramMap.get(IMEI), paramMap.get(IMEI_MD5),
                        paramMap.get(OAID), paramMap.get(UDID),
                        paramMap.get(AUID_MD5), ydid,
                        paramMap.get(AAID), caid.getVal(), paramMap.get(AAID),
                        paramMap.get(DIMEI), paramMap.get(IDFV),
                        paramMap.get(IDFA_MD5), paramMap.get(OAID_MD5),
                        caid.getMd5Val(), os);
            case ANDROID_SDK:
                String androidSdkDeviceId = DeviceIdUtils.getAndroidSDKDeviceId(
                        paramMap.get(IMEI), paramMap.get(OAID),
                        paramMap.get(AUID), paramMap.get(UDID), ydid,
                        paramMap.get(DIMEI), paramMap.get(OAID_MD5));
                checkAndLog(androidSdkDeviceId,
                        requestFrom.name() + " device id is empty, slotId=" + paramMap.get(SLOT_ID));
                return androidSdkDeviceId;
            case IOS_SDK:
                caid = DeviceIdUtils.getLatestCaid(paramMap);
                String iosSdkDeviceId = DeviceIdUtils.getIOSSdkDeviceId(
                        paramMap.get(UDID), caid.getVal(),
                        paramMap.getOrDefault(AAID, ""), ydid,paramMap.get(DIMEI),
                        paramMap.get(IDFV), paramMap.get(IDFA_MD5), caid.getMd5Val());
                checkAndLog(iosSdkDeviceId,
                        requestFrom.name() + " device id is empty, slotId=" + paramMap.get(SLOT_ID));
                return iosSdkDeviceId;
            case JS_SDK:
                return DeviceIdUtils.getJsSDKDeviceId(paramMap.get(IMEI), paramMap.get(OAID),
                        paramMap.get(UDID), ydid);
            default:
                log.warn(uniform(paramMap.get(SLOT_ID), "unknown request from", requestFrom));
                return "";
        }
    }

    /**
     * 如果 item 为空，打印 WARN 日志
     *
     * @param item       被检查的内容
     * @param logMessage log 信息
     */
    private void checkAndLog(@NotNull String item, @NotNull String logMessage) {
        if (StringUtils.isBlank(item)) {
            appSideWarnLog.warn(logMessage);
        }
    }

    /**
     * 设置版本相关的信息
     *
     * @param sdkAdRequest 移动广告请求
     * @param paramMap     请求参数
     */
    private void setVersions(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        sdkAdRequest.setSdkVersion(paramMap.getOrDefault(SDK_VERSION, ""));
        sdkAdRequest.setOsVersion(paramMap.getOrDefault(OS_VERSION, ""));
        sdkAdRequest.setAppVersion(paramMap.getOrDefault(APP_VERSION, ""));
    }

    /**
     * 如果此广告位的样式中有任何一个为{@link ADStyle.OpStatus#AUDITING}
     * 或{@link ADStyle.OpStatus#DRAFT}，则为自测请求，需要返回自测物料。
     */
    private void setTest(SdkAdRequest sdkAdRequest, SdkAdSlot slot) {
        ADSlotStyles styles = slot2ADStylesReadProxy.getData(slot.getSdkSlotUdid());
        if (styles != null) {
            List<Long> styleIds = styles.getStyleIDs();
            for (Long styleId : styleIds) {
                ADStyle style = adSlotStyleReadProxy.getData(styleId);
                if (isTestDeviceId(sdkAdRequest, slot.getTestDeviceIds()) && isTestStyle(style)) {
                    sdkAdRequest.setTest(true);
                    return;
                }
            }
        } else {
            log.error(slot.getSdkSlotUdid() + " has no style");
            return;
        }

        sdkAdRequest.setTest(false);
    }

    /**
     * 设置是否获取测试品牌广告
     *
     * @param sdkAdRequest
     * @param slot
     */
    private void setBrandTest(SdkAdRequest sdkAdRequest, SdkAdSlot slot) {
        List<String> brandTestDeviceIds = slot.getBrandTestDeviceIds();
        if (!CollectionUtils.isEmpty(brandTestDeviceIds)
                && brandTestDeviceIds.contains(sdkAdRequest.getDeviceId())) {
            sdkAdRequest.setBrandTest(true);
        }
    }

    /**
     * @return 是否为请求自测广告设备。
     */
    private boolean isTestDeviceId(SdkAdRequest sdkAdRequest, List<String> testDeviceIdList) {
        return !CollectionUtils.isEmpty(testDeviceIdList) && testDeviceIdList.contains(sdkAdRequest.getDeviceId());
    }

    private boolean isTestStyle(ADStyle style) {
        return style != null && (style.getAuditStatus() == ADStyle.OpStatus.AUDITING
                || style.getAuditStatus() == ADStyle.OpStatus.DRAFT);
    }

    /**
     * 检查广告位状态，确定是否出广告。
     * <p>
     * 在SdkSlot表里有两个状态列：SDK_STATUS 和 SDK_OP_STATUS
     * 操作状态是开发者自己来改变的，比如开发者把广告位删除了；状态是审核那边改变的，广告位需要经过审核才是有效的
     * 1、如果opstatus是暂停或者删除状态，不出任何广告
     * 2、如果opstautus是有效，但status非审核通过，不出任何广告
     * 3、如果opstatus是新建状态，出自测广告。
     *
     * @param slot 广告位
     * @throws AdStateException 检查广告位状态失败
     */
    private void validateSlotStatus(SdkAdSlot slot) throws AdStateException {
        SdkSlotOptStatus slotOptStatus = SdkSlotOptStatus.parse(slot.getSdkOpStatus());
        switch (slotOptStatus) {
            case VALID:
                // 此时应该出线上广告
                SdkSlotStatus slotStatus = SdkSlotStatus.parse(slot.getSdkStatus());
                if (slotStatus == SdkSlotStatus.VALID) {
                    break;
                }
            case DELETED:
            case PAUSE:
            case UNKNOWN:
            default:
                appSideWarnLog.warn("check slot status failed, slot status: {}, slot udid: {}", slot.getSdkOpStatus(), slot.getSdkSlotUdid());
                throw new AdStateException(AdState.INVALID_SDK_STATUS, "check slot status failed.");
        }
    }

    /**
     * 设置地域信息, 包括 ip, 省份，城市
     *
     * @param sdkAdRequest       移动广告请求
     * @param httpServletRequest http servlet request
     * @param paramMap           请求参数
     */
    private void setLocationInfo(@NotNull SdkAdRequest sdkAdRequest, HttpServletRequest httpServletRequest, Map<String, String> paramMap) {
        String ip = setIp(sdkAdRequest, httpServletRequest, paramMap);
        if (StringUtils.isNotBlank(ip)) {
            SdkAdRequest.Location location = locate(ip);
            sdkAdRequest.setLocation(location);
        } else {
            sdkAdRequest.setLocation(SdkAdRequest.Location.DEFAULT);
        }
    }

    /**
     * 根据 ip 定位地域
     *
     * @param ip ip 地址
     * @return 地址信息
     */
    private SdkAdRequest.Location locate(@NotNull String ip) {
        AdvancedIpSearcher.LocationInfo locationInfo = AdvancedIpSearcher.getLocationInfoByIp(ip);
        int provinceId = locationInfo.getProvinceId();
        int cityId = locationInfo.getCityId();
        SdkAdRequest.Location location = new SdkAdRequest.Location();
        if (provinceId != -1) {
            location.setProvince(provinceId);
            location.setProvinceName(locationInfo.getProvince());
        }
        if (cityId != -1) {
            location.setCity(cityId);
            location.setCityName(locationInfo.getCity());
        }
        location.setCountry(locationInfo.getCountryId());
        location.setCountryName(locationInfo.getCountryName());
        location.setIsoCountryCode(locationInfo.getIsoCountryCode());

        return location;
    }

    /**
     * 添加ip信息。
     * 优先使用 rip 参数里指定的 ip, 若无此参数，使用 gorgon 前端 nginx 设置的
     * X-Forwarded-For 作为请求 ip。避免 gorgon 拿到的 ip 是内部的 nginx 的 ip。
     *
     * @param httpServletRequest http servlet 请求
     * @param sdkAdRequest       移动广告请求
     * @param paramMap           请求参数
     * @return ip 地址
     */
    @NotNull
    private String setIp(SdkAdRequest sdkAdRequest, HttpServletRequest httpServletRequest, Map<String, String> paramMap) {
        String requestSourceIp = HttpRequestUtils.getIp(httpServletRequest, "X-Forwarded-For");
        String rip = paramMap.get(REAL_IP);
        String ip = StringUtils.isNotBlank(rip) ? rip : requestSourceIp;
        String ipSource = StringUtils.isNotBlank(rip) ? "rip" : "header";

        if (StringUtils.isNotBlank(ip) && (IpUtils.isIPv4(ip) || IpUtils.isIPv6(ip))) {
            sdkAdRequest.setIp(ip);
            sdkAdRequest.setIpSource(ipSource);
            return ip;
        } else {
            appSideWarnLog.warn(uniform(sdkAdRequest, "wrong ip format", ip));
            return "";
        }
    }

    /**
     * 设置网络类型
     *
     * @param sdkAdRequest 移动广告请求
     * @param paramMap     请求参数
     */
    private static void setNetworkType(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        String netType = paramMap.get(NETWORK_TYPE);
        if (CT_WIFI.equals(netType)) {
            sdkAdRequest.setNetworkType(Bid.BidRequest.NetworkType.WIFI);
        } else if (CT_MOBILE.equals(netType)) {
            String mobileNetType = paramMap.get(DETAIL_NETWORK_TYPE);
            if (DCT_3G_SET.contains(mobileNetType)) {
                sdkAdRequest.setNetworkType(Bid.BidRequest.NetworkType.G3);
            } else if (DCT_4G.equals(mobileNetType)) {
                sdkAdRequest.setNetworkType(Bid.BidRequest.NetworkType.G4);
            } else if (DCT_5G.equals(mobileNetType)) {
                sdkAdRequest.setNetworkType(Bid.BidRequest.NetworkType.G5);
            } else {
                sdkAdRequest.setNetworkType(Bid.BidRequest.NetworkType.OTHER);
            }
        } else if (CT_ETHERNET.equals(netType)) {
            sdkAdRequest.setNetworkType(Bid.BidRequest.NetworkType.OTHER);
        } else if (CT_UNKNOWN.equals(netType)) {
            sdkAdRequest.setNetworkType(Bid.BidRequest.NetworkType.OTHER);
        } else {
            appSideWarnLog.warn(uniform(sdkAdRequest.getAdSlot().getSdkSlotUdid(),
                    "unknown network type",
                    paramMap.toString()));
            sdkAdRequest.setNetworkType(Bid.BidRequest.NetworkType.OTHER);
        }

    }

    /**
     * 保证顺序对sdkAdRequest赋值
     *
     * @param sdkAdRequest 移动广告请求
     * @param paramMap
     * @param sdkSlot      移动广告位
     */
    private void orderedSetOsTypeAndIsSecure(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap,
                                             @NotNull SdkAdSlot sdkSlot) {
        setOsType(sdkAdRequest, paramMap, sdkSlot);
        setIsSecure(sdkAdRequest, paramMap);
    }


    /**
     * 设置 Sdk 请求的操作系统类型
     *
     * @param sdkAdRequest 移动广告请求
     * @param sdkSlot      移动广告位
     */
    private void setOsType(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap,
                           @NotNull SdkAdSlot sdkSlot) {
        Bid.BidRequest.Device.OS os = UNKONWN;

        if (hasJSSdkFlag(paramMap)) {
            String webOS = paramMap.get(WEBOS);
            if (StringUtils.isNotBlank(webOS)) {
                try {
                    int webOsValue = Integer.parseInt(webOS);
                    if (SdkAdRequestValues.IOS_TYPE_SET.contains(webOsValue)) {
                        os = IOS;
                    } else if (SdkAdRequestValues.ANDROID_TYPE_SET.contains(webOsValue)) {
                        os = ANDROID;
                    } else if (SdkAdRequestValues.WP_TYPE_SET.contains(webOsValue)) {
                        os = WP;
                    }
                } catch (Exception e) {
                    log.error(uniform(sdkAdRequest, "wrong os type from js jsk", sdkAdRequest.getIp()));
                }
            } else {
                os = Bid.BidRequest.Device.OS.valueOf(sdkSlot.getSdkOsType());
            }
        } else {
            os = Bid.BidRequest.Device.OS.valueOf(sdkSlot.getSdkOsType());
        }
        sdkAdRequest.setOs(os);
    }

    /**
     * 判断请求中是否有 js sdk 的标记。
     *
     * @param paramMap 请求参数映射
     * @return 如果有 jssdk 标记，返回 true, 否则返回 false
     */
    private boolean hasJSSdkFlag(@NotNull Map<String, String> paramMap) {
        // 只有 jssdk 会传来 reqFrom=web
        String reqFromInParam = paramMap.get(SDK_REQUEST_FROM);
        return StringUtils.equals(SDK_REQUEST_FROM_WEB, reqFromInParam);
    }

    /**
     * 获取广告位的完整信息
     *
     * @param paramMap 请求参数
     * @return 如果获取成功，返回广告位的完整信息, 如果获取失败，返回 null
     */
    @NotNull
    private SdkAdSlot getSlot(@NotNull Map<String, String> paramMap) throws AdStateException {
        String slotId = paramMap.get(SLOT_ID);
        if (StringUtils.isNotBlank(slotId)) {
            SdkAdSlot sdkAdSlot = sdkAdSlotService.getByUdid(slotId);
            if (sdkAdSlot != null) {
                return sdkAdSlot;
            } else {
                throw new AdStateException(AdState.NOT_IN_CACHE, "slot not in cache. slotId = " + slotId);
            }
        } else {
            throw new AdStateException(AdState.NULL_SLOTID, "slot id is null");
        }
    }

    /**
     * 调用前提： sdkAdRequest 中 app 已设置
     * <p>
     * 设置 synd id，默认为 SYND_ID_SDK
     *
     * @param sdkAdRequest 移动广告请求
     */
    private void setSyndicationID(SdkAdRequest sdkAdRequest) {
        SdkAppInfo app = sdkAdRequest.getApp();
        if (app != null && isSyndicationIDLegal(app.getSynId())) {
            sdkAdRequest.setSyndId(app.getSynId());
        } else {
            sdkAdRequest.setSyndId(SYND_ID_SDK);
        }
    }

    private boolean isSyndicationIDLegal(int synId) {
        return synId == SYND_ID_SDK || synId == SYND_ID_NEX;
    }

    /**
     * 从 {@link SdkAdRequestAttributes#M_KEYWORDS} 中解析出参数。
     */
    private Map<String, String> extractKeywords(SdkAdRequest adRequest) {
        String keywords = adRequest.getAttribute(M_KEYWORDS);
        if (StringUtils.isBlank(keywords)) {
            return Collections.emptyMap();
        } else {
            Map<String, String> keywordsMap = new HashMap<>();

            String[] keywordItem = keywords.split("AND");
            for (String o : keywordItem) {
                String[] keywordTuple = o.split(":");
                if (keywordTuple.length == 2) {
                    String key = keywordTuple[0].trim();
                    String value = StringUtils.trim(keywordTuple[1]);
                    if (StringUtils.isNoneBlank(key, value)) {
                        // 客户端传错了参数，要把 q=m_words 换成 m_words, value 去除首尾引号
                        // MOD:因为安卓词典有可能传过来类似这样的数据 key:'value' AND key1:'value1'
                        // 所以这里把在list里的value都处理成不带单引号的
                        if ("q=m_words".equals(key)) {
                            key = DICT_QUERY_KEYWORD;
                        }
                        if (SPECIAL_KEYWORDS.contains(key)) {
                            value = value.replaceAll("^\'|\'$", "");
                        }
                        keywordsMap.put(key, value);
                    }
                }
            }
            return keywordsMap;
        }
    }

    /**
     * 将 Http 请求中的参数抽取出来，保存到 SdkAdRequest 的属性中。
     * <p>
     * 同时将定义在 {@link SdkAdRequestAttributes#STRATEGYREQINFO} 中的参数，保存到{@link SdkAdRequest#getStrategyReqInfo()}中。
     * 经过此方法后所有 Http 请求参数都已解析完毕。
     */
    private void putHttpParametersIntoSdkAdRequest(@NotNull SdkAdRequest adRequest,
                                                   @NotNull Map<String, String> params) {

        adRequest.getAttributes().putAll(params);

        for (String key : STRATEGY_PARAM_NAMES) {
            String value = params.get(key);
            if (value != null) {
                adRequest.setStrategyAttribute(key, value);
            }
        }

        //解析 SdkAdRequestAttributes#M_KEYWORDS 字段
        Map<String, String> keywordsMap = extractKeywords(adRequest);
        adRequest.getStrategyReqInfo().putAll(keywordsMap);
        adRequest.getAttributes().putAll(keywordsMap);
    }

    /**
     * 将各种形式的设备id均转为大写，进行格式上的统一。
     *
     * @param adRequest ad request
     * @param params    parameters map
     */
    private void resetIdFormat(@NotNull SdkAdRequest adRequest,
                               @NotNull Map<String, String> params) {
        DEVICE_IDS_SHOULD_BE_UPPERCASE.forEach(x -> {
            if (StringUtils.isNotBlank(adRequest.getAttribute(x))) {
                adRequest.setAttribute(x, adRequest.getAttribute(x).toUpperCase());
            }
        });

        DEVICE_IDS_SHOULD_BE_UPPERCASE.forEach(x -> {
            if (StringUtils.isNotBlank(params.get(x))) {
                params.put(x, params.get(x).toUpperCase());
            }
        });
    }

    @NotNull
    @Override
    public List<AdRequest> buildBrandRequest(SdkAdRequest sdkAdRequest, List<String> brandSlotIds) {
        List<AdRequest> adRequestList = new ArrayList<>();
        try {
            String slotUdid = sdkAdRequest.getAttribute(SLOT_ID);
            SdkAdSlot sdkSlot = sdkAdSlotService.getByUdid(slotUdid);
            if (sdkSlot == null) {
                log.error(uniform(slotUdid, "not slot id in cache"));
                return Collections.emptyList();
            }
            //品牌广告位轮播与brandSequence处理互斥
            if (CarouselServiceImpl.isCarouselByVersion(sdkAdRequest.getAdSlot(), sdkAdRequest.getAppVersion())) {
                for (String id : brandSlotIds) {
                    AdRequest adRequest = buildBrandAdRequest(sdkAdRequest, id);
                    if (sdkAdRequest.hasMagicNo()) {
                        adRequest.setAttribute(SdkAdRequestFactory.MAGIC_NO, String.valueOf(sdkAdRequest.getMagicNo()));
                    } else {
                        adRequest.setAttribute(SdkAdRequestFactory.MAGIC_NO, "0");
                    }
                    adRequestList.add(adRequest);
                }
                setBrandTestFlag(sdkAdRequest, adRequestList);
                return adRequestList;
            }

            boolean isFetchFromAllBrandPositions = sdkAdRequest.getAdSlot() != null && sdkAdRequest.getAdSlot().isNominateAdFromAllConfigBrandPositions();
            if (isFetchFromAllBrandPositions) {
                if (sdkAdRequest.hasMagicNo() && log.isDebugEnabled()) {
                    log.debug("slot {} fetch from all brand positions will ignore magic no {}", slotUdid, sdkAdRequest.getMagicNo());
                }
                for (String brandSlotId : brandSlotIds) {
                    adRequestList.add(buildBrandAdRequest(sdkAdRequest, brandSlotId));
                }
            } else {
                if (sdkAdRequest.hasMagicNo()) {
                    int magicNo = sdkAdRequest.getMagicNo();
                    int adCount = sdkAdRequest.getAdCount();
                    //the magic_no sequence should show brand ad prior
                    List<Integer> brandSequence = sdkSlot.getBrandAdSeqArray();
                    String brandSlotId;
                    for (int i = 0; i < brandSequence.size(); i++) {
                        if (magicNo <= brandSequence.get(i) && brandSequence.get(i) < magicNo + adCount) {
                            brandSlotId = brandSlotIds.get(i);
                            AdRequest brandAdReq = buildBrandAdRequest(sdkAdRequest, brandSlotId);
                            brandAdReq.setAttribute(SdkAdRequestFactory.MAGIC_NO, String.valueOf(brandSequence.get(i)));
                            adRequestList.add(brandAdReq);
                        }
                    }
                } else {
                    appSideWarnLog.warn(uniform(sdkAdRequest, "get null magic number when build brand AD request!",
                            sdkAdRequest.getIp()));
                    return Collections.emptyList();
                }
            }
        } catch (Exception e) {
            log.error(uniform(sdkAdRequest, "create Brand AD request failed", sdkAdRequest.getIp()), e);
        }
        setBrandTestFlag(sdkAdRequest, adRequestList);
        return adRequestList;
    }

    @NotNull
    private AdRequest buildBrandAdRequest(SdkAdRequest sdkAdRequest, String brandSlotId) {
        String ip = sdkAdRequest.getIp();
        AdRequest brandAdReq = new AdRequest();
        brandAdReq.setBidId(sdkAdRequest.getBid());
        brandAdReq.setAttribute(IS_SECURE, sdkAdRequest.isSecure() ? IS_SECURE_TRUE : IS_SECURE_FALSE);
        brandAdReq.getUserAction().Ip = ip;
        brandAdReq.setIpSource(sdkAdRequest.getIpSource());
        brandAdReq.loadRequestDatas();
        brandAdReq.setAttribute(AdRequestFactory.POSID, brandSlotId);
        brandAdReq.setAttribute(AdRequestFactory.IMEI, sdkAdRequest.getImei());
        brandAdReq.setAttribute(AdRequestFactory.IDFA, sdkAdRequest.getIdfa());
        brandAdReq.setSupplyImei(sdkAdRequest.getSupplyImei());
        brandAdReq.setSupplyIdfa(sdkAdRequest.getSupplyIdfa());
        brandAdReq.setAttribute(AdRequestFactory.AAID, StringHelper.getFirstNotBlank(sdkAdRequest.getAlid(), sdkAdRequest.getAaid()));
        brandAdReq.setAttribute(AdRequestFactory.AUID, sdkAdRequest.getAuid());
        brandAdReq.setAttribute(AdRequestFactory.OAID, sdkAdRequest.getOaid());
        brandAdReq.setSupplyOaid(sdkAdRequest.getSupplyOaid());
        brandAdReq.setCaids(sdkAdRequest.getCaids());
        brandAdReq.setPaids(sdkAdRequest.getPaids());
        brandAdReq.setAttribute(AdRequestFactory.DIMEI, sdkAdRequest.getDimei());


        //设置设备物理宽高和logo区高度，用于判断提取不同尺寸的广告位上的广告
        brandAdReq.setAttribute(AdRequestFactory.HEIGHT, sdkAdRequest.getAttribute(SCREEN_PHYSICAL_HEIGHT_PIXELS));
        brandAdReq.setAttribute(AdRequestFactory.WIDTH, sdkAdRequest.getAttribute(SCREEN_PHYSICAL_WIDTH_PIXELS));
        brandAdReq.setAttribute(AdRequestFactory.LOGO_HEIGHT, sdkAdRequest.getAttribute(LOGO_HEIGHT));
        brandAdReq.setAttribute(AdRequestFactory.VENDOR, sdkAdRequest.getAttribute(AdRequestFactory.VENDOR));
        brandAdReq.setAttribute(AdRequestFactory.DEVICE_NAME, sdkAdRequest.getDeviceName());
        brandAdReq.setVenderId(sdkAdRequest.getVenderId());
        brandAdReq.setVenderSource(sdkAdRequest.getVenderSource());
        brandAdReq.setVenderGroupIds(sdkAdRequest.getVenderGroupIds());
        brandAdReq.setAttribute(AdRequestFactory.SDK_SLOT_ID, sdkAdRequest.getAdSlot().getSdkSlotUdid());
        brandAdReq.setAttribute(AdRequestFactory.BRAND, sdkAdRequest.getBrand());
        brandAdReq.setAttribute(AdRequestFactory.MODEL,
                StringUtils.firstNonBlank(
                        sdkAdRequest.getAttribute(SdkAdRequestAttributes.DEVICE_NAME),
                        sdkAdRequest.getAttribute(SdkAdRequestAttributes.DEVICE_MACHINE)
                )
        );
        brandAdReq.setOs(sdkAdRequest.getOs());
        brandAdReq.setAttribute(AdRequestFactory.MID, sdkAdRequest.getOsVersion());

        //根据广告位设置品牌广告的syndid
        sdkAdRequest.getAdSlot().getBrandSyndId(brandSlotId)
                .ifPresent(brandAdReq::setSyndicationId);
        //设置请求的广告数量
        DictAdPositionInfo positionInfo = dictAdPositionInfoProvider.provideDictAdPositionInfo(
                (int) brandAdReq.getSyndicationId(), Integer.parseInt(brandSlotId), STRATEGY, API_VERSION);
        brandAdReq.setAdNum(BrandUtil.getBrandPosCarouselNum(sdkAdRequest));
        //根据客户端操作系统以及app版本构造eadd能识别的keyfrom
        String appVersion = sdkAdRequest.getAttribute(APP_VERSION);
        if (StringUtils.isNotEmpty(appVersion)) {
            brandAdReq.setAttribute(APP_VERSION, appVersion);
            String keyfrom = switch (sdkAdRequest.getOs()) {
                case ANDROID -> DICT_KEYFROM_PREFIX + appVersion + DICT_KEYFROM_SUFFIX_ANDROID;
                case IOS -> DICT_KEYFROM_PREFIX + appVersion + DICT_KEYFROM_SUFFIX_IOS;
                default -> DICT_KEYFROM_PREFIX + appVersion + sdkAdRequest.getOs().name();
            };
            brandAdReq.setAttribute(AdRequestFactory.KEYFROM, keyfrom);
        }
        //设置词典查询词，用来提取关键词广告，查询词在参数"q"中以键"m_words"存储
        Map<String, String> keywordsMap = extractKeywords(sdkAdRequest);
        String dictKeyword = keywordsMap.get(DICT_QUERY_KEYWORD);
        if (dictKeyword != null) {
            brandAdReq.setKeyword(dictKeyword);
        }
        brandAdReq.setYouthMode(sdkAdRequest.isYouthMode());
        brandAdReq.setCrowdUserFeature(sdkAdRequest.getCrowdUserFeature());
        brandAdReq.setSupplyCrowdUserFeature(sdkAdRequest.getSupplyCrowdUserFeature());
        brandAdReq.setLoginUserId(sdkAdRequest.getLoginUserId());
        brandAdReq.getAttributesMap().put(AdRequestFactory.NAV_HEIGHT, sdkAdRequest.getAttributes().getOrDefault(AdRequestFactory.NAV_HEIGHT, ""));
        brandAdReq.getAttributesMap().put(AdRequestFactory.SCREEN, sdkAdRequest.getAttributes().getOrDefault(AdRequestFactory.SCREEN, ""));
        brandAdReq.setRandomFeatureIdentify(sdkAdRequest.getRandomFeatureIdentify());
        brandAdReq.setBrandDeviceId(sdkAdRequest.getBrandDeviceId());
        brandAdReq.setUserAgent(sdkAdRequest.getUa());
        brandAdReq.setSupplyDeviceFlag(sdkAdRequest.isSupplyDeviceFlag());
        brandAdReq.setAttribute(AdRequestFactory.OS, String.valueOf(Optional.ofNullable(sdkAdRequest.getOs()).orElse(UNKONWN).getNumber()));
        //todo 频控测试用
        if (sdkAdRequest.getAttributes().containsKey(AdRequestFactory.MOCK_TIMESTAMP)) {
            brandAdReq.setAttribute(AdRequestFactory.MOCK_TIMESTAMP, sdkAdRequest.getAttribute(AdRequestFactory.MOCK_TIMESTAMP));
        }
        brandAdReq.setBrandFrequencyMap(sdkAdRequest.getBrandFrequencyMap());
        brandAdReq.setOpenSecondBrandNomination(sdkAdRequest.getAdSlot().getIsOpenSecondBrandNomination());
        brandAdReq.setSupplyIdfa(sdkAdRequest.getSupplyIdfa());
        brandAdReq.setSupplyOaid(sdkAdRequest.getSupplyOaid());
        brandAdReq.setSupplyImei(sdkAdRequest.getSupplyImei());
        brandAdReq.setImeiMd5(sdkAdRequest.getImeimd5());
        brandAdReq.setOaidMd5(sdkAdRequest.getOaidMd5());
        brandAdReq.setIdfaMd5(sdkAdRequest.getIdfaMd5());
        brandAdReq.setSupplyIdfaMd5(sdkAdRequest.getSupplyIdfaMd5());
        brandAdReq.setSupplyOaidMd5(sdkAdRequest.getSupplyOaidMd5());
        brandAdReq.setSupplyImeiMd5(sdkAdRequest.getSupplyImeiMd5());
        brandAdReq.setRandomAge(sdkAdRequest.getRandomAge());
        brandAdReq.setRandomGender(sdkAdRequest.getRandomGender());
        brandAdReq.setInterfaceType(sdkAdRequest.getInterfaceType());
        brandAdReq.setFullChannel(sdkAdRequest.isFullChannel());
        brandAdReq.setAdGroupId(sdkAdRequest.getAdGroupId());
        brandAdReq.setDarkIp(isDarkIp(sdkAdRequest));
        brandAdReq.setDarkDevice(isDarkDevice(sdkAdRequest));
        brandStatService.produceBrandPv(brandAdReq);
        return brandAdReq;
    }

    private boolean isDarkIp(SdkAdRequest sdkAdRequest) {
        return false;
    }

    private boolean isDarkDevice(SdkAdRequest sdkAdRequest) {
        Bid.BidRequest.Device.OS os = sdkAdRequest.getOs();
        if (os == IOS) {
            String idfa = sdkAdRequest.getIdfa();
            String idfaMd5 = sdkAdRequest.getIdfaMd5();
            //广协黑名单中没有caid的数据，因此caid的校验后续再加
            if (StringUtils.isNotEmpty(idfa)) {
                S3DeviceIdType.IDFA
            }
        } else {
            String imei = sdkAdRequest.getImei();
            String imeiMd5 = sdkAdRequest.getImeimd5();
            String oaid = sdkAdRequest.getOaid();
            String oaidMd5 = sdkAdRequest.getOaidMd5();
        }
        return false;
    }

    /**
     * 设置批量请求标识
     *
     * @param sdkAdRequest 移动广告请求
     * @param paramMap     请求参数映射
     */
    private void setMultiRequest(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        String multiReq = paramMap.get(MULTI_NATIVE);
        if (SdkAdRequestValues.MULTI_NATIVE_REQUEST_TAG.equals(multiReq)) {
            sdkAdRequest.setMultiRequest(true);
        } else {
            String adCountStr = paramMap.get(AD_COUNT);
            if (StringUtils.isNumeric(adCountStr)) {
                int adCount = Integer.parseInt(adCountStr.trim());
                if (adCount > 1) {
                    sdkAdRequest.setMultiRequest(true);
                }
            }
        }
    }

    /**
     * 设置请求广告数
     *
     * @param sdkAdRequest 移动广告请求
     * @param paramMap     请求参数映射
     */
    private void setRequestCount(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        String reqAdCountStr = paramMap.get(AD_COUNT);
        if (StringUtils.isNumeric(reqAdCountStr)) {
            int reqAdCount = Integer.parseInt(reqAdCountStr.trim());
            if (reqAdCount > MAX_AD_COUNT) {
                reqAdCount = MAX_AD_COUNT;
            }
            sdkAdRequest.setAdCount(reqAdCount);
            sdkAdRequest.setCpcAdCount(reqAdCount);
        } else {
            sdkAdRequest.setAdCount(1);
            sdkAdRequest.setCpcAdCount(1);
        }
    }

    /**
     * 如果是品牌测试请求，则给品牌广告请求设置测试请求的标识，用来判断是否出测试品牌广告
     *
     * @param sdkAdRequest
     * @param requestList
     */
    private void setBrandTestFlag(SdkAdRequest sdkAdRequest, List<AdRequest> requestList) {
        if (sdkAdRequest.isBrandTest() && !CollectionUtils.isEmpty(requestList)) {
            for (AdRequest request : requestList) {
                request.setTest(true);
            }
        }
    }

    /**
     * 解析userAgent，并保存下来
     *
     * @param sdkAdRequest
     */
    private void setUserAgentDto(SdkAdRequest sdkAdRequest) {
        UserAgentDto userAgentDto = parseUa(sdkAdRequest.getUa());
        sdkAdRequest.setUserAgentDto(userAgentDto);
    }

    /**
     * Set brand and model info for this request.
     * <p>
     * 优先级：
     * <ul>
     *     <li>首选SDK传来的dn参数{@link SdkAdRequestAttributes#DEVICE_NAME}解析出的brand/model；</li>
     *     <li>次选user-agent解析出的brand/model；</li>
     *     <li>最后使用api传来的dn参数解析出来的brand/model。</li>
     * </ul>
     * <p>
     * Note：
     * <p>
     *     该方法依赖有效的{@link SdkAdRequest#getRequestFrom()}、{@link SdkAdRequest#getUa()}和{@link SdkAdRequest#getOs()}值，
     *     所以需要放在{@link #setRequestFrom(SdkAdRequest, Map)}、{@link #setUA(SdkAdRequest, HttpServletRequest)}
     *     和{@link #setOsType(SdkAdRequest, Map, SdkAdSlot)}之后。
     * </p>
     *
     * @param request request
     */
    private void setBrandAndModel(SdkAdRequest request) {
        String dn = request.getDeviceName();
        AbstractMap.SimpleEntry<String, String> brandModelEntry = parseSdkDn(request, dn);
        if (isEntryBlank(brandModelEntry)) {
            UserAgentDto userAgentDto = request.getUserAgentDto();
            brandModelEntry = new AbstractMap.SimpleEntry<>(userAgentDto.getBrand(), userAgentDto.getModel());
            if (isEntryBlank(brandModelEntry)) {
                brandModelEntry = parseApiDn(request, dn);
            }
        }

        // set
        request.setBrand(getMobileBrandName(brandModelEntry.getKey(), brandModelEntry.getValue()));
        request.setModel(Objects.toString(brandModelEntry.getValue(), ""));
    }


    /**
     * 优先级: ①brandFromRequest中在枚举中的品牌 > ②modelFromRequest解析出在枚举中的品牌 > ③brand字段在扫描一遍手机品牌-机型映射表 > ④保留原设备brand > ④null
     * 根据请求中的设备品牌和设备型号识别出手机品牌
     * 手机品牌枚举参考 {@link MobileBrandEnum}
     *
     * @param brandFromRequest 请求中的手机品牌(数据可能不准确)
     * @param modelFromRequest 请求中的手机型号
     * @return 手机品牌
     */
    private String getMobileBrandName(String brandFromRequest, String modelFromRequest) {
        MobileBrandEnum brandEnum = MobileBrandEnum.getMobileBrandEnum(brandFromRequest);
        // ①优先使用请求中的品牌字段数据
        if (!MobileBrandEnum.UNKNOWN.equals(brandEnum)) {
            return brandEnum.name();
        }
        // ②从手机型号中解析手机品牌;
        String brandByModel = mobileModelProvider.getMobileBrand(modelFromRequest);
        if (StringUtils.isNotBlank(brandByModel) && !MobileBrandEnum.UNKNOWN.name().equals(brandByModel.toUpperCase())) {
            return brandByModel.toUpperCase();
        }
        // ③brand字段在扫描一遍手机品牌-机型映射表(应对dn/ua中传入参数不规范的情况,即brand中传入了model,而model为空的场景)
        String brandByBrand = mobileModelProvider.getMobileBrand(brandFromRequest);
        if (StringUtils.isNotBlank(brandByBrand) && !MobileBrandEnum.UNKNOWN.name().equals(brandByBrand.toUpperCase())) {
            return brandByBrand.toUpperCase();
        }

        return StringUtils.isNotBlank(brandFromRequest) ? brandFromRequest.toUpperCase() : "";
    }


    private boolean isEntryBlank(@Nonnull AbstractMap.SimpleEntry<String, String> entry) {
        return StringUtils.isBlank(entry.getKey()) || StringUtils.isBlank(entry.getValue());
    }

    private final AbstractMap.SimpleEntry<String, String> DEFAULT_BLANK_ENTRY = new AbstractMap.SimpleEntry<>("", "");

    @Nonnull
    private UserAgentDto parseUa(String ua) {
        // parse user-agent
        String brand = "";
        String model = "";
        String osv = "";
        String osName = "";
        if (StringUtils.isNotBlank(ua)) {
            io.gromit.uaparser.model.Device device = parser.parseDevice(ua);
            if (!UA_PARSE_DEFAULT.equals(device.family)) {
                brand = device.brand;
                model = device.model;
            }
            OS os = parser.parseOS(ua);
            osName = os.family;
            if (!UA_PARSE_DEFAULT.equals(osName)) {
                osv = String.join(".", os.major, os.minor, os.patch);
            }
        }
        log.debug("UA: {} -> Brand: {}, Model: {}", ua, brand, model);
        // replace "Generic_Android" if possible
        brand = resetBrandByModel(brand, model);
        return new UserAgentDto(osName, osv, brand, model);
    }

    private final String GENERIC_ANDROID = "Generic_Android";

    /**
     * If brand parsed from ua is {@link #GENERIC_ANDROID} and model is not null,
     * reset brand to what is searched by model from database.
     *
     * @param brand brand of the device
     * @param model model of the device
     * @return brand found by the model
     */
    private String resetBrandByModel(String brand, String model) {
        if (GENERIC_ANDROID.equals(brand) && model != null) {
            brand = devicePriceService.getBrandByModel(model);
        }
        return brand;
    }

    @Nonnull
    private AbstractMap.SimpleEntry<String, String> parseSdkDn(SdkAdRequest request, String dn) {
        if (request.getRequestFrom() != null) {
            switch (request.getRequestFrom()) {
                case ANDROID_SDK:
                    return parseAndroidDn(dn);
                case IOS_SDK:
                    return parseIosDn(dn);
                default:
                    return DEFAULT_BLANK_ENTRY;
            }
        }
        return DEFAULT_BLANK_ENTRY;
    }

    @Nonnull
    private AbstractMap.SimpleEntry<String, String> parseApiDn(SdkAdRequest request, String dn) {
        if (request.getRequestFrom() == RequestFrom.API && request.getOs() != null) {
            switch (request.getOs()) {
                case ANDROID:
                    return parseAndroidDn(dn);
                case IOS:
                    return parseIosDn(dn);
            }
        }
        return DEFAULT_BLANK_ENTRY;
    }

    /**
     * Android dn format: [Build.MANUFACTURER,Build.MODEL,Build.PRODUCT]
     *
     * @param dn android device name
     * @return brand and model pair
     */
    @Nonnull
    private AbstractMap.SimpleEntry<String, String> parseAndroidDn(String dn) {
        if (StringUtils.isNotBlank(dn)) {
            String[] infos = dn.trim().replaceAll(",$", "").split(",");
            String brand = (infos.length >= 1) ? infos[0].trim() : "";
            String model = (infos.length >= 2) ? infos[1].trim() : "";
            if (StringUtils.isBlank(brand)) {
                brand = androidBrandService.getBrand(model);
            }
            return new AbstractMap.SimpleEntry<>(brand, model);
        }
        return DEFAULT_BLANK_ENTRY;
    }

    /**
     * Get ios model from dn. The brand is always "Apple".
     *
     * @param dn ios device name
     * @return brand and model pair
     */
    @Nonnull
    private AbstractMap.SimpleEntry<String, String> parseIosDn(String dn) {
        final String APPLE = "Apple";
        String model = appleModelService.getModel(dn);
        if (!model.isEmpty()) {
            return new AbstractMap.SimpleEntry<>(APPLE, model);
        } else {
            return DEFAULT_BLANK_ENTRY;
        }
    }

    /**
     * 设置品牌广告使用的
     *
     * @param sdkAdRequest
     * @param paramMap
     */
    private void setBrandDeviceId(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        sdkAdRequest.setBrandDeviceId(BrandDeviceId.parseBrandDeviceIdFromSdk(sdkAdRequest, paramMap));
    }

    /**
     * 设置动态底价参数，只有在广告位支持动态底价前提下才会生效
     */
    private void setDynamicBidFloorPrice(@NotNull SdkAdRequest sdkAdRequest, @NotNull Map<String, String> paramMap) {
        if (sdkAdRequest.getAdSlot().getIsDynamicBidFloor()) {
            int dynamicBidFloor = NumberUtils.toInt(paramMap.get(DYNAMIC_BID_FLOOR), 0);
            // 若媒体传入的bidFloor参数合法，则设置动态底价参数
            if (dynamicBidFloor > 0) {
                sdkAdRequest.setDynamicBidFloor(dynamicBidFloor);
            } else {
                appSideWarnLog.warn("bidFloor is invalid! bidFloor: {}, slotId: {}", paramMap.get(DYNAMIC_BID_FLOOR), sdkAdRequest.getAdSlot().getSdkSlotUdid());
            }
        }

    }
}
