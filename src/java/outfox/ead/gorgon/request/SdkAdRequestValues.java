package outfox.ead.gorgon.request;

import outfox.ead.data.DEVICEOS;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static java.util.Collections.unmodifiableList;
import static java.util.Collections.unmodifiableSet;

/**
 * 移动广告请求的参数值
 * 
 * <AUTHOR>
 *         created on 2016/12/5
 */
public final class SdkAdRequestValues {
    public static final String CT_UNKNOWN = "0";

    public static final String CT_ETHERNET = "1";

    public static final String CT_WIFI = "2";

    public static final String CT_MOBILE = "3";

    public static final Set<String> DCT_3G_SET = unmodifiableSet(new HashSet<>(Arrays.asList(
            "6", "3", "5", "8", "9", "10", "12", "14", "15")));

    public static final String DCT_4G = "13";
    public static final String DCT_5G = "50";

    public static final List<Integer> ANDROID_TYPE_SET = unmodifiableList(Arrays.asList(
            DEVICEOS.ANDROID.getIndex(), DEVICEOS.ANDROIDTABLET.getIndex()));
    
    public static final List<Integer> IOS_TYPE_SET = unmodifiableList(Arrays.asList(
            DEVICEOS.IPHONE.getIndex(), DEVICEOS.MAC.getIndex(), DEVICEOS.IPAD.getIndex()));
    
    public static final List<Integer> WP_TYPE_SET = unmodifiableList(Arrays.asList(
            DEVICEOS.WINDOWS.getIndex(), DEVICEOS.WINPHONE.getIndex(), DEVICEOS.WINTABLET.getIndex()));
    /**
     * 表示批量广告的请求
     */
    public static final String MULTI_NATIVE_REQUEST_TAG = "1";

    public static final String SUPPORT_ULINK_TRUE = "1";
}
