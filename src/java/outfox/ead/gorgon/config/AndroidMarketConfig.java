package outfox.ead.gorgon.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import outfox.ead.gorgon.utils.CentralDogmaClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 安卓应用市场url-schema配置
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
@Log4j2
@Component
public class AndroidMarketConfig {
    @Resource
    private CentralDogmaClient centralDogmaClient;

    @Value("${central_dogma_gorgon_project}")
    private String cdProject;

    @Value("${central_dogma_gorgon_repo}")
    private String cdRepo;

    @Value("${android_market_cd_conf}")
    private String cdConfFile;

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Getter
    private List<AndroidMarketConfig.ConfigData> configList;

    @Getter
    private Map<Integer, Pattern> patternMap = new HashMap<>();

    @PostConstruct
    private void init() {
        centralDogmaClient.watchConfig(cdProject, cdRepo, cdConfFile, (revision, conf) -> reload(conf));
    }

    private void reload(String conf) {
        try {
            if (StringUtils.isEmpty(conf)) {
                log.warn("android_market config is empty!");
                return;
            }
            configList = objectMapper.readValue(conf, new TypeReference<List<AndroidMarketConfig.ConfigData>>() {
            });
            configList.sort(Comparator.comparingInt(ConfigData::getOrder));
            Map<Integer, Pattern> tmpPatternMap = new HashMap<>();
            for (ConfigData configData : configList) {
                tmpPatternMap.put(configData.order, Pattern.compile(configData.regex));
            }
            patternMap = tmpPatternMap;
            log.info("android_market config is: {}", conf);
        } catch (Exception e) {
            log.error("error update android_market config.", e);
        }
    }

    @Data
    public static class ConfigData {

        /**
         * 匹配的优先级
         */
        private int order;

        /**
         * user-agent中解析出的brand字段
         */
        private String regex;

        /**
         * 品牌列表
         */
        private List<String> equalList = new ArrayList<>();

        /**
         * 应用市场跳转链接
         */
        private String urlSchema;

        /**
         * 规则生效的最小sdk版本号，为空则，所有版本都生效
         */
        private String minSdkVersion;
    }
}
