package outfox.ead.gorgon.service.filter;

import org.apache.commons.lang3.StringUtils;
import outfox.ead.adnet.dsp.rpc.bs.nominate.BrandAdNominator;
import outfox.ead.brand.model.Candidate;
import outfox.ead.brand.model.request.AdRequest;
import outfox.ead.brand.service.AdRequestFactory;
import outfox.ead.data.BrandConstants;
import outfox.ead.gorgon.utils.VersionCompareUtil;

import javax.annotation.Resource;

/**
 * 媒体层级的最低版本号过滤：https://confluence.inner.youdao.com/x/PT3UFg
 *
 * <AUTHOR>
 * @create 2025-06-30 11:04
 **/
public class BrandVersionFilter implements BrandFilter {
    @Resource
    private BrandAdNominator brandAdNominator;

    @Override
    public boolean filter(AdRequest adRequest, Candidate candidate) {
        if (!candidate.isVersionFilter()) {
            return false;
        }

        String minVersion = brandAdNominator.getMinVersion(candidate.getAdGroupId(),
                Long.parseLong(StringUtils.defaultIfBlank(candidate.getAdItemProperty(BrandConstants.MEDIA_ID), "0")));
        String appVersion = adRequest.getAttributeOrDefault(AdRequestFactory.APP_VERSION, "");
        if (StringUtils.isBlank(minVersion) || StringUtils.isBlank(appVersion)) {
            return false;
        }
        //app版本号<最小版本号 则过滤
        return VersionCompareUtil.compareVersion(appVersion, minVersion) < 0;
    }

    @Override
    public boolean filter(AdRequest adRequest, Candidate candidate, long scheduleStartTime, long scheduleEndTime) {
        return filter(adRequest, candidate);
    }
}
