package outfox.ead.gorgon.render;

import org.apache.commons.lang3.StringUtils;
import outfox.ead.adnet.dsp.rpc.bs.convert.SdkAttributeTag;
import outfox.ead.brand.model.AdText;
import outfox.ead.brand.model.Candidate;
import outfox.ead.brand.model.request.AdRequest;
import outfox.ead.brand.service.AdRequestFactory;
import outfox.ead.brand.util.BrandUtil;
import outfox.ead.data.AdItem;
import outfox.ead.data.BrandConstants;
import outfox.ead.dsp.protocol.youdao.TrackerCommonDimension;
import outfox.ead.gorgon.request.SdkAdRequest;

import java.util.Objects;

import static outfox.ead.brand.service.AdRequestFactory.*;
import static outfox.ead.brand.util.BrandUtil.WECHAT_ORIGIN_ID;

public class BrandTrackerDimensionUtils {
    public static TrackerCommonDimension.BrandTrackerDimension generateExtCommonDim(SdkAdRequest sdkAdRequest, Candidate candidate, String appName, String environment) {
        return TrackerCommonDimension.BrandTrackerDimension.newBuilder()
                .setBidId(sdkAdRequest.getBid())
                .setEnv(environment)
                .setGuid(java.util.UUID.randomUUID().toString())
                .setSponsorId(candidate.getSponsorId())
                .setCampaignId(candidate.getCampaignId())
                .setAdGroupId(candidate.getAdGroupId())
                .setAdVariationId(candidate.getAdVariationId())
                .setImprPos(candidate.getImprPos())
                .setImprIp(Objects.toString(candidate.getImprIp(), ""))
                .setProvince(Integer.parseInt(candidate.getLocalProperty(AdRequestFactory.AREA_ID, "-1")))
                .setCity(Integer.parseInt(candidate.getLocalProperty(AdRequestFactory.CITY_ID, "-1")))
                .setMediaId(Integer.parseInt(candidate.getAdItemProperty(BrandConstants.MEDIA_ID, "0")))
                .setStyleId(Integer.parseInt(candidate.getAdItemProperty(BrandConstants.STYLE_ID, "-1")))
                .setKeyFrom(sdkAdRequest.getKeyFrom())
                .setVendor(candidate.getLocalProperty(AdRequestFactory.VENDOR, ""))
                .setVenderId(candidate.getLocalProperty(AdRequestFactory.VENDER_ID, ""))
                .setVenderSource(candidate.getLocalProperty(AdRequestFactory.VENDER_SOURCE, ""))
                .setSupplyDeviceFlag(Boolean.parseBoolean(candidate.getLocalProperty(AdRequestFactory.SUPPLY_DEVICE_FLAG, "false")))
                .setIdfa(sdkAdRequest.getIdfa())
                .setCaid(TrackerCommonDimension.CAID.newBuilder().setValue(sdkAdRequest.getLatestCaid().getVal()).setVersion(sdkAdRequest.getLatestCaid().getVersion()))
                .setAaid(sdkAdRequest.getAaid())
                .setImei(sdkAdRequest.getImei())
                .setDimei(sdkAdRequest.getDimei())
                .setOaid(sdkAdRequest.getOaid())
                .setDeeplinkApp(sdkAdRequest.getDimei())
                .setSupplyImei(sdkAdRequest.getSupplyImei())
                .setSupplyOaid(sdkAdRequest.getSupplyOaid())
                .setSupplyIdfa(sdkAdRequest.getSupplyIdfa())
                .setDeeplinkApp(Objects.toString(appName, ""))
                .setWechatOriginId(Objects.toString(StringUtils.firstNonBlank(candidate.getLocalProperty(WECHAT_ORIGIN_ID), (String) candidate.getContext().get(SdkAttributeTag.WECHAT_ORIGIN_ID)), ""))
                .build();
    }

    public static TrackerCommonDimension.BrandTrackerDimension generateExtCommonDim(AdText adText, AdRequest adRequest, String appName, String environment) {
        return TrackerCommonDimension.BrandTrackerDimension.newBuilder()
                .setBidId(adRequest.getBidId())
                .setEnv(environment)
                .setGuid(java.util.UUID.randomUUID().toString())
                .setSponsorId(Long.parseLong(adText.getSponsor()))
                .setCampaignId(Long.parseLong(Objects.toString(adText.getAttribute(BrandUtil.ADCAMPAIGN_ID), "0")))
                .setAdGroupId(Long.parseLong(Objects.toString(adText.getAttribute(BrandUtil.ADGROUP_ID), "0")))
                .setAdVariationId(Long.parseLong(Objects.toString(adText.getAttribute(BrandUtil.ADV_ID), "0")))
                .setImprPos(Integer.parseInt(Objects.toString(adText.getAttribute(BrandUtil.POSID), "0")))
                .setImprIp(Objects.toString(adRequest.getImprIp(), ""))
                .setProvince(adRequest.getAreaId())
                .setCity(adRequest.getCityId())
                .setMediaId(Long.parseLong(Objects.toString(adText.getAttribute(BrandConstants.MEDIA_ID), "0")))
                .setStyleId(Long.parseLong(Objects.toString(adText.getAttribute(BrandConstants.STYLE_ID), "0")))
                .setVenderId(Objects.toString(adRequest.getVenderId(), ""))
                .setVenderSource(Objects.toString(adRequest.getVenderSource(), ""))
                .setKeyFrom(Objects.toString(adRequest.getAttribute(KEYFROM), ""))
                .setVendor(Objects.toString(adRequest.getAttribute(VENDOR), ""))
                .setIdfa(Objects.toString(adRequest.getAttribute(IDFA), ""))
                .setCaid(TrackerCommonDimension.CAID.newBuilder().setValue(adRequest.getLatestCaid().getVal()).setVersion(adRequest.getLatestCaid().getVersion()))
                .setAaid(Objects.toString(adRequest.getAttribute(AAID), ""))
                .setImei(Objects.toString(adRequest.getAttribute(IMEI), ""))
                .setOaid(Objects.toString(adRequest.getAttribute(OAID), ""))
                .setDimei(Objects.toString(adRequest.getAttribute(DIMEI), ""))
                .setSupplyDeviceFlag(Boolean.parseBoolean(Objects.toString(adText.getAttribute(AdRequestFactory.SUPPLY_DEVICE_FLAG), "false")))
                .setDeeplinkApp(Objects.toString(appName, ""))
                .setWechatOriginId(Objects.toString(adText.getAttribute(WECHAT_ORIGIN_ID), ""))
                .build();

    }
}
