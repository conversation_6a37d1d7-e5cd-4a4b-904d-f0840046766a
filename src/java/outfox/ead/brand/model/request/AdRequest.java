/*
 * @(#)AdRequest.java, 2012-6-4.
 * <p>
 * Copyright 2012 Youdao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.brand.model.request;

import com.youdao.quipu.avro.schema.GorgonInterfaceType;
import lombok.Getter;
import lombok.Setter;
import odis.app.data.DocID;
import odis.serialize.IWritable;
import odis.serialize.lib.StringWritable;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import outfox.ead.brand.model.UserAndAction;
import outfox.ead.brand.service.AdRequestFactory;
import outfox.ead.brand.util.MachineUtil;
import outfox.ead.brand.util.area.AdvancedIpSearcher;
import outfox.ead.brand.util.area.NewIpSearcher;
import outfox.ead.brand.util.http.HttpRequestHelper;
import outfox.ead.brand.util.log.LogMessager;
import outfox.ead.brand.util.qa.Query;
import outfox.ead.data.*;
import outfox.ead.dsp.protocol.youdao.Bid;
import outfox.ead.gorgon.request.userfeature.CrowdUserFeature;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import static outfox.ead.brand.service.AdRequestFactory.*;


/**
 * 广告请求
 *
 * <AUTHOR>
 * It's properties are set in {@link AdRequestFactory#create(HttpServletRequest,
 * HttpServletResponse)}, or in QueryAnalyzer.
 */
public class AdRequest extends LogMessager implements IWritable, Serializable {

    private static AtomicLong counter = new AtomicLong(0L);

    private static final long serialVersionUID = 3362690966120299056L;

    private static final Log LOG = LogFactory.getLog(AdRequest.class);

    /**
     * attrs map key, 唯一的请求id
     */
    public static final String AD_REQUEST_ID = "reqid";

    /**
     * attrs map key, 样式id
     */
    public static final String STYLE_ID = "styleid";

    // 用户以及用户的动作（包括用户所在的IP，动作发生的时间等）
    private UserAndAction userAction;

    // ip字段来源，rip or header
    private String ipSource;

    // 联盟ID和站点ID可能需要封装到另一个类中，并额外增加referer等信息，表示广告请求的来源
    private long syndicationId = 0;

    // 广告的codeId
    private long codeId = 0;

    // 通过SiteManager生成出来
    private int adNum;

    // 与线上服务挂接，以下三个属性与RRF无关
    private String userAgent;

    private String referer;

    private WritableMap attrs = new WritableMap();

    /**
     * 用来保存不需传到展示的数据
     */
    @Getter
    private Map<Object, Object> volatileMap = new HashMap<Object, Object>();

    // 调试用，指定要把请求发给哪个服务器
    private String serverName = null;

    // 算法标识符（原名siteid）
    private long matchId = 0;

    // 创建时建立请求的时间戳
    @Setter // for unit test
    private long timestamp = 0;

    // 请求的地域Id。未设置==Integer.MIN_VALUE，设置出错==38
    @Getter
    @Setter
    private int areaId = Integer.MIN_VALUE;
    public static final int OTHER_AREA_ID = 38;
    @Getter
    transient private String area;

    // 请求的地域Id（市一级）.未设置==Integer.MIN_VALUE，设置出错==581
    @Getter
    @Setter
    private int cityId = Integer.MIN_VALUE;
    public static final int OTHER_CITY_ID = 581;
    @Getter
    transient private String city;

    protected final String SIMPLE_NAME = this.getClass().getSimpleName();

    @Getter
    private String countryMcc = NewIpSearcher.UNKNOW_COUNTRY_MCC;

    /**
     * 当前日期为星期几，周日为1，周一为二，以此类推。主要为展示端验证投放计划用，不序列化
     */
    @Getter
    private int dayOfWeek;

    /**
     * 当前时间的小时值，0－23之间。主要为展示端验证投放计划用，不序列化
     */
    @Getter
    private int hourOfDay;

    /**
     * 用来计算当前时间的线程私有Canlendar
     */
    private final static ThreadLocal<Calendar> threadLocalCalendar = new ThreadLocal<Calendar>() {
        @Override
        protected Calendar initialValue() {
            // 依赖于Calendar.getInstance()返回的是新对象
            return Calendar.getInstance();
        }
    };

    private transient Query query;

    /**
     * The map of optional attributes for internal usage (different from
     * {@link #attrs}).Values of any type can be put into map for subsequent
     * usage.
     */
    transient Map<String, Object> optionalAttributeMap;

    /**
     * 根据请求的ip获得的详细地域信息，如"福建省泉州市惠安县"
     */
    private String detailArea;

    @Getter
    transient private String slotSize;

    @Getter
    transient private String adSize;

    /**
     * 上次返回给客户端的广告id
     */
    @Getter
    @Setter
    private String lastBrandId;

    /**
     * 客户端查询的关键词
     */
    @Getter
    @Setter
    private String keyword;

    /**
     * 是否从多个广告位取广告
     */
    @Getter
    @Setter
    private boolean isFetchFromSeveralSlot;

    /**
     * 从多个广告位取广告的广告位列表
     */
    @Getter
    @Setter
    private List<String> multiPosidList = new ArrayList<String>();

    /**
     * 是否是一次测试请求，如果是则可以出测试广告
     */
    @Setter
    @Getter
    private boolean isTest;

    /**
     * gorgon 生成的品牌广告请求唯一标识，可用于跟踪pv bid impr 等过程
     */
    @Setter
    @Getter
    private String bidId;

    /**
     * 是否过滤合并广告位后过时样式的广告
     */
    @Setter
    @Getter
    private boolean filterDeprecatedStyle;

    /**
     * 当前请求需要弃用的广告位id
     */
    @Setter
    @Getter
    private List<String> deprecatedStyleIds;
    /**
     * 青少年模式（App上的逻辑除了判断是否青少年模式外，还会判断指定模块是否需要进行广告屏蔽）
     */
    @Setter
    @Getter
    private boolean youthMode;

    /**
     * 词典登录用户的唯一id，从cookie中解析
     */
    @Setter
    @Getter
    private String loginUserId;

    @Setter
    @Getter
    private CrowdUserFeature crowdUserFeature = new CrowdUserFeature();

    @Setter
    @Getter
    private CrowdUserFeature supplyCrowdUserFeature = new CrowdUserFeature();

    @Getter
    private List<String> appInstalled = new ArrayList<>(0);

    /**
     * 用于随机生成品牌广告用户特征的标识字段，由设备号/设备信息等内容产生
     */
    @Setter
    @Getter
    private String randomFeatureIdentify;

    /**
     * 用于品牌广告获取用户特征的设备号id
     */
    @Getter
    @Setter
    private BrandDeviceId brandDeviceId = BrandDeviceId.emptyBrandDeviceId();

    @Getter
    @Setter
    private Bid.BidRequest.Device.OS os;
    /**
     * 是否从设备库补充的设备id
     */
    @Getter
    @Setter
    private boolean supplyDeviceFlag;

    /**
     * 优选广告计划id -> （时间窗口 -> 此窗口展示次数）
     */
    @Getter
    @Setter
    private Map<Long, Map<Integer,Integer>> brandFrequencyMap;

    /**
     * 联合频控id -> （时间窗口 -> 此窗口展示次数）
     * e.g. 131231 -> (1 -> 3)
     */
    @Getter
    @Setter
    private Map<Long, Map<Integer,Integer>> unionFrequencyRuleMap;

    /**
     * 是否已经获取到了此设备的品牌广告展示次数信息，true：已获取到，false：尚未获取
     */
    @Getter
    @Setter
    private boolean brandFrequencyFlag = false;

    /**
     * 品牌广告DSP退量后，该广告位是否开启了第二次品牌广告提名
     */
    @Getter
    @Setter
    boolean openSecondBrandNomination = false;

    @Setter
    @Getter
    private List<Caid> caids = new ArrayList<>();

    @Setter
    @Getter
    private Set<Paid> paids = new HashSet<>();
    @Setter
    @Getter
    private String traceParam = "";

    @Setter
    @Getter
    private String traceIp = "";

    @Setter
    @Getter
    private String imeiMd5;

    @Setter
    @Getter
    private String supplyImei;

    @Setter
    @Getter
    private String supplyImeiMd5;

    @Setter
    @Getter
    private String idfaMd5;

    @Setter
    @Getter
    private String supplyIdfa;

    @Setter
    @Getter
    private String supplyIdfaMd5;

    @Setter
    @Getter
    private String oaidMd5;

    @Setter
    @Getter
    private String supplyOaid;

    @Setter
    @Getter
    private String supplyOaidMd5;

    /**
     * 随机年龄
     */
    @Setter
    @Getter
    private int randomAge;

    /**
     * 随机性别,1-男，2-女
     */
    @Setter
    @Getter
    private String randomGender;

    /**
     * 本次请求的来源接口
     */
    @Setter
    @Getter
    private GorgonInterfaceType interfaceType = GorgonInterfaceType.UNKNOWN;


    /**
     * 是否是全链路请求
     */
    @Setter
    @Getter
    private boolean fullChannel;

    /**
     * 全链路请求的推广组id
     */
    @Setter
    @Getter
    private long adGroupId;

    @Getter
    @Setter
    private boolean hasFullChannelAdItem;

    @Getter
    @Setter
    private String venderId;

    @Getter
    @Setter
    private String venderSource;

    @Getter
    @Setter
    private List<Long> venderGroupIds;


    public Caid getLatestCaid() {
        if (CollectionUtils.isNotEmpty(caids)) {
            return caids.get(0);
        }
        return new Caid();
    }

    /**
     * 获取版本第二新的caid
     * @return
     */
    public Caid getSecondLateCaid() {
        if (CollectionUtils.size(caids) > 1) {
            return caids.get(1);
        }
        return new Caid();
    }

    /**
     *
     * @param <T>
     *            type of attribute value
     * @param key
     *            attribute key
     * @param value
     *            attribute value
     */
    public <T> void setOptionalAttribute(String key, T value) {
        if (optionalAttributeMap == null) {
            optionalAttributeMap = new ConcurrentHashMap<String, Object>();
        }
        optionalAttributeMap.put(key, value);
    }

    /**
     * Get the specified Optional attribute's value.
     *
     * @param <T>
     *            type of attribute value
     * @param key
     *            attribute key
     * @return attribute value, or null if not found
     */
    @SuppressWarnings("unchecked")
    public <T> T getOptionalAttribute(String key) {
        if (optionalAttributeMap != null) {
            return (T) optionalAttributeMap.get(key);
        }
        return null;
    }

    /**
     * Clear all Optional attributes.
     */
    public void clearOptionalAttributes() {
        if (optionalAttributeMap != null) {
            optionalAttributeMap.clear();
        }
    }

    public AdRequest(UserAndAction ua, long syndId, long codeId) {
        this.userAction = ua;
        this.syndicationId = syndId;
        this.codeId = codeId;
        this.bidId = java.util.UUID.randomUUID().toString();
    }

    public AdRequest() {
        userAction = new UserAndAction();
        this.bidId = java.util.UUID.randomUUID().toString();
    }

    public AdRequest(String request, long syndicationId, long siteId,
                     String ip, int adNum, long time) {
        this();
        this.userAction.Object = request;
        this.syndicationId = syndicationId;
        this.matchId = siteId;
        this.userAction.Ip = ip;
        this.adNum = adNum;
        this.userAction.Time = time;
    }

    public WritableMap getAttributesMap() {
        return attrs;
    }

    /**
     * 判断当前广告请求是否为vender请求，三个参数任意一个不为空即可
     *
     * @return
     */
    public boolean isVenderRequest() {;
        return !StringUtils.isAllBlank(venderSource, venderId);
    }

    @Override
    public String toString() {
        return SIMPLE_NAME + "[" + this.userAction
                + ", syndId=" + this.syndicationId + ", codeId=" + this.codeId
                + ", matchId=" + this.matchId + ", adNum=" + this.adNum
                + ", attrs=" + getAttrsRepr() + "]";
    }

    private String getAttrsRepr() {
        if (this.attrs == null) {
            return "null";
        }
        List<String> keys = new ArrayList<String>();
        keys.addAll(this.attrs.keySet());
        Collections.sort(keys);
        StringBuffer buffer = new StringBuffer();
        buffer.append("{");
        List<String> segments = new ArrayList<String>();
        for (String key : keys) {
            segments.add(key + "=" + attrs.get(key));
        }
        buffer.append(StringUtils.join(segments, ", "));
        buffer.append("}");
        return buffer.toString();
    }

    public long getRequestID() {
        return DocID.getDocID(this.getUserAction().toString()).get();
    }

    /**
     * 给请求盖戳
     */
    public void stamp() {
        if (attrs.containsKey(AdRequestFactory.USE_SETTED_TIME)) {
            timestamp = userAction.Time;
        } else {
            timestamp = System.currentTimeMillis();
        }

        Calendar calendar = threadLocalCalendar.get();
        calendar.setTimeInMillis(timestamp);
        dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        dayOfWeek = (dayOfWeek + 5) % 7;
        hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);

    }

    /**
     * should invoke stamp() before this.
     */
    public void genAdRequestId(int port) {
        String sb = MachineUtil.getSimpleHostName() +
                "_" +
                port +
                "_" +
                Thread.currentThread().getId() +
                "_" +
                timestamp +
                "_" +
                counter.getAndIncrement();
        setAttribute(AD_REQUEST_ID, sb);
    }

    /**
     * get attribute
     */
    public String getAttribute(String key) {
        return this.getAttributesMap().get(key);
    }

    public String getAttributeOrDefault(String key, String defaultValue) {
        return this.getAttributesMap().getOrDefault(key, defaultValue);
    }

    /**
     * set attribute
     */
    public void setAttribute(String key, String value) {
        this.getAttributesMap().put(key, value);
    }

    public String toHttpParam() {
        String tmp = "";
        if (userAction.Object != null) {
            tmp = "req=" + this.userAction.Object;
        }
        tmp += "&adnum=" + this.adNum + "&syndid=" + this.syndicationId
                + "&siteid=" + this.matchId + "&ip=" + this.userAction.Ip
                + "&time=" + this.userAction.Time;
        if (userAction != null && userAction.UserId != null) {
            tmp += "&" + AdRequestFactory.USERID + "=" + userAction.UserId
                    + "&" + AdRequestFactory.POSID + "="
                    + attrs.get(AdRequestFactory.POSID);
        }
        if (userAction.Action != null) {
            tmp += "&" + AdRequestFactory.ACTION + "="
                    + userAction.Action.param;
        }
        if (this.isLogOn()) {
            tmp += "&rpwt=tsjj";
        }
        if (this.serverName != null) {
            tmp += ("&servname=" + this.serverName);
        }
        return tmp;
    }

    public void writeFields(DataOutput out) throws IOException {
        super.writeFields(out);
        this.userAction.writeFields(out);
        StringWritable.writeStringNull(out, this.userAction.Object);
        StringWritable.writeStringNull(out, this.userAction.Ip);
        StringWritable.writeStringNull(out, this.serverName);
        StringWritable.writeStringNull(out, this.referer);
        StringWritable.writeStringNull(out, this.userAgent);
        StringWritable.writeStringNull(out, this.lastBrandId);
        StringWritable.writeStringNull(out, this.keyword);
        out.writeLong(this.syndicationId);
        out.writeLong(this.codeId);
        out.writeLong(this.matchId);
        StringWritable.writeStringNull(out, this.userAction.UserId);
        out.writeLong(this.userAction.Time);
        out.writeInt(this.adNum);
        out.writeBoolean(isTest);
        out.writeBoolean(this.isFetchFromSeveralSlot);
        StringWritable.writeStringNull(out, this.multiPosidList.toString());
        attrs.writeFields(out);
    }

    public void readFields(DataInput in) throws IOException {
        super.readFields(in);
        this.userAction = new UserAndAction();
        this.userAction.readFields(in);
        this.userAction.Object = StringWritable.readStringNull(in);
        this.userAction.Ip = StringWritable.readStringNull(in);
        this.serverName = StringWritable.readStringNull(in);
        this.referer = StringWritable.readStringNull(in);
        this.userAgent = StringWritable.readStringNull(in);
        this.lastBrandId = StringWritable.readStringNull(in);
        this.keyword = StringWritable.readStringNull(in);
        this.syndicationId = in.readLong();
        this.codeId = in.readLong();
        this.matchId = in.readLong();
        this.userAction.UserId = StringWritable.readStringNull(in);
        this.userAction.Time = in.readLong();
        this.adNum = in.readInt();
        this.isTest = in.readBoolean();
        this.isFetchFromSeveralSlot = in.readBoolean();
        String listStr = StringWritable.readStringNull(in);
        if (listStr != null && listStr.length() >= 2)
            this.multiPosidList = Arrays.asList(listStr.substring(1,listStr.length()-1).split(", "));
        attrs.readFields(in);
    }

    public IWritable copyFields(IWritable value) {
        throw new IllegalArgumentException("method not supported.");
    }

    // do NOT need setXXX, because all these properties are read-only after the
    // object is created.

    public String getRequest() {
        return this.userAction.Object;
    }

    public long getImprTime() {
        return userAction.Time;
    }

    public String getImprIp() {
        return userAction.Ip;
    }

    public String getUserId() {
        return this.userAction.UserId;
    }

    public void setUserId(String uid) {
        if (this.userAction == null) {
            this.userAction = new UserAndAction();
        }
        this.userAction.UserId = uid;

    }

    public UserAndAction getUserAction() {
        return userAction;
    }

    public void setUserAction(UserAndAction userAction) {
        this.userAction = userAction;
    }

    public String getIpSource() {
        return ipSource;
    }

    public void setIpSource(String ipSource) {
        this.ipSource = ipSource;
    }

    public int getAdNum() {
        return adNum;
    }

    public void setAdNum(int adNum) {
        this.adNum = adNum;
    }

    public long getSyndicationId() {
        return syndicationId;
    }

    public long getCodeId() {
        return codeId;
    }

    public long getMatchId() {
        return matchId;
    }

    public Query getQuery() {
        return query;
    }

    public void setQuery(Query query) {
        this.query = query;
    }

    public void setCodeId(long codeId) {
        this.codeId = codeId;
    }

    public void setMatchId(long mid) {
        this.matchId = mid;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String name) {
        this.serverName = name;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getReferer() {
        return referer;
    }

    public String getUserAgent() {
        return userAgent;
    }


    @Override
    public void turnLogOn() {
        if (HttpRequestHelper.isFromIntranet(this.userAction.Ip)) {
            super.turnLogOn();
        } else {
            LOG.error("invalid debug request from " + this.userAction.Ip);
        }
    }


    public int hashCode() {
        return this.userAction.Object.hashCode();
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }

        if (!(obj instanceof AdRequest)) {
            return false;
        }

        final AdRequest req = (AdRequest) obj;
        if (req.userAction.Object.compareTo(this.userAction.Object) == 0) {
            return true;
        }
        return false;
    }

    public void setSyndicationId(long syndicationId) {
        this.syndicationId = syndicationId;
    }
    /**
     * preLoad request datas.
     */
    public void loadRequestDatas() {
        String ip = getImprIp();
        if (ip != null && !ip.equals("0.0.0.0")) {
            AdvancedIpSearcher.LocationInfo locationInfo = AdvancedIpSearcher.getLocationInfoByIp(ip);
            if (locationInfo != null) {
                areaId = locationInfo.getProvinceId();
                area = locationInfo.getProvince();
                cityId = locationInfo.getCityId();
                city = locationInfo.getCity();
                detailArea = locationInfo.getDetailedArea();
            }
            countryMcc = NewIpSearcher.getCountryMccByAdvancedIp(ip);
        }
        if (areaId < 0) {
            areaId = OTHER_AREA_ID;
            area = "other";
        }
        if (cityId < 0) {
            cityId = OTHER_CITY_ID;
            city = "other";
        }

        slotSize = StringUtils.substring(getAttribute(TEMPLATE_NAME), 5);
        // 只有图片广告的adSize和slotSize不一样
        if (imageAcceptable()) {
            adSize = size(getAttribute(ACTUAL_WIDTH), getAttribute(ACTUAL_HEIGHT));
        }
        if (StringUtils.isBlank(adSize)) {
            adSize = slotSize;
        }
    }

    private static String size(Object x, Object y) {
        try {
            return String.format("%d_%d", Integer.parseInt(x.toString()),
                    Integer.parseInt(y.toString()));
        } catch (Exception e) {
            return "";
        }
    }

    public void putVolatileAttribute(Object key, Object value) {
        volatileMap.put(key, value);
    }

    public Object getVolatileAttribute(Object key) {
        return volatileMap.get(key);
    }

    private static final Map<AdType, Integer> adTypeMap = new HashMap<AdType, Integer>() {
        private static final long serialVersionUID = 1L;

        {
            put(AdType.TEXT, 1);
            put(AdType.IMAGE, 2);
            put(AdType.IMAGE_TEXT, 4);
            put(AdType.NOT_KNOWN, 0);
        }
    };

    /**
     * 本次广告请求的广告位能展示的广告类型
     *
     * @param acceptableTypes
     */
    public void setAcceptableAdTypes(AdType... acceptableTypes) {
        int result = 0;
        for (AdType adType : acceptableTypes) {
            result |= adTypeMap.get(adType);
        }
        attrs.put(AdRequestFactory.AD_SLOT_ACCEPTABLE_AD_TYPE,
                Integer.toString(result));
    }

    /**
     * 本次广告请求的广告位能否展示图片广告
     *
     * @return
     */
    public boolean imageAcceptable() {
        String string = attrs.get(AdRequestFactory.AD_SLOT_ACCEPTABLE_AD_TYPE);
        if (string == null) {
            return false;
        }
        int acceptableTypes = Integer.parseInt(string);
        return (acceptableTypes & adTypeMap.get(AdType.IMAGE)) > 0;
    }

    /**
     * ip库中无对应信息
     */
    public boolean unknownArea() {
        return areaId <= 0 && cityId <= 0;
    }

    public boolean isAreaIdNotSet() {
        return areaId == Integer.MIN_VALUE;
    }

    public String getDetailArea() {
        if (detailArea == null) {
            detailArea = AdvancedIpSearcher.getDetailArea(getImprIp());
        }
        return detailArea;
    }

    /**
     * @return 返回请求设备号 优先级为 imei>idfa>oaid>uid, 都不存在返回空
     */
    public String getDeviceId() {
        String imei = getAttribute(AdRequestFactory.IMEI);
        String idfa = getAttribute(AdRequestFactory.IDFA);
        String uid = getAttribute(AdRequestFactory.DICT_UID);
        String oaid = getAttribute(AdRequestFactory.OAID);
        return StringUtils.defaultString(StringUtils.firstNonBlank(imei, idfa, oaid, uid), "");
    }

    public boolean needFullChannelAdItem() {
        return this.isFullChannel() && this.isHasFullChannelAdItem() && adGroupId > 0L;
    }
}
