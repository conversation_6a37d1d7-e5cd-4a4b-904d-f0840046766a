/**
 * @(#)Filter.java, Aug 12, 2008. Copyright 2008 Yodao, Inc. All rights
 *                  reserved. YOUDAO PROPRIETARY/CONFIDENTIAL. Use is subject to
 *                  license terms.
 */
package outfox.ead.brand.util.http;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

/**
 * 根据HttpServletRequest的请求参数进行过滤
 *
 * <AUTHOR>
 */
public class HttpServletFilter {
    // 正则表达式效率高一些
    private static Pattern companyIpPattern = Pattern
            .compile("***********|************|*************|61.135.216.*|61.135.217.*|61.135.218.*|61.135.219.*|61.135.220.*|"
                    + "61.135.221.*|60.191.80.*|60.191.82.*|10.168.4.*|192.168.*|************|**************|**************|************|"
                    + "*************|**************|***********|**************|**************|************|*************|**************"
                    + "**************|*************|**************|**************|**************");

    // 正则表达式效率高一些
    private static Pattern spiderUserAgentPattern = Pattern
            .compile(".*Baiduspider.*|.*Googlebot.*|.*Yahoo!.*|.*msnbot.*|.*QihooBot.*|.*sogou.*"
                    + "|.*heritrix.*|.*Commons-HttpClient.*|.*Indy Library.*|.*Yodao Toolbar");

    // 不检查输入null
    public static boolean isCompanyIp(HttpServletRequest request) {
        String ip = request.getRemoteAddr();
        return companyIpPattern.matcher(ip).matches();
    }

    // 不检查输入null
    public static boolean isCompanyIp(String ip) {
        return companyIpPattern.matcher(ip).matches();
    }

    // 不检查输入null
    public static boolean isSpider(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return spiderUserAgentPattern.matcher(userAgent).matches();
    }

    // 不检查输入null
    public static boolean isSpider(String userAgent) {
        return spiderUserAgentPattern.matcher(userAgent).matches();
    }

    // 不检查输入null
    public static boolean isRefererNull(HttpServletRequest request) {
        String referer = request.getHeader("Referer");
        return referer == null ? true : false;
    }

}
