/*
 * @(#)DictAttributesProcessor.java, Dec 7, 2010. 
 * 
 * Copyright 2010 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.brand.processor;

import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import outfox.ead.brand.model.DictAdPositionInfo;
import outfox.ead.brand.model.request.AdRequest;
import outfox.ead.brand.service.AdRequestFactory;
import outfox.ead.brand.service.DictAdPositionInfoProvider;
import outfox.ead.brand.service.HandlerProvider;
import outfox.ead.brand.util.BrandUtil;
import outfox.ead.brand.util.VersionCompareUtil;
import outfox.ead.brand.util.area.AdvancedIpSearcher;
import outfox.ead.brand.util.http.HttpRequestHelper;
import outfox.ead.brand.util.log.BrandImprLogger;
import outfox.ead.data.AdType;
import outfox.ead.data.ClickAction;
import outfox.ead.data.SiteType;
import outfox.ead.gorgon.request.SdkAdRequestAttributes;
import outfox.ead.util.log.CodeIdUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.net.URLDecoder;

/**
 * 辞典特有属性的{@link outfox.ead.impr.processor.IAdRequestProcessor}.
 * <AUTHOR>
 * 
 * 增加了请求有效性检查,增加了处理样式模板的逻辑
 * <AUTHOR>
 * 
 * 
 * <AUTHOR>
 *
 */
public class DictAttributesProcessor implements IAdRequestProcessor {
    /**
     * log
     */
    private static final Log LOG = LogFactory.getLog(DictAttributesProcessor.class);

    /**
     * 品牌广告的logger.
     */
    @Setter
    private BrandImprLogger brandImprLogger;

    
    @Setter
    private HandlerProvider handlerProvider;
    
    @Setter
    private DictAdPositionInfoProvider dictAdPositionInfoProvider;

    /**
     * 两天的毫秒数
     */
    private static final long MILLISECONDS_OF_ONE_DAY = 24 * 60 * 60 * 1000;
    
    /**
     * 处理词典请求的所有参数
     */
    @Override
    public AdRequest process(AdRequest adRequest, HttpServletRequest request, HttpServletResponse response) {
        adRequest.stamp();

        adRequest.setAttribute("checkRequest", "true");
        
        // 设置广告位
        getParamFromHttpRequest(AdRequestFactory.POSID, adRequest, request);
        
        // 词典、翻译网页端posid为0，需要重新设置以接入品牌广告
        setWebTextPosId(adRequest);
        
        // 设置memberid，作为posid的替代品，以后会逐渐把posid废除掉，使用memberid
        getParamFromHttpRequest(AdRequestFactory.MEMBERID, adRequest, request);
        
        //use memberid to set posid temporarily, posid will be abolished later
        if (adRequest.getAttribute(AdRequestFactory.POSID) == null) {
            if (adRequest.getAttribute(AdRequestFactory.MEMBERID) != null) {
                adRequest.setAttribute(AdRequestFactory.POSID, 
                        adRequest.getAttribute(AdRequestFactory.MEMBERID));
            } else { //no posid or memberid parameter (syndid=998)
                adRequest.setAttribute(AdRequestFactory.POSID, "-1");
            }            
        } else {
            //posid exist, use posid to set memberid
            adRequest.setAttribute(AdRequestFactory.MEMBERID,
                    adRequest.getAttribute(AdRequestFactory.POSID));
        }
 
        //设置词典客户端特有的uid，客户端的唯一标识
        getParamFromHttpRequest(AdRequestFactory.DICT_UID, adRequest, request);
        
        // 获得请求里的strategy字段
        getParamFromHttpRequest(AdRequestFactory.DICT_STRATEGY, adRequest,
                request);
        
        // 获得请求里的apiversion字段
        getParamFromHttpRequest(AdRequestFactory.API_VERSION, adRequest,
                request);

        // 设置词典客户端版本，用于针对不同版本返回不同类型的广告
        getParamFromHttpRequest(AdRequestFactory.APP_VERSION, adRequest,
                request);
        
        // 移动端没有用appVer，使用了keyfrom表示版本号
        getParamFromHttpRequest(AdRequestFactory.KEYFROM, adRequest,
                request);
        
        // 登录用户userid
        getParamFromHttpRequest(AdRequestFactory.LOGIN_USERID, adRequest,
                request);
        
        //获得广告请求中的vendor信息，用以区分不同渠道的广告
        getParamFromHttpRequest(AdRequestFactory.VENDOR, adRequest,
                request);

        //获取设备型号
        getParamFromHttpRequest(AdRequestFactory.MODEL, adRequest, request);
        
        //获取操作系统版本号
        getParamFromHttpRequest(AdRequestFactory.MID, adRequest, request);
      
        //获取A/B测试参数
        getParamFromHttpRequest(AdRequestFactory.AB_TEST, adRequest, request);
        
        //Android imei
        getParamFromHttpRequest(AdRequestFactory.IMEI, adRequest, request);
        getParamFromHttpRequest(SdkAdRequestAttributes.IMEI_MD5, adRequest, request);

        //IOS idfa
        getParamFromHttpRequest(AdRequestFactory.IDFA, adRequest, request);
        getParamFromHttpRequest(AdRequestFactory.IDFA_MD5, adRequest, request);

        // android oaid
        getParamFromHttpRequest(AdRequestFactory.OAID, adRequest, request);
        getParamFromHttpRequest(AdRequestFactory.OAID_MD5, adRequest, request);

        // android auid
        getParamFromHttpRequest(AdRequestFactory.AUID, adRequest, request);

        getParamFromHttpRequest(AdRequestFactory.AAID, adRequest, request);

        getParamFromHttpRequest(AdRequestFactory.DIMEI, adRequest, request);

        //设置UA
        adRequest.setUserAgent(StringUtils.defaultString(request.getHeader("User-Agent")));
        if (StringUtils.isBlank(adRequest.getUserAgent())) {
            LOG.warn("eadd ua is empty, slotid: " + adRequest.getAttribute(AdRequestFactory.POSID));
        }
        
        String op = HttpRequestHelper.getStringFromParam("op", request);
        adRequest.setAttribute("op", op);
        if ("s".equals(op)){
            return adRequest;
        } else if("ms".equals(op)){
            //批量记录反馈展示log （打包请求）
            //todo 使用GET方式的展示上报接口替代此上报方式，此方法暂时置为无效
            //recordMultiImprInfo(adRequest, request, response);
            return adRequest;
        } else {
            // 设置query
            getQueryFromHttpRequest(adRequest, request);

            // 获取广告类型,取词使用的字段
            getParamFromHttpRequest(AdRequestFactory.DICT_AD_TYPE, adRequest,
                    request);

            // 设置词典结果页的offers，词典结果页使用
            getParamFromHttpRequest(AdRequestFactory.DICT_OFFERS, adRequest,
                    request);

            // 获得callback字段，底部通栏V2使用
            getParamFromHttpRequest(AdRequestFactory.CALLBACK, adRequest,
                    request);
            
            if (VersionCompareUtil.supportSwfFormatDictVersion(adRequest)) {
                adRequest.setAttribute(AdRequestFactory.FLASH_SUPPORT, "true");
            } else {
                adRequest.setAttribute(AdRequestFactory.FLASH_SUPPORT, "false");
            }
            

            // 获得请求里的模板，web端请求使用
            getParamFromHttpRequest(AdRequestFactory.TEMPLATE_NAME, adRequest, request);

            String width = HttpRequestHelper.getStringFromParam(AdRequestFactory.WIDTH, request);
            if (width != null){
                adRequest.setAttribute(AdRequestFactory.WIDTH, width);
                adRequest.setAttribute(AdRequestFactory.REQUEST_WIDTH, width);
            }

            String height = HttpRequestHelper.getStringFromParam(AdRequestFactory.HEIGHT, request);
            if (height != null){
                adRequest.setAttribute(AdRequestFactory.HEIGHT, height);
                adRequest.setAttribute(AdRequestFactory.REQUEST_HEIGHT, height);
            }

            String logoHeight = HttpRequestHelper.getStringFromParam(AdRequestFactory.LOGO_HEIGHT, request);
            if (logoHeight != null) {
                adRequest.setAttribute(AdRequestFactory.LOGO_HEIGHT, logoHeight);
            }

            String navHeight = HttpRequestHelper.getStringFromParam(AdRequestFactory.NAV_HEIGHT, request);
            if (navHeight != null) {
                adRequest.setAttribute(AdRequestFactory.NAV_HEIGHT, navHeight);
            }

            //弹窗请求特有参数，记录layout参数到展示log，ABtest结束后删除
            getParamFromHttpRequest("layout", adRequest, request);
            
            //处理SSP请求参数
            getParamFromHttpRequest(AdRequestFactory.SSP_BACKUP, adRequest, request);
            
            getParamFromHttpRequest(AdRequestFactory.SSP_ID, adRequest, request);
            
            //手机端请求参数问题，width和height值为0，解析screen参数
            getParamFromHttpRequest(AdRequestFactory.SCREEN, adRequest, request);
        }
        
        adRequest.setAttribute(AdRequestFactory.CITY_ID,
                AdvancedIpSearcher.getCityProvinceIdByIp(adRequest.getImprIp()) + "");
        
        handlerProvider.provide(adRequest);
        
        /*
         * 为了防止改posid时忘了同时改memberid，在设置词典广告请求属性的最后一步，把memberid写成和posid一样
         */
        setMemberIdFromPosId(adRequest);
        return adRequest;
    }

    private void setWebTextPosId(AdRequest adRequest) {
        String posId = adRequest.getAttribute(AdRequestFactory.POSID);
        String newPosId = null;
        if ("0".equals(posId)) {
            if (adRequest.getSyndicationId() == 57) {
                newPosId = "204";
            } else if (adRequest.getSyndicationId() == 58) {
                newPosId = "302";
            }
            if (newPosId != null) {
                adRequest.setAttribute(AdRequestFactory.POSID, newPosId);
            }
        }
    }
    /**
     * 把AdRequest里的memberid（AttributesMap、codeId里）设置成posid的值
     */
    private void setMemberIdFromPosId(AdRequest adRequest) {
        try {
            long channelId = SiteType.getSiteType(adRequest.getSyndicationId());
            String posidStr = adRequest.getAttribute(AdRequestFactory.POSID);
            long posid = Long.parseLong(posidStr);
            adRequest.setCodeId(CodeIdUtil.genCodeId(channelId, adRequest.getSyndicationId(), posid, 0));
            adRequest.setAttribute(AdRequestFactory.MEMBERID, posidStr);
        } catch (NumberFormatException e) {
            adRequest.setAttribute("checkRequest", "false");
            LOG.error(e, e);
        }
    }

    /**
     * 从request body中读取json数据
     */
    private String readStringFromRequestBody(HttpServletRequest request) {
        StringBuilder jb = new StringBuilder();
        String line;
        try {
            BufferedReader reader = request.getReader();
            while ((line = reader.readLine()) != null) {
                jb.append(line);
            }
        } catch (Exception e) {
            LOG.error("ERROR Reading JSON String!", e);
        }
        return jb.toString();
    }
    
    /**
     * 
     * 把httpServletRequest里的参数设置到adRequest的attributesMap里
     * 
     */
    protected void getParamFromHttpRequest(String paramName,AdRequest adRequest,HttpServletRequest request){
        String param = HttpRequestHelper.getStringFromParam(paramName, request);
        if(param != null){
            adRequest.setAttribute(paramName, param);
        }
    }
    
    
    /**
     * 获得词典查词query的请求内容 Use query as dict's request URL.
     * 
     */
    protected void getQueryFromHttpRequest(AdRequest adRequest, HttpServletRequest request){
        String query = HttpRequestHelper.getStringFromParam("query", request);
        if (query != null) {
            adRequest.getUserAction().Object = query;
        }
    }
    
}
