/**
 * @(#)LinkCreatorProcessor.java, Nov 09, 2011. 
 * 
 * Copyright 2011 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.brand.processor;

import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import outfox.ead.brand.model.Candidate;
import outfox.ead.brand.model.request.AdRequest;
import outfox.ead.brand.service.AdRequestFactory;
import outfox.ead.brand.util.render.WhitelistSupport;
import outfox.ead.data.AdVariation;
import outfox.ead.data.Helper;
import outfox.ead.data.Keyword;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * <AUTHOR> add whitelist support
 * 
 */
public class LinkCreatorProcessor implements WinnerProcessor {
    private static final Log LOG = LogFactory.getLog(LinkCreatorProcessor.class);
    /**
     * 用于创建跳转链接。
     */
    private LinkCreator linkCreator;
    /**
     * 用于诊断的白名单。
     */
    private WhitelistSupport whitelistSupport;

    @Setter
    private List<String> valueNames = Arrays.asList(AdRequest.AD_REQUEST_ID);

    public LinkCreatorProcessor() {
    }

    /**
     * 为选择出来将要展示的广告设置其跳转链接，因为务必要先跳转到我们的点击服务器进行
     * 点击的统计才可以。
     */
    @Override
    public void process(AdRequest request, List<Candidate> winners) {
        fillValues(request, winners);

        for (int index = 0; index < winners.size(); index++) {
            Candidate item = winners.get(index);
            if (item == null) {
                continue;
            }
            AdVariation adv = item.getAdVariation();
            if (adv == null) {
                Helper.debug(LOG,
                        "can NOT find associated variation for AdItem: ", item);
                winners.set(index, null);
                continue;
            }
            Keyword keyword = item.getKeyword();
            String link = null;
            if (keyword != null) {
                link = keyword.getLink();
            }
            // FIXME: 163这个标记是历史原因，无法解决。
            if (link == null || link.equals("163")) {
                link = adv.getLink();
            }
            if (linkCreator != null) {
                // item.setCodeId(request.getCodeId()); // 这个设置可能导致丢失对item的codeId的修改，如清除图片的styleId
                link = linkCreator.createLink(link, item);
                item.getAdVariation().setLink(link);
            }

        }
        if(whitelistSupport != null){
            whitelistSupport.process(request, winners);
        }
    }

    /**
     * 把需要传递到点击的数据写入AdItem的TextMap
     * @param request
     * @param winners
     */
    private void fillValues(AdRequest request, List<Candidate> winners) {
        for (Candidate candidate: winners) {
            candidate.setLocalProperty(AdRequestFactory.AD_TYPE, candidate.getAdType().toString());
            if (!StringUtils.isBlank(request.getSlotSize())){
                candidate.setLocalProperty(AdRequestFactory.SLOT_SIZE, request.getSlotSize().toString());
            }
            if (!StringUtils.isBlank(request.getAdSize())){
                candidate.setLocalProperty(AdRequestFactory.AD_SIZE, request.getAdSize().toString());
            }
            for (String valueName: valueNames) {
                String value = request.getAttribute(valueName);
                if (value != null && value.trim().length() > 0) {
                    candidate.setLocalProperty(valueName, value);
                }
            }
        }
    }

    public void setLinkCreator(LinkCreator linkCreator) {
        this.linkCreator = linkCreator;
    }

    public void setWhitelistSupport(WhitelistSupport whitelistSupport) {
        this.whitelistSupport = whitelistSupport;
    }
}
