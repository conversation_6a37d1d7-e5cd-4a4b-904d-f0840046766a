/**
 * @(#)AbstractAdRequestHandler.java, 2012-6-4. Copyright 2012 Yodao, Inc. All
 *                                    rights reserved. YODAO
 *                                    PROPRIETARY/CONFIDENTIAL. Use is subject
 *                                    to license terms.
 */
package outfox.ead.brand.handler;


import outfox.ead.brand.model.request.AdRequest;
import outfox.ead.brand.model.response.AdResponse;

/**
 * 用于处理广告请求的模板类. 需要注意的是在配置时，{@link RecordAdRequestInfoProcessor}
 * 是务必要配置到winnerProcessors里面的。
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface AbstractAdRequestHandler {

    /**
     * 广告处理，返回AdResponse对象
     * 
     * @param adRequest
     * @return
     */
    AdResponse handle(AdRequest adRequest);

}
