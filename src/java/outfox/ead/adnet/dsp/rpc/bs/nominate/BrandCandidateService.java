package outfox.ead.adnet.dsp.rpc.bs.nominate;

import com.google.common.collect.HashBasedTable;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import outfox.ead.brand.model.Candidate;
import outfox.ead.data.AdItem;
import outfox.ead.data.AdSensitivityDO;
import outfox.ead.data.AppPackage;
import outfox.ead.data.BrandAdStyle;
import outfox.ead.data.brand.BrandAdWeight;
import outfox.ead.data.datamanager.AdBrandProducer;
import outfox.ead.gorgon.service.scheduleBrand.ScheduledPlan;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/5/30.
 */
public class BrandCandidateService {

    @Setter
    private AdBrandProducer adBrandProducer;

    /**
     * 获取cpt候选广告
     *
     * @param key
     * @return
     */
    protected List<Candidate> getCptCandidates(String key) {
        List<AdItem> cptAds = adBrandProducer.getCpdKey2Ads().get(key);
        return convert(cptAds);
    }

    /**
     * 获取cpm候选广告
     *
     * @param key
     * @return
     */
    protected List<Candidate> getCpmCandidates(String key) {
        List<AdItem> cpmAds = adBrandProducer.getCpmKey2UnionAds().get(key);
        return convert(cpmAds);
    }

    /**
     * 获取实时接口的cpt广告，v2接口
     *
     * @param key
     * @return
     */
    protected List<Candidate> getRealtimeCptAds(String key) {
        ArrayList<AdItem> cptAds = new ArrayList<>(adBrandProducer.getCpdAdItemMap().getOrDefault(key, Collections.emptySet()));
        return convert(cptAds);
    }

    /**
     * 获取实时接口的cpm广告，v2接口
     *
     * @param key
     * @return
     */
    protected List<Candidate> getRealtimeCpmAds(String key) {
        ArrayList<AdItem> cptAds = new ArrayList<>(adBrandProducer.getCpmAdItemMap().getOrDefault(key, Collections.emptySet()));
        return convert(cptAds);
    }

    /**
     * 获取vender cpm候选广告
     *
     * @param key
     * @return
     */
    protected List<Candidate> getCpmCandidatesForVender(String key) {
        List<AdItem> cpmAds = adBrandProducer.getOverImprCountLimitCpmKey2UnionAds().getOrDefault(key, Collections.emptyList());
        return convert(cpmAds);
    }

    /**
     * 获取cpt候选的排期广告
     *
     * @param key
     * @return
     */
    protected Map<Long, List<Candidate>> getScheduleCandidates(String key) {
        Map<Long, List<AdItem>> adsMap = adBrandProducer.getScheduledAdsMap().getOrDefault(key, Collections.emptyMap());
        return convert(adsMap);
    }

    /**
     * 获取cpm候选的排期广告
     *
     * @param key
     * @return
     */
    protected Map<Long, List<Candidate>> getScheduleCpmCandidates(String key) {
        Map<Long, List<AdItem>> cpmAdMap = adBrandProducer.getScheduleCpmAdsMap().getOrDefault(key, Collections.emptyMap());
        return convert(cpmAdMap);
    }

    /**
     * 获取vender的cpm候选排期广告
     *
     * @param key
     * @return
     */
    protected Map<Long, List<Candidate>> getScheduleCpmCandidatesForVender(String key) {
        Map<Long, List<AdItem>> cpmAdMap = adBrandProducer.getOverImprCountLimitScheduleCpmAdsMap().getOrDefault(key, Collections.emptyMap());
        return convert(cpmAdMap);
    }

    /**
     * 获取vender cpt排期候选广告(V2)
     *
     * @param key
     * @return
     */
    protected List<Candidate> getScheduleAdMapV2ForVender(String key) {
        List<AdItem> tempAdItemList = adBrandProducer.getOverLimitScheduledAdMapV2()
                .getOrDefault(key, Collections.emptyList());

        return convert(tempAdItemList);
    }

    /**
     * 获取cpt排期候选广告(V2)
     *
     * @return
     */
    protected List<Candidate> getScheduleAdMapV2(String key) {
        List<AdItem> tempAdItemList = adBrandProducer.getScheduledAdMapV2().getOrDefault(key, Collections.emptyList());
        return convert(tempAdItemList);
    }


    /**
     * 获取cpt广告权重
     *
     * @param key
     * @return
     */
    protected BrandAdWeight getCpdKey2Weight(String key) {
        return adBrandProducer.getCpdKey2Weight().get(key);
    }

    /**
     * 获取cpt测试广告权重
     *
     * @param key
     * @return
     */
    protected BrandAdWeight getTestCpdKey2Weight(String key) {
        return adBrandProducer.getTestCpdKey2Weight().get(key);
    }

    /**
     * 获取cpt预取广告权重
     *
     * @param key
     * @return
     */
    protected Map<Long, BrandAdWeight> getScheduleCpdAdsWeight(String key) {
        return adBrandProducer.getScheduleCpdAdsWeight().getOrDefault(key, Collections.emptyMap());
    }

    /**
     * 获取cpt排期计划
     *
     * @param key
     * @return
     */
    protected HashBasedTable<Long, Long, List<ScheduledPlan>> getScheduledCpdPlanMapV2(String key) {
        return adBrandProducer.getScheduledCpdPlanMapV2().getOrDefault(key, HashBasedTable.create());
    }

    /**
     * 获取投放关键词
     *
     * @return
     */
    protected Set<String> getKeywordAds() {
        return adBrandProducer.getKeywordAds();
    }

    /**
     * 全球发音广告 keyword to audio-url
     *
     * @param keyword
     * @return
     */
    protected Map<String, String> getDirectionalWordAdItem2AudioUrl(String keyword) {
        return adBrandProducer
                .getKeyword2AdItemKey()
                .getOrDefault(keyword.toLowerCase(), new HashMap<>());
    }

    /**
     * 预取广告天数
     *
     * @return
     */
    protected int getScheduledDays() {
        return adBrandProducer.getScheduledDays();
    }

    public AppPackage appPackageById(Long id) {
        return adBrandProducer.appPackageById(id);
    }

    public BrandAdStyle mappingBrandAdStyle(Candidate candidate) {
        String posConvertKey = candidate.genGroupIdWithStyleIdKey();
        return adBrandProducer.mappingBrandAdStyleByKey(posConvertKey);
    }

    public List<Candidate> getDirectAdItemList(long brandPosId) {
        List<AdItem> adItems = adBrandProducer.getDirectAdsMap().getOrDefault(brandPosId, Collections.emptyList());
        return convert(adItems);
    }

    public long getAdGroupDailyImprCount(long groupId, int dayOfMonth) {
        return adBrandProducer.getAdGroupDailyImprCount(groupId, dayOfMonth);
    }
    /**
     * 获取cpm排期计划
     *
     * @param key
     * @return
     */
    protected HashBasedTable<Long, Long, List<ScheduledPlan>> getScheduledCpmPlanMapV2(String key) {
        return adBrandProducer.getScheduledCpmPlanMapV2().getOrDefault(key, HashBasedTable.create());
    }

    /**
     * 获取开屏灵敏度配置
     *
     * @param key
     * @return
     */
    protected AdSensitivityDO getAdSensitivityConfig(String key) {
        return adBrandProducer.getAdSensitivityConfigMap().get(key);
    }

    /**
     * 获取广告位是否支持灵敏度配置
     * @param positionId
     * @return
     */
    protected boolean isPositionSupportSensitivity(Long positionId) {
        return Boolean.TRUE.equals(adBrandProducer.getPositionSupportSensitivityMap().get(positionId));
    }

    /**
     * 将adItem转换为candidate对象
     *
     * @param adItemList
     * @return
     */
    private List<Candidate> convert(List<AdItem> adItemList) {
        if (CollectionUtils.isNotEmpty(adItemList)) {
            List<Candidate> candidates = new ArrayList<>();
            for (AdItem adItem : adItemList) {
                candidates.add(new Candidate(adItem));
            }
            return candidates;
        } else {
            return new ArrayList<>();
        }
    }

    private Map<Long, List<Candidate>> convert(Map<Long, List<AdItem>> adsMap) {
        if (MapUtils.isNotEmpty(adsMap)) {
            Map<Long, List<Candidate>> candidateMap = new HashMap<>();
            adsMap.forEach((k, v) -> {
                candidateMap.put(k, convert(v));
            });
            return candidateMap;
        } else {
            return Collections.emptyMap();
        }
    }

}
