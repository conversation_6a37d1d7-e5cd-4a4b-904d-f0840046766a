/*
 * @(#)EaddImageRawDataCache.java, 2012-09-06. Copyright 2012 Yodao, Inc. All
 * rights reserved. YODAO
 * PROPRIETARY/CONFIDENTIAL. Use is subject to
 * license terms.
 */
package outfox.ead.adnet.dsp.rpc.bs.nominate;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import outfox.ead.brand.model.Candidate;
import outfox.ead.brand.model.CpmLimit;
import outfox.ead.brand.model.request.AdRequest;
import outfox.ead.brand.service.AdRequestFactory;
import outfox.ead.brand.service.BrandAdProvider;
import outfox.ead.brand.service.DictAdPositionInfoProvider;
import outfox.ead.brand.service.DictHandlerProvider;
import outfox.ead.brand.util.BrandUtil;
import outfox.ead.brand.util.RandObjectPool;
import outfox.ead.data.AdItem;
import outfox.ead.data.AdSensitivityDO;
import outfox.ead.data.AdVariation;
import outfox.ead.data.BrandConstants;
import outfox.ead.data.Keyword;
import outfox.ead.data.brand.BrandAdWeight;
import outfox.ead.data.brand.ElementProperty;
import outfox.ead.gorgon.config.BrandGlobalConfig;
import outfox.ead.gorgon.model.BrandAdType;
import outfox.ead.gorgon.render.BrandSizeUtils;
import outfox.ead.gorgon.request.SdkAdRequest;
import outfox.ead.gorgon.request.nominate.IBrandAdNominatorWrapper.AllBrandAdsResult;
import outfox.ead.gorgon.request.trace.TraceHelper;
import outfox.ead.gorgon.service.VenderAdService;
import outfox.ead.gorgon.service.blacklist.BlackListRequestFilter;
import outfox.ead.gorgon.service.brand.BrandAdFilterService;
import outfox.ead.gorgon.service.brand.BrandDspAdService;
import outfox.ead.gorgon.service.brand.ScheduledBrandConfigService;
import outfox.ead.gorgon.service.filter.FullChannelFlagMarker;
import outfox.ead.gorgon.service.scheduleBrand.ScheduleBrandNominateResult;
import outfox.ead.gorgon.service.scheduleBrand.ScheduledPlan;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 重构为从dataServer中读取品牌广告的缓存数据
 * refactor at 2021-09-08
 *
 * <AUTHOR>
 */
@Slf4j
public class BrandAdNominator extends BrandCandidateService implements BrandAdProvider {

    /**
     * 根据在投广告物料标记当前广告请求是否是全链路请求
     * 之后再根据这个标记走全链路过滤器
     */
    @Setter
    private FullChannelFlagMarker fullChannelFlagMarker;

    @Setter
    private VenderAdService venderAdService;


    /**
     * 城市定向key分割符
     */
    public static final String SEG = "_city_";

    /**
     * 国家定向key分割符
     */
    public static final String COUNTRY_SEG = "_country_";

    /**
     * 参考 https://confluence.inner.youdao.com/pages/viewpage.action?pageId=2541967 AdvancedLocation编码，
     * 国内省市比这个小，国外省市比这个大。
     */
    private static final int AREA_ID_DIVIDING_LINE = 1000;

    /**
     * 默认轮播总数
     */
    private static final int DEFAULT_TOTAL_WEIGHT = 6;


    /**
     * 词典IOS端查词结果页广告id
     */
    private final String DICT_IOS_POSID = "600";

    /**
     * 最大轮播数
     */
    private final int CAROUSEL_NUM = 12;
    /**
     * Android请求keyfrom参数正则表达式
     */
    public static final Pattern ANDROID_KEYFROM_PATTERN = Pattern.compile("^mdict.[0-9]+.[0-9]+.[0-9]+.android$");

    /**
     * iOS请求keyfrom参数正则表达式
     */
    public static final Pattern IOS_KEYFROM_PATTERN = Pattern.compile("^mdict.[0-9]+.[0-9]+.[0-9]+.iphone(|pro)$");
    public static final String KEYFROM_PREFIX = "mdict.";
    /**
     * 负责提供词典广告位信息
     */
    @Setter
    private DictAdPositionInfoProvider dictAdPositionInfoProvider;


    @Setter
    private BrandDspAdService brandDspAdService;

    @Setter
    private BrandAdFilterService brandAdFilterService;

    /**
     * 负责提供词典广告位信息
     */
    @Setter
    private DictHandlerProvider dictHandlerProvider;

    @Autowired
    private ScheduledBrandConfigService scheduledBrandConfigService;

    @Autowired
    private BlackListRequestFilter blackListRequestFilter;

    /**
     * 1天的毫秒数
     */
    private static final long MILLISECONDS_OF_ONE_DAY = 24 * 60 * 60 * 1000;

    @Setter
    private BrandGlobalConfig brandGlobalConfig;

    @Autowired
    private TraceHelper traceHelper;

    public List<Candidate> getKey2Ads(AdRequest adRequest, String key) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        List<Candidate> result = null;
        List<Candidate> cptAds = getCptCandidates(key);
        List<Candidate> cpmAds = getCpmCandidates(key);
        if (CollectionUtils.isNotEmpty(cptAds)) {
            result = new ArrayList<>(cptAds);
        }
        if (CollectionUtils.isNotEmpty(cpmAds)) {
            if (result == null) {
                result = new ArrayList<>(cpmAds);
            } else {
                result.addAll(cpmAds);
            }
        }
        return loadSuitableSize(adRequest, result);
    }

    /**
     * provide all the brand ads which meet the position and city
     */
    @Override
    public List<Candidate> getAllBrandAds(AdRequest adRequest) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        String key = getTargetedKey(adRequest);

        List<Candidate> ads = null, result = new ArrayList<>();
        List<Candidate> cptCandidates = getCptCandidates(key);
        List<Candidate> cpmCandidates = getCpmCandidates(key);
        markAndFilter(adRequest, cptCandidates, cpmCandidates);

        // 如果发现需要全链路广告，那么cpd和cpm应该只有一个广告物料，直接全部加入result
        // 否则就走之前的逻辑，先查cpd，如果没有再查cpm
        if (adRequest.needFullChannelAdItem()) {
            result.addAll(cptCandidates);
            result.addAll(cpmCandidates);
        } else {
            // CPD ads first
            if (hasCpdAds(key, adRequest.isTest())) {
                result = cptCandidates;
                filterTestAds(ads, adRequest.isTest());
            }

            // CPM ads second
            if (CollectionUtils.isEmpty(result) && hasCpmAds(key)) {
                try {
                    ads = cpmCandidates;
                } catch (Exception e) {
                    return null;
                }
                if (CollectionUtils.isNotEmpty(ads)) {
                    result = new ArrayList<>();
                    if (ads.size() == 1) {
                        result.add(ads.get(0));
                    } else {
                        int weight = Integer.MAX_VALUE;
                        int maxAd = adRequest.getAdNum();
                        if (ads.size() <= maxAd) {
                            weight = 0;
                            for (Candidate ad : ads) {
                                weight += (ad.getEcpm() + 99) / 100;
                            }
                        }
                        if (weight <= maxAd) {
                            result = expandAds(ads);
                        } else {
                            RandObjectPool<Candidate> pool = new RandObjectPool<>();
                            for (Candidate ad : ads) {
                                pool.addWithoutDuplicateCheck(ad, ad.getEcpm());
                            }
                            for (int i = 0; i < maxAd; i++) {
                                result.add(pool.get());
                            }
                        }
                    }
                }
            }
        }

        return loadSuitableSize(adRequest, result);
    }

    /**
     * 根据 adRequest 获取本次请求具体的广告定向索引 key
     */
    private String getTargetedKey(AdRequest adRequest) {
        return getTargetedKey(adRequest.getAttribute(AdRequestFactory.POSID), adRequest);
    }

    /**
     * 根据 adRequest 获取本次请求具体的广告定向索引 key
     *
     * @param pid
     * @param adRequest
     * @return
     */
    public String getTargetedKey(String pid, AdRequest adRequest) {
        return pid;
    }

    /**
     * 随机获取单个品牌广告
     * <p>
     * provide only one of all the brand ads which meet the position and city
     */
    @Override
    public List<Candidate> getBrandAd(AdRequest adRequest) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        String key = getTargetedKey(adRequest);
        List<Candidate> ads, result = new ArrayList<>();
        List<Candidate> cptCandidates = getCptCandidates(key);
        List<Candidate> cpmCandidates = getCpmCandidates(key);
        markAndFilter(adRequest, cptCandidates, cpmCandidates);
        // 如果发现需要全链路广告，那么cpd和cpm应该只有一个广告物料，直接全部加入result
        // 否则就走之前的逻辑，先查cpd，如果没有再查cpm
        if (adRequest.needFullChannelAdItem()) {
            result.addAll(cptCandidates);
            result.addAll(cpmCandidates);
        }else{
            // CPD ads first
            if (hasCpdAds(key, adRequest.isTest())) {
                ads = cptCandidates;
                //防止随机取到测试广告
                filterTestAds(ads, adRequest.isTest());
                if (CollectionUtils.isNotEmpty(ads)) {
                    result = new ArrayList<>();
                    result.add(ads.get(ThreadLocalRandom.current().nextInt(ads.size())));
                }
            }
            // CPM ads second
            if (CollectionUtils.isEmpty(result) && hasCpmAds(key)) {
                try {
                    ads = cpmCandidates;
                } catch (Exception e) {
                    return null;
                }
                if (CollectionUtils.isNotEmpty(ads)) {
                    result = new ArrayList<>();
                    if (ads.size() == 1) {
                        result.add(ads.get(0));
                    } else {
                        RandObjectPool<Candidate> pool = new RandObjectPool<>();
                        for (Candidate ad : ads) {
                            pool.addWithoutDuplicateCheck(ad, ad.getEcpm());
                        }
                        result.add(pool.get());
                    }
                }
            }
        }

        return loadSuitableSize(adRequest, result);
    }

    /**
     * 如果是非测试设备，过滤测试广告
     * 如果是测试设备，将测试广告调到最前面，防止线上广告轮播数满导致测试广告出不来
     */
    private void filterTestAds(List<Candidate> candidates, boolean isTest) {
        if (candidates != null) {
            List<Candidate> testAdList = new ArrayList<>();
            Iterator<Candidate> iterator = candidates.iterator();
            while (iterator.hasNext()) {
                Candidate ad = iterator.next();
                if (isTestAd(ad)) {
                    testAdList.add(ad);
                    iterator.remove();
                }
            }
            if (isTest) {
                testAdList.addAll(candidates);
                candidates.clear();
                candidates.addAll(testAdList);
            }
        }
    }

    /**
     * 判断是否为测试品牌广告
     */
    private boolean isTestAd(Candidate ad) {
        String isTest = ad.getAdItemProperty(BrandConstants.IS_TEST_AD_ITEM);
        return Boolean.parseBoolean(isTest);
    }

    /**
     * provide all the brand ads, no according to the probability
     */
    @Override
    public AllBrandAdsResult getAllBrandAdsWithoutProbability(AdRequest adRequest) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new AllBrandAdsResult(new ArrayList<>(), new ArrayList<>());
        }
        String key = getTargetedKey(adRequest);
        BrandAdWeight weight = getCpdKey2Weight(key);
        int adNum = adRequest.getAdNum();
        //设置总的轮播数
        if (weight != null) {
            adRequest.setAttribute("totalWeight", weight.getTotalWeight() + "");
            adNum = weight.getTotalWeight();
        }
        List<Candidate> result = new ArrayList<>();
        List<Candidate> cptCandidates = getCptCandidates(key);
        List<Candidate> cpmCandidates = getCpmCandidates(key);
        markAndFilter(adRequest, cptCandidates, cpmCandidates);
        cptCandidates.removeIf(BrandAdNominator::isFirstShot);
        cpmCandidates.removeIf(BrandAdNominator::isFirstShot);
        List<Candidate> cpmAdsCopyList = new ArrayList<>();
        filterTestAds(cptCandidates, adRequest.isTest());

        String keyword = adRequest.getKeyword();
        if (StringUtils.isNotBlank(keyword) && !clearElseIfHitKeyword(cptCandidates, keyword)) {
            // 如果cpd未命中定向词，需要检查cpm是否命中，如果cpm命中就出cpm广告
            if (clearElseIfHitKeyword(cpmCandidates, keyword)) {
                cptCandidates.clear();
                // 多个cpm命中，根据轮播权重选取广告
                cptCandidates.addAll(getResidualCpmAds(cpmCandidates, adNum));
                // 清空cpmAds，防止后续重复加入
                cpmCandidates.clear();
            }
        }

        if (adRequest.isOpenSecondBrandNomination()) {
            cpmAdsCopyList.addAll(cpmCandidates);
        }
        if (cptCandidates.size() < adNum && hasCpmAds(key)) {
            cptCandidates.addAll(getResidualCpmAds(cpmCandidates, adNum - cptCandidates.size()));
        }

        String posId = adRequest.getAttribute(AdRequestFactory.POSID);
        //只有600（ios查词结果广告）做轮播！！但要排除该广告位与效果广告位一起轮播的情况，用nominator_not_carousel标识
        if (posId.equals(DICT_IOS_POSID)
                && adRequest.getAttribute("nominator_not_carousel") == null
                // 有全链路广告物料，直接出全链路广告
                && !(adRequest.needFullChannelAdItem())
        ) {
            filterCarouselAd(cptCandidates, adRequest);
        }
        if (CollectionUtils.isNotEmpty(cptCandidates)) {
            result.addAll(cptCandidates);
        }
        return new AllBrandAdsResult(loadSuitableSize(adRequest, result), loadSuitableSize(adRequest, cpmAdsCopyList));
    }

    public static List<AdItem> copyAdItemList(List<AdItem> adItems) {
        List<AdItem> res = new ArrayList<>(adItems.size());
        adItems.forEach(adItem -> {
            res.add(copyAdItem(adItem));
        });
        return res;
    }

    /**
     * provide brand scheduled ads in N days
     */
    @Override
    public List<Candidate> getBrandScheduledAds(AdRequest adRequest, int nDay) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        String key = getTargetedKey(adRequest);
        long queryEndTime = System.currentTimeMillis() + nDay * MILLISECONDS_OF_ONE_DAY;
        List<Candidate> result = new ArrayList<>();
        Map<Long, List<Candidate>> adsMap = getScheduleCandidates(key);
        if (MapUtils.isNotEmpty(adsMap)) {
            for (Map.Entry<Long, List<Candidate>> entry : adsMap.entrySet()) {
                long time = entry.getKey();
                if (time <= queryEndTime) {
                    List<Candidate> adList = entry.getValue();
                    if (CollectionUtils.isNotEmpty(adList)) {
                        result.addAll(adList);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            filterTestAds(result, adRequest.isTest());
        }
        return loadSuitableSize(adRequest, result);
    }

    /**
     * 提名完cpd广告之后填充cpm广告
     */
    @Override
    public List<Candidate> getCpmScheduledAds(AdRequest adRequest, List<String> posIdList, int nDay, boolean isForVender) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        //key：广告开始投放时间戳、value：<该时段所有cpm广告，该时段cpd已投权重>
        Map<Long, MutablePair<List<Candidate>, Integer>> cpmScheduleMap = new HashMap<>();
        long queryEndTime = System.currentTimeMillis() + nDay * MILLISECONDS_OF_ONE_DAY;
        int totalWeight = 0;
        //对于从多个广告位上提取cpd广告的情况，将多个广告位上的cpm广告合并再提名
        for (String posId : posIdList) {
            adRequest.setAttribute(AdRequestFactory.POSID, posId);
            String key = getTargetedKey(adRequest);
            // 若是vender提名广告，则cpm广告不受展示量上限的限制
            Map<Long, List<Candidate>> cpmAdMap = getScheduleCpmCandidates(key);
            if (isForVender) {
                cpmAdMap = getScheduleCpmCandidatesForVender(key);
            }

            Map<Long, BrandAdWeight> cpdWeightMap = getScheduleCpdAdsWeight(key);
            if (MapUtils.isNotEmpty(cpmAdMap) && MapUtils.isNotEmpty(cpdWeightMap)) {
                for (Map.Entry<Long, List<Candidate>> entry : cpmAdMap.entrySet()) {
                    long startTime = entry.getKey();
                    List<Candidate> cpmAds = entry.getValue();
                    if (startTime <= queryEndTime) {
                        BrandAdWeight cpdWeight = cpdWeightMap.get(startTime);
                        if (cpdWeight != null) {
                            totalWeight = cpdWeight.getTotalWeight();
                            MutablePair<List<Candidate>, Integer> pair = cpmScheduleMap
                                    .computeIfAbsent(startTime, k -> new MutablePair<>(new LinkedList<>(), 0));
                            pair.getLeft().addAll(cpmAds);
                            pair.setRight(pair.getRight() + cpdWeight.getWeight());
                        }
                    }
                }
            }
        }
        List<Candidate> result = new LinkedList<>();
        for (MutablePair<List<Candidate>, Integer> cpmAdsAndWeight : cpmScheduleMap.values()) {
            List<Candidate> firstShotAdItems = cpmAdsAndWeight.getLeft().stream()
                    .filter(BrandAdNominator::isFirstShot)
                    .collect(Collectors.toList());

            traceHelper.logIfTraceEnabled("first shot adItem", firstShotAdItems, TraceHelper.NOMINATE_GIDS_TO_STRING);

            List<Candidate> cpmAdItems = cpmAdsAndWeight.getLeft().stream()
                    .filter(c -> !isFirstShot(c))
                    .collect(Collectors.toList());

            traceHelper.logIfTraceEnabled("cpm adItem", cpmAdItems, TraceHelper.NOMINATE_GIDS_TO_STRING);
            if (!isForVender) {
                result.addAll(getNRandomItems(cpmAdItems, totalWeight - cpmAdsAndWeight.getRight()));
            } else {
                result.addAll(cpmAdItems);
            }
            // firstshot类型的广告属于cpm广告，但是不会计入轮播数
            result.addAll(firstShotAdItems);
        }
        return loadSuitableSize(adRequest, result);
    }

    @Override
    public Map<String, Map<Long, List<Candidate>>> getScheduledAdsMap() {
        return null;
    }

    private static Boolean isFirstShot(Candidate candidate) {
        if (candidate == null) {
            return false;
        }
        String firstShot = candidate.getAdItemProperty(BrandUtil.FIRST_SHOT);
        return BrandUtil.IS_FIRST_SHOT_VALUE.equals(firstShot);
    }

    /**
     * 从adItemList中随机获取N个广告，可以重复
     */
    private List<Candidate> getNRandomItems(List<Candidate> candidates, int n) {
        traceHelper.logIfTraceEnabled("candidate cpm candidates", candidates, TraceHelper.NOMINATE_GIDS_TO_STRING);
        if (CollectionUtils.isEmpty(candidates)) {
            return Collections.emptyList();
        }
        List<Integer> adItemsWithWeight = new ArrayList<>();
        for (int i = 0; i < candidates.size(); i++) {
            Candidate candidate = candidates.get(i);
            int weight = Integer.parseInt(candidate.getLocalProperty(BrandUtil.WEIGHT, "1"));
            while (weight-- > 0) {
                adItemsWithWeight.add(i);
            }
        }
        Map<Integer, Integer> adsId2Times = new HashMap<>();
        while (n-- > 0 && adItemsWithWeight.size() > 0) {
            int preIndex = ThreadLocalRandom.current().nextInt(adItemsWithWeight.size());
            int index = adItemsWithWeight.get(preIndex);
            // adsId2Times记录的是这个index出现的次数
            adsId2Times.put(index, adsId2Times.getOrDefault(index, 0) + 1);
            adItemsWithWeight.remove(preIndex);
        }
        List<Candidate> result = new LinkedList<>();
        //合并重复广告
        adsId2Times.forEach((index, times) -> {
            Candidate candidate = candidates.get(index);
            candidate.setLocalProperty(BrandUtil.WEIGHT, String.valueOf(times));
            result.add(candidate);
        });
        traceHelper.logIfTraceEnabled("random n cpm candidates", result, TraceHelper.NOMINATE_GIDS_TO_STRING);
        return result;
    }

    /**
     * 随机获取cpm品牌广告用于填充剩余流量
     *
     * @param cpmAds cmpAds
     * @param adNum  获取cpm广告的个数
     * @return cpm广告
     */
    private List<Candidate> getResidualCpmAds(List<Candidate> cpmAds, int adNum) {
        List<Candidate> cpmAdsWithWeight = new ArrayList<>();
        traceHelper.logIfTraceEnabled("candidate cpm adItems", cpmAds, TraceHelper.NOMINATE_GIDS_TO_STRING);
        for (Candidate candidate : cpmAds) {
            int weight = Integer.parseInt(candidate.getLocalProperty(BrandUtil.WEIGHT, "1"));
            while (weight-- > 0) {
                cpmAdsWithWeight.add(candidate);
            }
        }
        if (CollectionUtils.isEmpty(cpmAds)) {
            return Collections.emptyList();
        }
        List<Candidate> candidates = new LinkedList<>();
        //随机不放回选adNum个cpm广告
        while (adNum-- > 0 && CollectionUtils.isNotEmpty(cpmAdsWithWeight)) {
            int index = ThreadLocalRandom.current().nextInt(cpmAdsWithWeight.size());
            candidates.add(cpmAdsWithWeight.get(index));
            cpmAdsWithWeight.remove(index);
        }
        traceHelper.logIfTraceEnabled("random n cpm adItems", candidates, TraceHelper.NOMINATE_GIDS_TO_STRING);
        return candidates;
    }


    /**
     * 将原始list里的AdItem按照权重展开， 实现权重随机功能。
     */
    private List<Candidate> expandAds(List<Candidate> ads) {
        List<Candidate> result = null;
        if (CollectionUtils.isNotEmpty(ads)) {
            result = new ArrayList<>();
            if (ads.size() == 1) {
                result.add(ads.get(0));
            } else {
                for (Candidate ad : ads) {
                    int weight = (int) (((ad.getEcpm()) + 99) / 100);
                    for (int i = 0; i < weight; i++) {
                        result.add(ad);
                    }
                }
            }
        }
        return result;
    }

    /**
     * copy adItem
     */
    public static AdItem copyAdItem(AdItem item) {
        AdItem adItem = new AdItem(item);
        AdVariation adv = new AdVariation();
        adv.duplicate(item.getAdVariation());
        adItem.setAdVariation(adv);
        adItem.setKeyword(Keyword.EMPTY);
        adItem.setMaxCpc(0);
        adItem.setOrderType(item.getOrderType());
        Map<String, Object> context = new HashMap<>(item.getContext().size());
        context.putAll(item.getContext());
        adItem.setContext(context);
        Map<String, ElementProperty> elementPropertyMap = item.getElementPropertyMap();
        elementPropertyMap.forEach((key, value) -> adItem.getElementPropertyMap().put(key, value));
        return adItem;
    }

    /**
     * whether there is a brand ad in (posid, cityId) by probability
     */
    private boolean hasCpdAds(String key, boolean isTest) {
        BrandAdWeight weight = getCpdKey2Weight(key);
        //consider weight of test ads
        int testWeight = 0;
        BrandAdWeight brandAdWeight = getTestCpdKey2Weight(key);
        if (!isTest && brandAdWeight != null) {
            testWeight = brandAdWeight.getWeight();
        }
        return weight != null && ThreadLocalRandom.current().nextDouble() <= weight.getRatioExcludeTest(testWeight);
    }

    private boolean hasCpmAds(String key) {
        List<Candidate> cpmCandidates = getCpmCandidates(key);
        return CollectionUtils.isNotEmpty(cpmCandidates);
    }

    /**
     * 轮播处理方法
     */
    private void filterCarouselAd(List<Candidate> cpdAds, AdRequest request) {
        if (request == null || CollectionUtils.isEmpty(cpdAds)) {
            return;
        }
        List<Candidate> restFlowAds = new ArrayList<>(cpdAds);
        //根据权重填充广告位，不足的补null
        fillAdListByWeight(restFlowAds);
        //lastBrandId记录的应该是前面所有广告的id列表，包括空广告，记为0，例如5433,5434,0,5435
        String lastBrandId = request.getLastBrandId();
        int offset = 0;
        if (!StringUtils.isBlank(lastBrandId)) {
            int size = lastBrandId.split(",").length;
            offset = size % CAROUSEL_NUM;
        }
        String id = request.getUserId();
        int index = offset;
        if (!StringUtils.isBlank(id)) {
            index = (Math.abs(id.hashCode()) + offset) % CAROUSEL_NUM;
        }
        Candidate result = restFlowAds.get(index);
        cpdAds.clear();
        if (result != null) {
            cpdAds.add(result);
        }
    }


    /**
     * 根据广告权重扩展填充广告，同时不足轮播位数的填充null
     */
    private void fillAdListByWeight(List<Candidate> candidates) {
        if (candidates == null) {
            return;
        }
        //不足轮播数的填充null
        int restNum = CAROUSEL_NUM - candidates.size();
        for (int i = 0; i < restNum; i++) {
            candidates.add(null);
        }
    }


    @Override
    public List<Candidate> getAllBrandAdsForVender(AdRequest adRequest) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        String key = getTargetedKey(adRequest);
        List<Candidate> ads = null, result = new ArrayList<>();
        // CPD ads first
        result = getCptCandidates(key);
        filterTestAds(ads, adRequest.isTest());
        // CPM ads second
        try {
            // 获取在投的cpm广告列表，不受cpm展示量上线限制
            result.addAll(getCpmCandidatesForVender(key));
        } catch (Exception e) {
            return new ArrayList<>();
        }

        return loadSuitableSize(adRequest, result);
    }

    @Override
    public List<Candidate> getAllBrandScheduledAdsIgnoreType(AdRequest adRequest, List<String> posIdList, int nDay) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        List<Candidate> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(posIdList)) {
            return result;
        }
        for (String posId : posIdList) {
            adRequest.setAttribute(AdRequestFactory.POSID, posId);
            result.addAll(getBrandScheduledAds(adRequest, nDay));
        }
        return loadSuitableSize(adRequest, result);
    }


    @Override
    public List<Candidate> getBrandScheduledAdsAfterFilter(AdRequest adRequest, List<String> posIdList, int nDay, boolean weightControl) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        List<Candidate> result = new ArrayList<>();
        //key：广告开始投放时间戳、value：<该时段所有cpm广告，该时段cpd已投权重>
        Map<Long, MutablePair<List<Candidate>, Integer>> cpmScheduleMap = new HashMap<>();
        long queryEndTime = System.currentTimeMillis() + nDay * MILLISECONDS_OF_ONE_DAY;
        int totalWeight = 0;
        for (String posId : posIdList) {
            // 时间段(开始时间，结束时间) -> cpd被过滤掉的权重（即cpm广告需要额外补充的权重）
            Map<Pair<Long, Long>, Integer> time2FilterWeight = new HashMap<>();
            adRequest.setAttribute(AdRequestFactory.POSID, posId);
            String key = getTargetedKey(adRequest);
            Map<Long, List<Candidate>> adsMap = getScheduleCandidates(key);
            if (MapUtils.isNotEmpty(adsMap)) {
                for (Map.Entry<Long, List<Candidate>> entry : adsMap.entrySet()) {
                    long time = entry.getKey();
                    if (time <= queryEndTime) {
                        for (Candidate candidate : entry.getValue()) {
                            Integer weight = Integer.parseInt(candidate.getLocalProperty(BrandUtil.WEIGHT, "0"));
                            long endMs = candidate.getAdEndTime();
                            // 根据预期投放的起止时间过滤广告对象，若开始时间小于当前时间，则开始时间设置为当前时间
                            if (brandAdFilterService.filterAdItemBySchedule(adRequest, candidate, Math.max(entry.getKey(), System.currentTimeMillis()), endMs)) {
                                Pair<Long, Long> timePair = Pair.of(time, endMs);
                                if (time2FilterWeight.containsKey(timePair)) {
                                    time2FilterWeight.put(timePair,
                                            time2FilterWeight.get(timePair) + weight);
                                } else {
                                    time2FilterWeight.put(timePair, weight);
                                }
                            } else {
                                result.add(candidate);
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(result)) {
                filterTestAds(result, adRequest.isTest());
            }

            Map<Long, List<Candidate>> cpmAdMap = getScheduleCpmCandidates(key);
            Map<Long, BrandAdWeight> cpdWeightMap = getScheduleCpdAdsWeight(key);
            // 开始时间 -> cpd广告被过滤掉的权重（包含全部时间段的过滤权重信息）
            Map<Long, Integer> startTime2FilterWeight = new LinkedHashMap<>();
            // cpdWeightMap的keyset记录了最小粒度的预取时间段，遍历此map，将全部需要cpm广告填充空闲轮播的时间段都放入startTime2FilterWeight中
            for (Map.Entry<Long, BrandAdWeight> entry : cpdWeightMap.entrySet()) {
                startTime2FilterWeight.put(entry.getKey(), 0);
            }
            // 遍历全部的预取时间段，检查每个时间段是否有被过滤掉的cpd广告，若有过滤掉的cpd广告，则将被过滤掉的权重记录到对应的时间段开始时间中
            for (Map.Entry<Long, Integer> entry : startTime2FilterWeight.entrySet()) {
                for (Map.Entry<Pair<Long, Long>, Integer> time2Weight : time2FilterWeight.entrySet()) {
                    Long startTime = entry.getKey();
                    // 被过滤掉cpd广告的开始投放时间
                    Long filterStartTime = time2Weight.getKey().getLeft();
                    // 被过滤掉cpd广告的结束投放时间
                    Long filterEndTime = time2Weight.getKey().getRight();
                    // 当此时间段落入到这个cpd广告的投放时间范围内，则在这个时间段对应的entry中增加该cpd广告的权重
                    if (startTime >= filterStartTime && startTime <= filterEndTime) {
                        entry.setValue(entry.getValue() + time2Weight.getValue());
                    }
                }
            }
            if (MapUtils.isNotEmpty(cpmAdMap) && MapUtils.isNotEmpty(cpdWeightMap)) {
                for (Map.Entry<Long, List<Candidate>> entry : cpmAdMap.entrySet()) {
                    long startTime = entry.getKey();
                    List<Candidate> cpmAds = new ArrayList<>();
                    for (Candidate candidate : entry.getValue()) {
                        long endMs = candidate.getAdEndTime();
                        if (!brandAdFilterService.filterAdItemBySchedule(adRequest, candidate, Math.max(startTime, System.currentTimeMillis()), endMs)) {
                            cpmAds.add(candidate);
                        }
                    }
                    if (startTime <= queryEndTime) {
                        BrandAdWeight cpdWeight = cpdWeightMap.get(startTime);
                        Integer filteredWeight = startTime2FilterWeight.getOrDefault(startTime, 0);
                        if (cpdWeight != null) {
                            totalWeight = cpdWeight.getTotalWeight();
                            MutablePair<List<Candidate>, Integer> pair = cpmScheduleMap
                                    .computeIfAbsent(startTime, k -> new MutablePair<>(new LinkedList<>(), 0));
                            pair.getLeft().addAll(cpmAds);
                            pair.setRight(pair.getRight() + cpdWeight.getWeight() - filteredWeight);
                        }
                    }
                }
            }
        }

        for (MutablePair<List<Candidate>, Integer> cpmAdsAndWeight : cpmScheduleMap.values()) {
            List<Candidate> firstShotAdItems = cpmAdsAndWeight.getLeft().stream()
                    .filter(BrandAdNominator::isFirstShot)
                    .collect(Collectors.toList());
            List<Candidate> cpmAdItems = cpmAdsAndWeight.getLeft().stream()
                    .filter(c -> !isFirstShot(c))
                    .collect(Collectors.toList());
            if (weightControl) {
                result.addAll(getNRandomItems(cpmAdItems, totalWeight - cpmAdsAndWeight.getRight()));
            } else {
                result.addAll(cpmAdItems);
            }
            // firstshot类型的广告属于cpm广告，但是不会计入轮播数
            for (Candidate firstShotAdItem : firstShotAdItems) {
                traceHelper.logIfTraceEnabled("first shot adItem", firstShotAdItem, TraceHelper.NOMINATE_AD_TO_STRING);
                result.add(firstShotAdItem);
            }
        }

        return loadSuitableSize(adRequest, result);
    }


    /**
     * 根据广告请求获取经过了过滤器过滤后的广告
     *
     * @param sdkAdRequest
     * @param adRequest
     * @return
     */
    public List<Candidate> getBrandScheduledAdsV2(SdkAdRequest sdkAdRequest, AdRequest adRequest) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        dictHandlerProvider.provide(adRequest);
        String key = getTargetedKey(adRequest);
        List<Candidate> candidateList = new ArrayList<>();
        List<Candidate> tempCandidateList;
        if (sdkAdRequest.isVender()) {
            tempCandidateList = getScheduleAdMapV2ForVender(key)
                    .stream()
                    .filter(adItem -> venderAdService.isVenderCandidate(sdkAdRequest, adItem)).collect(Collectors.toList());
        } else {
            // 广告过滤定向提名
            tempCandidateList = getScheduleAdMapV2(key);
        }
        for (Candidate candidate : tempCandidateList) {
            if (!brandAdFilterService.filterInNominate(adRequest, candidate)) {
                candidateList.add(candidate);
            }
        }

        // 若venderid不为空，则删除全部非此vender的广告
        if (sdkAdRequest.isVender()) {
            candidateList.removeIf(candidate -> !venderAdService.isVenderCandidate(sdkAdRequest, candidate));
        }
        candidateList = loadSuitableSize(adRequest, candidateList);
        return candidateList;
    }

    /**
     * 根据传入的有效广告对象列表，计算出本次投放最终的投放计划列表
     *
     * @param validCandidateList
     * @param adRequest
     * @param totalWeight     此广告位总轮播权重
     * @return firstKey:开始时间，secondKey：结束时间，value：该时间段的投放列表
     */
    public HashBasedTable<Long, Long, List<ScheduledPlan>> getBrandScheduledPlanTable(List<Candidate> validCandidateList, AdRequest adRequest, boolean isBrandTest, int totalWeight) {
        traceHelper.logIfTraceEnabled("generate brand schedule plan table in process");
        if (requestFilter(adRequest) || CollectionUtils.isEmpty(validCandidateList)) {
            if (scheduledBrandConfigService.isNeedEffect(adRequest.getAttribute(AdRequestFactory.SDK_SLOT_ID))) {
                return buildFullEffectPlanTable(totalWeight);
            }
            return HashBasedTable.create();
        }
        // 在投放计划中出现过的广告id，用于删除无效物料，包含cpd广告和cpm广告
        Set<String> adIdSet = new HashSet<>();
        // 获取经过了过滤、程序化对接、venderId过滤之后剩余的有效广告id到adItem对象的映射
        Map<String, Candidate> adId2CandidateMap = new HashMap<>();
        for (Candidate candidate : validCandidateList) {
            adId2CandidateMap.putIfAbsent(candidate.getAdItemProperty(BrandUtil.AD_ID), candidate);
        }
        String key = getTargetedKey(adRequest);
        HashBasedTable<Long, Long, List<ScheduledPlan>> cpdPlanTable = getScheduledCpdPlanMapV2(key);
        HashBasedTable<Long, Long, List<ScheduledPlan>> cpmPlanTable = getScheduledCpmPlanMapV2(key);
        if (cpdPlanTable.isEmpty() && cpmPlanTable.isEmpty()) {
            if (scheduledBrandConfigService.isNeedEffect(adRequest.getAttribute(AdRequestFactory.SDK_SLOT_ID))) {
                return buildFullEffectPlanTable(totalWeight);
            }
            return HashBasedTable.create();
        }
        HashBasedTable<Long, Long, List<ScheduledPlan>> result = HashBasedTable.create();
        // 首先用cpd广告填充每个投放时间槽
        for (Table.Cell<Long, Long, List<ScheduledPlan>> cell : cpdPlanTable.cellSet()) {
            List<ScheduledPlan> validPlanList = new ArrayList<>();
            if (BrandUtil.isNotEmptyTableCell(cell)) {
                for (ScheduledPlan scheduledPlan : cell.getValue()) {
                    if (adId2CandidateMap.containsKey(scheduledPlan.getAdId())
                            && !brandAdFilterService.filterAfterNominate(adRequest, adId2CandidateMap.get(scheduledPlan.getAdId()), cell.getRowKey(), cell.getColumnKey())
                            && (scheduledPlan.getBrandAdType() == BrandAdType.NORMAL || scheduledPlan.getBrandAdType() == BrandAdType.FIRST_SHOT)) {
                        validPlanList.add(scheduledPlan);
                        adIdSet.add(scheduledPlan.getAdId());
                    }
                }
                result.put(cell.getRowKey(), cell.getColumnKey(), new ArrayList<>(validPlanList));
            }
            traceHelper.logIfTraceEnabled("cpd scheduled plan, start-time:" + cell.getRowKey() + "end-time:" + cell.getColumnKey(), validPlanList, TraceHelper.LIST_PLAN_TO_STRING);
        }
        // 然后使用cpm广告填充每个槽里的空余轮播
        for (Table.Cell<Long, Long, List<ScheduledPlan>> cell : result.cellSet()) {
            int cpdWeight = 0;
            List<ScheduledPlan> validCpmPlanList = new ArrayList<>();
            if (cpmPlanTable != null && BrandUtil.isNotEmptyTableCell(cell) && cpmPlanTable.contains(cell.getRowKey(), cell.getColumnKey())) {
                for (ScheduledPlan scheduledPlan : cpmPlanTable.get(cell.getRowKey(), cell.getColumnKey())) {
                    if (adId2CandidateMap.containsKey(scheduledPlan.getAdId())
                            && !brandAdFilterService.filterAfterNominate(adRequest, adId2CandidateMap.get(scheduledPlan.getAdId()), cell.getRowKey(), cell.getColumnKey())) {
                        validCpmPlanList.add(scheduledPlan);
                    }
                }
            }
            for (ScheduledPlan cpdScheduledPlan : cell.getValue()) {
                cpdWeight += cpdScheduledPlan.getWeight();
            }
            traceHelper.logIfTraceEnabled("valid cpm scheduled plan, start-time:" + cell.getRowKey() + " end-time:" + cell.getColumnKey(), validCpmPlanList, TraceHelper.LIST_PLAN_TO_STRING);
            // 使用
            cell.getValue().addAll(getNCpmOrEffectScheduledPlan(validCpmPlanList, totalWeight - cpdWeight, adIdSet, adRequest.getAttribute(AdRequestFactory.SDK_SLOT_ID)));
        }
        // 若是非测试请求，则需要删除没出现在投放计划中的adItem，防止响应无效物料信息
        if (!isBrandTest) {
            validCandidateList.removeIf(candidate -> !adIdSet.contains(candidate.getAdItemProperty(BrandUtil.AD_ID)));
        }

        return result;

    }

    /**
     * 根据需要填充的权重数，返回cpm投放权重和效果广告占位符权重
     * 首先用cpm广告填充剩余权重，若填不满，则用效果广告占位符填充
     *
     * @param cpmScheduledPlanList cpm广告投放计划列表
     * @param remainWeight         填充完cpd广告后还空缺的权重
     * @param slotId               广告位id
     * @return
     */
    private List<ScheduledPlan> getNCpmOrEffectScheduledPlan(List<ScheduledPlan> cpmScheduledPlanList, int remainWeight, Set<String> adIdSet, String slotId) {
        List<ScheduledPlan> result = new ArrayList<>();
        List<String> adIds = new ArrayList<>();
        // 效果广告占位符应该填充的权重数
        int effectWeight = 0;
        // 首先处理开机首刷广告
        ScheduledPlan firstShotPlan = cpmScheduledPlanList.stream().filter(c -> c.getBrandAdType() == BrandAdType.FIRST_SHOT).findFirst().orElse(null);
        if (firstShotPlan != null) {
            traceHelper.logIfTraceEnabled("first shot scheduled plan, adId:" + firstShotPlan.getAdId());
            result.add(ScheduledPlan.builder()
                    .adId(firstShotPlan.getAdId())
                    .weight(1)
                    .brandAdType(BrandAdType.FIRST_SHOT)
                    .build());
        }
        //在处理了开机首刷广告之后，若没有需要cpm广告填充的权重则直接返回
        if (remainWeight == 0) {
            return result;
        }

        // 处理cpm广告
        cpmScheduledPlanList.stream().filter(c -> c.getBrandAdType() == BrandAdType.NORMAL).forEach(c -> {
            for (int i = 0; i < c.getWeight(); i++) {
                adIds.add(c.getAdId());
            }
        });
        Collections.shuffle(adIds);
        // cpm广告可以填充的权重
        int cpmWeight = remainWeight;
        if (adIds.size() < remainWeight) {
            cpmWeight = adIds.size();
            effectWeight = remainWeight - cpmWeight;
        }
        // 随机不放回的抽取cpmWeight个广告
        Map<String, Integer> adWeightMap = new HashMap<>();
        for (int i = 0; i < cpmWeight; i++) {
            String adId = adIds.get(i);
            adWeightMap.put(adId, adWeightMap.getOrDefault(adId, 0) + 1);
        }
        traceHelper.logIfTraceEnabled("cpm random nominate result:" + adWeightMap);
        // 填充cpm广告
        for (Map.Entry<String, Integer> entry : adWeightMap.entrySet()) {
            String adId = entry.getKey();
            result.add(ScheduledPlan.builder()
                    .adId(adId)
                    .weight(entry.getValue())
                    .brandAdType(BrandAdType.NORMAL)
                    .build());
        }
        // 将提名出的cpm广告加入到广告id集合中
        result.forEach(plan -> adIdSet.add(plan.getAdId()));

        // 填充效果广告占位符
        traceHelper.logIfTraceEnabled("effectWeight is  " + effectWeight);
        if (effectWeight > 0 && scheduledBrandConfigService.isNeedEffect(slotId)) {
            result.add(ScheduledPlan.builder()
                    .adId("effect")
                    .weight(effectWeight)
                    .brandAdType(BrandAdType.EFFECT_AD)
                    .build());
        }
        return result;

    }

    private List<Candidate> loadSuitableSize(AdRequest adRequest, List<Candidate> candidates) {
        if (CollectionUtils.isEmpty(candidates)) {
            traceHelper.logIfTraceEnabled("brand nominate result", candidates, TraceHelper.NOMINATE_GIDS_TO_STRING);
            return candidates;
        }
        BrandSizeUtils.filterDeprecated(candidates, adRequest);
        List<Candidate> result = new ArrayList<>(candidates.size());
        for (Candidate candidate : candidates) {
            Candidate suitableAdItem = BrandSizeUtils.loadSuitableAdItem(adRequest, candidate);
            if (suitableAdItem != null) {
                result.add(suitableAdItem);
            }
        }
        traceHelper.logIfTraceEnabled("brand nominate result", result, TraceHelper.NOMINATE_GIDS_TO_STRING);
        return result;
    }

    @Override
    public void clearKeywordAds(List<Candidate> candidates) {
        Set<String> keywordAds = getKeywordAds();
        Iterator<Candidate> iterator = candidates.iterator();
        while (iterator.hasNext()) {
            Candidate item = iterator.next();
            String key = item.getBrandAdUniqueIdentifier();
            if (keywordAds.contains(key)) {
                iterator.remove();
            }
        }
    }

    /**
     * 判断allItems中是否有定向查词广告并且命中定向词，如果命中则删除其余广告只出该广告
     *
     * @param candidates -
     * @param keyword  关键词/定向词
     * @return true-命中 false-为命中
     */
    public boolean clearElseIfHitKeyword(List<Candidate> candidates, String keyword) {
        ArrayList<Candidate> tmpItems = new ArrayList<>();
        List<Candidate> cpdList = new ArrayList<>();
        List<Candidate> cpmList = new ArrayList<>();
        for (Candidate candidate : candidates) {
            if (BrandUtil.ORDER_CPD.equals(candidate.getOrderType())) {
                cpdList.add(candidate);
            } else if (BrandUtil.ORDER_CPM.equals(candidate.getOrderType())) {
                cpmList.add(candidate);
            }
        }

        Map<String, String> directionalWordAdItem2AudioUrl = getDirectionalWordAdItem2AudioUrl(keyword);
        Set<String> directionalWordAdItems = directionalWordAdItem2AudioUrl.keySet();
        // 优先出CPT的查词
        for (Candidate candidate : cpdList) {
            if (directionalWordAdItems.contains(candidate.getBrandAdUniqueIdentifier())) {
                tmpItems.add(candidate);
                candidate.setAudioUrl(directionalWordAdItem2AudioUrl.get(candidate.getBrandAdUniqueIdentifier()));
            }
        }
        if (!tmpItems.isEmpty()) {
            candidates.clear();
            candidates.addAll(tmpItems);
            traceHelper.logIfTraceEnabled("keyword target cpt ads", candidates, TraceHelper.NOMINATE_GIDS_TO_STRING);
            return true;
        }
        for (Candidate candidate : cpmList) {
            if (directionalWordAdItems.contains(candidate.getBrandAdUniqueIdentifier())) {
                tmpItems.add(candidate);
                candidate.setAudioUrl(directionalWordAdItem2AudioUrl.get(candidate.getBrandAdUniqueIdentifier()));
            }
        }
        if (tmpItems.isEmpty()) {
            return false;
        } else {
            candidates.clear();
            candidates.addAll(tmpItems);
            traceHelper.logIfTraceEnabled("keyword target cpm ads", candidates, TraceHelper.NOMINATE_GIDS_TO_STRING);
            return true;
        }
    }

    /**
     * 构建全为效果广告占位符的投放计划，开始时间为当前时间，结束时间为预取的排期结束时间
     *
     * @param weight
     * @return
     */
    private HashBasedTable<Long, Long, List<ScheduledPlan>> buildFullEffectPlanTable(int weight) {
        HashBasedTable<Long, Long, List<ScheduledPlan>> planTable = HashBasedTable.create();
        ScheduledPlan effectPlan = ScheduledPlan.builder()
                .adId("effect")
                .weight(weight)
                .brandAdType(BrandAdType.EFFECT_AD)
                .build();
        planTable.put(System.currentTimeMillis(),
                System.currentTimeMillis() + getScheduledDays() * MILLISECONDS_OF_ONE_DAY,
                Collections.singletonList(effectPlan));
        traceHelper.logIfTraceEnabled("need effect plan table " + planTable);
        return planTable;
    }



    public ScheduleBrandNominateResult buildDirectScheduleResult(List<AdRequest> brandAdRequestList, SdkAdRequest sdkAdRequest) {
        for (AdRequest brandAdRequest : brandAdRequestList) {
            List<Candidate> adItemList = getDirectScheduledBrandAds(sdkAdRequest, brandAdRequest);
            if (CollectionUtils.isNotEmpty(adItemList)) {
                return ScheduleBrandNominateResult.builder()
                        .adItemList(adItemList)
                        .planTable(buildDirectSchedulePlan(adItemList))
                        .adRequest(brandAdRequest)
                        .baseAdItem(null)
                        .build();
            }
        }
        return ScheduleBrandNominateResult.builder().build();
    }

    /**
     * 获取测试直达广告列表，若响应结果为空，说明此请求无对应的测试直达广告
     * @param sdkAdRequest
     * @param adRequest
     * @return
     */
    public List<Candidate> getDirectScheduledBrandAds(SdkAdRequest sdkAdRequest, AdRequest adRequest) {
        traceHelper.logIfTraceEnabled("brand direct nominate in process");
        dictHandlerProvider.provide(adRequest);
        long posId = Long.parseLong(adRequest.getAttributeOrDefault(AdRequestFactory.POSID, "0"));
        List<Candidate> candidateList = getDirectAdItemList(posId);
        candidateList = loadSuitableSize(adRequest, candidateList);
        // 替换程序化对接广告
        return brandDspAdService.replaceDspBrandAdIfPossible(candidateList, sdkAdRequest, adRequest);
    }

    /**
     * 构建测试直达广告的投放计划：
     * 投放时间只有一段：当前时间-三天后
     * 每个adItem权重都为1，且交替出现
     * @param directAdItemList 当前有效的测试直达广告列表
     */
    private HashBasedTable<Long, Long, List<ScheduledPlan>> buildDirectSchedulePlan(List<Candidate> directAdItemList){
        HashBasedTable<Long, Long, List<ScheduledPlan>> table = HashBasedTable.create();
        if (CollectionUtils.isEmpty(directAdItemList)) {
            return table;
        }
        List<ScheduledPlan> planList = new ArrayList<>();
        for (Candidate candidate : directAdItemList) {
            planList.add(ScheduledPlan.builder()
                    .cpmLimit(CpmLimit.instanceOf(candidate))
                    .adId(candidate.getBrandAdUniqueIdentifier())
                    .brandAdType(BrandUtil.parseBrandAdType(candidate))
                    .weight(1)
                    .build());
        }
        table.put(System.currentTimeMillis(), System.currentTimeMillis() + BrandUtil.MILLISECONDS_OF_ONE_DAY * 3, planList);
        return table;
    }

    /**
     * 获取全部有效的、通过了过滤器校验的品牌广告
     */
    public List<Candidate> getAllValidBrandAds(AdRequest adRequest) {
        traceHelper.logIfTraceEnabled("brand nominate in process");
        if (requestFilter(adRequest)) {
            return new ArrayList<>();
        }
        String key = getTargetedKey(adRequest);
        List<Candidate> ads = new ArrayList<>();
        ads.addAll(getCptCandidates(key));
        ads.addAll(getCpmCandidates(key));
        fullChannelFlagMarker.mark(adRequest, ads);
        brandAdFilterService.filterAdItems(adRequest, ads);
        return loadSuitableSize(adRequest, ads);
    }

    @Override
    public AllBrandAdsResult getAllRealtimeBrandAds(AdRequest adRequest){
        traceHelper.logIfTraceEnabled("brand realtime nominate v2 in process");
        if (requestFilter(adRequest)) {
            return new AllBrandAdsResult(new ArrayList<>(), new ArrayList<>());
        }
        String key = getTargetedKey(adRequest);
        String keyword = adRequest.getKeyword();
        List<Candidate> cptCandidates = getRealtimeCptAds(key);
        List<Candidate> cpmCandidates = getRealtimeCpmAds(key);
        markAndFilter(adRequest, cptCandidates, cpmCandidates);
        // 处理定向查词请求，若关键词为空或未命中关键词，则删除关键词定向广告
        if(StringUtils.isBlank(keyword) || !clearElseIfHitKeyword(cptCandidates, keyword)){
            clearKeywordAds(cptCandidates);
        }
        if(StringUtils.isBlank(keyword) || !clearElseIfHitKeyword(cpmCandidates, keyword)){
            clearKeywordAds(cpmCandidates);
        }
        List<Candidate> preferredAds = new ArrayList<>();
        preferredAds.addAll(cptCandidates);
        preferredAds.addAll(cpmCandidates);
        return new AllBrandAdsResult(loadSuitableSize(adRequest, preferredAds), loadSuitableSize(adRequest, cptCandidates), loadSuitableSize(adRequest, cpmCandidates));
    }

    @Override
    public AdSensitivityDO getAdSensitivityConfig(String key) {
        return super.getAdSensitivityConfig(key);
    }

    @Override
    public boolean isPositionSupportSensitivity(Long positionId) {
        return super.isPositionSupportSensitivity(positionId);
    }

    /**
     * 1. 由于部分需求需要根据在投的广告物料，反向标记请求，所以先根据cpm/cpd广告标记广告请求
     * 2. 过滤不符合条件的cpm/cpd广告
     */
    private void markAndFilter(AdRequest adRequest, List<Candidate> copiedCpdAds, List<Candidate> copiedCpmAds) {
        fullChannelFlagMarker.mark(adRequest, copiedCpdAds);
        fullChannelFlagMarker.mark(adRequest, copiedCpmAds);
        brandAdFilterService.filterAdItems(adRequest, copiedCpdAds);
        brandAdFilterService.filterAdItems(adRequest, copiedCpmAds);
    }


    /**
     * 拦截品牌竞价请求
     *
     * @param adRequest
     * @return 是否拦截
     */
    private boolean requestFilter(AdRequest adRequest) {
        return brandGlobalConfig.isBrandSwitch() || blackListRequestFilter.isInBlackList(adRequest.getImprIp());
    }

}



