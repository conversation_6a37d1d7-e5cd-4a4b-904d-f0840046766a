package outfox.ead.dsp.data;

import org.junit.Test;

import java.util.*;

import static org.junit.Assert.assertEquals;

/**
 * RelevanceSlotExplorationTest测试
 */
public class RelevanceSlotExplorationTest {
    @Test
    public void getTest() {

        Set<String> slotAllowedSet = new HashSet<String>(Arrays.asList("1", "2"));
        Map<String, Long> slotClick = new HashMap<String, Long>();
        RelevanceSlotExploration relevanceSlotExploration = new RelevanceSlotExploration(1, slotAllowedSet, slotClick);
        assertEquals(slotAllowedSet, relevanceSlotExploration.getSlotAllowedSet());
        assertEquals(slotClick, relevanceSlotExploration.getSlotClickMap());
        assertEquals(1, relevanceSlotExploration.getGroupId());
    }
}
