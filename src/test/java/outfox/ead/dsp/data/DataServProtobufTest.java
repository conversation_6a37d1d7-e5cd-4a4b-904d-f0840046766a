package outfox.ead.dsp.data;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import org.junit.Test;
import outfox.ead.data.protobuf.DataHolder.ThirdPartyDataPlatform;
import static org.junit.Assert.assertEquals;

public class DataServProtobufTest {

    @Test
    public void testSerialization() throws IOException {
        ThirdPartyDataPlatform platform = ThirdPartyDataPlatform.newBuilder()
            .setId(1)
            .setConvType("td")
            .setConvValid(true)
            .setSiteDomain("lnk0.com")
            .build();

        DataServProtobuf<ThirdPartyDataPlatform> dataToBeSerialized = new DataServProtobuf<ThirdPartyDataPlatform>(platform);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        DataOutputStream dataOutputStream = new DataOutputStream(byteArrayOutputStream);
        dataToBeSerialized.writeFields(dataOutputStream);
        byte[] serializedBytes = byteArrayOutputStream.toByteArray();

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(serializedBytes);
        DataInputStream dataInputStream = new DataInputStream(byteArrayInputStream);
        DataServProtobuf<ThirdPartyDataPlatform> deserializedData = new DataServProtobuf<ThirdPartyDataPlatform>();
        deserializedData.readFields(dataInputStream);

        assertEquals(platform, deserializedData.getProtoObj());

    }
}