package com.youdao.ead.util;

import com.youdao.ead.vo.request.ThirdRtaRequest;
import org.junit.Test;

import javax.validation.ValidationException;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/8/17.
 */
public class CombinedNotNullTest {
    @Test(expected = ValidationException.class)
    public void test() {
        ThirdRtaRequest thirdRtaRequest = new ThirdRtaRequest();
        ValidationUtils.validation(thirdRtaRequest);
    }


    @Test
    public void test2() {
        ThirdRtaRequest thirdRtaRequest = new ThirdRtaRequest();
        thirdRtaRequest.setImei("11111111");
        ValidationUtils.validation(thirdRtaRequest);
    }
}
