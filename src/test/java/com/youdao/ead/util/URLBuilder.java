package com.youdao.ead.util;

import org.springframework.web.util.UriComponentsBuilder;
import outfox.ead.dsp.protocol.youdao.ConvTracker;
import outfox.ead.dsp.protocol.youdao.TrackerCommonDimension;

import java.net.URLEncoder;
import java.util.Base64;

public class URLBuilder {

    public static final String CLK_HOST = "http://ad.youdao.com/r/click";
    public static final String IMPR_HOST = "http://ad.youdao.com/r/impr";

    //渠道替换
    public static final String CALLBACK = "http://ad.youdao.com/r/impr?sid=1&aid=9012";

    //渠道 第三方替换
    public static final String IMEI = "FB71E11E82D686B526A2CC1BA3059C4F";
    public static final String IDFA = "";
    public static final String OAID = "FB71E11E82D686B526A2CC1BA3059C44";
    public static final String CAID = "OAIDE11E82D686B526A2CC1BA3059C44";
    public static final String ALID = "OAIDE11E82D686B526A2CC1BA3059C44";
    public static final String TIMESTAMP = "1587622979000";
    public static final String IP = "********";
    public static final String USER_AGENT = "Chrome";

    //智选查询替换

    //转化相关
    public static final String CLICK_TRACKING_LINK = "http://ad.youdao.com/r/impr?conv_ext=__conv__&ua=__ua__&oaid=__oaid__&caid=__caid__&aaid=__aaid__&ip=__ip__&order_amount=73&conv_action=test&conv_id=111";
    public static final int CT_TYPE = 1;
    public static final int CT_DEBUG_STATUS = 2;
    public static final String CT_ID = "111";

    //广告主相关（下面信息是快手的一个广告变体信息）
    public static final long SPONROR_ID = 1;
    public static final long CAMPAIGN_ID = 1053418;
    public static final long VARIATION_ID = 24550284;
    public static final long GROUP_ID = 6182534;
    public static final String APP_ID = "1";

//    public static final long SPONROR_ID = 308388;
//    public static final long CAMPAIGN_ID = 1050344;
//    public static final long VARIATION_ID = 24479968;
//    public static final long GROUP_ID = 6166512;
//    public static final String APP_ID = "10368";

    //智选输入替换

    /**
     * 落地页链接
     */
    public static final String DESTLINK = "https://translate.google.cn/#en/zh-CN";
    public static final long ACTIVITY_ID = 9012;
    public static final String CHANNEL_ID = "9012-12";
    /*
     * 是否重定向
     */
    public static final boolean REDIRECT = false;


    /**
     * @param landPage 是否为落地页链接
     * @param redirect 是否为同步链接(即是否会跳转)
     * @return
     */
    public static String buildZhiXuanUrl(boolean landPage, boolean redirect) {
        ConvTracker.Conv conv = mockConv(true);
        String conv_ext = Base64.getUrlEncoder().withoutPadding().encodeToString(conv.toByteArray());


        String dest = DESTLINK;
        // 如果是落地页链接需要添加转化信息
        if (landPage) {
            dest = UriComponentsBuilder.fromUriString(dest)
                    .queryParam("ext_youdao", redirect ? "__CONV_EXT__" : conv_ext)
                    .queryParam("conv_id", CT_ID).build().toString();
        }
        dest = Base64.getUrlEncoder().withoutPadding().encodeToString((dest).getBytes());


        return UriComponentsBuilder.fromUriString(CLK_HOST)
                .queryParam("conv_ext", conv_ext)
                .queryParam("dest", dest)
                .queryParam("sid", SPONROR_ID)
                .queryParam("aid", ACTIVITY_ID)
                .queryParam("redirect", redirect)
                .queryParam("ch", CHANNEL_ID)
                .queryParam("idfa", "__IDFA__")
                .queryParam("caid", "__CAID__")
                .queryParam("aaid", "__AAID__")
                .queryParam("imei", "__IMEI__")
                .queryParam("ip", "__IP__")
                .queryParam("ua", "__UA__")
                .queryParam("ts", "__TS__")
                .queryParam("callback", "__CALLBACK__")
                .build().toString();
    }


    /**
     * @param landPage 是否为落地页链接
     * @param redirect 是否为同步链接(即是否会跳转)
     * @return
     */
    public static String buildReqestUrl(boolean landPage, boolean redirect) {
        ConvTracker.Conv conv = mockConv(true);
        String conv_ext = Base64.getUrlEncoder().withoutPadding().encodeToString(conv.toByteArray());


        String dest = DESTLINK;
        // 如果是落地页链接需要添加转化信息
        if (landPage) {
            dest = UriComponentsBuilder.fromUriString(dest)
                    .queryParam("ext_youdao", redirect ? "__CONV_EXT__" : conv_ext).build().toString();
        }
        dest = Base64.getUrlEncoder().withoutPadding().encodeToString((dest).getBytes());


        return UriComponentsBuilder.fromUriString(CLK_HOST)
                .queryParam("conv_ext", conv_ext)
                .queryParam("dest", dest)
                .queryParam("sid", SPONROR_ID)
                .queryParam("aid", ACTIVITY_ID)
                .queryParam("redirect", redirect)
                .queryParam("ch", CHANNEL_ID)
                .queryParam("idfa", IDFA)
                .queryParam("caid", CAID)
                .queryParam("aaid", ALID)
                .queryParam("imei", IMEI)
                .queryParam("oaid", OAID)
                .queryParam("ip", IP)
                .queryParam("ua", USER_AGENT)
//                .queryParam("ts", TIMESTAMP)
                .queryParam("callback", URLEncoder.encode(CALLBACK))
                .build().toString();
    }

    public static ConvTracker.Conv mockConv(boolean zhixuan) {
        TrackerCommonDimension.ThirdPartyDimension.Builder builder = TrackerCommonDimension.ThirdPartyDimension
                .newBuilder()
                .setSponsorId(SPONROR_ID)
                .setGroupId(GROUP_ID)
                .setCampaignId(CAMPAIGN_ID)
                .setVariationId(VARIATION_ID)
                .setAppId(APP_ID);

        //在第三方上报时填入信息 ，这些事智选无法获取的
        if (!zhixuan) {
            builder.setActivityId(ACTIVITY_ID)
                    .setSponsorId(SPONROR_ID) //第三方上报的时候会填入SID， 但会被智选预设的覆盖。
                    .setImei(IMEI)
                    .setIdfa(IDFA)
                    .setCaid(CAID)
                    .setAlid(ALID)
                    .setOaid(OAID)
                    .setChannelId(CHANNEL_ID)
                    .setTimestamp(Long.parseLong(TIMESTAMP))
                    .setIp(IP)
                    .setUserAgent(USER_AGENT);
        }

        return ConvTracker.Conv.newBuilder()
                .setCtId(CT_ID)
                .setCtType(CT_TYPE)
                .setCtDebugStatus(CT_DEBUG_STATUS)
                .setClkTrackLink(CLICK_TRACKING_LINK)
                .setDestLink(DESTLINK)
                .setThirdPartyDimension(builder.build())
                .build();
    }

    public static void main(String[] args) {
        System.out.println("******** 智选链接 ******");
        System.out.println("下载型 同步链接 \n" + buildZhiXuanUrl(false, true));
        System.out.println("下载型 异步链接 \n" + buildZhiXuanUrl(false, false));
        System.out.println("落地页型 同步链接 \n" + buildZhiXuanUrl(true, true));
        System.out.println("落地页型 异步链接 \n" + buildZhiXuanUrl(true, false));


        System.out.println("******** 第三方监测请求链接 ******");
        System.out.println("下载型 同步链接 \n" + buildReqestUrl(false, true));
        System.out.println("下载型 异步链接 \n" + buildReqestUrl(false, false));
        System.out.println("落地页型 同步链接 \n" + buildReqestUrl(true, true));
        System.out.println("落地页型 异步链接 \n" + buildReqestUrl(true, false));
    }
}
