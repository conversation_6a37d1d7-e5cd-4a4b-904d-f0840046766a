package com.youdao.ead.antifraud;

import com.youdao.ead.antifraud.eval.ModelFilterEvaluator;
import com.youdao.ead.antifraud.eval.ParamFilterEvaluator;
import com.youdao.ead.antifraud.eval.TagRuleEvaluator;
import com.youdao.ead.dto.ReporterClickDTO;
import com.youdao.quipu.avro.schema.ThirdPartyClick;
import lombok.AllArgsConstructor;
import org.assertj.core.api.Assertions;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.BitSet;
import java.util.Collections;

import static com.youdao.ead.antifraud.AntiFraudTagEnum.UNEXPECTED_OS;
import static com.youdao.ead.validator.ParamFilledValidator.*;

/**
 * <AUTHOR>
 * @date 2024/11/25
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = AntiFraudService.class)
@Ignore
@AllArgsConstructor
public class EvalTest {
    private final TagRuleEvaluator tagRuleEvaluator;
    private final ParamFilterEvaluator paramFilterEvaluator;
    private final ModelFilterEvaluator modelFilterEvaluator;

    @Test
    public void evalTest() {
        ReporterClickDTO reporterClickDto = new ReporterClickDTO();
        ThirdPartyClick.Builder clickBuilder = ThirdPartyClick.newBuilder();
        AntiFraudContext context = new AntiFraudContext(reporterClickDto);

        context.getDealUnsolvedFraudRuleIdSet().set(UNEXPECTED_OS.getRuleId());
        Assertions.assertThat(tagRuleEvaluator.evaluateClick(context, UNEXPECTED_OS.getRuleId(), "", new ArrayList<>())).isEqualTo(true);

        reporterClickDto.setIdfaMd5("123");
        Assertions.assertThat(paramFilterEvaluator.evaluateClick(context, -1, "", Collections.singletonList(PARAM_IDFA_MD5))).isEqualTo(false);
        Assertions.assertThat(context.isBlankParamFiltered()).isEqualTo(false);
        Assertions.assertThat(paramFilterEvaluator.evaluateClick(context, -1, "", Arrays.asList(PARAM_IDFA, PARAM_CAID))).isEqualTo(true);
        Assertions.assertThat(context.isBlankParamFiltered()).isEqualTo(true);

        reporterClickDto.setMobileBrand("123");
        Assertions.assertThat(modelFilterEvaluator.evaluateClick(context, -1, "", Collections.singletonList("123"))).isEqualTo(true);
        Assertions.assertThat(context.isModelFiltered()).isEqualTo(true);
    }
}
