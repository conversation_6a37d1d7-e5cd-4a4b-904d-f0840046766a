package outfox.ead.data;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static outfox.ead.data.SdkSlotConverter.ShowConfirmDialogInDB;
import static outfox.ead.data.SdkSlotConverter.ShowConfirmDialogInResponse;

/**
 * <AUTHOR>
 */
public class SdkSlotConverterTest {
    @Test
    public void testConvertShowConfirmDialog() {
        Map<Integer, String> db2ad = new HashMap<Integer, String>();
        db2ad.put(ShowConfirmDialogInDB.SHOW_WHEN_NON_WIFI, ShowConfirmDialogInResponse.SHOW_WHEN_NON_WIFI);
        db2ad.put(ShowConfirmDialogInDB.NEVER_SHOW, ShowConfirmDialogInResponse.NEVER_SHOW);
        db2ad.put(ShowConfirmDialogInDB.ALWAYS_SHOW, ShowConfirmDialogInResponse.ALWAYS_SHOW);
        db2ad.put(3, ShowConfirmDialogInResponse.SHOW_WHEN_NON_WIFI);
        db2ad.put(-1, ShowConfirmDialogInResponse.SHOW_WHEN_NON_WIFI);

        for (Map.Entry<Integer, String> e : db2ad.entrySet()) {
            assertEquals(e.getValue(), SdkSlotConverter.convertShowConfirmDialog(e.getKey()));
        }
    }
}