package outfox.ead.gorgon.render.service;

import com.youdao.openrtb.OpenRtb;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.reflect.Whitebox;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static outfox.ead.gorgon.render.SdkAdResponseAttributes.*;

public class VastServiceTest {

    @Test
    public void testExtractVastAds() throws Exception {
        OpenRtb.BidResponse.SeatBid.Bid bid = OpenRtb.BidResponse.SeatBid.Bid.newBuilder()
                .setId("1")
                .setImpid("!")
                .setPrice(1.2)
                .setAdm("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                        "<VAST version=\"2.0\">\n" +
                        "   <Ad id=\"3456763\">\n" +
                        "      <InLine>\n" +
                        "         <AdSystem>mDSP</AdSystem>\n" +
                        "         <AdTitle>VAST Tag</AdTitle>\n" +
                        "         <Impression id=\"mdsp\"><![CDATA[http://adserver.com?Impression1]]></Impression>\n" +
                        "         <Impression id=\"mdsp\"><![CDATA[http://adserver.com?Impression2]]></Impression>\n" +
                        "         <Creatives>\n" +
                        "            <Creative>\n" +
                        "               <Linear>\n" +
                        "                  <Duration>00:00:30.100</Duration>\n" +
                        "                  <VideoClicks>\n" +
                        "                     <ClickThrough>http://adserver.com?ClickThrough</ClickThrough>\n" +
                        "                     <ClickTracking><![CDATA[http://adserver.com?ClickTracking1]]></ClickTracking>\n" +
                        "                     <ClickTracking><![CDATA[http://adserver.com?ClickTracking2]]></ClickTracking>\n" +
                        "                  </VideoClicks>\n" +
                        "                  <MediaFiles>\n" +
                        "                     <MediaFile delivery=\"progressive\" type=\"video/mp4\" bitrate=\"300\" width=\"400\" height=\"250\" size=\"201809041520666\">http://adserver.mp4</MediaFile>\n" +
                        "                  </MediaFiles>\n" +
                        "                  <TrackingEvents>\n" +
                        "                     <Tracking event=\"mute\"><![CDATA[http://adserver.com?mute]]></Tracking>\n" +
                        "                     <Tracking event=\"unmute\"><![CDATA[http://adserver.com?unmute]]></Tracking>\n" +
                        "                     <Tracking event=\"firstQuartile\"><![CDATA[http://adserver.com?firstQuartile]]></Tracking>\n" +
                        "                     <Tracking event=\"midpoint\"><![CDATA[http://adserver.com?midpoint]]></Tracking>\n" +
                        "                     <Tracking event=\"thirdQuartile\"><![CDATA[http://adserver.com?thirdQuartile]]></Tracking>\n" +
                        "                     <Tracking event=\"complete\"><![CDATA[http://adserver.com?complete]]></Tracking>\n" +
                        "                  </TrackingEvents>\n" +
                        "               </Linear>\n" +
                        "            </Creative>\n" +
                        "         </Creatives>\n" +
                        "      </InLine>\n" +
                        "   </Ad>\n" +
                        "</VAST>")
                .build();
        OpenRtb.NativeResponse.Link link = OpenRtb.NativeResponse.Link.newBuilder()
                .setUrl("app://adserver?params=xxx")
                .setFallback("http://adserver.com?ClickThrough")
                .build();

        VastService vastService = new VastService();
        List<Map<String, Object>> ads = Whitebox.invokeMethod(vastService, "extractVastAds", bid.getAdm(), link);

        Assert.assertEquals(1, ads.size());
        Assert.assertEquals(Arrays.asList("http://adserver.com?Impression1", "http://adserver.com?Impression2"), ads.get(0).get(IMP_TRACKER));
        Assert.assertEquals("http://adserver.com?ClickTracking1", ads.get(0).get(CLK_TRACKER));
        Assert.assertEquals(Arrays.asList("http://adserver.com?ClickTracking1", "http://adserver.com?ClickTracking2"), ads.get(0).get(CLK_TRACKERS));
        Assert.assertEquals("http://adserver.com?ClickThrough", ads.get(0).get(CLK));
        Assert.assertEquals("00:00:30.100", ads.get(0).get(VIDEO_DURATION));
        Assert.assertEquals("http://adserver.mp4", ads.get(0).get(VIDEO_URL));
        Assert.assertEquals(250, ads.get(0).get(VIDEO_HEIGHT));
        Assert.assertEquals(400, ads.get(0).get(VIDEO_WIDTH));
        Assert.assertEquals(201809041520666L, ads.get(0).get(VIDEO_SIZE));
        @SuppressWarnings("unchecked")
        final Map<String, Object> playTrackers = (Map<String, Object>) ads.get(0).get(VIDEO_PLAY_TRACKERS_KEY);
        Assert.assertEquals(Collections.singletonList("http://adserver.com?mute"), playTrackers.get(VIDEO_MUTE_KEY));
        Assert.assertEquals(Collections.singletonList("http://adserver.com?unmute"), playTrackers.get(VIDEO_UN_MUTE_KEY));
        @SuppressWarnings("unchecked")
        final List<Map<String, Object>> playPercentages = (List<Map<String, Object>>) playTrackers.get(VIDEO_PERCENTAGE_KEY);
        Assert.assertEquals(4, playPercentages.size());
        Assert.assertEquals(0.25, playPercentages.get(0).get(VIDEO_PERCENT_CHECKPOINT_KEY));
        Assert.assertEquals(Collections.singletonList("http://adserver.com?firstQuartile"), playPercentages.get(0).get(VIDEO_PERCENT_URLS_KEY));
    }
}
