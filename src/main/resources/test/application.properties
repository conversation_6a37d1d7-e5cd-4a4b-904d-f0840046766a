server.port=11551
server.env=test

spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true

#server.tomcat.basedir=access-log
#server.tomcat.accesslog.enabled=true
#server.tomcat.accesslog.pattern=%t %a "%r" %s (%D ms) "%{Referer}i" "%{User-Agent}i"
#server.tomcat.accesslog.max-days=3

redis.uri=redis://<EMAIL>:6379
redis.timeout=80

hbase.site.xml.location=/mfs_ead/eadata/online/hadoop-conf/hbase-site.xml
hdfs.site.xml.location=/mfs_ead/eadata/online/hadoop-conf/hdfs-site.xml
hbase_table_name=third_party_callback_test
hbase.client.operation.timeout=1200000
hbase.rpc.timeout=60000
hbase.client.retries.number=3

third-party-reporter.callback.broker.list=ead-sandbox-kafka.inner.youdao.com:9092
third-party-reporter.callback.topic=third_party_callback
third-party-reporter.callback.group.id=ead.third_party_reporter.consumer
third-party-reporter.callback.concurrency=3
third-party-reporter.callback.consumer=disable
# 默认500
third-party-reporter.callback.max-poll-records=100

management.endpoints.web.exposure.include=health
management.endpoint.health.show-details=never
management.endpoints.enabled-by-default=false
management.endpoint.health.enabled=true

spring.datasource.eadb1.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.eadb1.url=**************************************************
spring.datasource.eadb1.username=eadonline4nb
spring.datasource.eadb1.password=new1ife4Th1sAugust
spring.datasource.eadb1.hikari.maximum-pool-size=20
spring.datasource.eadb1.hikari.minimum-idle=5
spring.datasource.eadb1.hikari.max-lifetime=27000000


spring.datasource.channel.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.channel.url=****************************************************
spring.datasource.channel.username=eadonline4nb
spring.datasource.channel.password=new1ife4Th1sAugust
spring.datasource.channel.hikari.maximum-pool-size=20
spring.datasource.channel.hikari.minimum-idle=5
spring.datasource.channel.hikari.max-lifetime=27000000

spring.jackson.deserialization.accept-single-value-as-array=true

redis.conv-ext.prefix=third-party-test:conv-ext:
redis.conv-ext.last-sync-time=third-party-test:conv-ext:last-sync-time
redis.secret-key.prefix=third-party-test:secret-key:
redis.debug-conv.prefix=third-party-test:debug-conv:
redis.third-config.prefix=third-party-test:third-config:
redis.vivo-auth-code.prefix=third-party-test:vivo-auth-code:
redis.vivo-access-token.prefix=third-party-test:vivo-access-token:
redis.conv-ext.ttl=30

vivo.accessTokenUrl=https://marketing-api.vivo.com.cn/openapi/v1/oauth2/token
vivo.refreshTokenUrl=https://marketing-api.vivo.com.cn/openapi/v1/oauth2/refreshToken

xxl-job-core.admin.addresses=https://ead-xxl-job-admin-test.inner.youdao.com/xxl-job-admin
xxl-job-core.executor.app-name=third-party-reporter-test
xxl-job-core.executor.logPath=applogs/xxl-job/jobhandler

# central_dogma 相关配置
central-dogma-hosts=test-centraldogma.inner.youdao.com
central-dogma-port=80
central-dogma-proj=third-party-reporter
central-dogma-repo=test
central-dogma-config-debug-device=/debug_device
central-dogma-config-rta-proj=bid-server
central-dogma-config-rta-repo=rta_v2
central-dogma-config-tanx-rta-proj=bid-server
central-dogma-config-tanx-rta-repo=tanx_rta
central-dogma-config-tanx-rta-file=/config-test-v2
central-dogma-config-click-rta=/click_rta_config.json
central-dogma-config-app-id-whitelist=/app-id-whitelist.json
central-dogma-config-callback-storage=/callback-storage-config.json
central-dogma-config-rta-api-entry=/rta-api-config.json
central-dogma-config-sampling-log=/sampling-log-v2.json
central-dogma-config-invalid-param-values=/invalid-param-values.json
central-dogma-config-click-strategy=/click-strategy.json
central-dogma-config-anti-fraud=/anti-fraud.json

third-party.rta.async=true
third-party.rta.recordTopic=rta_record_dev
third-party.rta.maxConnect=200
third-party.rta.maxPerRoute=100
third-party.rta.connectTimeout=1000
third-party.rta.readTimeout=60
third-party.rta.apiRtaConnectTimeout=1000
third-party.rta.apiRtaReadTimeout=300

tanx.rta.apiUrl=http://videoproxy.tanx.com/rta
tanx.rta.apiVersion=1.0
tanx.rta.androidPid=mm_26632544_2364850103_111476000347
tanx.rta.iosPid=mm_26632544_2364850103_111475350372
tanx.rta.recordTopic=rta_record
tanx.rta.maxConnect=200
tanx.rta.maxPerRoute=100
tanx.rta.connectTimeout=1000
tanx.rta.readTimeout=60

third-party.rta.recordBidTopic=third_rta_bid_jar_dev
third-party.rta.ttl=1800
third-party.rta.device.ttl=86400
rta.redis.uri=redis://<EMAIL>:6379
rta.redis.timeout=30

rta.cache.redis.uri=redis://<EMAIL>:6379
rta.cache.redis.timeout=30

oppo.rta.secretKey=123456

anti-fraud.redis.uri=redis://<EMAIL>:6379
anti-fraud.redis.timeout=30
anti-fraud.repeat-click.expire.sec=120
anti-fraud.diff-aid-click.expire.sec=60
anti-fraud.diff-click.expire.sec=7200
anti-fraud.ip-rate-click.expire.sec=60

device-info-click.kvrocks.uri=redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167
device-info-click.kvrocks.timeout=30
device-info-rta.kvrocks.uri=redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167
device-info-rta.kvrocks.timeout=10

device-standard-info.kvrocks.uri=redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167
device-standard-info.kvrocks.timeout=30

spring.cloud.sentinel.transport.dashboard=ead-sentinel-dashboard-test.inner.youdao.com
spring.cloud.sentinel.log.dir=logs/sentinel

# DSL
cache_port=2518
zookeeper_hosts=ad1-test-01.zookeeper.yodao.cn:2181,ad1-test-02.zookeeper.yodao.cn,ad1-test-03.zookeeper.yodao.cn

s3.accessKey=2627af6f-2822-927c-6fc8-d4a33890c1d2
s3.secretKey=NlIRzRxDtQIbvrhSZeiPm2ws8atefJCN
s3.entpoint=https://yds3-infra-arch.inner.youdao.com
s3.bucketName=givt