<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="logbase" value="logs"/>
    <!-- 按照每天生成日志文件 -->
    <appender name="accessLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${logbase}/access.log.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>512MB</maxFileSize>
            <MaxHistory>6</MaxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.access.PatternLayoutEncoder">
            <pattern>%h "%i{X-Forwarded-For}" %l %u [%t] "%r" %s %b "%i{Referer}" "%i{User-Agent}"</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender-ref ref="accessLog" />
</configuration>