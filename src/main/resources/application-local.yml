spring:
  jpa:
    hibernate:
      ddl-auto: none
  datasource:
    pacioli:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: eadonline4nb
      password: new1ife4Th1sAugust
      jdbc-url: ************************************************************************************************************************************************************************************************************
    eadb1:
      jdbc-url: ******************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
      driver-class-name: com.mysql.cj.jdbc.Driver
    crm:
      jdbc-url: ****************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
    adsales:
      jdbc-url: *******************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
  data:
    mongodb:
      host: localhost:27017
      database: pacioli_local

sentry:
  environment: local

logging:
  config: classpath:log4j2-local.xml