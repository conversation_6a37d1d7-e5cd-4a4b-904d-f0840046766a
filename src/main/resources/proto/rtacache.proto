syntax = "proto2";
package outfox.ead.data.rta;
option java_outer_classname = "RtaCache";

// 没有req_id参数的rta请求，包含一些直投平台，缓存数据key可用平台id+设备id+aid
message Data {
  // rta请求发生的时间
  required int64 timestamp = 1;
  // rta请求下发的广告位id
  optional int64 slot_id = 2;
  // 设备上次参竞时的请求id
  optional string req_id = 3;
  // rta请求信息压缩后的格式
  optional CompressRtaRequest compress_rta_request = 4;
}

// 有req_id参数的rta请求，缓存数据key可用req_id
message DataV2 {
  // rta请求发生的时间
  required int64 timestamp = 1;
  // 参竞的aid信息
  repeated AidBId aidBid = 2;
  // rta请求信息压缩后的格式
  optional CompressRtaRequest compress_rta_request = 3;
  // 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
  optional int64 slotId = 4;
}

message CompressRtaRequest {
  optional int32 imei = 1;
  optional int32 imeiMd5 = 2;
  optional int32 oaid = 4;
  optional int32 oaidMd5 = 5;
  optional int32 idfa = 6;
  optional int32 idfaMd5 = 7;
  repeated int32 caidAndVersion = 8;
  repeated int32 caidMd5AndVersion = 9;
  optional int32 ip = 11;
  optional int32 ua = 12;
  optional int32 model = 13;
  optional int32 osv = 14;
}

message AidBId {
  required int64 aid = 1;
  // rta请求下发的广告位id
  optional int64 slot_id = 2;
}

message Result {
  // rta请求下发的id
  optional string req_id = 1;
  // 是否参竞
  required bool bid = 2;
  // 参竞的广告主id
  repeated string sponsor_ids = 3;
  // 参竞的rta策略id
  repeated  string rta_ids = 4;
}