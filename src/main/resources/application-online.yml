springdoc:
  swagger-ui:
    enabled: false
  api-docs:
    enabled: false
sentry:
  environment: online
spring:
  datasource:
    pacioli:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: pacioli_rw
      password: Tvnc0kj0qGfpfNhkvOc2
      jdbc-url: ******************************************************************************************************************************************************************************************************************************
    eadb1:
      jdbc-url: *************************************************************************************************************************************
      username: ead
      password: ea89,d24
    crm:
      jdbc-url: ***********************************************************************************************************************************
      username: ead
      password: ea89,d24
    adsales:
      jdbc-url: ***************************************************************************************************************************************
      username: ead
      password: ea89,d24
    youdata:
      jdbc-url: *********************************************************************************************************************************************************************************************************************************************
      username: channel_youdata_report_rw
      password: ThRcp1iraXrcnrMczKxE
  data:
    mongodb:
      uri: mongodb://pacioli_rw:<EMAIL>:27018,zj042.corp.yodao.com:27018,zj043.corp.yodao.com:27018/pacioli

xxl-job-core:
  admin:
    addresses: https://ead-xxl-job-admin.inner.youdao.com/xxl-job-admin
  executor:
    app-name: pacioli
    log-path: /pacioli/logs
    log-retention-days: 7