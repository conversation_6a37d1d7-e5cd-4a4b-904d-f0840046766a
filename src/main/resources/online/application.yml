spring:
  application:
    name: third-party-data-pool
#  kafka:
#    consumer:
#      auto-commit-interval: 1000
#      max-poll-records: 10
#      key-deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
#      bootstrap-servers: ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666
#      group-id: third-party-data-pool-group
#    listener:
#      concurrency: 20
#      type: batch
central_dogma:
  hosts: central-dogma1-inner.yodao.com,central-dogma2-inner.yodao.com,central-dogma3-inner.yodao.com
  port: 36462
  cd_project: third-party-reporter
  cd_repo: online
  cd_conf: /invalid-param-values.json
thirdPartyKafka:
  topic:
    click: click_third_party
    rta: rta_record
  groupId:
    click_guizhou: clickListener
    rta_tianjin: rtaForTianjinListener
    rta_guizhou: rtaForGuizhouListener
  servers: ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666
  schema-repository: http://eadata-schema-repo.inner.youdao.com/schema-repo
deviceKvrocks:
  guizhou-click:
    uri: redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167
    timeout: 200
  tianjin-rta:
    uri: redis://***********:6166,redis://***********:6167,redis://***********:6168,redis://***********:6166,redis://***********:6167,redis://***********:6168
    timeout: 200
  guizhou-rta:
    uri: redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167,redis://************:6166,redis://************:6167
    timeout: 200