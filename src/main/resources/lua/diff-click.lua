--- 判断是否为跨渠道上报的点击，参数如下
--- KEYS[1] deviceId
--- ARGV[1] ttl
--- ARGV[2] activityId
--- ARGV[3] sponsorId
--- ARGV[4] channelDid
--- ARGV[5] productId

local key = KEYS[1]           -- 主键
local ttl = tonumber(ARGV[1]) -- 过期时间（秒），转为数字

-- 定义后缀列表 a:activity_id, s:sponsor_id, c:channel_did, p:product_id
local suffixes = {"a", "s", "c", "p"}
if #ARGV - 1 ~= #suffixes then
    return redis.error_reply("Number of values must match number of suffixes")
end

-- 获取当前时间（以秒为单位）
local current_time = redis.call('TIME')[1]
-- 计算过期时间阈值
local expire_threshold = current_time - ttl

-- 存储每个后缀的结果
local results = {}

-- 对每个后缀执行相同的逻辑
for i, suffix in ipairs(suffixes) do
    local value = ARGV[i + 1]  -- 按顺序获取对应的 value（从 ARGV[2] 开始）
    if value == "" or value == "null" then
        results[i] = -1
    else
        local time_key = '{' .. key .. '}:' .. suffix
        -- 获取该后缀的所有 value 的时间记录
        local time_map = redis.call('HGETALL', time_key)

        -- 更新当前 value 的时间
        redis.call('HSET', time_key, value, current_time)
        redis.call('EXPIRE', time_key, ttl)

        -- 如果这是第一次执行（time_map 为空），结果为 -1
        if next(time_map) == nil then
            results[i] = -1
        else
            -- 检查并删除过期 value，同时计算最小时间差
            local min_diff = nil
            for j = 1, #time_map, 2 do
                local stored_value = time_map[j]      -- 哈希的 key（value）
                local stored_time = tonumber(time_map[j + 1])  -- 哈希的 value（时间戳）

                -- 如果时间早于 ttl 前，删除该 value
                if stored_time < expire_threshold then
                    redis.call('HDEL', time_key, stored_value)
                elseif stored_value ~= value then
                    -- 只对未过期的其他 value 计算时间差
                    local diff = current_time - stored_time
                    if min_diff == nil or diff < min_diff then
                        min_diff = diff
                    end
                end
            end

            -- 如果没有其他未过期的 value，结果为 -1
            if min_diff == nil then
                results[i] = -1
            else
                results[i] = min_diff * 1000
            end
        end
    end

end

-- 返回结果数组
return results
