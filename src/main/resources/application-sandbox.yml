# 与test环境一致

spring:
  datasource:
    pacioli:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: eadonline4nb
      password: new1ife4Th1sAugust
      jdbc-url: ****************************************************************************************************************************************************************************************************************************
    eadb1:
      jdbc-url: **********************************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
      driver-class-name: com.mysql.cj.jdbc.Driver
    crm:
      jdbc-url: ********************************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
    adsales:
      jdbc-url: ************************************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
    youdata:
      jdbc-url: ***************************************************************************************************************************************************
      username: eadonline4nb
      password: new1ife4Th1sAugust
  data:
    mongodb:
      host: zj131.corp.yodao.com:27017
      database: pacioli_test