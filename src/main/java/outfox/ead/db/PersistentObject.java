package outfox.ead.db;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

import odis.serialize.IWritable;

/**
 * 持久化类
 * <p>
 * 能够被持久化写入数据库的类，具有long型的id属性。
 * <p>
 * 
 * <AUTHOR>
 */
public abstract class PersistentObject implements IWritable {

    protected long id;

    public long getId() {
        return id;
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        id = in.readLong();
    }

    @Override
    public void writeFields(DataOutput out) throws IOException {
        out.writeLong(id);
    }

    @Override
    public IWritable copyFields(IWritable value) {        
        return null;
    }

}
