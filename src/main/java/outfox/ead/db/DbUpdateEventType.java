/**
 * @(#)DbUpdateEventType.java, 2009-1-9. Copyright 2009 Yodao, Inc. All rights
 *                             reserved. YODAO PROPRIETARY/CONFIDENTIAL. Use is
 *                             subject to license terms.
 */
package outfox.ead.db;

/**
 * <AUTHOR>
 */
public enum DbUpdateEventType {
    // AdCampiagn
    //
    AD_CAMPAIGN_ADD_CONSUMPTION(101),
    //
    AD_CAMPAIGN_INSERT(102),
    //
    AD_CAMPAIGN_UPDATE_DAY_CONSUMPTION(103),
    //
    AD_CAMPAIGN_UPDATE_TO_DB(104),
    //
    AD_CAMPAIGN_GET(105),
    //
    AD_CAMPAIGN_UPDATE_OBJECT(106),
    //
    AD_CAMPAIGN_RESET_ALL_DAY_CONSUMPTION(107),

    // AdPlan
    //
    AD_PLAN_INSERT(201),
    //
    AD_PLAN_UPDATE_BUDGET_TO_DB(202),
    //
    AD_PLAN_UPDATE_DAILY_BUDGET(203),
    //
    AD_PLAN_UPDATE_TO_DB(204),
    //
    AD_PLAN_UPDATE_TO_DB_EXCLUDE_BUDGET(205),
    //
    AD_PLAN_GET(206),
    //
    AD_PLAN_UPDATE_OBJECT(207),
    //
    AD_PLAN_UPDATE_VIRTUAL_BUDGET(208),
    //
    AD_PLAN_UPDATE_VIRTUAL_DELIVERY_TYPE(209),

    // Sponsor
    SPONSOR_INSERT(301),
    //
    SPONSOR_UPDATE_TO_DB(302),
    //
    SPONSOR_GET(303),
    //
    SPONSOR_UPDATE_OBJECT(304),

    // AdVariation
    AD_VARIATION_INSERT(401),
    //
    AD_VARIATION_UPDATE_STATUS(402),
    //
    AD_VARIATION_UPDATE_TO_DB(403),
    //
    AD_VARIATION_GET(404),
    //
    AD_VARIATION_UPDATE_OBJECT(405),

    // Keyword
    KEYWORD_INSERT(501),
    //
    KEYWORD_UPDATE_STATUS(502),
    //
    KEYWORD_UPDATE_TO_DB(503),
    //
    KEYWORD_UPDATE_BIDDING_STATUS(504),
    //
    KEYWORD_GET(505),
    //
    KEYWORD_UPDATE_OBJECT(506),

    // AdGroup
    AD_GROUP_ADD_CONSUMPTION(601),
    //
    AD_GROUP_INSERT(602),
    //
    AD_GROUP_UPDATE_DAY_CONSUMPTION(603),
    //
    AD_GROUP_UPDATE_STATUS(604),
    //
    AD_GROUP_UPDATE_TO_DB(605),
    //
    AD_GROUP_DISABLE_BY(606),
    //
    AD_GROUP_ENABLE_BY(607),
    //
    AD_GROUP_RESET_ALL_DAY_CONSUMPTION(608),
    //
    AD_GROUP_GET(609),
    //
    AD_GROUP_UPDATE_OBJECT(610),

    // ProductAccount
    // 更新广告在广告平台的消费
    AD_PRODUCT_DAY_CONSUMPTION_INSERT(701),
    // 重置（清零）
    AD_PRODUCT_DAY_CONSUMPTION_RESSET(702),

    // AdGroupDeliveryInfo
    AD_GROUP_DELIVERY_INFO_RESET_STATUS(801),

    //
    NULL(0);

    private int value;

    private DbUpdateEventType(int value) {
        this.value = value;
    }

    public int intValue() {
        return value;
    }
}
