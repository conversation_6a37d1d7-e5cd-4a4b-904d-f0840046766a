package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.Element;
import outfox.ead.youxuan.mapper.youxuan.ElementMapper;
import outfox.ead.youxuan.web.ad.controller.vo.ElementListVO;
import outfox.ead.youxuan.web.ad.controller.vo.ElementVO;
import outfox.ead.youxuan.web.ad.service.ElementService;

import java.util.*;

import static outfox.ead.youxuan.constants.Constants.STATUS_VALID;

/**
 * <AUTHOR>
 */
@Service
public class ElementServiceImpl extends YouxuanServiceImpl<ElementMapper, Element> implements ElementService {
    @Override
    public List<ElementListVO> getValidByType(Integer type) {
        // 根据key对元素做聚合
        Map<String, ElementListVO> keyToElementListVO = new HashMap<>();
        List<Element> list = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(Element::getType, type)
                .eq(Element::getStatus, STATUS_VALID)
                .list();

        for (Element element : list) {
            keyToElementListVO.computeIfAbsent(element.getElementKey(), k -> {
                ElementListVO listVO = new ElementListVO();
                listVO.setElementKey(element.getElementKey());
                listVO.setName(element.getName());
                listVO.setType(element.getType());
                listVO.setElements(new ArrayList<>());
                return listVO;
            }).getElements().add(new ElementVO(element));
        }
        ArrayList<ElementListVO> res = new ArrayList<>(keyToElementListVO.values());
        Collections.sort(res);
        return res;
    }
}




