package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.entity.YexDsp;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface YexDspService extends YouxuanService<YexDsp> {
    /**
     * 获取dsp id2Name，如果是多主图样式只查支持多主图的dsp
     *
     * @param multipleMainImage
     * @return dsp列表
     */
    List<YexDsp> listId2DspName(Boolean multipleMainImage);

    /**
     * 根据dsps查询有效的dsp数量
     *
     * @param dsps dspId
     * @return 数量
     */
    long countValidById(String dsps);
}
