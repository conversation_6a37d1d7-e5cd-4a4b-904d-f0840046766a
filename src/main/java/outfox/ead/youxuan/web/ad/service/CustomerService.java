package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.entity.Customer;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface CustomerService extends YouxuanService<Customer> {

    /**
     * 通过名字查询用户
     *
     * @param customerName 用户名字
     * @return 用户
     */
    Customer getByName(String customerName);

    /**
     * 通过名字模糊查询
     *
     * @param customerName 客户名称
     * @return 客户列表
     */
    List<Customer> listLikeName(String customerName);

    /**
     * 客户名称是否重复
     *
     * @param name 客户名称
     * @return true-重复 false-不重复
     */
    Boolean nameRepeat(String name);

    /**
     * 获取客户id name 列表
     *
     * @return 客户列表
     */
    List<Customer> listIdToName();
}
