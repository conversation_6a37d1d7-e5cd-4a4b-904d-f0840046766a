package outfox.ead.youxuan.web.ad.service.impl;

import com.alibaba.excel.util.BooleanUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.annotation.DistributedLock;
import outfox.ead.youxuan.core.annotation.DistributedLockKey;
import outfox.ead.youxuan.core.config.security.JwtSecurityToken;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.AppAccount;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.mapper.youxuan.UserMapper;
import outfox.ead.youxuan.util.MailTemplateUtil;
import outfox.ead.youxuan.util.MailUtil;
import outfox.ead.youxuan.web.ad.controller.bo.ActiveBindToken;
import outfox.ead.youxuan.web.ad.service.UserService;
import outfox.ead.youxuan.web.kol.service.AppAccountService;

import javax.annotation.Nonnull;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static outfox.ead.youxuan.constants.Constants.TOKEN_BIND_MAIL;
import static outfox.ead.youxuan.constants.PlatformEnum.YOUDAO_DICT;
import static outfox.ead.youxuan.constants.ResponseType.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Validated
public class UserServiceImpl extends YouxuanServiceImpl<UserMapper, User> implements UserService {
    private final AppAccountService appAccountService;
    private final RedisTemplate<String, Object> redisTemplate;
    @Value("${youxuan.web}")
    private String youxuanWebBaseUrl;
    @Value("${expired}")
    private int expired;

    @Override
    public User getUserByUsername(String username) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(User::getUsername, username)
                .eq(User::getStatus, 1)
                .one();
    }

    @Override
    public void lockUser(List<Long> userIds) {
        baseMapper.lockUser(userIds);
    }


    @Override
    public Boolean exsistById(Long id) {
        return baseMapper.exsitsById(id);
    }

    @Override
    public User getUserByDictUid(String uid) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .eq(User::getDictUid, uid)
                .eq(User::getStatus, 1)
                .one();
    }

    @Override
    public void unbindUsername(Long userId) {
        unbindUsernamePreCheck(userId);
        new LambdaUpdateChainWrapper<>(baseMapper)
                .set(User::getUsername, null)
                .eq(User::getId, userId)
                .update();
    }

    private void unbindUsernamePreCheck(Long userId) {
        User user = getById(userId);
        if (Objects.isNull(user.getDictUid())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "该账户未绑定其余登陆方式，不支持解绑");
        }
    }


    @Override
    public void unbindDictUid(Long userId) {
        unbindDictUidPreCheck(userId);
        new LambdaUpdateChainWrapper<>(baseMapper)
                .set(User::getDictUid, null)
                .eq(User::getId, userId)
                .update();
    }

    private void unbindDictUidPreCheck(Long userId) {
        User user = getById(userId);
        if (Objects.isNull(user.getUsername())) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "该账户未绑定其余登陆方式，不支持解绑");
        }
        if (appAccountService.isBindDict(userId)) {
            throw new CustomException(INVALID_PARAMETERS, "该优选账户绑定了有道词典媒体，请先在【账户绑定】处解绑媒体后，再解绑登录账号。");
        }
    }

    @Override
    public void bindByUsername(Long userId, String username) {
        User user = getById(userId);
        if (Objects.nonNull(user.getUsername())) {
            throw new CustomException(INVALID_PARAMETERS, "请先解绑");
        }
        this.lambdaUpdate()
                .set(User::getUsername, username)
                .eq(User::getId, userId)
                .update();
    }

    @Override
    @DistributedLock(namespace = "appAccount")
    public void bindByDictUid(Long userId, @DistributedLockKey String uid) {
        bindByDictUidPreCheck(userId, uid);
        this.lambdaUpdate()
                .set(User::getDictUid, uid)
                .eq(User::getId, userId)
                .update();
    }

    private void bindByDictUidPreCheck(Long userId, String uid) {
        AppAccount appAccount = appAccountService.getBindByPlatformNameAndAppUserId(YOUDAO_DICT.getName(), uid);
        if (Objects.nonNull(appAccount) && !appAccount.getUserId().equals(userId)) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "该有道词典账户已注册其他优选账户");
        }
        User user = getUserByDictUid(uid);
        if (Objects.nonNull(user)) {
            if (!user.getId().equals(userId)) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "该有道词典账户已注册其他优选账户");
            }else{
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "该有道词典账户已绑定当前优选账户，无需重复注册");
            }
        }
    }


    @Override
    public User registerByMail(String username) {
        User user = getUserByUsername(username);
        if (Objects.isNull(user)) {
            user = new User();
            user.setUsername(username);
            save(user);
        }
        return user;
    }

    @Override
    public List<User> listByRoleKey(String roleKey) {
        return baseMapper.listByRoleKey(roleKey);
    }

    @Override
    public void sendActivationBindEmail(@Nonnull String email, Long userId) {
        User user = getUserByUsername(email);
        if (user != null) {
            throw new CustomException(INVALID_PARAMETERS, "该邮箱已被绑定");
        }
        String key = "youxuan_send_bind_account_email_" + email;
        if (BooleanUtils.isNotTrue(redisTemplate.opsForValue().setIfAbsent(key, "null", 60, TimeUnit.SECONDS))) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "操作太过频繁，请在" + redisTemplate.getExpire(key) + "s后重试");
        }
        try {
            MailUtil.sendMail(email, "有道优选邮箱绑定邮件", generateContent(userId, email));
        } catch (Exception e) {
            throw new CustomException(SERVICE_ERROR, "发送邮件失败");
        }
    }

    private String generateContent(Long id,String email) {
        String token = JwtSecurityToken.generateActiveToken(id, null, email, TOKEN_BIND_MAIL, expired);
        redisTemplate.opsForValue().set("youxuan_bind_account_" + email, token, Duration.ofSeconds(expired));
        return MailTemplateUtil.getActiveRegisterMailContent(email, youxuanWebBaseUrl + String.format("api/emailBind?activeToken=%s", token), "立即绑定");
    }

    @Override
    public String activeBindAccount(String activeBindToken) {
        ActiveBindToken token = JwtSecurityToken.of(activeBindToken, ActiveBindToken.class);
        String key = "youxuan_bind_account_" + token.getUsername();
        String s = (String) redisTemplate.opsForValue().get(key);
        if (Objects.isNull(s) || !s.equals(activeBindToken)) {
            throw new CustomException(TOKEN_INVALID);
        }
        if (token.getSub().equals(TOKEN_BIND_MAIL)) {
            bindByUsername(token.getId(), token.getUsername());
        } else {
            throw new CustomException(INVALID_PARAMETERS);
        }
        redisTemplate.delete(key);
        return token.getUsername();
    }
}
