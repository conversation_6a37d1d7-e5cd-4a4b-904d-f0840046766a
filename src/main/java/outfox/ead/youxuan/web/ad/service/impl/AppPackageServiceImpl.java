package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import outfox.ead.youxuan.entity.AppPackage;

import outfox.ead.youxuan.mapper.youxuan.AppPackageMapper;
import outfox.ead.youxuan.web.ad.service.AppPackageService;

import java.util.List;

@Service
public class AppPackageServiceImpl extends ServiceImpl<AppPackageMapper, AppPackage>
        implements AppPackageService {
    @Override
    public List<AppPackage> listValid() {
        return baseMapper.listValid();
    }
}
