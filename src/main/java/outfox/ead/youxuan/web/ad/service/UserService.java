package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.entity.User;

import javax.annotation.Nonnull;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface UserService extends YouxuanService<User> {
    /**
     * 通过用户名获取用户信息
     *
     * @param username 用户名，目前只支持163邮箱
     * @return 用户信息
     */
    User getUserByUsername(String username);

    /**
     * 施加行锁
     *
     * @param userIds 用户id列表
     */
    void lockUser(List<Long> userIds);

    /**
     * 是否存在有效的优选账户
     * @param id userId
     * @return true-存在
     */
    Boolean exsistById(Long id);

    User getUserByDictUid(String uid);

    void unbindUsername(Long userId);

    void unbindDictUid(Long userId);

    void bindByUsername(Long userId, String username);

    void bindByDictUid(Long userId, String uid);

    User registerByMail(String username);

    List<User> listByRoleKey(String roleKey);

    void sendActivationBindEmail(@Nonnull String email, Long userId);

    /**
     * 激活绑定邮箱
     * @param activeBindToken -
     * @return 绑定的邮箱账号
     */
    String activeBindAccount(String activeBindToken);
}
