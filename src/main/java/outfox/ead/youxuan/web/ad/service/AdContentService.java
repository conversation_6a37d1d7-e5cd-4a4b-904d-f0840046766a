package outfox.ead.youxuan.web.ad.service;

import outfox.ead.youxuan.entity.AdContent;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdContentService extends YouxuanService<AdContent> {
    /**
     * 通过推广组id列表查询AdContent
     *
     * @param adGroupIds 推广组id列表
     * @return List
     */
    List<AdContent> listByAdGroupIds(List<Long> adGroupIds);

    /**
     * 通过广告组id查询广告内容
     *
     * @param adGroupId 广告组id
     * @return 广告内容列表
     */
    List<AdContent> listValidByAdGroupId(Long adGroupId);

    /**
     * 通过组id删除content
     *
     * @param id 推广组id
     */
    void removeByAdGroupId(Long id);

    AdContent getByGroupIdAndType(Long id, Integer type);
}
