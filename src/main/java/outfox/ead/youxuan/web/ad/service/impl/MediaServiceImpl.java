package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.*;
import outfox.ead.youxuan.mapper.youxuan.MediaMapper;
import outfox.ead.youxuan.web.ad.controller.vo.*;
import outfox.ead.youxuan.web.ad.service.*;

import java.util.*;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.ResourceStatusEnum.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MediaServiceImpl extends YouxuanServiceImpl<MediaMapper, Media>
        implements MediaService {

    @Lazy
    private final AdPositionService adPositionService;
    @Lazy
    private final StyleService styleService;
    private final outfox.ead.youxuan.web.ad.controller.mapper.MediaMapper mediaMapper;
    private final AdDeliveryTimeService adDeliveryTimeService;
    private final AdContentRelationService adContentRelationService;
    @Lazy
    private final AdGroupService adGroupService;

    @Override
    public PageVO<MediaResponseVO> pageList(MediaCriteriaQueryVO mediaVO) {
        LambdaQueryChainWrapper<Media> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(Objects.nonNull(mediaVO.getId()), Media::getId, StringUtils.isNumeric(mediaVO.getId()) ? mediaVO.getId() : -1)
                .like(Objects.nonNull(mediaVO.getName()), Media::getName, mediaVO.getName());
        if (Objects.nonNull(mediaVO.getStatus())) {
            if (!mediaVO.getStatus().equals(NOT_DELETED.getCode())) {
                wrapper.eq(Media::getStatus, mediaVO.getStatus());
            } else {
                wrapper.ne(Media::getStatus, DELETED.getCode());
            }
        }

        Page<Media> page = wrapper.orderByDesc(Media::getCreateTime).
                page(new Page<>(mediaVO.getCurrent(), mediaVO.getSize()));
        List<Media> medias = page.getRecords();
        List<MediaResponseVO> mediaResponses = new ArrayList<>();
        for (Media media : medias) {
            MediaResponseVO mediaResponseVO = mediaMapper.doToMediaResponse(media);
            mediaResponses.add(mediaResponseVO);
        }
        return new PageVO<>(mediaVO.getCurrent(), mediaVO.getSize(), mediaResponses, page.getTotal());

    }

    @Override
    public Long saveOrUpdate(MediaSaveOrUpdateVO mediaVO) {
        Media media = mediaMapper.saveOrUpdateVoToDo(mediaVO);
        saveOrUpdatePreCheck(mediaVO);
        this.saveOrUpdate(media);
        return media.getId();
    }

    private void saveOrUpdatePreCheck(MediaSaveOrUpdateVO mediaVO) {
        nameRepeat(mediaMapper.doToCountByName(mediaVO));
        if (isUpdate(mediaVO.getId())) {
            Media media = this.getById(mediaVO.getId());
            if (Objects.nonNull(mediaVO.getOsType()) && !media.getOsType().equals(mediaVO.getOsType())) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "平台不支持修改");
            }
        }
    }

    private boolean isUpdate(Long id) {
        return id != null;
    }

    @Override
    public void nameRepeat(MediaCountByNameVO mediaCountByNameVO) {
        if (count(new QueryWrapper<Media>().lambda()
                .eq(Media::getName, mediaCountByNameVO.getName().trim())
                .ne(Objects.nonNull(mediaCountByNameVO.getId()), Media::getId, mediaCountByNameVO.getId())
                .ne(Media::getStatus, DELETED.getCode())) > 0) {
            throw new CustomException(ResponseType.NAME_REPEATED_EXCEPTION, "媒体名称重复，请修改");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchUpdateStatusById(BatchUpdateStatusVO mediaVO) {
        List<Media> medias = this.listByIds(mediaVO.getIds());
        updateBatchPreCheckAndFilterMedias(medias, mediaVO.getStatus());
        if (medias.isEmpty()) {
            return batchUpdateMessage(Lists.emptyList(), mediaVO.getStatus());
        }
        for (Media media : medias) {
            if (!media.getStatus().equals(mediaVO.getStatus())) {
                media.setStatus(mediaVO.getStatus());
            }
        }
        if (mediaVO.getStatus().equals(PAUSE.getCode()) || mediaVO.getStatus().equals(DELETED.getCode())) {
            // 暂停和删除要将子层级做相同操作
            List<AdPosition> adPositions = adPositionService.listValidByMediaIds(medias
                    .stream().map(Media::getId).collect(Collectors.toSet()));
            for (AdPosition adPosition : adPositions) {
                adPosition.setStatus(mediaVO.getStatus());
            }
            adPositionService.updateBatchById(adPositions);
            List<Style> styles = styleService.listNotDeletedByAdPositionIds(adPositions.stream().map(AdPosition::getId).collect(Collectors.toList()));
            for (Style style : styles) {
                style.setStatus(mediaVO.getStatus());
            }
            styleService.updateBatchById(styles);

        }
        this.updateBatchById(medias);
        return batchUpdateMessage(medias, mediaVO.getStatus());

    }

    private String batchUpdateMessage(List<Media> medias, Integer status) {
        StringBuilder sb = new StringBuilder();
        sb.append(medias.size());
        if (status.equals(VALID.getCode())) {
            sb.append("个媒体状态已修改为开启");
        } else if (status.equals(DELETED.getCode())) {
            sb.append("个媒体状态及其广告位，样式状态已修改为删除");
        } else {
            sb.append("个媒体状态及其广告位，样式状态已修改为暂停");
        }
        return sb.toString();
    }


    @Override
    public List<Media> listNotDeletedByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.emptyList();
        }
        return new LambdaQueryChainWrapper<>(baseMapper)
                .ne(Media::getStatus, DELETED.getCode())
                .in(Media::getId, ids)
                .list();
    }

    @Override
    public List<Media> listValidByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.emptyList();
        }
        return baseMapper.listValidByIds(ids);
    }

    @Override
    public List<Media> listLikeName(String mediaName) {
        return new LambdaQueryChainWrapper<>(baseMapper)
                .like(Objects.nonNull(mediaName), Media::getName, mediaName)
                .list();
    }

    @Override
    public Page<Media> pageListInIds(Set<Long> mediaIds, Long current, Long size) {
        return baseMapper.pageListInIds(mediaIds, current, size);
    }

    @Override
    public List<Media> listByStyleIds(List<Long> styleIds) {
        return baseMapper.listByStyleIds(styleIds);
    }

    @Override
    public Integer getOsByStyleId(Long styleId) {
        return baseMapper.getOsByStyleId(styleId);
    }

    @Override
    public List<Media> listByName(String name, List<Integer> status) {
        return baseMapper.listNotDeletedByName(name);
    }

    private void updateBatchPreCheckAndFilterMedias(List<Media> medias, Integer option) {
        medias.removeIf(media -> media.getStatus().equals(DELETED.getCode()) || media.getStatus().equals(option));
        if (option.equals(DELETED.getCode())) {
            // 历史投放过不删除
            medias.removeIf(media -> isDelivered(media.getId()));
            // 在投的不删除
            medias.removeIf(media -> isInDelivery(media.getId()));
        } else if (option.equals(PAUSE.getCode())) {
            // 在投的不暂停
            medias.removeIf(media -> isInDelivery(media.getId()));
        } else {
            if (!option.equals(VALID.getCode())) {
                throw new CustomException(ResponseType.INVALID_PARAMETERS, "状态错误");
            }
        }
    }


    /**
     * 历史投放过
     *
     * @param mediaId 媒体id
     * @return true-历史投放过
     */
    private boolean isDelivered(Long mediaId) {
        Set<Long> positionIds = adPositionService.listNotDeleteByMediaId(mediaId).stream().map(AdPosition::getId).collect(Collectors.toSet());
        if (positionIds.isEmpty()) {
            return false;
        }
        Set<Long> styleIds = styleService.listNotDeletedByAdPositionIds(positionIds).stream().map(Style::getId).collect(Collectors.toSet());
        if (styleIds.isEmpty()) {
            return false;
        }
        Set<Long> groupIds = adContentRelationService.listByStyleIds(styleIds)
                .stream()
                .map(AdContentRelation::getAdGroupId)
                .collect(Collectors.toSet());
        List<AdGroup> adGroups = adGroupService.listNotDeleteByIds(groupIds);
        Set<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());
        if (adPlanIds.isEmpty()) {
            return false;
        }
        return adDeliveryTimeService.isDelivered(adPlanIds);
    }

    /**
     * 在投 投放中+即将投放
     *
     * @param mediaId 媒体id
     * @return true-在投
     */
    private boolean isInDelivery(Long mediaId) {
        Set<Long> positionIds = adPositionService.listNotDeleteByMediaId(mediaId).stream().map(AdPosition::getId).collect(Collectors.toSet());
        if (positionIds.isEmpty()) {
            return false;
        }
        Set<Long> styleIds = styleService.listNotDeletedByAdPositionIds(positionIds).stream().map(Style::getId).collect(Collectors.toSet());
        if (styleIds.isEmpty()) {
            return false;
        }
        Set<Long> groupIds = adContentRelationService.listByStyleIds(styleIds)
                .stream()
                .map(AdContentRelation::getAdGroupId)
                .collect(Collectors.toSet());
        List<AdGroup> adGroups = adGroupService.listNotDeleteByIds(groupIds);
        Set<Long> adPlanIds = adGroups.stream().map(AdGroup::getAdPlanId).collect(Collectors.toSet());
        if (adPlanIds.isEmpty()) {
            return false;
        }
        return adDeliveryTimeService.isInDelivery(adPlanIds);
    }
}




