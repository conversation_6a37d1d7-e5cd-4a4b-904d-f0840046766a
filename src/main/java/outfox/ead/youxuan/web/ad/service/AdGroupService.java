package outfox.ead.youxuan.web.ad.service;

import com.github.rholder.retry.RetryException;
import outfox.ead.youxuan.constants.InteractionTypeEnum;
import outfox.ead.youxuan.entity.AdGroup;
import outfox.ead.youxuan.entity.AdPlan;
import outfox.ead.youxuan.entity.Style;
import outfox.ead.youxuan.web.ad.controller.vo.*;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
public interface AdGroupService extends YouxuanService<AdGroup> {

    /**
     * 根据id查询详细信息，用于复制/修改操作的信息返回
     *
     * @param id 推广组主键
     * @return AdGroupByIdVO
     */
    AdGroupByIdVO getAdGroupDetailById(Long id);

    /**
     * 列表条件查询
     *
     * @param adGroupCriteriaQueryVO 推广组名称（模糊查询）、推广组id（精确查询）、计划名称（模糊查询）、计划ID（精确查询)
     * @return list
     */
    PageVO<AdGroupListVO> page(AdGroupCriteriaQueryVO adGroupCriteriaQueryVO);

    /**
     * 批量修改推广组状态
     *
     * @param adGroupIds 推广组id列表
     * @param status     状态
     * @return msg       提示信息
     */
    String batchUpdateStatusById(List<Long> adGroupIds, Integer status);

    /**
     * 插入/修改推广组
     *
     * @param adGroupSaveOrUpdateVO 推广组相关属性
     */
    void saveOrUpdate(AdGroupSaveOrUpdateVO adGroupSaveOrUpdateVO) throws ExecutionException, RetryException;

    /**
     * 保存yex数据
     *
     * @param dspId
     * @param dealId
     * @param dealRemark
     * @param id
     * @param adPlan
     * @param validStyles
     */
    void saveYexData(String dspId, String dealId, String dealRemark, Long id, AdPlan adPlan, List<Style> validStyles);

    /**
     * 查询有效广告组
     *
     * @param adPlanIds 推广计划id列表
     * @return 推广组列表
     */
    List<AdGroup> listNoteDeleteByAdPlanIds(Collection<Long> adPlanIds);

    /**
     * 查询有效的推广计划  排除adGroupId（可选）
     *
     * @param adPlanIds 推广计划id
     * @param adGroupId 需要排除掉的推广组id 用于修改的时候资源校验
     * @return 推广组列表
     */
    List<AdGroup> listNotDeleteByAdPlanIdsExcludeAdGroup(Collection<Long> adPlanIds, Long adGroupId);

    /**
     * 推广组名字判重
     *
     * @param name     推广组名字
     * @param id       推广组id
     * @param adPlanId 推广计划id
     * @return true-重复
     */
    Boolean nameRepeat(String name, Long id, Long adPlanId);

    /**
     * 通过推广计划id查询未删除的推广组
     *
     * @param adPlanId 推广计划id
     * @return 推广组列表
     */
    List<AdGroup> listValidByAdPlanId(Long adPlanId);

    /**
     * 通过id查询推广组
     *
     * @param adGroupIds 推广组id列表
     * @return 推广组列表
     */
    List<AdGroup> listNotDeleteByIds(Collection<Long> adGroupIds);

    /**
     * 新建推广组，初始化需要填写的内容
     *
     * @param styleIds          样式id列表
     * @param adPlanId          推广计划id
     * @param adGroupId         推广组id
     * @param mode              0-修改 1-复制
     * @param interactionType   {@link InteractionTypeEnum#getCode()}
     * @return 初始化数据
     */
    InitialVO initialize(List<Long> styleIds, Long adPlanId, Long adGroupId, Integer mode, Integer interactionType);

    /**
     * 查询有效广告组
     *
     * @param id 推广计划id
     * @return 推广组列表
     */
    List<AdGroup> listNoteDeleteByAdPlanId(Long id);

    /**
     * 更新推广组状态
     *
     * @param id     推广计划
     * @param status 状态
     */
    void updateValidStatusByAdPlanId(Long id, Integer status);

    /**
     * 查询未删除的推广组个数
     *
     * @param id 推广计划id
     * @return 推广组个数
     */
    long countNotDeleteByAdPlanId(Long id);

    /**
     * 通过名字模糊查询，或者推广计划id在列表中的数据
     *
     * @param name      名字
     * @param adPlanIds 推广计划id列表
     * @return 推广组列表
     */
    List<AdGroup> listNotDeletedAdGroupsLikeNameOrInAdPlanIds(String name, Set<Long> adPlanIds);

    Collection<AdGroup> listByName(String adGroupName);

    String updateTestDirectStatusById(long adGroupId, boolean testDirect);
}
