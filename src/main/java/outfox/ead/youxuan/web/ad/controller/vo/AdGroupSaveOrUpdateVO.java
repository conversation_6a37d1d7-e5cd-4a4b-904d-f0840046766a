package outfox.ead.youxuan.web.ad.controller.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import outfox.ead.youxuan.constants.InteractionTypeEnum;
import outfox.ead.youxuan.core.validator.HaveNoBlank;
import outfox.ead.youxuan.core.validator.ListLength;
import outfox.ead.youxuan.entity.AdContentRelation;
import outfox.ead.youxuan.entity.VersionFilterItemDTO;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

import static outfox.ead.youxuan.constants.Constants.UPPER_LIMIT;

/**
 * <AUTHOR> <EMAIL>
 * @date 2021/8/26 17:09
 */
@Data
public class AdGroupSaveOrUpdateVO {
    private Long id;
    @ApiModelProperty("推广组名称")
    @NotBlank
    private String name;
    @ApiModelProperty("推广计划主键")
    @NotNull
    private Long adPlanId;
    @ApiModelProperty("状态")
    @NotNull(message = "状态类型不能为空")
    @Min(value = 0, message = "状态类型错误")
    private Integer status;
    @ApiModelProperty("cpm 出价")
    @Max(value = UPPER_LIMIT, message = "CPM出价超出上限")
    private Integer cpmPrice;
    @ApiModelProperty("总展示量")
    @Max(value = UPPER_LIMIT, message = "总展示量超出上限")
    private Integer sumDisplayCount;
    @ApiModelProperty("日展示量上限")
    @Max(value = UPPER_LIMIT, message = "日展示量超出上限")
    private Integer dailyDisplayLimit;
    @ApiModelProperty("投放速度,0代表快速，1代表均匀")
    @Min(value = 0, message = "投放速度类型错误")
    @Max(value = 1, message = "投放速度类型错误")
    private Integer deliverySpeed;
    @ApiModelProperty("订单id")
    @HaveNoBlank
    private String dealId;
    @ApiModelProperty("开机首刷，0不开启，1开启")
    @Min(value = 0, message = "开机首刷类型错误")
    @Max(value = 1, message = "开机首刷类型错误")
    private Integer bootFirstRefresh;
    @ApiModelProperty("开屏回收，0不开启，1开启")
    @Min(value = 0, message = "开屏回收类型错误")
    @Max(value = 1, message = "开屏回收类型错误")
    private Integer openScreenRecycle;
    @ApiModelProperty("目标投放词列表")
    @ListLength(message = "目标投放词列表长度不合法，检查其长度", minLength = 1, maxLength = 50)
    private List<String> advertisingKeywordList;
    @ApiModelProperty("目标投放词音频列表")
    private List<String> audioUrls;
    @ApiModelProperty("订单备注")
    private String dealRemark;
    @ApiModelProperty("dsp id")
    private String dspId;
    @ApiModelProperty("打底广告，0不填充，1填充")
    @Min(value = 0, message = "打底广告类型错误")
    @Max(value = 1, message = "打底广告类型错误")
    private Integer basePadding;
    @ApiModelProperty("轮播信息，投放位置")
    private List<AdContentRelation> relations;
    @ApiModelProperty("创意内容")
    private List<AdContentSaveOrUpdateVO> creativeContents;
    @ApiModelProperty("跳转添加类型")
    private Integer brandClkType;
    @ApiModelProperty("落地页链接")
    private String landingPageLink;
    @ApiModelProperty("深链接")
    private String deeplinkUrl;
    @ApiModelProperty("小程序APPID")
    private String wechatAppId;
    @ApiModelProperty("小程序原始id")
    private String wechatOriginalId;
    @ApiModelProperty("目标路径")
    private String wechatPath;
    @ApiModelProperty("曝光监测链接")
    @HaveNoBlank
    private String expoDeteLink;
    @ApiModelProperty("点击监测链接")
    @HaveNoBlank
    private String clickDeteLink;
    @ApiModelProperty("是否屏蔽青少年，0-否，1-是")
    private Boolean youthMode;
    @ApiModelProperty("性别定向 0-无 1-男 2-女")
    private Integer genderOrientation;
    @ApiModelProperty("年龄定向区间")
    private  List<List<Integer>> ageOrientation;
    @ApiModelProperty("人群标签")
    private List<Integer> crowdLabel;
    @ApiModelProperty(value = "无设备号过滤")
    private Boolean mustDeviceId;
    @ApiModelProperty(value = "定向模式 0-无 1-精准人群定向 2-扩展人群定向")
    @NotNull(message = "定向模式不能为空")
    private Integer orientationMode;
    @ApiModelProperty("监测链接是否需要宏替换")
    private Boolean replaceMacro;
    @ApiModelProperty("是否开启扩量推广")
    private Boolean expansionPromotion;
    /**
     * {@link InteractionTypeEnum#getCode()}
     */
    @ApiModelProperty("交互类型, 0:无，1:摇一摇，2:滑动互动，3:双link，4:三link,5:扭一扭, 6:三合一")
    private Integer interactionType;
    @ApiModelProperty("落地页链接1")
    private String landingPageLink1;
    @ApiModelProperty("深链接1")
    private String deeplinkUrl1;
    @ApiModelProperty("小程序APPID1")
    private String wechatAppId1;
    @ApiModelProperty("小程序原始id1")
    private String wechatOriginalId1;
    @ApiModelProperty("目标路径1")
    private String wechatPath1;
    @ApiModelProperty("落地页链接2")
    private String landingPageLink2;
    @ApiModelProperty("深链接2")
    private String deeplinkUrl2;
    @ApiModelProperty("小程序APPID2")
    private String wechatAppId2;
    @ApiModelProperty("小程序原始id2")
    private String wechatOriginalId2;
    @ApiModelProperty("目标路径2")
    private String wechatPath2;
    @ApiModelProperty("是否开启应用定向，1-开启，0-不开启")
    private Boolean appInstalledOrientation;
    @ApiModelProperty("定向应用id列表")
    private List<Long> installedAppPackageIds;
    @ApiModelProperty("微信小程序定向")
    private Boolean wechatOrientation;
    @ApiModelProperty(value = "测试直达状态")
    private Boolean testDirect;
    @ApiModelProperty("直达有效开始时间")
    private LocalDateTime directStartTime;
    @ApiModelProperty("直达有效结束时间")
    private LocalDateTime directEndTime;
    @ApiModelProperty("是否开启自定义人群包")
    private Boolean openCustomCrowdPack;
    @ApiModelProperty("true-定向人群投放 false-排除人群投放")
    private Boolean includeCrowdPack;
    @ApiModelProperty("人群包id列表")
    private List<Integer> crowdPackIds;
    @ApiModelProperty("是否开启h5合成链接")
    private Boolean h5TransitUrl;
    @ApiModelProperty("是否开启deeplink合成链接")
    private Boolean dpTransitUrl;
    @ApiModelProperty("合成链接中目标小程序的appId")
    private String transitTargetWechatAppId;
    @ApiModelProperty("品牌cpm广告消费类型，0-正常消费，1-匀速消费，2-加速消费(默认0)")
    private Integer cpmCostType;
    @ApiModelProperty("cpm广告加速系数，当广告模式为加速模式时生效，范围为1-100，默认为1")
    private Integer cpmAccelerateRatio;
    @ApiModelProperty("是否允许caid作为有效设备号类型")
    private Boolean allowCaid;
    @ApiModelProperty("是否支持全链路投放")
    private Boolean fullChannel;
    @ApiModelProperty("是否上报第三方曝光检测")
    private Boolean reportExpoDeteLink;
    @ApiModelProperty("是否支持全屏点击")
    private boolean fullClickIsOpen;
    @ApiModelProperty("全屏点击是否为全部城市")
    private Boolean fullClickIsAllCity;
    @ApiModelProperty("全屏点击支持的城市等级")
    private List<Integer> fullClickClassCities;
    @ApiModelProperty("是否开启无效流量过滤")
    private Boolean invalidTrafficFilter;
    @ApiModelProperty("是否开启版本号过滤")
    private Boolean versionFilter;
    @ApiModelProperty("需要过滤的版本号,格式为:[{appId:版本号},{appId:版本号}]")
    private List<VersionFilterItemDTO> versionFilterList;
}
