package outfox.ead.youxuan.web.ad.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.Role;
import outfox.ead.youxuan.entity.UserDetail;
import outfox.ead.youxuan.entity.UserRelation;
import outfox.ead.youxuan.mapper.youxuan.UserRelationMapper;
import outfox.ead.youxuan.util.SecurityUtil;
import outfox.ead.youxuan.web.ad.controller.vo.PageVO;
import outfox.ead.youxuan.web.ad.controller.vo.UserRelationVO;
import outfox.ead.youxuan.web.ad.service.RoleService;
import outfox.ead.youxuan.web.ad.service.UserDetailService;
import outfox.ead.youxuan.web.ad.service.UserRelationService;
import outfox.ead.youxuan.web.ad.service.UserService;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.youxuan.constants.Constants.BIND;
import static outfox.ead.youxuan.constants.Constants.UNBIND;
import static outfox.ead.youxuan.constants.ResponseType.DATA_ACCESS_DENIED;
import static outfox.ead.youxuan.constants.ResponseType.INVALID_PARAMETERS;

/**
 * <AUTHOR>
 * @description 针对表【UserRelation(用户单向绑定表)】的数据库操作Service实现
 * @date 2022-02-09 19:44:19
 */
@Service
@AllArgsConstructor
public class UserRelationServiceImpl extends YouxuanServiceImpl<UserRelationMapper, UserRelation>
        implements UserRelationService {
    private final UserDetailService userDetailService;
    private final UserService userService;
    private final RoleService roleService;
    private final outfox.ead.youxuan.web.ad.controller.mapper.UserRelationMapper userRelationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindUser(Long kolUserId, Long sponsorUserId) {
        lockUser(Arrays.asList(sponsorUserId, kolUserId));
        preCheck(kolUserId, sponsorUserId);
        UserRelation ur = baseMapper.getByUserId(kolUserId, sponsorUserId);
        if (Objects.nonNull(ur)) {
            ur.setStatus(BIND);
            this.updateById(ur);
        } else {
            UserRelation userRelation = new UserRelation();
            userRelation.setKolUserId(kolUserId);
            userRelation.setSponsorUserId(sponsorUserId);
            userRelation.setStatus(BIND);
            this.save(userRelation);
        }
    }

    @Override
    public PageVO<UserRelationVO> pageByKolUserId(Long kolUserId, Long current, Long size) {
        Page<UserRelation> page = baseMapper.pageByKolUserId(kolUserId, current, size);
        List<UserRelationVO> userRelationVOs = new ArrayList<>(page.getRecords().size());
        page.getRecords().forEach(userRelation -> userRelationVOs.add(
                userRelationMapper.userRelation2VO(userRelation,
                        SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ? userRelation.getKolUserId() : userRelation.getSponsorUserId(),
                        SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ? userRelation.getSponsorUserId() : userRelation.getKolUserId())));
        Map<Long, UserDetail> id2UserDetail = userDetailService
                .listByUserIdsAndRole(userRelationVOs
                                .stream().map(UserRelationVO::getBoundUserId).collect(Collectors.toList()),
                        roleService.getByRoleKey(RoleEnum.SPONSOR.getRoleKey()))
                .stream()
                .collect(Collectors.toMap(UserDetail::getUserId, Function.identity()));
        return getUserRelationVOPageVO(page, userRelationVOs, id2UserDetail);
    }

    @Override
    public PageVO<UserRelationVO> pageBySponsorUserId(Long sponsorUserId, Long current, Long size) {
        Page<UserRelation> page = baseMapper.pageBySponsorUserId(sponsorUserId, current, size);
        List<UserRelationVO> userRelationVOs = new ArrayList<>(page.getRecords().size());
        page.getRecords().forEach(userRelation -> userRelationVOs.add(
                userRelationMapper.userRelation2VO(userRelation,
                        SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ? userRelation.getKolUserId() : userRelation.getSponsorUserId(),
                        SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ? userRelation.getSponsorUserId() : userRelation.getKolUserId())));
        Map<Long, UserDetail> id2UserDetail = userDetailService
                .listByUserIdsAndRole(userRelationVOs
                                .stream().map(UserRelationVO::getBoundUserId).collect(Collectors.toList()),
                        roleService.getByRoleKey(RoleEnum.BRAND_KOL.getRoleKey()))
                .stream()
                .collect(Collectors.toMap(UserDetail::getUserId, Function.identity()));
        return getUserRelationVOPageVO(page, userRelationVOs, id2UserDetail);
    }

    private PageVO<UserRelationVO> getUserRelationVOPageVO(Page<UserRelation> page, List<UserRelationVO> userRelationVOs, Map<Long, UserDetail> id2UserDetail) {
        userRelationVOs.forEach(r -> {
            r.setAvatar(id2UserDetail.getOrDefault(r.getBoundUserId(), new UserDetail()).getAvatar());
            r.setNickname(id2UserDetail.getOrDefault(r.getBoundUserId(), new UserDetail()).getNickname());
        });
        return new PageVO<>(page.getCurrent(),
                page.getSize(),
                userRelationVOs,
                page.getTotal());
    }

    @Override
    public Boolean existsValidByUserIdAndBoundUserId(Long sponsorUserId, Long kolUserId) {
        return baseMapper.existsValid(sponsorUserId, kolUserId);
    }

    @Override
    public Boolean existValidByKolUserId(Long kolUserId) {
        return baseMapper.existsValidByKolUserId(kolUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusById(Long relationId, int status) {
        UserRelation userRelation = this.getById(relationId);
        if (!SecurityUtil.isAdmin() &&
                !Objects.equals(SecurityUtil.getUserId(),
                        SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ?
                                userRelation.getSponsorUserId() : userRelation.getKolUserId()
                )
        ) {
            throw new CustomException(DATA_ACCESS_DENIED);
        }
        updateSyncStatusIfNeed(status, userRelation);
        lockUser(Arrays.asList(userRelation.getKolUserId(), userRelation.getSponsorUserId()));
        userRelation.setStatus(status);
        this.updateById(userRelation);
    }

    /**
     * 解绑时将品牌号的同步状态也设置为false
     */
    private void updateSyncStatusIfNeed(int status, UserRelation userRelation) {
        if (UNBIND == status) {
            userDetailService.updateSyncStatus(userRelation.getKolUserId(), false);
        }
    }

    @Override
    public void rebind(Long relationId, int bind) {
        UserRelation userRelation = this.getById(relationId);
        if (!SecurityUtil.isAdmin() &&
                !Objects.equals(SecurityUtil.getUserId(),
                        SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR) ?
                                userRelation.getSponsorUserId() : userRelation.getKolUserId()
                )
        ) {
            throw new CustomException(DATA_ACCESS_DENIED);
        }
        lockUser(Arrays.asList(userRelation.getKolUserId(), userRelation.getSponsorUserId()));
        preCheck(userRelation.getKolUserId(), userRelation.getSponsorUserId());
        userRelation.setStatus(bind);
        this.updateById(userRelation);
    }

    @Override
    public List<UserRelation> listValidByKolUserId(Long kolUserId) {
        return baseMapper.listValidByKolUserId(kolUserId);
    }

    /**
     * 绑定时校验
     */
    private void preCheck(Long kolUserId, Long sponsorUserId) {
        if (!userService.exsistById(sponsorUserId)) {
            throw new CustomException(INVALID_PARAMETERS, "该广告主账户不存在，请检查后重新输入");
        }
        if (!userService.exsistById(kolUserId)) {
            throw new CustomException(INVALID_PARAMETERS, "该机构创作者账户不存在，请检查后重新输入");
        }

        if (baseMapper.existsValid(sponsorUserId, kolUserId)) {
            if (SecurityUtil.getLoginUser().checkRole(RoleEnum.SPONSOR.getRoleKey())) {
                throw new CustomException(INVALID_PARAMETERS, "当前创作者账户已绑定过，无需重复绑定");
            } else if (SecurityUtil.getLoginUser().checkRole(RoleEnum.BRAND_KOL.getRoleKey())) {
                throw new CustomException(INVALID_PARAMETERS, "当前广告主账户已绑定过，无需重复绑定");
            }
        } else {
            if (baseMapper.existsValidByKolUserId(kolUserId)) {
                if (SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR)) {
                    throw new CustomException(INVALID_PARAMETERS, "该创作者账户已绑定其他广告主账户，不支持绑定当前账户");
                }else{
                    throw new CustomException(INVALID_PARAMETERS, "同一时间只能绑定一个优选广告主账户");
                }
            }
        }
        checkUserRole(sponsorUserId, kolUserId);
    }

    private void checkUserRole(Long sponsorUserId, Long kolUserId) {
        if (SecurityUtil.checkCurrentRole(RoleEnum.SPONSOR)) {
            Collection<Role> kolRoles = roleService.listByUserId(kolUserId);
            if (!checkRole(kolRoles, RoleEnum.BRAND_KOL)) {
                throw new CustomException(INVALID_PARAMETERS, "广告主账户只能绑定机构创作者账户");
            }
        }else{
            Collection<Role> sponsorRoles = roleService.listByUserId(sponsorUserId);
            if (!checkRole(sponsorRoles, RoleEnum.SPONSOR)) {
                throw new CustomException(INVALID_PARAMETERS, "机构创作者账户只能绑定广告主账户");
            }
            if (baseMapper.existsValidByKolUserId(kolUserId)) {
                throw new CustomException(INVALID_PARAMETERS, "同一时间只能绑定一个优选广告主账户");
            }
        }
    }

    private boolean checkRole(Collection<Role> roles, RoleEnum roleEnum) {
        for (Role role : roles) {
            if (role.getRoleKey().equals(roleEnum.getRoleKey())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 由于用户只能被唯一绑定，在绑定关系成功建立之前先进行锁定
     * 必须在事务中使用该方法，不然无效
     *
     * @param userIds 被绑定用户id
     */
    private void lockUser(List<Long> userIds) {
        userService.lockUser(userIds);
    }

    @Override
    public Collection<UserRelation> listValidBySponsorId(Long sponsorId) {
        return baseMapper.listValidBySponsorUserId(sponsorId);
    }
}




