package outfox.ead.youxuan.web.ad.service.impl;

import com.alibaba.excel.util.BooleanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import outfox.ead.youxuan.constants.ResponseType;
import outfox.ead.youxuan.constants.RoleEnum;
import outfox.ead.youxuan.core.annotation.DistributedLock;
import outfox.ead.youxuan.core.annotation.DistributedLockKey;
import outfox.ead.youxuan.core.config.security.JwtSecurityToken;
import outfox.ead.youxuan.core.exception.CustomException;
import outfox.ead.youxuan.entity.User;
import outfox.ead.youxuan.util.MailTemplateUtil;
import outfox.ead.youxuan.util.MailUtil;
import outfox.ead.youxuan.web.ad.controller.bo.ActiveRegisterToken;
import outfox.ead.youxuan.web.ad.controller.vo.RegisterUserVO;
import outfox.ead.youxuan.web.ad.service.RegisterService;
import outfox.ead.youxuan.web.ad.service.UserRoleRelationService;
import outfox.ead.youxuan.web.ad.service.UserService;

import javax.mail.MessagingException;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.UnsupportedEncodingException;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static outfox.ead.youxuan.constants.Constants.TOKEN_ADD_ROLE;
import static outfox.ead.youxuan.constants.Constants.TOKEN_REGISTER;
import static outfox.ead.youxuan.constants.ResponseType.*;

/**
 * <AUTHOR>
 * @date 2022年07月18日 12:17
 */
@Service
@RequiredArgsConstructor
@Validated
@Slf4j
public class RegisterServiceImpl implements RegisterService {
    private final UserService userService;
    private final UserRoleRelationService userRoleRelationService;
    @Value("${youxuan.web}")
    private String youxuanWebBaseUrl;
    @Value("${expired}")
    private long expired;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public void sendActivationRegisterEmail(@Valid
                                            @Pattern(regexp = "(\\w){4,20}@163\\.com$", message = "请使用网易163邮箱注册")
                                                    String email,
                                            String roleKey) {
        User user = userService.getUserByUsername(email);
        if (Objects.nonNull(user)) {
            userRoleRelationService.rolePreCheck(user.getId(), roleKey);
        }
        String key = "youxuan_send_register_email_" + email;
        if (BooleanUtils.isNotTrue(redisTemplate.opsForValue().setIfAbsent(key, "null", 60, TimeUnit.SECONDS))) {
            throw new CustomException(ResponseType.INVALID_PARAMETERS, "操作太过频繁，请在" + redisTemplate.getExpire(email) + "s后重试");
        }
        try {
            MailUtil.sendMail(email, "有道优选激活邮件", generateContent(Objects.isNull(user) ? null : user.getId(), roleKey, email));
        } catch (MessagingException | UnsupportedEncodingException e) {
            throw new CustomException(SERVICE_ERROR, "发送邮件失败", e);
        }
    }

    private String generateContent(Long id, String roleKey, String email) {
        String token = JwtSecurityToken.generateActiveToken(id, roleKey, email, id == null ? TOKEN_REGISTER : TOKEN_ADD_ROLE, expired);
        redisTemplate.opsForValue().set("youxuan_register_" + email, token, Duration.ofSeconds(expired));
        return MailTemplateUtil.getActiveRegisterMailContent(email, youxuanWebBaseUrl + String.format("api/emailRegister?activeToken=%s", token), "立即激活");
    }

    @Override
    public RegisterUserVO activeRegister(String activeRegisterToken) {
        ActiveRegisterToken token = JwtSecurityToken.of(activeRegisterToken, ActiveRegisterToken.class);
        String key = "youxuan_register_" + token.getUsername();
        String s = (String) redisTemplate.opsForValue().get(key);
        if (Objects.isNull(s) || !s.equals(activeRegisterToken)) {
            throw new CustomException(TOKEN_INVALID, "token已失效");
        }
        User user;
        switch (token.getSub()) {
            case TOKEN_REGISTER: {
                user = userService.registerByMail(token.getUsername());
                userRoleRelationService.save(user.getId(), token.getRoleKey(), false);
                break;
            }
            case TOKEN_ADD_ROLE: {
                user = userService.getById(token.getId());
                if (Objects.isNull(user) || user.getStatus() != 1 || !user.getUsername().equals(token.getUsername())) {
                    throw new CustomException(INVALID_PARAMETERS, "该账户不存在，参数异常");
                }
                userRoleRelationService.save(token.getId(), token.getRoleKey(), false);
                break;
            }
            default:
                throw new CustomException(INVALID_PARAMETERS);
        }
        redisTemplate.delete(key);
        return RegisterUserVO.builder()
                .username(user.getUsername())
                .id(user.getId())
                .dictUid(user.getDictUid())
                .roleKey(token.getRoleKey())
                .build();
    }

    @Override
    @DistributedLock(namespace = "appAccount")
    public User registerByDict(@DistributedLockKey @Valid @NotNull(message = "有道词典账户id不能为空") String uid,
                               String roleKey, Boolean roleCanRepeat) {
        if (uid == null) {
            throw new CustomException(INVALID_PARAMETERS, "有道词典账户id不能为空");
        }
        User user = userService.getUserByDictUid(uid);
        if (Objects.isNull(user)) {
            user = new User();
            user.setDictUid(uid);
            userService.save(user);
        }
        userRoleRelationService.save(user.getId(), roleKey, roleCanRepeat);
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User registerRandomUsername(String roleKey) {
        String username;
        do {
            username = UUID.randomUUID().toString();
        } while (Objects.nonNull(userService.getUserByUsername(username)));
        User user = new User();
        user.setUsername(username);
        userService.save(user);
        userRoleRelationService.save(user.getId(), roleKey, true);
        return user;
    }

    @Override
    public User registerUserIfAbsent(@NotNull(message = "163邮箱账户不能为空")
                                     @Pattern(regexp = "(\\w){4,20}@163\\.com$", message = "请使用网易163邮箱注册") String username,
                                     RoleEnum roleEnum, Map<Long, String> id2RoleKey) {

        if (username == null) {
            throw new CustomException(INVALID_PARAMETERS, "用户名不能为空");
        }
        User user = userService.getUserByUsername(username);
        if (Objects.isNull(user)) {
            user = new User();
            user.setUsername(username);
            userService.save(user);
        }
        userRoleRelationService.save(user.getId(), roleEnum.getRoleKey(), true);
        return user;
    }
}
