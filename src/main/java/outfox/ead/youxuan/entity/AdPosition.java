package outfox.ead.youxuan.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@TableName(value = "AdPosition", autoResultMap = true)
@Data
public class AdPosition implements Serializable {
    /**
     * 广告位主键
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;
    /**
     * 媒体ID
     */
    @TableField(value = "MEDIA_ID")
    private Long mediaId;
    /**
     * 广告位名称
     */
    @TableField(value = "NAME")
    private String name;
    /**
     * 广告位类型 0-信息流 1-开屏 2-插屏 3-焦点图 4-激励视频 5-横幅 6-自定义
     */
    @TableField(value = "TYPE")
    private Integer type;

    /**
     * 支持轮播图片数量
     */
    @TableField(value = "DISPLAY_TIMES")
    private Integer displayTimes;
    /**
     * 推广标类型 <p>
     * 1-落地页推广<p>
     * 2-应用直达<p>
     * 3-小程序推广
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> promotionType;
    /**
     * 是否接受定向查词
     */
    @TableField(value = "SUPPORT_ADVERTISING_BY_KEYWORDS")
    private boolean supportAdvertisingByKeywords;
    /**
     * 状态
     */
    @TableField(value = "STATUS")
    private Integer status;
    /**
     * SdkElement
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(hidden = true)
    @TableField(value = "LAST_MOD_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModTime;
    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 修改人
     */
    @TableField(value = "MODIFIER", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    /**
     * 是否支持摇一摇
     */
    @TableField(value = "HAS_SHAKABLE")
    private Boolean hasShakable;

    @TableField(value = "FULL_CHANNEL")
    private Boolean fullChannel;

    @TableField(value = "IS_AUDIO")
    private Boolean isAudio;
}
