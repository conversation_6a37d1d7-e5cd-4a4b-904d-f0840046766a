package outfox.ead.youxuan.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.*;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年07月25日 15:31
 */
@Configuration
@Slf4j
public class RestTemplateConfig {

    /**
     * restTemplate 单例
     */
    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory httpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate(httpRequestFactory);

        // 设置拦截器，答应请求信息，方便Debug
        List<ClientHttpRequestInterceptor> interceptors = new ArrayList<>();
        interceptors.add(new ClientHttpRequestInterceptor() {
            @Override
            public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
                long startTime = System.currentTimeMillis();
                traceRequest(request, body);

                ClientHttpResponse response = null;
                try {
                    response = execution.execute(request, body);
                    traceResponse(response);
                    return response;
                } catch (Exception e) {
                    long endTime = System.currentTimeMillis();
                    log.error("HTTP request failed. URI: {}, Method: {}, Duration: {}ms, Error: {}",
                            request.getURI(), request.getMethod(), (endTime - startTime), e.getMessage(), e);
                    throw e;
                } finally {
                    long endTime = System.currentTimeMillis();
                    log.info("HTTP request completed. URI: {}, Method: {}, Duration: {}ms, Status: {}",
                            request.getURI(), request.getMethod(), (endTime - startTime),
                            response != null ? response.getStatusCode() : "FAILED");
                }
            }
            private void traceRequest(HttpRequest request, byte[] body) {
                log.info("=========================== request begin ===========================");
                log.info("uri : {}", request.getURI());
                log.info("method : {}", request.getMethod());
                log.info("headers : {}", request.getHeaders());
                log.info("request body : {}", (request.getMethod() == HttpMethod.POST ? "" : new String(body, StandardCharsets.UTF_8)));
                log.info("============================ request end ============================");
            }

            private void traceResponse(ClientHttpResponse httpResponse) throws IOException {
                StringBuilder inputStringBuilder = new StringBuilder();
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(httpResponse.getBody(), StandardCharsets.UTF_8));
                String line = bufferedReader.readLine();
                while (line != null) {
                    inputStringBuilder.append(line);
                    inputStringBuilder.append('\n');
                    line = bufferedReader.readLine();
                }
                log.info("============================ response begin ============================");
                log.info("Status code  : {}", httpResponse.getStatusCode());
                log.info("Status text  : {}", httpResponse.getStatusText());
                log.info("Headers      : {}", httpResponse.getHeaders());
                log.info("Response body: {}", inputStringBuilder);
                log.info("============================= response end =============================");
            }
        });

        restTemplate.setInterceptors(interceptors);

        //提供对传出/传入流的缓冲,可以让响应body多次读取(如果不配置,拦截器读取了Response流,再响应数据时会返回body=null)
        restTemplate.setRequestFactory(new BufferingClientHttpRequestFactory(httpRequestFactory));

        return restTemplate;
    }
    @Bean
    public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(15000);
        factory.setReadTimeout(5000);
        return factory;
    }
}
