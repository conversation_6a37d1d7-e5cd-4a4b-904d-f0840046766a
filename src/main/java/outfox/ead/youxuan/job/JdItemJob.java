package outfox.ead.youxuan.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import outfox.ead.youxuan.web.ad.service.LoginService;
import outfox.ead.youxuan.web.kol.service.ItemService;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2022年08月31日 11:50
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JdItemJob extends Job {

    private final LoginService loginService;
    private final ItemService itemService;

    @Override
    @XxlJob("JdItemJob")
    public void run() {
        super.run();
    }

    @Override
    protected void init() {
        log.info("Ready to execute JdItemJob.");
        loginService.login4Job();
    }

    @Override
    protected void doRun() {
        LocalTime now = LocalTime.now();
        // 跳过 23:50 这个时间的执行，这个时间JD数据返回的空
        if (is2350(now)) {
            return;
        }
        itemService.refreshItem();
    }

    public static boolean is2350(LocalTime now) {
        return now.isAfter(LocalTime.of(23, 49, 59)) && now.isBefore(LocalTime.of(23, 51));
    }

    @Override
    protected void finish() {
        log.info("JdItemJob DONE");
    }
}
