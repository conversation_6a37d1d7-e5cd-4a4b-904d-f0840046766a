package outfox.ead.data.thirdparty.bes;

import com.googlecode.protobuf.format.JsonFormat;
import lombok.NonNull;
import lombok.ToString;
import odis.serialize.IWritable;
import outfox.ead.data.protobuf.DataHolder.APISettingInfo;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/29
 */
@ToString
public class APISettingInfoResponse implements IWritable  {

    private APISettingInfo instance;

    private static JsonFormat jsonFormat = new JsonFormat();

    public APISettingInfo getInstance() {
        return instance;
    }

    public void setInstance(APISettingInfo instance) {
        this.instance = instance;
    }

    public APISettingInfoResponse(@NonNull APISettingInfo instance) {
        this.instance = instance;
    }

    public APISettingInfoResponse(){}

    @Override
    public void readFields(DataInput in) throws IOException {
        int byteLength = in.readInt();
        byte[] bytes = new byte[byteLength];
        in.readFully(bytes);
        instance = APISettingInfo.parseFrom(bytes);
    }

    @Override
    public void writeFields(DataOutput out) throws IOException {
        byte[] bytes = instance.toByteArray();
        out.writeInt(bytes.length);
        out.write(bytes);
    }

    @Override
    public IWritable copyFields(IWritable arg0) {
        throw new UnsupportedOperationException();
    }

    /**
     * @return 媒体过滤设置ID，Long类型，bes bid request 中的settingId与这项相对应
     */
    public Long getSettingId() {
        return instance.getSettingId();
    }

    /**
     * @return 过滤ID，String类型，暂无用处，联调过程发现该过滤ID与上面的媒体过滤设置ID相同
     */
    public String getSettingContentSettingId() {
        return instance.getSettingContent().getSettingId();
    }

    public List<String> getExcludedAdvertiserWebsiteUrl() {
        return instance.getSettingContent().getExcludedAdvertiserWebsiteUrlList();
    }

    public List<Integer> getMegV2Industry() {
        return instance.getSettingContent().getMegV2IndustryList();
    }

    public int getIsExcludedVulgar() {
        return instance.getSettingContent().getIsExcludedVulgar();
    }

    public List<String> getExcludedKeyword() {
        return instance.getSettingContent().getExcludedKeywordList();
    }

    public List<String> getPackageName() {
        return instance.getSettingContent().getPackageNameList();
    }

    @Override
    public String toString() {
        return jsonFormat.printToString(instance);
    }


}
