/**
 * @(#)ImageSponsorDeliverySiteDaoIml.java, 2011-5-30. 
 * 
 * Copyright 2011 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data.image.direct;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * {@link ImageSponsorDeliverySiteDao}的JDBC数据库连接实现。使用了辅助类{@link JdbcDaoHelperHandler}。
 * 
 * <AUTHOR>
 */
public class ImageSponsorDeliverySiteDaoIml extends JdbcDaoHelper implements
        ImageSponsorDeliverySiteDao {

    @Override
    public List<ImageSponsorDeliverySite> getAll() {
        return getList(new JdbcDaoHelperHandler<ImageSponsorDeliverySite>() {

            private String sql = "SELECT status,sponsorId,siteId,sponsor_ratio FROM "+tableName;

            @Override
            public ImageSponsorDeliverySite translate(ResultSet rst)
                    throws SQLException {
                return new ImageSponsorDeliverySite(rst.getInt(1),
                        rst.getLong(2), rst.getLong(3), rst.getInt(4));
            }

            @Override
            public String getSql() {
                return sql ;
            }

            @Override
            public String getExceptionMsg(Exception e) {
                return "SQL Exception while getAll(): " + e;
            }
        });
    }

    @Override
    public List<Long> getSiteIdBySponsorId(final long sponsorId) {
        return getList(new JdbcDaoHelperHandler<Long>() {
            
            String sql = "SELECT siteId FROM "+tableName +" WHERE sponsorId= "+sponsorId;
            
            @Override
            public Long translate(ResultSet rst)
                    throws SQLException {
                return rst.getLong(1);
            }

            @Override
            public String getSql() {
                return sql;
            }

            @Override
            public String getExceptionMsg(Exception e) {
                return "SQL Exception while getSiteIdBySponsorId(): " + e;
            }
        });
    }
}
