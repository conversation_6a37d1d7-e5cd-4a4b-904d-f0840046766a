/**
 * @(#)ImageSponsorDeliverySite.java, 2011-5-30. 
 * 
 * Copyright 2011 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data.image.direct;

/**
 * 图片直投相关数据结构，对应表:IMAGE_SPONSOR_DELIVERY_SITE，记录媒体（站点）与广告商的直投关系， 以及这个媒体上广告商的权重
 * 
 * <AUTHOR>
 */
public class ImageSponsorDeliverySite {
    private int status;
    private long sponsorId;
    private long siteId;
    private int weight;
    
    public ImageSponsorDeliverySite(int status, long sponsorId, long siteId,
            int weight) {
        this.status = status;
        this.sponsorId = sponsorId;
        this.siteId = siteId;
        this.weight = weight;
    }
    public int getStatus() {
        return status;
    }
    public void setStatus(int status) {
        this.status = status;
    }
    public long getSponsorId() {
        return sponsorId;
    }
    public void setSponsorId(long sponsorId) {
        this.sponsorId = sponsorId;
    }
    public long getSiteId() {
        return siteId;
    }
    public void setSiteId(long siteId) {
        this.siteId = siteId;
    }
    public int getWeight() {
        return weight;
    }
    public void setWeight(int weight) {
        this.weight = weight;
    }
}
