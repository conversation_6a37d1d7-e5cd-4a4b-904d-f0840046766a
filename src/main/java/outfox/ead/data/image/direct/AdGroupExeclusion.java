/**
 * @(#)AdGroupExeclusion.java, 2011-5-30. 
 * 
 * Copyright 2011 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data.image.direct;

/**
 * 图片直投相关数据结构，对应表:AdGroup2Cast，记录广告组是否勾选”仅投放所选媒体“
 * 
 * <AUTHOR>
 */
public class AdGroupExeclusion {

    private long adGroupId;

    /**
     * 是否勾选”仅投放所选媒体“
     */
    private boolean isExeclussive;

    public AdGroupExeclusion(long adGroupId, boolean isExeclussive) {
        this.adGroupId = adGroupId;
        this.isExeclussive = isExeclussive;
    }

    public long getAdGroupId() {
        return adGroupId;
    }

    public void setAdGroupId(long adGroupId) {
        this.adGroupId = adGroupId;
    }

    public boolean isExeclussive() {
        return isExeclussive;
    }

    public void setExeclussive(boolean isExeclussive) {
        this.isExeclussive = isExeclussive;
    }
}
