// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rtacache.proto

package outfox.ead.data.rta;

public final class RtaCache {
  private RtaCache() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface DataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:outfox.ead.data.rta.Data)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required int64 timestamp = 1;</code>
     *
     * <pre>
     * rta请求发生的时间
     * </pre>
     */
    boolean hasTimestamp();
    /**
     * <code>required int64 timestamp = 1;</code>
     *
     * <pre>
     * rta请求发生的时间
     * </pre>
     */
    long getTimestamp();

    /**
     * <code>optional int64 slot_id = 2;</code>
     *
     * <pre>
     * rta请求下发的广告位id
     * </pre>
     */
    boolean hasSlotId();
    /**
     * <code>optional int64 slot_id = 2;</code>
     *
     * <pre>
     * rta请求下发的广告位id
     * </pre>
     */
    long getSlotId();

    /**
     * <code>optional string req_id = 3;</code>
     *
     * <pre>
     * 设备上次参竞时的请求id
     * </pre>
     */
    boolean hasReqId();
    /**
     * <code>optional string req_id = 3;</code>
     *
     * <pre>
     * 设备上次参竞时的请求id
     * </pre>
     */
    java.lang.String getReqId();
    /**
     * <code>optional string req_id = 3;</code>
     *
     * <pre>
     * 设备上次参竞时的请求id
     * </pre>
     */
    com.google.protobuf.ByteString
        getReqIdBytes();

    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    boolean hasCompressRtaRequest();
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    outfox.ead.data.rta.RtaCache.CompressRtaRequest getCompressRtaRequest();
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder getCompressRtaRequestOrBuilder();
  }
  /**
   * Protobuf type {@code outfox.ead.data.rta.Data}
   *
   * <pre>
   * 没有req_id参数的rta请求，包含一些直投平台，缓存数据key可用平台id+设备id+aid
   * </pre>
   */
  public static final class Data extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:outfox.ead.data.rta.Data)
      DataOrBuilder {
    // Use Data.newBuilder() to construct.
    private Data(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Data(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Data defaultInstance;
    public static Data getDefaultInstance() {
      return defaultInstance;
    }

    public Data getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Data(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              timestamp_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              slotId_ = input.readInt64();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              reqId_ = bs;
              break;
            }
            case 34: {
              outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) == 0x00000008)) {
                subBuilder = compressRtaRequest_.toBuilder();
              }
              compressRtaRequest_ = input.readMessage(outfox.ead.data.rta.RtaCache.CompressRtaRequest.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(compressRtaRequest_);
                compressRtaRequest_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Data_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Data_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              outfox.ead.data.rta.RtaCache.Data.class, outfox.ead.data.rta.RtaCache.Data.Builder.class);
    }

    public static com.google.protobuf.Parser<Data> PARSER =
        new com.google.protobuf.AbstractParser<Data>() {
      public Data parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Data(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Data> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    public static final int TIMESTAMP_FIELD_NUMBER = 1;
    private long timestamp_;
    /**
     * <code>required int64 timestamp = 1;</code>
     *
     * <pre>
     * rta请求发生的时间
     * </pre>
     */
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 timestamp = 1;</code>
     *
     * <pre>
     * rta请求发生的时间
     * </pre>
     */
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int SLOT_ID_FIELD_NUMBER = 2;
    private long slotId_;
    /**
     * <code>optional int64 slot_id = 2;</code>
     *
     * <pre>
     * rta请求下发的广告位id
     * </pre>
     */
    public boolean hasSlotId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 slot_id = 2;</code>
     *
     * <pre>
     * rta请求下发的广告位id
     * </pre>
     */
    public long getSlotId() {
      return slotId_;
    }

    public static final int REQ_ID_FIELD_NUMBER = 3;
    private java.lang.Object reqId_;
    /**
     * <code>optional string req_id = 3;</code>
     *
     * <pre>
     * 设备上次参竞时的请求id
     * </pre>
     */
    public boolean hasReqId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string req_id = 3;</code>
     *
     * <pre>
     * 设备上次参竞时的请求id
     * </pre>
     */
    public java.lang.String getReqId() {
      java.lang.Object ref = reqId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reqId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string req_id = 3;</code>
     *
     * <pre>
     * 设备上次参竞时的请求id
     * </pre>
     */
    public com.google.protobuf.ByteString
        getReqIdBytes() {
      java.lang.Object ref = reqId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reqId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int COMPRESS_RTA_REQUEST_FIELD_NUMBER = 4;
    private outfox.ead.data.rta.RtaCache.CompressRtaRequest compressRtaRequest_;
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    public boolean hasCompressRtaRequest() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    public outfox.ead.data.rta.RtaCache.CompressRtaRequest getCompressRtaRequest() {
      return compressRtaRequest_;
    }
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    public outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder getCompressRtaRequestOrBuilder() {
      return compressRtaRequest_;
    }

    private void initFields() {
      timestamp_ = 0L;
      slotId_ = 0L;
      reqId_ = "";
      compressRtaRequest_ = outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasTimestamp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, timestamp_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, slotId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getReqIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeMessage(4, compressRtaRequest_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, timestamp_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, slotId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getReqIdBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, compressRtaRequest_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static outfox.ead.data.rta.RtaCache.Data parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.Data parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(outfox.ead.data.rta.RtaCache.Data prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code outfox.ead.data.rta.Data}
     *
     * <pre>
     * 没有req_id参数的rta请求，包含一些直投平台，缓存数据key可用平台id+设备id+aid
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:outfox.ead.data.rta.Data)
        outfox.ead.data.rta.RtaCache.DataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Data_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Data_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                outfox.ead.data.rta.RtaCache.Data.class, outfox.ead.data.rta.RtaCache.Data.Builder.class);
      }

      // Construct using outfox.ead.data.rta.RtaCache.Data.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getCompressRtaRequestFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        timestamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        slotId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        reqId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        if (compressRtaRequestBuilder_ == null) {
          compressRtaRequest_ = outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
        } else {
          compressRtaRequestBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Data_descriptor;
      }

      public outfox.ead.data.rta.RtaCache.Data getDefaultInstanceForType() {
        return outfox.ead.data.rta.RtaCache.Data.getDefaultInstance();
      }

      public outfox.ead.data.rta.RtaCache.Data build() {
        outfox.ead.data.rta.RtaCache.Data result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public outfox.ead.data.rta.RtaCache.Data buildPartial() {
        outfox.ead.data.rta.RtaCache.Data result = new outfox.ead.data.rta.RtaCache.Data(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.timestamp_ = timestamp_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.slotId_ = slotId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.reqId_ = reqId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        if (compressRtaRequestBuilder_ == null) {
          result.compressRtaRequest_ = compressRtaRequest_;
        } else {
          result.compressRtaRequest_ = compressRtaRequestBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof outfox.ead.data.rta.RtaCache.Data) {
          return mergeFrom((outfox.ead.data.rta.RtaCache.Data)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(outfox.ead.data.rta.RtaCache.Data other) {
        if (other == outfox.ead.data.rta.RtaCache.Data.getDefaultInstance()) return this;
        if (other.hasTimestamp()) {
          setTimestamp(other.getTimestamp());
        }
        if (other.hasSlotId()) {
          setSlotId(other.getSlotId());
        }
        if (other.hasReqId()) {
          bitField0_ |= 0x00000004;
          reqId_ = other.reqId_;
          onChanged();
        }
        if (other.hasCompressRtaRequest()) {
          mergeCompressRtaRequest(other.getCompressRtaRequest());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasTimestamp()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        outfox.ead.data.rta.RtaCache.Data parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (outfox.ead.data.rta.RtaCache.Data) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long timestamp_ ;
      /**
       * <code>required int64 timestamp = 1;</code>
       *
       * <pre>
       * rta请求发生的时间
       * </pre>
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 timestamp = 1;</code>
       *
       * <pre>
       * rta请求发生的时间
       * </pre>
       */
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <code>required int64 timestamp = 1;</code>
       *
       * <pre>
       * rta请求发生的时间
       * </pre>
       */
      public Builder setTimestamp(long value) {
        bitField0_ |= 0x00000001;
        timestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 timestamp = 1;</code>
       *
       * <pre>
       * rta请求发生的时间
       * </pre>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private long slotId_ ;
      /**
       * <code>optional int64 slot_id = 2;</code>
       *
       * <pre>
       * rta请求下发的广告位id
       * </pre>
       */
      public boolean hasSlotId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 slot_id = 2;</code>
       *
       * <pre>
       * rta请求下发的广告位id
       * </pre>
       */
      public long getSlotId() {
        return slotId_;
      }
      /**
       * <code>optional int64 slot_id = 2;</code>
       *
       * <pre>
       * rta请求下发的广告位id
       * </pre>
       */
      public Builder setSlotId(long value) {
        bitField0_ |= 0x00000002;
        slotId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 slot_id = 2;</code>
       *
       * <pre>
       * rta请求下发的广告位id
       * </pre>
       */
      public Builder clearSlotId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        slotId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object reqId_ = "";
      /**
       * <code>optional string req_id = 3;</code>
       *
       * <pre>
       * 设备上次参竞时的请求id
       * </pre>
       */
      public boolean hasReqId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string req_id = 3;</code>
       *
       * <pre>
       * 设备上次参竞时的请求id
       * </pre>
       */
      public java.lang.String getReqId() {
        java.lang.Object ref = reqId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            reqId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string req_id = 3;</code>
       *
       * <pre>
       * 设备上次参竞时的请求id
       * </pre>
       */
      public com.google.protobuf.ByteString
          getReqIdBytes() {
        java.lang.Object ref = reqId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          reqId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string req_id = 3;</code>
       *
       * <pre>
       * 设备上次参竞时的请求id
       * </pre>
       */
      public Builder setReqId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        reqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string req_id = 3;</code>
       *
       * <pre>
       * 设备上次参竞时的请求id
       * </pre>
       */
      public Builder clearReqId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        reqId_ = getDefaultInstance().getReqId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string req_id = 3;</code>
       *
       * <pre>
       * 设备上次参竞时的请求id
       * </pre>
       */
      public Builder setReqIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        reqId_ = value;
        onChanged();
        return this;
      }

      private outfox.ead.data.rta.RtaCache.CompressRtaRequest compressRtaRequest_ = outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          outfox.ead.data.rta.RtaCache.CompressRtaRequest, outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder, outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder> compressRtaRequestBuilder_;
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public boolean hasCompressRtaRequest() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.CompressRtaRequest getCompressRtaRequest() {
        if (compressRtaRequestBuilder_ == null) {
          return compressRtaRequest_;
        } else {
          return compressRtaRequestBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public Builder setCompressRtaRequest(outfox.ead.data.rta.RtaCache.CompressRtaRequest value) {
        if (compressRtaRequestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          compressRtaRequest_ = value;
          onChanged();
        } else {
          compressRtaRequestBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public Builder setCompressRtaRequest(
          outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder builderForValue) {
        if (compressRtaRequestBuilder_ == null) {
          compressRtaRequest_ = builderForValue.build();
          onChanged();
        } else {
          compressRtaRequestBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public Builder mergeCompressRtaRequest(outfox.ead.data.rta.RtaCache.CompressRtaRequest value) {
        if (compressRtaRequestBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008) &&
              compressRtaRequest_ != outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance()) {
            compressRtaRequest_ =
              outfox.ead.data.rta.RtaCache.CompressRtaRequest.newBuilder(compressRtaRequest_).mergeFrom(value).buildPartial();
          } else {
            compressRtaRequest_ = value;
          }
          onChanged();
        } else {
          compressRtaRequestBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public Builder clearCompressRtaRequest() {
        if (compressRtaRequestBuilder_ == null) {
          compressRtaRequest_ = outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
          onChanged();
        } else {
          compressRtaRequestBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder getCompressRtaRequestBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getCompressRtaRequestFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder getCompressRtaRequestOrBuilder() {
        if (compressRtaRequestBuilder_ != null) {
          return compressRtaRequestBuilder_.getMessageOrBuilder();
        } else {
          return compressRtaRequest_;
        }
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 4;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          outfox.ead.data.rta.RtaCache.CompressRtaRequest, outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder, outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder> 
          getCompressRtaRequestFieldBuilder() {
        if (compressRtaRequestBuilder_ == null) {
          compressRtaRequestBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              outfox.ead.data.rta.RtaCache.CompressRtaRequest, outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder, outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder>(
                  getCompressRtaRequest(),
                  getParentForChildren(),
                  isClean());
          compressRtaRequest_ = null;
        }
        return compressRtaRequestBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:outfox.ead.data.rta.Data)
    }

    static {
      defaultInstance = new Data(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:outfox.ead.data.rta.Data)
  }

  public interface DataV2OrBuilder extends
      // @@protoc_insertion_point(interface_extends:outfox.ead.data.rta.DataV2)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required int64 timestamp = 1;</code>
     *
     * <pre>
     * rta请求发生的时间
     * </pre>
     */
    boolean hasTimestamp();
    /**
     * <code>required int64 timestamp = 1;</code>
     *
     * <pre>
     * rta请求发生的时间
     * </pre>
     */
    long getTimestamp();

    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    java.util.List<outfox.ead.data.rta.RtaCache.AidBId> 
        getAidBidList();
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    outfox.ead.data.rta.RtaCache.AidBId getAidBid(int index);
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    int getAidBidCount();
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    java.util.List<? extends outfox.ead.data.rta.RtaCache.AidBIdOrBuilder> 
        getAidBidOrBuilderList();
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    outfox.ead.data.rta.RtaCache.AidBIdOrBuilder getAidBidOrBuilder(
        int index);

    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    boolean hasCompressRtaRequest();
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    outfox.ead.data.rta.RtaCache.CompressRtaRequest getCompressRtaRequest();
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder getCompressRtaRequestOrBuilder();

    /**
     * <code>optional int64 slotId = 4;</code>
     *
     * <pre>
     * 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
     * </pre>
     */
    boolean hasSlotId();
    /**
     * <code>optional int64 slotId = 4;</code>
     *
     * <pre>
     * 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
     * </pre>
     */
    long getSlotId();
  }
  /**
   * Protobuf type {@code outfox.ead.data.rta.DataV2}
   *
   * <pre>
   * 有req_id参数的rta请求，缓存数据key可用req_id
   * </pre>
   */
  public static final class DataV2 extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:outfox.ead.data.rta.DataV2)
      DataV2OrBuilder {
    // Use DataV2.newBuilder() to construct.
    private DataV2(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private DataV2(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final DataV2 defaultInstance;
    public static DataV2 getDefaultInstance() {
      return defaultInstance;
    }

    public DataV2 getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private DataV2(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              timestamp_ = input.readInt64();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                aidBid_ = new java.util.ArrayList<outfox.ead.data.rta.RtaCache.AidBId>();
                mutable_bitField0_ |= 0x00000002;
              }
              aidBid_.add(input.readMessage(outfox.ead.data.rta.RtaCache.AidBId.PARSER, extensionRegistry));
              break;
            }
            case 26: {
              outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) == 0x00000002)) {
                subBuilder = compressRtaRequest_.toBuilder();
              }
              compressRtaRequest_ = input.readMessage(outfox.ead.data.rta.RtaCache.CompressRtaRequest.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(compressRtaRequest_);
                compressRtaRequest_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              slotId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          aidBid_ = java.util.Collections.unmodifiableList(aidBid_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_DataV2_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_DataV2_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              outfox.ead.data.rta.RtaCache.DataV2.class, outfox.ead.data.rta.RtaCache.DataV2.Builder.class);
    }

    public static com.google.protobuf.Parser<DataV2> PARSER =
        new com.google.protobuf.AbstractParser<DataV2>() {
      public DataV2 parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DataV2(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<DataV2> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    public static final int TIMESTAMP_FIELD_NUMBER = 1;
    private long timestamp_;
    /**
     * <code>required int64 timestamp = 1;</code>
     *
     * <pre>
     * rta请求发生的时间
     * </pre>
     */
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 timestamp = 1;</code>
     *
     * <pre>
     * rta请求发生的时间
     * </pre>
     */
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int AIDBID_FIELD_NUMBER = 2;
    private java.util.List<outfox.ead.data.rta.RtaCache.AidBId> aidBid_;
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    public java.util.List<outfox.ead.data.rta.RtaCache.AidBId> getAidBidList() {
      return aidBid_;
    }
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    public java.util.List<? extends outfox.ead.data.rta.RtaCache.AidBIdOrBuilder> 
        getAidBidOrBuilderList() {
      return aidBid_;
    }
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    public int getAidBidCount() {
      return aidBid_.size();
    }
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    public outfox.ead.data.rta.RtaCache.AidBId getAidBid(int index) {
      return aidBid_.get(index);
    }
    /**
     * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
     *
     * <pre>
     * 参竞的aid信息
     * </pre>
     */
    public outfox.ead.data.rta.RtaCache.AidBIdOrBuilder getAidBidOrBuilder(
        int index) {
      return aidBid_.get(index);
    }

    public static final int COMPRESS_RTA_REQUEST_FIELD_NUMBER = 3;
    private outfox.ead.data.rta.RtaCache.CompressRtaRequest compressRtaRequest_;
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    public boolean hasCompressRtaRequest() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    public outfox.ead.data.rta.RtaCache.CompressRtaRequest getCompressRtaRequest() {
      return compressRtaRequest_;
    }
    /**
     * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
     *
     * <pre>
     * rta请求信息压缩后的格式
     * </pre>
     */
    public outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder getCompressRtaRequestOrBuilder() {
      return compressRtaRequest_;
    }

    public static final int SLOTID_FIELD_NUMBER = 4;
    private long slotId_;
    /**
     * <code>optional int64 slotId = 4;</code>
     *
     * <pre>
     * 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
     * </pre>
     */
    public boolean hasSlotId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 slotId = 4;</code>
     *
     * <pre>
     * 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
     * </pre>
     */
    public long getSlotId() {
      return slotId_;
    }

    private void initFields() {
      timestamp_ = 0L;
      aidBid_ = java.util.Collections.emptyList();
      compressRtaRequest_ = outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
      slotId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasTimestamp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getAidBidCount(); i++) {
        if (!getAidBid(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, timestamp_);
      }
      for (int i = 0; i < aidBid_.size(); i++) {
        output.writeMessage(2, aidBid_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(3, compressRtaRequest_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(4, slotId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, timestamp_);
      }
      for (int i = 0; i < aidBid_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, aidBid_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, compressRtaRequest_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, slotId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static outfox.ead.data.rta.RtaCache.DataV2 parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.DataV2 parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(outfox.ead.data.rta.RtaCache.DataV2 prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code outfox.ead.data.rta.DataV2}
     *
     * <pre>
     * 有req_id参数的rta请求，缓存数据key可用req_id
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:outfox.ead.data.rta.DataV2)
        outfox.ead.data.rta.RtaCache.DataV2OrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_DataV2_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_DataV2_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                outfox.ead.data.rta.RtaCache.DataV2.class, outfox.ead.data.rta.RtaCache.DataV2.Builder.class);
      }

      // Construct using outfox.ead.data.rta.RtaCache.DataV2.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getAidBidFieldBuilder();
          getCompressRtaRequestFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        timestamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (aidBidBuilder_ == null) {
          aidBid_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          aidBidBuilder_.clear();
        }
        if (compressRtaRequestBuilder_ == null) {
          compressRtaRequest_ = outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
        } else {
          compressRtaRequestBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        slotId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_DataV2_descriptor;
      }

      public outfox.ead.data.rta.RtaCache.DataV2 getDefaultInstanceForType() {
        return outfox.ead.data.rta.RtaCache.DataV2.getDefaultInstance();
      }

      public outfox.ead.data.rta.RtaCache.DataV2 build() {
        outfox.ead.data.rta.RtaCache.DataV2 result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public outfox.ead.data.rta.RtaCache.DataV2 buildPartial() {
        outfox.ead.data.rta.RtaCache.DataV2 result = new outfox.ead.data.rta.RtaCache.DataV2(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.timestamp_ = timestamp_;
        if (aidBidBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            aidBid_ = java.util.Collections.unmodifiableList(aidBid_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.aidBid_ = aidBid_;
        } else {
          result.aidBid_ = aidBidBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000002;
        }
        if (compressRtaRequestBuilder_ == null) {
          result.compressRtaRequest_ = compressRtaRequest_;
        } else {
          result.compressRtaRequest_ = compressRtaRequestBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        result.slotId_ = slotId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof outfox.ead.data.rta.RtaCache.DataV2) {
          return mergeFrom((outfox.ead.data.rta.RtaCache.DataV2)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(outfox.ead.data.rta.RtaCache.DataV2 other) {
        if (other == outfox.ead.data.rta.RtaCache.DataV2.getDefaultInstance()) return this;
        if (other.hasTimestamp()) {
          setTimestamp(other.getTimestamp());
        }
        if (aidBidBuilder_ == null) {
          if (!other.aidBid_.isEmpty()) {
            if (aidBid_.isEmpty()) {
              aidBid_ = other.aidBid_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureAidBidIsMutable();
              aidBid_.addAll(other.aidBid_);
            }
            onChanged();
          }
        } else {
          if (!other.aidBid_.isEmpty()) {
            if (aidBidBuilder_.isEmpty()) {
              aidBidBuilder_.dispose();
              aidBidBuilder_ = null;
              aidBid_ = other.aidBid_;
              bitField0_ = (bitField0_ & ~0x00000002);
              aidBidBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getAidBidFieldBuilder() : null;
            } else {
              aidBidBuilder_.addAllMessages(other.aidBid_);
            }
          }
        }
        if (other.hasCompressRtaRequest()) {
          mergeCompressRtaRequest(other.getCompressRtaRequest());
        }
        if (other.hasSlotId()) {
          setSlotId(other.getSlotId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasTimestamp()) {
          
          return false;
        }
        for (int i = 0; i < getAidBidCount(); i++) {
          if (!getAidBid(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        outfox.ead.data.rta.RtaCache.DataV2 parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (outfox.ead.data.rta.RtaCache.DataV2) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long timestamp_ ;
      /**
       * <code>required int64 timestamp = 1;</code>
       *
       * <pre>
       * rta请求发生的时间
       * </pre>
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 timestamp = 1;</code>
       *
       * <pre>
       * rta请求发生的时间
       * </pre>
       */
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <code>required int64 timestamp = 1;</code>
       *
       * <pre>
       * rta请求发生的时间
       * </pre>
       */
      public Builder setTimestamp(long value) {
        bitField0_ |= 0x00000001;
        timestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 timestamp = 1;</code>
       *
       * <pre>
       * rta请求发生的时间
       * </pre>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<outfox.ead.data.rta.RtaCache.AidBId> aidBid_ =
        java.util.Collections.emptyList();
      private void ensureAidBidIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          aidBid_ = new java.util.ArrayList<outfox.ead.data.rta.RtaCache.AidBId>(aidBid_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          outfox.ead.data.rta.RtaCache.AidBId, outfox.ead.data.rta.RtaCache.AidBId.Builder, outfox.ead.data.rta.RtaCache.AidBIdOrBuilder> aidBidBuilder_;

      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public java.util.List<outfox.ead.data.rta.RtaCache.AidBId> getAidBidList() {
        if (aidBidBuilder_ == null) {
          return java.util.Collections.unmodifiableList(aidBid_);
        } else {
          return aidBidBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public int getAidBidCount() {
        if (aidBidBuilder_ == null) {
          return aidBid_.size();
        } else {
          return aidBidBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.AidBId getAidBid(int index) {
        if (aidBidBuilder_ == null) {
          return aidBid_.get(index);
        } else {
          return aidBidBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder setAidBid(
          int index, outfox.ead.data.rta.RtaCache.AidBId value) {
        if (aidBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAidBidIsMutable();
          aidBid_.set(index, value);
          onChanged();
        } else {
          aidBidBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder setAidBid(
          int index, outfox.ead.data.rta.RtaCache.AidBId.Builder builderForValue) {
        if (aidBidBuilder_ == null) {
          ensureAidBidIsMutable();
          aidBid_.set(index, builderForValue.build());
          onChanged();
        } else {
          aidBidBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder addAidBid(outfox.ead.data.rta.RtaCache.AidBId value) {
        if (aidBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAidBidIsMutable();
          aidBid_.add(value);
          onChanged();
        } else {
          aidBidBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder addAidBid(
          int index, outfox.ead.data.rta.RtaCache.AidBId value) {
        if (aidBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAidBidIsMutable();
          aidBid_.add(index, value);
          onChanged();
        } else {
          aidBidBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder addAidBid(
          outfox.ead.data.rta.RtaCache.AidBId.Builder builderForValue) {
        if (aidBidBuilder_ == null) {
          ensureAidBidIsMutable();
          aidBid_.add(builderForValue.build());
          onChanged();
        } else {
          aidBidBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder addAidBid(
          int index, outfox.ead.data.rta.RtaCache.AidBId.Builder builderForValue) {
        if (aidBidBuilder_ == null) {
          ensureAidBidIsMutable();
          aidBid_.add(index, builderForValue.build());
          onChanged();
        } else {
          aidBidBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder addAllAidBid(
          java.lang.Iterable<? extends outfox.ead.data.rta.RtaCache.AidBId> values) {
        if (aidBidBuilder_ == null) {
          ensureAidBidIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, aidBid_);
          onChanged();
        } else {
          aidBidBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder clearAidBid() {
        if (aidBidBuilder_ == null) {
          aidBid_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          aidBidBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public Builder removeAidBid(int index) {
        if (aidBidBuilder_ == null) {
          ensureAidBidIsMutable();
          aidBid_.remove(index);
          onChanged();
        } else {
          aidBidBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.AidBId.Builder getAidBidBuilder(
          int index) {
        return getAidBidFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.AidBIdOrBuilder getAidBidOrBuilder(
          int index) {
        if (aidBidBuilder_ == null) {
          return aidBid_.get(index);  } else {
          return aidBidBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public java.util.List<? extends outfox.ead.data.rta.RtaCache.AidBIdOrBuilder> 
           getAidBidOrBuilderList() {
        if (aidBidBuilder_ != null) {
          return aidBidBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(aidBid_);
        }
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.AidBId.Builder addAidBidBuilder() {
        return getAidBidFieldBuilder().addBuilder(
            outfox.ead.data.rta.RtaCache.AidBId.getDefaultInstance());
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.AidBId.Builder addAidBidBuilder(
          int index) {
        return getAidBidFieldBuilder().addBuilder(
            index, outfox.ead.data.rta.RtaCache.AidBId.getDefaultInstance());
      }
      /**
       * <code>repeated .outfox.ead.data.rta.AidBId aidBid = 2;</code>
       *
       * <pre>
       * 参竞的aid信息
       * </pre>
       */
      public java.util.List<outfox.ead.data.rta.RtaCache.AidBId.Builder> 
           getAidBidBuilderList() {
        return getAidBidFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          outfox.ead.data.rta.RtaCache.AidBId, outfox.ead.data.rta.RtaCache.AidBId.Builder, outfox.ead.data.rta.RtaCache.AidBIdOrBuilder> 
          getAidBidFieldBuilder() {
        if (aidBidBuilder_ == null) {
          aidBidBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              outfox.ead.data.rta.RtaCache.AidBId, outfox.ead.data.rta.RtaCache.AidBId.Builder, outfox.ead.data.rta.RtaCache.AidBIdOrBuilder>(
                  aidBid_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          aidBid_ = null;
        }
        return aidBidBuilder_;
      }

      private outfox.ead.data.rta.RtaCache.CompressRtaRequest compressRtaRequest_ = outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          outfox.ead.data.rta.RtaCache.CompressRtaRequest, outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder, outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder> compressRtaRequestBuilder_;
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public boolean hasCompressRtaRequest() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.CompressRtaRequest getCompressRtaRequest() {
        if (compressRtaRequestBuilder_ == null) {
          return compressRtaRequest_;
        } else {
          return compressRtaRequestBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public Builder setCompressRtaRequest(outfox.ead.data.rta.RtaCache.CompressRtaRequest value) {
        if (compressRtaRequestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          compressRtaRequest_ = value;
          onChanged();
        } else {
          compressRtaRequestBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public Builder setCompressRtaRequest(
          outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder builderForValue) {
        if (compressRtaRequestBuilder_ == null) {
          compressRtaRequest_ = builderForValue.build();
          onChanged();
        } else {
          compressRtaRequestBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public Builder mergeCompressRtaRequest(outfox.ead.data.rta.RtaCache.CompressRtaRequest value) {
        if (compressRtaRequestBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004) &&
              compressRtaRequest_ != outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance()) {
            compressRtaRequest_ =
              outfox.ead.data.rta.RtaCache.CompressRtaRequest.newBuilder(compressRtaRequest_).mergeFrom(value).buildPartial();
          } else {
            compressRtaRequest_ = value;
          }
          onChanged();
        } else {
          compressRtaRequestBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public Builder clearCompressRtaRequest() {
        if (compressRtaRequestBuilder_ == null) {
          compressRtaRequest_ = outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
          onChanged();
        } else {
          compressRtaRequestBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder getCompressRtaRequestBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCompressRtaRequestFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      public outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder getCompressRtaRequestOrBuilder() {
        if (compressRtaRequestBuilder_ != null) {
          return compressRtaRequestBuilder_.getMessageOrBuilder();
        } else {
          return compressRtaRequest_;
        }
      }
      /**
       * <code>optional .outfox.ead.data.rta.CompressRtaRequest compress_rta_request = 3;</code>
       *
       * <pre>
       * rta请求信息压缩后的格式
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          outfox.ead.data.rta.RtaCache.CompressRtaRequest, outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder, outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder> 
          getCompressRtaRequestFieldBuilder() {
        if (compressRtaRequestBuilder_ == null) {
          compressRtaRequestBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              outfox.ead.data.rta.RtaCache.CompressRtaRequest, outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder, outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder>(
                  getCompressRtaRequest(),
                  getParentForChildren(),
                  isClean());
          compressRtaRequest_ = null;
        }
        return compressRtaRequestBuilder_;
      }

      private long slotId_ ;
      /**
       * <code>optional int64 slotId = 4;</code>
       *
       * <pre>
       * 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
       * </pre>
       */
      public boolean hasSlotId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int64 slotId = 4;</code>
       *
       * <pre>
       * 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
       * </pre>
       */
      public long getSlotId() {
        return slotId_;
      }
      /**
       * <code>optional int64 slotId = 4;</code>
       *
       * <pre>
       * 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
       * </pre>
       */
      public Builder setSlotId(long value) {
        bitField0_ |= 0x00000008;
        slotId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 slotId = 4;</code>
       *
       * <pre>
       * 缓存的key如果包含了rtaMappingId, 则记录下当前rta请求下发的广告位id, 不再从aidBid中获取
       * </pre>
       */
      public Builder clearSlotId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        slotId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:outfox.ead.data.rta.DataV2)
    }

    static {
      defaultInstance = new DataV2(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:outfox.ead.data.rta.DataV2)
  }

  public interface CompressRtaRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:outfox.ead.data.rta.CompressRtaRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 imei = 1;</code>
     */
    boolean hasImei();
    /**
     * <code>optional int32 imei = 1;</code>
     */
    int getImei();

    /**
     * <code>optional int32 imeiMd5 = 2;</code>
     */
    boolean hasImeiMd5();
    /**
     * <code>optional int32 imeiMd5 = 2;</code>
     */
    int getImeiMd5();

    /**
     * <code>optional int32 oaid = 4;</code>
     */
    boolean hasOaid();
    /**
     * <code>optional int32 oaid = 4;</code>
     */
    int getOaid();

    /**
     * <code>optional int32 oaidMd5 = 5;</code>
     */
    boolean hasOaidMd5();
    /**
     * <code>optional int32 oaidMd5 = 5;</code>
     */
    int getOaidMd5();

    /**
     * <code>optional int32 idfa = 6;</code>
     */
    boolean hasIdfa();
    /**
     * <code>optional int32 idfa = 6;</code>
     */
    int getIdfa();

    /**
     * <code>optional int32 idfaMd5 = 7;</code>
     */
    boolean hasIdfaMd5();
    /**
     * <code>optional int32 idfaMd5 = 7;</code>
     */
    int getIdfaMd5();

    /**
     * <code>repeated int32 caidAndVersion = 8;</code>
     */
    java.util.List<java.lang.Integer> getCaidAndVersionList();
    /**
     * <code>repeated int32 caidAndVersion = 8;</code>
     */
    int getCaidAndVersionCount();
    /**
     * <code>repeated int32 caidAndVersion = 8;</code>
     */
    int getCaidAndVersion(int index);

    /**
     * <code>repeated int32 caidMd5AndVersion = 9;</code>
     */
    java.util.List<java.lang.Integer> getCaidMd5AndVersionList();
    /**
     * <code>repeated int32 caidMd5AndVersion = 9;</code>
     */
    int getCaidMd5AndVersionCount();
    /**
     * <code>repeated int32 caidMd5AndVersion = 9;</code>
     */
    int getCaidMd5AndVersion(int index);

    /**
     * <code>optional int32 ip = 11;</code>
     */
    boolean hasIp();
    /**
     * <code>optional int32 ip = 11;</code>
     */
    int getIp();

    /**
     * <code>optional int32 ua = 12;</code>
     */
    boolean hasUa();
    /**
     * <code>optional int32 ua = 12;</code>
     */
    int getUa();

    /**
     * <code>optional int32 model = 13;</code>
     */
    boolean hasModel();
    /**
     * <code>optional int32 model = 13;</code>
     */
    int getModel();

    /**
     * <code>optional int32 osv = 14;</code>
     */
    boolean hasOsv();
    /**
     * <code>optional int32 osv = 14;</code>
     */
    int getOsv();
  }
  /**
   * Protobuf type {@code outfox.ead.data.rta.CompressRtaRequest}
   */
  public static final class CompressRtaRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:outfox.ead.data.rta.CompressRtaRequest)
      CompressRtaRequestOrBuilder {
    // Use CompressRtaRequest.newBuilder() to construct.
    private CompressRtaRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private CompressRtaRequest(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final CompressRtaRequest defaultInstance;
    public static CompressRtaRequest getDefaultInstance() {
      return defaultInstance;
    }

    public CompressRtaRequest getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private CompressRtaRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              imei_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              imeiMd5_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              oaid_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              oaidMd5_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000010;
              idfa_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000020;
              idfaMd5_ = input.readInt32();
              break;
            }
            case 64: {
              if (!((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
                caidAndVersion_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000040;
              }
              caidAndVersion_.add(input.readInt32());
              break;
            }
            case 66: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000040) == 0x00000040) && input.getBytesUntilLimit() > 0) {
                caidAndVersion_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000040;
              }
              while (input.getBytesUntilLimit() > 0) {
                caidAndVersion_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 72: {
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
                caidMd5AndVersion_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000080;
              }
              caidMd5AndVersion_.add(input.readInt32());
              break;
            }
            case 74: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000080) == 0x00000080) && input.getBytesUntilLimit() > 0) {
                caidMd5AndVersion_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000080;
              }
              while (input.getBytesUntilLimit() > 0) {
                caidMd5AndVersion_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 88: {
              bitField0_ |= 0x00000040;
              ip_ = input.readInt32();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000080;
              ua_ = input.readInt32();
              break;
            }
            case 104: {
              bitField0_ |= 0x00000100;
              model_ = input.readInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00000200;
              osv_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
          caidAndVersion_ = java.util.Collections.unmodifiableList(caidAndVersion_);
        }
        if (((mutable_bitField0_ & 0x00000080) == 0x00000080)) {
          caidMd5AndVersion_ = java.util.Collections.unmodifiableList(caidMd5AndVersion_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_CompressRtaRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_CompressRtaRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              outfox.ead.data.rta.RtaCache.CompressRtaRequest.class, outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder.class);
    }

    public static com.google.protobuf.Parser<CompressRtaRequest> PARSER =
        new com.google.protobuf.AbstractParser<CompressRtaRequest>() {
      public CompressRtaRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CompressRtaRequest(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<CompressRtaRequest> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    public static final int IMEI_FIELD_NUMBER = 1;
    private int imei_;
    /**
     * <code>optional int32 imei = 1;</code>
     */
    public boolean hasImei() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 imei = 1;</code>
     */
    public int getImei() {
      return imei_;
    }

    public static final int IMEIMD5_FIELD_NUMBER = 2;
    private int imeiMd5_;
    /**
     * <code>optional int32 imeiMd5 = 2;</code>
     */
    public boolean hasImeiMd5() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 imeiMd5 = 2;</code>
     */
    public int getImeiMd5() {
      return imeiMd5_;
    }

    public static final int OAID_FIELD_NUMBER = 4;
    private int oaid_;
    /**
     * <code>optional int32 oaid = 4;</code>
     */
    public boolean hasOaid() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 oaid = 4;</code>
     */
    public int getOaid() {
      return oaid_;
    }

    public static final int OAIDMD5_FIELD_NUMBER = 5;
    private int oaidMd5_;
    /**
     * <code>optional int32 oaidMd5 = 5;</code>
     */
    public boolean hasOaidMd5() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 oaidMd5 = 5;</code>
     */
    public int getOaidMd5() {
      return oaidMd5_;
    }

    public static final int IDFA_FIELD_NUMBER = 6;
    private int idfa_;
    /**
     * <code>optional int32 idfa = 6;</code>
     */
    public boolean hasIdfa() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 idfa = 6;</code>
     */
    public int getIdfa() {
      return idfa_;
    }

    public static final int IDFAMD5_FIELD_NUMBER = 7;
    private int idfaMd5_;
    /**
     * <code>optional int32 idfaMd5 = 7;</code>
     */
    public boolean hasIdfaMd5() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int32 idfaMd5 = 7;</code>
     */
    public int getIdfaMd5() {
      return idfaMd5_;
    }

    public static final int CAIDANDVERSION_FIELD_NUMBER = 8;
    private java.util.List<java.lang.Integer> caidAndVersion_;
    /**
     * <code>repeated int32 caidAndVersion = 8;</code>
     */
    public java.util.List<java.lang.Integer>
        getCaidAndVersionList() {
      return caidAndVersion_;
    }
    /**
     * <code>repeated int32 caidAndVersion = 8;</code>
     */
    public int getCaidAndVersionCount() {
      return caidAndVersion_.size();
    }
    /**
     * <code>repeated int32 caidAndVersion = 8;</code>
     */
    public int getCaidAndVersion(int index) {
      return caidAndVersion_.get(index);
    }

    public static final int CAIDMD5ANDVERSION_FIELD_NUMBER = 9;
    private java.util.List<java.lang.Integer> caidMd5AndVersion_;
    /**
     * <code>repeated int32 caidMd5AndVersion = 9;</code>
     */
    public java.util.List<java.lang.Integer>
        getCaidMd5AndVersionList() {
      return caidMd5AndVersion_;
    }
    /**
     * <code>repeated int32 caidMd5AndVersion = 9;</code>
     */
    public int getCaidMd5AndVersionCount() {
      return caidMd5AndVersion_.size();
    }
    /**
     * <code>repeated int32 caidMd5AndVersion = 9;</code>
     */
    public int getCaidMd5AndVersion(int index) {
      return caidMd5AndVersion_.get(index);
    }

    public static final int IP_FIELD_NUMBER = 11;
    private int ip_;
    /**
     * <code>optional int32 ip = 11;</code>
     */
    public boolean hasIp() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int32 ip = 11;</code>
     */
    public int getIp() {
      return ip_;
    }

    public static final int UA_FIELD_NUMBER = 12;
    private int ua_;
    /**
     * <code>optional int32 ua = 12;</code>
     */
    public boolean hasUa() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional int32 ua = 12;</code>
     */
    public int getUa() {
      return ua_;
    }

    public static final int MODEL_FIELD_NUMBER = 13;
    private int model_;
    /**
     * <code>optional int32 model = 13;</code>
     */
    public boolean hasModel() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional int32 model = 13;</code>
     */
    public int getModel() {
      return model_;
    }

    public static final int OSV_FIELD_NUMBER = 14;
    private int osv_;
    /**
     * <code>optional int32 osv = 14;</code>
     */
    public boolean hasOsv() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional int32 osv = 14;</code>
     */
    public int getOsv() {
      return osv_;
    }

    private void initFields() {
      imei_ = 0;
      imeiMd5_ = 0;
      oaid_ = 0;
      oaidMd5_ = 0;
      idfa_ = 0;
      idfaMd5_ = 0;
      caidAndVersion_ = java.util.Collections.emptyList();
      caidMd5AndVersion_ = java.util.Collections.emptyList();
      ip_ = 0;
      ua_ = 0;
      model_ = 0;
      osv_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, imei_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, imeiMd5_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(4, oaid_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(5, oaidMd5_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(6, idfa_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(7, idfaMd5_);
      }
      for (int i = 0; i < caidAndVersion_.size(); i++) {
        output.writeInt32(8, caidAndVersion_.get(i));
      }
      for (int i = 0; i < caidMd5AndVersion_.size(); i++) {
        output.writeInt32(9, caidMd5AndVersion_.get(i));
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(11, ip_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt32(12, ua_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeInt32(13, model_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeInt32(14, osv_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, imei_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, imeiMd5_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, oaid_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, oaidMd5_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, idfa_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, idfaMd5_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < caidAndVersion_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(caidAndVersion_.get(i));
        }
        size += dataSize;
        size += 1 * getCaidAndVersionList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < caidMd5AndVersion_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(caidMd5AndVersion_.get(i));
        }
        size += dataSize;
        size += 1 * getCaidMd5AndVersionList().size();
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, ip_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, ua_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, model_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(14, osv_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.CompressRtaRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(outfox.ead.data.rta.RtaCache.CompressRtaRequest prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code outfox.ead.data.rta.CompressRtaRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:outfox.ead.data.rta.CompressRtaRequest)
        outfox.ead.data.rta.RtaCache.CompressRtaRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_CompressRtaRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_CompressRtaRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                outfox.ead.data.rta.RtaCache.CompressRtaRequest.class, outfox.ead.data.rta.RtaCache.CompressRtaRequest.Builder.class);
      }

      // Construct using outfox.ead.data.rta.RtaCache.CompressRtaRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        imei_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        imeiMd5_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        oaid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        oaidMd5_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        idfa_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        idfaMd5_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        caidAndVersion_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        caidMd5AndVersion_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        ip_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        ua_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        model_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        osv_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_CompressRtaRequest_descriptor;
      }

      public outfox.ead.data.rta.RtaCache.CompressRtaRequest getDefaultInstanceForType() {
        return outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance();
      }

      public outfox.ead.data.rta.RtaCache.CompressRtaRequest build() {
        outfox.ead.data.rta.RtaCache.CompressRtaRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public outfox.ead.data.rta.RtaCache.CompressRtaRequest buildPartial() {
        outfox.ead.data.rta.RtaCache.CompressRtaRequest result = new outfox.ead.data.rta.RtaCache.CompressRtaRequest(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.imei_ = imei_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.imeiMd5_ = imeiMd5_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.oaid_ = oaid_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.oaidMd5_ = oaidMd5_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.idfa_ = idfa_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.idfaMd5_ = idfaMd5_;
        if (((bitField0_ & 0x00000040) == 0x00000040)) {
          caidAndVersion_ = java.util.Collections.unmodifiableList(caidAndVersion_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.caidAndVersion_ = caidAndVersion_;
        if (((bitField0_ & 0x00000080) == 0x00000080)) {
          caidMd5AndVersion_ = java.util.Collections.unmodifiableList(caidMd5AndVersion_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.caidMd5AndVersion_ = caidMd5AndVersion_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000040;
        }
        result.ip_ = ip_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000080;
        }
        result.ua_ = ua_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000100;
        }
        result.model_ = model_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000200;
        }
        result.osv_ = osv_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof outfox.ead.data.rta.RtaCache.CompressRtaRequest) {
          return mergeFrom((outfox.ead.data.rta.RtaCache.CompressRtaRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(outfox.ead.data.rta.RtaCache.CompressRtaRequest other) {
        if (other == outfox.ead.data.rta.RtaCache.CompressRtaRequest.getDefaultInstance()) return this;
        if (other.hasImei()) {
          setImei(other.getImei());
        }
        if (other.hasImeiMd5()) {
          setImeiMd5(other.getImeiMd5());
        }
        if (other.hasOaid()) {
          setOaid(other.getOaid());
        }
        if (other.hasOaidMd5()) {
          setOaidMd5(other.getOaidMd5());
        }
        if (other.hasIdfa()) {
          setIdfa(other.getIdfa());
        }
        if (other.hasIdfaMd5()) {
          setIdfaMd5(other.getIdfaMd5());
        }
        if (!other.caidAndVersion_.isEmpty()) {
          if (caidAndVersion_.isEmpty()) {
            caidAndVersion_ = other.caidAndVersion_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureCaidAndVersionIsMutable();
            caidAndVersion_.addAll(other.caidAndVersion_);
          }
          onChanged();
        }
        if (!other.caidMd5AndVersion_.isEmpty()) {
          if (caidMd5AndVersion_.isEmpty()) {
            caidMd5AndVersion_ = other.caidMd5AndVersion_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureCaidMd5AndVersionIsMutable();
            caidMd5AndVersion_.addAll(other.caidMd5AndVersion_);
          }
          onChanged();
        }
        if (other.hasIp()) {
          setIp(other.getIp());
        }
        if (other.hasUa()) {
          setUa(other.getUa());
        }
        if (other.hasModel()) {
          setModel(other.getModel());
        }
        if (other.hasOsv()) {
          setOsv(other.getOsv());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        outfox.ead.data.rta.RtaCache.CompressRtaRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (outfox.ead.data.rta.RtaCache.CompressRtaRequest) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int imei_ ;
      /**
       * <code>optional int32 imei = 1;</code>
       */
      public boolean hasImei() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int32 imei = 1;</code>
       */
      public int getImei() {
        return imei_;
      }
      /**
       * <code>optional int32 imei = 1;</code>
       */
      public Builder setImei(int value) {
        bitField0_ |= 0x00000001;
        imei_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 imei = 1;</code>
       */
      public Builder clearImei() {
        bitField0_ = (bitField0_ & ~0x00000001);
        imei_ = 0;
        onChanged();
        return this;
      }

      private int imeiMd5_ ;
      /**
       * <code>optional int32 imeiMd5 = 2;</code>
       */
      public boolean hasImeiMd5() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 imeiMd5 = 2;</code>
       */
      public int getImeiMd5() {
        return imeiMd5_;
      }
      /**
       * <code>optional int32 imeiMd5 = 2;</code>
       */
      public Builder setImeiMd5(int value) {
        bitField0_ |= 0x00000002;
        imeiMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 imeiMd5 = 2;</code>
       */
      public Builder clearImeiMd5() {
        bitField0_ = (bitField0_ & ~0x00000002);
        imeiMd5_ = 0;
        onChanged();
        return this;
      }

      private int oaid_ ;
      /**
       * <code>optional int32 oaid = 4;</code>
       */
      public boolean hasOaid() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 oaid = 4;</code>
       */
      public int getOaid() {
        return oaid_;
      }
      /**
       * <code>optional int32 oaid = 4;</code>
       */
      public Builder setOaid(int value) {
        bitField0_ |= 0x00000004;
        oaid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 oaid = 4;</code>
       */
      public Builder clearOaid() {
        bitField0_ = (bitField0_ & ~0x00000004);
        oaid_ = 0;
        onChanged();
        return this;
      }

      private int oaidMd5_ ;
      /**
       * <code>optional int32 oaidMd5 = 5;</code>
       */
      public boolean hasOaidMd5() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 oaidMd5 = 5;</code>
       */
      public int getOaidMd5() {
        return oaidMd5_;
      }
      /**
       * <code>optional int32 oaidMd5 = 5;</code>
       */
      public Builder setOaidMd5(int value) {
        bitField0_ |= 0x00000008;
        oaidMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 oaidMd5 = 5;</code>
       */
      public Builder clearOaidMd5() {
        bitField0_ = (bitField0_ & ~0x00000008);
        oaidMd5_ = 0;
        onChanged();
        return this;
      }

      private int idfa_ ;
      /**
       * <code>optional int32 idfa = 6;</code>
       */
      public boolean hasIdfa() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 idfa = 6;</code>
       */
      public int getIdfa() {
        return idfa_;
      }
      /**
       * <code>optional int32 idfa = 6;</code>
       */
      public Builder setIdfa(int value) {
        bitField0_ |= 0x00000010;
        idfa_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 idfa = 6;</code>
       */
      public Builder clearIdfa() {
        bitField0_ = (bitField0_ & ~0x00000010);
        idfa_ = 0;
        onChanged();
        return this;
      }

      private int idfaMd5_ ;
      /**
       * <code>optional int32 idfaMd5 = 7;</code>
       */
      public boolean hasIdfaMd5() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int32 idfaMd5 = 7;</code>
       */
      public int getIdfaMd5() {
        return idfaMd5_;
      }
      /**
       * <code>optional int32 idfaMd5 = 7;</code>
       */
      public Builder setIdfaMd5(int value) {
        bitField0_ |= 0x00000020;
        idfaMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 idfaMd5 = 7;</code>
       */
      public Builder clearIdfaMd5() {
        bitField0_ = (bitField0_ & ~0x00000020);
        idfaMd5_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> caidAndVersion_ = java.util.Collections.emptyList();
      private void ensureCaidAndVersionIsMutable() {
        if (!((bitField0_ & 0x00000040) == 0x00000040)) {
          caidAndVersion_ = new java.util.ArrayList<java.lang.Integer>(caidAndVersion_);
          bitField0_ |= 0x00000040;
         }
      }
      /**
       * <code>repeated int32 caidAndVersion = 8;</code>
       */
      public java.util.List<java.lang.Integer>
          getCaidAndVersionList() {
        return java.util.Collections.unmodifiableList(caidAndVersion_);
      }
      /**
       * <code>repeated int32 caidAndVersion = 8;</code>
       */
      public int getCaidAndVersionCount() {
        return caidAndVersion_.size();
      }
      /**
       * <code>repeated int32 caidAndVersion = 8;</code>
       */
      public int getCaidAndVersion(int index) {
        return caidAndVersion_.get(index);
      }
      /**
       * <code>repeated int32 caidAndVersion = 8;</code>
       */
      public Builder setCaidAndVersion(
          int index, int value) {
        ensureCaidAndVersionIsMutable();
        caidAndVersion_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 caidAndVersion = 8;</code>
       */
      public Builder addCaidAndVersion(int value) {
        ensureCaidAndVersionIsMutable();
        caidAndVersion_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 caidAndVersion = 8;</code>
       */
      public Builder addAllCaidAndVersion(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureCaidAndVersionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, caidAndVersion_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 caidAndVersion = 8;</code>
       */
      public Builder clearCaidAndVersion() {
        caidAndVersion_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> caidMd5AndVersion_ = java.util.Collections.emptyList();
      private void ensureCaidMd5AndVersionIsMutable() {
        if (!((bitField0_ & 0x00000080) == 0x00000080)) {
          caidMd5AndVersion_ = new java.util.ArrayList<java.lang.Integer>(caidMd5AndVersion_);
          bitField0_ |= 0x00000080;
         }
      }
      /**
       * <code>repeated int32 caidMd5AndVersion = 9;</code>
       */
      public java.util.List<java.lang.Integer>
          getCaidMd5AndVersionList() {
        return java.util.Collections.unmodifiableList(caidMd5AndVersion_);
      }
      /**
       * <code>repeated int32 caidMd5AndVersion = 9;</code>
       */
      public int getCaidMd5AndVersionCount() {
        return caidMd5AndVersion_.size();
      }
      /**
       * <code>repeated int32 caidMd5AndVersion = 9;</code>
       */
      public int getCaidMd5AndVersion(int index) {
        return caidMd5AndVersion_.get(index);
      }
      /**
       * <code>repeated int32 caidMd5AndVersion = 9;</code>
       */
      public Builder setCaidMd5AndVersion(
          int index, int value) {
        ensureCaidMd5AndVersionIsMutable();
        caidMd5AndVersion_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 caidMd5AndVersion = 9;</code>
       */
      public Builder addCaidMd5AndVersion(int value) {
        ensureCaidMd5AndVersionIsMutable();
        caidMd5AndVersion_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 caidMd5AndVersion = 9;</code>
       */
      public Builder addAllCaidMd5AndVersion(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureCaidMd5AndVersionIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, caidMd5AndVersion_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 caidMd5AndVersion = 9;</code>
       */
      public Builder clearCaidMd5AndVersion() {
        caidMd5AndVersion_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }

      private int ip_ ;
      /**
       * <code>optional int32 ip = 11;</code>
       */
      public boolean hasIp() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional int32 ip = 11;</code>
       */
      public int getIp() {
        return ip_;
      }
      /**
       * <code>optional int32 ip = 11;</code>
       */
      public Builder setIp(int value) {
        bitField0_ |= 0x00000100;
        ip_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ip = 11;</code>
       */
      public Builder clearIp() {
        bitField0_ = (bitField0_ & ~0x00000100);
        ip_ = 0;
        onChanged();
        return this;
      }

      private int ua_ ;
      /**
       * <code>optional int32 ua = 12;</code>
       */
      public boolean hasUa() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional int32 ua = 12;</code>
       */
      public int getUa() {
        return ua_;
      }
      /**
       * <code>optional int32 ua = 12;</code>
       */
      public Builder setUa(int value) {
        bitField0_ |= 0x00000200;
        ua_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ua = 12;</code>
       */
      public Builder clearUa() {
        bitField0_ = (bitField0_ & ~0x00000200);
        ua_ = 0;
        onChanged();
        return this;
      }

      private int model_ ;
      /**
       * <code>optional int32 model = 13;</code>
       */
      public boolean hasModel() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional int32 model = 13;</code>
       */
      public int getModel() {
        return model_;
      }
      /**
       * <code>optional int32 model = 13;</code>
       */
      public Builder setModel(int value) {
        bitField0_ |= 0x00000400;
        model_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 model = 13;</code>
       */
      public Builder clearModel() {
        bitField0_ = (bitField0_ & ~0x00000400);
        model_ = 0;
        onChanged();
        return this;
      }

      private int osv_ ;
      /**
       * <code>optional int32 osv = 14;</code>
       */
      public boolean hasOsv() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional int32 osv = 14;</code>
       */
      public int getOsv() {
        return osv_;
      }
      /**
       * <code>optional int32 osv = 14;</code>
       */
      public Builder setOsv(int value) {
        bitField0_ |= 0x00000800;
        osv_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 osv = 14;</code>
       */
      public Builder clearOsv() {
        bitField0_ = (bitField0_ & ~0x00000800);
        osv_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:outfox.ead.data.rta.CompressRtaRequest)
    }

    static {
      defaultInstance = new CompressRtaRequest(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:outfox.ead.data.rta.CompressRtaRequest)
  }

  public interface AidBIdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:outfox.ead.data.rta.AidBId)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required int64 aid = 1;</code>
     */
    boolean hasAid();
    /**
     * <code>required int64 aid = 1;</code>
     */
    long getAid();

    /**
     * <code>optional int64 slot_id = 2;</code>
     *
     * <pre>
     * rta请求下发的广告位id
     * </pre>
     */
    boolean hasSlotId();
    /**
     * <code>optional int64 slot_id = 2;</code>
     *
     * <pre>
     * rta请求下发的广告位id
     * </pre>
     */
    long getSlotId();
  }
  /**
   * Protobuf type {@code outfox.ead.data.rta.AidBId}
   */
  public static final class AidBId extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:outfox.ead.data.rta.AidBId)
      AidBIdOrBuilder {
    // Use AidBId.newBuilder() to construct.
    private AidBId(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private AidBId(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final AidBId defaultInstance;
    public static AidBId getDefaultInstance() {
      return defaultInstance;
    }

    public AidBId getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private AidBId(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              aid_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              slotId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_AidBId_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_AidBId_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              outfox.ead.data.rta.RtaCache.AidBId.class, outfox.ead.data.rta.RtaCache.AidBId.Builder.class);
    }

    public static com.google.protobuf.Parser<AidBId> PARSER =
        new com.google.protobuf.AbstractParser<AidBId>() {
      public AidBId parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AidBId(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<AidBId> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    public static final int AID_FIELD_NUMBER = 1;
    private long aid_;
    /**
     * <code>required int64 aid = 1;</code>
     */
    public boolean hasAid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 aid = 1;</code>
     */
    public long getAid() {
      return aid_;
    }

    public static final int SLOT_ID_FIELD_NUMBER = 2;
    private long slotId_;
    /**
     * <code>optional int64 slot_id = 2;</code>
     *
     * <pre>
     * rta请求下发的广告位id
     * </pre>
     */
    public boolean hasSlotId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 slot_id = 2;</code>
     *
     * <pre>
     * rta请求下发的广告位id
     * </pre>
     */
    public long getSlotId() {
      return slotId_;
    }

    private void initFields() {
      aid_ = 0L;
      slotId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasAid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, aid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, slotId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, aid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, slotId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static outfox.ead.data.rta.RtaCache.AidBId parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.AidBId parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(outfox.ead.data.rta.RtaCache.AidBId prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code outfox.ead.data.rta.AidBId}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:outfox.ead.data.rta.AidBId)
        outfox.ead.data.rta.RtaCache.AidBIdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_AidBId_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_AidBId_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                outfox.ead.data.rta.RtaCache.AidBId.class, outfox.ead.data.rta.RtaCache.AidBId.Builder.class);
      }

      // Construct using outfox.ead.data.rta.RtaCache.AidBId.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        aid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        slotId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_AidBId_descriptor;
      }

      public outfox.ead.data.rta.RtaCache.AidBId getDefaultInstanceForType() {
        return outfox.ead.data.rta.RtaCache.AidBId.getDefaultInstance();
      }

      public outfox.ead.data.rta.RtaCache.AidBId build() {
        outfox.ead.data.rta.RtaCache.AidBId result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public outfox.ead.data.rta.RtaCache.AidBId buildPartial() {
        outfox.ead.data.rta.RtaCache.AidBId result = new outfox.ead.data.rta.RtaCache.AidBId(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.aid_ = aid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.slotId_ = slotId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof outfox.ead.data.rta.RtaCache.AidBId) {
          return mergeFrom((outfox.ead.data.rta.RtaCache.AidBId)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(outfox.ead.data.rta.RtaCache.AidBId other) {
        if (other == outfox.ead.data.rta.RtaCache.AidBId.getDefaultInstance()) return this;
        if (other.hasAid()) {
          setAid(other.getAid());
        }
        if (other.hasSlotId()) {
          setSlotId(other.getSlotId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        outfox.ead.data.rta.RtaCache.AidBId parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (outfox.ead.data.rta.RtaCache.AidBId) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long aid_ ;
      /**
       * <code>required int64 aid = 1;</code>
       */
      public boolean hasAid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 aid = 1;</code>
       */
      public long getAid() {
        return aid_;
      }
      /**
       * <code>required int64 aid = 1;</code>
       */
      public Builder setAid(long value) {
        bitField0_ |= 0x00000001;
        aid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 aid = 1;</code>
       */
      public Builder clearAid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        aid_ = 0L;
        onChanged();
        return this;
      }

      private long slotId_ ;
      /**
       * <code>optional int64 slot_id = 2;</code>
       *
       * <pre>
       * rta请求下发的广告位id
       * </pre>
       */
      public boolean hasSlotId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 slot_id = 2;</code>
       *
       * <pre>
       * rta请求下发的广告位id
       * </pre>
       */
      public long getSlotId() {
        return slotId_;
      }
      /**
       * <code>optional int64 slot_id = 2;</code>
       *
       * <pre>
       * rta请求下发的广告位id
       * </pre>
       */
      public Builder setSlotId(long value) {
        bitField0_ |= 0x00000002;
        slotId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 slot_id = 2;</code>
       *
       * <pre>
       * rta请求下发的广告位id
       * </pre>
       */
      public Builder clearSlotId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        slotId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:outfox.ead.data.rta.AidBId)
    }

    static {
      defaultInstance = new AidBId(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:outfox.ead.data.rta.AidBId)
  }

  public interface ResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:outfox.ead.data.rta.Result)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string req_id = 1;</code>
     *
     * <pre>
     * rta请求下发的id
     * </pre>
     */
    boolean hasReqId();
    /**
     * <code>optional string req_id = 1;</code>
     *
     * <pre>
     * rta请求下发的id
     * </pre>
     */
    java.lang.String getReqId();
    /**
     * <code>optional string req_id = 1;</code>
     *
     * <pre>
     * rta请求下发的id
     * </pre>
     */
    com.google.protobuf.ByteString
        getReqIdBytes();

    /**
     * <code>required bool bid = 2;</code>
     *
     * <pre>
     * 是否参竞
     * </pre>
     */
    boolean hasBid();
    /**
     * <code>required bool bid = 2;</code>
     *
     * <pre>
     * 是否参竞
     * </pre>
     */
    boolean getBid();

    /**
     * <code>repeated string sponsor_ids = 3;</code>
     *
     * <pre>
     * 参竞的广告主id
     * </pre>
     */
    com.google.protobuf.ProtocolStringList
        getSponsorIdsList();
    /**
     * <code>repeated string sponsor_ids = 3;</code>
     *
     * <pre>
     * 参竞的广告主id
     * </pre>
     */
    int getSponsorIdsCount();
    /**
     * <code>repeated string sponsor_ids = 3;</code>
     *
     * <pre>
     * 参竞的广告主id
     * </pre>
     */
    java.lang.String getSponsorIds(int index);
    /**
     * <code>repeated string sponsor_ids = 3;</code>
     *
     * <pre>
     * 参竞的广告主id
     * </pre>
     */
    com.google.protobuf.ByteString
        getSponsorIdsBytes(int index);

    /**
     * <code>repeated string rta_ids = 4;</code>
     *
     * <pre>
     * 参竞的rta策略id
     * </pre>
     */
    com.google.protobuf.ProtocolStringList
        getRtaIdsList();
    /**
     * <code>repeated string rta_ids = 4;</code>
     *
     * <pre>
     * 参竞的rta策略id
     * </pre>
     */
    int getRtaIdsCount();
    /**
     * <code>repeated string rta_ids = 4;</code>
     *
     * <pre>
     * 参竞的rta策略id
     * </pre>
     */
    java.lang.String getRtaIds(int index);
    /**
     * <code>repeated string rta_ids = 4;</code>
     *
     * <pre>
     * 参竞的rta策略id
     * </pre>
     */
    com.google.protobuf.ByteString
        getRtaIdsBytes(int index);
  }
  /**
   * Protobuf type {@code outfox.ead.data.rta.Result}
   */
  public static final class Result extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:outfox.ead.data.rta.Result)
      ResultOrBuilder {
    // Use Result.newBuilder() to construct.
    private Result(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Result(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Result defaultInstance;
    public static Result getDefaultInstance() {
      return defaultInstance;
    }

    public Result getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Result(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              reqId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              bid_ = input.readBool();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                sponsorIds_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000004;
              }
              sponsorIds_.add(bs);
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                rtaIds_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000008;
              }
              rtaIds_.add(bs);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          sponsorIds_ = sponsorIds_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          rtaIds_ = rtaIds_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Result_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Result_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              outfox.ead.data.rta.RtaCache.Result.class, outfox.ead.data.rta.RtaCache.Result.Builder.class);
    }

    public static com.google.protobuf.Parser<Result> PARSER =
        new com.google.protobuf.AbstractParser<Result>() {
      public Result parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Result(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Result> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    public static final int REQ_ID_FIELD_NUMBER = 1;
    private java.lang.Object reqId_;
    /**
     * <code>optional string req_id = 1;</code>
     *
     * <pre>
     * rta请求下发的id
     * </pre>
     */
    public boolean hasReqId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional string req_id = 1;</code>
     *
     * <pre>
     * rta请求下发的id
     * </pre>
     */
    public java.lang.String getReqId() {
      java.lang.Object ref = reqId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reqId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string req_id = 1;</code>
     *
     * <pre>
     * rta请求下发的id
     * </pre>
     */
    public com.google.protobuf.ByteString
        getReqIdBytes() {
      java.lang.Object ref = reqId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reqId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BID_FIELD_NUMBER = 2;
    private boolean bid_;
    /**
     * <code>required bool bid = 2;</code>
     *
     * <pre>
     * 是否参竞
     * </pre>
     */
    public boolean hasBid() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required bool bid = 2;</code>
     *
     * <pre>
     * 是否参竞
     * </pre>
     */
    public boolean getBid() {
      return bid_;
    }

    public static final int SPONSOR_IDS_FIELD_NUMBER = 3;
    private com.google.protobuf.LazyStringList sponsorIds_;
    /**
     * <code>repeated string sponsor_ids = 3;</code>
     *
     * <pre>
     * 参竞的广告主id
     * </pre>
     */
    public com.google.protobuf.ProtocolStringList
        getSponsorIdsList() {
      return sponsorIds_;
    }
    /**
     * <code>repeated string sponsor_ids = 3;</code>
     *
     * <pre>
     * 参竞的广告主id
     * </pre>
     */
    public int getSponsorIdsCount() {
      return sponsorIds_.size();
    }
    /**
     * <code>repeated string sponsor_ids = 3;</code>
     *
     * <pre>
     * 参竞的广告主id
     * </pre>
     */
    public java.lang.String getSponsorIds(int index) {
      return sponsorIds_.get(index);
    }
    /**
     * <code>repeated string sponsor_ids = 3;</code>
     *
     * <pre>
     * 参竞的广告主id
     * </pre>
     */
    public com.google.protobuf.ByteString
        getSponsorIdsBytes(int index) {
      return sponsorIds_.getByteString(index);
    }

    public static final int RTA_IDS_FIELD_NUMBER = 4;
    private com.google.protobuf.LazyStringList rtaIds_;
    /**
     * <code>repeated string rta_ids = 4;</code>
     *
     * <pre>
     * 参竞的rta策略id
     * </pre>
     */
    public com.google.protobuf.ProtocolStringList
        getRtaIdsList() {
      return rtaIds_;
    }
    /**
     * <code>repeated string rta_ids = 4;</code>
     *
     * <pre>
     * 参竞的rta策略id
     * </pre>
     */
    public int getRtaIdsCount() {
      return rtaIds_.size();
    }
    /**
     * <code>repeated string rta_ids = 4;</code>
     *
     * <pre>
     * 参竞的rta策略id
     * </pre>
     */
    public java.lang.String getRtaIds(int index) {
      return rtaIds_.get(index);
    }
    /**
     * <code>repeated string rta_ids = 4;</code>
     *
     * <pre>
     * 参竞的rta策略id
     * </pre>
     */
    public com.google.protobuf.ByteString
        getRtaIdsBytes(int index) {
      return rtaIds_.getByteString(index);
    }

    private void initFields() {
      reqId_ = "";
      bid_ = false;
      sponsorIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      rtaIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasBid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getReqIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBool(2, bid_);
      }
      for (int i = 0; i < sponsorIds_.size(); i++) {
        output.writeBytes(3, sponsorIds_.getByteString(i));
      }
      for (int i = 0; i < rtaIds_.size(); i++) {
        output.writeBytes(4, rtaIds_.getByteString(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getReqIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, bid_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < sponsorIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(sponsorIds_.getByteString(i));
        }
        size += dataSize;
        size += 1 * getSponsorIdsList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rtaIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(rtaIds_.getByteString(i));
        }
        size += dataSize;
        size += 1 * getRtaIdsList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static outfox.ead.data.rta.RtaCache.Result parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static outfox.ead.data.rta.RtaCache.Result parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(outfox.ead.data.rta.RtaCache.Result prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code outfox.ead.data.rta.Result}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:outfox.ead.data.rta.Result)
        outfox.ead.data.rta.RtaCache.ResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Result_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Result_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                outfox.ead.data.rta.RtaCache.Result.class, outfox.ead.data.rta.RtaCache.Result.Builder.class);
      }

      // Construct using outfox.ead.data.rta.RtaCache.Result.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        reqId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        bid_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        sponsorIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        rtaIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return outfox.ead.data.rta.RtaCache.internal_static_outfox_ead_data_rta_Result_descriptor;
      }

      public outfox.ead.data.rta.RtaCache.Result getDefaultInstanceForType() {
        return outfox.ead.data.rta.RtaCache.Result.getDefaultInstance();
      }

      public outfox.ead.data.rta.RtaCache.Result build() {
        outfox.ead.data.rta.RtaCache.Result result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public outfox.ead.data.rta.RtaCache.Result buildPartial() {
        outfox.ead.data.rta.RtaCache.Result result = new outfox.ead.data.rta.RtaCache.Result(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.reqId_ = reqId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.bid_ = bid_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          sponsorIds_ = sponsorIds_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.sponsorIds_ = sponsorIds_;
        if (((bitField0_ & 0x00000008) == 0x00000008)) {
          rtaIds_ = rtaIds_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.rtaIds_ = rtaIds_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof outfox.ead.data.rta.RtaCache.Result) {
          return mergeFrom((outfox.ead.data.rta.RtaCache.Result)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(outfox.ead.data.rta.RtaCache.Result other) {
        if (other == outfox.ead.data.rta.RtaCache.Result.getDefaultInstance()) return this;
        if (other.hasReqId()) {
          bitField0_ |= 0x00000001;
          reqId_ = other.reqId_;
          onChanged();
        }
        if (other.hasBid()) {
          setBid(other.getBid());
        }
        if (!other.sponsorIds_.isEmpty()) {
          if (sponsorIds_.isEmpty()) {
            sponsorIds_ = other.sponsorIds_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureSponsorIdsIsMutable();
            sponsorIds_.addAll(other.sponsorIds_);
          }
          onChanged();
        }
        if (!other.rtaIds_.isEmpty()) {
          if (rtaIds_.isEmpty()) {
            rtaIds_ = other.rtaIds_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureRtaIdsIsMutable();
            rtaIds_.addAll(other.rtaIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasBid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        outfox.ead.data.rta.RtaCache.Result parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (outfox.ead.data.rta.RtaCache.Result) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object reqId_ = "";
      /**
       * <code>optional string req_id = 1;</code>
       *
       * <pre>
       * rta请求下发的id
       * </pre>
       */
      public boolean hasReqId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional string req_id = 1;</code>
       *
       * <pre>
       * rta请求下发的id
       * </pre>
       */
      public java.lang.String getReqId() {
        java.lang.Object ref = reqId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            reqId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string req_id = 1;</code>
       *
       * <pre>
       * rta请求下发的id
       * </pre>
       */
      public com.google.protobuf.ByteString
          getReqIdBytes() {
        java.lang.Object ref = reqId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          reqId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string req_id = 1;</code>
       *
       * <pre>
       * rta请求下发的id
       * </pre>
       */
      public Builder setReqId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        reqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string req_id = 1;</code>
       *
       * <pre>
       * rta请求下发的id
       * </pre>
       */
      public Builder clearReqId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        reqId_ = getDefaultInstance().getReqId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string req_id = 1;</code>
       *
       * <pre>
       * rta请求下发的id
       * </pre>
       */
      public Builder setReqIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        reqId_ = value;
        onChanged();
        return this;
      }

      private boolean bid_ ;
      /**
       * <code>required bool bid = 2;</code>
       *
       * <pre>
       * 是否参竞
       * </pre>
       */
      public boolean hasBid() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required bool bid = 2;</code>
       *
       * <pre>
       * 是否参竞
       * </pre>
       */
      public boolean getBid() {
        return bid_;
      }
      /**
       * <code>required bool bid = 2;</code>
       *
       * <pre>
       * 是否参竞
       * </pre>
       */
      public Builder setBid(boolean value) {
        bitField0_ |= 0x00000002;
        bid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool bid = 2;</code>
       *
       * <pre>
       * 是否参竞
       * </pre>
       */
      public Builder clearBid() {
        bitField0_ = (bitField0_ & ~0x00000002);
        bid_ = false;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList sponsorIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureSponsorIdsIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          sponsorIds_ = new com.google.protobuf.LazyStringArrayList(sponsorIds_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public com.google.protobuf.ProtocolStringList
          getSponsorIdsList() {
        return sponsorIds_.getUnmodifiableView();
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public int getSponsorIdsCount() {
        return sponsorIds_.size();
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public java.lang.String getSponsorIds(int index) {
        return sponsorIds_.get(index);
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public com.google.protobuf.ByteString
          getSponsorIdsBytes(int index) {
        return sponsorIds_.getByteString(index);
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public Builder setSponsorIds(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureSponsorIdsIsMutable();
        sponsorIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public Builder addSponsorIds(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureSponsorIdsIsMutable();
        sponsorIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public Builder addAllSponsorIds(
          java.lang.Iterable<java.lang.String> values) {
        ensureSponsorIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, sponsorIds_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public Builder clearSponsorIds() {
        sponsorIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string sponsor_ids = 3;</code>
       *
       * <pre>
       * 参竞的广告主id
       * </pre>
       */
      public Builder addSponsorIdsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureSponsorIdsIsMutable();
        sponsorIds_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList rtaIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureRtaIdsIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          rtaIds_ = new com.google.protobuf.LazyStringArrayList(rtaIds_);
          bitField0_ |= 0x00000008;
         }
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public com.google.protobuf.ProtocolStringList
          getRtaIdsList() {
        return rtaIds_.getUnmodifiableView();
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public int getRtaIdsCount() {
        return rtaIds_.size();
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public java.lang.String getRtaIds(int index) {
        return rtaIds_.get(index);
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public com.google.protobuf.ByteString
          getRtaIdsBytes(int index) {
        return rtaIds_.getByteString(index);
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public Builder setRtaIds(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureRtaIdsIsMutable();
        rtaIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public Builder addRtaIds(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureRtaIdsIsMutable();
        rtaIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public Builder addAllRtaIds(
          java.lang.Iterable<java.lang.String> values) {
        ensureRtaIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rtaIds_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public Builder clearRtaIds() {
        rtaIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string rta_ids = 4;</code>
       *
       * <pre>
       * 参竞的rta策略id
       * </pre>
       */
      public Builder addRtaIdsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureRtaIdsIsMutable();
        rtaIds_.add(value);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:outfox.ead.data.rta.Result)
    }

    static {
      defaultInstance = new Result(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:outfox.ead.data.rta.Result)
  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_outfox_ead_data_rta_Data_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_outfox_ead_data_rta_Data_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_outfox_ead_data_rta_DataV2_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_outfox_ead_data_rta_DataV2_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_outfox_ead_data_rta_CompressRtaRequest_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_outfox_ead_data_rta_CompressRtaRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_outfox_ead_data_rta_AidBId_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_outfox_ead_data_rta_AidBId_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_outfox_ead_data_rta_Result_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_outfox_ead_data_rta_Result_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016rtacache.proto\022\023outfox.ead.data.rta\"\201\001" +
      "\n\004Data\022\021\n\ttimestamp\030\001 \002(\003\022\017\n\007slot_id\030\002 \001" +
      "(\003\022\016\n\006req_id\030\003 \001(\t\022E\n\024compress_rta_reque" +
      "st\030\004 \001(\0132\'.outfox.ead.data.rta.CompressR" +
      "taRequest\"\237\001\n\006DataV2\022\021\n\ttimestamp\030\001 \002(\003\022" +
      "+\n\006aidBid\030\002 \003(\0132\033.outfox.ead.data.rta.Ai" +
      "dBId\022E\n\024compress_rta_request\030\003 \001(\0132\'.out" +
      "fox.ead.data.rta.CompressRtaRequest\022\016\n\006s" +
      "lotId\030\004 \001(\003\"\330\001\n\022CompressRtaRequest\022\014\n\004im" +
      "ei\030\001 \001(\005\022\017\n\007imeiMd5\030\002 \001(\005\022\014\n\004oaid\030\004 \001(\005\022",
      "\017\n\007oaidMd5\030\005 \001(\005\022\014\n\004idfa\030\006 \001(\005\022\017\n\007idfaMd" +
      "5\030\007 \001(\005\022\026\n\016caidAndVersion\030\010 \003(\005\022\031\n\021caidM" +
      "d5AndVersion\030\t \003(\005\022\n\n\002ip\030\013 \001(\005\022\n\n\002ua\030\014 \001" +
      "(\005\022\r\n\005model\030\r \001(\005\022\013\n\003osv\030\016 \001(\005\"&\n\006AidBId" +
      "\022\013\n\003aid\030\001 \002(\003\022\017\n\007slot_id\030\002 \001(\003\"K\n\006Result" +
      "\022\016\n\006req_id\030\001 \001(\t\022\013\n\003bid\030\002 \002(\010\022\023\n\013sponsor" +
      "_ids\030\003 \003(\t\022\017\n\007rta_ids\030\004 \003(\tB\nB\010RtaCache"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_outfox_ead_data_rta_Data_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_outfox_ead_data_rta_Data_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_outfox_ead_data_rta_Data_descriptor,
        new java.lang.String[] { "Timestamp", "SlotId", "ReqId", "CompressRtaRequest", });
    internal_static_outfox_ead_data_rta_DataV2_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_outfox_ead_data_rta_DataV2_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_outfox_ead_data_rta_DataV2_descriptor,
        new java.lang.String[] { "Timestamp", "AidBid", "CompressRtaRequest", "SlotId", });
    internal_static_outfox_ead_data_rta_CompressRtaRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_outfox_ead_data_rta_CompressRtaRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_outfox_ead_data_rta_CompressRtaRequest_descriptor,
        new java.lang.String[] { "Imei", "ImeiMd5", "Oaid", "OaidMd5", "Idfa", "IdfaMd5", "CaidAndVersion", "CaidMd5AndVersion", "Ip", "Ua", "Model", "Osv", });
    internal_static_outfox_ead_data_rta_AidBId_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_outfox_ead_data_rta_AidBId_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_outfox_ead_data_rta_AidBId_descriptor,
        new java.lang.String[] { "Aid", "SlotId", });
    internal_static_outfox_ead_data_rta_Result_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_outfox_ead_data_rta_Result_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_outfox_ead_data_rta_Result_descriptor,
        new java.lang.String[] { "ReqId", "Bid", "SponsorIds", "RtaIds", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
