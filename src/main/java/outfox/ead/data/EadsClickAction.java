/**
 * @(#)EadsClickAction.java, 2010-7-6. 
 *
 * Copyright 2010 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data;

import java.io.ByteArrayInputStream;
import java.io.DataInput;
import java.io.DataInputStream;
import java.io.DataOutput;
import java.io.IOException;
import java.io.Serializable;

import odis.serialize.IWritable;
import odis.serialize.lib.UTF8Writable;

/**
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class EadsClickAction extends ClickAction implements IWritable, Serializable {
    protected String vendor;
    protected String keyFrom;
    protected String productName;
    protected String filterReason; // real time cheating filtered reason
    
    public EadsClickAction() {
    }

    public EadsClickAction(AdItem ad) {
        super(ad);
        vendor = ad.getTextMap().get("vendor");
        keyFrom = ad.getTextMap().get("keyFrom");
        productName = ad.getTextMap().get("productName");
        filterReason = ad.getTextMap().get("filterReason");
    }

    public EadsClickAction(long id) {
        super(id);
    }

    public String getVendor() {
        return vendor;
    }

    public String getKeyFrom() {
        return keyFrom;
    }

    public String getProductName() {
        return productName;
    }
    
    public String getFilterReason() {
        return filterReason;
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        super.readFields(in);
        vendor = UTF8Writable.readStringNull(in);
        keyFrom = UTF8Writable.readStringNull(in);
        productName = UTF8Writable.readStringNull(in);
        filterReason = UTF8Writable.readStringNull(in);
    }

    @Override
    public void writeFields(DataOutput out) throws IOException {
        super.writeFields(out);
        UTF8Writable.writeStringNull(out, vendor);
        UTF8Writable.writeStringNull(out, keyFrom);
        UTF8Writable.writeStringNull(out, productName);
        UTF8Writable.writeStringNull(out, filterReason);
    }

    /**
     * 工厂类方法
     * <p>
     * 从二进制的字节数组中创建对象
     *
     * @param bytes
     * @return
     * @throws IOException
     */
    public static EadsClickAction deserialize(byte[] bytes) throws IOException {
        DataInputStream in = new DataInputStream(
                new ByteArrayInputStream(bytes));
        EadsClickAction click = new EadsClickAction();
        click.readFields(in);
        return click;
    }

    @Override
    public String toString() {
        return "EadsClickAction{" +
                "vendor='" + vendor + '\'' +
                ", keyFrom='" + keyFrom + '\'' +
                ", productName='" + productName + '\'' +
                ", filterReason='" + filterReason + '\'' +
                "} " + super.toString();
    }
}