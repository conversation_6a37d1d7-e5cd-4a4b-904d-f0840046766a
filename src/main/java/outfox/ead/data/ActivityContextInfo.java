package outfox.ead.data;

import lombok.ToString;
import odis.serialize.IWritable;
import outfox.ead.data.protobuf.DataHolder;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/29
 */
@ToString
public class ActivityContextInfo implements IWritable {

    DataHolder.ActivityContext instance;

    Map<Integer, DataHolder.GeoDispatchMap> geoDispatchMap = new HashMap<>();

    public ActivityContextInfo() {
    }

    public ActivityContextInfo(DataHolder.ActivityContext instance) {
        this.instance = instance;
    }

    public DataHolder.ActivityContext getInstance() {
        return instance;
    }

    @Override
    public void writeFields(DataOutput out) throws IOException {
        byte[] bytes = instance.toByteArray();
        out.writeInt(bytes.length);
        out.write(bytes);
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        int byteLength = in.readInt();
        byte[] bytes = new byte[byteLength];
        in.readFully(bytes);
        instance = DataHolder.ActivityContext.parseFrom(bytes);
        List<DataHolder.GeoDispatchMap> geoDispatchMapList = instance.getGeoDispatchMapList();
        for (DataHolder.GeoDispatchMap dispatchMap : geoDispatchMapList) {
            geoDispatchMap.put(dispatchMap.getCityId(), dispatchMap);
        }
    }

    @Override
    public IWritable copyFields(IWritable iWritable) {
        throw new UnsupportedOperationException();
    }

    public Long getActivityId() {
        return instance.getActivityId();
    }

    public Long getSponsorId() {
        return instance.getSponsorId();
    }

    public Long getCampaignId() {
        return instance.getCampaignId();
    }

    public Long getGroupId() {
        return instance.getGroupId();
    }

    public Long getContentId() {
        return instance.getAdContentId();
    }

    public String getConvExt() {
        return instance.getConvExt();
    }

    public String getDestLink() {
        return instance.getDestLink();
    }

    public Boolean getDeleted() {
        return instance.getDeleted();
    }

    public Integer getOperateStatus() {
        return instance.getOperateStatus();
    }

    public Integer getPromotionType() {
        return instance.getPromotionType();
    }

    public Integer getTdpPlatformId() {
        return instance.getTdpPlatformId();
    }

    public String getTdpCreativeIds() {
        return instance.getTdpCreativeIds();
    }

    public String getTdpSponsorIds() {
        return instance.getTdpSponsorIds();
    }

    public Integer getActivityType() {
        return instance.getActivityType();
    }

    public List<Long> getNonRtaContentIds() {
        return instance.getNonRtaContentIdsList();
    }

    public Long getRtaContentId() {
        return instance.getRtaContentId();
    }

    public String getConvertTrackingUid() {
        return instance.getConvertTrackingUID();
    }

    public String getConvertTrackingAction() {
        return instance.getConvertTrackingAction();
    }

    public String getSecretKey() {
        return instance.getSecretKey();
    }

    public String getClientId() {
        return instance.getClientId();
    }

    public String getPackageName() {
        return instance.getPackageName();
    }

    public String getTdpConvertId() {
        return instance.getTdpConvertId();
    }

    public String getSponsorCategoryOrder() {
        return instance.getSponsorCategoryOrder();
    }

    public Integer getStrategy() {
        return instance.getStrategy();
    }

    public Boolean getFilterNoBidConv() {
        return instance.getFilterNoBidConv();
    }

    public List<Integer> getDestProvinces() {
        return instance.getDestProvincesList();
    }

    public List<Integer> getDestCities() {
        return instance.getDestCitiesList();
    }

    public List<Integer> getDestOverseas() {
        return instance.getDestOverseasList();
    }

    public Boolean getEnableGeoTargeting() {
        return instance.getEnableGeoTargeting();
    }

    public long getDailyClickLimit() {
        return instance.getDailyClickLimit();
    }

    public Boolean getEnableGeoDispatch() {
        return instance.getEnableGeoDispatch();
    }

    public Map<Integer, DataHolder.GeoDispatchMap> getGeoDispatchMap() {
        return geoDispatchMap;
    }

}
