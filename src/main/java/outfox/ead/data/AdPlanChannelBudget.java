/**
 * @(#)AdPlanChannelBudget.java, 2008-12-2. Copyright 2008 Yodao, Inc. All rights
 *                        reserved. YODAO PROPRIETARY/CONFIDENTIAL. Use is
 *                        subject to license terms.
 */
package outfox.ead.data;

import java.io.Externalizable;
import java.io.IOException;
import java.io.ObjectInput;
import java.io.ObjectOutput;

import outfox.ead.db.PersistentObject;

/**
 * 广告投放计划在不同平台的预算。初步开放给客服帮客户设置。
 *
 * <AUTHOR>
 */
public class AdPlanChannelBudget extends PersistentObject
    implements Externalizable {
    private long adDeliveryPlanId;

    private int deliverySite;

    private int virtualBudget;

    public AdPlanChannelBudget() {
        // do nothing
    }

    public AdPlanChannelBudget(long id) {
        this.id = id;
    }

    public long getAdDeliveryPlanId() {
        return adDeliveryPlanId;
    }

    public void setAdDeliveryPlanId(long adDeliveryPlanId) {
        this.adDeliveryPlanId = adDeliveryPlanId;
    }

    public int getDeliverySite() {
        return deliverySite;
    }

    public void setDeliverySite(int deliverySite) {
        this.deliverySite = deliverySite;
    }

    public int getVirtualBudget() {
        return virtualBudget;
    }

    public void setVirtualBudget(int virtualBudget) {
        this.virtualBudget = virtualBudget;
    }

    public String toString() {
        return this.getClass().getSimpleName() + "[id=" + id
            + ", adDeliveryPlanId=" + adDeliveryPlanId + ", deliverySite="
            + deliverySite + ", virtualBudget=" + virtualBudget
            + "]";
    }

    public void readExternal(ObjectInput in) throws IOException,
        ClassNotFoundException {
        this.id = in.readLong();
        this.adDeliveryPlanId = in.readLong();
        this.deliverySite = in.readInt();
        this.virtualBudget = in.readInt();
    }

    public void writeExternal(ObjectOutput out) throws IOException {
        out.writeLong(this.id);
        out.writeLong(this.adDeliveryPlanId);
        out.writeInt(this.deliverySite);
        out.writeInt(this.virtualBudget);
    }
}