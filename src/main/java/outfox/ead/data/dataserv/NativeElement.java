/**
 * @(#)NativeElement.java, 2015年7月13日. 
 * 
 * Copyright 2015 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data.dataserv;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

import lombok.ToString;
import odis.serialize.IWritable;
import odis.serialize.lib.StringWritable;

/**
 *
 * <AUTHOR>
 *
 */
@ToString
public class NativeElement implements IWritable {
    private long id;
    private String name;
    private String key;
    private int length;
    private int height;
    private int width;
    private int type;
    private boolean isStandard;
    private int status;
    
    /**
     * 用于将Youdao NativeElement映射到Google openRTB 中的 Native.DataAssetType
     * value > 0
     */
    private int dataAssetTypeId;
    
    @Override
    public IWritable copyFields(IWritable arg0) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        this.id = in.readLong();
        this.name = StringWritable.readStringNull(in);
        this.key = StringWritable.readStringNull(in);
        this.length = in.readInt();
        this.height = in.readInt();
        this.width = in.readInt();
        this.type = in.readInt();
        this.isStandard = in.readBoolean();
        this.status = in.readInt();
        this.dataAssetTypeId = in.readInt();
    }

    @Override
    public void writeFields(DataOutput out) throws IOException {
        out.writeLong(id);
        StringWritable.writeStringNull(out, name);
        StringWritable.writeStringNull(out, key);
        out.writeInt(length);
        out.writeInt(height);
        out.writeInt(width);
        out.writeInt(type);
        out.writeBoolean(isStandard);
        out.writeInt(status);
        out.writeInt(dataAssetTypeId);
    }

    /**
     * @return the id
     */
    public long getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(long id) {
        this.id = id;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return the key
     */
    public String getKey() {
        return key;
    }

    /**
     * @param key the key to set
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * @return the length
     */
    public int getLength() {
        return length;
    }

    /**
     * @param length the length to set
     */
    public void setLength(int length) {
        this.length = length;
    }

    /**
     * @return the height
     */
    public int getHeight() {
        return height;
    }

    /**
     * @param height the height to set
     */
    public void setHeight(int height) {
        this.height = height;
    }

    /**
     * @return the width
     */
    public int getWidth() {
        return width;
    }

    /**
     * @param width the width to set
     */
    public void setWidth(int width) {
        this.width = width;
    }

    /**
     * @return the type
     */
    public int getType() {
        return type;
    }

    /**
     * @param type the type to set
     */
    public void setType(int type) {
        this.type = type;
    }

    /**
     * @return the isStandard
     */
    public boolean isStandard() {
        return isStandard;
    }

    /**
     * @param isStandard the isStandard to set
     */
    public void setStandard(boolean isStandard) {
        this.isStandard = isStandard;
    }

    /**
     * @return the status
     */
    public int getStatus() {
        return status;
    }

    /**
     * @param status the status to set
     */
    public void setStatus(int status) {
        this.status = status;
    }

    public int getDataAssetTypeId() {
        return dataAssetTypeId;
    }

    public void setDataAssetTypeId(int dataAssetTypeId) {
        this.dataAssetTypeId = dataAssetTypeId;
    }

}
