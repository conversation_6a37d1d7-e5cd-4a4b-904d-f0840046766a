/**
 * @(#)DataServSpecificHashMap.java, Jun 26, 2010.
 *
 * Copyright 2010 Youdao, Inc. All rights reserved.
 * YOUDAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data.dataserv;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.util.HashMap;

import odis.serialize.IWritable;
/**
 * 为数据服务层定制的用于传输的HashMap。客户端用它来存储需要更新的keys，服务器端则用它来将keys对应的blocks传输给客户端。
 * 
 * @param <K> 键值类型，为了便于传输，K必须能转化为等价的字符串，即能用toString()方法得到等价的表示,
 * 还必须拥有参数类型为String的构造方法。
 * 
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class DataServSpecificHashMap<K, V extends IWritable> extends HashMap<K, V> 
        implements IWritable {
    private static final org.apache.commons.logging.Log LOG =
        org.apache.commons.logging.LogFactory.getLog(DataServSpecificHashMap.class);
    
    private String domain;

    private long dataTime;
    
    private final Class<K> KEY_CLASS;

    private final Class<V> VALUE_CLASS;

    public DataServSpecificHashMap(Class<K> keyClass, Class<V> valueClass, String domain, long dataTime) {
        this.KEY_CLASS = keyClass;
        this.VALUE_CLASS = valueClass;
        this.domain = domain;
        this.dataTime = dataTime;
    }
    
    /**
     * 需要在生成对象后立即調用读入数据方法（readFields）。若生成对象后立即調用写出对象，则domain和dataTime是没有意义的。
     */
    public DataServSpecificHashMap(Class<K> keyClass, Class<V> valueClass) { 
        this(keyClass, valueClass, "domainNotSet", 0);
    }
    
    public synchronized void writeFields(DataOutput out) throws IOException {
        out.writeUTF(domain);
        out.writeLong(dataTime);
        out.writeInt(this.size());
        for (K key: keySet()) {
            out.writeUTF(key.toString());
            get(key).writeFields(out);
        }
    }

    public synchronized void readFields(DataInput in) throws IOException {
        domain = in.readUTF();
        dataTime = in.readLong();
        int size = in.readInt();
        for (int i = 0; i < size; i++) {            
            String key = in.readUTF();
            K k = newKey(key);  
            V v = newValue();
            v.readFields(in);
            
            put(k, v);
        }
    }

    @SuppressWarnings("unchecked")
    public synchronized IWritable copyFields(IWritable value) {
        clear();
        DataServSpecificHashMap<K, V> other = (DataServSpecificHashMap<K, V>) value;
        for (K key: other.keySet()) {
            if (key != null) {
                V v = other.get(key);
                if (v != null) {
                    put(key, v);
                }
            }
        }
        return this;
    }
    
    protected K newKey(String k) {
        K key;
        try {
            Constructor<K> con = KEY_CLASS.getConstructor(String.class);
            key = con.newInstance(k);
        } catch (Exception e) {
            LOG.error("Error happend when instantiating a new key of " +
                    domain + " of class " + KEY_CLASS);
            key = null;
        }
        return key;
    }
    
    protected V newValue() {
        V value;
        try {
            value = VALUE_CLASS.newInstance();
        } catch (Exception e) {
            LOG.error("Error happend when instantiating a new value of " +
                    domain + " of class " + VALUE_CLASS);
            value = null;
        }
        return value;
    }

    public String getDomain() {
        return domain;
    }

    public long getDataTime() {
        return dataTime;
    }

}
