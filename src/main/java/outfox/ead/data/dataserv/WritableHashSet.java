/**
 * @(#)WritableHashSet.java, Apr 17, 2010.
 *
 * Copyright 2010 Youdao, Inc. All rights reserved.
 * YOUDAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data.dataserv;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.util.HashSet;
import java.util.Iterator;

import odis.serialize.IWritable;

/**
 *
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public abstract class WritableHashSet<E extends IWritable> extends HashSet<E> implements IWritable{

    protected abstract E newElementInstance();

    public void readFields(DataInput in) throws IOException {
        clear();

        final int size = in.readInt();
        for(int i=0; i<size; i++) {
            E obj = newElementInstance();
            obj.readFields(in);
            add(obj);
        }
    }

    public void writeFields(DataOutput out) throws IOException {
        out.writeInt(size());
        Iterator<E> itr = iterator();
        while (itr.hasNext()) {
            itr.next().writeFields(out);
        }
    }

    @SuppressWarnings("unchecked")
    public IWritable copyFields(IWritable value) {
        clear();

        WritableHashSet<E> other = (WritableHashSet<E>) value;
        Iterator<E> itr = other.iterator();
        while (itr.hasNext()) {
            add(itr.next());
        }

        return this;
    }

}
