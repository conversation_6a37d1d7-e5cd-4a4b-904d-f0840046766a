/**
 * @(#)AdVariationDaoImplBIZ.java, Jun 7, 2010.
 *
 * Copyright 2010 Youdao, Inc. All rights reserved.
 * YOUDAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import outfox.ead.data.AdType;

/**
 * 广告变体的DAO
 *
 * <AUTHOR>
 * 
 * 从业务库中读取数据。
 * 
 * <AUTHOR> 
 */
public class AdVariationDaoImplBIZ extends AdGroupAppendantDaoBIZ<AdVariation>
        implements AdVariationDao {

    private static final Log logger = LogFactory
            .getLog(AdVariationDaoImplBIZ.class);

    public AdVariationDaoImplBIZ() {
        super("AdContent", "AD_CONTENT_ID");
    }

    //@Override
    public AdVariation translate(ResultSet resultSet) throws SQLException {
        long id = resultSet.getLong(1);
        long adId = resultSet.getLong(16);
        
        AdType adType = AdType.getAdType(resultSet.getInt(17));
        if(adType == AdType.TEXT) {
            //codes below use AdVariationDaoImplBIZ.translate(ResultSet) for reference
        try {
            String title = resultSet.getString(2).replace('\t', ' ');
            String line1 = resultSet.getString(3).replace('\t', ' ');
            String line2 = resultSet.getString(4).replace('\t', ' ');
            String url = resultSet.getString(6).replace('\t', ' ');
            String link = resultSet.getString(5).replace('\t', ' ');
                AdVariation adv = new AdVariation(id, adId, title, line1, line2, url, link);
            adv.setStatus(resultSet.getInt(7));
            return adv;
        } catch (NullPointerException e) {
                logger.error("AdContent " + id + " will be filtered as it has null column.");
            return null;
        }
        } else if(adType == AdType.IMAGE) {
            String title = resultSet.getString(2);
            String link = resultSet.getString(5);
            String mimeType = resultSet.getString(19);
            String mimeSrc = resultSet.getString(20);
            int mimeWidth = resultSet.getInt(21);
            int mimeHeight = resultSet.getInt(22);
            AdVariation adv = new AdVariation(id, adId, title, link, adType,
                    mimeType, mimeSrc, mimeWidth, mimeHeight);
            adv.setStatus(resultSet.getInt(7));
            return adv;
    }
        
        return null;
    }

    //@Override
    protected boolean isConsistent(AdVariation original, ResultSet resultSet)
            throws SQLException {
        if (!super.isConsistent(original, resultSet)) {
            return false;
        }
        long adId = resultSet.getLong(16);
        if (adId != original.getAdGroupId()) {
            logger.error("can NOT use AdVariation(id=" + adId + ") to update "
                    + original.toString());
            return false;
        }
        return true;
    }

    //@Override
    protected boolean translateInto(AdVariation adv, ResultSet resultSet)
            throws SQLException {
        AdType adType = AdType.getAdType(resultSet.getInt(17));
        if(adType == AdType.TEXT) {
            //codes below use AdVariationDaoImplBIZ.translate(ResultSet) for reference
        try {
            String title = resultSet.getString(2).replace('\t', ' ');
            String line1 = resultSet.getString(3).replace('\t', ' ');
            String line2 = resultSet.getString(4).replace('\t', ' ');
            String url = resultSet.getString(6).replace('\t', ' ');
            String link = resultSet.getString(5).replace('\t', ' ');
            adv.setText(title, line1, line2, url, link);
            adv.setStatus(resultSet.getInt(7));
            return true;
        } catch (NullPointerException e) {
                logger.error("AdContent " + adv.getId() + " will be filtered as it has null column.");
            return false;
        }
        } else if(adType == AdType.IMAGE) {
            String title = resultSet.getString(2);
            String link = resultSet.getString(5);
            String mimeType = resultSet.getString(19);
            String mimeSrc = resultSet.getString(20);
            int mimeWidth = resultSet.getInt(21);
            int mimeHeight = resultSet.getInt(22);
            adv.setMimeProperties(title, link, adType, mimeType, mimeSrc, mimeWidth, mimeHeight);
            adv.setStatus(resultSet.getInt(7));
            return true;
    }

        return false;
    }

    public void insert(AdVariation adv, Connection conn) throws SQLException {
        throw new NyiAssertionError("MODIFICATION TO BIZ-DB NOT SUPPORTED!");
    }

    public void updateToDb(AdVariation adv, Connection conn)
            throws SQLException {
        throw new NyiAssertionError("MODIFICATION TO BIZ-DB NOT SUPPORTED!");
    }

    public AdVariation[] get(String title) throws SQLException {
        Connection conn = this.getConnectionByKey(null);
        try {
            return get(title, conn);
        } finally {
            conn.close();
        }
    }

    public List<AdVariation> getAllAvailables() throws SQLException {
        Connection conn = this.getConnectionByKey(null);
        PreparedStatement pst = null;
        ResultSet rst = null;
        
        List<Long> temp = new ArrayList<Long>();
        boolean noException = false;
        try {
            String sql = "select AD_GROUP_ID from AdGroup where status=0";
            pst = conn.prepareStatement(sql);
            rst = pst.executeQuery();
            while (rst.next()) {
                temp.add(rst.getLong(1));
            }
            noException = true;
        } finally {
            closeResultSet(rst);
            closePreparedStatement(pst);
            if(!noException && conn!=null)
                conn.close();
        }
        
        List<AdVariation> list = new ArrayList<AdVariation>();
        try {
            for(long groupID : temp) {
                String sql = "SELECT * FROM " + tableName + " where AD_GROUP_ID = ? and status=0";
                pst = conn.prepareStatement(sql);
                pst.setLong(1, groupID);
                rst = pst.executeQuery();
                while (rst.next()) {
                    AdVariation object = translate(rst);
                    if (object != null) {
                        list.add(object);
                    }
                }
            }
        } finally {
            closeResultSet(rst);
            closePreparedStatement(pst);
            if (conn != null)
                conn.close();
        }
        
        return list;
    }

    protected AdVariation[] get(String title, Connection conn)
            throws SQLException {
        PreparedStatement pst = null;
        ResultSet rst = null;
        List<AdVariation> list = new ArrayList<AdVariation>();
        try {
            pst = conn.prepareStatement("SELECT * FROM " + this.tableName
                    + " WHERE TITLE LIKE '%" + title + "%'");
            rst = pst.executeQuery();
            while (rst.next()) {
                AdVariation adv = translate(rst);
                if (adv != null) list.add(adv);
            }
        } finally {
            this.closePreparedStatement(pst);
            this.closeResultSet(rst);
        }
        AdVariation[] advs = new AdVariation[list.size()];
        list.toArray(advs);
        return advs;
    }

    /*
     * (non-Javadoc)
     *
     * @see outfox.ead.data.AdVariationDao#updateStatus(long, long, int)
     */

    public void updateStatus(long adGroupId, long adVariationId, int status)
            throws SQLException {
        throw new NyiAssertionError("MODIFICATION TO BIZ-DB NOT SUPPORTED!");
    }
    
    @Override
    public List<AdVariation> load() throws SQLException {
        Connection conn = getConnection();
        List<AdVariation> variationsList = new ArrayList<AdVariation>();
        try{
            load(conn, variationsList);
        } finally {
            if (conn != null) conn.close();
        }
        return variationsList;
    }

    private void load(Connection conn, List<AdVariation> variationsList) throws SQLException {
        PreparedStatement pst = null;
        ResultSet rst = null;
        long start = System.currentTimeMillis();
        
        //load available adGroup IDs from db
        Set<Long> groupIdSet = new HashSet<Long>();        
        try {
            String sql = "SELECT AD_GROUP_ID FROM AdGroup WHERE STATUS = 0";
            pst = conn.prepareStatement(sql);
            rst = pst.executeQuery();
            while(rst.next()) {
                groupIdSet.add(rst.getLong(1));
            }
        } finally {
            closeResultSet(rst);
            closePreparedStatement(pst);
        }
        
        //load valid adVariations which also have valid adGroups to be attached to 
        try {
            //codes below use AdGroupAppendantDaoBIZ.loadInto(list, conn) for reference
            pst = conn.prepareStatement("SELECT max(AD_CONTENT_ID) FROM AdContent");
            rst = pst.executeQuery();
            rst.next();
            int n = rst.getInt(1);
            int num = 5000, p = (n / num) + 1;
            pst = conn.prepareStatement("SELECT AD_CONTENT_ID, TITLE, DESC1, DESC2, " +
                    "DEST_LINK, IMPRESSION_LINK, AD_GROUP_ID, " +
                    "TYPE, MIME_TYPE, MIME_SRC, MIME_WIDTH, MIME_HEIGHT " + //columns for image-ad
                    "FROM AdContent WHERE AD_CONTENT_ID>=? and AD_CONTENT_ID<? and STATUS=0"); 
            for (int i = 0; i < p; i++) {
                pst.setInt(1, i * num);
                pst.setInt(2, (i + 1) * num);
                rst = pst.executeQuery();
                while (rst.next()) {
                    long adId = rst.getLong(7);
                    if(!groupIdSet.contains(adId)) continue;//不载入没有对应广告组的广告变体
                    
                    //codes below use AdVariationDaoImplBIZ.translate(ResultSet) for reference                    
                    AdType adType = AdType.getAdType(rst.getInt(8));
                    if(adType == AdType.TEXT) {
                        //codes below use AdVariationDaoImplBIZ.translate(ResultSet) for reference                    
                    long id = rst.getLong(1);
                    try {
                        String title = rst.getString(2).replace('\t', ' ');
                        String line1 = rst.getString(3).replace('\t', ' ');
                        String line2 = rst.getString(4).replace('\t', ' ');
                        String link = rst.getString(5).replace('\t', ' ');
                        String url = rst.getString(6).replace('\t', ' ');
                        AdVariation adv = new AdVariation(id, adId, title, line1, line2, url, link);
                        adv.setStatus(0);

                        variationsList.add(adv);
                    } catch (NullPointerException e) {
                            logger.error("AdContent " + id + " will be filtered as it has null column.");
                    }
                    } else if(adType == AdType.IMAGE) {
                        long id = rst.getLong(1);
                        String title = rst.getString(2);
                        String link = rst.getString(5);
                        String mimeType = rst.getString(9);
                        String mimeSrc = rst.getString(10);
                        int mimeWidth = rst.getInt(11);
                        int mimeHeight = rst.getInt(12);
                        AdVariation adv = new AdVariation(id, adId, title, link, adType,
                                mimeType, mimeSrc, mimeWidth, mimeHeight);
                        adv.setStatus(0);

                        variationsList.add(adv);
                }

            }
            }
        } finally {
            closeResultSet(rst);
            closePreparedStatement(pst);
            long elapse = System.currentTimeMillis() - start;
            logger.info(variationsList.size() + " variations were loaded, elapsed " + elapse + "ms");
        }
    }
}
