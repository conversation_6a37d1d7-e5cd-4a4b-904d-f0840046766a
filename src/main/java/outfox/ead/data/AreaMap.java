/**
 * @(#)B.java, 2007-5-18. Copyright 2007 Yodao, Inc. All rights reserved. YOUDAO
 *             PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data;

/**
 * 地区列表
 * 
 * <AUTHOR>
 */
public class AreaMap {

    // area id
    private int id;

    // name represented in publish sytem
    private String publishName;

    // name in loc segs
    private String locSegName;

    @Override
    public String toString() {
        String ret = "[Id = " + id + "; PublishName = " + publishName
                + "; LocSegName = " + locSegName + "]";
        return ret;
    }

    // ////////////////////////////////////////
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPublishName() {
        return publishName;
    }

    public void setPublishName(String name) {
        this.publishName = name;
    }

    public String getLocSegName() {
        return locSegName;
    }

    public void setLocSegName(String locSegName) {
        this.locSegName = locSegName;
    }

}
