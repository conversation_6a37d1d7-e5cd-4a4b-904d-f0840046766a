/**
 * @(#)AdType.java, 2008-12-2. Copyright 2008 Yodao, Inc. All rights
 *                        reserved. YODAO PROPRIETARY/CONFIDENTIAL. Use is
 *                        subject to license terms.
 */
package outfox.ead.data;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * 广告类型
 * <AUTHOR>
 * 
 * add NOT_KNOWN type and re-implement getAdType(value) method
 * <AUTHOR>
 * @deprecated used outfox.ead.data.protobuf.DataHolder.AdType instead since 4.0.0
 */
public enum AdType {
    TEXT, IMAGE, IMAGE_TEXT, DYNAMIC, FORM, NOT_KNOWN;
    
    private static final Log LOG = LogFactory.getLog(AdType.class);
    
    public static AdType getAdType(int value) {
        if(value < 0 || value >= values().length) {
            if(LOG.isDebugEnabled()) LOG.debug("invalid ad type value:" + value);
            return NOT_KNOWN;
        }
        
        return values()[value];
    }
    
    public static AdType valueOfStr(String str){
        try {
            return valueOf(str);
        } catch (Exception e) {
            return NOT_KNOWN;
        }
    }
}