package outfox.ead.data.brand;

import lombok.*;

/**
 * 品牌广告权重的结构类
 *
 * <AUTHOR>
 * <AUTHOR> copy from eadd
 * @date 2021/8/24.
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class BrandAdWeight {


    /**
     * 自身权重
     */
    int weight;

    /**
     * 总权重
     */
    int totalWeight;

    /**
     * 实时获得权重比例
     */
    public double getRatio() {
        if (totalWeight == 0) {
            return 0;
        }
        return (double) weight / (double) totalWeight;
    }

    /**
     * 实时获得除去测试权重的实际权重比例
     */
    public double getRatioExcludeTest(int testWeight) {
        if (totalWeight == 0) {
            return 0;
        }
        return ((double) (weight - testWeight)) / (double) totalWeight;
    }

    public void plus(int weight) {
        this.weight += weight;
    }

    /**
     * toString
     */
    @Override
    public String toString() {
        return "AdWeight [weight=" + weight + ", totalWeight=" + totalWeight + "]";
    }

}
