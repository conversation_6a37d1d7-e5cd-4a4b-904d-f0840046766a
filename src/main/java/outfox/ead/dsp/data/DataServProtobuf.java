package outfox.ead.dsp.data;

import com.google.protobuf.Message;
import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.lang.reflect.Method;
import lombok.Getter;
import odis.serialize.IWritable;
import org.apache.commons.lang.SerializationUtils;

/**
 * 实现了 IWritable 的 Protobuf 工具类。
 *
 * 由于数据服务层不直接支持 Protobuf 类，只支持实现了 IWritable 接口的类，因此，在传输 protobuf 类的实例时， 需要用此类包装一下。
 *
 * @param <T> ProtoBuf 生成的类
 */
public class DataServProtobuf<T extends Message> implements IWritable {

    /**
     * 获取在数据服务层中传输的 protobuf 对象
     */
    @Getter
    private T protoObj;

    private Class<? extends Message> protoClass;

    /**
     * 数据服务层要求传输对象必须有无参构造器
     */
    public DataServProtobuf() {
    }

    /**
     * @param protoObj 需要在数据服务层传输的 protobuf 对象
     */
    public DataServProtobuf(T protoObj) {
        this.protoObj = protoObj;
        this.protoClass = protoObj.getClass();
    }

    @Override
    public void writeFields(DataOutput out) throws IOException {
        byte[] classBytes = SerializationUtils.serialize(protoClass);
        out.writeInt(classBytes.length);
        out.write(classBytes);

        byte[] objBytes = protoObj.toByteArray();
        out.writeInt(objBytes.length);
        out.write(objBytes);

    }

    @Override
    public void readFields(DataInput in) throws IOException {
        int classBytesLen = in.readInt();
        byte[] classBytes = new byte[classBytesLen];
        in.readFully(classBytes);

        int objBytesLen = in.readInt();
        byte[] objBytes = new byte[objBytesLen];
        in.readFully(objBytes);

        try {
            protoClass = (Class<? extends Message>) SerializationUtils.deserialize(classBytes);
            Method parseFromMethod = protoClass.getMethod("parseFrom", byte[].class);
            protoObj = (T) parseFromMethod.invoke(protoClass, objBytes);
        } catch (Exception e) {
            throw new IOException("deserialize protobuf object error", e);
        }
    }

    @Override
    public IWritable copyFields(IWritable value) {
        throw new UnsupportedOperationException();
    }
}
