package outfox.ead.dsp.data;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import odis.serialize.IWritable;
import outfox.ead.data.protobuf.DataHolder;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.util.*;

/**
 * 相关性广告投放广告位探索
 *
 * <AUTHOR>
 */
@ToString(exclude = "builder")
public class RelevanceSlotExploration implements IWritable {
    @Getter
    @Setter
    private DataHolder.RelevanceSlotExploration.Builder builder;

    /**
     * 允许投放的广告位集合
     */
    @Getter
    private ImmutableSet<String> slotAllowedSet;

    /**
     * 广告组在广告位上点击数
     */
    @Getter
    private ImmutableMap<String, Long> slotClickMap;

    /**
     * 广告组id
     */
    @Getter
    long groupId;

    public RelevanceSlotExploration() {
        this.groupId = 0;
        this.builder = DataHolder.RelevanceSlotExploration.newBuilder();
        this.builder.setGroupId(this.groupId);
    }

    public RelevanceSlotExploration(long groupId, Set<String> slotAllowedSet, Map<String, Long> slotClickMap) {
        this.groupId = groupId;
        this.builder = DataHolder.RelevanceSlotExploration.newBuilder();
        this.builder.setGroupId(this.groupId);
        if (slotAllowedSet != null) {
            this.builder.addAllSlotAllowed(slotAllowedSet);
        }
        if (slotClickMap != null) {
            List<DataHolder.RelevanceSlotExploration.SlotClick> slotClickList =
                    new ArrayList<DataHolder.RelevanceSlotExploration.SlotClick>(slotClickMap.size());
            for (Map.Entry<String, Long> entry : slotClickMap.entrySet()) {
                DataHolder.RelevanceSlotExploration.SlotClick.Builder slotClickBuilder =
                        DataHolder.RelevanceSlotExploration.SlotClick.newBuilder();
                slotClickBuilder.setSlotId(entry.getKey())
                        .setClick(entry.getValue());
                slotClickList.add(slotClickBuilder.build());
            }
            this.builder.addAllSlotClick(slotClickList);
        }
        setSlotAllowedSets();
        setSlotClickMap();
    }


    @Override
    public void writeFields(DataOutput out) throws IOException {
        byte[] bytes = this.builder.build().toByteArray();
        out.writeInt(bytes.length);
        out.write(bytes);
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        int byteLength = in.readInt();
        byte[] bytes = new byte[byteLength];
        in.readFully(bytes);

        this.builder = DataHolder.RelevanceSlotExploration.parseFrom(bytes).toBuilder();
        this.groupId = this.builder.getGroupId();
        setSlotAllowedSets();
        setSlotClickMap();
    }

    @Override
    public IWritable copyFields(IWritable value) {
        throw new UnsupportedOperationException();
    }


    private void setSlotAllowedSets() {
        this.slotAllowedSet = new ImmutableSet.Builder<String>()
                .addAll(this.builder.getSlotAllowedList()).build();
    }

    private void setSlotClickMap() {
        List<DataHolder.RelevanceSlotExploration.SlotClick> slotClickList = this.builder.getSlotClickList();
        Map<String, Long> map = new HashMap<String, Long>(slotClickList.size());
        for (DataHolder.RelevanceSlotExploration.SlotClick slotClick : slotClickList) {
            map.put(slotClick.getSlotId(), slotClick.getClick());
        }
        this.slotClickMap = new ImmutableMap.Builder<String, Long>().putAll(map).build();
    }
}
