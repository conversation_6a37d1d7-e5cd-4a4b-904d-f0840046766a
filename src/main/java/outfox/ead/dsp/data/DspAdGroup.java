/**
 * @(#)DspAdGroup.java, 2012-11-9.
 *
 * Copyright 2012 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.dsp.data;

import com.google.common.collect.ImmutableMap;
import com.google.protobuf.ByteString;
import lombok.NonNull;
import lombok.ToString;
import odis.serialize.IWritable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.SerializationUtils;
import outfox.ead.data.AdGroup;
import outfox.ead.data.SdkAppInfo;
import outfox.ead.data.StateObject;
import outfox.ead.data.protobuf.DataHolder.ChargeType;
import outfox.ead.data.protobuf.DataHolder.*;
import outfox.ead.data.protobuf.DataHolder.DspAdVariation;
import outfox.ead.data.protobuf.DataHolder.DspAdGroupPb.*;

import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * AdGroup bean for dsp. Compared with {@link AdGroup}, it removed all
 * attributes about Keywords and SponsorAccount, and added ChargeType and Cost.
 * The operations on Advariations are not thread safe.
 *
 * <AUTHOR>
 */
@ToString
public class DspAdGroup extends StateObject implements IWritable {
    public static long INVALID_ID = 0;

    private DspAdGroupPb.Builder builder;

    /**
     * 广告组上所有的 App 限价信息 key: sdkAppId, value: price.
     */
    private ImmutableMap<Long, Integer> appLimitPrice;

    /**
     * 广告组在广告位上的限价 key: slotId, value: price.
     */
    private ImmutableMap<String, Integer> slotLimitPrice;

    /**
     * MoreInfo, with byte[] convert from Object
     */
    private ImmutableMap<String, Object> moreInfo;


    /**
     * sponsor id of adExchange. key: syndId, value: sponsor id for the adExchange.
     */
    private ImmutableMap<Integer, Integer> sponsorRtbStatusExtMap;

    /**
     * sponsor rtbStatus for some adx. key: syndId, value: rtb status.
     */
    private ImmutableMap<Integer, String> adExchangeSids;

    /**
     * 广告组在广告位上的相关性数值 key: slotId, value: relevancy.
     */
    private ImmutableMap<String, Double> slotRelevancy;

    /**
     * Empty Constructor.
     */
    public DspAdGroup() {
        this.id = 0;
        this.builder = DspAdGroupPb.newBuilder()
                .setId(0)
                .setSponsorId(0)
                .setChargeType(ChargeType.CPC)
                .setCost(0);
    }

    public DspAdGroup(long id, AdCampaign adCampaign, long sponsorId,
                      AdDeliveryPlan adPlan) {
        this(id, adCampaign, sponsorId, adPlan, ChargeType.CPC, 0);
    }

    public DspAdGroup(long id, AdCampaign adCampaign, long sponsorId,
                      AdDeliveryPlan adPlan, ChargeType chargeType, int cost) {
        this.id = id;
        DspAdGroupPb.Builder builder = DspAdGroupPb.newBuilder()
                .setId(id)
                .setSponsorId(sponsorId)
                .setCost(cost);

        if(chargeType != null){
            builder.setChargeType(chargeType);
        }
        if (adCampaign != null) {
            builder.setAdCampaign(adCampaign);
        }
        if (adPlan != null){
            builder.setAdPlan(adPlan);
        }
        this.builder = builder;
    }

    public DspAdGroup(@NonNull DspAdGroupPb instance) {
        this.builder = instance.toBuilder();
    }

    public DspAdGroupPb.Builder getBuilder() {
        return builder;
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        int byteLength = in.readInt();
        byte[] bytes = new byte[byteLength];
        in.readFully(bytes);
        builder = DspAdGroupPb.parseFrom(bytes).toBuilder();

        setAppLimitPrice();
        setSlotLimitPrice();
        setMoreInfo();
        setAdExchangeSids();
        setSponsorRtbStatusExtMap();
        setSlotRelevancy();
    }

    @Override
    public void writeFields(DataOutput out) throws IOException {
        byte[] bytes = builder.build().toByteArray();
        out.writeInt(bytes.length);
        out.write(bytes);
    }

    @Override
    public IWritable copyFields(IWritable arg0) {
        throw new UnsupportedOperationException();
    }

    @Override
    public long getId(){
        return builder.getId();
    }

    public Double getSmoothAlpha() {
        return builder.getSmoothAlpha();
    }

    public void setSmoothAlpha(Double smoothAlpha) {
        builder.setSmoothAlpha(smoothAlpha);
    }

    public Double getCampaignSmoothAlpha() {
        return builder.getCampaignSmoothAlpha();
    }

    public void setCampaignSmoothAlpha(Double smoothAlpha) {
        builder.setCampaignSmoothAlpha(smoothAlpha);
    }

    public Integer getBudget() {
        return builder.getBudget();
    }

    public AdCampaign getAdCampaign() {
        return builder.getAdCampaign();
    }

    public AdDeliveryPlan getAdPlan() {
        return builder.getAdPlan();
    }

    public long getSponsorId() {
        return builder.getSponsorId();
    }

    public Sponsor getSponsor() {
        return builder.getSponsor();
    }

    public String getAdvertiserId() {
        return builder.getAdvertiserId();
    }

    public long getAgentId() {
        return builder.getAgentId();
    }

    public int getCost() {
        return builder.getCost();
    }

    public int getCpaCost() {
        return builder.getCpaCost();
    }

    public int getOptimizationGoal() {
        return builder.getOptimizationGoal();
    }

    public int getOcpcPhase() {
        return builder.getOcpcPhase();
    }

    public String getOcpcConvertAction() {
        return builder.getOcpcConvertAction();
    }

    public AppDelivery getAppDelivery() {
        return builder.getAppDelivery();
    }

    public ConvertTracking getConvertTracking() {
        return builder.getConvertTracking();
    }

    private void setAppLimitPrice() {
        HashMap<Long, Integer> map = new HashMap<Long, Integer>();
        for (AppLimitPriceEntity entity : builder.getAppLimitPriceList()) {
            map.put(entity.getSdkAppId(), entity.getPrice());
        }
        this.appLimitPrice = new ImmutableMap.Builder<Long, Integer>().putAll(map).build();
    }

    public ImmutableMap<Long, Integer> getAppLimitPrice() {
        return appLimitPrice;
    }

    private void setSlotLimitPrice() {
        HashMap<String, Integer> map = new HashMap<String, Integer>();
        for (SlotLimitPriceEntity entity : builder.getSlotLimitPriceList()) {
            map.put(entity.getSlotId(), entity.getPrice());
        }
        this.slotLimitPrice = new ImmutableMap.Builder<String, Integer>().putAll(map).build();
    }

    public ImmutableMap<String, Integer> getSlotLimitPrice() {
        return slotLimitPrice;
    }

    public ChargeType getChargeType() {
        if (builder.hasChargeType()) {
            return builder.getChargeType();
        }
        return null;
    }

    /**
     *  设置MoreInfo, 将Pb中的ByteString 转化成 Object并存储到moreinfo中
     */
    private void setMoreInfo() {
        HashMap<String, Object> map = new HashMap<String, Object>(builder.getMoreInfoList().size());
        for (MoreInfo entity : builder.getMoreInfoList()) {
            map.put(entity.getKey(),
                    SerializationUtils.deserialize(entity.getValue().toByteArray())
            );
        }
        this.moreInfo = new ImmutableMap.Builder<String, Object>().putAll(map).build();
    }

    public ImmutableMap<String, Object> getMoreInfo() {
        return moreInfo;
    }

    public long getSponsorRtbStatus() {
        return builder.getSponsorRtbStatus();
    }

    private void setSponsorRtbStatusExtMap() {
        HashMap<Integer, Integer> map = new HashMap<Integer, Integer>(builder.getSponsorRtbStatusExtMapList().size());
        for (SponsorRtbStatusExtMapEntity entity : builder.getSponsorRtbStatusExtMapList()) {
            map.put(entity.getSyndId(), entity.getStatus());
        }
        this.sponsorRtbStatusExtMap = new ImmutableMap.Builder<Integer, Integer>().putAll(map).build();
    }

   public ImmutableMap<Integer, Integer> getSponsorRtbStatusExtMap() {
        return sponsorRtbStatusExtMap;
    }

    private void setAdExchangeSids() {
        HashMap<Integer, String> map = new HashMap<Integer, String>(builder.getAdExchangeSidsList().size());
        for (AdExchangeSidsEntity entity : builder.getAdExchangeSidsList()) {
            map.put(entity.getSyndId(), entity.getVid());
        }
        this.adExchangeSids = new ImmutableMap.Builder<Integer, String>().putAll(map).build();
    }

   public ImmutableMap<Integer, String> getAdExchangeSids() {
        return adExchangeSids;
    }

    private void setSlotRelevancy() {
        HashMap<String, Double> map = new HashMap<String, Double>(builder.getSponsor().getSponsorRelevancyList().size());
        for (SponsorRelevancy entity : builder.getSponsor().getSponsorRelevancyList()) {
            map.put(entity.getSlotId(), entity.getRelevancy());
        }
        this.slotRelevancy = new ImmutableMap.Builder<String, Double>().putAll(map).build();
    }

    public ImmutableMap<String, Double> getSlotRelevancy() {
        return slotRelevancy;
    }

    public long getDeliveryScope() {
        return builder.getDeliveryScope();
    }

    public int getProductType() {
        return builder.getProductType();
    }

    public int getImprLimitTimes() {
        return builder.getImprLimitTimes();
    }

    public int getConsumptionType() {
        return builder.getConsumptionType();
    }

    public boolean isDeviceTargeted() {
        return builder.getDeviceTargeted();
    }

    public int getGeneralCost() {
        return builder.getGeneralCost();
    }

    public int getBrandSiteCost() {
        return builder.getBrandSiteCost();
    }

    /**
     * 根据 App id 获取基本出价
     *
     * @return
     *     如果用户在此广告组上针对此 App 设置过出价，则返回此出价；
     *     否则返回在此广告组上的基本出价
     */
    private int getGeneralCost(long sdkAppId) {
        if (SdkAppInfo.isValidSdkAppId(sdkAppId)) {
            int appLimitPrice = getAppLimitPrice(sdkAppId);
            if (appLimitPrice != APP_LIMIT_PRICE_NOT_SET) {
                return appLimitPrice;
            }
        }
        return builder.getGeneralCost();
    }

    /**
     * 返回广告组出价优先级依次为：
     * <ul>
     *     <li>广告位上设定出价</li>
     *     <li>app上设定的出价</li>
     *     <li>{@link DspAdGroup#getGeneralCost()}</li>
     * </ul>
     * @param slotId 广告位id
     * @param appId 媒体应用id
     * @return 广告组出价
     */
    public int getGeneralCost(String slotId, long appId){
        int price = getSlotLimitPrice(slotId);
        if (price != SLOT_LIMIT_PRICE_NOT_SET){
            return price;
        }

        return getGeneralCost(appId);
    }

    /**
     * 当广告组在 App 上未设置限价时，获取此 App 限价值将返回 APP_LIMIT_PRICE_NOT_SET
     */
    public static final int APP_LIMIT_PRICE_NOT_SET = -1;

    /**
     * 当广告组在广告位上未设置限价时，获取此广告位限价值将返回此值
     */
    public static final int SLOT_LIMIT_PRICE_NOT_SET = -1;

    /**
     * 当广告组对应广告商在广告位上没有相关性数据时，获取此广告位和广告组相关性将返回此值
     */
    public static final double SLOT_RELEVANCY_NOT_SET = 0.0;

    /**
     * 获取当前 dspAdGroup 在特定 App 上的出价
     *
     * @param sdkAppId App id
     * @return
     *     如果对此 App 进行过价格限制，返回此限价；
     *     否则返回 {@link #APP_LIMIT_PRICE_NOT_SET}
     */
    public int getAppLimitPrice(long sdkAppId) {
        if (appLimitPrice != null && appLimitPrice.containsKey(sdkAppId)) {
          return appLimitPrice.get(sdkAppId);
        }
        return APP_LIMIT_PRICE_NOT_SET;
    }

    /**
     * 获取当前 dspAdGroup 在特定广告位上的出价
     *
     * @param slotId 广告位id
     * @return
     *     如果对此广告位进行过价格限制，返回此限价；
     *     否则返回 {@link #SLOT_LIMIT_PRICE_NOT_SET}
     */
    public int getSlotLimitPrice(String slotId) {
        if (slotLimitPrice != null && slotLimitPrice.containsKey(slotId)) {
           return slotLimitPrice.get(slotId);
        }
        return SLOT_LIMIT_PRICE_NOT_SET;
    }

    public List<DspAdVariation> getVariations() {
        return builder.getVariationsList();
    }

    /**
     * 获得投放计划的城市定向条件
     */
    public List<Integer> getDeliveryCities() {
        return builder.getDeliveryCitiesList();
    }

    /**
     * get sponsor's rtbStatus by a syndId. return null, if not exist.
     */
    public Integer getSponsorRtbStatusExt(int syndId) {

        if (sponsorRtbStatusExtMap != null && sponsorRtbStatusExtMap.containsKey(syndId)) {
            return sponsorRtbStatusExtMap.get(syndId);
        }
        return null;
    }

    /**
     * get ad exchange sid by a syndId. return null, if not exist.
     */
    public String getAdExchangeSid(int syndId) {

        if (adExchangeSids != null && adExchangeSids.containsKey(syndId)) {
            return adExchangeSids.get(syndId);
        }
        return null;
    }

    /**
     * 获取DeepLink,这个DeepLink是设置在组级别上的
     */
    public String getDeepLink() {
        return builder.getDeepLink();
    }

    /**
     * 使用组级别的DeepLink
     */
    public boolean enableDeepLink() {
        return builder.getEnableDeepLink();
    }

    /**
     * 使用合成链接，合成链接定义：wiki：http://confluence.inner.youdao.com/display/ead/glossary
     */
    public boolean enableCompositeLink() {
        return builder.getEnableCompositeLink();
    }

    /**
     * 获取当前 dspAdGroup 在特定广告位上的相关性数据
     *
     * @param slotId 广告位id
     * @return
     *     如果有广告组在此广告位上的相关性数据，返回此数据；
     *     否则返回 {@link #SLOT_RELEVANCY_NOT_SET}
     */
    public double getSlotRelevancy(String slotId) {
        if (slotRelevancy != null && slotRelevancy.containsKey(slotId)) {
            return slotRelevancy.get(slotId);
        }
        return SLOT_RELEVANCY_NOT_SET;
    }

    /**
     * judge whether the sponsor is delivered in the given syndId.
     */
    public boolean isAbleToDeliver(int syndId) {
        if(syndId > 100 && syndId < 117) {
            int position = (syndId - 101) << 2;
            return ((builder.getSponsorRtbStatus() >>> position) & 0xf) == 0x2;
        } else {
            Integer status = getSponsorRtbStatusExt(syndId);
            if (status != null) {
                return status == 2;
            } else {
                // if not set adx status, then reject to deliver
                return false;
            }
        }
    }

    /**
     * judge whether the sponsor is rejected by the given syndId.
     */
    public boolean isRejectedToDeliver(int syndId) {
        return !isAbleToDeliver(syndId);
    }

    @Override
    public int hashCode() {
        return (int) (builder.getId() ^ (builder.getId() >>> 32));
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof DspAdGroup) {
            DspAdGroup other = (DspAdGroup) obj;
            return other.getId() == builder.getId();
        }
        return false;
    }

    /**
     * @return AdCampaign id， if null, return 0;
     */
    public long getAdCampaignId() {
        if (builder.getAdCampaign() != null) {
            return builder.getAdCampaign().getId();
        }
        return INVALID_ID;
    }

    /**
     * @return AdPlan id, if null, return 0;
     */
    public long getAdPlanId() {
        if (builder.getAdPlan() != null) {
            return builder.getAdPlan().getId();
        }
        return INVALID_ID;
    }

    /**
     * get an info from moreInfo.
     * @return value, null if not exist.
     */
    @SuppressWarnings("unchecked")
    public <T> T getInfo(String key){
        if (moreInfo == null) {
            return null;
        }
        return (T) moreInfo.get(key);
    }

    /**
     * judge whether the dspAdGroup is able to delivery to given city.
     */
    public boolean isAbleToDelivery(int cityId) {
        return isAbleToDeliveryByAdPlan(cityId) && isAbleToDeliveryByTag(cityId);
    }

    /**
     * 判断广告组自定义标签里的城市定向是否允许此城市投放出去
     */
    private boolean isAbleToDeliveryByTag(int cityId) {
        // 广告组自定义标签里的城市定向为空的话，允许投放
        // 城市定向为空，说明：广告组未选用自定义标签
        if (CollectionUtils.isEmpty(getDeliveryCitiesByTag())) {
            return true;
        }

        // 广告组自定义标签里的城市定向不是通投，且不包含这个cityId，也不能投放出去
        if (!getDeliveryCitiesByTag().contains(0) && !getDeliveryCitiesByTag().contains(cityId)) {
            return false;
        }

        return true;
    }

    /**
     * 判断广告计划的城市定向是否允许此城市投放出去
     */
    private boolean isAbleToDeliveryByAdPlan(int cityId) {
        // 投放计划里的城市定向为空的话，不能投放出去
        // 这里是因为：每个广告组都有对应的投放计划，生产投放计划的城市定向的时候，如果投放计划的城市定向为空，会给城市定向赋值0，表示通投
        // 正常情况下这里不可能为empty
        if (CollectionUtils.isEmpty(getDeliveryCities())) {
            return false;
        }

        // 投放计划里的城市定向不是通投，且不包含这个cityId，也不能投放出去
        if (!getDeliveryCities().contains(0) && !getDeliveryCities().contains(cityId)) {
            return false;
        }

        return true;
    }

    /**
     * @return whether is able to delivery to all cities
     */
    public boolean isAllCity() {
        // 如果标签的城市定向为空，表明该广告组未选用自定义标签，那自然是能投出去所有城市的
        return getDeliveryCities().contains(0) && (CollectionUtils.isEmpty(getDeliveryCitiesByTag()) || getDeliveryCitiesByTag().contains(0));
    }

    /**
     * @return 返回广告组所有的投放广告位
     */
    public List<String> getSlotIds(){
        return builder.getSlotIdsList();
    }

    /**
     * @return 返回广告组所有的投放场景id
     */
    public List<Long> getScenarioIds(){
        return builder.getScenarioIdsList();
    }

    /**
     * @return 返回广告组投放时相关性定向的下限
     */
    public double getMinRelevancy() { return builder.getMinRelevancy(); }

    /**
     * @return 返回广告组使用的所有的自定义标签里的省份定向内容
     * 多个标签的省份定向取并集
     */
    public ByteString getDeliveryProvincesByTag() {
        return builder.getDeliveryProvincesByTag();
    }

    /**
     * @return 返回广告组使用的所有的自定义标签里的城市定向内容
     * 多个标签的城市定向取并集
     */
    public List<Integer> getDeliveryCitiesByTag() {
        return builder.getDeliveryCitiesByTagList();
    }

    /**
     * @return 返回广告组的名称。
     */
    public String getName() {
        return builder.getName();
    }

    public String getAppPermission() {
        return builder.getAppPermission();
    }

    public String getPrivacyPolicy() {
        return builder.getPrivacyPolicy();
    }

    public String getDeveloperName() {
        return builder.getDeveloperName();
    }

    public String getAppTitle() {
        return builder.getAppTitle();
    }

    public String getAppIconImage() {
        return builder.getAppIconImage();
    }

    public String getAppVersion() {
        return builder.getAppVersion();
    }

    public int getAndroidDownloadType(){
        return builder.getAndroidDownloadType();
    }

    public String getAndroidPackageName(){
        return builder.getAndroidPackageName();
    }

    public String getWechatOriginId() {
        return builder.getWechatOriginId();
    }

    public String getWechatPath() {
        return builder.getWechatPath();
    }

    public boolean isShakable() {
        return builder.getShakable();
    }

    public boolean isSlideInteract() {
        return builder.getSlideInteract();
    }

    public boolean isIgnoreMacroReplace() {
        return builder.getIgnoreMacroReplace();
    }

    public String getAppDescUrl(){
        return builder.getAppDescUrl();
    }

    public boolean isOsVersionTarget() {
        return builder.getOsVersionTarget();
    }

    public List<OsVersionInterval> getOsVersionIntervalList() {
        return builder.getOsVersionIntervalList();
    }

    public int getAttributionType() {
        return builder.getAttributionType();
    }

    public boolean getIsPo(){
        return builder.getIsPo();
    }

    public int getBudgetClutchForConv() {
        return builder.getBudgetClutchForConv();
    }

    public boolean isTrafficAllocationEnabled() {
        return builder.getTrafficAllocationEnabled();
    }

}
