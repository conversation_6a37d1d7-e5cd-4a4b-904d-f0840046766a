package outfox.ead.noah.job;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import outfox.ead.dataserv2.noticer.NoticeProxy;
import outfox.ead.noah.dao.AdCampaignDao;
import outfox.ead.noah.dao.AdGroupExtendForDspDao;
import outfox.ead.noah.dao.AdPlanDao;
import outfox.ead.noah.dao.SponsorExtendForDspDao;
import outfox.ead.noah.entity.AdCampaign;
import outfox.ead.noah.entity.AdGroupExtendForDsp;
import outfox.ead.noah.entity.AdPlan;
import outfox.ead.noah.entity.SponsorExtendForDsp;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 每天凌晨用次日预算覆盖今日预算
 */
@Component
@Slf4j
public class NextDayBudgetCoverDayBudgetJob extends AbstractLeaderRunnableJob {

    private static final String CRON_EXPRESSION_0 = "0 0 0 * * ?";

    @Autowired
    private AdPlanDao adPlanDao;

    @Autowired
    private AdGroupExtendForDspDao adGroupExtendForDspDao;

    @Autowired
    private AdCampaignDao adCampaignDao;

    @Autowired
    private NoticeProxy noticeProxy;

    @Autowired
    private SponsorExtendForDspDao sponsorExtendForDspDao;

    @Scheduled(cron = CRON_EXPRESSION_0)
    public void nextDayBudgetCoverDayBudget() {
        log.info("ready to do job ---> NextDayBudgetCoverDayBudgetJob");
        StopWatch stopWatch = new StopWatch();

        stopWatch.start("collect target data");
        List<SponsorExtendForDsp> sponsorsHaveNextDayBudget = sponsorExtendForDspDao.findAllNextDayBudgetNotNull();
        List<AdPlan> adPlans = adPlanDao.findByNextDayBudgetIsNotNull();
        List<AdCampaign> adCampaigns = adCampaignDao.findAllByAdPlanIds(adPlans.stream().map(AdPlan::getAdPlanId).collect(Collectors.toList()));
        List<AdGroupExtendForDsp> adGroupExtendForDsps = adGroupExtendForDspDao.findAllByNextDailyBudgetIsNotNull();
        stopWatch.stop();

        stopWatch.start("use next day budget cover daily budget");
        sponsorExtendForDspDao.nextDayBudgetCoverDailyBudget();
        adPlanDao.nextDayBudgetCoverDayBudget();
        adGroupExtendForDspDao.nextDayBudgetCoverDayBudget();
        stopWatch.stop();

        stopWatch.start("send realtime produce message");
        noticeProxy.sendSponsorChangeMessage(sponsorsHaveNextDayBudget.stream().map(SponsorExtendForDsp::getSponsorId).collect(Collectors.toList()));
        noticeProxy.sendAdCampaignChangeMessage(adCampaigns.stream().map(AdCampaign::getAdCampaignId).collect(Collectors.toList()));
        noticeProxy.sendAdGroupChangeMessage(adGroupExtendForDsps.stream().map(AdGroupExtendForDsp::getAdGroupId).collect(Collectors.toList()));
        stopWatch.stop();
        log.info("job finished ---> NextDayBudgetCoverDayBudgetJob");
        log.info("{} Sponsor(s), {} adPlan(s), {} adCampaigns and {} adGroup(s) updated.",
                sponsorsHaveNextDayBudget.size(), adPlans.size(), adCampaigns.size(), adGroupExtendForDsps.size());
        log.info(stopWatch.prettyPrint());
    }
}
