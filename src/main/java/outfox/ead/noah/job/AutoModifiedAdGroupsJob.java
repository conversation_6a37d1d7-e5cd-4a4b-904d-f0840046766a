package outfox.ead.noah.job;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import outfox.ead.noah.dao.ModifiedTemplateDao;
import outfox.ead.noah.entity.ModifiedTemplate;
import outfox.ead.noah.service.AdGroupService;

import java.util.List;

/**
 * 模板映射发生该表时，需要同步修改选择该模板的广告组
 */
@Slf4j
@Component
public class AutoModifiedAdGroupsJob extends AbstractLeaderRunnableJob {
    private final static Logger logger = LoggerFactory.getLogger(AutoModifiedAdGroupsJob.class);

    @Autowired
    ModifiedTemplateDao modifiedTemplateDao;

    @Autowired
    AdGroupService adGroupService;

    public AutoModifiedAdGroupsJob() {
        logger.info("start AutoModifiedAdGroupsJob .......");
    }

    @Scheduled(cron = "0 0 2 * * ?")
    public void modifiedAdGroups() {
        log.info("Start modifiedAdGroups...");
        StopWatch stopWatch = new StopWatch();
        List<ModifiedTemplate> templates = modifiedTemplateDao.getAllTemplateIds();
        log.info("modified template list: {}", templates);
        for (ModifiedTemplate template : templates) {
            Long templateId = template.getTemplateId();
            stopWatch.start(String.valueOf(templateId));
            try {
                log.info("start with templateId {} ", templateId);
                adGroupService.updateAdGroupWhenTemplateMapIsChanged(templateId);
                modifiedTemplateDao.delByTemplateId(templateId);
                log.info("done with templateId {} ", templateId);
            } catch (Exception e) {
                log.warn("update adgroup due to template update failed, templateId: {}", templateId, e);
            } finally {
                stopWatch.stop();
            }
        }
        log.info("Done with modifiedAdGroups...");
        log.info(stopWatch.prettyPrint());
    }
}
