package outfox.ead.noah.annotation.resolvers;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import outfox.ead.noah.annotation.CurrentUser;
import outfox.ead.noah.dao.SdkDeveloperDao;
import outfox.ead.noah.dao.SponsorDao;
import outfox.ead.noah.entity.SdkDeveloper;
import outfox.ead.noah.entity.Sponsor;
import outfox.ead.noah.exception.BadRequestException;
import outfox.ead.noah.util.logger.LogUtil;
import outfox.ead.noah.util.params.AuthParams;

/**
 * 增加方法注入，将含有CurrentUser注解的方法参数注入当前登录用户
 *
 * <AUTHOR>
 * @date 2016/5/10.
 * @see outfox.ead.noah.annotation.CurrentUser
 */
@Component
@Lazy
@Slf4j
public class CurrentUserMethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Autowired
    private SponsorDao sponsorDao;

    @Autowired
    private SdkDeveloperDao sdkDeveloperDao;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        //如果参数类型是Sponsor或SdkDeveloper并且有CurrentUser注解则支持
        return (parameter.getParameterType().isAssignableFrom(Sponsor.class) ||
                parameter.getParameterType().isAssignableFrom(SdkDeveloper.class)) &&
                parameter.hasParameterAnnotation(CurrentUser.class);
    }

    @Override
    public Object resolveArgument(
            MethodParameter parameter, ModelAndViewContainer mavContainer,
            NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        //取出鉴权时存入的登录用户token
        String sponsorId = webRequest.getHeader(AuthParams.SPONSOR_ID);
        String sdkDevId = webRequest.getHeader(AuthParams.SDK_DEV_ID);
        if (sponsorId != null) {
            //从数据库中查询并返回
            try {
                Long sponsorIdLong = Long.parseLong(sponsorId);
                Sponsor sponsor = sponsorDao.getSponsor(sponsorIdLong);
                Assert.notNull(sponsor, "No Sponsor logged in");
                return sponsor;
            } catch (Exception e) {
                LogUtil.errorLog(log, e);
                throw new BadRequestException("sponsorId not correct");
            }

        } else if (sdkDevId != null) {
            //从数据库中查询并返回
            try {
                Long sdkDevIdLong = Long.parseLong(sdkDevId);
                return sdkDeveloperDao.getById(sdkDevIdLong);
            } catch (Exception e) {
                LogUtil.errorLog(log, e);
                throw new BadRequestException("sdkDevId not correct");
            }
        } else {
            throw new MissingServletRequestPartException(
                    AuthParams.SPONSOR_ID + " or " + AuthParams.SDK_DEV_ID);
        }
    }

}
