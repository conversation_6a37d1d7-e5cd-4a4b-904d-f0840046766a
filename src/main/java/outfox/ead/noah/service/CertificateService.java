package outfox.ead.noah.service;

import outfox.ead.noah.entity.Certificate;
import outfox.ead.noah.entity.models.simple.SimpleCertificate;

import java.util.List;
import java.util.Set;

public interface CertificateService {

    List<SimpleCertificate> getAllSimpleCertificate(long sponsorId);

    Certificate getCertificate(long sponsorId,long cert_id);
    
    void saveOrUpdate(Certificate cert);

    List<Certificate> listBySponsorIdWithSort(long sponsorId);

    void deleteCertificate(long sponsorId,long certId);
    
    boolean uploadCertificate(long sponsorId,long srcId,Certificate cert);
//    void makeupMiss(long sponsorId,Certificate cert);
//    void newCert(long sponsorId,Certificate cert);
    void submitForAudit(List<Certificate> certs);
    
    /**
     * 判断是否即将过期 或者已经过期
     * @param cert
     * @return
     */
    boolean isExpiring(Certificate cert);

    void submitForAudit(long sponsorId, Set<Long> certIds);

    void uploadCertFile(long sponsorId, long certId, String fileName, String fileData) throws Exception;

    String getCertBase64(Certificate certificate) throws Exception;
}