package outfox.ead.noah.service.builder;

import io.druid.data.input.Row;

import java.util.HashMap;
import java.util.Map;

/**
 * 获取转化数据、合并转化数等功能。
 */
public interface ConvertGetter {

    /**
     * 从sdk_stat_v2、landingpage_stat表中获取转化。
     */
    long getConvert(Row row);

    /**
     * 合并两个从转化数的Key到转化数的map。key有可能是string类型的时间，或者{@link outfox.ead.noah.service.convert.Key}
     */
    static <K> Map<K, Long> merge(Map<K, Long> convData1, Map<K, Long> convData2) {
        if (convData1 == null) {
            convData1 = new HashMap<>();
        }
        Map<K, Long> mergeConvData = new HashMap<>(convData1);
        if (convData2 != null) {
            convData2.forEach((key, conv) -> {
                mergeConvData.put(key, mergeConvData.getOrDefault(key, 0L) + conv);
            });
        }
        return mergeConvData;
    }

    /**
     * 使用{@link #merge(Map, Map)}进行两次合并。
     */
    static <K> Map<K, Long> merge(Map<K, Long> convData1, Map<K, Long> convData2, Map<K, Long> convData3) {
        return merge(merge(convData1, convData2), convData3);
    }
}
