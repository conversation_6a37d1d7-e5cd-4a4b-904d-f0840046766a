package outfox.ead.noah.service;

import outfox.ead.finance.daoV1.SponsorCostAggregation;
import outfox.ead.finance.dto.SponsorAccountHistoryDTO;
import outfox.ead.finance.hibernate.SponsorAccountHistory;
import outfox.ead.noah.entity.models.simple.SimpleInvoice;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 调用外部Service，查询用户Finance数据
 * <p>
 * Created by huanghuan on 16/5/11.
 */
public interface FinanceStatService {

    /**
     * 查询Sponsor的账户历史纪录
     *
     * @param startDate 可以为null
     * @param endDate   可以为null
     */
    public List<SponsorAccountHistoryDTO> getSponsorAccountHistory(long sponsorId, Long startDate, Long endDate);

    public List<SimpleInvoice> getAllSimpleInvoice(long sponsorId);

    Map<String, Double> invoiceTotalAvailable(long sponsorId);

    /**
     * 查询Sponsor的消费信息
     *
     * @param startDate 可以为null
     * @param endDate   可以为null
     */
    public List<SponsorCostAggregation> getSponsorCostAggregation(long sponsorId, Date startDate, Date endDate);

    /**
     * 获取Sponsor某一天的消费
     *
     * @param day 0表示当天，负数表示今天前的某一天，正数表示今天后的某一天
     */
    public long getSponsorConsumptionByDay(long sponsorId, int day);

    /**
     * 获取Sponsor的历史总消费
     */
    public long getTotalExpenses(long sponsorId);

    /**
     * 获取Sponsor的余额
     */
    public long getSponsorBalance(long sponsorId);
}
