package outfox.ead.noah.service.impl;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.dataserv2.noticer.NoticeProxy;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.controller.AdContentController;
import outfox.ead.noah.controller.AdGroupController;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.dao.*;
import outfox.ead.noah.dto.ConvertTrackingRecent7DaysDTO;
import outfox.ead.noah.dto.CreateOrUpdateAdGroupDTO;
import outfox.ead.noah.entity.*;
import outfox.ead.noah.exception.ServerException;
import outfox.ead.noah.service.ConvertTrackingService;
import outfox.ead.noah.service.ConvertTrackingStatService;
import outfox.ead.noah.service.ThirdPartPromotionInfoService;
import outfox.ead.noah.service.async.AsyncWrapper;
import outfox.ead.noah.util.ExceptionUtil;
import outfox.ead.noah.util.SegmentStatusUtil;
import outfox.ead.noah.util.code.TypeCode;
import outfox.ead.noah.util.enums.ConvertActionEnum;
import outfox.ead.noah.util.logger.LogUtil;
import outfox.ead.noah.util.logger.OpType;
import outfox.ead.noah.util.stringutil.StringUtil;
import outfox.venus.client2.VenusClient;
import outfox.venus.client2.data.BasicData;
import outfox.venus.client2.data.Data;
import outfox.venus.client2.data.Id;

import java.security.MessageDigest;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.transaction.annotation.Isolation.SERIALIZABLE;
import static outfox.ead.noah.constants.Constants.*;
import static outfox.ead.noah.constants.ConvertActionsStatusConstants.*;
import static outfox.ead.noah.entity.ConvertTrackingInfo.FIELD_URL_SCHEME_REPLACED;
import static outfox.ead.noah.entity.ConvertTrackingInfo.FIELD_WECHAT_PATH_REPLACED;
import static outfox.ead.noah.util.code.NameCode.CLICK;
import static outfox.ead.noah.util.code.StatusCode.*;
import static outfox.ead.noah.util.code.TypeCode.*;
import static outfox.ead.noah.util.params.AdContentParams.AD_CONTENT_IDS;
import static outfox.ead.noah.util.params.AdGroupParams.AD_GROUP_ID;
import static outfox.ead.noah.util.params.AdGroupParams.TARGET_TYPE_SLOT;

/**
 * Created by wangmo on 2018/2/2.
 */
@Service
public class ConvertTrackingServiceImpl implements ConvertTrackingService {

    private static final Logger logger = LoggerFactory.getLogger(ConvertTrackingServiceImpl.class);

    private static FastDateFormat targetFormat = FastDateFormat.getInstance("yyyy-MM-dd", TimeZone.getTimeZone(Constants.ASIA_SHANGHAI));

    @Autowired
    @Qualifier("convertDebugAdContentSdkData")
    private String convertDebugAdContentSdkData;

    @Autowired
    private VenusClient venusClient;

    @Autowired
    private NoticeProxy noticeProxy;

    @Autowired
    private AsyncWrapper asyncWrapper;

    @Autowired
    private ConvertTrackingDao convertTrackingDao;

    @Autowired
    private SdkSelfTestDeviceDao sdkSelfTestDeviceDao;

    @Autowired
    private AdGroupDao adGroupDao;

    @Autowired
    private AdGroupExtendForDspDao adGroupExtendForDspDao;

    @Autowired
    private AdContentDao adContentDao;

    @Autowired
    private AdContentExtendForSdkDao adContentExtendForSdkDao;

    @Autowired
    private SponsorDao sponsorDao;

    @Autowired
    private AdContentCategoryDao adContentCategoryDao;

    @Autowired
    private AdContentApkInfoDao adContentApkInfoDao;

    @Autowired
    private ConvertTrackingStatService convertTrackingStatService;

    @Autowired
    private AdGroupController adGroupController;

    @Autowired
    private AdContentController adContentController;

    @Autowired
    private ThirdPartPromotionInfoDao thirdPartPromotionInfoDao;

    @Autowired
    private ThirdPartPromotionInfoService infoService;

    @Override
    public boolean isConvertTrackingNameUsed(Long sponsorId, String convertTrackingName) {
        return convertTrackingDao.isConvertTrackingNameUsed(sponsorId, convertTrackingName);
    }

    @Override
    @Transactional(transactionManager = "eadb1tx", isolation = SERIALIZABLE)
    public String createConvertTracking(ConvertTrackingInfo convertTrackingInfo) {
        try {
            // todo: 临时解决方案，建议后期做成完善的功能
            // 创建名称以_api结尾的落地页型转化跟踪时，无需联调直接激活
            if (convertTrackingInfo.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE
                    && convertTrackingInfo.getConvertTrackingName().endsWith("_api")) {
                convertTrackingInfo.setConvertTrackingDebugStatus(CONVERT_DEBUG_STATUS_SUCCEED);
            }
            convertTrackingDao.save(convertTrackingInfo);
            String uid = convertTrackingInfo.getConvertTrackingId() + getRandomString(5);
            convertTrackingInfo.setConvertTrackingUid(uid);

            // generate self test material
            if (convertTrackingInfo.getConvertTrackingType() != CONVERT_TYPE_LANDINGPAGE) {
                Long adGroupId = generateSelfTestMaterial(convertTrackingInfo);
                convertTrackingInfo.setConvertTrackingDebugAdGroupId(adGroupId);
            }

            convertTrackingDao.update(convertTrackingInfo);
            LogUtil.toDb(Type.CONVERT_TRACKING, convertTrackingInfo.getSponsorId(), OpType.CONVERT_TRACKING_CREATE,
                    String.format("create convert tracking, uid: %s, detail: %s ", uid, convertTrackingInfo));
            return uid;
        } catch (Exception e) {
            logger.error("createConvertTracking failed...", e);
            throw new ServerException("保存转化跟踪失败");
        }
    }

    private Long generateSelfTestMaterial(ConvertTrackingInfo convertTrackingInfo) {
        String convertTrackingUid = convertTrackingInfo.getConvertTrackingUid();
        String downloadLink = convertTrackingInfo.getDownloadLink();
        Integer convertTrackingType = convertTrackingInfo.getConvertTrackingType();
        Sponsor sponsor = sponsorDao.getSponsor(CONVERT_DEBUG_SPONSOR_ID);
        String adGroupName = "转化跟踪联调_勿删_" + convertTrackingUid;
        String adContentName = "转化跟踪联调" + convertTrackingUid;
        Long adCampaignId;
        String appId, slotId, appOs;
        String appPermission = null;
        String privacyPolicy = null;
        String developerName = null;
        String appTitle = null;
        String appIconImage = null;
        String appVersion = null;
        String appDescUrl = null;
        int enableUrlSchema = 0;
        String urlSchema = "";
        if (convertTrackingType == TypeCode.CONVERT_TYPE_ANDROID_DOWNLOAD) {
            adCampaignId = CONVERT_DEBUG_ANDROID_AD_CAMPAIGN_ID;
            appId = CONVERT_DEBUG_APP_ANDROID;
            slotId = CONVERT_DEBUG_SLOT_ANDROID_UDID;
            appOs = "1";
            // only android app download group need params below.
            appPermission = "https://www.youdao.com";
            privacyPolicy = "https://www.youdao.com/";
            developerName = "有道智选广告";
            appTitle = "有道智选";
            appVersion = "1.0";
            appIconImage = "https://ydlunacommon.nos-jd.163yun.com/a1b1f8b46ad7fb4f899f618e87f1d59a.png";
            appDescUrl = "https://www.youdao.com";
        } else if (convertTrackingType == CONVERT_TYPE_LANDINGPAGE_API) {
            adCampaignId = CONVERT_DEBUG_LANDINGPAGE_CAMPAIGN_ID;
            appId = CONVERT_DEBUG_APP_ANDROID + "," + CONVERT_DEBUG_APP_IOS;
            slotId = CONVERT_DEBUG_SLOT_ANDROID_UDID + "," + CONVERT_DEBUG_SLOT_IOS_UDID;
            appOs = null;
        } else if (convertTrackingType == CONVERT_TYPE_WECHAT || convertTrackingType == CONVERT_TYPE_WECHAT_V2) {
            // todo 自测联调物料需要配置
            adCampaignId = CONVERT_DEBUG_LANDINGPAGE_CAMPAIGN_ID;
            appId = CONVERT_DEBUG_APP_ANDROID + "," + CONVERT_DEBUG_APP_IOS;
            slotId = CONVERT_DEBUG_SLOT_ANDROID_UDID + "," + CONVERT_DEBUG_SLOT_IOS_UDID;
            appOs = null;
            downloadLink = "https://zhixuan.163.com/";
        } else if (convertTrackingType == CONVERT_TYPE_ANDROID_DEEPLINK) {
            adCampaignId = CONVERT_DEBUG_ANDROID_AD_CAMPAIGN_ID;
            appId = CONVERT_DEBUG_APP_ANDROID;
            slotId = CONVERT_DEBUG_SLOT_ANDROID_UDID;
            appOs = "1";
            enableUrlSchema = 1;
            urlSchema = convertTrackingInfo.getUrlScheme();
            downloadLink = "https://zhixuan.163.com/";
            // only android app download group need params below.
            appPermission = "https://www.youdao.com";
            privacyPolicy = "https://www.youdao.com/";
            developerName = "有道智选广告";
            appTitle = "有道智选";
            appVersion = "1.0";
            appIconImage = "https://ydlunacommon.nos-jd.163yun.com/a1b1f8b46ad7fb4f899f618e87f1d59a.png";
            appDescUrl = "https://www.youdao.com";
        } else if (convertTrackingType == CONVERT_TYPE_IOS_DEEPLINK) {
            adCampaignId = CONVERT_DEBUG_IOS_AD_CAMPAIGN_ID;
            appId = CONVERT_DEBUG_APP_IOS;
            slotId = CONVERT_DEBUG_SLOT_IOS_UDID;
            appOs = "0";
            enableUrlSchema = 1;
            urlSchema = convertTrackingInfo.getUrlScheme();
            downloadLink = "https://zhixuan.163.com/";
        } else {
            adCampaignId = CONVERT_DEBUG_IOS_AD_CAMPAIGN_ID;
            appId = CONVERT_DEBUG_APP_IOS;
            slotId = CONVERT_DEBUG_SLOT_IOS_UDID;
            appOs = "0";
        }
        CreateOrUpdateAdGroupDTO dto = new CreateOrUpdateAdGroupDTO(adCampaignId, adGroupName,
                downloadLink, enableUrlSchema, urlSchema,
                convertTrackingUid, "0",   // 正常消费
                "100", null, "1000000", "0",
                null, "1", appId, slotId, appOs, null,
                null, -1, null, TARGET_TYPE_SLOT,
                0, "0", null, null, null, null, null, "0", null, CLICK,
                null, null, null, null,
                null, null, null, -1,
                null, 0, null, null,
                appPermission, privacyPolicy, developerName, appTitle,
                appIconImage, appVersion, appDescUrl, DOWNLOAD_TYPE_DIRECT, "", convertTrackingInfo.getWechatOriginId(),
                convertTrackingInfo.getWechatPath(),
                false, false, false, null, null,
                null, 1, 0, 0, null,
                null, null, null, null, null, null,false, null, null);

        // cpc
        String createAdGroupResult = adGroupController.createAdGroup(sponsor, dto);

        logger.info("create AdGroup result is:" + createAdGroupResult);

        Long adGroupId = Long.parseLong(JSONObject.fromObject(createAdGroupResult)
                .getJSONObject("data")
                .getString(AD_GROUP_ID));

        String createAdContentResult = adContentController.createAdContent(sponsor,
                adGroupId,
                adContentName,
                convertDebugAdContentSdkData,
                null);

        logger.info("create AdContent result is:" + createAdContentResult);

        Long adContentId = JSONObject.fromObject(createAdContentResult).getJSONObject("data")
                .getJSONArray(AD_CONTENT_IDS).getLong(0);

        AdGroup adGroup = adGroupDao.getAdGroup(adGroupId);
        adGroup.setStatus(0);
        adGroup.setEditStatus(0);
        adGroupDao.saveOrUpdateAdGroup(adGroup);
        AdGroupExtendForDsp adGroupExtendForDsp = adGroupExtendForDspDao.getByGroupId(adGroupId);
        adGroupExtendForDsp.setDeviceTargeted((byte) 1);  // 设备定投
        adGroupExtendForDspDao.saveOrUpdateAdGroupExtendForDsp(adGroupExtendForDsp);
        AdContent adContent = adContentDao.getAdContent(adContentId);
        adContent.setStatus(0); // 设为有效
        adContentDao.updateAdContent(adContent);
        AdContentExtendForSdk adContentExtendForSdk = adContentExtendForSdkDao.getByAdContentId(adContentId);
        adContentExtendForSdk.setSdkDataType(0);  // a test ad
        adContentExtendForSdkDao.saveOrUpdateAdContentExtendForSdk(adContentExtendForSdk);
        AdContentCategory adContentCategory = new AdContentCategory(); // 保存创意的种类，否则物料会被bs过滤
        adContentCategory.setAdContentId(adContentId);
        adContentCategory.setCategoryId(CATEGORY_ID);
        adContentCategory.setCreateTime(new Date());
        adContentCategory.setLastModifyTime(new Date());
        adContentCategoryDao.saveOrUpdateAdContentCategory(adContentCategory);

        asyncWrapper.asyncRunMethod(() -> noticeProxy.sendAdCampaignChangeMessage(adCampaignId));

        return adGroupId;
    }

    @Override
    public boolean changeConvertTrackingName(ConvertTrackingInfo ctInfo, String convertTrackingName) {
        try {
            ctInfo.setConvertTrackingName(convertTrackingName);
            convertTrackingDao.update(ctInfo);
            LogUtil.toDb(Type.CONVERT_TRACKING, ctInfo.getSponsorId(), OpType.CONVERT_TRACKING_NAME_UPDATE,
                    "update convert tracking name, convertTrackingId: " + ctInfo.getConvertTrackingId()
                            + ", convertTrackingName: " + convertTrackingName);
            return true;
        } catch (Exception e) {
            logger.error("changeConvertTrackingName failed..., ctInfo: {}, ctName: {}. ",
                    ctInfo, convertTrackingName, e);
            throw new ServerException("修改转化跟踪名称失败");
        }
    }

    @Override
    public boolean changeConvertTrackingStatus(List<ConvertTrackingInfo> ctInfos, Integer status) {
        try {
            ctInfos.forEach(ctInfo -> ctInfo.setConvertTrackingStatus(status));
            convertTrackingDao.update(ctInfos);
            LogUtil.toDb(Type.CONVERT_TRACKING, ctInfos.get(0).getSponsorId(), OpType.CONVERT_TRACKING_STATUS_UPDATE,
                    "update convert tracking status, convertTrackingId: " + ctInfos.stream()
                            .map(ConvertTrackingInfo::getConvertTrackingId).collect(Collectors.toList())
                            + ", status: " + status);
            return true;
        } catch (Exception e) {
            logger.error("changeConvertTrackingStatus failed...", e);
            throw new ServerException("修改转化跟踪状态失败");
        }
    }

    @Override
    public JSONObject getConvertTrackingJSON(ConvertTrackingInfo ctInfo) {
        try {
            return toJson(ctInfo);
        } catch (Exception e) {
            logger.error("getConvertTrackingJSON failed...", e);
            throw new ServerException("根据Uid查询转化跟踪失败");
        }
    }

    @Override
    public JSONObject getConvertTrackingJSONList(Long sponsorId, long startEpochMilli, long endEpochMilli,
                                                 Integer offset, Integer limit) {
        JSONObject result = new JSONObject();
        List<ConvertTrackingInfo> ctInfos =
                convertTrackingDao.getConvertTrackingInfoList(sponsorId, offset, limit);

        // 获取转化数据时候，只需要获取当前已经激活的。
        // 如果落地页实际已经激活，在未查询到的时候，也不会被使用，逻辑上不用查询
        // 另，由于使用了事务，也保证了在未查到已经激活的时候，不会有任何线上非联调数据
        Map<String, Long> convertNumMap = convertTrackingStatService.getConvertNumCount(sponsorId,
                ctInfos.stream()
                        .filter(c -> c.getConvertTrackingDebugStatus() == CONVERT_DEBUG_STATUS_SUCCEED)
                        .collect(Collectors.toList()),
                startEpochMilli, endEpochMilli);

        JSONArray convertList = new JSONArray();
        for (ConvertTrackingInfo ctInfo : ctInfos) {
            JSONObject jo = toJson(ctInfo);
            if (convertNumMap.containsKey(ctInfo.getConvertTrackingUid())) {
                jo.put("convertNum", convertNumMap.get(ctInfo.getConvertTrackingUid()));
            } else {
                jo.put("convertNum", 0);
            }
            convertList.add(jo);
        }
        result.put("convertList", convertList);
        result.put("total", convertTrackingDao.getConvertTrackingInfoListCount(sponsorId));
        Map<String, Boolean> lpConvertActivatedMap = convertTrackingStatService.isLPConvertActivated(
                ctInfos.stream()
                        .filter(c -> c.getConvertTrackingDebugStatus() != CONVERT_DEBUG_STATUS_SUCCEED
                                && c.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE)
                        .collect(Collectors.toList()));

        ctInfos.stream()
                .filter(c -> c.getConvertTrackingDebugStatus() != CONVERT_DEBUG_STATUS_SUCCEED
                        && c.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE)
                .forEach(c -> {
                    if (lpConvertActivatedMap.containsKey(c.getConvertTrackingUid())
                            && lpConvertActivatedMap.get(c.getConvertTrackingUid())) {
                        c.setConvertTrackingDebugStatus(CONVERT_DEBUG_STATUS_SUCCEED);
                        convertTrackingDao.update(c);
                    }
                });
        return result;
    }

    @Override
    public JSONArray getActivatedConvertTrackingInfoListByType(Long sponsorId, List<Integer> convertTrackingTypes) {
        try {
            List<ConvertTrackingInfo> ctInfos =
                    convertTrackingDao.getActivatedByConvertTrackingType(sponsorId, convertTrackingTypes);
            JSONArray result = new JSONArray();
            ctInfos.forEach(ctInfo -> {
                JSONObject jo = new JSONObject();
                jo.put("convertId", ctInfo.getConvertTrackingUid());
                jo.put("convertName", ctInfo.getConvertTrackingName());
                String[] convertActions = ctInfo.getConvertTrackingActions().split(",");
                Map<String, String> convertActionKeyToNameMap = new HashMap<>();
                for (String action : convertActions) {
                    ConvertActionEnum cae = ConvertActionEnum.getByKey(action);
                    if (cae != null) {
                        convertActionKeyToNameMap.put(action, cae.getName());
                    }
                }
                jo.put("convertActions", convertActionKeyToNameMap);
                jo.put("convertType", ctInfo.getConvertTrackingType());
                if (CONVERT_TYPE_WECHAT == ctInfo.getConvertTrackingType() || CONVERT_TYPE_WECHAT_V2 == ctInfo.getConvertTrackingType()) {
                    jo.put("wechatOriginId", ctInfo.getWechatOriginId());
                    jo.put("wechatPath", ctInfo.getWechatPath());
                }
                if (CONVERT_TYPE_ANDROID_DEEPLINK == ctInfo.getConvertTrackingType() || CONVERT_TYPE_IOS_DEEPLINK == ctInfo.getConvertTrackingType()) {
                    jo.put("urlScheme", ctInfo.getUrlScheme());
                }
                result.add(jo);
            });
            return result;
        } catch (Exception e) {
            logger.error("getUsableConvertTrackingInfos failed...", e);
            throw new ServerException("查询所有已激活转化失败");
        }
    }

    @Override
    @Transactional(transactionManager = "eadb1tx", isolation = SERIALIZABLE)
    public JSONObject initConvertTrackingDebug(ConvertTrackingInfo ctInfo, String testDeviceId) {
        try {
            JSONObject result = new JSONObject();

            if (ctInfo.getConvertTrackingDebugStatus() != CONVERT_DEBUG_STATUS_SUCCEED) {
                ctInfo.setConvertTrackingDebugStatus(CONVERT_DEBUG_STATUS_READY_TO_TEST);
                ctInfo.setClickTrackingLinkUploaded(null);
                ctInfo.setWechatPathReplaced(null);
                ctInfo.setUrlSchemeReplaced(null);
                convertTrackingDao.update(ctInfo);
                // set ad group status to 0

                activateDebugGroup(ctInfo);

                // write self test device
                sdkSelfTestDeviceDao.save(ctInfo.getSponsorId(), testDeviceId, ctInfo.getConvertTrackingUid());

                // send a message, real time produce SdkSelfTestDevice
                Long slotId;
                if (ctInfo.getConvertTrackingStatus() == TypeCode.CONVERT_TYPE_ANDROID_DOWNLOAD || ctInfo.getConvertTrackingStatus() == CONVERT_TYPE_ANDROID_DEEPLINK) {
                    slotId = CONVERT_DEBUG_SLOT_ANDROID_ID;
                } else {
                    slotId = CONVERT_DEBUG_SLOT_IOS_ID;
                }

                asyncWrapper.asyncRunMethod(() -> noticeProxy.sendSdkAdSlotChangeMessage(slotId));

                // 写Venus testDeviceId & ctInfo.getConvertTrackingDebugAdGroupId()
                String md5DeviceId = Hex.encodeHexString(
                        MessageDigest.getInstance("MD5").digest(testDeviceId.toUpperCase().getBytes())).toUpperCase();
                Id id = new Id(
                        md5DeviceId,
                        VENUS_DEVICE_BINDING_FIELD_ID,
                        ctInfo.getConvertTrackingDebugAdGroupId() + "");

                Data data = new BasicData(id);
                logger.info("start write device binding to venus, deviceId is " + testDeviceId
                        + ", md5DeviceId is " + md5DeviceId + "adGroupId is " + ctInfo.getConvertTrackingDebugAdGroupId());
                Map<String, Data> dataMap = new HashMap<>(1);
                dataMap.put(data.getId().getDataId(), data);
                venusClient.replaceByField(data.getId().getKey(), data.getId().getFieldId(), dataMap);
                logger.info("finish write device binding to venus...");
            }
            result.put("status", ctInfo.getConvertTrackingDebugStatus());
            return result;
        } catch (Exception e) {
            logger.error("initConvertTrackingDebug failed...", e);
            throw new ServerException("转化跟踪联调失败");
        }
    }

    /**
     * 调整联调推广组的状态为有效（如果是其他状态）并发通知给生产者
     */
    private void activateDebugGroup(ConvertTrackingInfo ctInfo) {
        AdGroup adGroup = adGroupDao.getAdGroup(ctInfo.getConvertTrackingDebugAdGroupId());
        if (adGroup.getStatus() != COMMON_STATUS_NORMAL) {
            adGroupDao.updateAdGroupStatus(ctInfo.getConvertTrackingDebugAdGroupId(), COMMON_STATUS_NORMAL);
            asyncWrapper.asyncRunMethod(() -> noticeProxy.sendAdGroupChangeMessage(ctInfo.getConvertTrackingDebugAdGroupId()));
        }
    }

    @Override
    @Transactional(transactionManager = "eadb1tx", isolation = SERIALIZABLE)
    public JSONObject getConvertTrackingDebugStatus(ConvertTrackingInfo ctInfo) {
        try {
            JSONObject result = new JSONObject();

            if (ctInfo.getConvertTrackingDebugStatus() != CONVERT_DEBUG_STATUS_SUCCEED) {
                // query statistic, if succeed update convertTrackingDao.update(ctInfo);
                boolean activated = convertTrackingStatService.isDownloadAndLpApiConvertActivated(ctInfo);
                if (activated) {
                    ctInfo.setConvertTrackingDebugStatus(CONVERT_DEBUG_STATUS_SUCCEED);
                    convertTrackingDao.update(ctInfo);
                    pauseDebugAdGroup(ctInfo);
                } else {
                    if (StringUtils.isNotBlank(ctInfo.getClickTrackingLinkUploaded())) {
                        result.put("clickTrackingLinkUploaded", ctInfo.getClickTrackingLinkUploaded());
                    }
                }
                result.put(FIELD_URL_SCHEME_REPLACED, ctInfo.getUrlSchemeReplaced());
                result.put(FIELD_WECHAT_PATH_REPLACED, ctInfo.getWechatPathReplaced());
            }
            result.put("status", ctInfo.getConvertTrackingDebugStatus());
            return result;
        } catch (Exception e) {
            logger.error("getConvertTrackingDebugStatus failed...", e);
            throw new ServerException("转化跟踪联调状态获取失败");
        }
    }

    /**
     * 转化跟踪激活后，将联调用的组暂停，防止大量僵尸组堆积在数据服务层
     */
    @Override
    public void pauseDebugAdGroup(ConvertTrackingInfo ctInfo) {
        AdGroup adGroup = adGroupDao.getAdGroup(ctInfo.getConvertTrackingDebugAdGroupId());
        if (adGroup.getStatus().equals(COMMON_STATUS_NORMAL)) {
            adGroupDao.updateAdGroupStatus(ctInfo.getConvertTrackingDebugAdGroupId(), SegmentStatusUtil.getUserSegStatus(adGroup.getStatus(), STOP));
            logger.info("convert tracking uid: {} is activate, relate debug group: {} is paused", ctInfo.getConvertTrackingUid(), ctInfo.getConvertTrackingDebugAdGroupId());
        }
    }

    @Override
    public void activateConvertTracking(ConvertTrackingInfo convertTrackingInfo) {
        convertTrackingInfo.setConvertTrackingDebugStatus(CONVERT_DEBUG_STATUS_SUCCEED);
        convertTrackingDao.update(convertTrackingInfo);
    }

    @Override
    public boolean canDirectlyActivate(ConvertTrackingInfo convertTrackingInfo) {
        if (convertTrackingInfo == null
                || convertTrackingInfo.getConvertTrackingType() == TypeCode.CONVERT_TYPE_LANDINGPAGE
                || convertTrackingInfo.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE_API) {
            return false;
        }

        if (convertTrackingInfo.getConvertTrackingType() == TypeCode.CONVERT_TYPE_IOS_DOWNLOAD) {
            return canDirectlyActivateForTypeIOS(convertTrackingInfo);
        } else if (convertTrackingInfo.getConvertTrackingType() == TypeCode.CONVERT_TYPE_ANDROID_DOWNLOAD) {
            return canDirectlyActivateForTypeAndroid(convertTrackingInfo);
        } else {
            return false;
        }
    }


    @Override
    public void updateClickTrackingLink(ConvertTrackingInfo convertTrackingInfo, String clickTrackingLink) {
        if (!clickTrackingLink.equals(convertTrackingInfo.getClickTrackingLink())) {
            convertTrackingInfo.setClickTrackingLink(clickTrackingLink);
            convertTrackingDao.update(convertTrackingInfo);
            noticeConvertChange(convertTrackingInfo);
        }
    }

    private void noticeConvertChange(ConvertTrackingInfo convertTrackingInfo) {
        List<AdGroupExtendForDsp> adGroupList = adGroupExtendForDspDao.getAdGroupListByCtUid(convertTrackingInfo.getConvertTrackingUid());
        if (CollectionUtils.isNotEmpty(adGroupList)) {
            List<Long> adGroupIds = adGroupList.stream().map(AdGroupExtendForDsp::getAdGroupId).collect(Collectors.toList());
            asyncWrapper.asyncRunMethod(() -> noticeProxy.sendAdGroupChangeMessage(adGroupIds));
        }
        List<ThirdPartPromotionInfo> promotionInfos = thirdPartPromotionInfoDao.getThirdPartPromotionListByConvertId(convertTrackingInfo.getConvertTrackingUid());
        if (CollectionUtils.isNotEmpty(promotionInfos)) {
            List<Long> promotionIds = promotionInfos.stream().map(ThirdPartPromotionInfo::getPromotionId).collect(Collectors.toList());
            asyncWrapper.asyncRunMethod(() -> noticeProxy.sendActivityChangeMessage(promotionIds));
        }
    }

    private boolean canDirectlyActivateForTypeIOS(ConvertTrackingInfo convertTrackingInfo) {
        // 获取转化事件相同的已激活的转化跟踪。
        List<ConvertTrackingInfo> referredConvertTrackingInfos = convertTrackingDao.getActivatedByActions(
                convertTrackingInfo.getSponsorId(), convertTrackingInfo.getConvertTrackingType(), convertTrackingInfo.getConvertTrackingActions());
        if (CollectionUtils.isEmpty(referredConvertTrackingInfos)) {
            return false;
        }

        // 如果包含下载链接相同，点击检测链接域名相同的，返回true
        String targetClickTrackingLinkDomain = StringUtil.extractDomain(convertTrackingInfo.getClickTrackingLink());
        return referredConvertTrackingInfos.stream()
                .filter(ct -> ct.getDownloadLink().equals(convertTrackingInfo.getDownloadLink()))
                .anyMatch(ct -> Objects.equals(targetClickTrackingLinkDomain, StringUtil.extractDomain(ct.getClickTrackingLink())));
    }

    private boolean canDirectlyActivateForTypeAndroid(ConvertTrackingInfo convertTrackingInfo) {
        // 获取转化事件相同的已激活的转化跟踪。
        List<ConvertTrackingInfo> referredConvertTrackingInfos = convertTrackingDao.getActivatedByActions(
                convertTrackingInfo.getSponsorId(), convertTrackingInfo.getConvertTrackingType(), convertTrackingInfo.getConvertTrackingActions());
        if (CollectionUtils.isEmpty(referredConvertTrackingInfos)) {
            return false;
        }

        // 获取安卓应用的包名，如果是空，返回false。
        Set<String> targetPackageNames = adContentApkInfoDao.getPackageNamesByUrls(Collections.singleton(convertTrackingInfo.getDownloadLink()));
        if (CollectionUtils.isEmpty(targetPackageNames)) {
            return false;
        }
        String targetPackageName = new ArrayList<>(targetPackageNames).get(0);
        if (StringUtils.isEmpty(targetPackageName)) {
            return false;
        }

        // 过滤掉点击检测链接域名不相同的。如果过滤后集合为空，返回false。
        String targetClickTrackingLinkDomain = StringUtil.extractDomain(convertTrackingInfo.getClickTrackingLink());
        referredConvertTrackingInfos = referredConvertTrackingInfos.stream()
                .filter(ct -> Objects.equals(targetClickTrackingLinkDomain, StringUtil.extractDomain(ct.getClickTrackingLink())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(referredConvertTrackingInfos)) {
            return false;
        }

        // 获取转化事件相同的已激活的转化跟踪的包名。如不包含当前转化跟踪下载链接的包名，返回false。
        List<String> downloadLinks = referredConvertTrackingInfos.stream().map(ConvertTrackingInfo::getDownloadLink).collect(Collectors.toList());
        Set<String> packageNames = adContentApkInfoDao.getPackageNamesByUrls(downloadLinks);
        return packageNames.contains(targetPackageName);
    }

    private JSONObject toJson(ConvertTrackingInfo ctInfo) {
        JSONObject result = new JSONObject();
        result.put("convertId", ctInfo.getConvertTrackingUid());
        result.put("convertName", ctInfo.getConvertTrackingName());
        result.put("convertType", ctInfo.getConvertTrackingType());
        result.put("convertStatus", ctInfo.getConvertTrackingStatus());
        result.put("convertActions", ctInfo.getConvertTrackingActions().split(","));
        result.put("convertActivateStatus",
                ctInfo.getConvertTrackingDebugStatus() == CONVERT_DEBUG_STATUS_SUCCEED ?
                        CONVERT_ACTIVATE_STATUS_ACTIVATED : CONVERT_ACTIVATE_STATUS_INACTIVE);
        if (ctInfo.getConvertTrackingType() == TypeCode.CONVERT_TYPE_ANDROID_DOWNLOAD
                || ctInfo.getConvertTrackingType() == TypeCode.CONVERT_TYPE_IOS_DOWNLOAD) {
            result.put("downloadLink", ctInfo.getDownloadLink());
            result.put("clickTrackingLink", ctInfo.getClickTrackingLink());
        } else if (ctInfo.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE_API) {
            result.put("downloadLink", ctInfo.getDownloadLink());
        } else if (ctInfo.getConvertTrackingType() == CONVERT_TYPE_WECHAT || ctInfo.getConvertTrackingType() == CONVERT_TYPE_WECHAT_V2) {
            result.put("clickTrackingLink", ctInfo.getClickTrackingLink());
        } else if (ctInfo.getConvertTrackingType() == CONVERT_TYPE_ANDROID_DEEPLINK || ctInfo.getConvertTrackingType() == CONVERT_TYPE_IOS_DEEPLINK) {
            result.put("clickTrackingLink", ctInfo.getClickTrackingLink());
            result.put("urlScheme", ctInfo.getUrlScheme());
        }
        result.put("convertPageLevel", ctInfo.getConvertPageLevel());
        result.put("createTime", targetFormat.format(ctInfo.getCreateTime()));
        result.put("wechatOriginId", ctInfo.getWechatOriginId());
        result.put("wechatPath", ctInfo.getWechatPath());
        return result;
    }

    @Override
    public void updateConvertUrls(ConvertTrackingInfo convertTrackingInfo, String wechatPath, String urlScheme) {
        boolean convertChanged = false;

        if (StringUtils.isNotBlank(wechatPath)) {
            convertTrackingInfo.setWechatPath(wechatPath);
            convertChanged = true;
        }

        if (StringUtils.isNotBlank(urlScheme)) {
            convertTrackingInfo.setUrlScheme(urlScheme);
            convertChanged = true;
        }

        if (convertChanged) {
            convertTrackingDao.update(convertTrackingInfo);
            noticeConvertChange(convertTrackingInfo);
            updateRelateGroupAndPromotionUrls(convertTrackingInfo);
        }
    }

    @Override
    public Set<ConvertTrackingRecent7DaysDTO> getRecent7DaysConverts(Long convertUid, Long sponsorId) {
        ConvertTrackingInfo info = convertTrackingDao.getConvertTrackingInfoByUid(sponsorId, String.valueOf(convertUid));
        ExceptionUtil.notFoundIf(Objects.isNull(info), "convert tracking not exist or deleted");

        // 因为转化跟踪可以跨广告主推送，所以查统计的时候需要将sponsorId也作为查询条件
        Set<ConvertTrackingRecent7DaysDTO> recent7DaysConvertData = convertTrackingStatService.getRecent7DaysConvertData(convertUid, sponsorId, info.getConvertTrackingDebugStatus());
        List<String> convertActionsSelected = Arrays.asList(info.getConvertTrackingActions().split(","));
        mergeConvertActionsWithoutStat(recent7DaysConvertData, convertActionsSelected);
        judgeAndSetConvertActionStatus(recent7DaysConvertData, convertActionsSelected);
        return recent7DaysConvertData;
    }

    private static void mergeConvertActionsWithoutStat(Set<ConvertTrackingRecent7DaysDTO> recent7DaysConvertData, List<String> convertActionsSelected) {
        recent7DaysConvertData.addAll(getMissingEvents(recent7DaysConvertData, convertActionsSelected));
    }

    private void judgeAndSetConvertActionStatus(Set<ConvertTrackingRecent7DaysDTO> dtos, List<String> convertActionsSelected) {
        for (ConvertTrackingRecent7DaysDTO dto : dtos) {
            if (!ConvertActionEnum.getAllKeys().contains(dto.getConvertAction())) {
                dto.setStatus(UNKNOWN_EVENT);
                continue;
            }

            if (!convertActionsSelected.contains(dto.getConvertAction()) && dto.getRecent7DaysConvertCount() > 0) {
                dto.setStatus(REPORTED_BUT_NOT_REGISTER);
                continue;
            }

            if (dto.getRecent7DaysConvertCount() <= 0) {
                dto.setStatus(NO_REPORT_RECENT_7_DAYS);
                continue;
            }

            if (ConvertActionEnum.PURCHASE_ACTION_KEYS.contains(dto.getConvertAction()) && dto.getOrderAmount() <= 0) {
                dto.setStatus(REPORTED_BUT_NO_AMOUNT);
            } else {
                dto.setStatus(REPORTED);
            }
        }
    }

    /**
     * 把转化跟踪中勾选，但是查统计没查出来的转化事件整理成dto
     */
    private static List<ConvertTrackingRecent7DaysDTO> getMissingEvents(Set<ConvertTrackingRecent7DaysDTO> dtos, List<String> convertActionsSelected) {
        List<String> convertActionsFromDruid = dtos.stream()
                .map(ConvertTrackingRecent7DaysDTO::getConvertAction)
                .collect(Collectors.toList());

        return convertActionsSelected.stream()
                .filter(item -> !convertActionsFromDruid.contains(item))
                .map(item -> ConvertTrackingRecent7DaysDTO.builder()
                                 .convertAction(item)
                                 .recent7DaysConvertCount(0L)
                                 .orderAmount(0L).build())
                .collect(Collectors.toList());
    }

    @Override
    public boolean appendConvertEvent(Long convertId, Long sponsorId, String convertEvent) {
        ExceptionUtil.badRequestIf(!ConvertActionEnum.getAllKeys().contains(convertEvent), "unknown convert event");
        // 转化事件近7天存在转化数据且状态为上报过但未在转化跟踪勾选才可添加到转化跟踪
        Set<ConvertTrackingRecent7DaysDTO> recent7DaysConverts = getRecent7DaysConverts(convertId, sponsorId);
        Optional<ConvertTrackingRecent7DaysDTO> any = recent7DaysConverts.stream().filter(item -> item.getConvertAction().equals(convertEvent)).findAny();
        ConvertTrackingRecent7DaysDTO convertEventInfo = any.orElseThrow(() -> new ServerException("statics data missing"));
        ExceptionUtil.badRequestIf(!convertEventInfo.getStatus().equals(REPORTED_BUT_NOT_REGISTER), "can not add this event to convert tracking.");
        // 添加的转化事件需要和转化跟踪的类型相符
        ConvertTrackingInfo info = convertTrackingDao.getConvertTrackingInfoByUid(sponsorId, String.valueOf(convertId));
        ExceptionUtil.badRequestIf(Objects.isNull(ConvertActionEnum.getByKeyAndType(convertEvent, info.getConvertTrackingType())),
                "this convert tracking can not append this event:" + convertEvent);
        if (StringUtils.isNotBlank(info.getConvertTrackingActions())) {
            List<String> list = new ArrayList<>(Arrays.asList(info.getConvertTrackingActions().split(",")));
            ExceptionUtil.conflictIf(list.contains(convertEvent), "event is already exist");
            list.add(convertEvent);
            info.setConvertTrackingActions(String.join(",", list));
            convertTrackingDao.update(info);
            logger.info("convert tracking: {}, append event: {}, success", convertId, convertEvent);
            return true;
        }
        logger.warn("convert tracking: {} has none convert event yet", convertId);
        return false;
    }

    /**
     * 同步更新关联推广组和推广活动的投放小程序和应用直达链接
     *
     * @param convertTrackingInfo
     */
    private void updateRelateGroupAndPromotionUrls(ConvertTrackingInfo convertTrackingInfo) {
        List<AdGroupExtendForDsp> adGroupList = adGroupExtendForDspDao.getAdGroupListByCtUid(convertTrackingInfo.getConvertTrackingUid());
        if (CollectionUtils.isNotEmpty(adGroupList)) {
            for (AdGroupExtendForDsp adGroupExtendForDsp : adGroupList) {
                adGroupExtendForDsp.setWechatPath(convertTrackingInfo.getWechatPath());
                adGroupExtendForDsp.setDeepLinkUrl(convertTrackingInfo.getUrlScheme());
                adGroupExtendForDspDao.saveOrUpdateAdGroupExtendForDsp(adGroupExtendForDsp);
            }
            List<Long> adGroupIds = adGroupList.stream().map(AdGroupExtendForDsp::getAdGroupId).collect(Collectors.toList());
            asyncWrapper.asyncRunMethod(() -> noticeProxy.sendAdGroupChangeMessage(adGroupIds));
        }
        List<ThirdPartPromotionInfo> promotionInfos = thirdPartPromotionInfoDao.getThirdPartPromotionListByConvertId(convertTrackingInfo.getConvertTrackingUid());
        if (CollectionUtils.isNotEmpty(promotionInfos)) {
            for (ThirdPartPromotionInfo promotionInfo : promotionInfos) {
                promotionInfo.setWechatPath(convertTrackingInfo.getWechatPath());
                promotionInfo.setUrlScheme(convertTrackingInfo.getUrlScheme());
                infoService.createNewDestLink(promotionInfo, convertTrackingInfo);
                infoService.updateThirdPartPromotionInfo(promotionInfo);
            }
        }
    }


    /**
     * generate a numerical string with a length
     */
    private String getRandomString(int length) {
        StringBuilder str = new StringBuilder();
        Random rm = new Random();
        for (int i = 0; i < length; i++) {
            /* ******* 产生一个0-9之间的数 ******* */
            int x = (rm.nextInt() >>> 1) % 10;
            str.append(x);
        }
        return str.toString();
    }

}
