package outfox.ead.noah.service.impl;

import lombok.extern.java.Log;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import outfox.ead.noah.dao.GlobalDataDao;
import outfox.ead.noah.entity.Area;
import outfox.ead.noah.entity.IndustryType;
import outfox.ead.noah.entity.OverseasArea;
import outfox.ead.noah.entity.models.City;
import outfox.ead.noah.entity.models.Province;
import outfox.ead.noah.service.GlobalDataService;
import outfox.ead.noah.util.initialize.GlobalDataManager;

import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang.StringUtils.join;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static outfox.ead.noah.util.datautil.DataUtil.filter;
import static outfox.ead.noah.util.datautil.DataUtil.toIntegers;
import static outfox.ead.noah.util.params.AdCampaignParams.*;

/**
 * Created by huanghuan on 16/4/27.
 */
@Service
@Log
public class GlobalDataServiceImpl implements GlobalDataService {

    @Autowired
    private GlobalDataDao globalDataDao;

    @Override
    public List<Province> getAreaWithKeyword(String keyword) {
        List<Province> provinces = new ArrayList<>();
        if (GlobalDataManager.deliveryProvinceToCities != null) {

            for (Area provinceArea : GlobalDataManager.deliveryProvinceToCities.keySet()) {
                // 先检查省是否包含关键字，若包含，则将该省及其下所有市加入
                if (StringUtils.isEmpty(keyword)
                        || provinceArea.getName().contains(keyword)) {
                    Province prov = new Province(provinceArea.getId(),
                            provinceArea.getName(),
                            new ArrayList<>());
                    provinces.add(prov);
                    for (Area cityArea : GlobalDataManager.deliveryProvinceToCities.get(provinceArea)) {
                        prov.getCities().add(new City(cityArea.getId(), cityArea.getName()));
                    }
                } else { // 省不含关键字，检查市是否包含，若包含，则将该市以及其所属省加入
                    List<City> cities = new ArrayList<>();
                    for (Area cityArea : GlobalDataManager.deliveryProvinceToCities.get(provinceArea)) {
                        if (cityArea.getName().contains(keyword)) {
                            cities.add(new City(cityArea.getId(), cityArea.getName()));
                        }
                    }
                    if (cities.size() > 0) {
                        Province prov = new Province(provinceArea.getId(),
                                provinceArea.getName(),
                                cities);
                        provinces.add(prov);
                    }
                }
            }

        }
        log.info("provinces size: " + provinces.size());
        return provinces;
    }

    @Override
    public String getReadableArea(String provinceIdsStr, String cityIdsStr) {
        if (isBlank(provinceIdsStr) || provinceIdsStr.equals("0")) {
            return "不限";
        }

        Set<Integer> provinceIds = toIntegers(provinceIdsStr);
        Set<Integer> cityIds = toIntegers(cityIdsStr);

        Map<Integer, Area> areaMap = GlobalDataManager.deliveryAreaMap;
        if (MapUtils.isEmpty(areaMap)) {
            return "不限";
        }
        Set<Integer> validAreaIds = Collections.unmodifiableSet(areaMap.keySet());
        provinceIds = filter(provinceIds, p -> validAreaIds.contains(p));
        cityIds = filter(cityIds, c -> validAreaIds.contains(c));

        Map<Integer, List<Integer>> prov2City = new HashMap<>();
        for (Integer provId : provinceIds) {
            prov2City.put(provId, new ArrayList<>());
        }

        for (Integer cityId : cityIds) {
            Area city = areaMap.get(cityId);
            Area prov = areaMap.get(city.getParentId());
            List<Integer> citys = prov2City.get(prov.getId());
            if (citys != null) {
                citys.add(cityId);
            }
        }

        List<String> readableAreas = new ArrayList<>();
        prov2City.forEach((provId, cityId) -> {
            readableAreas.add(readable(provId, cityId, areaMap));
        });
        return join(readableAreas, ",");
    }

    @Override
    public String getReadableArea(String internalDestIdsStr) {
        if (NO_REGION_LIMIT.equals(internalDestIdsStr) || !isBlank(internalDestIdsStr) && internalDestIdsStr.equals(SELECT_ALL)) {
            return "国内不限";
        }

        Set<Integer> internalDestIds = toIntegers(internalDestIdsStr);

        Map<Integer, Area> areaMap = GlobalDataManager.deliveryAreaMap;
        if (MapUtils.isEmpty(areaMap)) {
            return "国内不限";
        }
        Set<Integer> validAreaIds = Collections.unmodifiableSet(areaMap.keySet());
        internalDestIds = filter(internalDestIds, validAreaIds::contains);
        Set<Integer> province = new HashSet<>();
        Set<Integer> city = new HashSet<>();
        for (Integer lastLayerId : internalDestIds) {
            if (areaMap.get(lastLayerId).getParentId() == 0) {
                province.add(lastLayerId);
            } else {
                city.add(lastLayerId);
            }
        }

        List<String> readableAreas = new ArrayList<>();
        province.forEach((provinceId) -> {
            readableAreas.add(areaMap.get(provinceId).getName());
        });
        city.forEach((cityId) -> {
            readableAreas.add(areaMap.get(areaMap.get(cityId).getParentId()).getName() + "/" + areaMap.get(cityId).getName());
        });
        int index = readableAreas.indexOf("其他");
        if (index != -1) {
            readableAreas.set(index, "国内其他");
        }
        return join(readableAreas, ",");
    }

    @Override
    public String getReadableOverseasArea(String overseasAreaIdsStr) {
        if (!isBlank(overseasAreaIdsStr) && overseasAreaIdsStr.equals(SELECT_ALL)) {
            return "海外不限";
        }

        Set<Integer> overseasAreaIds = toIntegers(overseasAreaIdsStr);

        Map<Integer, OverseasArea> areaMap = GlobalDataManager.overseasAreaMap;
        if (MapUtils.isEmpty(areaMap)) {
            return "海外不限";
        }
        Set<Integer> validAreaIds = Collections.unmodifiableSet(areaMap.keySet());
        overseasAreaIds = filter(overseasAreaIds, validAreaIds::contains);
        Set<Integer> continent = new HashSet<>();
        Set<Integer> country = new HashSet<>();
        Set<Integer> city = new HashSet<>();
        for (Integer lastLayerId : overseasAreaIds) {
            if (areaMap.get(lastLayerId).getParentId() == 0) {
                continent.add(lastLayerId);
            } else {
                if (lastLayerId > COUNTRY_SEGMENT) {//大于1000 判断为城市
                    city.add(lastLayerId);
                } else {
                    country.add(lastLayerId);
                }
            }
        }

        List<String> readableAreas = new ArrayList<>();
        continent.forEach((continentId) -> {
            readableAreas.add(areaMap.get(continentId).getName());
        });
        country.forEach((countryId) -> {
            readableAreas.add(areaMap.get(areaMap.get(countryId).getParentId()).getName() + "/" + areaMap.get(countryId).getName());
        });
        city.forEach((cityId) -> {
            readableAreas.add(areaMap.get(areaMap.get(areaMap.get(cityId).getParentId()).getParentId()).getName() + "/" + areaMap.get(areaMap.get(cityId).getParentId()).getName() + "/" + areaMap.get(cityId).getName());
        });
        int index = readableAreas.indexOf("其他");
        if (index != -1) {
            readableAreas.set(index, "海外其他");
        }
        return join(readableAreas, ",");
    }

    @Override
    public List<Province> getAreaWithMunicipalityArea() {
        List<Province> provinces = new ArrayList<>();
        if (GlobalDataManager.provinceToCitiesWithMunicipalityArea != null) {
            for (Area provinceArea : GlobalDataManager.provinceToCitiesWithMunicipalityArea.keySet()) {
                Province prov = new Province(provinceArea.getId(),
                    provinceArea.getName(),
                    new ArrayList<>());
                provinces.add(prov);
                for (Area cityArea : GlobalDataManager.provinceToCitiesWithMunicipalityArea.get(provinceArea)) {
                    prov.getCities().add(new City(cityArea.getId(), cityArea.getName()));
                }
            }
        }
        log.info("provinces size: " + provinces.size());
        return provinces;
    }

    private String readable(Integer provId, List<Integer> cityIds, Map<Integer, Area> areaMap) {
        String provName = areaMap.get(provId).getName();
        if (CollectionUtils.isEmpty(cityIds)) {
            return provName;
        } else {
            List<String> cityNames = cityIds.stream().map(c -> areaMap.get(c).getName()).collect(Collectors.toList());
            return provName + "(" + join(cityNames, ",") + ")";
        }
    }


    @Override
    public List<Area> getArea() throws Exception {
        return globalDataDao.getArea();
    }

    @Override
    public List<OverseasArea> getOverseasArea() throws Exception {
        return globalDataDao.getOverseasArea();
    }

    @Override
    public List<IndustryType> getIndustryType() throws Exception {
        return globalDataDao.getIndustryType();
    }
}
