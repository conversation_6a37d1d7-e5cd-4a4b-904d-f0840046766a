package outfox.ead.noah.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.util.SubnetUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.conf.CompanyIpConfig;
import outfox.ead.noah.service.CompanyIpService;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/5/25.
 */
@Service
@Slf4j
public class CompanyIpServiceImpl implements CompanyIpService {
    @Autowired
    CompanyIpConfig companyIpConfig;

    @Override
    public boolean isCompanyIp(long sponsorId, String remoteIp) {
        Set<String> companyIpSet = companyIpConfig.getCompanyIpSet();
        boolean innerCompany = false;
        if (companyIpSet.contains(remoteIp)) {
            innerCompany = true;
        } else {
            for (SubnetUtils subnet : companyIpConfig.getCompanyCidrSet()) {
                if (subnet.getInfo().isInRange(remoteIp)) {
                    innerCompany = true;
                }
            }
        }
        log.info("sponsor:{}, login ip:{}, INNER_COMPANY_PERMISSION:{}.", sponsorId, remoteIp, innerCompany);
        return innerCompany;
    }

}
