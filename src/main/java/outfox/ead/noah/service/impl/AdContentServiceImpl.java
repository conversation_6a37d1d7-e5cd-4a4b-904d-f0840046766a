package outfox.ead.noah.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.rholder.retry.*;
import com.google.gson.Gson;
import com.googlecode.mp4parser.authoring.Movie;
import com.googlecode.mp4parser.authoring.Track;
import com.googlecode.mp4parser.authoring.container.mp4.MovieCreator;
import net.sf.json.JSONObject;
import net.sf.json.JSONSerializer;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;
import outfox.ead.newstat.model.Column;
import outfox.ead.newstat.model.QueryResult;
import outfox.ead.noah.conf.CDNConfig;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.constants.SdkElementTypeConstants;
import outfox.ead.noah.controller.argument.AdContentArgument;
import outfox.ead.noah.controller.extracter.AppAdContentExtracter;
import outfox.ead.noah.controller.factory.ExtractorFactory;
import outfox.ead.noah.controller.factory.ValidatorFactory;
import outfox.ead.noah.controller.validator.AdContentValidator;
import outfox.ead.noah.controller.validator.MaterialLibraryValidator;
import outfox.ead.noah.core.SimplePage;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.dao.*;
import outfox.ead.noah.dto.DictVideoPost;
import outfox.ead.noah.entity.*;
import outfox.ead.noah.entity.models.complete.CompleteAdContent;
import outfox.ead.noah.entity.models.dsp.DspCommonStatItem;
import outfox.ead.noah.entity.models.simple.SimpleElem;
import outfox.ead.noah.entity.models.simple.SimpleElemTypeList;
import outfox.ead.noah.exception.BadRequestException;
import outfox.ead.noah.exception.NotFoundException;
import outfox.ead.noah.service.*;
import outfox.ead.noah.util.*;
import outfox.ead.noah.util.code.NameCode;
import outfox.ead.noah.util.code.StatusCode;
import outfox.ead.noah.util.datautil.DataUtil;
import outfox.ead.noah.util.datautil.DspCommonStatUtil;
import outfox.ead.noah.util.datautil.StatusUtil;
import outfox.ead.noah.util.logger.LogUtil;
import outfox.ead.noah.util.logger.OpType;
import outfox.ead.noah.util.params.AdCampaignParams;
import outfox.ead.noah.util.params.AdContentParams;
import outfox.ead.noah.util.params.AdGroupParams;
import outfox.ead.noah.util.params.ImageLibraryParams;
import outfox.ead.noah.util.stringutil.StringUtil;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.noah.constants.Constants.*;
import static outfox.ead.noah.util.datautil.DataUtil.*;

/**
 * Created by huanghuan on 16/6/14.
 * <p>
 * Edited by lizai on 16/6/29
 */
@Service
public class AdContentServiceImpl implements AdContentService {

    private static final Logger logger = LoggerFactory.getLogger(AdContentServiceImpl.class);

    @Autowired
    private AdContentDao adContentDao;

    @Autowired
    private AdContentExtendForDspDao adContentExtendForDspDao;

    @Autowired
    private AdContentExtendForSdkDao adContentExtendForSdkDao;

    @Autowired
    private AdContentExtendForHugeMaterialDao adContentExtendForHugeMaterialDao;

    @Autowired
    private AdContentCategoryDao adContentCategoryDao;

    @Resource(name = "dspStatsServiceImplAdapter")
    private DspStatsService dspStatsService;

    @Autowired
    private AdCampaignDao adCampaignDao;

    @Autowired
    private AdGroupDao adGroupDao;

    @Autowired
    private AdTemplateDao adTemplateDao;

    @Autowired
    private AdGroupService adGroupService;

    @Autowired
    private AdCampaignExtendForDspDao adCampaignExtendForDspDao;

    @Autowired
    private MaterialLibraryService materialLibraryService;

    @Autowired
    private SdkElementService sdkElementService;

    @Autowired
    private SdkElementDao sdkElementDao;

    @Autowired
    private SdkSchemaService sdkSchemaService;

    @Autowired
    private DictService dictService;

    @Autowired
    private DictPostIdHistoryDao dictPostIdHistoryDao;

    @Autowired
    private ImageMappingService imageMappingService;

    @Autowired
    private CDNConfig cdnConfig;

    @Autowired
    private AdContentAssetAuditResultService adContentAssetAuditResultService;

    private static final Gson GSON = new Gson();

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static final TypeReference<Map<Long, String>> LONG_TO_STRING_TYPE_REF = new TypeReference<Map<Long, String>>() {
    };

    @Override
    public Object getAdContentDetailById(Long adContentId, List<String> extraDataSetting) {
        CompleteAdContent completeAdContent = null;
        AdContent adContent = adContentDao.getAdContent(adContentId);
        if (adContent != null) {
            if (extraDataSetting == null || extraDataSetting.size() <= 0) {
                return adContent;
            } else {
                completeAdContent = new CompleteAdContent();
                if (extraDataSetting.contains(AdContentParams.CONDITION_AD_CONTENT_DSP_EXTEND)) {
                    AdContentExtendForDsp adContentExtendForDsp = adContentExtendForDspDao.getByAdContentId(adContentId);
                    completeAdContent.setAdContentExtendForDsp(adContentExtendForDsp);
                }
                if (extraDataSetting.contains(AdContentParams.CONDITION_AD_CONTENT_SDK_EXTEND)) {
                    AdContentExtendForSdk adContentExtendForSdk = adContentExtendForSdkDao.getByAdContentId(adContentId);
                    completeAdContent.setAdContentExtendForSdk(adContentExtendForSdk);
                }
                if (extraDataSetting.contains(AdContentParams.CONDITION_CONTENT_CATEGORY)) {
                    AdContentCategory adContentCategory = adContentCategoryDao.getAdContentCategoryByAdContentId(adContentId);
                    completeAdContent.setAdContentCategory(adContentCategory);
                }
                if (completeAdContent.getAdContentCategory() == null && completeAdContent.getAdContentExtendForDsp() == null && completeAdContent.getAdContentExtendForSdk() == null) {
                    return adContent;
                } else {
                    completeAdContent.setAdContent(adContent);
                }
            }
        }
        return completeAdContent;
    }

    @Override
    public Map<String, Object> getAdContentDetailById(Long adContentId) {
        Map<String, Object> adContentDetail = new HashMap<>();

        AdContent adContent = adContentDao.getAdContent(adContentId);
        adContentDetail.put(AdContentParams.AD_CONTENT_ID, adContent.getAdContentId());
        adContentDetail.put(AdContentParams.AD_CONTENT_NAME, adContent.getTitle());

        if (adContent.getProductType() == Constants.PRODUCT_TYPE_APP
                || adContent.getProductType() == Constants.PRODUCT_TYPE_APP) {
            adContentDetail.put(AdContentParams.IMPRESSION_LINK, adContent.getImpressionLink());
            adContentDetail.put("image", adContent.getMimeSrc());
        }

        return adContentDetail;
    }

    @Override
    public List getAllAdContentsBySponsorId(Long sponsorId, List<String> extraDataSetting) throws Exception {
        if (extraDataSetting == null || extraDataSetting.size() <= 0) {
            return adContentDao.getAdContentListBySponsorId(sponsorId);
        } else {
            List<Object> completeAdContents = null;
            List<Long> adContentIds = adContentDao.getAdContentIdsBySponsorId(sponsorId);
            if (adContentIds != null && adContentIds.size() > 0) {
                completeAdContents = new ArrayList<Object>();
                for (Long adContentId : adContentIds) {
                    Object completeAdContent = getAdContentDetailById(adContentId, extraDataSetting);
                    completeAdContents.add(completeAdContent);
                }
            }
            return completeAdContents;
        }
    }

    @Override
    public List getAllAdContentsByAdGroupId(Long adGroupId, List<String> extraDataSetting) throws Exception {
        if (extraDataSetting == null || extraDataSetting.size() <= 0) {
            return adContentDao.getAdContentListByAdGroupId(adGroupId);
        } else {
            List<Object> completeAdContents = null;
            List<Long> adContentIds = adContentDao.getAdContentIdsByAdGroupId(adGroupId);
            if (adContentIds != null && adContentIds.size() > 0) {
                completeAdContents = new ArrayList<Object>();
                for (Long adContentId : adContentIds) {
                    Object completeAdContent = getAdContentDetailById(adContentId, extraDataSetting);
                    completeAdContents.add(completeAdContent);
                }
            }
            return completeAdContents;
        }
    }


    @Override
    public Map<String, Long> getAdContentNumberWithStatus(long sponsorId, Set<String> status) throws Exception {

        Map<String, Long> result = new HashMap<String, Long>();


        for (String s : status) {
            switch (s) {
                case StatusCode.STATUS_INVALID:
                case StatusCode.STATUS_TO_BE_AUDIT:
                    result.put(s, adContentDao.getAdContentCount(sponsorId,
                            null, null, -1, null, s));
                    break;
                default:
                    break;
            }
        }

        return result;
    }

    @Override
    public boolean checkSponsorAccess(long sponsorId, Set<Long> adContentIds) throws Exception {
        if (adContentIds == null || adContentIds.size() == 0) {
            return false;
        }

        List<AdContent> adContents = adContentDao.getAdContentListByContentIds(adContentIds);
        Set<Long> sponsorIds = new HashSet<Long>();

        if (adContents != null) {
            for (AdContent adContent : adContents) {
                sponsorIds.add(adContent.getSponsorId());
            }
        }

        return sponsorIds.size() == 1 && sponsorIds.contains(sponsorId);
    }

    @Override
    public List<DspCommonStatItem> getAdContentsStatData(long sponsorId, Set<Long> adContentIds, long fromTime, long toTime) {
        List<QueryResult> statResult = dspStatsService.getDetailStatForIds(Type.CONTENT, sponsorId, adContentIds, fromTime, toTime);
        List<DspCommonStatItem> dspCommonStatItems = DspCommonStatUtil.getDspCommonStatItems(statResult);
        for (int i = 0; i < dspCommonStatItems.size(); ++i) {
            dspCommonStatItems.get(i).setId(
                    Long.parseLong(statResult.get(i).getValue(Column.VARIATION_ID)));
        }
        DspCommonStatUtil.completeDspCommonStatItemList(dspCommonStatItems, adContentIds);
        return dspCommonStatItems;
    }


    @Override
    public SimplePage<AdContent> getAdContentWithKeywordStatusLandingType(
            long sponsorId, Type idType, long campaignOrGroupId, String keyword, String status,
            String landingType, int pageNo, int pageCapacity) throws Exception {

        int landingTypeValue = DataUtil.getLandingTypeValue(landingType);

        Set<Long> adGroupIds = new HashSet<>();
        Set<Long> adCampaignIds = new HashSet<>();
        if (idType == Type.CAMPAIGN) {
            adCampaignIds.add(campaignOrGroupId);
        } else if (idType == Type.GROUP) {
            adGroupIds.add(campaignOrGroupId);
        }

        long count = adContentDao.getAdContentCount(sponsorId, adGroupIds, adCampaignIds, landingTypeValue, keyword, status);

        Pager pager = Pager.of(pageNo, pageCapacity, count);
        if (pager.excess()) {
            return SimplePage.of(count, pageNo, pageCapacity, new ArrayList());
        }

        List<Long> adContentIdList = adContentDao.getAdContentIdList(sponsorId, adGroupIds, adCampaignIds, landingTypeValue,
                keyword, status, pager.getPosition(), pager.getLength(), Constants.ORDER_TYPE_DESC);

        List<AdContent> contents = adContentDao
                .getAdContentListByContentIds(new HashSet<>(adContentIdList));
        contents.sort((content1, content2) -> {
            long idDiff = content1.getAdContentId() - content2.getAdContentId();
            return idDiff < 0 ? 1 : -1;
        });

        return SimplePage.of(count, pageNo, pageCapacity, contents);
    }

    @Override
    public Map<String, List> getAbstractContent(List<AdContent> adContents) throws Exception {
        Map<String, List> result = new HashedMap();
        result.put(AdContentParams.AD_CONTENT_ID, new ArrayList<Long>());
        result.put(AdContentParams.AD_CONTENT_NAME, new ArrayList<String>());
        result.put(AdGroupParams.AD_GROUP_ID, new ArrayList<Long>());
        result.put(AdGroupParams.AD_GROUP_NAME, new ArrayList<String>());
        result.put(AdCampaignParams.AD_CAMPAIGN_ID, new ArrayList<Long>());
        result.put(AdCampaignParams.AD_CAMPAIGN_NAME, new ArrayList<String>());
        result.put(NameCode.STATUS, new ArrayList<String>());
        result.put(NameCode.SWITCH, new ArrayList<String>());
        result.put(NameCode.LANDING_TYPE, new ArrayList());
        result.put(AdCampaignParams.AD_PLATFORM, new ArrayList());

        if (adContents == null || adContents.size() == 0) {
            return result;
        }

        Set<Long> adGroupIds = new HashSet<Long>();
        for (AdContent adContent : adContents) {
            adGroupIds.add(adContent.getAdGroupId());
        }

        List<AdGroup> adGroups = adGroupDao.getAdGroupListByIds(adGroupIds);

        Set<Long> adCampaignIds = new HashSet<>();
        Map<Long, AdGroup> groupIdToGroup = new HashMap<>();
        for (AdGroup adGroup : adGroups) {
            adCampaignIds.add(adGroup.getAdCampaignId());
            groupIdToGroup.put(adGroup.getAdGroupId(), adGroup);
        }

        List<AdCampaign> adCampaigns = adCampaignDao.getAdCampaignListByIds(adCampaignIds);
        Map<Long, AdCampaign> idCampaignMap = new HashMap<>();
        for (AdCampaign adCampaign : adCampaigns) {
            idCampaignMap.put(adCampaign.getAdCampaignId(), adCampaign);
        }

        List<AdCampaignExtendForDsp> adCampaignExtendForDspList = adCampaignExtendForDspDao
                .getExtendForDspsByCampaignsOrIds(new ArrayList(adCampaignIds));
        Map<Long, Integer> campaignIdToPlatform = new HashMap<>();
        for (AdCampaignExtendForDsp adCampaignExtendForDsp : adCampaignExtendForDspList) {
            campaignIdToPlatform.put(adCampaignExtendForDsp.getAdCampaignId(), adCampaignExtendForDsp.getAdPlatform());
        }

        for (AdContent adContent : adContents) {
            result.get(AdContentParams.AD_CONTENT_ID).add(adContent.getAdContentId());
            result.get(AdContentParams.AD_CONTENT_NAME).add(adContent.getTitle());
            result.get(AdGroupParams.AD_GROUP_ID).add(adContent.getAdGroupId());
            AdGroup group = groupIdToGroup.get(adContent.getAdGroupId());
            result.get(AdGroupParams.AD_GROUP_NAME).add(group.getName());
            long adCampaignId = group.getAdCampaignId();
            result.get(AdCampaignParams.AD_CAMPAIGN_ID).add(adCampaignId);
            result.get(AdCampaignParams.AD_PLATFORM).add(campaignIdToPlatform.get(adCampaignId));
            AdCampaign adCampaign = idCampaignMap.get(adCampaignId);
            result.get(AdCampaignParams.AD_CAMPAIGN_NAME).add(adCampaign.getName());
            result.get(NameCode.LANDING_TYPE).add(DataUtil.getLandingTypeStr(adCampaign.getLandingPageType()));
            Integer groupStatus = group.getStatus();
            Integer contentStatus = adContent.getStatus();
            result.get(NameCode.STATUS).add(StatusUtil.getAdContentStatusName(groupStatus, contentStatus));
            result.get(NameCode.SWITCH).add(StatusUtil.getAdContentSwitchStatus(groupStatus, contentStatus));
        }

        return result;
    }

    @Override
    public void saveOrUpdateAdContent(AdContent adContent) throws Exception {
        adContentDao.saveOrUpdateAdContent(adContent);
        LogUtil.toDb(Type.CONTENT, adContent.getSponsorId(), OpType.CHANGE_AD_CONTENT_NAME,
                "change ad content name, adContentId: " + adContent.getAdContentId()
                        + ", name: " + adContent.getTitle());
    }

    private void submitContentExtend(List<AdContent> adContentList,
                                     AdContentExtendForDsp adContentExtendForDsp) {
        logger.debug("submitContentExtend!");
        if (adContentList != null && adContentList.size() > 0
                && adContentExtendForDsp != null) {
            if (adContentExtendForDsp.getAdContentDspId() > 0) {
                // 动态物料 如果不是以%%URL%%开头，需要加上"http://"
                String dynamicUrl = adContentExtendForDsp.getDynamicUrl();
                // 用于投放
                String html = adContentExtendForDsp.getPublishHtml();
                // 用于预览
                String htmlCode = adContentExtendForDsp.getHtmlCode();
                adContentExtendForDsp = adContentExtendForDspDao.getById(adContentExtendForDsp
                        .getAdContentDspId());
                logger.debug("submitContentExtend!"
                        + adContentExtendForDsp.getAdContentDspId());
                // 变体图片url为模板图片url
                // 动态物料一次只能上传一个
                AdContent content = adContentList.get(0);
                Long contentId = content.getAdContentId();
                Long templateId = adContentExtendForDsp.getTemplateId();
                AdTemplate template = adTemplateDao.getAdTemplate(templateId);
                content.setMimeSrc(template.getImageUrl());
                content.setType(Constants.DAO_AD_TYPE_DYNAMIC);
                content.setMimeWidth(template.getTemplateWidth());
                content.setMimeHeight(template.getTemplateHeight());
                adContentDao.saveOrUpdateAdContent(content);
                // 扩展表
                adContentExtendForDsp.setAdContentId(contentId);
                adContentExtendForDsp.setAdGroupId(content.getAdGroupId());

                dynamicUrl = StringUtil.getDynamicUrl(dynamicUrl);

                adContentExtendForDsp.setDynamicUrl(dynamicUrl);
                adContentExtendForDsp.setPublishHtml(html);
                adContentExtendForDsp.setHtmlCode(htmlCode);
                logger.debug("contentId:" + contentId);
                adContentExtendForDspDao
                        .saveOrUpdateAdContentExtendForDsp(adContentExtendForDsp);
            } else {
                logger.info("create dynamic content err!");
            }
        }
    }

    @Override
    public boolean checkSponsorAccess(long sponsorId, Long adContentId) throws Exception {
        if (adContentId == null) {
            return false;
        }

        AdContent adContent = adContentDao.getAdContent(adContentId);
        return adContent != null && sponsorId == adContent.getSponsorId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public long submitAdContent(AdContent adContent, AdContentExtendForDsp adContentExtendForDsp,
                                AdContentExtendForSdk adContentExtendForSdk) throws Exception {
        adContentDao.saveOrUpdateAdContent(adContent);
        long adContentId = adContent.getAdContentId();
        logger.info("saved adContent id: {}", adContentId);

        adContentExtendForDsp.setAdContentId(adContentId);
        adContentExtendForDspDao.saveOrUpdateAdContentExtendForDsp(adContentExtendForDsp);
        logger.info("saved adContentExtendForDsp id:{},adContent id:{}",
                adContentExtendForDsp.getAdContentDspId(), adContent.getAdContentId());

        if (adContentExtendForSdk != null) {
            adContentExtendForSdk.setAdContentId(adContentId);
            checkAndSendContentToDict(adContentExtendForSdk);
            adContentExtendForSdkDao.saveOrUpdateAdContentExtendForSdk(adContentExtendForSdk);
            logger.info("saved adContentExtendForSdk id:{},adContent id:{}",
                    adContentExtendForSdk.getSdkAdContentId(), adContent.getAdContentId());
        }
        if (StringUtils.isNotBlank(adContentExtendForSdk.getSdkData())) {
            updateHugeMaterialRelateContentId(adContentExtendForSdk.getSdkData(), adContentId);
        }
        LogUtil.toDb(Type.CONTENT, adContent.getSponsorId(), OpType.ADD_AD_CONTENT,
                "add ad content, adContentId:" + adContentId + ", adContent:" + GSON.toJson(adContent));

        return adContentId;
    }

    private void updateHugeMaterialRelateContentId(String sdkData, Long adContentId) {
        Map<String, String> sdkDataMap = JSONObject.fromObject(sdkData);
        List<SdkElement> htmlContentElements = sdkElementDao.getListByIdsAndType(sdkDataMap.keySet().stream().map(Long::parseLong).collect(Collectors.toSet()), SdkElementTypeConstants.HTML_CONTENT);
        Set<Long> htmlContentElementIds = htmlContentElements.stream().map(SdkElement::getSdkElementId).collect(Collectors.toSet());
        Set<Long> hugeMaterialIds = sdkDataMap.entrySet().stream().filter(entry -> htmlContentElementIds.contains(Long.parseLong(entry.getKey())))
                .map(item -> Long.parseLong(item.getValue()))
                .collect(Collectors.toSet());
        List<AdContentExtendForHugeMaterial> hugeMaterials = adContentExtendForHugeMaterialDao.findByIds(hugeMaterialIds);
        hugeMaterials.forEach(item -> {
            item.setAdContentId(adContentId);
        });
        adContentExtendForHugeMaterialDao.batchInsertOrUpdate(hugeMaterials);
        removeDatelessHugeMaterial(adContentId, hugeMaterialIds);
    }

    /**
     * 移除掉无用的大物料
     */
    private void removeDatelessHugeMaterial(Long adContentId, Set<Long> hugeMaterialIds) {
        List<AdContentExtendForHugeMaterial> byAdContentId = adContentExtendForHugeMaterialDao.findByAdContentId(adContentId);
        Set<AdContentExtendForHugeMaterial> datelessRecord = byAdContentId.stream()
                .filter(item -> !hugeMaterialIds.contains(item.getId()))
                .collect(Collectors.toSet());
        adContentExtendForHugeMaterialDao.removeAll(datelessRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long submitCopyContent(AdContent adContent, AdContentExtendForDsp adContentExtendForDsp,
                                  AdContentExtendForSdk adContentExtendForSdk, AdContentCategory adContentCategory, List<AdContentExtendForHugeMaterial> adContentExtendForHugeMaterials) throws Exception {
        logger.info("保存adContent");
        adContentDao.saveOrUpdateAdContent(adContent);

        logger.info("保存adContentExtendForDsp");
        adContentExtendForDsp.setAdContentId(adContent.getAdContentId());
        adContentExtendForDspDao.saveOrUpdateAdContentExtendForDsp(adContentExtendForDsp);


        if (CollectionUtils.isNotEmpty(adContentExtendForHugeMaterials)) {
            logger.info("save adContentExtendForHugeMaterials");
            adContentExtendForHugeMaterials.forEach(item -> {
                item.setAdContentId(adContent.getAdContentId());
            });
            adContentExtendForHugeMaterialDao.batchInsertOrUpdate(adContentExtendForHugeMaterials);
        }

        if (adContentExtendForSdk != null) {
            logger.info("保存adContentExtendForSdk");
            adContentExtendForSdk.setAdContentId(adContent.getAdContentId());
            adContentExtendForSdk.setSdkData(replaceCopiedHugeMaterialIdInSdkData(adContentExtendForSdk.getSdkData(), adContentExtendForHugeMaterials));
            checkAndSendContentToDict(adContentExtendForSdk);
            adContentExtendForSdkDao.saveOrUpdateAdContentExtendForSdk(adContentExtendForSdk);
        }

        if (adContentCategory != null) {
            logger.info("保存adcontentcategory");
            adContentCategory.setAdContentId(adContent.getAdContentId());
            adContentCategoryDao.saveOrUpdateAdContentCategory(adContentCategory);
        }

        return adContent.getAdContentId();
    }

    @Override
    @Nullable
    public String replaceCopiedHugeMaterialIdInSdkData(String sdkData, List<AdContentExtendForHugeMaterial> hugeMaterials) {
        if (StringUtils.isBlank(sdkData)) {
            return null;
        }
        Map<Long, AdContentExtendForHugeMaterial> id2Material = hugeMaterials.stream().collect(Collectors.toMap(AdContentExtendForHugeMaterial::getElementId, Function.identity()));
        Map<String, String> sdkDataMap = JSONObject.fromObject(sdkData);
        for (String elementId : sdkDataMap.keySet()) {
            if (id2Material.containsKey(Long.parseLong(elementId))) {
                sdkDataMap.put(elementId, String.valueOf(id2Material.get(Long.parseLong(elementId)).getId()));
            }
        }
        return JSONSerializer.toJSON(sdkDataMap).toString();
    }

    @Override
    public void updateAdContent(AdContent adContent, AdContentExtendForDsp adContentExtendForDsp,
                                AdContentExtendForSdk adContentExtendForSdk, boolean isAdContentModified) throws Exception {
        logger.info("修改变体-保存adcontent");
        adContentDao.updateAdContent(adContent);

        logger.info("修改变体-保存adContentExtendForDsp");
        adContentExtendForDspDao.saveOrUpdateAdContentExtendForDsp(adContentExtendForDsp);
        if (adContentExtendForSdk != null) {
            logger.info("修改变体-保存adContentExtendForSdk");
            checkAndSendContentToDict(adContentExtendForSdk);
            adContentExtendForSdkDao.saveOrUpdateAdContentExtendForSdk(adContentExtendForSdk);
            if (StringUtils.isNotBlank(adContentExtendForSdk.getSdkData()) && isAdContentModified) {
                updateHugeMaterialRelateContentId(adContentExtendForSdk.getSdkData(), adContent.getAdContentId());
            }
        }
        if (isAdContentModified) {
            adContentAssetAuditResultService.deleteByAdContentId(adContent.getAdContentId());
        }
        LogUtil.toDb(Type.CONTENT, adContent.getSponsorId(), OpType.EDIT_AD_CONTENT,
                "edit ad content, adContentId:" + adContent.getAdContentId()
                        + ", adContent:" + GSON.toJson(adContent));

    }

    @Override
    public void checkAndSendContentToDict(AdContentExtendForSdk adContentExtendForSdk) throws Exception {
        // DICT_POST_ID and DICT_UID will storage as JSON.
        if (StringUtils.isBlank(adContentExtendForSdk.getSdkSchemaSatisfied())) {
            // empty schema, do nothing.
            return;
        }
        Set<Long> schemaIds = toLongs(adContentExtendForSdk.getSdkSchemaSatisfied());
        List<SdkSchema> sdkSchemas = sdkSchemaService.listByIds(schemaIds);
        Map<String, String> videoUrlCache = new HashMap<>();
        if (StringUtils.isNotBlank(adContentExtendForSdk.getDictPostId())) {
            // update an exist content
            List<DictVideoPost> dictVideoPosts = new ArrayList<>(sdkSchemas.size());
            for (SdkSchema sdkSchema : sdkSchemas) {
                if (sdkSchema.getDictVideoPost()) {
                    DictVideoPost newDictVideoPost = produceDictVideoPost(adContentExtendForSdk, sdkSchema, videoUrlCache);
                    if (newDictVideoPost.isAnyBlank()) {
                        continue;
                    }
                    DictVideoPost oldDictVideoPost = produceDictVideoPost(adContentExtendForSdkDao.getByAdContentId(adContentExtendForSdk.getAdContentId()), sdkSchema, videoUrlCache);
                    // 这里应PM要求每次保存都推送信息以提高成功率...
                    if (isVirtualUserParamsChanged(newDictVideoPost, oldDictVideoPost) || isKeyParamsChanged(newDictVideoPost, oldDictVideoPost)) {
                        // uid will update.
                        if (isVirtualUserParamsChanged(newDictVideoPost, oldDictVideoPost)) {
                            newDictVideoPost.setUid(StringUtils.EMPTY);
                        }
                        if (isKeyParamsChanged(newDictVideoPost, oldDictVideoPost)) {
                            newDictVideoPost.setDictPostId(StringUtils.EMPTY);
                        }
                        newDictVideoPost.setSchemaId(sdkSchema.getSdkSchemaId());
                        dictVideoPosts.add(newDictVideoPost);
                    } else {
                        oldDictVideoPost.setSchemaId(sdkSchema.getSdkSchemaId());
                        dictVideoPosts.add(oldDictVideoPost);
                    }
                }
            }
            // Update
            setUidAndPostId(dictVideoPosts);
            Map<Long, String> schemaId2Uid = new HashMap<>();
            if (StringUtils.isNotBlank(adContentExtendForSdk.getDictUid())) {
                schemaId2Uid = OBJECT_MAPPER.readValue(adContentExtendForSdk.getDictUid(), LONG_TO_STRING_TYPE_REF);
            }
            Map<Long, String> schemaId2PostId = new HashMap<>();
            if (StringUtils.isNotBlank(adContentExtendForSdk.getDictPostId())) {
                schemaId2PostId = OBJECT_MAPPER.readValue(adContentExtendForSdk.getDictPostId(), LONG_TO_STRING_TYPE_REF);
            }
            for (DictVideoPost dictVideoPost : dictVideoPosts) {
                schemaId2Uid.put(dictVideoPost.getSchemaId(), dictVideoPost.getUid());
                schemaId2PostId.put(dictVideoPost.getSchemaId(), dictVideoPost.getDictPostId());
            }
            adContentExtendForSdk.setDictUid(OBJECT_MAPPER.writeValueAsString(schemaId2Uid));
            adContentExtendForSdk.setDictPostId(OBJECT_MAPPER.writeValueAsString(schemaId2PostId));
            saveDictPostIdHistory(adContentExtendForSdk.getAdContentId(), dictVideoPosts);
        } else {
            // create or copy a content
            if (StringUtils.isNotBlank(adContentExtendForSdk.getSdkSchemaSatisfied())) {
                List<DictVideoPost> dictVideoPosts = new ArrayList<>(sdkSchemas.size());
                for (SdkSchema sdkSchema : sdkSchemas) {
                    if (sdkSchema.getDictVideoPost()) {
                        // build dictVideoPost
                        DictVideoPost dictVideoPost = produceDictVideoPost(adContentExtendForSdk, sdkSchema, videoUrlCache);
                        if (dictVideoPost.isAnyBlank()) {
                            continue;
                        }
                        dictVideoPost.setSchemaId(sdkSchema.getSdkSchemaId());
                        dictVideoPosts.add(dictVideoPost);
                    }
                }
                // schema id
                if (CollectionUtils.isNotEmpty(dictVideoPosts)) {
                    setUidAndPostId(dictVideoPosts);
                    adContentExtendForSdk.setDictPostId(getPostIdJson(dictVideoPosts));
                    adContentExtendForSdk.setDictUid(getUidJson(dictVideoPosts));
                    saveDictPostIdHistory(adContentExtendForSdk.getAdContentId(), dictVideoPosts);
                }
            }
        }
    }

    private void saveDictPostIdHistory(Long adContentId, List<DictVideoPost> dictVideoPosts) {
        List<DictPostIdHistory> dictPostIdHistoryList = new ArrayList<>(dictVideoPosts.size());
        for (DictVideoPost dictVideoPost : dictVideoPosts) {
            dictPostIdHistoryList.add(
                    DictPostIdHistory.builder().adContentId(adContentId)
                            .sdkSchemaId(dictVideoPost.getSchemaId())
                            .dictPostId(dictVideoPost.getDictPostId())
                            .build()
            );
        }
        dictPostIdHistoryDao.batchSave(dictPostIdHistoryList);
    }

    /**
     * 将AdContent对应的schema的 Uid 组装成JSON
     * eg: {schemaId: UID}
     *
     * @param dictVideoPosts
     */
    private String getUidJson(List<DictVideoPost> dictVideoPosts) throws Exception {
        Map<Long, String> schemaId2Uid = new HashMap<>(dictVideoPosts.size());
        for (DictVideoPost dictVideoPost : dictVideoPosts) {
            schemaId2Uid.put(dictVideoPost.getSchemaId(), dictVideoPost.getUid());
        }
        if (MapUtils.isEmpty(schemaId2Uid)) {
            return StringUtils.EMPTY;
        }
        return OBJECT_MAPPER.writeValueAsString(schemaId2Uid);
    }

    /**
     * 将AdContent对应的schema的 Uid 组装成JSON
     * eg: {schemaId: POST}
     *
     * @param dictVideoPosts
     */
    private String getPostIdJson(List<DictVideoPost> dictVideoPosts) throws Exception {
        Map<Long, String> schemaId2PostId = new HashMap<>(dictVideoPosts.size());
        for (DictVideoPost dictVideoPost : dictVideoPosts) {
            schemaId2PostId.put(dictVideoPost.getSchemaId(), dictVideoPost.getDictPostId());
        }
        if (MapUtils.isEmpty(schemaId2PostId)) {
            return StringUtils.EMPTY;
        }
        return OBJECT_MAPPER.writeValueAsString(schemaId2PostId);
    }

    /**
     * @param newProperties 新的变体相关数据
     */
    private void setUidAndPostId(List<DictVideoPost> newProperties) {
        for (DictVideoPost dictVideoPost : newProperties) {
            dictVideoPost.setUid(dictService.insertOrUpdateUser(dictVideoPost.getIconImage(), dictVideoPost.getAppName(), dictVideoPost.getUid()));
            dictVideoPost.setDictPostId(dictService.postContent(dictVideoPost));
        }
    }

    private DictVideoPost produceDictVideoPost(AdContentExtendForSdk adContentExtendForSdk, SdkSchema sdkSchema, Map<String, String> videoUrlCache) throws IOException, ExecutionException, RetryException {
        String adContent = adContentExtendForSdk.getSdkData();
        Map<Long, String> id2ElementContent;
        Map<Long, String> schemaId2DictUid = new HashMap<>();
        Map<Long, String> schemaId2DictPostId = new HashMap<>();
        try {
            id2ElementContent = OBJECT_MAPPER.readValue(adContent, LONG_TO_STRING_TYPE_REF);
            if (StringUtils.isNotBlank(adContentExtendForSdk.getDictUid())) {
                schemaId2DictUid = OBJECT_MAPPER.readValue(adContentExtendForSdk.getDictUid(), LONG_TO_STRING_TYPE_REF);
            }
            if (StringUtils.isNotBlank(adContentExtendForSdk.getDictPostId())) {
                schemaId2DictPostId = OBJECT_MAPPER.readValue(adContentExtendForSdk.getDictPostId(), LONG_TO_STRING_TYPE_REF);
            }
        } catch (Exception e) {
            logger.error("deserialize adContentExtendForSdk.sdk_data: {} FAILED!!!", adContent, e);
            throw new BadRequestException("生产词典帖子失败...");
        }
        Map<Long, SdkElement> id2SdkElement = sdkElementService.getElementBySchemaId(sdkSchema.getSdkSchemaId())
                .stream()
                .collect(Collectors.toMap(SdkElement::getSdkElementId, Function.identity()));

        Map<String, List<SdkElement>> key2SdkElements = sdkElementDao.getListByIds(new ArrayList<>(id2ElementContent.keySet()))
                .stream()
                .collect(Collectors.groupingBy(SdkElement::getSdkElementKey));

        String videoUrlWithSize;
        try {
            videoUrlWithSize = spliceVideoSize(matchElementByType(id2SdkElement, Constants.SDK_ELEMENT_KEY_VIDEO_URL, key2SdkElements, id2ElementContent), videoUrlCache);
        } catch (Exception e) {
            logger.error("split video size ERROR! {}", e.getMessage());
            throw new BadRequestException("视频无法解析，元素信息未能成功保存，请检查视频链接是否正确。");
        }
        // some params maybe get null.
        return DictVideoPost.builder()
                .dictPostId(schemaId2DictPostId.get(sdkSchema.getSdkSchemaId()))
                .appName(matchElementByType(id2SdkElement, Constants.SDK_ELEMENT_KEY_APP_NAME, key2SdkElements, id2ElementContent))
                .video(videoUrlWithSize)
                .text(matchElementByType(id2SdkElement, Constants.SDK_ELEMENT_KEY_TEXT, key2SdkElements, id2ElementContent))
                .title(matchElementByType(id2SdkElement, Constants.SDK_ELEMENT_KEY_TITLE, key2SdkElements, id2ElementContent))
                .iconImage(matchElementByType(id2SdkElement, Constants.SDK_ELEMENT_KEY_ICON_IMAGE, key2SdkElements, id2ElementContent))
                .coverImage(getCoverImage(sdkSchema, id2ElementContent, id2SdkElement, key2SdkElements))
                .uid(schemaId2DictUid.get(sdkSchema.getSdkSchemaId()))
                .youthVisible(false)
                .build();
    }

    private String getCoverImage(SdkSchema sdkSchema,
                                 Map<Long, String> id2ElementContent,
                                 Map<Long, SdkElement> id2SdkElement,
                                 Map<String, List<SdkElement>> key2SdkElements) throws IOException {
        List<SdkElement> sdkElements = key2SdkElements.get(SDK_ELEMENT_KEY_COVER_IMAGE);
        if (CollectionUtils.isEmpty(sdkElements)) {
            return StringUtils.EMPTY;
        }
        Set<Long> coverImageId = sdkElements.stream().map(SdkElement::getSdkElementId).collect(Collectors.toSet());
        Map<Long, Map<String, Integer>> m = OBJECT_MAPPER.readValue(sdkSchema.getSdkElements(), new TypeReference<Map<Long, Map<String, Integer>>>() {
        });
        Integer coverImageWidth = null;
        Integer coverImageHeight = null;
        Long realElementId = null;
        for (Long elementId : m.keySet()) {
            if (coverImageId.contains(elementId)) {
                Map<String, Integer> stringIntegerMap = m.get(elementId);
                coverImageWidth = stringIntegerMap.get("width");
                coverImageHeight = stringIntegerMap.get("height");
                realElementId = elementId;
                return imageMappingService.getImageURLWithSize(
                        matchElementByType(id2SdkElement, SDK_ELEMENT_KEY_COVER_IMAGE, key2SdkElements, id2ElementContent),
                        coverImageWidth,
                        coverImageHeight,
                        id2SdkElement.get(realElementId),
                        sdkSchema.getMappingMethod());
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * @param url
     * @param videoUrlCache 视频链接缓存
     * @return
     * @throws ExecutionException
     * @throws RetryException
     */
    private String spliceVideoSize(String url, Map<String, String> videoUrlCache) throws ExecutionException, RetryException {
        if (StringUtils.isBlank(url)) {
            return url;
        } else if (videoUrlCache.containsKey(url)) {
            return videoUrlCache.get(url);
        } else {
            Retryer<String> retryer = RetryerBuilder.<String>newBuilder()
                    .retryIfException()
                    .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                    .withRetryListener(new RetryListener() {
                        @Override
                        public <V> void onRetry(Attempt<V> attempt) {
                            if (attempt.hasException()) {
                                logger.error("retry to splice video size,video url is {},attempt number is {}", url, attempt.getAttemptNumber(), attempt.getExceptionCause());
                            }
                        }
                    }).build();
            return retryer.call(() -> {
                final Path tempDir = Files.createDirectories(Paths.get("./temp"));
                final Path tmpFile = Files.createTempFile(tempDir, "video", ".tmp");

                FileUtils.copyURLToFile(new URL(url), tmpFile.toFile());
                Movie movie = MovieCreator.build(tmpFile.toFile().getPath());
                int width = 0, height = 0;
                for (Track track : movie.getTracks()) {
                    if ("vide".equals(track.getHandler())) {
                        width = new Double(track.getTrackMetaData().getWidth()).intValue();
                        height = new Double(track.getTrackMetaData().getHeight()).intValue();
                        break;
                    }
                }
                Files.deleteIfExists(tmpFile);
                String res;
                if (url.contains("?")) {
                    res = url + String.format("&_w=%d&_h=%d", width, height);
                } else {
                    res = url + String.format("?_w=%d&_h=%d", width, height);
                }
                videoUrlCache.put(url, res);
                return res;
            });
        }
    }

    private String matchElementByType(Map<Long, SdkElement> id2SdkElement,
                                      String elementKey,
                                      Map<String, List<SdkElement>> key2SdkElements,
                                      Map<Long, String> id2ElementContent) {
        if (key2SdkElements.containsKey(elementKey)) {
            for (SdkElement sdkElement : key2SdkElements.get(elementKey)) {
                if (id2SdkElement.containsKey(sdkElement.getSdkElementId())) {
                    return id2ElementContent.get(sdkElement.getSdkElementId());
                }
            }
        }
        logger.info("match element get null result!");
        return StringUtils.EMPTY;
    }

    /**
     * 检查关键的元素是否存在变化
     * 关键元素：
     * 广告视频标题 title
     * 广告视频描述 text
     * <p>
     * 广告视频封面图 coverImage
     * 广告视频内容 videoUrl
     */
    private Boolean isKeyParamsChanged(DictVideoPost newDictVideoPost, DictVideoPost oldDictVideoPost) {
        // any not equal return true.
        return !(StringUtils.equals(newDictVideoPost.getTitle(), oldDictVideoPost.getTitle()) &&
                StringUtils.equals(newDictVideoPost.getText(), oldDictVideoPost.getText()) &&
                StringUtils.equals(newDictVideoPost.getCoverImage(), oldDictVideoPost.getCoverImage()) &&
                StringUtils.equals(newDictVideoPost.getVideo(), oldDictVideoPost.getVideo()));
    }

    /**
     * 检查词典虚拟用户的属性是否发生变化
     * 广告主头像 iconImage
     * 广告主名称 appName
     */
    private Boolean isVirtualUserParamsChanged(DictVideoPost newDictVideoPost, DictVideoPost oldDictVideoPost) {
        return !(StringUtils.equals(newDictVideoPost.getIconImage(), oldDictVideoPost.getIconImage()) &&
                StringUtils.equals(newDictVideoPost.getAppName(), oldDictVideoPost.getAppName()));
    }

    @Override
    public void validateContentGroupCampaign(long adContentId) throws Exception {
        AdContent adContent = adContentDao.getAdContent(adContentId);
        if (adContent == null) {
            throw new NotFoundException("变体不存在");
        }
        adGroupService.validateGroupCampaign(adContent.getAdGroupId());
    }

    @Override
    public List<AdContent> getAdContentByName(long adGroupId, String name) {
        if (StringUtils.isEmpty(name)) {
            return new ArrayList<>();
        }
        return adContentDao.getAdContentByName(adGroupId, name);
    }

    @Override
    public void updateContentDestLinkUnderGroup(long adGroupId, String destLink) {
        List<AdContent> adContentList = adContentDao.getAdContentListByAdGroupId(adGroupId);
        if (CollectionUtils.isEmpty(adContentList)) {
            return;
        }
        logger.info("update {} ad content's destination link of group {} to {}.", adContentList.size(), adGroupId, destLink);

        for (AdContent adContent : adContentList) {
            adContent.setDestLink(destLink);
            adContent.setStatus(SegmentStatusUtil.getAuditSegStatus(adContent.getStatus(),
                    StatusCode.AUDIT_UNCHECK));
            adContentAssetAuditResultService.deleteByAdContentId(adContent.getAdContentId());
            adContentDao.saveOrUpdateAdContent(adContent);
            logger.info("update ad content {} destination link to {}.", adContent.getAdContentId(), destLink);
        }
    }

    @Override
    public boolean checkLandingPageAndPlatform(long adContentId, long adCampaignId)
            throws Exception {
        AdContent adContent = adContentDao.getAdContent(adContentId);
        if (adContent == null) {
            return false;
        }
        AdGroup adGroup = adGroupDao.getAdGroup(adContent.getAdGroupId());
        if (adGroup == null) {
            return false;
        }
        AdCampaign adCampaign = adCampaignDao.getAdCampaign(adGroup.getAdCampaignId());
        AdCampaignExtendForDsp adCampaignExtendForDsp =
                adCampaignExtendForDspDao.getByCampaignId(adCampaign.getAdCampaignId());
        if (adCampaignExtendForDsp == null) {
            return false;
        }

        Integer landingPageType = adCampaign.getLandingPageType();
        Integer platform = adCampaignExtendForDsp.getAdPlatform();

        AdCampaign adCampaign1 = adCampaignDao.getAdCampaign(adCampaignId);
        AdCampaignExtendForDsp adCampaignExtendForDsp1 =
                adCampaignExtendForDspDao.getByCampaignId(adCampaignId);
        if (adCampaign1 == null || adCampaignExtendForDsp1 == null) {
            return false;
        }

        if (landingPageType != null && landingPageType.equals(adCampaign1.getLandingPageType())
                && platform != null && platform == adCampaignExtendForDsp1.getAdPlatform()) {

            return true;
        }

        return false;
    }

    @Override
    public boolean checkAdGroupBelongsToAdCampaign(long adGroupId, long adCampaignId) throws Exception {
        AdGroup adGroup = adGroupDao.getAdGroup(adGroupId);
        return adGroup != null && adGroup.getAdCampaignId() == adCampaignId;
    }

    @Override
    public Map<String, List> getAdContentsStatus(Set<Long> ids) {
        Map<String, List> contentStatus = new HashMap<>();
        contentStatus.put(AdContentParams.AD_CONTENT_ID, new ArrayList());
        contentStatus.put(NameCode.SWITCH, new ArrayList());
        contentStatus.put(NameCode.STATUS, new ArrayList());

        List<AdContent> adContentList = adContentDao.getAdContentListByContentIds(ids);

        Set<Long> adGroupIds = new HashSet<>();
        for (AdContent adContent : adContentList) {
            adGroupIds.add(adContent.getAdGroupId());
        }
        List<AdGroup> adGroupList = adGroupDao.getAdGroupListByIds(adGroupIds);
        Map<Long, Integer> adGroupStatusMap = new HashMap<>();
        for (AdGroup adGroup : adGroupList) {
            adGroupStatusMap.put(adGroup.getAdGroupId(), adGroup.getStatus());
        }

        for (AdContent adContent : adContentList) {
            contentStatus.get(AdContentParams.AD_CONTENT_ID).add(adContent.getAdContentId());
            int adGroupStatus = adGroupStatusMap.get(adContent.getAdGroupId());
            contentStatus.get(NameCode.SWITCH).add(StatusUtil.getAdContentSwitchStatus(adGroupStatus, adContent.getStatus()));
            contentStatus.get(NameCode.STATUS).add(StatusUtil.getAdContentStatusName(adGroupStatus, adContent.getStatus()));
        }

        return contentStatus;
    }

    @Override
    public String getAdContentDestLink(Long adContentId) {
        AdContent adContent = adContentDao.getAdContent(adContentId);
        if (adContent == null) {
            return "";
        }
        return adContent.getDestLink();
    }

    /**
     * 参考：{@link AdContentService#batchUpdateSdkElement(List, long, String)}
     */
    @Override
    public void batchUpdateSdkElement(List<AdContent> adContentList, long elementId, String value) throws Exception {
        for (AdContent adContent : adContentList) {
            updateSdkElement(adContent, elementId, value);
        }
    }

    /**
     * @see AdContentService#updateAdContentSdk(AdGroup adGroup, boolean addSdkSlot)
     */
    @Override
    public void updateAdContentSdk(AdGroup adGroup, boolean needAudit) throws Exception {
        if (adGroup == null) {
            return;
        }
        List<AdContent> adContentList = adContentDao.getAdContentListByAdGroupId(adGroup.getAdGroupId());
        if (CollectionUtils.isEmpty(adContentList)) {
            return;
        }

        List<AdContentExtendForSdk> sdks = new ArrayList<>();
        for (AdContent adContent : adContentList) {
            AdContentExtendForSdk sdk = updateAdContentSdk(adContent, needAudit);
            if (sdk != null) {
                sdks.add(sdk);
            }
        }
        adContentExtendForSdkDao.batchUpdateOrInsert(sdks);
    }

    private List<AdContentExtendForSdk> getNewAdContentSdks(List<AdContent> adContents) {
        List<Long> adContentIds = adContents.stream().map(AdContent::getAdContentId).collect(Collectors.toList());
        List<AdContentExtendForSdk> adContentExtendForSdks = adContentExtendForSdkDao.getByAdContentIds(adContentIds);
        Map<Long, AdContentExtendForSdk> idSdk = adContentExtendForSdks.stream().collect(Collectors.toMap(AdContentExtendForSdk::getAdContentId, x -> x));
        List<AdContentExtendForSdk> newAdContentExtendForSdks = new ArrayList<>();
        for (AdContent adContent : adContents) {
            try {
                String newSdkData = getNewSdkData(adContent);
                AdContentArgument adContentArgument =
                        AdContentArgument.builder().adContentName(adContent.getTitle()).sdkData(newSdkData).build();
                AdContentValidator adContentValidator = ValidatorFactory.getAdContentValidator(adContentArgument);
                adContentValidator.validateModified(newHashMap(Pair.of(AdGroupParams.AD_GROUP_ID, adContent.getAdGroupId()),
                        Pair.of(AdContentParams.AD_CONTENT_ID, adContent.getAdContentId()),
                        Pair.of(AdContentParams.IS_NEED_CHECK_NAME_EXIST, AdContentParams.DO_NOT_CHECK_NAME_EXIST)));
                AppAdContentExtracter adContentExtracter = ExtractorFactory
                        .getAdContentExtracter(adContentValidator, adContent.getAdGroupId());
                if (idSdk.containsKey(adContent.getAdContentId())) {
                    newAdContentExtendForSdks.add(adContentExtracter.extractAdContentSdkForModifiedSdkSlot(idSdk.get(adContent.getAdContentId())));
                } else {
                    logger.warn("ad content: {} can't found relate extendForSdk", adContent.getAdContentId());
                }
            } catch (Exception e) {
                logger.error("error getAdContentExtendForSdk {}", adContent.getAdContentId(), e);
            }

        }
        return newAdContentExtendForSdks;
    }

    /**
     * 更新创意广告位，组更新广告位时，更新创意的审核状态
     *
     * @param adContent
     */
    private AdContentExtendForSdk updateAdContentSdk(AdContent adContent, boolean needAudit) throws Exception {
        AdContentExtendForSdk adContentExtendForSdk =
                adContentExtendForSdkDao.getByAdContentId(adContent.getAdContentId());
        String newSdkData = getNewSdkData(adContent);
        AdContentArgument adContentArgument =
                AdContentArgument.builder().adContentName(adContent.getTitle()).sdkData(newSdkData).build();
        AdContentValidator adContentValidator = ValidatorFactory.getAdContentValidator(adContentArgument);
        adContentValidator.validateModified(newHashMap(Pair.of(AdGroupParams.AD_GROUP_ID, adContent.getAdGroupId()),
                Pair.of(AdContentParams.AD_CONTENT_ID, adContent.getAdContentId())));
        AppAdContentExtracter adContentExtracter = ExtractorFactory
                .getAdContentExtracter(adContentValidator, adContent.getAdGroupId());
        adContentExtendForSdk = adContentExtracter.extractAdContentSdkForModifiedSdkSlot(adContentExtendForSdk);
        if (needAudit) {
            adContent.setStatus(SegmentStatusUtil.getAuditSegStatus(adContent.getStatus(),
                    StatusCode.AUDIT_UNCHECK));
            adContentDao.saveOrUpdateAdContent(adContent);
            adContentAssetAuditResultService.deleteByAdContentId(adContent.getAdContentId());
        }
        logger.info("更新组广告位覆盖创意-保存adContentExtendForSdk");
        return adContentExtendForSdk;
    }

    private String getNewSdkData(AdContent adContent) throws Exception {
        List<SimpleElemTypeList> contentElems = sdkElementService.getDeliverySdkElement(Type.CONTENT, adContent.getAdContentId(), 0);
        return toSdkDataStr(contentElems);
    }

    @Override
    public String getCopiedSdkData(Long copiedAdGroupId, AdContentExtendForSdk originalAdContentExtendForSdk) throws Exception {
        List<SimpleElemTypeList> contentElems = sdkElementService.getCopiedDeliverySdkElement(copiedAdGroupId, originalAdContentExtendForSdk);
        return toSdkDataStr(contentElems);
    }

    @Override
    public void batchUpdateAdContentSdk(List<Long> adGroupIds, boolean needAudit) {
        if (CollectionUtils.isEmpty(adGroupIds)) {
            return;
        }
        List<AdContent> adContentList = adContentDao.getAdContentListByAdGroupIds(adGroupIds);
        if (CollectionUtils.isEmpty(adContentList)) {
            return;
        }
        List<AdContentExtendForSdk> sdks = getNewAdContentSdks(adContentList);
        adContentExtendForSdkDao.batchUpdateOrInsert(sdks);
    }

    private String toSdkDataStr(List<SimpleElemTypeList> contentElems) {
        List<String> sdkDataList = new ArrayList<>();
        for (SimpleElemTypeList elemTypeList : contentElems) {
            for (SimpleElem elem : elemTypeList.getList()) {
                if (StringUtils.isNotBlank(elem.getData())) {
                    // 对data编码是为了后边复用AppAdContentValidator.getSdkDataMap
                    String item = elem.getId() + ":" + URLEncoder.encode(elem.getData());
                    // java中的编码会将空格编码成+,通不过sdkData的校验，因而将空格编码成%20
                    item = StringUtils.replacePattern(item, "\\+", "%20");
                    sdkDataList.add(item);
                }
            }
        }
        if (CollectionUtils.isEmpty(sdkDataList)) {
            return null;
        }
        return StringUtils.join(sdkDataList, ",");
    }

    /**
     * 修改创意的单一元素
     *
     * @param adContent 创意
     * @param elementId 元素id
     * @param value     元素值
     * @throws Exception AdContentValidator.validateModified()方法抛出异常
     */
    private void updateSdkElement(AdContent adContent, long elementId, String value) throws Exception {
        AdContentExtendForSdk adContentExtendForSdk =
                adContentExtendForSdkDao.getByAdContentId(adContent.getAdContentId());
        String elementIdStr = String.valueOf(elementId);
        if (Objects.isNull(adContentExtendForSdk)) {
            return;
        }
        String oldSdkData = adContentExtendForSdk.getSdkData();
        if (oldSdkData == null && value == null) {
            return;
        }
        String newSdkData = createNewSdkData(oldSdkData, elementIdStr, value);

        AdContentArgument adContentArgument =
                AdContentArgument.builder().adContentName(adContent.getTitle()).sdkData(newSdkData).build();
        AdContentValidator adContentValidator = ValidatorFactory.getAdContentValidator(adContentArgument);
        adContentValidator.validateModified(newHashMap(Pair.of(AdGroupParams.AD_GROUP_ID, adContent.getAdGroupId()),
                Pair.of(AdContentParams.AD_CONTENT_ID, adContent.getAdContentId())));
        AppAdContentExtracter adContentExtracter = ExtractorFactory
                .getAdContentExtracter(adContentValidator, adContent.getAdGroupId());

        adContentExtendForSdk = adContentExtracter.extractModifiedAdContentSdk(adContentExtendForSdk, adContent.getAdContentId());
        if (adContentExtracter.isAdContentModified()) {
            adContent.setStatus(SegmentStatusUtil.getAuditSegStatus(adContent.getStatus(),
                    StatusCode.AUDIT_UNCHECK));
            adContentDao.saveOrUpdateAdContent(adContent);
            AdGroup adGroup = adGroupDao.getAdGroup(adContent.getAdGroupId());
            // 修改组edit_status
            adGroup.setEditStatus(StatusCode.AD_GROUP_AUDIT_NOT_FINISH);
            adGroupDao.saveOrUpdateAdGroup(adGroup);
            adContentAssetAuditResultService.deleteByAdContentId(adContent.getAdContentId());
        }
        if (adContentExtendForSdk != null) {
            logger.info("批量修改单一元素-保存adContentExtendForSdk");
            adContentExtendForSdkDao.saveOrUpdateAdContentExtendForSdk(adContentExtendForSdk);
            LogUtil.toDb(Type.CONTENT, adContent.getSponsorId(), OpType.UPDATE_SDK_ELEMENT,
                    "update ad group id:" + adContent.getAdGroupId()
                            + ", elementId:" + elementId + ", value: " + value);
        }
    }

    /**
     * 如果传入elementIdStr和value不为null，则在原有的SDKData中新增或者替换或者删除element
     * 如果传入elementIdStr和value为null,则构造新的URL编码后的sdkData
     *
     * @param oldSdkData
     * @param elementIdStr
     * @param value
     * @return
     * @throws Exception
     */
    private String createNewSdkData(String oldSdkData, String elementIdStr, String value) throws Exception {
        String newSdkData;
        if (oldSdkData == null) {
            if (StringUtil.isNull(elementIdStr) && StringUtil.isNull(value)) {
                return null;
            }
            newSdkData = new StringBuilder(elementIdStr).append(":").append(value).toString();
        } else {
            JSONObject sdkDataJson = JSONObject.fromObject(oldSdkData);
            Iterator iterator = sdkDataJson.keySet().iterator();
            while (iterator.hasNext()) {
                String k = (String) iterator.next();
                String v = sdkDataJson.getString(k);
                /**
                 * encode方法会将原来字符串中的空格编码成 "+" 号，
                 * 在验证参数格式 {@link outfox.ead.noah.controller.validator.AppAdContentValidator#sdkData} 的时候不符合格式
                 * 所以需要将 "+" 替换成 "%20"
                 */
                v = URLEncoder.encode(v, "UTF-8").replaceAll("\\+", "%20");
                sdkDataJson.put(k, v);
            }

            if (StringUtils.isNotBlank(elementIdStr)) {
                if (StringUtil.isNull(value)) {
                    // value等于null，直接remove
                    sdkDataJson.remove(elementIdStr);
                } else {
                    // value不等于null，新增或者替换
                    sdkDataJson.put(elementIdStr, value);
                }
            }
            newSdkData = sdkDataJson.toString();
            newSdkData = newSdkData.substring(1, newSdkData.length() - 1).replaceAll("\"", "");
        }
        return "".equals(newSdkData) ? null : newSdkData;
    }

    @Override
    public void submitMaterialLibrary(Long sponsorId, Map<Long, String> sdkDataMap) {

        Set<String> titles = new HashSet<>();
        if (sdkDataMap.containsKey(NameCode.TITLE_NORMAL)) {
            titles.add(removeWordPackageDefaultWord(sdkDataMap.get(NameCode.TITLE_NORMAL)));
        }
        if (sdkDataMap.containsKey(NameCode.TITLE_LONG)) {
            titles.add(removeWordPackageDefaultWord(sdkDataMap.get(NameCode.TITLE_LONG)));
        }
        if (sdkDataMap.containsKey(NameCode.TITLE_SHORT)) {
            titles.add(removeWordPackageDefaultWord(sdkDataMap.get(NameCode.TITLE_SHORT)));
        }
        Set<String> mimeIds = MaterialLibraryValidator.validateImageUrls(new ArrayList<>(sdkDataMap.values()), sponsorId);
        if (!CollectionUtils.isEmpty(titles)) {
            logger.info("保存标题至素材库...");
            materialLibraryService.batchInsertOrUpdateTitles(sponsorId, titles);
        }
        if (!CollectionUtils.isEmpty(mimeIds)) {
            logger.info("保存图片至至素材库...");
            materialLibraryService.saveImage(sponsorId, new ArrayList<>(mimeIds), ImageLibraryParams.NO_GROUP_ID);
        }
    }

    private String removeWordPackageDefaultWord(String title) {
        if (title.indexOf('|') >= 0 && title.indexOf('}') >= 0) {
            return StringUtils.replace(title, title.substring(title.indexOf('|'), title.indexOf('}')), "");
        }
        return title;
    }

    public String uploadVideoMaterial(long elementId, Long sponsorId, MultipartFile videoFile) throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("写入磁盘时间");
        String extension = FilenameUtils.getExtension(videoFile.getOriginalFilename());
        if (!LANDING_PAGE_VIDEO_FORMAT.equalsIgnoreCase(extension)) {
            throw new BadRequestException("不支持的视频格式！");
        }
        long size = videoFile.getSize();
        if (MAX_LANDING_PAGE_VIDEO_SIZE < size) {
            throw new BadRequestException(String.format("视频大小超出限制！当前文件：%s，最大：%s", FileUtils.byteCountToDisplaySize(size), FileUtils.byteCountToDisplaySize(MAX_LANDING_PAGE_VIDEO_SIZE)));
        }
        byte[] bytes = videoFile.getBytes();
        String videoMd5 = MD5.byteArrayToHexString(bytes);
        String sponsorIdMd5 = MD5.GetMD5Code(String.valueOf(sponsorId));
        String s = videoMd5 + sponsorIdMd5;
        int md5Hash = s.hashCode();
        String fileName = md5Hash + "." + extension;
        // 将视频流写入磁盘
        File file = VideoUtil.writeVideoFile(cdnConfig.getVideoMaterialCdnPath(), fileName, bytes);
        stopWatch.stop();
        stopWatch.start("解析时间");
        // 解析视频，获取视频宽高，长度等信息
        VideoInfo videoInfo = VideoUtil.parseVideoInfo(file.getPath());
        // 校验视频宽高和长度
        checkVideoElement(elementId, videoInfo);
        stopWatch.stop();
        logger.info("filename: {}\n , {}", fileName, stopWatch.prettyPrint());
        return cdnConfig.getVideoMaterialUrlPrefix() + fileName;
    }

    @Override
    public void checkVideoUrl(long elementId, String videoUrl) {
        SdkElement element = sdkElementDao.getElementById(elementId);
        // 当视频元素存在对视频尺寸的限制时，再做检查
        if (element.getSdkElementWidth() > 0 && element.getSdkElementHeight() > 0) {
            checkVideoElement(elementId, VideoUtil.parseVideoInfoFromUrl(videoUrl));
        }
    }

    @Override
    public Long uploadPlainTextFile(Long sponsorId, Long elementId, String filename, String content) {
        AdContentExtendForHugeMaterial adContentExtendForHugeMaterial = AdContentExtendForHugeMaterial.builder()
                .elementId(elementId)
                .fileName(filename)
                .materialContent(content)
                .sponsorId(sponsorId).build();
        adContentExtendForHugeMaterialDao.saveOrUpdate(adContentExtendForHugeMaterial);
        return adContentExtendForHugeMaterial.getId();
    }

    private void checkVideoElement(long elementId, VideoInfo videoInfo) {
        SdkElement element = sdkElementDao.getElementById(elementId);
        int elementWidth = element.getSdkElementWidth();
        int elementHeight = element.getSdkElementHeight();
        long elementDuration = element.getDuration();
        String rule = minScale(elementWidth, elementHeight);
        if (elementWidth > 0 && elementHeight > 0) {
            String ratio = minScale(videoInfo.getWidth(), videoInfo.getHeight());
            if (!rule.equalsIgnoreCase(ratio)) {
                throw new BadRequestException(String.format("视频尺寸有误，需求的宽高为：%s:%s，当前视频宽高：%s:%s", elementWidth, elementHeight, videoInfo.getWidth(), videoInfo.getHeight()));
            }
        }
        if (elementDuration > 0 && videoInfo.getDuration() > elementDuration) {
            throw new BadRequestException(String.format("视频长度超过限制，支持的最大长度为：%s s，当前长度：%s s", elementDuration, videoInfo.getDuration()));
        }
    }
}
