package outfox.ead.noah.service.impl;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.dao.AdContentTrackerDao;
import outfox.ead.noah.entity.AdContentTracker;
import outfox.ead.noah.service.AdContentTrackerService;
import outfox.ead.noah.util.logger.LogUtil;
import outfox.ead.noah.util.logger.OpType;
import outfox.ead.noah.util.params.AdContentParams;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * created by <PERSON><PERSON><PERSON>feng on 18/5/9
 */
@Service
public class AdContentTrackerServiceImpl implements AdContentTrackerService {

    @Autowired
    AdContentTrackerDao adContentTrackerDao;
    @Override
    public void saveOrUpdateAdContentTracker(Long sponsorId, Long adContentId,String link,int type) {
       adContentTrackerDao.saveOrUpdateAdContentTracker(adContentId,link,type);
        LogUtil.toDb(Type.CONTENT, sponsorId, OpType.CHANGE_THIRD_PARTY_DESTLINK,
                "change third party destLink, adContentId:" + adContentId + ", link:" + link);

    }
    @Override
    public Map<String, String> getAdContentTracker(Long adContentId) {
        Map<String, String> monitors = new HashMap<>();
        AdContentTracker exposureMonitor = adContentTrackerDao.getAdContentTracker(adContentId, Constants.EXPOSURE_MONITOR);
        String exposureMonitorLink = exposureMonitor == null ? "" : exposureMonitor.getThirdPartyLink();
        monitors.put(AdContentParams.EXPOSURE_MONITOR,exposureMonitorLink);
        AdContentTracker clickMonitor = adContentTrackerDao.getAdContentTracker(adContentId, Constants.CLICK_MONITOR);
        String clickMonitorLink = clickMonitor == null ? "" : clickMonitor.getThirdPartyLink();
        monitors.put(AdContentParams.CLICK_MONITOR,clickMonitorLink);
        return monitors;
    }

    @Override
    public String getAdContentTracker(Long adContentId, int type) {
        AdContentTracker monitor = adContentTrackerDao.getAdContentTracker(adContentId, type);
        String monitorLink = monitor == null ? "" : monitor.getThirdPartyLink();
        return monitorLink;
    }

    @Override
    public void deleteAdContentTracker(Long adContentId, int type) {
         adContentTrackerDao.deleteAdContentTracker(adContentId,type);
    }

    @Override
    public void copyAdContentTracker(Long originalAdContentId, Long copyAdContentId) {
        List<AdContentTracker> adContentTrackers = adContentTrackerDao.getAllAdContentTracker(originalAdContentId);
        if (CollectionUtils.isNotEmpty(adContentTrackers)) {
            adContentTrackers.forEach(adContentTracker -> adContentTrackerDao.saveOrUpdateAdContentTracker(copyAdContentId, adContentTracker.getThirdPartyLink(), adContentTracker.getThirdPartyLinkType()));
        }
    }
}
