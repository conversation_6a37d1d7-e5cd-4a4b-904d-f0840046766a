package outfox.ead.noah.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.dao.SdkDeveloperDao;
import outfox.ead.noah.entity.SdkDeveloper;
import outfox.ead.noah.service.SdkDeveloperService;
import outfox.ead.noah.util.datautil.DataUtil;
import outfox.ead.noah.util.mail.HTMLMailSender;

import java.util.List;

//import static outfox.ead.noah.constants.Constants.DEVSTATUS_UNACTIVE;
//import static outfox.ead.noah.util.datautil.DataUtil.gennerateValidateString;
//import static SdkDeveloper.PROPERTYNAME_USER_NAME;

/**
 * SdkDeveloperService Implementation
 * Created by huanghuan on 16/9/21.
 */
@Service
public class SdkDeveloperServiceImpl implements SdkDeveloperService {

    @Autowired
    private SdkDeveloperDao sdkDeveloperDao;

    private static final Logger logger = LoggerFactory.getLogger(SdkDeveloperServiceImpl.class);

    @Override
    public boolean activateSdkDeveloper(String userName, String validateCode) throws Exception {
        SdkDeveloper sdkDeveloper = getSdkDeveloperByUserName(userName);
        if (sdkDeveloper != null) {
            String correctValidateCode = DataUtil.gennerateValidateString(sdkDeveloper);
            if (validateCode.trim().equals(correctValidateCode)) {
                sdkDeveloper.setStatus(Constants.DEVSTATUS_ACTIVE);
                saveOrUpdateSdkDeveloper(sdkDeveloper);
                return true;
            }
        }
        return false;
    }

    @Override
    public void saveOrUpdateSdkDeveloper(SdkDeveloper sdkDeveloper) throws Exception {
        if (sdkDeveloper == null) {
            return;
        }
        sdkDeveloperDao.saveOrUpdateSdkDeveloper(sdkDeveloper);
    }

    @Override
    public void registerSdkDeveloper(SdkDeveloper sdkDeveloper) throws Exception {
        if (sdkDeveloper == null) {
            return;
        }
        /*********** 注册用户，初始为未激活状态 ********/
        sdkDeveloper.setStatus(Constants.DEVSTATUS_UNACTIVE);
        saveOrUpdateSdkDeveloper(sdkDeveloper);
        /************** 发送激活邮件 ***************/
        sendActivateEmail(sdkDeveloper);
    }

    private void sendActivateEmail(SdkDeveloper sdkDeveloper) {
        if (sdkDeveloper == null) {
            return;
        }
        try {
            HTMLMailSender.sendMail(sdkDeveloper.getUserName(),
                    HTMLMailSender.DEFAULT_FROM_USER, "有道开发者注册成功!",
                    HTMLMailSender.getContent(sdkDeveloper));
        } catch (Exception e) {
            logger.error("send mail to developer:" + sdkDeveloper.getUserName() + " error.", e);
        }

    }

    @Override
    public SdkDeveloper getSdkDeveloperByUserName(String userName) throws Exception {
        List<SdkDeveloper> developers;
        developers = sdkDeveloperDao.getByAdContentByOneColumn("userName", userName);
        if (developers != null && developers.size() > 0) {
            return developers.get(0);
        }
        return null;
    }
}
