package outfox.ead.noah.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.dao.AdContentApkInfoDao;
import outfox.ead.noah.exception.ServerException;
import outfox.ead.noah.service.ApkInfoService;

/**
 * Created by wangmo on 2017/11/24.
 */
@Service
public class ApkInfoServiceImpl implements ApkInfoService {

    @Autowired
    private AdContentApkInfoDao adContentApkInfoDao;

    @Override
    public String getApkNameByUrl(String url) {
        try {
            return adContentApkInfoDao.getApkNameByUrl(url);
        } catch (Exception e) {
            throw new ServerException("查询应用名称失败");
        }
    }
}
