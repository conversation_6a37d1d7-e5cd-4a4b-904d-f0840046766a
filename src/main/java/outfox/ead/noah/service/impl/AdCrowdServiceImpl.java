package outfox.ead.noah.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.noah.dao.AdCrowdDao;
import outfox.ead.noah.entity.AdCrowd;
import outfox.ead.noah.service.AdCrowdService;

import java.util.List;

/**
 * Created by huanghuan on 16/5/4.
 */
@Service
public class AdCrowdServiceImpl implements AdCrowdService {

    @Autowired
    private AdCrowdDao adCrowdDao;

    @Override
    @Transactional
    public void addAdCrowd(AdCrowd adCrowd) {
        adCrowdDao.saveOrUpdateAdCrowd(adCrowd);
    }

    @Override
    @Transactional
    public void updateCrowd(AdCrowd adCrowd) {
        adCrowdDao.updateCrowd(adCrowd);
    }

    @Override
    public List<AdCrowd> getAdCrowdsBySponsorId(long sponsorId) {
        return adCrowdDao.getCrowdsBySponsorId(sponsorId);
    }
}
