package outfox.ead.noah.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.constants.SdkElementTypeConstants;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.dao.*;
import outfox.ead.noah.entity.*;
import outfox.ead.noah.entity.models.simple.*;
import outfox.ead.noah.service.*;
import outfox.ead.noah.util.ExceptionUtil;
import outfox.ead.noah.util.code.NameCode;
import outfox.ead.noah.util.code.TypeCode;
import outfox.ead.noah.util.datautil.DataUtil;
import outfox.ead.noah.util.logger.LogUtil;

import java.util.*;
import java.util.stream.Collectors;

import static java.lang.String.valueOf;

/**
 * Created by zhaoteng on 2017/6/8.
 */
@Slf4j
@Service
public class SdkElementServiceImpl implements SdkElementService {

    @Autowired
    private SdkSchemaDao sdkSchemaDao;

    @Autowired
    private SdkElementDao sdkElementDao;

    @Autowired
    private AdContentExtendForSdkDao adContentExtendForSdkDao;

    @Autowired
    private AdContentDao adContentDao;

    @Autowired
    private SdkSchemaService sdkSchemaService;

    @Autowired
    private AdCampaignDao adCampaignDao;

    @Autowired
    private AdGroupDao adGroupDao;

    @Autowired
    private ImageSizeMappingDao imageSizeMappingDao;

    @Autowired
    private GroupDeliverySettingsService groupDeliverySettingsService;

    @Autowired
    private AdTemplateService adTemplateService;

    @Autowired
    private AdGroupService adGroupService;

    @Autowired
    private ImageTemplateTagDao imageTemplateTagDao;

    @Autowired
    private AdContentExtendForHugeMaterialDao adContentExtendForHugeMaterialDao;

    @Override
    public List<SdkElement> getElementBySchemaId(Long schemaId) {
        return getElementOfSchema(sdkSchemaDao.getSdkSchemasById(schemaId));
    }

    @Override
    public List<SimpleElemTypeList> getDeliverySdkElement(Type type, long id, Integer queryType) throws Exception {

        if (type != Type.CONTENT && type != Type.GROUP) {
            return null;
        }

        if (groupDeliverySettingsService.slotTarget(type, id)) {
            // 广告位
            if (type == Type.CONTENT) {
                AdContent adContent = adContentDao.getAdContent(id);
                return inputHqData(getDeliverySdkElement(adContent.getAdGroupId(), sdkDataJson(id), queryType), queryType);
            } else {
                return inputHqData(getDeliverySdkElement(id, null, queryType), queryType);
            }
        } else {
            // 广告模板
            if (type == Type.CONTENT) {
                AdContent adContent = adContentDao.getAdContent(id);
                return inputHqData(getTemplateElement(adContent.getAdGroupId(), sdkDataJson(id), queryType), queryType);
            } else {
                return inputHqData(getTemplateElement(id, null, queryType), queryType);
            }
        }
    }

    private List<SimpleElemTypeList> inputHqData(List<SimpleElemTypeList> hqDeliverySdkElement, Integer queryType) {
        // 如果不是高质量的元素 也直接返回 处理空值 有可能获取不到高质量和元素
        if (!queryType.equals(Constants.ELEMENT_QUERY_HQ) || CollectionUtils.isEmpty(hqDeliverySdkElement) || CollectionUtils.isEmpty(hqDeliverySdkElement.get(0).getList())) {
            return hqDeliverySdkElement;
        }
        List<Long> elementIds = new ArrayList<>();
        for (SimpleElemTypeList simpleElemTypeList : hqDeliverySdkElement) {
            for (SimpleElem simpleElem : simpleElemTypeList.getList()) {
                elementIds.add(simpleElem.getId());
            }
        }
        Map<Long, String> sdkElementId2Key = sdkElementDao.getListByIds(elementIds)
                .stream()
                .collect(Collectors.toMap(SdkElement::getSdkElementId, SdkElement::getSdkElementKey));
        Map<Long, ImageTemplateTag> elementIdImageTemplateTagMap = imageTemplateTagDao.getByElementIds(elementIds)
                .stream()
                .collect(Collectors.toMap(ImageTemplateTag::getElementId, o -> o));
        for (SimpleElemTypeList simpleElemTypeList : hqDeliverySdkElement) {
            List<SimpleElem> listWithHqData = new ArrayList<>();
            for (SimpleElem simpleElem : simpleElemTypeList.getList()) {
                SimpleImageElemWithHq simpleImageElemWithHq = new SimpleImageElemWithHq();
                try {
                    PropertyUtils.copyProperties(simpleImageElemWithHq, simpleElem);
                } catch (Exception e) {
                    LogUtil.errorLog(log, e);
                }
                simpleImageElemWithHq.setRatio(DataUtil.minScale(Integer.parseInt(simpleImageElemWithHq.getWidth() + ""), Integer.parseInt(simpleImageElemWithHq.getHeight() + "")));
                simpleImageElemWithHq.setSlotName(sdkElementId2Key.get(simpleElem.getId()).trim().substring(12));
                simpleImageElemWithHq.setTagId(elementIdImageTemplateTagMap.get(simpleImageElemWithHq.getId()).getId());
                listWithHqData.add(simpleImageElemWithHq);
            }
            simpleElemTypeList.setList(listWithHqData);
        }
        return hqDeliverySdkElement;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<SimpleElemTypeList> getCopiedDeliverySdkElement(Long copiedAdGroupId, AdContentExtendForSdk adContentExtendForSdk) throws Exception {
        String sdkData = adContentExtendForSdk.getSdkData();
        if (groupDeliverySettingsService.slotTarget(Type.GROUP, copiedAdGroupId)) {
            log.info("slot targeting copy sdkData from group:{}, adContentId:{}", copiedAdGroupId, adContentExtendForSdk.getAdContentId());
            return getDeliverySdkElement(copiedAdGroupId, StringUtils.isEmpty(sdkData) ? null : JSONObject.fromObject(sdkData), Constants.ELEMENT_QUERY_BOTH);
        } else {
            log.info("template targeting copy sdkData from group:{}, adContentId:{}", copiedAdGroupId, adContentExtendForSdk.getAdContentId());
            return getTemplateElement(copiedAdGroupId, StringUtils.isEmpty(sdkData) ? null : JSONObject.fromObject(sdkData), Constants.ELEMENT_QUERY_BOTH);
        }
    }

    private List<SimpleElemTypeList> getTemplateElement(long adGroupId, Map<String, String> sdkDataJson, Integer isQueryHqElement) {
        checkFlow(adGroupId);

        GroupDeliverySettings templateSettings = groupDeliverySettingsService.validateDeliverySetting(adGroupId, NameCode.GROUP_SDK_TEMPLATE);
        Set<Long> templateIds = DataUtil.toLongs(templateSettings.getSettingsContent());

        // key: template element, value: element  id
        Map<Long, Set<Long>> templateEleId2eleIds = adTemplateService.templateEleId2eleIdsByTemplateSchemaIds(templateIds);
        Map<String, String> templateEle2Data = new HashMap<>();  // templateElementId -> user input
        if (sdkDataJson != null) {
            // 多个元素映射到同一个模板元素，任选其中一个元素的数据显示
            templateEleId2eleIds.forEach((teId, eIds) -> {
                for (Long eId : eIds) {
                    String data = sdkDataJson.get(eId + "");
                    if (data != null) {
                        templateEle2Data.put(valueOf(teId), data);
                        return;
                    }
                }
            });
        }

        // template element
        List<SdkElement> sdkElements = sdkElementDao.getListByIds(templateEleId2eleIds.keySet());

        Map<String, List<SimpleElem>> key2Elems = new LinkedHashMap<>();
        for (SdkElement element : sdkElements) {
            SimpleElem elem = toSimpleElem(element, templateEle2Data);
            if (elem == null ||
                    (element.getSdkElementType() != SdkElementTypeConstants.HQ_IMAGE && isQueryHqElement.equals(Constants.ELEMENT_QUERY_HQ)) ||
                    (element.getSdkElementType() == SdkElementTypeConstants.HQ_IMAGE && isQueryHqElement.equals(Constants.ELEMENT_QUERY_NORMAL))) {
                continue;
            }
            putTo(key2Elems, elem, element);
        }

        return toElemTypeLists(key2Elems);
    }

    /**
     * @param adGroupId
     * @param sdkDataJson
     * @param queryType   0都查询 1仅查普通 2查高质量
     * @return
     * @throws Exception
     */
    private List<SimpleElemTypeList> getDeliverySdkElement(long adGroupId, Map<String, String> sdkDataJson, Integer queryType) throws Exception {
        int flowType = checkFlow(adGroupId);

        AdGroup adGroup = adGroupDao.getAdGroup(adGroupId);
        AdCampaign adCampaign = adCampaignDao.getAdCampaign(adGroup.getAdCampaignId());
        int landingType = adCampaign.getLandingPageType();

        GroupDeliverySettings sdkSlotDeliverySettings = groupDeliverySettingsService.validateDeliverySetting(adGroupId, NameCode.GROUP_SDK_SLOT);
        List<String> deliverySlotUdids = Arrays.asList(sdkSlotDeliverySettings.getSettingsContent().split(","));

        if (CollectionUtils.isEmpty(deliverySlotUdids)) {
            return null;
        }

        List<SdkSchema> schemaDatas = null;
        if (adGroupService.isBrandAdSponsor(adGroup.getSponsorId())) {
            schemaDatas = sdkSchemaService.getSchemaBySlotUdidsAndType(deliverySlotUdids, landingType, -1);
        } else {
            schemaDatas = sdkSchemaService.getSchemaBySlotUdidsAndType(deliverySlotUdids, landingType, TypeCode.SDK_SCHEMA_AD_TYPE_BRAND);
        }
        if (CollectionUtils.isEmpty(schemaDatas)) {
            return null;
        }

        /**
         * 如果样式A和B都有元素x，A需要映射，B不映射，那么x不映射,
         * 需映射元素集合为mappingElems，不映射元素集合为nonMappingElems，
         * 最终需映射元素为mappingElems-nonMappingElems
         */
        Set<Long> mappingElemIds = new HashSet<>();
        Set<Long> nonMappingElemIds = new HashSet<>();
        Set<SdkElement> elementsSet = new HashSet<>();
        for (SdkSchema schema : schemaDatas) {
            List<SdkElement> sdkElements = getElementOfSchema(schema);
            elementsSet.addAll(sdkElements);

            Set<Long> elemIds = DataUtil.sbMap(sdkElements, SdkElement::getSdkElementId);
            if (schema.getMappingMethod() == 1 || schema.getMappingMethod() == 2) { // 1,2时映射
                mappingElemIds.addAll(elemIds);
            } else {
                nonMappingElemIds.addAll(elemIds);
            }
        }
        if (CollectionUtils.isEmpty(elementsSet)) {
            return null;
        }
        mappingElemIds.removeAll(nonMappingElemIds);

        List<SdkElement> elementDatas = new ArrayList<>(elementsSet);
        Collections.sort(elementDatas, (o1, o2) -> {
                    Long l1 = o1.getSdkElementId();
                    Long l2 = o2.getSdkElementId();
                    return l1.compareTo(l2);
                }
        );

        List<ImageSizeMapping> sizeMappings = imageSizeMappingDao.getAll();

        Map<String, List<SimpleElem>> key2Elems = new LinkedHashMap<>();
        for (SdkElement se : elementDatas) {
            SimpleElem elem = toSimpleElem(se, sdkDataJson);
            if (elem == null) {
                continue;
            }
            // 要查询高质量 但是不是高质量的 丢弃
            if (queryType.equals(Constants.ELEMENT_QUERY_HQ) && se.getSdkElementType() != SdkElementTypeConstants.HQ_IMAGE) {
                continue;
            }
            // 要查询普通的 但是是高质量的 丢弃
            if (queryType.equals(Constants.ELEMENT_QUERY_NORMAL) && se.getSdkElementType() == SdkElementTypeConstants.HQ_IMAGE) {
                continue;
            }
            // 处理映射
            if (mappingElemIds.contains(se.getSdkElementId())) {
                mapping(elem, se, sizeMappings);
            }
            /************* 填充定制数据 **************/
            if (se.getSdkElementType() == 2 && flowType != -1) {
                DataUtil.fillInSpecifiedContents(elem, se, flowType);
            }

            putTo(key2Elems, elem, se);
        }

        return toElemTypeLists(key2Elems);
    }

    private JSONObject sdkDataJson(long adContentId) {
        AdContentExtendForSdk adContentExtendForSdk = adContentExtendForSdkDao.getByAdContentId(adContentId);
        if (adContentExtendForSdk != null) {
            String sdkData = adContentExtendForSdk.getSdkData();
            if (!StringUtils.isEmpty(sdkData)) {
                return JSONObject.fromObject(sdkData);
            }
        }
        return null;
    }

    private int checkFlow(long adGroupId) {
        GroupDeliverySettings flowDeliverySetting = groupDeliverySettingsService.validateDeliverySetting(adGroupId, NameCode.GROUP_FLOW_TYPE);
        int flowType = Integer.parseInt(flowDeliverySetting.getSettingsContent());
        ExceptionUtil.badRequestIf(flowType == 0, "组流量定向错误，不能为效果流量");
        return flowType;
    }

    private void putTo(Map<String, List<SimpleElem>> key2Elems, SimpleElem elem, SdkElement se) {
        List<SimpleElem> elemJsonArray = key2Elems.get(se.getSdkElementKey());
        elemJsonArray = elemJsonArray == null ? new ArrayList<>() : elemJsonArray;
        elemJsonArray.add(elem);
        key2Elems.put(se.getSdkElementKey(), elemJsonArray);
    }

    private List<SimpleElemTypeList> toElemTypeLists(Map<String, List<SimpleElem>> key2Elems) {
        List<SimpleElemTypeList> simpleElemTypeLists = new ArrayList<>();
        key2Elems.forEach((k, v) -> {
            SimpleElemTypeList typeList = new SimpleElemTypeList();
            typeList.setType(k);
            typeList.setList(v);
            simpleElemTypeLists.add(typeList);
        });
        return simpleElemTypeLists;
    }

    private SimpleElem toSimpleElem(SdkElement se, Map<String, String> sdkDataJson) {
        SimpleElem elem = null;
        if (greaterThanZero(se.getSdkElementLength())) {
            if (se.getSdkElementType() == SdkElementTypeConstants.HTML_CONTENT) {
                elem = buildFileElement(se, sdkDataJson);
            } else {
                SimpleTxtElem txtElem = new SimpleTxtElem();
                txtElem.setId(se.getSdkElementId());
                txtElem.setName(se.getSdkElementName());
                txtElem.setLength(se.getSdkElementLength());
                elem = txtElem;
            }
        } else if (StringUtils.containsIgnoreCase(se.getSdkElementKey(), "video") && greaterThanZero(se.getSdkElementHeight()) && greaterThanZero(se.getSdkElementWidth())) {
            SimpleVideoElem videoElem = new SimpleVideoElem();
            videoElem.setId(se.getSdkElementId());
            videoElem.setName(se.getSdkElementName());
            videoElem.setWidth(se.getSdkElementWidth());
            videoElem.setHeight(se.getSdkElementHeight());
            videoElem.setDuration(se.getDuration());
            elem = videoElem;
        } else if (greaterThanZero(se.getSdkElementHeight()) && greaterThanZero(se.getSdkElementWidth())) {
            SimpleImageElem imageElem = new SimpleImageElem();
            imageElem.setId(se.getSdkElementId());
            imageElem.setName(se.getSdkElementName());
            imageElem.setHeight(se.getSdkElementHeight());
            imageElem.setWidth(se.getSdkElementWidth());
            elem = imageElem;
        }

        if (sdkDataJson != null && elem != null) {
            String data = sdkDataJson.get(valueOf(se.getSdkElementId()));
            if (!StringUtils.isEmpty(data)) {
                elem.setData(data);
            }
        }

        return elem;
    }

    @NotNull
    private SimpleFileElem buildFileElement(SdkElement se, Map<String, String> sdkDataJson) {
        SimpleFileElem fileElem = new SimpleFileElem();
        fileElem.setId(se.getSdkElementId());
        fileElem.setName(se.getSdkElementName());
        if (MapUtils.isNotEmpty(sdkDataJson)) {
            String hugeMaterialId = sdkDataJson.get(String.valueOf(se.getSdkElementId()));
            if (StringUtils.isNotBlank(hugeMaterialId)) {
                AdContentExtendForHugeMaterial adContentExtendForHugeMaterial = adContentExtendForHugeMaterialDao.get(Long.parseLong(hugeMaterialId));
                if (Objects.nonNull(adContentExtendForHugeMaterial)) {
                    fileElem.setFileName(adContentExtendForHugeMaterial.getFileName());
                }
            }
        }
        return fileElem;
    }

    private List<SdkElement> getElementOfSchema(SdkSchema schema) {
        String elements = schema.getSdkElements();
        if (!StringUtils.isEmpty(elements)) {
            Set<String> elemIdStrSet = JSONObject.fromObject(elements).keySet();
            List<Long> ids = elemIdStrSet.stream()
                    .map(s -> Long.parseLong(s))
                    .collect(Collectors.toList());
            return sdkElementDao.getListByIds(ids);
        }
        return Collections.emptyList();
    }

    private boolean greaterThanZero(Integer i) {
        return i != null && i > 0;
    }

    /**
     * 如果元素为标准元素且比例在映射表旧比例中存在，则直接映射为新尺寸
     * 若为非标准，查找旧尺寸，找到则映射
     * 过程中任何sb数据导致没法进行就不映射
     *
     * @param o
     */
    private void mapping(SimpleElem o, SdkElement se, List<ImageSizeMapping> sizeMappings) {
        // 排除文字等元素
        if (!greaterThanZero(se.getSdkElementHeight()) || !greaterThanZero(se.getSdkElementWidth())) {
            return;
        }
        Optional<Long> newRatioElemId = Optional.empty();
        if (se.getSdkElementIsstandard() == 0) {
            String oldRatio = imageRatio(se.getSdkElementName());
            if (oldRatio == null) {
                return;
            }
            newRatioElemId =
                    sizeMappings.stream()
                            .filter(m -> !StringUtils.isEmpty(m.getOldRatio()))
                            .filter(m -> m.getOldRatio().trim().equals(oldRatio))
                            .findAny()
                            .map(ImageSizeMapping::getNewRatioElementId);
        } else {
            String oldSize = se.getSdkElementWidth() + "*" + se.getSdkElementHeight();
            newRatioElemId =
                    sizeMappings.stream()
                            .filter(m -> StringUtils.isEmpty(m.getOldRatio()))
                            .filter(m -> m.getOldSize().trim().equals(oldSize))
                            .findAny()
                            .map(ImageSizeMapping::getNewRatioElementId);
        }

        if (newRatioElemId.isPresent()) {
            SdkElement newRatioElem = sdkElementDao.getElementById(newRatioElemId.get());
            // 修改为新比例，尺寸
            Integer newHeight = newRatioElem.getSdkElementHeight();
            Integer newWidth = newRatioElem.getSdkElementWidth();
            String newRatio = imageRatio(newRatioElem.getSdkElementName());
            if (newHeight == null || newWidth == null || newRatio == null) {
                return;
            }
            o.setName(nameWithNewRatio(se.getSdkElementName(), newRatio));
            ((SimpleImageElem) o).setHeight(newHeight);
            ((SimpleImageElem) o).setWidth(newWidth);
        }
    }

    private String imageRatio(String elemName) {
        String[] arr = elemName.split("_");
        if (arr.length < 2) {
            return null;
        }
        String ratio = arr[arr.length - 1];
        if (!ratio.contains(":")) {
            return null;
        }
        return ratio.trim();
    }

    private String nameWithNewRatio(String elemName, String newRatio) {
        int index = elemName.lastIndexOf("_");
        return elemName.substring(0, index) + "_" + newRatio;
    }
}
