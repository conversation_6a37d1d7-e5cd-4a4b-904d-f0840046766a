package outfox.ead.noah.service.impl;

import com.google.common.base.Objects;
import io.druid.data.input.Row;
import io.druid.java.util.common.granularity.Granularities;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.dao.*;
import outfox.ead.noah.druid.DruidBuilder;
import outfox.ead.noah.dto.ConvertTrackingRecent7DaysDTO;
import outfox.ead.noah.entity.*;
import outfox.ead.noah.service.ConvertTrackingStatService;
import outfox.ead.noah.service.DruidService;
import outfox.ead.noah.service.StatDataSourceService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static outfox.ead.noah.constants.Constants.*;
import static outfox.ead.noah.druid.Builder.Criteria.*;
import static outfox.ead.noah.druid.DruidBuilder.CONV_DSP_STAT;
import static outfox.ead.noah.druid.DruidBuilder.DruidCol.*;
import static outfox.ead.noah.util.DateUtil.interval;
import static outfox.ead.noah.util.code.StatusCode.CONVERT_DEBUG_STATUS_SUCCEED;
import static outfox.ead.noah.util.code.TypeCode.CONVERT_TYPE_LANDINGPAGE;
import static outfox.ead.noah.util.code.TypeCode.CONVERT_TYPE_LANDINGPAGE_API;
import static outfox.ead.noah.util.datautil.DataUtil.getDimension;

/**
 *
 * Created by wangmo on 2018/3/5.
 *
 */
@Service
public class ConvertTrackingStatServiceImpl implements ConvertTrackingStatService {

    private FastDateFormat targetFormat = FastDateFormat.getInstance("yyyy-MM-dd", TimeZone.getTimeZone(Constants.ASIA_SHANGHAI));

    @Autowired
    private DruidService druidService;

    @Autowired
    private StatDataSourceService statDataSourceService;

    @Autowired
    private ConvertTrackingDao convertTrackingDao;

    @Autowired
    private AdCampaignDao adCampaignDao;

    @Autowired
    private AdGroupDao adGroupDao;

    @Autowired
    private AdContentDao adContentDao;

    @Autowired
    private SdkAppInfoDao sdkAppInfoDao;

    @Autowired
    private SdkSlotDao sdkSlotDao;

    @Autowired
    private AdGroupExtendForDspDao adGroupExtendForDspDao;

    /**
     * 获取多个转化跟踪在一段时间内的转化数。返回转化Uid到转化数的map。
     * 落地页型转化跟踪也查询外部数据。
     * 注意：由于转化跟踪可以推送，查询时sponsorId以登录账号为准，而不使用ConvertTrackingInfo表中的sponsorId
     */
    @Override
    public Map<String, Long> getConvertNumCount(Long sponsorId, List<ConvertTrackingInfo> ctInfos,
                                                  long startEpochMilli, long endEpochMilli) {
        if (CollectionUtils.isEmpty(ctInfos)) {
            return Collections.emptyMap();
        }

        List<Long> debugAdGroupIds = ctInfos.stream()
            .filter(c -> (c.getConvertTrackingType() != CONVERT_TYPE_LANDINGPAGE
                    || c.getConvertTrackingType() != CONVERT_TYPE_LANDINGPAGE_API)
                    && c.getConvertTrackingDebugAdGroupId() != null)
            .map(ConvertTrackingInfo::getConvertTrackingDebugAdGroupId)
            .collect(Collectors.toList());

        DruidBuilder builder = DruidBuilder.get();
        builder.select(CONVERT_NUM);
        builder.from(statDataSourceService.getSdkStatDataSourceForConv(sponsorId));
        // 过滤下载联调推广组
        builder.whereIf(CollectionUtils.isNotEmpty(debugAdGroupIds), not(AD_GROUP_ID, debugAdGroupIds));
        builder.where(in(CONVERT_ID, ctInfos.stream().map(ConvertTrackingInfo::getConvertTrackingUid).collect(Collectors.toList())));
        builder.where(eq(SPONSOR_ID,sponsorId));
        builder.interval(interval(startEpochMilli, endEpochMilli));
        builder.granularity(Granularities.ALL);
        builder.groupBy(CONVERT_ID);
        builder.groupBy(CONVERT_ACTION);

        List<Row> druidQueryResult = druidService.groupByQuery(builder.buildGroupBy());

        // 转化事件的map，用于过滤不属于转化的事件
        Map<String, Set<String>> convertActionsMap = new HashMap<>();
        ctInfos.forEach(ctInfo -> {
            convertActionsMap.put(ctInfo.getConvertTrackingUid(), new HashSet<>(Arrays.asList(ctInfo.getConvertTrackingActions().split(","))));
        });

        Map<String, Long> convertIdToNumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(druidQueryResult)) {
            druidQueryResult.forEach(row -> {
                String convertId = getDimension(row, CONVERT_ID);
                String convertAction = getDimension(row, CONVERT_ACTION);
                Long convertNum = row.getLongMetric(CONVERT_NUM.name);
                if (convertActionsMap.containsKey(convertId) && convertActionsMap.get(convertId).contains(convertAction)) {
                    convertIdToNumMap.put(convertId, convertIdToNumMap.getOrDefault(convertId, 0L) + convertNum);
                }
            });
        }

        List<String> lpConvertIds = ctInfos.stream()
                .filter(c -> c.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE
                        || c.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE_API)
                .map(ConvertTrackingInfo::getConvertTrackingUid).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(lpConvertIds)) {
            return convertIdToNumMap;
        }

        // 查询外部数据
        DruidBuilder outerDataBuilder = DruidBuilder.get();
        outerDataBuilder.select(CONVERT_NUM);
        outerDataBuilder.from(CONV_DSP_STAT);
        outerDataBuilder.where(in(CONVERT_ID, lpConvertIds));
        outerDataBuilder.interval(interval(startEpochMilli, endEpochMilli));
        outerDataBuilder.granularity(Granularities.ALL);
        outerDataBuilder.groupBy(CONVERT_ID);
        outerDataBuilder.groupBy(CONVERT_ACTION);

        List<Row> outerDataDruidQueryResult = druidService.groupByQuery(outerDataBuilder.buildGroupBy());
        if (CollectionUtils.isNotEmpty(outerDataDruidQueryResult)) {
            outerDataDruidQueryResult.forEach(row -> {
                String convertId = getDimension(row, CONVERT_ID);
                String convertAction = getDimension(row, CONVERT_ACTION);
                Long convertNum = row.getLongMetric(CONVERT_NUM.name);
                if (convertActionsMap.containsKey(convertId) && convertActionsMap.get(convertId).contains(convertAction)) {
                    convertIdToNumMap.merge(convertId, convertNum, (innerConv, outerConv) -> innerConv + outerConv);
                }
            });
        }

        return convertIdToNumMap;
    }

    /**
     * 获取落地页型转化是否激活。激活指的是每个事件都至少有一个无广告信息转化。
     * 返回转化Uid到是否激活的map。
     */
    @Override
    public Map<String, Boolean> isLPConvertActivated(List<ConvertTrackingInfo> lpctInfos) {
        if (CollectionUtils.isEmpty(lpctInfos)) {
            return Collections.emptyMap();
        }

        List<String> convertIds = new ArrayList<>();
        Map<String, Set<String>> convertActionMap = new HashMap<>();
        long startEpochMilli = System.currentTimeMillis();
        long endEpochMilli = System.currentTimeMillis();

        for (ConvertTrackingInfo c : lpctInfos) {
            if (c.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE) {
                convertIds.add(c.getConvertTrackingUid());
                convertActionMap.put(c.getConvertTrackingUid(),
                    new HashSet<>(Arrays.asList(c.getConvertTrackingActions().split(","))));
                if (c.getCreateTime().getTime() < startEpochMilli) {
                    startEpochMilli = c.getCreateTime().getTime();
                    // first milli second of this hour, stat count to hour
                    startEpochMilli = startEpochMilli - startEpochMilli % 3600000;
                }
            }
        }

        if (CollectionUtils.isEmpty(convertIds)) {
            return Collections.emptyMap();
        }

        DruidBuilder builder = DruidBuilder.get();
        builder.select(CONVERT_NUM);
        builder.from(CONV_DSP_STAT);
        builder.where(in(CONVERT_ID, convertIds));
        builder.interval(interval(startEpochMilli, endEpochMilli));
        builder.granularity(Granularities.ALL);
        builder.groupBy(CONVERT_ID);
        builder.groupBy(CONVERT_ACTION);

        List<Row> druidQueryResult = druidService.groupByQuery(builder.buildGroupBy());

        if (CollectionUtils.isNotEmpty(druidQueryResult)) {
            druidQueryResult.forEach(row -> {
                if (row.getLongMetric(CONVERT_NUM.name) > 0) {
                    String convertId = getDimension(row, CONVERT_ID);
                    if (convertActionMap.containsKey(convertId)) {
                        convertActionMap.get(convertId).remove(getDimension(row, CONVERT_ACTION));
                    }
                }
            });
        }

        Map<String, Boolean> convertIdToActivatedMap = new HashMap<>();

        convertActionMap.forEach((convertId, nonConvertedActions) -> {
            convertIdToActivatedMap.put(convertId, CollectionUtils.isEmpty(nonConvertedActions));
        });

        return convertIdToActivatedMap;
    }

    /**
     * 查询下载型和落地页API型转化是否激活，即下载型转化的测试推广组里面是否每个事件至少有一个转化。
     */
    @Override
    public boolean isDownloadAndLpApiConvertActivated(ConvertTrackingInfo ctInfo) {
        if (ctInfo == null
                || ctInfo.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE
                || ctInfo.getConvertTrackingDebugAdGroupId() == null) {
            return false;
        }
        DruidBuilder builder = DruidBuilder.get();
        builder.select(CONVERT_ID);
        builder.select(CONVERT_NUM);
        builder.from(statDataSourceService.getSdkStatDataSourceForConv(ctInfo.getSponsorId()));
        builder.where(eq(AD_GROUP_ID, ctInfo.getConvertTrackingDebugAdGroupId()));
        builder.where(eq(CONVERT_ID, ctInfo.getConvertTrackingUid()));
        List<String> ctActionList = Arrays.asList(ctInfo.getConvertTrackingActions().split(","));
        builder.where(in(CONVERT_ACTION, ctActionList));
        builder.groupBy(CONVERT_ACTION);
        long startEpochMilli = ctInfo.getCreateTime().getTime();
        startEpochMilli = startEpochMilli - startEpochMilli % 3600000;
        builder.interval(interval(startEpochMilli, System.currentTimeMillis()));
        builder.granularity(Granularities.ALL);

        List<Row> druidQueryResult = druidService.groupByQuery(builder.buildGroupBy());
        if (CollectionUtils.isNotEmpty(druidQueryResult)) {
            if (ctActionList.size() == druidQueryResult.stream().filter(r ->r.getLongMetric(CONVERT_NUM.name) > 0).count()) {
                return true;
            }
        }

        // 查询外部数据
        DruidBuilder outerDataBuilder = DruidBuilder.get();
        outerDataBuilder.select(CONVERT_ID);
        outerDataBuilder.select(CONVERT_NUM);
        outerDataBuilder.from(CONV_DSP_STAT);
        outerDataBuilder.where(eq(CONVERT_ID, ctInfo.getConvertTrackingUid()));
        outerDataBuilder.where(in(CONVERT_ACTION, ctActionList));
        outerDataBuilder.groupBy(CONVERT_ACTION);
        outerDataBuilder.interval(interval(startEpochMilli, System.currentTimeMillis()));
        outerDataBuilder.granularity(Granularities.ALL);

        List<Row> outerDataDruidQueryResult = druidService.groupByQuery(outerDataBuilder.buildGroupBy());

        return CollectionUtils.isNotEmpty(outerDataDruidQueryResult)
                && ctActionList.size() == outerDataDruidQueryResult.stream().filter(r ->r.getLongMetric(CONVERT_NUM.name) > 0).count();
    }

    /**
     * 查询统计数据。分成按落地页url查询落地页型转化数据 和 按广告信息查询转化数据。
     *
     * @param offset 下载时候为null，不分页
     * @param limit 下载时候为null，不分页
     */
    @Override
    public JSONObject getConvertStatData(ConvertTrackingInfo ctInfo, int groupByGranularity,
                                         Long adCampaignId, Long adGroupId, long startEpochMilli,
                                         long endEpochMilli, Integer offset, Integer limit) throws Exception {
        ConvertTrackingStatQueryResult result;
        if (groupByGranularity == GRANULARITY_LANDINGPAGE) {
            result = getLandingPageConvertTrackingStatData(ctInfo,
                startEpochMilli, endEpochMilli, offset, limit);
        } else {
            // 按照推广系列、推广组、创意、广告位、APP来group by
            result = getGroupByAdInfoStatData(ctInfo, groupByGranularity, adCampaignId, adGroupId,
                startEpochMilli, endEpochMilli, offset, limit);
        }

        // 如果是落地页型转化跟踪，查询外部产生的转化, 将外部的数据加到总和里面去
        if (ctInfo.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE || ctInfo.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE_API) {
            Map<String, Long> outerConvertStatData = getLandingPageConvertTrackingOuterData(ctInfo, startEpochMilli, endEpochMilli);
            Map<String, Long> totalStatData = result.getTotalStatData();
            outerConvertStatData.forEach((k, v) -> totalStatData.put(k, v + totalStatData.get(k)));
            result.setOuterConvertStatData(outerConvertStatData);
        }

        return JSONObject.fromObject(result);
    }

    /**
     * 获取非有道投放的落地页型转化数。数据取自{@link DruidBuilder#CONV_DSP_STAT}。其中，联调成功的转化的数据才会展示出来
     */
    private Map<String, Long> getLandingPageConvertTrackingOuterData(ConvertTrackingInfo ctInfo, long startEpochMilli, long endEpochMilli) {
        Map<String, Long> result = new HashMap<>();
        DruidBuilder builder = DruidBuilder.get();
        builder.select(CONVERT_NUM);
        builder.from(CONV_DSP_STAT);
        builder.where(eq(CONVERT_ID, ctInfo.getConvertTrackingUid()));
        builder.where(in(CONVERT_ACTION, Arrays.asList(ctInfo.getConvertTrackingActions().split(","))));
        builder.interval(interval(startEpochMilli, endEpochMilli));
        builder.granularity(Granularities.ALL);
        builder.groupBy(CONVERT_ACTION);

        List<Row> druidQueryResult = druidService.groupByQuery(builder.buildGroupBy());

        if (CollectionUtils.isNotEmpty(druidQueryResult)) {
            druidQueryResult.forEach(row -> {
                result.put(getDimension(row, CONVERT_ACTION), row.getLongMetric(CONVERT_NUM.name));
            });
        }

        List<String> convertActions = Arrays.asList(ctInfo.getConvertTrackingActions().split(","));
        convertActions.forEach(convertAction -> result.putIfAbsent(convertAction, 0L));

        return result;
    }

    private ConvertTrackingStatQueryResult getGroupByAdInfoStatData(ConvertTrackingInfo ctInfo, int groupByGranularity,
                                     Long adCampaignId, Long adGroupId, long startEpochMilli,
                                     long endEpochMilli, Integer offset, Integer limit) {

        DruidBuilder builder = DruidBuilder.get();
        builder.select(CONVERT_NUM);
        builder.from(statDataSourceService.getSdkStatDataSourceForConv(ctInfo.getSponsorId()));
        builder.where(eq(CONVERT_ID, ctInfo.getConvertTrackingUid()));
        builder.where(eq(SPONSOR_ID, ctInfo.getSponsorId()));
        builder.where(in(CONVERT_ACTION, Arrays.asList(ctInfo.getConvertTrackingActions().split(","))));
        // 过滤联调数据
        builder.whereIf((ctInfo.getConvertTrackingType() != CONVERT_TYPE_LANDINGPAGE
                        || ctInfo.getConvertTrackingType() != CONVERT_TYPE_LANDINGPAGE_API)
                        && ctInfo.getConvertTrackingDebugAdGroupId() != null,
                ne(AD_GROUP_ID, ctInfo.getConvertTrackingDebugAdGroupId()));
        builder.whereIf(groupByGranularity > GRANULARITY_AD_CAMPAIGN && adCampaignId != null,
            eq(AD_CAMPAIGN_ID, adCampaignId));
        builder.whereIf(groupByGranularity > GRANULARITY_AD_GROUP && adGroupId != null,
            eq(AD_GROUP_ID, adGroupId));
        builder.groupByIf(groupByGranularity >= GRANULARITY_AD_CAMPAIGN, AD_CAMPAIGN_ID);
        builder.groupByIf(groupByGranularity >= GRANULARITY_AD_GROUP, AD_GROUP_ID);
        builder.groupByIf(groupByGranularity >= GRANULARITY_AD_CONTENT, AD_CONTENT_ID);
        builder.groupByIf(groupByGranularity >= GRANULARITY_AD_SLOT, APP_ID, SLOT_ID);
        builder.groupByIf(ctInfo.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE
                || ctInfo.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE_API, DEST_LINK);
        builder.groupBy(CONVERT_ACTION);
        builder.interval(interval(startEpochMilli, endEpochMilli));

        return doDruidQueryResult(ctInfo, builder, offset, limit);
    }

    private ConvertTrackingStatQueryResult getLandingPageConvertTrackingStatData(ConvertTrackingInfo ctInfo,
                                                                                 long startEpochMilli,
                                                                                 long endEpochMilli, Integer offset, Integer limit) {
        DruidBuilder builder = DruidBuilder.get();
        builder.select(CONVERT_NUM);
        builder.select(DEST_LINK);
        builder.from(statDataSourceService.getSdkStatDataSourceForConv(ctInfo.getSponsorId()));
        builder.where(eq(CONVERT_ID, ctInfo.getConvertTrackingUid()));
        builder.where(eq(SPONSOR_ID, ctInfo.getSponsorId()));
        builder.where(in(CONVERT_ACTION, Arrays.asList(ctInfo.getConvertTrackingActions().split(","))));
        builder.groupBy(CONVERT_ACTION);
        builder.interval(interval(startEpochMilli, endEpochMilli));

        return doDruidQueryResult(ctInfo, builder, offset, limit);
    }

    @Override
    public Set<ConvertTrackingRecent7DaysDTO> getRecent7DaysConvertData(Long convertTrackingUid, Long sponsorId, int debugStatus) {
        DruidBuilder builder = DruidBuilder.get();
        builder.select(CONVERT_NUM, ORDER_AMOUNT);
        builder.from(statDataSourceService.getSdkStatDataSourceForConv(sponsorId));
        builder.where(eq(CONVERT_ID, convertTrackingUid));
        if (debugStatus == CONVERT_DEBUG_STATUS_SUCCEED) {
            builder.where(eq(SPONSOR_ID, sponsorId));
        }
        builder.groupBy(CONVERT_ACTION);
        builder.granularity(Granularities.ALL);
        LocalDateTime start = LocalDate.now().minusDays(6).atStartOfDay();
        LocalDateTime end = LocalDate.now().plusDays(1).atStartOfDay();
        builder.interval(interval(start.toInstant(ZoneOffset.ofHours(8)).toEpochMilli(),
                end.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()));
        List<Row> rows = druidService.groupByQuery(builder.buildGroupBy());
        Set<ConvertTrackingRecent7DaysDTO> result = new HashSet<>();
        for (Row row : rows) {
            String ctAction = String.valueOf(row.getRaw("ct_action"));
            if (StringUtils.isNotBlank(ctAction) && !"null".equals(ctAction)) {
                result.add(ConvertTrackingRecent7DaysDTO.builder()
                        .convertAction(ctAction)
                        .recent7DaysConvertCount(row.getLongMetric(CONVERT_NUM.name))
                        .orderAmount(row.getLongMetric(ORDER_AMOUNT.name))
                        .build());
            }
        }
        return result;
    }



    /**
     * 进行查询，处理结果。
     * 1. 查询druid
     * 2. 将结果放到 ConvertStatData 对象中
     * 3. 将同一个转化跟踪的数据聚合到一起，key是ConvertStatData，value是其转化类型与转化数的map
     * 4. 将聚合的数据排序，分页（或者不分页）
     * 5. 将广告信息的Name根据Id查出来
     * 6. 过滤不需要的字段，得到JSONArray
     * 7. 根据所有查询结果计算合计值
     * 8. 返回接口文档要求的数据
     */
    private ConvertTrackingStatQueryResult doDruidQueryResult(ConvertTrackingInfo ctInfo,
                                              DruidBuilder builder, Integer offset, Integer limit) {
        // 1. 查询druid
        List<Row> druidQueryResult = druidService.groupByQuery(builder.buildGroupBy());
        Set<DruidBuilder.DruidCol> selectCols = new HashSet<>();
        selectCols.addAll(builder.getSelectCols());
        selectCols.addAll(builder.getGroupByCols());
        // 2. 将结果放到 ConvertStatData 对象中
        List<ConvertStatData> convertStatDataList = getConvertStatData(druidQueryResult, selectCols);
        // 3. 将同一个转化跟踪的数据聚合到一起，key是ConvertStatData，value是其转化类型与转化数的map
        Map<ConvertStatData, Map<String, Long>> convertActionNumMap = getConvertActionNumMap(convertStatDataList);
        // 4. 将聚合的数据排序，分页（或者不分页）
        List<ConvertStatData> pagedConvertStatDataList = getPagedConvertStatDataList(convertActionNumMap,
            offset, limit);
        // 5. 将广告信息的Name根据Id查出来
        fillAdInfosName(pagedConvertStatDataList);
        // 6. 过滤不需要的字段，得到JSONArray
        List<String> convertActions = Arrays.asList(ctInfo.getConvertTrackingActions().split(","));
        setDefaultValueZeroForConvertStatDataList(pagedConvertStatDataList, convertActions);
        JSONArray convertStatDataListJSON = getConvertStatDataJSON(pagedConvertStatDataList);
        // 7. 根据所有查询结果计算合计值
        Map<String, Long> totalStatDataMap = getTotalStatDataMap(convertStatDataList, convertActions);
        // 8. 返回接口文档要求的数据
        ConvertTrackingStatQueryResult result = new ConvertTrackingStatQueryResult();
        result.setActionColumnList(convertActions);
        result.setTotalStatData(totalStatDataMap);
        result.setConvertName(ctInfo.getConvertTrackingName());
        result.setNRecords(convertActionNumMap.keySet().size());
        result.setConvertStatDataList(convertStatDataListJSON);
        return result;

    }

    /**
     * 查询sponsor在某段时间的转化。
     */
    @Override
    public JSONArray getConvertStatReport(long sponsorId, long startEpochMilli,
                                          long endEpochMilli) throws Exception {

        JSONArray contentList = new JSONArray();

        List<ConvertTrackingInfo> ctInfos = convertTrackingDao.getConvertTrackingInfoList(sponsorId);

        if (CollectionUtils.isEmpty(ctInfos)) {
            return contentList;
        }

        Map<String, ConvertTrackingInfo> ctMap =
            ctInfos.stream().collect(Collectors.toMap(ConvertTrackingInfo::getConvertTrackingUid, c -> c));

        List<Long> debugAdGroupIds = ctMap.values().stream()
            .filter(c -> c.getConvertTrackingDebugAdGroupId() != null)
            .map(ConvertTrackingInfo::getConvertTrackingDebugAdGroupId)
            .collect(Collectors.toList());

        Map<String, List<JSONObject>> dayToOuterDataMap = getConvertTrackingOuterReportData(ctInfos, startEpochMilli, endEpochMilli, ctMap);
        Map<String, List<JSONObject>> dayToInnerDataMap = getConvertTrackingInnerReportData(sponsorId, ctInfos, debugAdGroupIds, startEpochMilli, endEpochMilli, ctMap);

        Set<String> allDateSet = new HashSet<>();
        allDateSet.addAll(dayToInnerDataMap.keySet());
        allDateSet.addAll(dayToOuterDataMap.keySet());
        List<String> allSortedDateList = new ArrayList<>(allDateSet);
        Collections.sort(allSortedDateList);
        // 为了确保外部数据展示在平台内数据的后面，将每天的数据收入到list中，取所有date的集合，从map里面顺序拿出来
        allSortedDateList.forEach(date -> {
            contentList.addAll(dayToInnerDataMap.get(date));
            contentList.addAll(dayToOuterDataMap.get(date));
        });

        return contentList;
    }

    /**
     * 获取经过智选投放的转化跟踪的转化数据(第三方检测1.1上线后，加入推广活动数据)。这些数据要求排序在外部数据的上面。并且按天。所以返回date到数据的map
     */
    private Map<String, List<JSONObject>> getConvertTrackingInnerReportData(Long sponsorId, List<ConvertTrackingInfo> ctInfos,
                                                                            List<Long> debugAdGroupIds,
                                                                            long startEpochMilli,
                                                                            long endEpochMilli,
                                                                            Map<String, ConvertTrackingInfo> ctMap) {

        List<String> activatedConvertId = ctInfos.stream()
            .filter(ct -> ct.getConvertTrackingDebugStatus() == CONVERT_DEBUG_STATUS_SUCCEED)
            .map(ConvertTrackingInfo::getConvertTrackingUid)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activatedConvertId)) {
            return Collections.emptyMap();
        }

        DruidBuilder builder = DruidBuilder.get();
        builder.select(AD_CAMPAIGN_ID);
        builder.select(AD_GROUP_ID);
        builder.select(APP_ID);
        builder.select(CONVERT_NUM);
        builder.from(statDataSourceService.getSdkStatDataSourceForConv(sponsorId));
        builder.where(in(CONVERT_ID, activatedConvertId));
        builder.where(eq(SPONSOR_ID, sponsorId));
        // 过滤联调数据
        builder.whereIf(CollectionUtils.isNotEmpty(debugAdGroupIds), not(AD_GROUP_ID, debugAdGroupIds));
        builder.interval(interval(startEpochMilli, endEpochMilli));
        builder.groupBy(CONVERT_ID, DEST_LINK, CONVERT_ACTION, AD_CAMPAIGN_ID, AD_GROUP_ID, AD_CONTENT_ID);
        builder.granularity(Granularities.DAY);
        builder.orderByAsc(DAY.name);

        List<Row> druidQueryResult = druidService.groupByQuery(builder.buildGroupBy());

        if (CollectionUtils.isEmpty(druidQueryResult)) {
            return Collections.emptyMap();
        }

        Set<Long> adCampaignIds = new HashSet<>();
        Set<Long> adGroupIds = new HashSet<>();
        Set<Long> adContentIds = new HashSet<>();

        druidQueryResult.forEach(row -> {
            adCampaignIds.add(row.getLongMetric(AD_CAMPAIGN_ID.name));
            adGroupIds.add(row.getLongMetric(AD_GROUP_ID.name));
            adContentIds.add(row.getLongMetric(AD_CONTENT_ID.name));
        });

        Map<Long, AdCampaign> adCampaignMap = adCampaignDao.getAdCampaignMapByIds(adCampaignIds);
        Map<Long, AdGroup> adGroupMap = adGroupDao.getAdGroupMapByIds(adGroupIds);
        Map<Long, AdGroupExtendForDsp> adGroupExtendForDspMap =
            adGroupExtendForDspDao.getAdGroupExtendForDspMapByGroupIds(adGroupIds);
        Map<Long, AdContent> adContentMap = adContentDao.getAdContentMapByContentIds(adContentIds);

        return druidQueryResult.stream().map(row -> {
                String convertId = getDimension(row, CONVERT_ID);
                Long adCampaignId = row.getLongMetric(AD_CAMPAIGN_ID.name);
                Long adGroupId = row.getLongMetric(AD_GROUP_ID.name);
                Long adContentId = row.getLongMetric(AD_CONTENT_ID.name);
                ConvertTrackingInfo convert = ctMap.get(convertId);
                if (convert == null) {
                    return null;
                }
                List<String> legalConvertActions = Arrays.asList(convert.getConvertTrackingActions().split(","));
                String convertAction = getDimension(row, CONVERT_ACTION);
                if (!legalConvertActions.contains(convertAction)) {
                    return null;
                }

                JSONObject content = new JSONObject();
                content.put("date", targetFormat.format(row.getTimestamp().toDate()));
                content.put("convertName", convert.getConvertTrackingName());
                content.put("convertId", convertId);
                content.put("adCampaignName", adCampaignMap.containsKey(adCampaignId) ? adCampaignMap.get(adCampaignId).getName() : "--");
                content.put("adGroupName", adGroupMap.containsKey(adGroupId) ? adGroupMap.get(adGroupId).getName() : "--");
                content.put("adContentName", adContentMap.containsKey(adContentId) ? adContentMap.get(adContentId).getTitle() : "--");
                Object destLink = row.getRaw(DEST_LINK.name) ;
                if (destLink == null && adGroupExtendForDspMap.containsKey(adGroupId)) {
                    destLink = adGroupExtendForDspMap.get(adGroupId).getDestLink();
                }
                content.put("landingpageUrl", destLink);
                content.put("convertType", convert.getConvertTrackingType());
                content.put("convertAction", convertAction);
                content.put("convertNum", row.getLongMetric(CONVERT_NUM.name));

                return content;
            })
            .filter(java.util.Objects::nonNull)
            .collect(Collectors.groupingBy(content -> content.getString("date")));
    }

    /**
     * 获取未经过智选投放的落地页型的转化跟踪的转化数据。这些数据要求排序在内部数据的下面。并且按天。所以返回date到数据的map
     */
    private Map<String, List<JSONObject>> getConvertTrackingOuterReportData(List<ConvertTrackingInfo> ctInfos,
                                                        long startEpochMilli, long endEpochMilli,
                                                        Map<String, ConvertTrackingInfo> ctMap) {
        if (CollectionUtils.isEmpty(ctInfos)) {
            return Collections.emptyMap();
        }

        // 暂时不显示下载型来自外部的数据
        List<String> convertIds = ctInfos.stream()
                .filter(ct -> (ct.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE
                        || ct.getConvertTrackingType() == CONVERT_TYPE_LANDINGPAGE_API)
                        && ct.getConvertTrackingDebugStatus() == CONVERT_DEBUG_STATUS_SUCCEED)
                .map(ConvertTrackingInfo::getConvertTrackingUid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(convertIds)) {
            return Collections.emptyMap();
        }

        DruidBuilder builder = DruidBuilder.get();
        builder.select(CONVERT_NUM);
        builder.from(CONV_DSP_STAT);
        builder.where(in(CONVERT_ID, convertIds));
        builder.interval(interval(startEpochMilli, endEpochMilli));
        builder.groupBy(CONVERT_ID, CONVERT_ACTION);
        builder.granularity(Granularities.DAY);
        builder.orderByAsc(DAY.name);

        List<Row> druidQueryResult = druidService.groupByQuery(builder.buildGroupBy());

        return druidQueryResult.stream().map(row -> {
                String date = targetFormat.format(row.getTimestamp().toDate());
                String convertId = getDimension(row, CONVERT_ID);
                ConvertTrackingInfo convert = ctMap.get(convertId);
                if (convert == null) {
                    return null;
                }
                List<String> legalConvertActions = Arrays.asList(convert.getConvertTrackingActions().split(","));
                String convertAction = getDimension(row, CONVERT_ACTION);
                if (!legalConvertActions.contains(convertAction)) {
                    return null;
                }

                JSONObject content = new JSONObject();
                content.put("date", date);
                content.put("convertName", convert.getConvertTrackingName());
                content.put("convertId", convertId);
                content.put("adCampaignName", "--");
                content.put("adGroupName", "--");
                content.put("adContentName", "--");
                content.put("landingpageUrl", "--");
                content.put("convertType", convert.getConvertTrackingType());
                content.put("convertAction", convertAction);
                content.put("convertNum", row.getLongMetric(CONVERT_NUM.name));
                return content;
            })
            .filter(java.util.Objects::nonNull)
            .collect(Collectors.groupingBy(content -> content.getString("date")));
    }

    // 过滤不需要的字段，得到JSONArray
    private JSONArray getConvertStatDataJSON(List<ConvertStatData> convertStatData) {
        JSONArray result = new JSONArray();
        if (CollectionUtils.isEmpty(convertStatData)) {
            return result;
        }
        convertStatData.forEach(statData -> {
            JSONObject jo = new JSONObject();
            if (statData.adCampaignName != null) jo.put("adCampaignName", statData.adCampaignName);
            if (statData.adGroupName != null) jo.put("adGroupName", statData.adGroupName);
            if (statData.adContentName != null) jo.put("adContentName", statData.adContentName);
            if (statData.slotName != null) jo.put("slotName", statData.slotName);
            if (statData.appName != null) jo.put("appName", statData.appName);
            if (statData.adCampaignId != null) jo.put("adCampaignId", statData.adCampaignId);
            if (statData.adGroupId != null) jo.put("adGroupId", statData.adGroupId);
            if (statData.landingpageUrl != null) jo.put("landingpageUrl", statData.landingpageUrl);
            if (statData.convertActionToConvertNumMap != null) jo.putAll(statData.convertActionToConvertNumMap);
            result.add(jo);
        });
        return result;
    }

    // 将广告信息的Name根据Id查出来
    private void fillAdInfosName(List<ConvertStatData> convertStatData) {
        Set<Long> adCampaignIds = new HashSet<>();
        Set<Long> adGroupIds = new HashSet<>();
        Set<Long> adContentIds = new HashSet<>();
        Set<Long> sdkAppInfoIds = new HashSet<>();
        Set<String> sdkSlotIds = new HashSet<>();

        convertStatData.forEach(statData -> {
            if (statData.adCampaignId != null) adCampaignIds.add(statData.adCampaignId);
            if (statData.adGroupId != null) adGroupIds.add(statData.adGroupId);
            if (statData.adContentId != null) adContentIds.add(statData.adContentId);
            if (statData.appId != null) sdkAppInfoIds.add(statData.appId);
            if (statData.slotUdid != null) sdkSlotIds.add(statData.slotUdid);
        });

        Map<Long, AdCampaign> adCampaignMap = adCampaignDao.getAdCampaignMapByIds(adCampaignIds);
        Map<Long, AdGroup> adGroupMap = adGroupDao.getAdGroupMapByIds(adGroupIds);
        Map<Long, AdContent> adContentMap = adContentDao.getAdContentMapByContentIds(adContentIds);
        Map<Long, SdkAppInfo> sdkAppInfoMap = sdkAppInfoDao.getMapByIds(sdkAppInfoIds);
        Map<String, SdkSlot> sdkSlotMap = sdkSlotDao.getMapByUdids(sdkSlotIds);

        convertStatData.forEach(statData -> {
            if (statData.adCampaignId != null && adCampaignMap.containsKey(statData.adCampaignId)) {
                statData.adCampaignName = adCampaignMap.get(statData.adCampaignId).getName();
            }
            if (statData.adGroupId != null && adGroupMap.containsKey(statData.adGroupId)) {
                statData.adGroupName = adGroupMap.get(statData.adGroupId).getName();
            }
            if (statData.adContentId != null && adContentMap.containsKey(statData.adContentId)) {
                statData.adContentName = adContentMap.get(statData.adContentId).getTitle();
            }
            if (statData.appId != null && sdkAppInfoMap.containsKey(statData.appId)) {
                statData.appName = sdkAppInfoMap.get(statData.appId).getSdkAppName();
            }
            if (statData.slotUdid != null && sdkSlotMap.containsKey(statData.slotUdid)) {
                statData.slotName = sdkSlotMap.get(statData.slotUdid).getSdkSlotName();
            }
        });
    }

    // 将结果放到 ConvertStatData 对象中
    private List<ConvertStatData> getConvertStatData(List<Row> druidQueryResult,
                                                     Set<DruidBuilder.DruidCol> selectCols) {
        if (CollectionUtils.isEmpty(druidQueryResult) || CollectionUtils.isEmpty(selectCols)) {
            return Collections.emptyList();
        }
        List<ConvertStatData> result = new ArrayList<>();
        for (Row row : druidQueryResult) {
            ConvertStatData statData = new ConvertStatData();
            if (selectCols.contains(DAY))
                statData.date = targetFormat.format(row.getTimestamp().toDate());
            if (selectCols.contains(CONVERT_ID))
                statData.convertId = getDimension(row, CONVERT_ID);
            if (selectCols.contains(CONVERT_ACTION))
                statData.convertAction = getDimension(row, CONVERT_ACTION);
            if (selectCols.contains(AD_CAMPAIGN_ID))
                statData.adCampaignId = row.getLongMetric(AD_CAMPAIGN_ID.name);
            if (selectCols.contains(AD_GROUP_ID))
                statData.adGroupId = row.getLongMetric(AD_GROUP_ID.name);
            if (selectCols.contains(AD_CONTENT_ID))
                statData.adContentId = row.getLongMetric(AD_CONTENT_ID.name);
            if (selectCols.contains(APP_ID))
                statData.appId = row.getLongMetric(APP_ID.name);
            if (selectCols.contains(SLOT_ID) && row.getRaw(SLOT_ID.name) != null)
                statData.slotUdid = getDimension(row, SLOT_ID);
            if (selectCols.contains(DEST_LINK) && row.getRaw(DEST_LINK.name) != null)
                statData.landingpageUrl = getDimension(row, DEST_LINK);
            if (selectCols.contains(CONVERT_NUM))
                statData.convertNum = row.getLongMetric(CONVERT_NUM.name);
            result.add(statData);
        }
        return result;
    }

    // 将同一个转化跟踪的数据聚合到一起，key是ConvertStatData，value是其转化类型与转化数的map
    private Map<ConvertStatData, Map<String, Long>> getConvertActionNumMap(
        List<ConvertStatData> convertStatDataList) {
        Map<ConvertStatData, Map<String, Long>> convertActionDataMap = new HashMap<>();
        for (ConvertStatData statData : convertStatDataList) {
            if (convertActionDataMap.containsKey(statData)) {
                convertActionDataMap.get(statData).put(statData.getConvertAction(), statData.getConvertNum());
            } else {
                Map<String, Long> actionToNum = new HashMap<>();
                actionToNum.put(statData.getConvertAction(), statData.getConvertNum());
                convertActionDataMap.put(statData, actionToNum);
            }
        }
        return convertActionDataMap;
    }

    // 将聚合的数据排序，分页（或者不分页）
    private List<ConvertStatData> getPagedConvertStatDataList(
        Map<ConvertStatData, Map<String, Long>> convertActionDataMap, Integer offset, Integer limit) {
        List<ConvertStatData> aggregatedList = new ArrayList<>(convertActionDataMap.keySet());
        aggregatedList.sort(convertStatDataSortComparator);
        List<ConvertStatData> result = new ArrayList<>();
        if (offset == null) offset = 0;
        if (limit == null) limit = aggregatedList.size();
        if (offset > aggregatedList.size()) return Collections.emptyList();
        for (int i = offset; i < offset + limit && i < aggregatedList.size(); i++) {
            ConvertStatData statData = aggregatedList.get(i);
            statData.convertActionToConvertNumMap = convertActionDataMap.getOrDefault(statData, new HashMap<>());
            result.add(statData);
        }
        return result;
    }

    private void setDefaultValueZeroForConvertStatDataList(List<ConvertStatData> pagedConvertStatDataList, List<String> convertActions) {
        if (CollectionUtils.isEmpty(pagedConvertStatDataList) || CollectionUtils.isEmpty(convertActions)) {
            return;
        }
        for (ConvertStatData convertStatData : pagedConvertStatDataList) {
            Map<String, Long> convertActionToConvertNumMap = convertStatData.getConvertActionToConvertNumMap();
            for (String convertAction : convertActions) {
                if (!convertActionToConvertNumMap.containsKey(convertAction)) {
                    convertActionToConvertNumMap.put(convertAction, 0L);
                }
            }
        }

    }

    // 根据所有查询结果计算合计值
    private Map<String, Long> getTotalStatDataMap(List<ConvertStatData> convertStatDataList,
                                                  List<String> convertActions) {
        Map<String, Long> actionNumMap = new HashMap<>();
        convertStatDataList.forEach(statData -> {
            Long convertNum = actionNumMap.getOrDefault(statData.convertAction, 0L);
            actionNumMap.put(statData.convertAction, convertNum + statData.getConvertNum());
        });
        convertActions.forEach(convertAction -> {
            if (!actionNumMap.containsKey(convertAction)) {
                actionNumMap.put(convertAction, 0L);
            }
        });
        return actionNumMap;
    }

    private Comparator<ConvertStatData> convertStatDataSortComparator = (o1, o2) -> {
        if (o1 == null) return -1;
        if (o2 == null) return 1;
        int c;
        if (o1.date != null && o2.date != null) {
            c = o1.date.compareTo(o2.date);
            if (c != 0) return c;
        }
        if (o1.convertId != null && o2.convertId != null) {
            c = o1.convertId.compareTo(o2.convertId);
            if (c != 0) return c;
        }
        if (o1.convertAction != null && o2.convertAction != null) {
            c = o1.convertAction.compareTo(o2.convertAction);
            if (c != 0) return c;
        }
        if (o1.landingpageUrl != null && o2.landingpageUrl != null) {
            c = o1.landingpageUrl.compareTo(o2.landingpageUrl);
            if (c != 0) return c;
        }
        if (o1.adCampaignId != null && o2.adCampaignId != null) {
            c = o1.adCampaignId.compareTo(o2.adCampaignId);
            if (c != 0) return c;
        }
        if (o1.adGroupId != null && o2.adGroupId != null) {
            c = o1.adGroupId.compareTo(o2.adGroupId);
            if (c != 0) return c;
        }
        if (o1.adContentId != null && o2.adContentId != null) {
            c = o1.adContentId.compareTo(o2.adContentId);
            if (c != 0) return c;
        }
        if (o1.appId != null && o2.appId != null) {
            c = o1.appId.compareTo(o2.appId);
            if (c != 0) return c;
        }
        if (o1.slotUdid != null && o2.slotUdid != null) {
            c = o1.slotUdid.compareTo(o2.slotUdid);
            if (c != 0) return c;
        }
        return 0;
    };

    @Data
    public static class ConvertStatData {
        String date;
        String convertId;
        String convertAction;
        Long adCampaignId;
        Long adGroupId;
        Long adContentId;
        Long appId;
        String slotUdid;
        String adCampaignName;
        String adGroupName;
        String adContentName;
        String appName;
        String slotName;
        String landingpageUrl;
        Long convertNum;
        Map<String, Long> convertActionToConvertNumMap;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            ConvertStatData that = (ConvertStatData) o;

            return Objects.equal(date, that.date)
                && Objects.equal(convertId, that.convertId)
                && Objects.equal(adCampaignId, that.adCampaignId)
                && Objects.equal(adGroupId, that.adGroupId)
                && Objects.equal(adContentId, that.adContentId)
                && Objects.equal(appId, that.appId)
                && Objects.equal(slotUdid, that.slotUdid)
                && Objects.equal(landingpageUrl, that.landingpageUrl);
        }

        @Override
        public int hashCode() {
            int result = (date != null ? date.hashCode() : 0);
            result = 31 * result + (convertId != null ? convertId.hashCode() : 0);
            result = 31 * result + (int) (adCampaignId != null ? adCampaignId ^ (adCampaignId >>> 32) : 0);
            result = 31 * result + (int) (adGroupId != null ? adGroupId ^ (adGroupId >>> 32) : 0);
            result = 31 * result + (int) (adContentId != null ? adContentId ^ (adContentId >>> 32) : 0);
            result = 31 * result + (int) (appId != null ? appId ^ (appId >>> 32) : 0);
            result = 31 * result + (slotUdid != null ? slotUdid.hashCode() : 0);
            result = 31 * result + (landingpageUrl != null ? landingpageUrl.hashCode() : 0);
            return result;
        }
    }

    @Data
    public static class ConvertTrackingStatQueryResult {

        /**
         * 转化行为列表
         */
        List<String> actionColumnList;

        /**
         * 合计数据
         */
        Map<String, Long> totalStatData;

        /**
         * 转化名称
         */
        String convertName;

        /**
         * 总记录数
         */
        @Getter(AccessLevel.NONE)
        Integer nRecords;

        /**
         * 转化跟踪数据列表
         */
        JSONArray convertStatDataList;

        /**
         * 外部转化数据，key是convertAction，value是外部的数据。如果为落地页型，才有该字段
         */
        Map<String, Long> outerConvertStatData;

        public Integer getnRecords() {
            return nRecords;
        }

    }

}
