package outfox.ead.noah.service.impl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.dao.CustomizedTagSettingsDao;
import outfox.ead.noah.dto.TargetingParams;
import outfox.ead.noah.entity.CustomizedTagSettings;
import outfox.ead.noah.service.CustomizedTagSettingsService;
import outfox.ead.noah.service.InterestService;
import outfox.ead.noah.util.code.NameCode;
import outfox.ead.noah.util.code.TypeCode;
import outfox.ead.noah.util.params.SdkAppInfoParams;

import java.util.ArrayList;
import java.util.List;

/**
 * @Date 2019/3/17 15:17
 * <AUTHOR>
 * @Description
 **/
@Service
public class CustomizedTagSettingsServiceImpl implements CustomizedTagSettingsService {

    @Autowired
    CustomizedTagSettingsDao customizedTagSettingsDao;

    @Autowired
    InterestService interestService;

    @Override
    public List<CustomizedTagSettings> getCustomizedTagSettingsByTagIds(String tagIds) {
        return customizedTagSettingsDao.getCustomizedTagSettingsByTagIds(tagIds);
    }

    @Override
    public TargetingParams getSettingDetails(List<CustomizedTagSettings> settings) {
        if (CollectionUtils.isEmpty(settings)) {
            return TargetingParams.builder().gender(String.valueOf(TypeCode.GENDER_ALL)).appOs(-1).network("0")
                    .deliveryApp("").age(TypeCode.AGE_ALL).interest(TypeCode.INTEREST_ALL).build();
        }
        Integer appOs = -2;
        List<String> network = new ArrayList<>();
        List<String> interest = new ArrayList<>();
        List<String> age = new ArrayList<>();
        List<String> genderStr = new ArrayList<>();
        List<String> deliveryApp = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(settings)) {
            for (CustomizedTagSettings setting : settings) {

                switch (setting.getDeliveryScopeType()) {
                    case NameCode.GROUP_DELIVERY_APPOS:
                        appOs = getRightAppOs(appOs, setting.getSettingsContent());
                        break;
                    case NameCode.GROUP_NET_WORK:
                        network.add(setting.getSettingsContent());
                        break;
                    case NameCode.GROUP_SDK_INTEREST:
                        interest.add(setting.getSettingsContent());
                        break;
                    case NameCode.GROUP_SDK_AGE:
                        age.add(setting.getSettingsContent());
                        break;
                    case NameCode.GROUP_SDK_GENDER:
                        genderStr.add(setting.getSettingsContent());
                        break;
                    case NameCode.TAG_APP_DELIVERY_TYPE:
                        deliveryApp.add(setting.getSettingsContent());
                        break;
                    default:
                        break;
                }
            }
            if (appOs == -2) {
                appOs = -1;
            }
        }
        String deliveryAppStr = "";
        if (CollectionUtils.isEmpty(deliveryApp)) {
            deliveryAppStr = StringUtils.join(deliveryApp, ",");
        }
        return TargetingParams.builder().appOs(appOs).network(listToStr(network)).interest(listToStr(interest)).age(listToStr(age))
                .gender(listToStr(genderStr)).deliveryApp(deliveryAppStr).build();
    }

    private Integer getRightAppOs(Integer appOs, String content) {
        if (appOs != -1) {
            if (content.equals(String.valueOf(SdkAppInfoParams.OS_TYPE_ANDROID))) {
                if (appOs == SdkAppInfoParams.OS_TYPE_IOS) {
                    appOs = -1;
                } else {
                    appOs = SdkAppInfoParams.OS_TYPE_ANDROID;
                }
            }
            if (content.equals(String.valueOf(SdkAppInfoParams.OS_TYPE_IOS))) {
                if (appOs == SdkAppInfoParams.OS_TYPE_ANDROID) {
                    appOs = -1;
                } else {
                    appOs = SdkAppInfoParams.OS_TYPE_IOS;
                }
            }
        }
        return appOs;
    }

    private String listToStr(List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "0";
        }
        return StringUtils.join(list, ",");
    }
}
