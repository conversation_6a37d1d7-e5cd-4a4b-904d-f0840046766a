package outfox.ead.noah.service.impl;

import io.druid.data.input.Row;
import io.druid.java.util.common.granularity.Granularities;
import io.druid.java.util.common.granularity.Granularity;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.druid.DruidBuilder;
import outfox.ead.noah.dto.ReportSlotItem;
import outfox.ead.noah.entity.models.report.*;
import outfox.ead.noah.service.CtActionDataQueryService;
import outfox.ead.noah.service.MergeCommonColumnDataService;
import outfox.ead.noah.util.DateTime;
import outfox.ead.noah.util.DateUtil;
import outfox.ead.noah.util.datautil.DataUtil;
import outfox.ead.noah.util.datautil.DspCommonStatUtil;
import outfox.ead.noah.util.enums.CustomColumnEnum;
import outfox.ead.noah.util.params.AdCampaignParams;
import outfox.ead.noah.util.params.AdContentParams;
import outfox.ead.noah.util.params.AdGroupParams;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static outfox.ead.noah.util.datautil.DataUtil.getIdDruidColumnByType;

/**
 * 将自定义列数据合并到原有的固定列维度数据
 * <p>
 * 目前一共有三个地方需要合并自定义列数据：推广管理（系列，组，创意），数据报表详细数据（报表按小时、按天、汇总查询结果），数据报表合计（报表中首行的合计结果）
 * 推广管理，存储固定列数据结果的对象是 Map<String, Object> propertyToValuesMap
 * 数据报表详细数据，存储固定列数据结果的对象是 List<ReportBaseItem> reportBaseItemList
 * 数据报表合计，存储固定列数据结果的对象是 ReportTotal reportTotal
 * <p>
 * 本类通过getDayTypeIdToCustomColumnDataMap方法从druid的SDK_TABLE_V2表获取用户的自定义列统计数据，然后
 * 合并到propertyToValuesMap/reportBaseItemList/reportTotal中
 *
 * <AUTHOR>
 **/
@Service
public class MergeCommonColumnDataServiceImpl implements MergeCommonColumnDataService {

    private static final String SEPARATOR = "#";

    private static final String PROPERTY_CONSUMPTION = "consumption";

    private static final String PROPERTY_CLICK_NUM = "clickNum";

    @Autowired
    private CtActionDataQueryService ctActionDataQueryService;

    @Override
    public void getAndMergeCustomColumnDataToReportBaseItemList(List<ReportBaseItem> reportBaseItemList, Long sponsorId, Type type, Set<Long> typeIds,
                                                                Set<String> customColumnSet, long fromTime, long toTime, Granularity granularity) throws Exception {
        if (CollectionUtils.isEmpty(reportBaseItemList) || CollectionUtils.isEmpty(customColumnSet)) {
            return;
        }
        Map<String, Map<String, Long>> dayTypeIdToCustomDataMap = getDayTypeIdToCustomColumnDataMap
                (sponsorId, type, typeIds, customColumnSet, fromTime, toTime, granularity, true, false);
        addCustomColumnDataToReportBaseItemList(reportBaseItemList, dayTypeIdToCustomDataMap, customColumnSet, sponsorId);
    }

    @Override
    public void getAndMergeCustomColumnDataToReportTotal(ReportTotal reportTotal, Long sponsorId, Type type, Set<Long> typeIds,
                                                         Set<String> customColumnSet, long fromTime, long toTime, Granularity granularity) throws Exception {
        if (reportTotal == null || CollectionUtils.isEmpty(customColumnSet)) {
            return;
        }
        Map<String, Map<String, Long>> dayTypeIdToCustomDataMap = getDayTypeIdToCustomColumnDataMap
                (sponsorId, type, typeIds, customColumnSet, fromTime, toTime, granularity, false, false);
        Map<String, Long> customColumnDataTotalMap = new HashMap<>();
        if (MapUtils.isNotEmpty(dayTypeIdToCustomDataMap)) {
            customColumnDataTotalMap = dayTypeIdToCustomDataMap.values().iterator().next();
        }
        addCustomColumnDataToReportTotal(reportTotal, customColumnDataTotalMap, customColumnSet);
    }

    @Override
    public void getAndMergeCustomColumnDataToPropertyValuesMap(Map<String, Object> propertyToValuesMap, Long sponsorId, Type type, Set<Long> typeIds,
                                                               Set<String> customColumnSet, long fromTime, long toTime, Granularity granularity) throws Exception {
        if (MapUtils.isEmpty(propertyToValuesMap) || CollectionUtils.isEmpty(customColumnSet)) {
            return;
        }
        Map<String, Map<String, Long>> dayTypeIdToCustomDataMap = getDayTypeIdToCustomColumnDataMap
                (sponsorId, type, typeIds, customColumnSet, fromTime, toTime, granularity, true, false);
        // 非留存转化数据
        Set<String> normalCustomColumnSet = customColumnSet.stream().filter(col -> !CustomColumnEnum.isRetentionType(col)).collect(Collectors.toSet());
        // 留存转化数据
        Set<String> retentionCustomColumnSet = customColumnSet.stream().filter(CustomColumnEnum::isRetentionType).collect(Collectors.toSet());
        addCustomColumnDataToPropertyToValuesMap(propertyToValuesMap, type, dayTypeIdToCustomDataMap, normalCustomColumnSet, DateTime.getDate(fromTime) + " - " + DateTime.getDate(toTime));
        addCustomColumnDataToPropertyToValuesMap(propertyToValuesMap, type, dayTypeIdToCustomDataMap, retentionCustomColumnSet, DateTime.getDate(fromTime) + " - " + DateTime.getDate(toTime));
    }

    @Override
    public void getAndMergeCustomColumnDataToSlotList(List<ReportSlotItem> reportSlotItemList, Long sponsorId, Type type, Set<Long> typeIds, Set<String> customColumnSet,
                                                      long fromTime, long toTime) throws Exception {
        if (CollectionUtils.isEmpty(reportSlotItemList) || CollectionUtils.isEmpty(customColumnSet)) {
            return;
        }
        Map<String, Map<String, Long>> slotUdidToCustomDataMap = getDayTypeIdToCustomColumnDataMap
                (sponsorId, type, typeIds, customColumnSet, fromTime, toTime, Granularities.ALL, false, true);
        addCustomColumnDataToSlotStatDataList(reportSlotItemList, slotUdidToCustomDataMap, customColumnSet);
    }

    /**
     * 获取自定义列的统计数据
     *
     * @param sponsorId       账号id
     * @param type            统计数据的层级类型
     * @param typeIds         type对应的id集合，查询时的限定条件
     * @param customColumnSet 自定义列集合
     * @param fromTime        开始时间，yyyyMMdd
     * @param toTime          截止时间，yyyyMMdd
     * @param granularity     聚合方式
     * @param groupByTypeId   查询时是否对typeId进行groupBy
     * @return 键的形式为“day#typeId”或广告位udid，值为(commonColumnName, num)的map
     * @throws Exception
     */
    private Map<String, Map<String, Long>> getDayTypeIdToCustomColumnDataMap
    (Long sponsorId, Type type, Set<Long> typeIds, Set<String> customColumnSet, long fromTime, long toTime, Granularity granularity, boolean groupByTypeId, boolean groupBySlot) throws Exception {
        Future<List<Row>> orderAmountFuture = null;
        Future<List<Row>> ctActionFuture = null;
        Future<List<Row>> day1RetentionFuture = null, day3RetentionFuture = null, day7RetentionFuture = null, day14RetentionFuture = null, day30RetentionFuture = null;
        Future<List<Row>> clickConvertFuture = null;
        Future<List<Row>> activePurchaseNDaysAmountFuture = null;
        if (CustomColumnEnum.hasOrderAmountType(customColumnSet)) {
            Collection<String> orderAmountMetrics = CollectionUtils.intersection(CustomColumnEnum.ORDER_AMOUNT_METRICS_TYPE_KEYS, customColumnSet);
            orderAmountFuture = ctActionDataQueryService.queryOrderAmount(sponsorId, type, typeIds, fromTime, toTime, groupByTypeId, groupBySlot, granularity, orderAmountMetrics);
        }
        if (CustomColumnEnum.hasDownloadOrLandingpageType(customColumnSet)) {
            ctActionFuture = ctActionDataQueryService.queryCtAction(sponsorId, type, typeIds, fromTime, toTime, groupByTypeId, groupBySlot, granularity);
        }
        day1RetentionFuture = getRetentionCtFuture(sponsorId, type, typeIds, customColumnSet, fromTime, toTime, granularity,
                groupByTypeId, groupBySlot, CustomColumnEnum.DOWNLOAD_DAY1RETENTION, 1);
        day3RetentionFuture = getRetentionCtFuture(sponsorId, type, typeIds, customColumnSet, fromTime, toTime, granularity,
                groupByTypeId, groupBySlot, CustomColumnEnum.DOWNLOAD_RETEBTION3Days, 3);
        day7RetentionFuture = getRetentionCtFuture(sponsorId, type, typeIds, customColumnSet, fromTime, toTime, granularity,
                groupByTypeId, groupBySlot, CustomColumnEnum.DOWNLOAD_RETEBTION7Days, 7);
        day14RetentionFuture = getRetentionCtFuture(sponsorId, type, typeIds, customColumnSet, fromTime, toTime, granularity,
                groupByTypeId, groupBySlot, CustomColumnEnum.DOWNLOAD_RETEBTION14Days, 14);
        day30RetentionFuture = getRetentionCtFuture(sponsorId, type, typeIds, customColumnSet, fromTime, toTime, granularity,
                groupByTypeId, groupBySlot, CustomColumnEnum.DOWNLOAD_RETEBTION30Days, 30);

        if (customColumnSet != null && customColumnSet.contains(CustomColumnEnum.CLICK_CONVERT.getKey())) {
            clickConvertFuture = ctActionDataQueryService.queryClickConvert(sponsorId, type, typeIds, fromTime, toTime, groupByTypeId, groupBySlot, granularity);
        }
        activePurchaseNDaysAmountFuture = ctActionDataQueryService.queryActivePurchaseIntervalDays(sponsorId, type,
                typeIds, fromTime, toTime,groupByTypeId, groupBySlot, granularity, customColumnSet);

        Map<String, Map<String, Long>> dayTypeIdToCustomColumnDataMap = new HashMap<>();
        List<Row> orderAmountList = orderAmountFuture == null ? null : orderAmountFuture.get();
        setOrderAmountMetrics(type, customColumnSet, fromTime, toTime, granularity, groupBySlot, orderAmountList, dayTypeIdToCustomColumnDataMap);
        List<Row> ctActionFutureList = ctActionFuture == null ? null : ctActionFuture.get();
        if (CollectionUtils.isNotEmpty(ctActionFutureList)) {
            ctActionFutureList.forEach(row -> {
                String ctAction = DataUtil.getDimension(row, DruidBuilder.DruidCol.CONVERT_ACTION);
                if (!CustomColumnEnum.isRetentionAction(ctAction)) {
                    String key = buildKey(granularity, row, fromTime, toTime, type, groupBySlot, 0);
                    Map<String, Long> customColumnsMap = dayTypeIdToCustomColumnDataMap.computeIfAbsent(key, k -> new HashMap<>());
                    customColumnsMap.put(ctAction, row.getLongMetric(DruidBuilder.DruidCol.CONVERT_NUM.name));
                }
            });
        }
        getRetentionCtStat(type, fromTime, toTime, granularity, groupBySlot, day1RetentionFuture, dayTypeIdToCustomColumnDataMap, CustomColumnEnum.DOWNLOAD_DAY1RETENTION, 1);
        getRetentionCtStat(type, fromTime, toTime, granularity, groupBySlot, day3RetentionFuture, dayTypeIdToCustomColumnDataMap, CustomColumnEnum.DOWNLOAD_RETEBTION3Days, 3);
        getRetentionCtStat(type, fromTime, toTime, granularity, groupBySlot, day7RetentionFuture, dayTypeIdToCustomColumnDataMap, CustomColumnEnum.DOWNLOAD_RETEBTION7Days, 7);
        getRetentionCtStat(type, fromTime, toTime, granularity, groupBySlot, day14RetentionFuture, dayTypeIdToCustomColumnDataMap, CustomColumnEnum.DOWNLOAD_RETEBTION14Days, 14);
        getRetentionCtStat(type, fromTime, toTime, granularity, groupBySlot, day30RetentionFuture, dayTypeIdToCustomColumnDataMap, CustomColumnEnum.DOWNLOAD_RETEBTION30Days, 30);
        setActivatePurchaseNDaysMetrics(type, fromTime, toTime, granularity, groupBySlot, activePurchaseNDaysAmountFuture, dayTypeIdToCustomColumnDataMap, customColumnSet);
        List<Row> clickConvertFutureList = clickConvertFuture == null ? null : clickConvertFuture.get();
        if (CollectionUtils.isNotEmpty(clickConvertFutureList)) {
            clickConvertFutureList.forEach(row -> {
                String key = buildKey(granularity, row, fromTime, toTime, type, groupBySlot, 0);
                Map<String, Long> customColumnsMap = dayTypeIdToCustomColumnDataMap.computeIfAbsent(key, k -> new HashMap<>());
                customColumnsMap.put(DruidBuilder.DruidCol.CLICK_CONVERT.name, row.getLongMetric(DruidBuilder.DruidCol.CLICK_CONVERT.name));
            });
        }
        return dayTypeIdToCustomColumnDataMap;
    }

    private void setActivatePurchaseNDaysMetrics(Type type, long fromTime, long toTime, Granularity granularity, boolean groupBySlot,
                                                 Future<List<Row>> activatePurchaseNDaysAmountFuture, Map<String, Map<String, Long>> dayTypeIdToCustomColumnDataMap,
                                                 Set<String> customColumnSet) throws Exception{
        List<Row> rows = activatePurchaseNDaysAmountFuture.get();
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        rows.forEach(row -> {
            String activePurchaseIntervalDays = DataUtil.getDimension(row, DruidBuilder.DruidCol.ACTIVE_PURCHASE_INTERVAL_DAYS);
            if (StringUtils.isBlank(activePurchaseIntervalDays) || "null".equals(activePurchaseIntervalDays) || "-1".equals(activePurchaseIntervalDays)) {
                return;
            }
            int activePurchaseIntervalDaysInInteger = Integer.parseInt(activePurchaseIntervalDays);
            if (customColumnSet.contains(CustomColumnEnum.DOWNLOAD_ACTIVE_3DAYS_ORDER_AMOUNT.getKey()) && activePurchaseIntervalDaysInInteger < 3) {
                putActiveNDaysOrderAmount(CustomColumnEnum.DOWNLOAD_ACTIVE_3DAYS_ORDER_AMOUNT.getKey(), type, fromTime,
                        toTime, granularity, groupBySlot, dayTypeIdToCustomColumnDataMap, row, activePurchaseIntervalDaysInInteger);
            }
            if (customColumnSet.contains(CustomColumnEnum.DOWNLOAD_ACTIVE_7DAYS_ORDER_AMOUNT.getKey()) && activePurchaseIntervalDaysInInteger < 7) {
                putActiveNDaysOrderAmount(CustomColumnEnum.DOWNLOAD_ACTIVE_7DAYS_ORDER_AMOUNT.getKey(), type, fromTime,
                        toTime, granularity, groupBySlot, dayTypeIdToCustomColumnDataMap, row, activePurchaseIntervalDaysInInteger);
            }
        });
    }

    private void putActiveNDaysOrderAmount(String metricsName, Type type, long fromTime, long toTime, Granularity granularity,
                                           boolean groupBySlot, Map<String, Map<String, Long>> dayTypeIdToCustomColumnDataMap,
                                           Row row, Integer activePurchaseIntervalDays) {
        String key = buildKey(granularity, row, fromTime, toTime, type, groupBySlot, activePurchaseIntervalDays);
        Map<String, Long> customColumnsMap = dayTypeIdToCustomColumnDataMap.computeIfAbsent(key, value -> new HashMap<>());
        Long existValue = customColumnsMap.getOrDefault(metricsName, 0L);
        customColumnsMap.put(metricsName, row.getLongMetric(DruidBuilder.DruidCol.ORDER_AMOUNT.name) + existValue);
    }

    private static void setOrderAmountMetrics(Type type,
                                              Set<String> customColumnSet,
                                              long fromTime,
                                              long toTime,
                                              Granularity granularity,
                                              boolean groupBySlot,
                                              List<Row> orderAmountList,
                                              Map<String, Map<String, Long>> dayTypeIdToCustomColumnDataMap) {
        if (CollectionUtils.isNotEmpty(orderAmountList)) {
            orderAmountList.forEach(row -> {
                String key = DateUtil.getDayStringByGranularity(granularity, row, fromTime, toTime, 0) + SEPARATOR + row.getLongMetric(getIdDruidColumnByType(type).name);
                if (groupBySlot) {
                    key = row.getRaw(DruidBuilder.DruidCol.SLOT_ID.name).toString();
                }
                Map<String, Long> customColumnsMap = dayTypeIdToCustomColumnDataMap.computeIfAbsent(key, k -> new HashMap<>());
                if (customColumnSet.contains(CustomColumnEnum.DOWNLOAD_ORDERAMOUNT.getKey())) {
                    customColumnsMap.put(DruidBuilder.DruidCol.ORDER_AMOUNT.name, row.getLongMetric(DruidBuilder.DruidCol.ORDER_AMOUNT.name));
                }
                if (customColumnSet.contains(CustomColumnEnum.DOWNLOAD_FIRST_DAY_ORDER_AMOUNT.getKey())) {
                    customColumnsMap.put(DruidBuilder.DruidCol.FIRST_DAY_ORDER_AMOUNT.name, row.getLongMetric(DruidBuilder.DruidCol.FIRST_DAY_ORDER_AMOUNT.name));
                }
                if (customColumnSet.contains(CustomColumnEnum.DOWNLOAD_IN_24_HOURS_ORDER_AMOUNT.getKey())) {
                    customColumnsMap.put(DruidBuilder.DruidCol.IN_24_HOURS_ORDER_AMOUNT.name, row.getLongMetric(DruidBuilder.DruidCol.IN_24_HOURS_ORDER_AMOUNT.name));
                }

            });
        }
    }

    private Future<List<Row>> getRetentionCtFuture(Long sponsorId, Type type, Set<Long> typeIds, Set<String> customColumnSet,
                                                   long fromTime, long toTime, Granularity granularity, boolean groupByTypeId,
                                                   boolean groupBySlot, CustomColumnEnum columnEnum, int retentionDays) {
        Future<List<Row>> retentionFuture = null;
        if (customColumnSet != null && customColumnSet.contains(columnEnum.getKey())) {
            retentionFuture = ctActionDataQueryService.queryRetentionCtAction(sponsorId, type, typeIds,
                    DateUtil.getDateAfterNday(fromTime, retentionDays), DateUtil.getDateAfterNday(toTime, retentionDays), groupByTypeId, groupBySlot, granularity);
        }
        return retentionFuture;
    }

    /**
     * 获取留存转化数据
     *
     * @param type
     * @param fromTime
     * @param toTime
     * @param granularity
     * @param groupBySlot
     * @param retentionFuture
     * @param dayTypeIdToCustomColumnDataMap
     * @param columnEnum
     * @throws InterruptedException
     * @throws java.util.concurrent.ExecutionException
     */
    private void getRetentionCtStat(Type type, long fromTime, long toTime, Granularity granularity, boolean groupBySlot,
                                    Future<List<Row>> retentionFuture, Map<String, Map<String, Long>> dayTypeIdToCustomColumnDataMap,
                                    CustomColumnEnum columnEnum, int targetRetentionDays) throws InterruptedException, java.util.concurrent.ExecutionException {
        List<Row> retentionFutureList = retentionFuture == null ? null : retentionFuture.get();
        if (CollectionUtils.isNotEmpty(retentionFutureList)) {
            retentionFutureList.forEach(row -> {
                String ctAction = DataUtil.getDimension(row, DruidBuilder.DruidCol.CONVERT_ACTION);
                String retentionDaysCol = DataUtil.getDimension(row, DruidBuilder.DruidCol.RETENTION_DAYS);
                boolean retentionDaysMatched = Objects.toString(targetRetentionDays).equals(retentionDaysCol) || targetRetentionDays == 1;
                if (columnEnum.getCtActions().contains(ctAction) && retentionDaysMatched) {
                    String key = buildKey(granularity, row, fromTime, toTime, type, groupBySlot, targetRetentionDays);
                    Map<String, Long> customColumnsMap = dayTypeIdToCustomColumnDataMap.computeIfAbsent(key, k -> new HashMap<>());
                    Long oldValue = customColumnsMap.getOrDefault(ctAction, 0L);
                    customColumnsMap.put(ctAction, oldValue + row.getLongMetric(DruidBuilder.DruidCol.CONVERT_NUM.name));
                }
            });
        }
    }

    private String buildKey(Granularity granularity, Row row, long fromTime, long toTime, Type type, boolean groupBySlot, int offsetDays) {
        if (groupBySlot) {
            return row.getRaw(DruidBuilder.DruidCol.SLOT_ID.name).toString();
        } else {
            return DateUtil.getDayStringByGranularity(granularity, row, fromTime, toTime, offsetDays) + SEPARATOR + row.getLongMetric(getIdDruidColumnByType(type).name);
        }
    }

    /**
     * 推广管理-自定义列数据的合并：用于系列，组，创意的数据合并
     *
     * @param type                     类型，可取值系列，组，创意
     * @param propertyToValuesMap      结果map，键为数据的属性名称，值为多个数据组成的list
     * @param dayTypeIdToCustomDataMap 键为（日期#类型id）组成字符串, 值为(转化动作,动作对应的转换数)组成的map，待将数据合并到propertyToValuesMap中
     * @param customColumnSet          自定义列的集合
     * @param dayStr                   时间字符串，生成自定义列map的键的一部分，获取dayTypeIdToCustomDataMap的值时使用
     */
    private void addCustomColumnDataToPropertyToValuesMap(Map<String, Object> propertyToValuesMap, Type type,
                                                          Map<String, Map<String, Long>> dayTypeIdToCustomDataMap,
                                                          Set<String> customColumnSet, String dayStr) {
        for (String columnKey : customColumnSet) {
            // CustomColumnEnum.CONVERT的转化数据已经合并到propertyToValuesMap中，此处直接跳过即可
            if (columnKey.equals(CustomColumnEnum.CONVERT.getKey())) {
                continue;
            }
            List<Long> customColumnNum = new ArrayList<>();
            List<Double> customColumnCost = new ArrayList<>();
            List<Double> customColumnRate = new ArrayList<>();
            List<Double> customColumnRoi = new ArrayList<>();
            String camelIdCol = null;
            if (type == Type.CAMPAIGN) {
                camelIdCol = AdCampaignParams.AD_CAMPAIGN_ID;
            } else if (type == Type.GROUP) {
                camelIdCol = AdGroupParams.AD_GROUP_ID;
            } else {
                camelIdCol = AdContentParams.AD_CONTENT_ID;
            }
            List<Object> typeIds = (List<Object>) propertyToValuesMap.getOrDefault(camelIdCol, Collections.emptyList());
            getNumCostRateDataForTypeIds(customColumnNum, customColumnCost, customColumnRate, customColumnRoi, typeIds,
                    dayTypeIdToCustomDataMap, propertyToValuesMap, columnKey, dayStr);
            if (CustomColumnEnum.ORDER_AMOUNT_TYPE_KEYS.contains(columnKey)) {
                propertyToValuesMap.put(columnKey, customColumnNum);
                if (CustomColumnEnum.isOrderAmountWithTimeRangeType(columnKey)) {
                    propertyToValuesMap.put(columnKey + Constants.ROI, customColumnRoi);
                }
            } else {
                propertyToValuesMap.put(columnKey + Constants.CT_ACTION_NUM, customColumnNum);
                propertyToValuesMap.put(columnKey + Constants.CT_ACTION_COST, customColumnCost);
                propertyToValuesMap.put(columnKey + Constants.CT_ACTION_RATE, customColumnRate);
            }

        }
    }

    /**
     * 为typeId集合中的每个id获取其对应的事件个数，事件转化成本，事件转化率，
     * 并存储到customColumnNum，customColumnCost，customColumnRate中
     *
     * @param customColumnNum          事件个数map
     * @param customColumnCost         事件转化成本map
     * @param customColumnRate         事件转化率map
     * @param customColumnRoi
     * @param typeIds                  类型id的集合，可能为广告系列id，广告组id，创意id
     * @param dayTypeIdToCustomDataMap 键为（日期#类型id）组成字符串, 值为(转化动作,动作对应的转换数)组成的map
     * @param propertyToValuesMap      键为数据的属性名称，值为多个数据组成的list。本方法中需要用此map获取消费数，点击数，用来计算自定义列事件的转化成本，转化率
     * @param columnKey                自定义列
     * @param dayStr                   时间字符串，生成自定义列map的键的一部分，获取dayTypeIdToCustomDataMap的值时使用
     */
    private void getNumCostRateDataForTypeIds(List<Long> customColumnNum, List<Double> customColumnCost, List<Double> customColumnRate,
                                              List<Double> customColumnRoi, List<Object> typeIds, Map<String, Map<String, Long>> dayTypeIdToCustomDataMap,
                                              Map<String, Object> propertyToValuesMap, String columnKey, String dayStr) {
        if (typeIds.isEmpty()) {
            return;
        }
        for (int i = 0; i < typeIds.size(); i++) {
            Long ctActionNum = 0L;
            if (CustomColumnEnum.isActivatePurchaseIntervalType(columnKey)) {
                ctActionNum = dayTypeIdToCustomDataMap.getOrDefault(dayStr + SEPARATOR + typeIds.get(i), Collections.emptyMap()).getOrDefault(columnKey, 0L);
            } else {
                List<String> ctActions = CustomColumnEnum.getByKey(columnKey).getCtActions();
                for (String ctAction : ctActions) {
                    Long curCtActionNum = dayTypeIdToCustomDataMap.getOrDefault(dayStr + SEPARATOR + typeIds.get(i), Collections.emptyMap()).getOrDefault(ctAction, 0L);
                    ctActionNum += curCtActionNum;
                }
            }
            // 计算xx事件个数，xx事件转化成本，xx事件转化率；对于订单金额无需计算转化成本和转化率
            // 对于首日付费和24h内付费等指标还需要额外计算ROI
            if (CustomColumnEnum.ORDER_AMOUNT_TYPE_KEYS.contains(columnKey)) {
                customColumnNum.add(ctActionNum);
                if (CustomColumnEnum.ORDER_AMOUNT_TYPE_KEYS.contains(columnKey)) {
                    Double consumption = ((List<Double>) propertyToValuesMap.get(PROPERTY_CONSUMPTION)).get(i);
                    customColumnRoi.add(DspCommonStatUtil.doRoi(ctActionNum, consumption));
                }
            } else if (CustomColumnEnum.isRetentionType(columnKey)) {
                customColumnNum.add(ctActionNum);
                Double consumption = ((List<Double>) propertyToValuesMap.get(PROPERTY_CONSUMPTION)).get(i);
                Long curDayActiveNum = 0L;
                String activateColKey = "";
                if (CustomColumnEnum.isDownloadRetentionType(columnKey)) {
                    activateColKey = CustomColumnEnum.DOWNLOAD_ACTIVATE.getKey() + Constants.CT_ACTION_NUM;
                } else if (CustomColumnEnum.isWXRetentionType(columnKey)) {
                    activateColKey = CustomColumnEnum.WX_MINI_PROGRAM_ACTIVATE.getKey() + Constants.CT_ACTION_NUM;
                }
                if (StringUtils.isNotBlank(activateColKey)) {
                    List<Long> activateCtActionNum = (List<Long>) propertyToValuesMap.get(activateColKey);
                    if (CollectionUtils.isNotEmpty(activateCtActionNum)) {
                        curDayActiveNum = activateCtActionNum.get(i);
                    }
                }
                customColumnCost.add(DspCommonStatUtil.div(consumption, ctActionNum));
                customColumnRate.add(DspCommonStatUtil.getPercent(ctActionNum.doubleValue(), curDayActiveNum.doubleValue()));
            } else {
                customColumnNum.add(ctActionNum);
                Double consumption = ((List<Double>) propertyToValuesMap.get(PROPERTY_CONSUMPTION)).get(i);
                Long clickNum = ((List<Long>) propertyToValuesMap.get(PROPERTY_CLICK_NUM)).get(i);
                customColumnCost.add(DspCommonStatUtil.div(consumption, ctActionNum));
                customColumnRate.add(DspCommonStatUtil.getPercent(ctActionNum.doubleValue(), clickNum.doubleValue()));
            }
        }
    }

    /**
     * 数据报表-自定义列数据的合并：用于账号，系列，组，创意的合计数据合并
     *
     * @param reportTotal               数据报表合计数据
     * @param sourceCustomColumnDataMap 自定义列统计数据，待合并到reportTotal中
     * @param customColumnSet           自定义列的集合
     */
    private void addCustomColumnDataToReportTotal(ReportTotal reportTotal, Map<String, Long> sourceCustomColumnDataMap, Set<String> customColumnSet) {
        Map<String, Object> destCustomColumnDataMap = new HashMap<>();
        for (String columnKey : customColumnSet) {
            if (CustomColumnEnum.isRetentionType(columnKey)) {
                continue;
            }
            // CustomColumnEnum.CONVERT在当前版本之前为固定列，后改为自定义可选列，在ReportTotal存有其数据
            if (columnKey.equals(CustomColumnEnum.CONVERT.getKey())) {
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_NUM, reportTotal.getTotalConv());
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_COST, reportTotal.getTotalConvCost());
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_RATE, reportTotal.getTotalConvRate());
                continue;
            }
            Long ctActionNum = 0L;
            if (CustomColumnEnum.isActivatePurchaseIntervalType(columnKey)) {
                ctActionNum = sourceCustomColumnDataMap.getOrDefault(CustomColumnEnum.getByKey(columnKey).getKey(), 0L);
            } else {
                List<String> ctActions = CustomColumnEnum.getByKey(columnKey).getCtActions();
                for (String ctAction : ctActions) {
                    ctActionNum += sourceCustomColumnDataMap.getOrDefault(ctAction, 0L);
                }
            }
            // 计算xx事件个数，xx事件转化成本，xx事件转化率；对于订单金额无需计算转化成本和转化率
            // 对于首日付费和24h内付费还需要额外计算ROI
            if (CustomColumnEnum.ORDER_AMOUNT_TYPE_KEYS.contains(columnKey)) {
                destCustomColumnDataMap.put(columnKey, ctActionNum);
                if (CustomColumnEnum.ORDER_AMOUNT_TYPE_KEYS.contains(columnKey)) {
                    // 这里ctActionNum代表的实际上是订单金额
                    destCustomColumnDataMap.put(columnKey + Constants.ROI, DspCommonStatUtil.doRoi(ctActionNum, reportTotal.getTotalConsumption()));
                }
            } else {
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_NUM, ctActionNum);
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_COST, DspCommonStatUtil.div(reportTotal.getTotalConsumption(), ctActionNum));
                long dividend = CustomColumnEnum.isRetentionType(columnKey) ?
                        sourceCustomColumnDataMap.getOrDefault(CustomColumnEnum.DOWNLOAD_ACTIVATE.getKey(), 0L) : reportTotal.getTotalClick();
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_RATE, DspCommonStatUtil.getPercent(ctActionNum, dividend));
            }
        }
        for (String columnKey : customColumnSet) {
            if (CustomColumnEnum.isRetentionType(columnKey)) {
                Long ctActionNum = 0L;
                List<String> ctActions = CustomColumnEnum.getByKey(columnKey).getCtActions();
                for (String ctAction : ctActions) {
                    Long curCtActionNum = sourceCustomColumnDataMap.getOrDefault(ctAction, 0L);
                    ctActionNum += curCtActionNum;
                }
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_NUM, ctActionNum);
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_COST, DspCommonStatUtil.div(reportTotal.getTotalConsumption(), ctActionNum));
                long dividend = 0;
                if (CustomColumnEnum.isDownloadRetentionType(columnKey)) {
                    Object activeCtActionNum = destCustomColumnDataMap.get(CustomColumnEnum.DOWNLOAD_ACTIVATE.getKey() + Constants.CT_ACTION_NUM);
                    if (activeCtActionNum != null) {
                        dividend = (long) activeCtActionNum;
                    }
                } else if (CustomColumnEnum.isWXRetentionType(columnKey)) {
                    Object activeCtActionNum = destCustomColumnDataMap.get(CustomColumnEnum.WX_MINI_PROGRAM_ACTIVATE.getKey() + Constants.CT_ACTION_NUM);
                    if (activeCtActionNum != null) {
                        dividend = (long) activeCtActionNum;
                    }
                } else if (CustomColumnEnum.isOrderAmountWithTimeRangeType(columnKey)) {
                    destCustomColumnDataMap.put(columnKey + Constants.ROI, DspCommonStatUtil.doRoi(sourceCustomColumnDataMap.getOrDefault(columnKey, 0L), reportTotal.getTotalConsumption()));

                }
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_RATE, DspCommonStatUtil.getPercent(ctActionNum, dividend));
            }
        }
        reportTotal.setCustomColumnData(destCustomColumnDataMap);
    }

    /**
     * 数据报表-自定义列数据的合并：用于账号，系列，组，创意的详细数据合并
     *
     * @param reportBaseItemList       数据报表详细数据(账号、系列、组、创意层级)
     * @param dayTypeIdToCustomDataMap 键为（日期#类型id）组成字符串, 值为(转化动作,动作对应的转换数)组成的map，待将数据合并到propertyToValuesMap中
     * @param customColumnSet          自定义列的集合
     */
    private void addCustomColumnDataToReportBaseItemList(List<ReportBaseItem> reportBaseItemList,
                                                         Map<String, Map<String, Long>> dayTypeIdToCustomDataMap,
                                                         Set<String> customColumnSet, long sponsorId) {
        for (ReportBaseItem reportBaseItem : reportBaseItemList) {
            Map<String, Object> destCustomColumnDataMap = new HashMap<>();
            String key = reportBaseItem.getDay();
            if (reportBaseItem instanceof ReportContentItem) {
                key += SEPARATOR + ((ReportContentItem) reportBaseItem).getContentId();
            } else if (reportBaseItem instanceof ReportGroupItem) {
                key += SEPARATOR + ((ReportGroupItem) reportBaseItem).getGroupId();
            } else if (reportBaseItem instanceof ReportCampaignItem) {
                key += SEPARATOR + ((ReportCampaignItem) reportBaseItem).getCampaignId();
            } else {
                key += SEPARATOR + sponsorId;
            }
            Map<String, Long> sourceCustomColumnDataMap = dayTypeIdToCustomDataMap.getOrDefault(key, Collections.emptyMap());
            putNumCostRateDataToDestMap(destCustomColumnDataMap, sourceCustomColumnDataMap, reportBaseItem, customColumnSet);
            reportBaseItem.setCustomColumnData(destCustomColumnDataMap);
        }

    }

    /**
     * 将事件个数，事件转化成本，事件转化率存储到目标map中
     *
     * @param destCustomColumnDataMap   目标map，存储所需的自定义列结果
     * @param sourceCustomColumnDataMap 通过druid查询到的自定义列结果，按需获取组装数值后放入destCustomColumnDataMap中
     * @param reportBaseItem            固定列数据结果，在计算自定义列事件的转化成本，转化率时会用到reportBaseItem中的某些数据
     * @param customColumnSet           自定义列的集合
     */
    private void putNumCostRateDataToDestMap(Map<String, Object> destCustomColumnDataMap, Map<String, Long> sourceCustomColumnDataMap,
                                             ReportBaseItem reportBaseItem, Set<String> customColumnSet) {
        for (String columnKey : customColumnSet) {
            if (CustomColumnEnum.isRetentionType(columnKey)) {
                continue;
            }
            // CustomColumnEnum.CONVERT在当前版本之前为固定列，后改为自定义可选列，在ReportContentItem存有其数据,直接拷贝到destMap中即可
            if (columnKey.equals(CustomColumnEnum.CONVERT.getKey())) {
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_NUM, reportBaseItem.getConvertNum());
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_COST, reportBaseItem.getConvertCost());
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_RATE, reportBaseItem.getConvertRate());
                continue;
            }
            long ctActionNum = 0L;
            if (CustomColumnEnum.isActivatePurchaseIntervalType(columnKey)) {
                ctActionNum = sourceCustomColumnDataMap.getOrDefault(CustomColumnEnum.getByKey(columnKey).getKey(), 0L);
            } else {
                List<String> ctActions = CustomColumnEnum.getByKey(columnKey).getCtActions();
                for (String ctAction : ctActions) {
                    ctActionNum += sourceCustomColumnDataMap.getOrDefault(ctAction, 0L);
                }
            }
            // 计算xx事件个数，xx事件转化成本，xx事件转化率；对于订单金额无需计算转化成本和转化率
            // 对于首日付费和24h内付费还需要额外计算ROI
            if (CustomColumnEnum.ORDER_AMOUNT_TYPE_KEYS.contains(columnKey)) {
                destCustomColumnDataMap.put(columnKey, ctActionNum);
                if (CustomColumnEnum.isOrderAmountWithTimeRangeType(columnKey)) {
                    // 这里的ctActionNum实际上是金额
                    destCustomColumnDataMap.put(columnKey + Constants.ROI, DspCommonStatUtil.doRoi(ctActionNum, reportBaseItem.getConsumption()));
                }
            } else {
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_NUM, ctActionNum);
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_COST, DspCommonStatUtil.div(reportBaseItem.getConsumption(), ctActionNum));
                long dividend = reportBaseItem.getClickNum();
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_RATE, DspCommonStatUtil.getPercent(ctActionNum, dividend));
            }
        }
        // 计算留存转化数、转化成本、转化率指标
        for (String columnKey : customColumnSet) {
            if (CustomColumnEnum.isRetentionType(columnKey)) {
                Long ctActionNum = 0L;
                List<String> ctActions = CustomColumnEnum.getByKey(columnKey).getCtActions();
                for (String ctAction : ctActions) {
                    Long curCtActionNum = sourceCustomColumnDataMap.getOrDefault(ctAction, 0L);
                    ctActionNum += curCtActionNum;
                }
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_NUM, ctActionNum);
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_COST, DspCommonStatUtil.div(reportBaseItem.getConsumption(), ctActionNum));
                long dividend = 0;
                if (CustomColumnEnum.isDownloadRetentionType(columnKey)) {
                    Object activeCtActionNum = destCustomColumnDataMap.get(CustomColumnEnum.DOWNLOAD_ACTIVATE.getKey() + Constants.CT_ACTION_NUM);
                    if (activeCtActionNum != null) {
                        dividend = (long) activeCtActionNum;
                    }
                } else if (CustomColumnEnum.isWXRetentionType(columnKey)) {
                    Object activeCtActionNum = destCustomColumnDataMap.get(CustomColumnEnum.WX_MINI_PROGRAM_ACTIVATE.getKey() + Constants.CT_ACTION_NUM);
                    if (activeCtActionNum != null) {
                        dividend = (long) activeCtActionNum;
                    }
                }
                destCustomColumnDataMap.put(columnKey + Constants.CT_ACTION_RATE, DspCommonStatUtil.getPercent(ctActionNum, dividend));
            }
        }
    }

    private void addCustomColumnDataToSlotStatDataList(List<ReportSlotItem> reportSlotItemList, Map<String, Map<String, Long>> slotUdidToCustomDataMap, Set<String> customColumnSet) {
        for (ReportSlotItem reportSlotItem : reportSlotItemList) {
            Map<String, Object> destCustomColumnDataMap = new HashMap<>();
            String key = reportSlotItem.getSlotUdid();
            Map<String, Long> sourceCustomColumnDataMap = slotUdidToCustomDataMap.getOrDefault(key, Collections.emptyMap());
            putNumCostRateDataToDestMap(destCustomColumnDataMap, sourceCustomColumnDataMap, reportSlotItem, customColumnSet);
            reportSlotItem.setCustomColumnData(destCustomColumnDataMap);
        }
    }

}
