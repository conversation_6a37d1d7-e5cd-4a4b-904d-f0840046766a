package outfox.ead.noah.service.impl;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.dao.ScenarioDao;
import outfox.ead.noah.entity.Scenario;
import outfox.ead.noah.entity.models.ScenarioDto;
import outfox.ead.noah.service.ScenarioService;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/12/25
 */
@Service
public class ScenarioServiceImpl implements ScenarioService {

    private final ScenarioDao scenarioDao;

    @Autowired
    public ScenarioServiceImpl(ScenarioDao scenarioDao) {
        this.scenarioDao = scenarioDao;
    }

    @Override
    public List<ScenarioDto> getScenarioDtoList() {
        List<Scenario> scenarioList = scenarioDao.getAllValidScenarios();
        if (CollectionUtils.isEmpty(scenarioList)) {
            return Collections.emptyList();
        }
        return scenarioList.stream()
            .map(scenario -> ScenarioDto.of(scenario.getId(), scenario.getName()))
            .collect(Collectors.toList());
    }

}
