package outfox.ead.noah.service.impl;

import com.netflix.hystrix.HystrixCommand;
import com.simplaex.clients.druid.DruidClient;
import io.druid.data.input.Row;
import io.druid.query.Result;
import io.druid.query.groupby.GroupByQuery;
import io.druid.query.topn.TopNQuery;
import io.druid.query.topn.TopNResultValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.breaker.StatHystrixCommand;
import outfox.ead.noah.metrics.annotation.StatisticQueryTimer;
import outfox.ead.noah.service.DruidService;
import outfox.ead.noah.util.MetricsUtil;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static outfox.ead.noah.constants.Constants.STATISTIC_SYSTEM_DRUID;
import static outfox.ead.noah.util.params.AdGroupParams.DRUID_TIMEOUT_COVERAGE;
import static outfox.ead.noah.util.params.AdGroupParams.DRUID_TIMEOUT_NORMAL;

@Slf4j
@Service
public class DruidServiceImpl implements DruidService {

    @Autowired
    DruidClient druidClient;

    public static final Map<String, Object> DRUID_CONTEXT = new HashMap<String, Object>() {{
        put("source", "noah");
    }};

    @Override
    @StatisticQueryTimer(system = STATISTIC_SYSTEM_DRUID)
    public List<Row> groupByQuery(GroupByQuery query) {
        return runQuery(query, DRUID_TIMEOUT_NORMAL);
    }

    @Override
    @StatisticQueryTimer(system = STATISTIC_SYSTEM_DRUID)
    public List<Row> groupByQuery(GroupByQuery query, Integer timeout) {
        return runQuery(query, DRUID_TIMEOUT_COVERAGE);
    }

    @Override
    public List<Result<TopNResultValue>> topNQuery(TopNQuery query) {
        TopNQuery queryWithContext = setQueryContext(query);
        HystrixCommand<List<Result<TopNResultValue>>> druidQueryHystrixCommand =
                new HystrixCommand<List<Result<TopNResultValue>>>(StatHystrixCommand.getHystrixCommandSetter(DRUID_TIMEOUT_NORMAL)) {
                    @Override
                    protected List<Result<TopNResultValue>> run() throws Exception {
                        return druidClient.run(queryWithContext).toList();
                    }

                    @Override
                    protected List<Result<TopNResultValue>> getFallback() {
                        log.warn("query that touch off exception is {}", queryWithContext.toString());
                        log.warn("druidService fallback...", getExecutionException());
                        return Collections.emptyList();
                    }
                };
        return druidQueryHystrixCommand.execute();
    }

    private List<Row> runQuery(GroupByQuery query, Integer timeout) {
        GroupByQuery queryWithContext = setQueryContext(query);
        HystrixCommand<List<Row>> druidQueryHystrixCommand =
                new HystrixCommand<List<Row>>(StatHystrixCommand.getHystrixCommandSetter(timeout)) {
                    @Override
                    protected List<Row> run() throws Exception {
                        log.info("druid query conditions: {}", queryWithContext.toString());
                        return druidClient.run(queryWithContext).toList();
                    }

                    @Override
                    protected List<Row> getFallback() {
                        log.warn("query that touch off exception is {}", queryWithContext.toString());
                        log.warn("druidService fallback...", getExecutionException());
                        MetricsUtil.druidFallbackMeter();
                        return Collections.emptyList();
                    }
                };
        return druidQueryHystrixCommand.execute();
    }

    private TopNQuery setQueryContext(TopNQuery query) {
        return query.withOverriddenContext(DRUID_CONTEXT);
    }

    private GroupByQuery setQueryContext(GroupByQuery query) {
        return query.withOverriddenContext(DRUID_CONTEXT);
    }
}
