package outfox.ead.noah.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableSet;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.controller.argument.*;
import outfox.ead.noah.core.PageList;
import outfox.ead.noah.dao.*;
import outfox.ead.noah.dto.*;
import outfox.ead.noah.entity.*;
import outfox.ead.noah.entity.models.materiallibrary.*;
import outfox.ead.noah.entity.models.simple.SimpleTitleLibrary;
import outfox.ead.noah.exception.BadRequestException;
import outfox.ead.noah.exception.ForbiddenException;
import outfox.ead.noah.exception.NotFoundException;
import outfox.ead.noah.service.ImageService;
import outfox.ead.noah.service.MaterialLibraryService;
import outfox.ead.noah.util.DateTime;
import outfox.ead.noah.util.ExceptionUtil;
import outfox.ead.noah.util.ImageUploadResult;
import outfox.ead.noah.util.datautil.DataUtil;
import outfox.ead.noah.util.datautil.ImageUtil;
import outfox.ead.noah.util.logger.LogUtil;
import outfox.ead.noah.util.logger.OpType;
import outfox.ead.noah.util.logger.UserType;
import outfox.ead.noah.util.web.RequestUtil;
import outfox.ead.noah.util.web.SessionManager;
import sun.misc.BASE64Decoder;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;
import static outfox.ead.noah.constants.Constants.IMAGE_TEMPLATE_SORT_BY_TIME;
import static outfox.ead.noah.util.params.ImageLibraryParams.NO_GROUP_ID;


/**
 * @Date 2019/5/15 17:44
 * <AUTHOR>
 * @Description
 **/
@Service
@Slf4j
public class MaterialLibraryServiceImpl implements MaterialLibraryService {

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageLibraryDao imageLibraryDao;

    @Autowired
    private MimeUploadHistoryDao mimeUploadHistoryDao;

    @Autowired
    private TitleLibraryDao titleLibraryDao;

    @Autowired
    private ImageLibraryGroupDao imageLibraryGroupDao;

    @Autowired
    private ImageLibraryGroupImagesDao imageLibraryGroupImagesDao;

    @Autowired
    private ImageTemplateRelDao imageTemplateRelDao;

    @Autowired
    private ImageTemplateTagDao templateTagDao;

    @Autowired
    private ImageTemplateTagsMappingDao imageTemplateTagsMappingDao;

    @Autowired
    private ImageLibraryTagsMappingDao imageLibraryTagsMappingDao;

    @Autowired
    private SdkElementDao sdkElementDao;

    @Autowired
    private TransportMaterialWhiteListDao transportMaterialWhiteListDao;

    @Autowired
    private SponsorDao sponsorDao;

    private static final Gson GSON = new Gson();


    @Override
    public int saveImage(Long sponsorId, List<String> mimeIds, Long imageGroupId) {

        List<MimeUploadHistory> mimeUploadHistories = mimeUploadHistoryDao.getByMimeIdsWithUniqueName(mimeIds, sponsorId);
        List<ImageLibrary> repeatedImages = imageLibraryDao.getImagesByMimeIds(mimeIds, sponsorId);
        Map<String, ImageLibrary> mimeIdMap = repeatedImages
                .stream()
                .collect(Collectors.toMap(ImageLibrary::getMimeId, Function.identity(), (oldVal, newVal) -> oldVal));
        Set<ImageLibrary> saveOrUpdateImages = new HashSet<>();

        mimeUploadHistories.forEach(mimeUploadHistory -> {
            String mimeId = mimeUploadHistory.getMimeId();
            if (mimeIdMap.containsKey(mimeId) && sponsorId.equals(mimeIdMap.get(mimeId).getSponsorId())) {
                //更新素材库图片创建时间，更新名字
                ImageLibrary image = mimeIdMap.get(mimeId);
                image.setImageName(getImageName(mimeUploadHistory.getMimeName()));
                image.setCreateTime(new Timestamp(System.currentTimeMillis()));
                saveOrUpdateImages.add(image);
            } else {
                //批量插入素材库
                ImageLibrary image = new ImageLibrary();
                image.setSponsorId(sponsorId);
                //仅保存不带扩展名的图片名字
                image.setImageName(getImageName(mimeUploadHistory.getMimeName()));
                image.setImageWidth(mimeUploadHistory.getMimeWidth());
                image.setImageHeight(mimeUploadHistory.getMimeHeight());
                image.setSource(Constants.IMAGE_LIBRARY_SOURCE_UPLOAD);
                image.setImageSrc(mimeUploadHistory.getMimeSrc());
                image.setAspectRatio(DataUtil.minScale(mimeUploadHistory.getMimeWidth(), mimeUploadHistory.getMimeHeight()));
                image.setMimeId(mimeId);
                image.setCreateTime(new Timestamp(System.currentTimeMillis()));
                saveOrUpdateImages.add(image);
            }
        });
        imageLibraryDao.batchInsertOrUpdateImages(saveOrUpdateImages);
        if (!imageGroupId.equals(NO_GROUP_ID)) {
            saveImageGroup(sponsorId, mimeIds, imageGroupId);
        }
        return repeatedImages.size();
    }

    private void saveImageGroup(Long sponsorId, List<String> mimeIds, Long imageGroupId) {
        List<ImageLibrary> images = imageLibraryDao.getImagesByMimeIds(mimeIds, sponsorId);
        List<ImageLibraryGroupImages> existingImages = imageLibraryGroupImagesDao.getByImageGroupId(imageGroupId);
        Set<Long> existingImageIds = existingImages.stream().map(ImageLibraryGroupImages::getImageId).collect(Collectors.toSet());
        List<ImageLibraryGroupImages> imageLibraryGroupImages = new ArrayList<>();
        images.forEach(image -> {
            if (!existingImageIds.contains(image.getId())) {
                ImageLibraryGroupImages imageLibraryGroupImage = new ImageLibraryGroupImages();
                imageLibraryGroupImage.setSponsorId(sponsorId);
                imageLibraryGroupImage.setImageId(image.getId());
                imageLibraryGroupImage.setImageLibraryGroupId(imageGroupId);
                imageLibraryGroupImages.add(imageLibraryGroupImage);
            }
        });
        imageLibraryGroupImagesDao.insertItems(imageLibraryGroupImages);
    }

    /**
     * 获取不带图片扩展名的图片名字
     *
     * @param mimeSrc
     * @return
     */
    private String getImageName(String mimeSrc) {
        return mimeSrc.substring(0, mimeSrc.lastIndexOf('.'));
    }

    @Override
    public Boolean checkAccess(Long sponsorId, String ids) {
        Set<Long> imageIds = Arrays.asList(StringUtils.split(ids, ",")).stream().map(Long::parseLong).collect(Collectors.toSet());
        List<ImageLibrary> image = imageLibraryDao.getByIds(sponsorId, imageIds);
        return image.size() == imageIds.size();
    }

    @Override
    public void editImageName(Long sponsorId, Long imageId, String imageName) {
        if (!checkAccess(sponsorId, imageId.toString())) {
            throw new BadRequestException("该广告主无权访问该图片");
        }
        ImageLibrary image = imageLibraryDao.getById(imageId);
        image.setImageName(imageName);
        imageLibraryDao.saveOrUpdateImage(image);
    }

    @Override
    public void deleteByImageIds(Long sponsorId, String ids, Long imageGroupId) {
        if (!checkAccess(sponsorId, ids)) {
            throw new BadRequestException("该广告主无权访问部分id");
        }
        Set<Long> imageIds = Arrays.asList(StringUtils.split(ids, ",")).stream().map(Long::parseLong).collect(Collectors.toSet());
        if (imageGroupId == null) {
            //在全部分组下删除，需删除图片，及删除图片与组对应关系
            imageLibraryGroupImagesDao.deleteItemsByImageIds(sponsorId, imageIds);
        } else if (!NO_GROUP_ID.equals(imageGroupId)) {
            //删除图片分组中的图片，若只在当前分组存在，则删除图片，否则仅删除图片与组对应关系
            imageLibraryGroupImagesDao.deleteItemsByGroupIdsAndImageIds(sponsorId, ImmutableSet.of(imageGroupId), imageIds);
            List<ImageLibraryGroupImages> imageLibraryGroupImages = imageLibraryGroupImagesDao.getByImageIdGroupIds(sponsorId, new HashSet<>(), imageIds);
            Set<Long> noDeletedImageIds = imageLibraryGroupImages.stream().map(ImageLibraryGroupImages::getImageId).collect(Collectors.toSet());
            imageIds.removeAll(noDeletedImageIds);
        }
        imageLibraryTagsMappingDao.removeByImageIds(new ArrayList<>(imageIds));
        imageLibraryDao.deleteByIds(sponsorId, imageIds);
    }

    @Override
    public List<ImageLibraryListDto> pageImagesByImageDetails(Long sponsorId, ImagesQueryConditions conditions) {
        // 这里需要判断是不是空的 有些老数据是没有标签的
        List<ImageLibraryListDto> imageLibraryListDtos = imageLibraryDao.pageByImageDetails(sponsorId, conditions);
        //拿到所有相关的关联关系
        List<ImageLibraryTagsMapping> tagsMappingByImageIds = imageLibraryTagsMappingDao.getTagsMappingByImageIds(
                imageLibraryListDtos.stream().map(ImageLibraryListDto::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(tagsMappingByImageIds)) {
            //没有带标签的图片 跳过
            return imageLibraryListDtos;
        }
        //完整的广告位标签
        List<ImageTemplateTag> topLayerByType = templateTagDao.getTopLayerIncludeHiddenByType(Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT);
        List<ImageTemplateTag> secLayer = new ArrayList<>();
        topLayerByType.forEach(
                slotTagItem -> secLayer.addAll(templateTagDao.getImageTemplateTagsByParentIds(Collections.singletonList(slotTagItem.getId()))));
        //构造出Map方便处理
        Map<Long, ImageTemplateTag> topLayerMap = topLayerByType.stream().collect(Collectors.toMap(ImageTemplateTag::getId, o -> o));

        Map<Long, ImageTemplateTag> secLayerMap = secLayer.stream().collect(Collectors.toMap(ImageTemplateTag::getId, o -> o));
        imageLibraryListDtos.forEach(item -> {
            //找到与当前item关联的关系 如果没有关联关系就不管他 一张图片能且最多只能归属于一个一级标签
            List<ImageLibraryTagsMapping> mappings = tagsMappingByImageIds.stream().filter(tagsMapping -> tagsMapping.getImageId().equals(item.getId())).collect(Collectors.toList());
            //mapping -> subTag -> parentTag
            if (CollectionUtils.isNotEmpty(mappings)) {
                item.setAppName(topLayerMap.get(secLayerMap.get(mappings.get(0).getTagId()).getParentId()).getTagName());
                List<String> slotNames = new ArrayList<>();
                List<Long> tagIds = new ArrayList<>();
                mappings.forEach(mapping -> {
                    slotNames.add(secLayerMap.get(mapping.getTagId()).getTagName());
                    tagIds.add(secLayerMap.get(mapping.getTagId()).getId());
                });
                item.setSlotNames(slotNames);
                item.setTagIds(tagIds);
            }
        });
        return imageLibraryListDtos;
    }

    @Override
    public String getEffectiveAspectRatiosInAdContent(Long sponsorId, String aspectRatios) {
        List<String> ratios = Arrays.asList(StringUtils.split(aspectRatios, ","));
        List<String> legalRatios = new ArrayList<>();
        ratios.forEach(ratio -> {
            legalRatios.add(DataUtil.minScale(Integer.valueOf(StringUtils.split(ratio, ":")[0]),
                    Integer.valueOf(StringUtils.split(ratio, ":")[1])));
        });
        List<ImageLibrary> images = imageLibraryDao.getImagesByAspectRatios(sponsorId, legalRatios);
        Set<String> legalRatiosSet = new HashSet<>();
        images.forEach(image -> {
            legalRatiosSet.add(image.getAspectRatio());
        });
        return StringUtils.join(legalRatiosSet, ",");
    }

    @Override
    public int countImagesByImageDetails(Long sponsorId, ImagesQueryConditions conditions) {
        conditions.setOffset(-1);
        conditions.setLimit(-1);
        return imageLibraryDao.pageByImageDetails(sponsorId, conditions).size();
    }

    @Override
    public int batchInsertOrUpdateTitles(Long sponsorId, Set<String> titles) {
        List<TitleLibrary> repeatedTitlesInLibrary = getByTitleNames(sponsorId, titles);
        Map<String, TitleLibrary> repeatedTitleName = repeatedTitlesInLibrary
                .stream()
                .collect(Collectors.toMap(TitleLibrary::getTitle, Function.identity(), (oldVal, newVal) -> oldVal));
        Set<TitleLibrary> titleLibraries = new HashSet<>();
        titles.forEach(title -> {
            TitleLibrary titleLibrary = new TitleLibrary();
            if (repeatedTitleName.containsKey(title)) {
                titleLibrary = repeatedTitleName.get(title);
                titleLibrary.setCreateTime(new Timestamp(System.currentTimeMillis()));
                titleLibrary.setLastModTime(new Timestamp(System.currentTimeMillis()));
            } else {
                titleLibrary.setTitle(title);
                titleLibrary.setTitleLength(DataUtil.getTitleLength(title));
                titleLibrary.setSponsorId(sponsorId);
                titleLibrary.setCreateTime(new Timestamp(System.currentTimeMillis()));
                titleLibrary.setLastModTime(new Timestamp(System.currentTimeMillis()));
            }
            titleLibraries.add(titleLibrary);
        });
        titleLibraryDao.batchInsertOrUpdateTitles(titleLibraries);
        return repeatedTitleName.size();
    }

    @Override
    public List<TitleLibrary> getByTitleNames(Long sponsorId, Set<String> titleNames) {
        return titleLibraryDao.getByTitleNames(sponsorId, titleNames);
    }

    @Override
    public Boolean editTitle(Long sponsorId, Long titleId, String titleName) {
        TitleLibrary title = checkAccessTitles(sponsorId, new HashSet<>(Collections.singletonList(titleId))).get(0);
        List<TitleLibrary> titleByName = titleLibraryDao.getByTitleNames(sponsorId, new HashSet<>(Collections.singletonList(titleName)));
        if (titleByName.stream().filter(x -> !x.getId().equals(titleId)).collect(Collectors.toList()).size() > 0) {
            return false;
        } else {
            title.setTitle(titleName);
            title.setTitleLength(DataUtil.getTitleLength(titleName));
            title.setLastModTime(new Timestamp(System.currentTimeMillis()));
            titleLibraryDao.saveOrUpdateTitle(title);
            return true;
        }
    }

    @Override
    public void deleteByTitleId(Long sponsorId, Long titleId) {
        checkAccessTitles(sponsorId, new HashSet<>(Collections.singletonList(titleId)));
        titleLibraryDao.deleteById(titleId);
    }

    private List<TitleLibrary> checkAccessTitles(Long sponsorId, Set<Long> titleIds) {
        List<TitleLibrary> titlesById = titleLibraryDao.getByIds(titleIds);
        if (CollectionUtils.isEmpty(titlesById)) {
            throw new ForbiddenException("该标题ID不存在");
        }
        titlesById.forEach(title -> {
            if (!sponsorId.equals(title.getSponsorId())) {
                throw new ForbiddenException("该广告主无权访问该标题");
            }
        });
        return titlesById;
    }

    @Override
    public List<SimpleTitleLibrary> getSimpleTitlesByConditions(Long sponsorId, TitlesQueryConditions titlesQueryConditions) {
        List<TitleLibrary> titleLibraryList = titleLibraryDao.getByConditions(sponsorId, titlesQueryConditions);
        List<SimpleTitleLibrary> results = new ArrayList<>();
        titleLibraryList.forEach(titleLibrary -> {
            SimpleTitleLibrary simpleTitleLibrary = new SimpleTitleLibrary();
            simpleTitleLibrary.setTitleId(titleLibrary.getId());
            simpleTitleLibrary.setTitleName(titleLibrary.getTitle());
            simpleTitleLibrary.setCreateTime(DataUtil.formatTime(titleLibrary.getCreateTime(), "yyyy-MM-dd"));
            simpleTitleLibrary.setLastModTime(DataUtil.formatTime(titleLibrary.getLastModTime(), "yyyy-MM-dd"));
            results.add(simpleTitleLibrary);
        });
        return results;
    }

    @Override
    public int countTitlesByConditions(Long sponsorId, TitlesQueryConditions titlesQueryConditions) {
        titlesQueryConditions.setLimit(-1);
        titlesQueryConditions.setOffset(-1);
        return titleLibraryDao.getByConditions(sponsorId, titlesQueryConditions).size();
    }

    @Override
    public ImageUploadResult handleFileItem(long sponsorId, String imageName, String imageFileData) throws Exception {
        BASE64Decoder decoder = new BASE64Decoder();
        //Base64解码
        byte[] b = decoder.decodeBuffer(imageFileData);
        for (int i = 0; i < b.length; ++i) {
            if (b[i] < 0) {//调整异常数据
                b[i] += 256;
            }
        }
        //生成jpeg图片
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem("image", "text/plain", true, imageName);
        OutputStream fileout = item.getOutputStream();
        fileout.write(b);
        fileout.flush();
        fileout.close();

        byte[] imageData = item.get();
        if (!ImageUtil.isValidLength(imageData.length)) {
            return new ImageUploadResult(null, imageName,
                    String.format("文件大小超过限制(%dKB)",
                            ImageUtil.IMAGE_MAX_SIZE / 1024));
        }
        return imageService.handleFileItem(sponsorId, imageName, imageFileData);
    }

    @Override
    public ImageLibraryGroup createImageGroup(Long sponsorId, String imageName) {
        ImageLibraryGroup group = new ImageLibraryGroup();
        group.setSponsorId(sponsorId);
        group.setImageGroupName(imageName);
        return imageLibraryGroupDao.saveImageLibraryGroup(group);
    }

    @Override
    public void deleteImageGroups(Long sponsorId, String imageGroupIds) {
        Set<Long> imageGroupIdSet = Arrays.asList(StringUtils.split(imageGroupIds, ",")).stream().map(Long::parseLong).collect(Collectors.toSet());
        List<ImageLibraryGroupImages> imageLibraryGroupImages = imageLibraryGroupImagesDao.getByImageIdGroupIds(sponsorId, imageGroupIdSet, new HashSet<>());
        Map<Long, Set<Long>> imageIdDeletedGroupIdsMap = imageLibraryGroupImages.stream().
                collect(groupingBy(ImageLibraryGroupImages::getImageId, Collectors.mapping(ImageLibraryGroupImages::getImageLibraryGroupId, Collectors.toSet())));
        Set<Long> imageIdsSet = imageIdDeletedGroupIdsMap.keySet();
        List<ImageLibraryGroupImages> imagesInAllGroup = imageLibraryGroupImagesDao.getByImageIdGroupIds(sponsorId, new HashSet<>(), imageIdsSet);
        Map<Long, Set<Long>> imageIdAllGroupIdsMap = imagesInAllGroup.stream().
                collect(groupingBy(ImageLibraryGroupImages::getImageId, Collectors.mapping(ImageLibraryGroupImages::getImageLibraryGroupId, Collectors.toSet())));

        Set<Long> deletedImageIds = new HashSet<>();
        imageIdDeletedGroupIdsMap.forEach((imageId, groups) -> {
            if (groups.containsAll(imageIdAllGroupIdsMap.get(imageId))) {
                deletedImageIds.add(imageId);
            }
        });

        imageLibraryDao.deleteByIds(sponsorId, deletedImageIds);
        imageLibraryGroupImagesDao.deleteItemsByGroupIdsAndImageIds(sponsorId, imageGroupIdSet, new HashSet<>());
        imageLibraryGroupDao.deleteImageGroups(sponsorId, imageGroupIdSet);
    }

    @Override
    public ImageLibraryGroup editImageGroupName(Long imageGroupId, String imageGroupName) {
        ImageLibraryGroup group = imageLibraryGroupDao.getByImageGroupId(imageGroupId);
        group.setImageGroupName(imageGroupName);
        return imageLibraryGroupDao.saveImageLibraryGroup(group);
    }

    @Override
    public void moveImagesToAnotherGroup(Long sponsorId, String imageIdsStr, Long fromImageGroupId, String toImageGroupIdsStr) {
        Set<Long> imageIds = DataUtil.toLongs(imageIdsStr);
        Set<Long> toImageGroupIds = DataUtil.toLongs(toImageGroupIdsStr);
        List<ImageLibraryGroupImages> existedItems = imageLibraryGroupImagesDao.getByImageIdGroupIds(sponsorId, toImageGroupIds, imageIds);
        Map<Long, Set<Long>> groupIdImageIdsMap = existedItems.stream().
                collect(groupingBy(ImageLibraryGroupImages::getImageLibraryGroupId, Collectors.mapping(ImageLibraryGroupImages::getImageId, Collectors.toSet())));
        List<ImageLibraryGroupImages> items = new ArrayList<>();
        toImageGroupIds.forEach(imageGroupId -> {
            imageIds.forEach(imageId -> {
                if (!groupIdImageIdsMap.getOrDefault(imageGroupId, new HashSet<>()).contains(imageId)) {
                    ImageLibraryGroupImages item = new ImageLibraryGroupImages();
                    item.setImageLibraryGroupId(imageGroupId);
                    item.setImageId(imageId);
                    item.setSponsorId(sponsorId);
                    items.add(item);
                }
            });
        });
        if (ObjectUtils.notEqual(fromImageGroupId, NO_GROUP_ID) && !toImageGroupIds.contains(fromImageGroupId)) {
            imageLibraryGroupImagesDao.deleteItemsByImageGroupId(sponsorId, fromImageGroupId, imageIds);
        }
        imageLibraryGroupImagesDao.insertItems(items);
    }

    @Override
    public List<ImageLibraryGroup> getAllImageGroups(Long sponsorId) {
        return imageLibraryGroupDao.getAllBySponsorId(sponsorId);
    }

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ImageTemplateDao imageTemplateDao;
    @Autowired
    private ImageTemplateConfigurationDao configurationDao;


    @Override
    public Long saveImageTemplate(Long sponsorId, ImageTemplateReqVo reqVO) {
        String configuration = reqVO.getConfiguration();
        try {
            TemplateUrlDto templateUrlDto = objectMapper.readValue(configuration, TemplateUrlDto.class);
            ImageTemplate imageTemplate = new ImageTemplate();
            imageTemplate.setTemplateName(reqVO.getName());
            imageTemplate.setPreviewUrl(templateUrlDto.getPreview());
            imageTemplate.setThumbnailUrl(templateUrlDto.getThumbnail());
            imageTemplate.setCreateUserId(sponsorId);
            imageTemplateDao.saveTemplate(imageTemplate);

            ImageTemplateConfiguration configurationDO = new ImageTemplateConfiguration();
            configurationDO.setTemplateConfig(configuration);
            configurationDO.setTemplateId(imageTemplate.getId());
            configurationDao.saveConfiguration(configurationDO);
            //现在只有媒体和行业可以多选，素材类型只能是单选的
            List<String> slotTagSplit = Arrays.asList(reqVO.getSlotTagId().trim().split(","));
            List<String> industrySplit = Arrays.asList(reqVO.getIndustryTagId().trim().split(","));

            List<String> allTagsId = new ArrayList<>();
            allTagsId.addAll(slotTagSplit);
            allTagsId.add(String.valueOf(reqVO.getTemplateTagId()));
            allTagsId.addAll(industrySplit);

            Map<Long, ImageTemplateTag> imageTemplateTagByIds = templateTagDao.getImageTemplateTagByIds(allTagsId.stream().map(Long::parseLong).collect(Collectors.toList())).stream().collect(Collectors.toMap(ImageTemplateTag::getId, o -> o));
            //需要构建的关系
            List<ImageTemplateTagsMapping> imageTemplateTagsMappingsBuffer = new ArrayList<>();
            //需要恢复的TagId
            List<Long> recoverTagsId = new ArrayList<>();

            slotTagSplit.forEach(slotTagId -> {
                imageTemplateTagsMappingsBuffer.add(genImageTemplateTagsMapping(imageTemplate.getId(), Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT, Long.parseLong(slotTagId)));
                if (imageTemplateTagByIds.get(Long.parseLong(slotTagId)).getIsHidden().equals(1)) {
                    recoverTagsId.add(Long.parseLong(slotTagId));
                }
            });
            imageTemplateTagsMappingsBuffer.add(genImageTemplateTagsMapping(imageTemplate.getId(), Constants.IMAGE_LIBRARY_TAG_TYPE_TEMPLATE, reqVO.getTemplateTagId()));
            if (imageTemplateTagByIds.get(reqVO.getTemplateTagId()).getIsHidden().equals(1)) {
                recoverTagsId.add(reqVO.getTemplateTagId());
            }
            industrySplit.forEach(industryTagId -> {
                imageTemplateTagsMappingsBuffer.add(genImageTemplateTagsMapping(imageTemplate.getId(), Constants.IMAGE_LIBRARY_TAG_TYPE_INDUSTRY, Long.parseLong(industryTagId)));
                if (imageTemplateTagByIds.get(Long.parseLong(industryTagId)).getIsHidden().equals(1)) {
                    recoverTagsId.add(Long.parseLong(industryTagId));
                }
            });
            //操作数据库
            imageTemplateTagsMappingDao.batchSaveOrUpdate(imageTemplateTagsMappingsBuffer);
            templateTagDao.batchRecoverTag(recoverTagsId);
            return imageTemplate.getId();
        } catch (IOException ioException) {
            log.error("反序列化模板configuration失败,{}", ioException.getMessage());
            throw new BadRequestException();
        }

    }

    private ImageTemplateTagsMapping genImageTemplateTagsMapping(Long templateId, Integer tagType, Long tagId) {
        ImageTemplateTagsMapping imageTemplateTagsMapping = new ImageTemplateTagsMapping();
        imageTemplateTagsMapping.setImageTemplateId(templateId);
        imageTemplateTagsMapping.setTagType(tagType);
        imageTemplateTagsMapping.setTagId(tagId);
        return imageTemplateTagsMapping;
    }

    @Override
    public PageList<ImageTemplateDto> pageImageTemplate(Long sponsorId, int offset, int limit, String sort, Long slotTagId, Long templateTagId, Long industryTagId) {
        Long count = imageTemplateDao.count(slotTagId, templateTagId, industryTagId);
        List<ImageTemplateDto> dataList = new ArrayList<>();
        switch (sort) {
            case Constants.IMAGE_TEMPLATE_SORT_BY_HEAT:
                dataList = imageTemplateDao.pageByHeat(sponsorId, offset, limit, slotTagId, templateTagId, industryTagId);
                break;
            case IMAGE_TEMPLATE_SORT_BY_TIME:
                dataList = imageTemplateDao.pageByTime(sponsorId, offset, limit, slotTagId, templateTagId, industryTagId);
                break;
            default:
                break;
        }
        if (count != 0) {
            List<Long> imageTemplateIds = dataList.stream().map(ImageTemplateDto::getId).collect(Collectors.toList());
            Map<Long, ImageTemplateTagsMapping> collect = imageTemplateTagsMappingDao.getTemplateTagByTemplateIds(imageTemplateIds).stream().collect(toMap(ImageTemplateTagsMapping::getImageTemplateId, o -> o));
            //templateId -> ImageTemplateConfiguration
            Map<Long, ImageTemplateConfiguration> imageTemplateConfigurationMap = configurationDao.getByTemplateIds(imageTemplateIds).stream().collect(toMap(ImageTemplateConfiguration::getTemplateId, o -> o));
            //patch able
            dataList.forEach(item -> {
                ImageTemplateTagsMapping templateTagByTemplateId = collect.get(item.getId());
                if (templateTagByTemplateId == null || templateTagByTemplateId.getTagId() == Constants.TEMPLATE_TAG_TEMPLATE_TYPE_DYNAMIC) {
                    item.setIsEditable(1);
                } else {
                    item.setIsEditable(0);
                }
                ImageTemplateConfigElements imageTemplateConfigElements = GSON.fromJson(imageTemplateConfigurationMap.get(item.getId()).getTemplateConfig(), ImageTemplateConfigElements.class);
                item.setHeight(imageTemplateConfigElements.getHeight());
                item.setWidth(imageTemplateConfigElements.getWidth());
            });
        }
        //填入模板的宽高信息
        return PageList.of(count, dataList);
    }

    @Override
    public ImageTemplateDetailDto getImageTemplateById(Long templateId) {
        return imageTemplateDao.findDetailById(templateId);
    }

    @Override
    public void removeTemplate(Long sponsorId, Long templateId) {
        ImageTemplate imageTemplate = imageTemplateDao.findById(templateId);
        if (imageTemplate == null) {
            throw new NotFoundException();
        }
        imageTemplateDao.delete(sponsorId, templateId);
        checkTagMappingRelOnLessTagAction(templateId);
    }

    @Transactional()
    @Override
    public MaterialSaveResult saveImageFromTemplate(Long sponsorId, List<ImageLibraryReqVo> imageLibraryReqVoList, Long imageGroupId) {
        //判断是否重复上传的mime
        Map<String, ImageLibraryReqVo> mimeMap = new HashMap<>();
        StringBuilder illegalUrlStrBuilder = new StringBuilder();
        imageLibraryReqVoList.forEach(reqVo -> {
            String url = reqVo.getUrl();
            if (StringUtils.isNotBlank(DataUtil.getMimeId(url))) {
                mimeMap.put(DataUtil.getMimeId(url), reqVo);
            }
        });
        ArrayList<String> mimeIds = new ArrayList<>(mimeMap.keySet());
        //使用mimeId查询上传历史记录
        List<MimeUploadHistory> legalMimeList = mimeUploadHistoryDao.getByMimeIdsWithUniqueName(mimeIds, sponsorId);
        Set<String> legalUrls = legalMimeList.stream().map(MimeUploadHistory::getMimeSrc).collect(Collectors.toSet());
        //找出不合法的url
        imageLibraryReqVoList.forEach(reqVo -> {
            if (!legalUrls.contains(reqVo.getUrl())) {
                illegalUrlStrBuilder.append(reqVo.getUrl()).append(",");
                String illegalMimeId = DataUtil.getMimeId(reqVo.getUrl());
                if (StringUtils.isNotBlank(illegalMimeId)) {
                    mimeIds.remove(illegalMimeId);
                }
            }
        });
        //当前广告主重复上传的图片
        List<ImageLibrary> repeatedImages = imageLibraryDao.getImagesByMimeIds(mimeIds, sponsorId);
        Map<String, ImageLibrary> repeatedMimeIdMap = repeatedImages
                .stream()
                .collect(Collectors.toMap(ImageLibrary::getMimeId, Function.identity(), (oldVal, newVal) -> oldVal));
        //rel表里重复的rel
        List<String> existMimeIds = imageTemplateRelDao.getByMimeIds(mimeIds)
                .stream()
                .map(ImageTemplateRel::getMimeId)
                .collect(Collectors.toList());
        List<ImageLibrary> saveImages = new ArrayList<>();
        List<ImageTemplateRel> imageTemplateRels = new ArrayList<>();
        List<Long> templateIds = new ArrayList<>();
        List<Long> repeatedImageIds = new ArrayList<>();
        legalMimeList.forEach(uploadHistory -> {

            String mimeId = uploadHistory.getMimeId();
            ImageLibraryReqVo reqVo = mimeMap.get(mimeId);
            if (repeatedMimeIdMap.containsKey(mimeId) && sponsorId.equals(repeatedMimeIdMap.get(mimeId).getSponsorId())) {
                //更新素材库图片创建时间，更新名字
                ImageLibrary image = repeatedMimeIdMap.get(mimeId);
                image.setImageName(getImageName(uploadHistory.getMimeName()));
                image.setCreateTime(new Timestamp(System.currentTimeMillis()));
                //需要处理其关联关系
                saveImages.add(image);
                repeatedImageIds.add(image.getId());
                templateIds.add(reqVo.getTemplateId());
            } else {
                //批量插入素材库
                ImageLibrary image = new ImageLibrary();
                image.setSponsorId(sponsorId);
                //仅保存不带扩展名的图片名字
                image.setImageName(getImageName(uploadHistory.getMimeName()));
                image.setImageWidth(uploadHistory.getMimeWidth());
                image.setImageHeight(uploadHistory.getMimeHeight());
                image.setImageSrc(uploadHistory.getMimeSrc());
                image.setAspectRatio(DataUtil.minScale(uploadHistory.getMimeWidth(), uploadHistory.getMimeHeight()));
                image.setMimeId(mimeId);
                image.setSource(Constants.IMAGE_LIBRARY_SOURCE_TEMPLATE);
                image.setCreateTime(new Timestamp(System.currentTimeMillis()));
                saveImages.add(image);

                if (!existMimeIds.contains(mimeId)) {
                    ImageTemplateRel imageTemplateRel = new ImageTemplateRel();
                    imageTemplateRel.setTemplateId(reqVo.getTemplateId());
                    imageTemplateRel.setMimeId(mimeId);
                    imageTemplateRels.add(imageTemplateRel);
                }
                //查到所有的模板
                templateIds.add(reqVo.getTemplateId());
            }
        });
        imageLibraryDao.batchInsertOrUpdateImages(saveImages);
        //只有新上传的图片需要维护Rel，之前上传过的图片不用
        List<ImageLibrary> newImages = saveImages
                .stream()
                .filter(item -> !repeatedImageIds.contains(item.getId()))
                .collect(Collectors.toList());
        imageTemplateRelDao.batchInsert(imageTemplateRels, newImages);
        //新上传的图片
        if (CollectionUtils.isNotEmpty(templateIds)) {
            List<ImageTemplateTagsMapping> imageTemplateTagsMappingByTemplateIdsAndTagType =
                    imageTemplateTagsMappingDao.getImageTemplateTagsMappingByTemplateIdsAndTagType(templateIds, Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT);
            //保存过的关系
            Map<Long, List<ImageLibraryTagsMapping>> mappingsGroupByImageId = imageLibraryTagsMappingDao.getTagsMappingByImageIds(repeatedImageIds)
                    .stream()
                    .collect(groupingBy(ImageLibraryTagsMapping::getImageId));
            List<ImageLibraryTagsMapping> mappingsShouldInsert = new ArrayList<>();
            for (ImageLibrary saveImage : saveImages) {
                List<ImageTemplateTagsMapping> allRelShouldCopy;
                ImageLibraryReqVo imageLibraryReqVo = mimeMap.get(saveImage.getMimeId());
                //是重复的图片，并且之前有保存过的关系
                if (repeatedImageIds.contains(saveImage.getId()) && CollectionUtils.isNotEmpty(mappingsGroupByImageId.get(saveImage.getId()))) {
                    //找到已经存在的关系
                    Map<Long, ImageLibraryTagsMapping> tagIdToImageLibraryTagsMapping = mappingsGroupByImageId.get(saveImage.getId())
                            .stream()
                            .collect(toMap(ImageLibraryTagsMapping::getTagId, o -> o));
                    allRelShouldCopy = imageTemplateTagsMappingByTemplateIdsAndTagType
                            .stream()
                            .filter(o ->
                                    o.getImageTemplateId().equals(imageLibraryReqVo.getTemplateId())
                                            && !tagIdToImageLibraryTagsMapping.containsKey(o.getTagId()))
                            .collect(Collectors.toList());
                } else {
                    //新添加的图片 或者是之前添加过图片 但是一条与Tag的关联关系都没有的
                    allRelShouldCopy = imageTemplateTagsMappingByTemplateIdsAndTagType
                            .stream()
                            .filter(o -> o.getImageTemplateId().equals(imageLibraryReqVo.getTemplateId()))
                            .collect(Collectors.toList());
                }
                for (ImageTemplateTagsMapping imageTemplateTagsMapping : allRelShouldCopy) {
                    ImageLibraryTagsMapping imageLibraryTagsMapping = new ImageLibraryTagsMapping();
                    imageLibraryTagsMapping.setImageId(saveImage.getId());
                    imageLibraryTagsMapping.setTagType(Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT);
                    imageLibraryTagsMapping.setTagId(imageTemplateTagsMapping.getTagId());
                    mappingsShouldInsert.add(imageLibraryTagsMapping);
                }
            }
            imageLibraryTagsMappingDao.batchSave(mappingsShouldInsert);
        }
        //重复上传的图片 需要做一次增量更新

        if (!imageGroupId.equals(NO_GROUP_ID)) {
            saveImageGroup(sponsorId, mimeIds, imageGroupId);
        }
        int repeatedNums = repeatedImages.size();
        MaterialSaveResult materialSaveResult = new MaterialSaveResult();
        if (repeatedNums > 0) {
            materialSaveResult.setWarningMsg("有" + repeatedNums + "张图片已在库中存在，本次上传将覆盖历史图片");
        }
        String illegalUrlStr = illegalUrlStrBuilder.toString();
        if (StringUtils.isNotBlank(illegalUrlStr)) {
            materialSaveResult.setWarningMsg1("以下url不合法：" + illegalUrlStr);
        }
        return materialSaveResult;
    }

    @Transactional
    @Override
    public Integer saveImageFromStaticTemplate(Long sponsorId, Long templateId, String name) {
        ImageTemplate imageTemplate = imageTemplateDao.findById(templateId);
        String mimeId = DataUtil.getMimeId(imageTemplate.getPreviewUrl());
        ImageLibrary imageLibrary = imageLibraryDao.getByMimeIdAndSponsorId(mimeId, sponsorId);
        Boolean isRepeatImage = false;
        if (imageLibrary != null) {
            //重复上传的图片
            imageLibrary.setImageName(name);
            imageLibraryDao.saveOrUpdateImage(imageLibrary);
            isRepeatImage = true;
        } else {
            //新保存的图片
            ImageTemplateConfiguration imageTemplateConfiguration = configurationDao.getByTemplateId(templateId);
            ImageTemplateConfig imageTemplateConfig = GSON.fromJson(imageTemplateConfiguration.getTemplateConfig(), ImageTemplateConfig.class);

            ImageLibrary genImageLibrary = new ImageLibrary();
            genImageLibrary.setImageName(name);
            genImageLibrary.setImageWidth(imageTemplateConfig.getWidth());
            genImageLibrary.setImageHeight(imageTemplateConfig.getHeight());
            genImageLibrary.setImageSrc(imageTemplateConfig.getPreview());
            genImageLibrary.setAspectRatio(DataUtil.minScale(imageTemplateConfig.getWidth(), imageTemplateConfig.getHeight()));
            genImageLibrary.setMimeId(mimeId);
            genImageLibrary.setSponsorId(sponsorId);
            genImageLibrary.setSource(Constants.IMAGE_LIBRARY_SOURCE_TEMPLATE);
            genImageLibrary.setCreateTime(new Timestamp(System.currentTimeMillis()));
            imageLibraryDao.saveOrUpdateImage(genImageLibrary);
            imageLibrary = genImageLibrary;
            //因为mimeId是直接从数据库里取的 所以可能出现重复的情况
            if (CollectionUtils.isEmpty(imageTemplateRelDao.getByMimeIds(Collections.singletonList(mimeId)))) {
                ImageTemplateRel imageTemplateRel = new ImageTemplateRel();
                imageTemplateRel.setImageLibraryId(genImageLibrary.getId());
                imageTemplateRel.setTemplateId(templateId);
                imageTemplateRel.setMimeId(mimeId);
                imageTemplateRelDao.save(imageTemplateRel);
            }
        }
        List<ImageTemplateTagsMapping> imageTemplateTagsMappingByTemplateIdAndTagType = imageTemplateTagsMappingDao.getImageTemplateTagsMappingByTemplateIdAndTagType(templateId, Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT);
        List<ImageLibraryTagsMapping> insertMappings = new ArrayList<>();
        //已经在数据库中存在的 图片与标签的关系
        List<Long> existMappingTagIds = imageLibraryTagsMappingDao
                .getTagsMappingByImageIds(Collections.singletonList(imageLibrary.getId()))
                .stream()
                .map(ImageLibraryTagsMapping::getTagId)
                .collect(Collectors.toList());
        for (ImageTemplateTagsMapping imageTemplateTagsMapping : imageTemplateTagsMappingByTemplateIdAndTagType) {
            if (!existMappingTagIds.contains(imageTemplateTagsMapping.getTagId())) {
                ImageLibraryTagsMapping imageLibraryTagsMapping = new ImageLibraryTagsMapping();
                imageLibraryTagsMapping.setImageId(imageLibrary.getId());
                imageLibraryTagsMapping.setTagType(Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT);
                imageLibraryTagsMapping.setTagId(imageTemplateTagsMapping.getTagId());
                insertMappings.add(imageLibraryTagsMapping);
            }
        }
        imageLibraryTagsMappingDao.batchSave(insertMappings);
        return isRepeatImage ? 1 : 0;
    }

    @Override
    public AllTagsListDto getAllTagsList() {
        AllTagsListDto allTagsListDto = new AllTagsListDto();
        //获取第一层
        List<ImageTemplateTag> allTopLayer = templateTagDao.getAllTopLayer();
        allTagsListDto.setSlotTags(new ArrayList<>());
        allTagsListDto.setTemplateTags(new ArrayList<>());
        allTagsListDto.setIndustryTags(new ArrayList<>());
        for (ImageTemplateTag imageTemplateTag : allTopLayer) {
            switch (imageTemplateTag.getTagType()) {
                case Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT: {
                    allTagsListDto.getSlotTags().add(imageTemplateTag2TagItemWithSub(imageTemplateTag));
                    break;
                }
                case Constants.IMAGE_LIBRARY_TAG_TYPE_TEMPLATE: {
                    allTagsListDto.getTemplateTags().add(imageTemplateTag2TagItemWithSub(imageTemplateTag));
                    break;
                }
                case Constants.IMAGE_LIBRARY_TAG_TYPE_INDUSTRY: {
                    allTagsListDto.getIndustryTags().add(imageTemplateTag2TagItem(imageTemplateTag));
                    break;
                }
                default: {
                    break;
                }
            }
        }
        //获取第二层 现在只有媒体有第二级标签
        List<ImageTemplateTag> secLayer = templateTagDao.getSecLayer();
        for (ImageTemplateTag imageTemplateTag : secLayer) {
            if (imageTemplateTag.getTagType() == Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT) {
                for (TagItemWithSub slotTag : allTagsListDto.getSlotTags()) {
                    if (slotTag.getId().equals(imageTemplateTag.getParentId())) {
                        slotTag.getSubItems().add(imageTemplateTag2TagItem(imageTemplateTag));
                    }
                }
            }
        }
        allTagsListDto.getTemplateTags().sort(Comparator.comparing(TagItem::getId).reversed());
        return allTagsListDto;
    }

    @Override
    public List<TagItemWithSub> getImageLibraryTagsList(Long sponsorId) {
        List<TagItemWithSub> topLayerIncludeDeletedByType = new ArrayList<>();
        List<ImageTemplateTag> allSecLayer = imageLibraryTagsMappingDao.getAllSecLayer(sponsorId);
        templateTagDao.getTopLayerIncludeHiddenByType(Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT).forEach(item -> {
            TagItemWithSub tagItemWithSub = imageTemplateTag2TagItemWithSub(item);
            for (ImageTemplateTag imageTemplateTag : allSecLayer) {
                if (imageTemplateTag.getParentId().equals(tagItemWithSub.getId())) {
                    TagItem tagItem = new TagItem();
                    tagItem.setName(imageTemplateTag.getTagName());
                    tagItem.setId(imageTemplateTag.getId());
                    tagItemWithSub.getSubItems().add(tagItem);
                }
            }
            if (CollectionUtils.isNotEmpty(tagItemWithSub.getSubItems())) {
                topLayerIncludeDeletedByType.add(tagItemWithSub);
            }
        });
        return topLayerIncludeDeletedByType;
    }

    @Override
    public AllTagListWithSelectedDto getTagListByTemplateId(Long templateId) {
        ImageTemplate byId = imageTemplateDao.findById(templateId);
        ExceptionUtil.badRequestIf(byId == null || byId.getDeleted(), "模板不存在...");
        AllTagListWithSelectedDto allTagListWithSelectedDto = new AllTagListWithSelectedDto();
        //一级标签有且只有一个
        TagItemWithSelectedSub tagItemWithSelectedSub =
                templateTagDao.getTopLayerByTemplateIdAndTagType(templateId, Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT);
        tagItemWithSelectedSub.setSubItems(new ArrayList<>());
        //已经选择的标签ID
        List<Long> selectedTemplateTagIds = templateTagDao.getSelectedStatusItemByTemplateAndParentId(templateId, tagItemWithSelectedSub.getId())
                .stream()
                .map(ImageTemplateTag::getId)
                .collect(Collectors.toList());
        //所有的二级标签
        List<ImageTemplateTag> secLayerByParentId = templateTagDao.getSecLayerByParentId(tagItemWithSelectedSub.getId());
        secLayerByParentId.forEach(item -> {
            TagItemWithSelected tagItemWithSelected = new TagItemWithSelected();
            tagItemWithSelected.setId(item.getId());
            tagItemWithSelected.setName(item.getTagName());
            tagItemWithSelected.setSelected(selectedTemplateTagIds.contains(item.getId()));
            tagItemWithSelectedSub.getSubItems().add(tagItemWithSelected);
        });
        //这里暂时只有一级标签，同时不支持修改
        List<TagItemWithSelected> templateTagWithSelected = templateTagDao.getTemplateTagWithSelected(templateId);

        List<TagItemWithSelected> singleLayerIndustry = new ArrayList<>();
        List<Long> selectedIndustryTagIds = templateTagDao.getSingleLayerTagByTemplateIdAndTagType(templateId, Constants.IMAGE_LIBRARY_TAG_TYPE_INDUSTRY)
                .stream()
                .map(ImageTemplateTag::getId)
                .collect(Collectors.toList());
        List<ImageTemplateTag> singleLayerTagsByType = templateTagDao.getSingleLayerTagsByType(Constants.IMAGE_LIBRARY_TAG_TYPE_INDUSTRY);
        for (ImageTemplateTag imageTemplateTag : singleLayerTagsByType) {
            TagItemWithSelected tagItemWithSelected = new TagItemWithSelected();
            tagItemWithSelected.setId(imageTemplateTag.getId());
            tagItemWithSelected.setName(imageTemplateTag.getTagName());
            tagItemWithSelected.setSelected(selectedIndustryTagIds.contains(imageTemplateTag.getId()));
            singleLayerIndustry.add(tagItemWithSelected);
        }
        List<TagItemWithSelectedSub> tagItemWithSelectedSubList = new ArrayList<>();
        tagItemWithSelectedSubList.add(tagItemWithSelectedSub);
        allTagListWithSelectedDto.setSlotTags(tagItemWithSelectedSubList);
        allTagListWithSelectedDto.setTemplateTags(templateTagWithSelected);
        allTagListWithSelectedDto.setIndustryTags(singleLayerIndustry);

        return allTagListWithSelectedDto;
    }

    @Override
    public void editTemplateTags(Long imageTemplateId, ImageTemplateEditVo imageTemplateEditVo) {
        ImageTemplate byId = imageTemplateDao.findById(imageTemplateId);
        ExceptionUtil.badRequestIf(byId == null || byId.getDeleted(), "模板不存在");


        String[] slotTags = imageTemplateEditVo.getSlotTagId().split(",");
        //template目前为单选 暂时不需要分割 也**暂不能**开放编辑
        String[] industryTags = imageTemplateEditVo.getIndustryTagId().split(",");

        List<ImageTemplateTagsMapping> imageTemplateTagsMappingList = new ArrayList<>();

        imageTemplateTagsMappingDao.removeSlotTagAndIndustryTagByTemplateId(imageTemplateId);

        for (String slotTag : slotTags) {
            ImageTemplateTagsMapping imageTemplateTagsMapping = new ImageTemplateTagsMapping();
            imageTemplateTagsMapping.setImageTemplateId(imageTemplateId);
            imageTemplateTagsMapping.setTagType(Constants.IMAGE_LIBRARY_TAG_TYPE_SLOT);
            imageTemplateTagsMapping.setTagId(Long.parseLong(slotTag));
            imageTemplateTagsMappingList.add(imageTemplateTagsMapping);
        }
        for (String industryTag : industryTags) {
            ImageTemplateTagsMapping imageTemplateTagsMapping = new ImageTemplateTagsMapping();
            imageTemplateTagsMapping.setImageTemplateId(imageTemplateId);
            imageTemplateTagsMapping.setTagType(Constants.IMAGE_LIBRARY_TAG_TYPE_INDUSTRY);
            imageTemplateTagsMapping.setTagId(Long.parseLong(industryTag));
            imageTemplateTagsMappingList.add(imageTemplateTagsMapping);
        }
        imageTemplateTagsMappingDao.batchSaveOrUpdate(imageTemplateTagsMappingList);
    }

    @Override
    public TagItemWithSub getSlotTagsByElementId(Long elementId) {
        ImageTemplateTag imageTemplateByElementId = templateTagDao.getImageTemplateByElementId(elementId);
        ImageTemplateTag imageTemplateTag = templateTagDao.get(imageTemplateByElementId.getParentId());
        TagItem tagItem = new TagItem();
        tagItem.setId(imageTemplateByElementId.getId());
        tagItem.setName(imageTemplateByElementId.getTagName());
        TagItemWithSub tagItemWithSub = new TagItemWithSub();
        tagItemWithSub.setId(imageTemplateTag.getId());
        tagItemWithSub.setName(imageTemplateTag.getTagName());
        tagItemWithSub.setSubItems(Collections.singletonList(tagItem));
        return tagItemWithSub;
    }

    /**
     * 通过sponsorId判断客户是否在素材传输白名单内
     *
     * @param sponsorId
     * @return boolean
     */

    @Override
    public boolean isInTransportMaterialWhiteListBySponsorId(Long sponsorId) {
        //通过sponsorID查询为null则不在白名单内
        Sponsor sponsor = sponsorDao.getSponsor(sponsorId);
        if (sponsor == null) {
            return false;
        }
        return this.isInTransportMaterialWhiteList(sponsor.getUserName());
    }

    /**
     * 通过sponsor.userName判断客户是否在素材传输白名单内
     *
     * @param userName
     * @return boolean
     */
    @Override
    public boolean isInTransportMaterialWhiteList(String userName) {
        //通过username 获取Status为有效状态的白名单列表
        List<TransportMaterialWhiteListItem> whiteList = transportMaterialWhiteListDao.getList(userName, Constants.TRANS_MATERIAL_WHITE_LIST_ENABLE);
        //集合非空，返回true，否则false
        return CollectionUtils.isNotEmpty(whiteList);
    }

    /**
     * 判断两个账户是否在同一scope里
     *
     * @param userName1
     * @param userName2
     * @return boolean
     */
    @Override
    public boolean isInSameScope(String userName1, String userName2) {
        List<TransportMaterialWhiteListItem> senderWhiteList = transportMaterialWhiteListDao.getList(userName1, Constants.TRANS_MATERIAL_WHITE_LIST_ENABLE);
        List<TransportMaterialWhiteListItem> receiverWhiteList = transportMaterialWhiteListDao.getList(userName2, Constants.TRANS_MATERIAL_WHITE_LIST_ENABLE);
        //获取receiverWhiteList中status为1的scope集合
        Set<String> receiverScopeSet = receiverWhiteList
                .stream()
                .map(x -> x.getScope().trim())
                .collect(Collectors.toSet());
        //只要存在一个域匹配，就判定为true
        for (TransportMaterialWhiteListItem senderItem : senderWhiteList) {
            if (receiverScopeSet.contains(senderItem.getScope().trim()))
                return true;
        }
        return false;
    }

    /**
     * 向指定账户发送图片
     *
     * @param request
     * @param sponsor
     * @param imageIds
     * @param receiverUserName
     * @return int :对方账户内已存在图片数
     */
    @Override
    public int sendImages(HttpServletRequest request, Sponsor sponsor, String imageIds, String receiverUserName) {
        Long sponsorId = sponsor.getSponsorId();
        Set<Long> imageIdSet = Arrays.asList(StringUtils.split(imageIds, ",")).stream()
                .map(s -> Long.parseLong(s.trim()))
                .collect(Collectors.toSet());
        Sponsor receiver = sponsorDao.getSponsor(receiverUserName);
        if (receiver == null) {
            throw new BadRequestException("目标用户存在于白名单，但不存在于数据库，请联系管理查询白名单账户是否输入有误！");
        }
        //获取要发送的图片，为空直接返回
        List<ImageLibrary> sendImages = imageLibraryDao.getByIds(sponsorId, imageIdSet);
        if (sendImages.size() == 0) {
            return 0;
        }
        //获取图片的mimeId
        List<String> mimeIdList = sendImages.stream()
                .map(ImageLibrary::getMimeId)
                .collect(Collectors.toList());
        //获取重复的图片
        List<ImageLibrary> repeatedImages = imageLibraryDao.getImagesByMimeIds(mimeIdList, receiver.getSponsorId());
        Set<String> repeatedMimeId = repeatedImages.stream()
                .map(ImageLibrary::getMimeId).collect(Collectors.toSet());
        //对于没有重复的图片，需要new。
        List<ImageLibrary> saveOrUpdateImages = new ArrayList<>();
        sendImages.forEach(sendImage -> {
            String mimeId = sendImage.getMimeId();
            //如果图片重复存在,不做更新,不重复再批量插入
            if (!repeatedMimeId.contains(mimeId)) {
                ImageLibrary image = new ImageLibrary();
                image.setSponsorId(receiver.getSponsorId());
                image.setImageName(sendImage.getImageName());
                image.setImageWidth(sendImage.getImageWidth());
                image.setImageHeight(sendImage.getImageHeight());
                image.setSource(sendImage.getSource());
                image.setImageSrc(sendImage.getImageSrc());
                image.setAspectRatio(sendImage.getAspectRatio());
                image.setMimeId(mimeId);
                image.setCreateTime(sendImage.getCreateTime());
                saveOrUpdateImages.add(image);
            }
        });
        imageLibraryDao.batchInsertOrUpdateImages(saveOrUpdateImages);

        //获取目标图片分组 接收素材yyyyMMdd,可在常量文件修改,不存在则自动创建
        String targetGroupName = Constants.RECEIVE_GROUP_NAME + DateTime.getDateFromLongForFinance(System.currentTimeMillis());
        ImageLibraryGroup targetGroup = imageLibraryGroupDao.getOne(receiver.getSponsorId(), targetGroupName);
        if (targetGroup == null) {
            targetGroup = createImageGroup(receiver.getSponsorId(), targetGroupName);
        }
        //创建关联分组
        saveImageGroup(receiver.getSponsorId(), mimeIdList, targetGroup.getId());
        //新建list用于mongo保存信息
        List<String> imageNameAndIdList = sendImages
                .stream()
                .map(sendImage -> String.format("%s(%s)", sendImage.getImageName(), sendImage.getId()))
                .collect(Collectors.toList());
        //发送完成，记录kafka日志
        LogUtil.toKafka(UserType.parseInt(SessionManager.getType()),
                sponsor.getUserName(), sponsor.getAgentId(),
                sponsor.getSponsorId(), OpType.SEND_IMAGES,
                String.format("Send Images to %s(%d).images:%s,imagesMimeId:%s", receiverUserName, receiver.getSponsorId(), imageNameAndIdList, mimeIdList),
                RequestUtil.getIp(request), request);
        return repeatedImages.size();
    }


    /**
     * 向指定账户发送标题
     *
     * @param request
     * @param sponsor
     * @param titleIds
     * @param receiverUserName
     * @return int :对方账户内已存在标题数
     */
    @Override
    public int sendTitles(HttpServletRequest request, Sponsor sponsor, String titleIds, String receiverUserName) {
        Long sponsorId = sponsor.getSponsorId();
        //获取titleId列表
        Set<Long> titleIdSet = Arrays.asList(StringUtils.split(titleIds, ",")).stream()
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        Sponsor receiver = sponsorDao.getSponsor(receiverUserName);
        if (receiver == null) {
            throw new BadRequestException("目标用户存在于白名单，但不存在于数据库，请联系管理查询白名单账户是否输入有误！");
        }
        //获取发送方要发送的的TitleLibrary列表
        List<TitleLibrary> titleLibraries = titleLibraryDao.getList(sponsorId, titleIdSet);
        //所发送的标题都不是本人拥有的，直接返回
        if (CollectionUtils.isEmpty(titleLibraries)) {
            return 0;
        }
        //获取要发送的图片标题名称
        Set<String> titleSet = titleLibraries.stream()
                .map(TitleLibrary::getTitle)
                .collect(Collectors.toSet());
        //获取已存在的标题（title相同）
        List<TitleLibrary> repeatedTitles = titleLibraryDao.getByTitleNames(receiver.getSponsorId(), titleSet);
        Set<String> repeatedTitleSet = repeatedTitles.stream()
                .map(TitleLibrary::getTitle)
                .collect(Collectors.toSet());
        //遍历进行批量插入
        List<TitleLibrary> savedTitleLibraries = new ArrayList<>();
        titleLibraries.forEach(sendTitle -> {
            String title = sendTitle.getTitle();
            if (!repeatedTitleSet.contains(title)) {
                TitleLibrary titleLibrary = new TitleLibrary();
                titleLibrary.setTitle(title);
                titleLibrary.setTitleLength(sendTitle.getTitleLength());
                titleLibrary.setSponsorId(receiver.getSponsorId());
                titleLibrary.setCreateTime(sendTitle.getCreateTime());
                titleLibrary.setLastModTime(sendTitle.getLastModTime());
                savedTitleLibraries.add(titleLibrary);
            }
        });
        titleLibraryDao.batchInsertOrUpdateTitles(savedTitleLibraries);
        //发送完成，记录kafka日志
        LogUtil.toKafka(UserType.parseInt(SessionManager.getType()),
                sponsor.getUserName(), sponsor.getAgentId(),
                sponsor.getSponsorId(), OpType.SEND_IMAGES,
                String.format("Send Titles to %s(%d).titles:%s,titleIds:%s", receiverUserName, receiver.getSponsorId(), titleSet, titleIdSet),
                RequestUtil.getIp(request), request);
        return repeatedTitleSet.size();
    }

    /**
     * 检查标签状态 当 发生标签可能变少的情况下
     *
     * @param imageTemplateId
     */
    private void checkTagMappingRelOnLessTagAction(Long imageTemplateId) {
        imageTemplateTagsMappingDao.getImageTemplateTagsMappingByTemplateId(imageTemplateId).forEach(tagsMapping -> {
            //先删除记录
            imageTemplateTagsMappingDao.remove(tagsMapping.getId());
            //检查这个标签是否有还有关联关系
            Long countByTagId = imageTemplateTagsMappingDao.getCountByTagId(tagsMapping.getTagId());
            if (0 == countByTagId) {
                templateTagDao.makeTagHide(tagsMapping.getTagId());
            }
        });
    }

    /**
     * ImageTemplate转为TagItem
     *
     * @param imageTemplateTag
     * @return
     */
    private TagItem imageTemplateTag2TagItem(ImageTemplateTag imageTemplateTag) {
        TagItem tagItem = new TagItem();
        tagItem.setId(imageTemplateTag.getId());
        tagItem.setName(imageTemplateTag.getTagName());
        return tagItem;
    }

    /**
     * ImageTemplate转为TagItemWithSub
     *
     * @param imageTemplateTag
     * @return
     */
    private TagItemWithSub imageTemplateTag2TagItemWithSub(ImageTemplateTag imageTemplateTag) {
        TagItemWithSub tagItemWithSub = new TagItemWithSub();
        tagItemWithSub.setName(imageTemplateTag.getTagName());
        tagItemWithSub.setId(imageTemplateTag.getId());
        tagItemWithSub.setSubItems(new ArrayList<>());
        return tagItemWithSub;
    }
}
