package outfox.ead.noah.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.core.SimplePage;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.dao.MessageContentDao;
import outfox.ead.noah.dao.MessageDao;
import outfox.ead.noah.entity.Message;
import outfox.ead.noah.entity.MessageContent;
import outfox.ead.noah.entity.models.simple.SimpleMessage;
import outfox.ead.noah.service.MessageService;
import outfox.ead.noah.util.DateTime;
import outfox.ead.noah.util.Pager;
import outfox.ead.noah.util.code.MessageStatus;
import outfox.ead.noah.util.code.MessageType;
import outfox.ead.noah.util.datautil.DataUtil;
import outfox.ead.noah.util.logger.LogUtil;
import outfox.ead.noah.util.logger.OpType;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static outfox.ead.noah.util.code.MessageStatus.IGNORE_STATUS;
import static outfox.ead.noah.util.code.MessageStatus.UN_READ;
import static outfox.ead.noah.util.code.MessageTemplateType.INSUFFICIENT_BALANCE_TEMPLATE;
import static outfox.ead.noah.util.code.MessageTemplateType.OCPC_COMPENSATION_TEMPLATE;
import static outfox.ead.noah.util.code.MessageType.SERVICE_MESSAGE;
import static outfox.ead.noah.util.code.MessageType.SYS_MESSAGE;

/**
 * Created by yuanzhch on 2017/9/8.
 */
@Service
@Slf4j
public class MessageServiceImpl implements MessageService {

    public static final String SPONSOR = "sponsor";

    public static final String ID = "id";
    public static final String TITLE = "title";
    public static final String TYPE = "type";
    public static final String TIME = "time";
    public static final String STATUS = "status";
    public static final String CONTENT = "content";

    private static Map<Integer, Set<Integer>> TYPE_TO_MESSAGE_TYPE = new HashMap<>();
    static {
        Set<Integer> sysMsg = DataUtil.newHashSet(SYS_MESSAGE);
        Set<Integer> userMsg = DataUtil.newHashSet(MessageType.AUDIT_MESSAGE, SERVICE_MESSAGE, MessageType.AGENT_MESSAGE);
        Set<Integer> allMsg = DataUtil.newHashSet(sysMsg, userMsg);
        TYPE_TO_MESSAGE_TYPE.put(Constants.SYSTEM_MSG, sysMsg);
        TYPE_TO_MESSAGE_TYPE.put(Constants.USER_MSG, userMsg);
        TYPE_TO_MESSAGE_TYPE.put(Constants.ALL_MSG, allMsg);
    }

    private static Map<Integer, Integer> MESSAGE_TYPE_TO_TYPE = new HashMap<>();
    static {
        MESSAGE_TYPE_TO_TYPE.put(SYS_MESSAGE, Constants.SYSTEM_MSG);

        MESSAGE_TYPE_TO_TYPE.put(MessageType.AGENT_MESSAGE, Constants.USER_MSG);
        MESSAGE_TYPE_TO_TYPE.put(MessageType.AUDIT_MESSAGE, Constants.USER_MSG);
        MESSAGE_TYPE_TO_TYPE.put(SERVICE_MESSAGE, Constants.USER_MSG);
    }

    @Autowired
    private MessageDao messageDao;

    @Autowired
    private MessageContentDao messageContentDao;

    @Override
    public Message getMessage(long messageId) {
        return messageDao.getMessage(messageId);
    }

    @Override
    public MessageContent getMessageContent(long messageContentId) {
        return messageContentDao.getMessageContent(messageContentId);
    }

    @Override
    public void changeMessageStatus(long sponsorId, long messageId, int status) {
        messageDao.changeMessageStatus(messageId, status);
        LogUtil.toDb(Type.SPONSOR, sponsorId, OpType.CHANGE_MESSAGE_STATUS_READ,
                "change message read, messageId:" + messageId);
    }

    @Override
    public SimplePage<SimpleMessage> getPagedSimpleMessageList(long sponsorId, int type, int pageNo, int pageCapacity) {
        long count = messageDao.getMessageCount(sponsorId, TYPE_TO_MESSAGE_TYPE.get(type));
        Pager pager = Pager.of(pageNo, pageCapacity, count);
        if (pager.excess()) {
            return SimplePage.of(count, pageNo, pageCapacity, new ArrayList());
        }
        List<Message> messageList = messageDao.getPagedMessage(sponsorId, TYPE_TO_MESSAGE_TYPE.get(type),
                pager.getPosition(), pager.getLength());
        return SimplePage.of(count, pageNo, pageCapacity, toSimpleMessage(messageList));
    }

    @Override
    public long getUnreadMessageCount(long sponsorId) {
        return messageDao.getUnreadMessageCount(sponsorId);
    }

    /**
     * @see MessageService#getPopupMessage(Long)
     */
    @Override
    public List<JSONObject> getPopupMessage(Long sponsorId) {
        List<Message> allSysAndServiceMessage = messageDao.getMessageList(null, sponsorId, MessageStatus.ALL,
                DataUtil.newArrayList(SYS_MESSAGE, SERVICE_MESSAGE), null , -1, -1);
        List<Long> contentIds = allSysAndServiceMessage.stream().map(Message::getMessageId).collect(Collectors.toList());
        List<MessageContent> messageContentList = messageContentDao.getListByIds(contentIds);
        //构造message对象的id->MessageContent的map方便查找消息的内容是否为余额不足提醒
        Map<Long, MessageContent> idToContentMap =
                messageContentList.stream().collect(Collectors.toMap(MessageContent::getMessageId, Function.identity()));
        List<JSONObject> result = new ArrayList<>(3);
        int count = 0;
        for (Message message : allSysAndServiceMessage) {
            MessageContent messageContent = idToContentMap.get(message.getMessageId());
            //只对系统消息,账户消息中的余额不足消息,账户消息中的oCPC返款消息做弹窗
            if (message.getType() == SYS_MESSAGE || messageContent.getTemplate() == INSUFFICIENT_BALANCE_TEMPLATE
                    || messageContent.getTemplate() == OCPC_COMPENSATION_TEMPLATE) {
                count++;
                if (message.getStatus() == UN_READ) {
                    JSONObject content = new JSONObject();
                    content.put(ID, message.getId());
                    content.put(TITLE, messageContent.getTitle());
                    content.put(TYPE, MESSAGE_TYPE_TO_TYPE.get(message.getType()));
                    content.put(TIME, DateTime.yyyy_MM_dd(message.getOperateTime()));
                    content.put(CONTENT, messageContent.getContent());
                    content.put(STATUS, message.getStatus());
                    result.add(content);
                }
                if (count == 3) {
                    break;
                }
            }
        }
        return result;
    }

    /**
     * @see MessageService#batchUpdateMessageStatus(Long, Collection, Integer)
     */
    @Override
    public void batchUpdateMessageStatus(Long sponsorId, Collection<Long> msgIds, Integer status) {
        if (CollectionUtils.isEmpty(msgIds)) {
            return;
        }
        messageDao.batchUpdateMessageStatus(sponsorId, msgIds, status);
        LogUtil.toDb(Type.SPONSOR, sponsorId, OpType.BATH_UPDATE_MESSAGE_STATUS,
                "batch update message status, msgIds: " + msgIds.toString() + ", status: " + status);
    }

    private List<SimpleMessage> toSimpleMessage(List<Message> messageList) {
        Set<Long> msgContentIds = messageList.stream()
                .map(Message::getMessageId)
                .collect(Collectors.toSet());
        List<MessageContent> messageContentList = messageContentDao.getListByIds(msgContentIds);

        Map<Long, MessageContent> idMsgContent = messageContentList.stream()
                .collect(Collectors.toMap(MessageContent::getMessageId, Function.identity()));

        return messageList.stream().map(msg -> {
            SimpleMessage simpleMessage = new SimpleMessage();
            simpleMessage.setDate(DateTime.yyyy_MM_dd(msg.getOperateTime()));
            simpleMessage.setType(MESSAGE_TYPE_TO_TYPE.get(msg.getType()));
            simpleMessage.setId(msg.getId());
            //IGNORE_STATUS为忽略状态，也属于未读状态
            simpleMessage.setUnRead(msg.getStatus() == UN_READ || msg.getStatus() == IGNORE_STATUS);
            MessageContent content = idMsgContent.get(msg.getMessageId());
            if (content != null) {
                simpleMessage.setTitle(content.getTitle());
                simpleMessage.setContent(content.getContent());
            }
            return simpleMessage;
        }).collect(Collectors.toList());
    }

}
