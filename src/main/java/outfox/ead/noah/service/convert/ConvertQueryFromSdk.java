package outfox.ead.noah.service.convert;

import io.druid.java.util.common.granularity.Granularities;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.dao.ThirdPartyDataPlatformDao;
import outfox.ead.noah.druid.Builder;
import outfox.ead.noah.druid.DruidBuilder;
import outfox.ead.noah.service.builder.SdkConvertGetter;

import java.util.Set;

import static io.druid.java.util.common.granularity.Granularities.HOUR;
import static outfox.ead.noah.constants.Constants.AGGREGATE_TYPE_DAY;
import static outfox.ead.noah.constants.Constants.AGGREGATE_TYPE_HOUR;
import static outfox.ead.noah.druid.DruidBuilder.DruidCol.*;
import static outfox.ead.noah.util.DateUtil.statInterval;

/**
 * @see outfox.ead.noah.service.builder.ConvertForChartFromSdk
 */
@Slf4j
public class ConvertQueryFromSdk extends AbstractConvertQuery implements SdkConvertGetter {

    public ConvertQueryFromSdk(long sponsorId, Type type, Set<Long> ids,
                               long fromTime, long toTime, String aggregate, boolean groupBySlot) {
        super(sponsorId, type, ids, fromTime, toTime, aggregate, groupBySlot);
    }

    /**
     * Query convert data from non-convert-tracking. sdk_stat_v2.conv
     * conv_type in {@link ThirdPartyDataPlatformDao#getAllConvTypes()}
     */
    @Override
    protected void druidQueryBuilder() {
        builder = DruidBuilder.get();
        builder.select(CONVERT_NUM);
        builder.from(beanRepository.getStatDataSourceService().getSdkStatDataSourceForConv(sponsorId));
        builder.where(Builder.Criteria.eq(SPONSOR_ID, sponsorId));
        builder.whereIf(type != Type.SPONSOR && CollectionUtils.isNotEmpty(ids), Builder.Criteria.in(idCol, ids));
        builder.where(Builder.Criteria.in(CONV_TYPE, beanRepository.getThirdPartyDataPlatformDao().getAllConvTypes()));
        builder.interval(statInterval(fromTime, toTime));
        builder.groupBy(idCol);
        builder.groupByIf(groupBySlot, SLOT_ID);
        if (AGGREGATE_TYPE_DAY.equals(aggregate)) {
            builder.granularity(Granularities.DAY);
        } else if (AGGREGATE_TYPE_HOUR.equals(aggregate)) {
            builder.granularity(HOUR);
        }
    }

    /**
     * Query convert data from non-convert-tracking. sdk_stat_v2.conv
     * conv_type in {@link ThirdPartyDataPlatformDao#getAllConvTypes()}
     */
    @Override
    protected void druidQueryBuilderForTotal() {
        builder = DruidBuilder.get();
        builder.select(CONVERT_NUM);
        builder.from(beanRepository.getStatDataSourceService().getSdkStatDataSourceForConv(sponsorId));
        builder.where(Builder.Criteria.eq(SPONSOR_ID, sponsorId));
        builder.whereIf(type != Type.SPONSOR && CollectionUtils.isNotEmpty(ids), Builder.Criteria.in(idCol, ids));
        builder.where(Builder.Criteria.in(CONV_TYPE, beanRepository.getThirdPartyDataPlatformDao().getAllConvTypes()));
        builder.interval(statInterval(fromTime, toTime));
    }

}
