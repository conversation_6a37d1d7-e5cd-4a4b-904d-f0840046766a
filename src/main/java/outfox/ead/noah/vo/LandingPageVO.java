package outfox.ead.noah.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LandingPageVO {
    private Long landingPageId;
    private String name;
    private Byte landingPageType;
    private Long templateId;
    private Map<String, Object> data;
    private Long landingPageFormId;
    private Byte transmit;
    private String transmitAddress;
    private Byte status;
    private Boolean autoInputPhone;
    private Boolean injectTrackingScript;
    private String platformConvertTrackingId;
    private Integer trackingPlatform;
}
