package outfox.ead.noah.boot;

import com.xxl.job.core.autoconfig.EnableXxlJobCore;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.web.MultipartAutoConfiguration;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Created by huanghuan on 16/4/12.
 */
@EnableCircuitBreaker
@SpringBootApplication
@EnableAutoConfiguration(exclude = {MultipartAutoConfiguration.class, MongoAutoConfiguration.class})
@ComponentScan({"outfox.ead.noah"})
@EnableAsync
@EnableXxlJobCore
public class Starter {

    public static void main(String[] args) {
        SpringApplication.run(Starter.class, args);
    }

}
