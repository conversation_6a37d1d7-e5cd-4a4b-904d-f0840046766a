package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * Created by lizai on 16/5/19.
 */
@Entity
public class ConversionType {
    private long conversionTypeId;
    private int serialNumber;
    private int parentSerialNumber;
    private String name;
    private Integer status;
    private String remark;

    @Id
    @Column(name = "CONVERSION_TYPE_ID", nullable = false, insertable = true, updatable = true)
    public long getConversionTypeId() {
        return conversionTypeId;
    }

    public void setConversionTypeId(long conversionTypeId) {
        this.conversionTypeId = conversionTypeId;
    }

    @Basic
    @Column(name = "SERIAL_NUMBER", nullable = false, insertable = true, updatable = true)
    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    @Basic
    @Column(name = "PARENT_SERIAL_NUMBER", nullable = false, insertable = true, updatable = true)
    public int getParentSerialNumber() {
        return parentSerialNumber;
    }

    public void setParentSerialNumber(int parentSerialNumber) {
        this.parentSerialNumber = parentSerialNumber;
    }

    @Basic
    @Column(name = "NAME", nullable = true, insertable = true, updatable = true, length = 255)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "STATUS", nullable = true, insertable = true, updatable = true)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "REMARK", nullable = true, insertable = true, updatable = true, length = 255)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ConversionType that = (ConversionType) o;

        if (conversionTypeId != that.conversionTypeId) return false;
        if (serialNumber != that.serialNumber) return false;
        if (parentSerialNumber != that.parentSerialNumber) return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (status != null ? !status.equals(that.status) : that.status != null) return false;
        if (remark != null ? !remark.equals(that.remark) : that.remark != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (conversionTypeId ^ (conversionTypeId >>> 32));
        result = 31 * result + serialNumber;
        result = 31 * result + parentSerialNumber;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (remark != null ? remark.hashCode() : 0);
        return result;
    }
}
