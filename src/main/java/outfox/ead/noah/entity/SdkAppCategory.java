package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * Created by lizai on 16/5/19.
 */
@Entity
public class SdkAppCategory {
    private long sdkAppCategoryId;
    private int sdkAppParentCategory;
    private int sdkAppCategory;
    private String sdkAppCategoryName;
    private Integer sdkAppCategoryStatus;
    private Long createTime;
    private Long lastModTime;

    @Id
    @Column(name = "SDK_APP_CATEGORY_ID", nullable = false, insertable = true, updatable = true)
    public long getSdkAppCategoryId() {
        return sdkAppCategoryId;
    }

    public void setSdkAppCategoryId(long sdkAppCategoryId) {
        this.sdkAppCategoryId = sdkAppCategoryId;
    }

    @Basic
    @Column(name = "SDK_APP_PARENT_CATEGORY", nullable = false, insertable = true, updatable = true)
    public int getSdkAppParentCategory() {
        return sdkAppParentCategory;
    }

    public void setSdkAppParentCategory(int sdkAppParentCategory) {
        this.sdkAppParentCategory = sdkAppParentCategory;
    }

    @Basic
    @Column(name = "SDK_APP_CATEGORY", nullable = false, insertable = true, updatable = true)
    public int getSdkAppCategory() {
        return sdkAppCategory;
    }

    public void setSdkAppCategory(int sdkAppCategory) {
        this.sdkAppCategory = sdkAppCategory;
    }

    @Basic
    @Column(name = "SDK_APP_CATEGORY_NAME", nullable = true, insertable = true, updatable = true, length = 255)
    public String getSdkAppCategoryName() {
        return sdkAppCategoryName;
    }

    public void setSdkAppCategoryName(String sdkAppCategoryName) {
        this.sdkAppCategoryName = sdkAppCategoryName;
    }

    @Basic
    @Column(name = "SDK_APP_CATEGORY_STATUS", nullable = true, insertable = true, updatable = true)
    public Integer getSdkAppCategoryStatus() {
        return sdkAppCategoryStatus;
    }

    public void setSdkAppCategoryStatus(Integer sdkAppCategoryStatus) {
        this.sdkAppCategoryStatus = sdkAppCategoryStatus;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SdkAppCategory that = (SdkAppCategory) o;

        if (sdkAppCategoryId != that.sdkAppCategoryId) return false;
        if (sdkAppParentCategory != that.sdkAppParentCategory) return false;
        if (sdkAppCategory != that.sdkAppCategory) return false;
        if (sdkAppCategoryName != null ? !sdkAppCategoryName.equals(that.sdkAppCategoryName) : that.sdkAppCategoryName != null)
            return false;
        if (sdkAppCategoryStatus != null ? !sdkAppCategoryStatus.equals(that.sdkAppCategoryStatus) : that.sdkAppCategoryStatus != null)
            return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(that.lastModTime) : that.lastModTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (sdkAppCategoryId ^ (sdkAppCategoryId >>> 32));
        result = 31 * result + sdkAppParentCategory;
        result = 31 * result + sdkAppCategory;
        result = 31 * result + (sdkAppCategoryName != null ? sdkAppCategoryName.hashCode() : 0);
        result = 31 * result + (sdkAppCategoryStatus != null ? sdkAppCategoryStatus.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        return result;
    }
}
