package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * Created by lizai on 16/5/19.
 */
@Entity
public class SdkAccountRate {
    private long sdkAccountId;
    private long sdkAppId;
    private Integer sdkAccountDate;
    private Integer sdkAccountRate;
    private Long createTime;
    private Long lastModTime;

    @Id
    @Column(name = "SDK_ACCOUNT_ID", nullable = false, insertable = true, updatable = true)
    public long getSdkAccountId() {
        return sdkAccountId;
    }

    public void setSdkAccountId(long sdkAccountId) {
        this.sdkAccountId = sdkAccountId;
    }

    @Basic
    @Column(name = "SDK_APP_ID", nullable = false, insertable = true, updatable = true)
    public long getSdkAppId() {
        return sdkAppId;
    }

    public void setSdkAppId(long sdkAppId) {
        this.sdkAppId = sdkAppId;
    }

    @Basic
    @Column(name = "SDK_ACCOUNT_DATE", nullable = true, insertable = true, updatable = true)
    public Integer getSdkAccountDate() {
        return sdkAccountDate;
    }

    public void setSdkAccountDate(Integer sdkAccountDate) {
        this.sdkAccountDate = sdkAccountDate;
    }

    @Basic
    @Column(name = "SDK_ACCOUNT_RATE", nullable = true, insertable = true, updatable = true)
    public Integer getSdkAccountRate() {
        return sdkAccountRate;
    }

    public void setSdkAccountRate(Integer sdkAccountRate) {
        this.sdkAccountRate = sdkAccountRate;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SdkAccountRate that = (SdkAccountRate) o;

        if (sdkAccountId != that.sdkAccountId) return false;
        if (sdkAppId != that.sdkAppId) return false;
        if (sdkAccountDate != null ? !sdkAccountDate.equals(that.sdkAccountDate) : that.sdkAccountDate != null)
            return false;
        if (sdkAccountRate != null ? !sdkAccountRate.equals(that.sdkAccountRate) : that.sdkAccountRate != null)
            return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(that.lastModTime) : that.lastModTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (sdkAccountId ^ (sdkAccountId >>> 32));
        result = 31 * result + (int) (sdkAppId ^ (sdkAppId >>> 32));
        result = 31 * result + (sdkAccountDate != null ? sdkAccountDate.hashCode() : 0);
        result = 31 * result + (sdkAccountRate != null ? sdkAccountRate.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        return result;
    }
}
