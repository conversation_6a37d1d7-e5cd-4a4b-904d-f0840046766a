package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Created by yuan<PERSON><PERSON><PERSON> on 2017/8/31.
 */
@Entity
@Table(name = "CERT_TYPE")
public class CertType {
    private int certTypeId;
    private int parentId;
    private String name;
    private String remark;

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "CERT_TYPE_ID", nullable = false)
    public int getCertTypeId() {
        return certTypeId;
    }

    public void setCertTypeId(int certTypeId) {
        this.certTypeId = certTypeId;
    }

    @Basic
    @Column(name = "PARENT_ID", nullable = false)
    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    @Basic
    @Column(name = "NAME", nullable = true, length = 256)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "REMARK", nullable = true, length = 256)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CertType certType = (CertType) o;

        if (certTypeId != certType.certTypeId) return false;
        if (parentId != certType.parentId) return false;
        if (name != null ? !name.equals(certType.name) : certType.name != null) return false;
        if (remark != null ? !remark.equals(certType.remark) : certType.remark != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = certTypeId;
        result = 31 * result + parentId;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (remark != null ? remark.hashCode() : 0);
        return result;
    }
}
