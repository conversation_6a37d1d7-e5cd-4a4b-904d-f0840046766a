package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Created by huanghuan on 16/5/3.
 */
@Entity
@Table(name = "AdTemplate")
public class AdTemplate {
    private long templaeteId;
    private String templateName;
    private String configJson;
    private String defaultStyle;
    private String htmlStructure;
    private String imageUrl;
    private Long createdTime = 0L;
    private Long lastModTime = 0L;
    private Integer templateWidth = 0;
    private Integer templateHeight = 0;
    private Integer status = 0;
    private String datas;

    @Id
    @GeneratedValue(generator = "identityGenerator")
    @GenericGenerator(name = "identityGenerator", strategy = "identity")
    @Column(name = "TEMPLAETE_ID", nullable = false, insertable = true, updatable = true)
    public long getTemplaeteId() {
        return templaeteId;
    }

    public void setTemplaeteId(long templaeteId) {
        this.templaeteId = templaeteId;
    }

    @Basic
    @Column(name = "TEMPLATE_NAME", nullable = true, insertable = true, updatable = true, length = 255)
    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    @Basic
    @Column(name = "CONFIG_JSON", nullable = true, insertable = true, updatable = true, length = 65535)
    public String getConfigJson() {
        return configJson;
    }

    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }

    @Basic
    @Column(name = "DEFAULT_STYLE", nullable = true, insertable = true, updatable = true, length = 65535)
    public String getDefaultStyle() {
        return defaultStyle;
    }

    public void setDefaultStyle(String defaultStyle) {
        this.defaultStyle = defaultStyle;
    }

    @Basic
    @Column(name = "HTML_STRUCTURE", nullable = true, insertable = true, updatable = true, length = 65535)
    public String getHtmlStructure() {
        return htmlStructure;
    }

    public void setHtmlStructure(String htmlStructure) {
        this.htmlStructure = htmlStructure;
    }

    @Basic
    @Column(name = "IMAGE_URL", nullable = true, insertable = true, updatable = true, length = 1023)
    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Basic
    @Column(name = "CREATED_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Basic
    @Column(name = "TEMPLATE_WIDTH", nullable = true, insertable = true, updatable = true)
    public Integer getTemplateWidth() {
        return templateWidth;
    }

    public void setTemplateWidth(Integer templateWidth) {
        this.templateWidth = templateWidth;
    }

    @Basic
    @Column(name = "TEMPLATE_HEIGHT", nullable = true, insertable = true, updatable = true)
    public Integer getTemplateHeight() {
        return templateHeight;
    }

    public void setTemplateHeight(Integer templateHeight) {
        this.templateHeight = templateHeight;
    }

    @Basic
    @Column(name = "STATUS", nullable = true, insertable = true, updatable = true)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "DATAS", nullable = true, insertable = true, updatable = true, length = 65535)
    public String getDatas() {
        return datas;
    }

    public void setDatas(String datas) {
        this.datas = datas;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AdTemplate that = (AdTemplate) o;

        if (templaeteId != that.templaeteId) return false;
        if (templateName != null ? !templateName.equals(that.templateName) : that.templateName != null) return false;
        if (configJson != null ? !configJson.equals(that.configJson) : that.configJson != null) return false;
        if (defaultStyle != null ? !defaultStyle.equals(that.defaultStyle) : that.defaultStyle != null) return false;
        if (htmlStructure != null ? !htmlStructure.equals(that.htmlStructure) : that.htmlStructure != null)
            return false;
        if (imageUrl != null ? !imageUrl.equals(that.imageUrl) : that.imageUrl != null) return false;
        if (createdTime != null ? !createdTime.equals(that.createdTime) : that.createdTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(that.lastModTime) : that.lastModTime != null) return false;
        if (templateWidth != null ? !templateWidth.equals(that.templateWidth) : that.templateWidth != null)
            return false;
        if (templateHeight != null ? !templateHeight.equals(that.templateHeight) : that.templateHeight != null)
            return false;
        if (status != null ? !status.equals(that.status) : that.status != null) return false;
        if (datas != null ? !datas.equals(that.datas) : that.datas != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (templaeteId ^ (templaeteId >>> 32));
        result = 31 * result + (templateName != null ? templateName.hashCode() : 0);
        result = 31 * result + (configJson != null ? configJson.hashCode() : 0);
        result = 31 * result + (defaultStyle != null ? defaultStyle.hashCode() : 0);
        result = 31 * result + (htmlStructure != null ? htmlStructure.hashCode() : 0);
        result = 31 * result + (imageUrl != null ? imageUrl.hashCode() : 0);
        result = 31 * result + (createdTime != null ? createdTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        result = 31 * result + (templateWidth != null ? templateWidth.hashCode() : 0);
        result = 31 * result + (templateHeight != null ? templateHeight.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (datas != null ? datas.hashCode() : 0);
        return result;
    }
}
