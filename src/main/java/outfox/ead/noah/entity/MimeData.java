package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.util.Arrays;

/**
 * Created by yuanzhch on 2017/6/27.
 */
@Entity
public class MimeData {
    private String mimeId;
    private byte[] data;

    @Id
    @Column(name = "mimeId")
    public String getMimeId() {
        return mimeId;
    }

    public void setMimeId(String mimeId) {
        this.mimeId = mimeId;
    }

    @Basic
    @Column(name = "data")
    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        MimeData mimeData = (MimeData) o;

        if (mimeId != null ? !mimeId.equals(mimeData.mimeId) : mimeData.mimeId != null) return false;
        if (!Arrays.equals(data, mimeData.data)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = mimeId != null ? mimeId.hashCode() : 0;
        result = 31 * result + Arrays.hashCode(data);
        return result;
    }
}
