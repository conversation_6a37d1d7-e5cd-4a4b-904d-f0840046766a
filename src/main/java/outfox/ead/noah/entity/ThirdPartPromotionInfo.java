package outfox.ead.noah.entity;

import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import outfox.ead.noah.util.code.SourceCode;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * 描述:第三方平台广告监测相关信息实体类
 *
 * <AUTHOR>
 * @create 2018-07-20 19:55
 */
@Entity
@ToString
@EqualsAndHashCode
public class ThirdPartPromotionInfo {
    private Long promotionId;
    private Long sponsorId;
    private String promotionUID;
    private String promotionName;
    private Integer promotionType;
    private Boolean hasSubchannel;
    private String destLink;
    private String newDestLink;
    private String crowdTag;
    private String syncClickMonitorLink;
    private String asyncClickMonitorLink;
    private String exposureMonitorLink;
    private Integer useConvertTracking;
    private String convertTrackingUID;
    private String convertTrackingAction;
    private Long adContentId;
    private Boolean enableGeoTargeting;
    private Long dailyClickLimit;
    private Integer tdpPlatform;
    private String tdpCreativeIds;
    private String tdpSponsorIds;
    private String secretKey;
    private String clientId;
    private String packageName;
    private String tdpConvertId;
    private Timestamp createTime;
    private Timestamp lastModTime;
    private Integer promotionStatus;
    private Boolean useRta;
    private String xiaomiRtaTokenPair;
    private String huaweiRtaIdPairs;
    private String honorRtaIdPairs;
    private String wifiRtaIdParis;
    private String oppoAdIdPairs;
    private String baiduAdIdPairs;
    private Integer strategy;
    private Boolean accelAreaFilter;
    private String accelArea;
    private Integer accelDeviceIdType;
    private String accelEffectPeriod;
    private Integer accelReportDailyLimit;
    private Integer accelAlgPercent;
    private String accelCampId;
    private String accelMediaPkgId;
    private String accelSuperficialTarget;
    private String accelDeepTargets;
    private Integer mediaWithCampType;
    private Integer activityType;
    private String nonRtaContentIds;
    private Long rtaContentId;
    private Boolean useWechatMiniProgram;
    private String wechatPath;
    private String wechatOriginId;
    private String urlScheme;
    /**
     * Spider系统直投开关状态 0-关闭 1-开启
     */
    private Integer directDeliveryStatus;

    /**
     * 渠道投放开关，控制推广活动点击监测的接收开启和暂停，0：暂停，1：开启
     */
    private Integer operateStatus;

    /**
     * 创建来源 0-智选 1-Spider
     */
    private Integer source = SourceCode.FROM_ZHIXUAN;
    public static final String PROMOTION_ID = "promotionId";
    public static final String THIRD_PART_SPONSOR_ID = "sponsorId";
    public static final String PROMOTION_UID = "promotionUID";
    public static final String PROMOTION_NAME = "promotionName";
    public static final String PROMOTION_TYPE = "promotionType";
    public static final String HAS_SUBCHANNEL = "hasSubchannel";
    public static final String DEST_LINK = "destLink";
    public static final String NEW_DEST_LINK = "newDestLink";
    public static final String CROWD_TAG = "crowdTag";
    public static final String SYNC_CLICK_MONITOR_LINK = "syncClickMonitorLink";
    public static final String ASYN_CLICK_MONITOR_LINK = "asyncClickMonitorLink";
    public static final String EXPOSURE_MONITOR_LINK = "exposureMonitorLink";
    public static final String USE_CONVERTTRSCKING = "useConvertTracking";
    public static final String CONVERT_TRACKING_UID = "convertTrackingUID";
    public static final String CONVERT_TRACKING_ACTION = "convertTrackingAction";
    public static final String USE_RELATED_AD_CONTENT = "useRelateAdContent";
    public static final String IS_AD_CONTENT_VALID = "isAdContentValid";
    public static final String AD_CONTENT_ID = "adContentId";
    public static final String ENABLE_GEO_TARGETING = "enableGeoTargeting";
    public static final String DAILY_CLICK_LIMIT = "dailyClickLimit";
    public static final String TDP_PLATFORM = "tdpPlatform";
    public static final String TDP_CREATIVE_IDS = "tdpCreativeIds";
    public static final String TDP_SPONSOR_IDS = "tdpSponsorIds";
    public static final String TDP_SECRET_KEY = "secretKey";
    public static final String TDP_CLIENT_ID = "clientId";
    public static final String TDP_PACKAGE_NAME = "packageName";
    public static final String TDP_CONVERT_ID = "tdpConvertId";
    public static final String USE_RTA = "useRta";
    public static final String XIAOMI_RTA_TOKEN_PAIR = "xiaomiRtaTokenPairs";
    public static final String HUAWEI_RTA_ID_PAIRS = "huaweiRtaIdPairs";
    public static final String HONOR_RTA_ID_PAIRS = "honorRtaIdPairs";
    public static final String WIFI_RTA_ID_PAIRS = "wifiRtaIdPairs";
    public static final String OPPO_AD_ID_PAIRS = "oppoAdIdPairs";
    public static final String BAIDU_AD_ID_PAIRS = "baiduRtaIdPairs";
    public static final String CONV_EXT = "convExt";
    public static final String RELATE_AD_CONTENT_INFO = "relateAdContentInfo";
    public static final String PROMOTION_STATUS = "promotionStatus";
    public static final String KEYWORD = "keyword";
    public static final String STRATEGY = "strategy";
    public static final String ACCEL_AREA_FILTER = "accelAreaFilter";
    public static final String ACCEL_AREA = "accelArea";
    public static final String ACCEL_DEVICE_ID_TYPE = "accelDeviceIdType";
    public static final String ACCEL_EFFECT_PERIOD = "accelEffectPeriod";
    public static final String ACCEL_REPORT_DAILY_LIMIT = "accelReportDailyLimit";
    public static final String ACCEL_ALG_PERCENT = "accelAlgPercent";
    public static final String ACCEL_CAMP_ID = "accelCampId";
    public static final String ACCEL_CAMP_ID_2_NAME_EXPIRED = "accelCampId2NameExpired";
    public static final String ACCEL_MEDIA_PKG_ID = "accelMediaPkgId";
    public static final String ACCEL_SUPERFICIAL_TARGET = "accelSuperficialTarget";
    public static final String ACCEL_DEEP_TARGETS = "accelDeepTargets";
    public static final String MEDIA_WITH_CAMP_TYPE = "mediaWithCampType";

    public static final String ACTIVITY_TYPE = "activityType";
    public static final String NON_RTA_CONTENT_IDS = "nonRtaContentIds";
    public static final String RTA_CONTENT_ID = "rtaContentId";
    public static final String USE_WECHAT_MINI_PROGRAM = "useWechatMiniProgram";
    public static final String WECHAT_PATH = "wechatPath";
    public static final String WECHAT_ORIGIN_ID = "wechatOriginId";
    public static final String URLSCHEME = "urlScheme";

    public static final String DIRECT_DELIVERY_STATUS = "directDeliveryStatus";
    public static final String SOURCE = "source";
    // 前端接口交互使用的字段名称
    public static final String OP_STATUS = "opStatus";
    // 数据库存储使用的字段名称
    public static final String OPERATE_STATUS = "operateStatus";

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "PROMOTION_ID", nullable = false)
    public Long getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(Long promotionId) {
        this.promotionId = promotionId;
    }

    @Basic
    @Column(name = "SPONSOR_ID", nullable = false)
    public Long getSponsorId() {
        return sponsorId;
    }

    public void setSponsorId(Long sponsorId) {
        this.sponsorId = sponsorId;
    }

    @Basic
    @Column(name = "PROMOTION_UID", nullable = false, length = 255)
    public String getPromotionUID() {
        return promotionUID;
    }

    public void setPromotionUID(String promotionUID) {
        this.promotionUID = promotionUID;
    }

    @Basic
    @Column(name = "PROMOTION_NAME", nullable = false, length = 255)
    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    @Basic
    @Column(name = "PROMOTION_TYPE", nullable = false)
    public Integer getPromotionType() {
        return promotionType;
    }

    public void setPromotionType(Integer promotionType) {
        this.promotionType = promotionType;
    }

    @Basic
    @Column(name = "HAS_SUBCHANNEL", nullable = false)
    public Boolean getHasSubchannel() {
        return hasSubchannel;
    }

    public void setHasSubchannel(Boolean hasSubchannel) {
        this.hasSubchannel = hasSubchannel;
    }

    @Basic
    @Column(name = "DEST_LINK", nullable = false, length = 1024)
    public String getDestLink() {
        return destLink;
    }

    public void setDestLink(String destLink) {
        this.destLink = destLink;
    }

    @Basic
    @Column(name = "NEW_DEST_LINK", nullable = true, length = 1024)
    public String getNewDestLink() {
        return newDestLink;
    }

    public void setNewDestLink(String newDestLink) {
        this.newDestLink = newDestLink;
    }

    @Basic
    @Column(name = "CROWD_TAG", nullable = false, length = 1024)
    public String getCrowdTag() {
        return crowdTag;
    }

    public void setCrowdTag(String crowdTag) {
        this.crowdTag = crowdTag;
    }

    @Basic
    @Column(name = "SYNC_CLICK_MONITOR_LINK", nullable = true, length = 2048)
    public String getSyncClickMonitorLink() {
        return syncClickMonitorLink;
    }

    public void setSyncClickMonitorLink(String syncClickMonitorLink) {
        this.syncClickMonitorLink = syncClickMonitorLink;
    }

    @Basic
    @Column(name = "ASYNC_CLICK_MONITOR_LINK", nullable = true, length = 2048)
    public String getAsyncClickMonitorLink() {
        return asyncClickMonitorLink;
    }

    public void setAsyncClickMonitorLink(String asyncClickMonitorLink) {
        this.asyncClickMonitorLink = asyncClickMonitorLink;
    }

    @Basic
    @Column(name = "EXPOSURE_MONITOR_LINK", nullable = true, length = 2048)
    public String getExposureMonitorLink() {
        return exposureMonitorLink;
    }

    public void setExposureMonitorLink(String exposureMonitorLink) {
        this.exposureMonitorLink = exposureMonitorLink;
    }

    @Basic
    @Column(name = "USE_CONVERT_TRACKING", nullable = false)
    public Integer getUseConvertTracking() {
        return useConvertTracking;
    }

    public void setUseConvertTracking(Integer useConvertTracking) {
        this.useConvertTracking = useConvertTracking;
    }

    @Basic
    @Column(name = "CONVERT_TRACKING_UID", length = 255)
    public String getConvertTrackingUID() {
        return convertTrackingUID;
    }

    public void setConvertTrackingUID(String convertTrackingUID) {
        this.convertTrackingUID = convertTrackingUID;
    }

    @Basic
    @Column(name = "CONVERT_TRACKING_ACTION", length = 255)
    public String getConvertTrackingAction() {
        return convertTrackingAction;
    }

    public void setConvertTrackingAction(String convetrTrackingAction) {
        this.convertTrackingAction = convetrTrackingAction;
    }

    @Basic
    @Column(name = "RELATE_AD_CONTENT_ID", nullable = true)
    public Long getAdContentId() {
        return adContentId;
    }

    public void setAdContentId(Long adContentId) {
        this.adContentId = adContentId;
    }

    @Basic
    @Column(name = "ENABLE_GEO_TARGETING", nullable = false)
    public Boolean getEnableGeoTargeting() {
        return enableGeoTargeting;
    }

    public void setEnableGeoTargeting(Boolean enableGeoTargeting) {
        this.enableGeoTargeting = enableGeoTargeting;
    }

    @Basic
    @Column(name = "DAILY_CLICK_LIMIT")
    public Long getDailyClickLimit() {
        return dailyClickLimit;
    }

    public void setDailyClickLimit(Long dailyClickLimit) {
        this.dailyClickLimit = dailyClickLimit;
    }

    @Basic
    @Column(name = "TDP_PLATFORM", nullable = true)
    public Integer getTdpPlatform() {
        return tdpPlatform;
    }

    public void setTdpPlatform(Integer tdpPlatform) {
        this.tdpPlatform = tdpPlatform;
    }

    @Basic
    @Column(name = "TDP_CREATIVE_IDS", nullable = true)
    public String getTdpCreativeIds() {
        return tdpCreativeIds;
    }

    public void setTdpCreativeIds(String tdpCreativeIds) {
        this.tdpCreativeIds = tdpCreativeIds;
    }

    @Basic
    @Column(name = "TDP_SPONSOR_IDS", nullable = true)
    public String getTdpSponsorIds() {
        return tdpSponsorIds;
    }

    public void setTdpSponsorIds(String tdpSponsorIds) {
        this.tdpSponsorIds = tdpSponsorIds;
    }


    @Basic
    @Column(name = "SECRET_KEY", nullable = true)
    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    @Basic
    @Column(name = "CLIENT_ID", nullable = true)
    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    @Basic
    @Column(name = "PACKAGE_NAME", nullable = true)
    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    @Basic
    @Column(name = "TDP_CONVERT_ID", nullable = true)
    public String getTdpConvertId() {
        return tdpConvertId;
    }

    public void setTdpConvertId(String tdpConvertId) {
        this.tdpConvertId = tdpConvertId;
    }

    @Basic
    @Column(name = "CREATE_TIME")
    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME")
    public Timestamp getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Timestamp lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Basic
    @Column(name = "PROMOTION_STATUS", nullable = false)
    public Integer getPromotionStatus() {
        return promotionStatus;
    }

    public void setPromotionStatus(Integer promotionStatus) {
        this.promotionStatus = promotionStatus;
    }

    @Basic
    @Column(name = "USE_RTA", nullable = false)
    public Boolean getUseRta() {
        return useRta;
    }

    public void setUseRta(Boolean useRta) {
        this.useRta = useRta;
    }

    @Basic
    @Column(name = "XIAOMI_RTA_TOKEN_PAIR")
    public String getXiaomiRtaTokenPair() {
        return xiaomiRtaTokenPair;
    }

    public void setXiaomiRtaTokenPair(String xiaomiRtaTokenPair) {
        this.xiaomiRtaTokenPair = xiaomiRtaTokenPair;
    }

    @Basic
    @Column(name = "HUAWEI_RTA_ID_PAIR")
    public String getHuaweiRtaIdPairs() {
        return huaweiRtaIdPairs;
    }

    public void setHuaweiRtaIdPairs(String huaweiRtaIdPairs) {
        this.huaweiRtaIdPairs = huaweiRtaIdPairs;
    }

    @Basic
    @Column(name = "HONOR_RTA_ID_PAIR")
    public String getHonorRtaIdPairs() {
        return honorRtaIdPairs;
    }

    public void setHonorRtaIdPairs(String honorRtaIdPairs) {
        this.honorRtaIdPairs = honorRtaIdPairs;
    }

    @Basic
    @Column(name = "WIFI_RTA_ID_PAIR")
    public String getWifiRtaIdParis() {
        return wifiRtaIdParis;
    }

    public void setWifiRtaIdParis(String wifiRtaIdParis) {
        this.wifiRtaIdParis = wifiRtaIdParis;
    }

    @Basic
    @Column(name = "OPPO_AD_ID_PAIR")
    public String getOppoAdIdPairs() {
        return oppoAdIdPairs;
    }

    public void setOppoAdIdPairs(String oppoAdIdPairs) {
        this.oppoAdIdPairs = oppoAdIdPairs;
    }

    @Basic
    @Column(name = "BAIDU_RTA_ID_PAIR")
    public String getBaiduRtaIdPairs() {
        return baiduAdIdPairs;
    }

    public void setBaiduRtaIdPairs(String baiduRtaIdPairs) {
        this.baiduAdIdPairs = baiduRtaIdPairs;
    }


    @Basic
    @Column(name = "STRATEGY")
    public Integer getStrategy() {
        return strategy;
    }

    public void setStrategy(Integer strategy) {
        this.strategy = strategy;
    }
  

    @Basic
    @Column(name = "ACCEL_AREA_FILTER")
    public Boolean getAccelAreaFilter() {
        return accelAreaFilter;
    }

    public void setAccelAreaFilter(Boolean accelAreaFilter) {
        this.accelAreaFilter = accelAreaFilter;
    }

    @Basic
    @Column(name = "ACCEL_AREA")
    public String getAccelArea() {
        return accelArea;
    }

    public void setAccelArea(String accelArea) {
        this.accelArea = accelArea;
    }

    @Basic
    @Column(name = "ACCEL_DEVICE_ID_TYPE")
    public Integer getAccelDeviceIdType() {
        return accelDeviceIdType;
    }

    public void setAccelDeviceIdType(Integer accelDeviceIdType) {
        this.accelDeviceIdType = accelDeviceIdType;
    }

    @Basic
    @Column(name = "ACCEL_EFFECT_PERIOD")
    public String getAccelEffectPeriod() {
        return accelEffectPeriod;
    }

    public void setAccelEffectPeriod(String accelEffectPeriod) {
        this.accelEffectPeriod = accelEffectPeriod;
    }

    @Basic
    @Column(name = "ACCEL_REPORT_DAILY_LIMIT")
    public Integer getAccelReportDailyLimit() {
        return accelReportDailyLimit;
    }


    public void setAccelReportDailyLimit(Integer accelReportDailyLimit) {
        this.accelReportDailyLimit = accelReportDailyLimit;
    }

    // 功能已失效，不再手动配置比例，已经全部改为程序自动识别分配
    @Deprecated
    @Basic
    @Column(name = "ACCEL_ALG_PERCENT")
    public Integer getAccelAlgPercent() {
        return accelAlgPercent;
    }

    public void setAccelAlgPercent(Integer accelAlgPercent) {
        this.accelAlgPercent = accelAlgPercent;
    }

    @Basic
    @Column(name = "ACCEL_CAMP_ID")
    public String getAccelCampId() {
        return accelCampId;
    }

    public void setAccelCampId(String accelCampId) {
        this.accelCampId = accelCampId;
    }

    @Basic
    @Column(name = "ACCEL_MEDIA_PKG_ID")
    public String getAccelMediaPkgId() {
        return accelMediaPkgId;
    }

    public void setAccelMediaPkgId(String accelMediaPkgId) {
        this.accelMediaPkgId = accelMediaPkgId;
    }

    @Basic
    @Column(name = "ACCEL_SUPERFICIAL_TARGET")
    public String getAccelSuperficialTarget() {
        return accelSuperficialTarget;
    }

    public void setAccelSuperficialTarget(String accelSuperficialTarget) {
        this.accelSuperficialTarget = accelSuperficialTarget;
    }

    @Basic
    @Column(name = "ACCEL_DEEP_TARGETS")
    public String getAccelDeepTargets() {
        return accelDeepTargets;
    }

    public void setAccelDeepTargets(String accelDeepTargets) {
        this.accelDeepTargets = accelDeepTargets;
    }

    @Basic
    @Column(name = "MEDIA_WITH_CAMP_TYPE")
    public Integer getMediaWithCampType() {
        return mediaWithCampType;
    }

    public void setMediaWithCampType(Integer mediaWithCampType) {
        this.mediaWithCampType = mediaWithCampType;
    }
    
    @Basic
    @Column(name = "ACTIVITY_TYPE")
    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }
    
    @Column(name = "NON_RTA_CONTENT_IDS")
    public String getNonRtaContentIds() {
        return nonRtaContentIds;
    }

    public void setNonRtaContentIds(String nonRtaContentIds) {
        this.nonRtaContentIds = nonRtaContentIds;
    }

    @Basic
    @Column(name = "RTA_CONTENT_ID")
    public Long getRtaContentId() {
        return rtaContentId;
    }

    @Basic
    @Column(name = "USE_WECHAT_MINI_PROGRAM")
    public Boolean getUseWechatMiniProgram() {
        return useWechatMiniProgram;
    }

    public void setUseWechatMiniProgram(Boolean useWechatMiniProgram) {
        this.useWechatMiniProgram = useWechatMiniProgram;
    }

    @Basic
    @Column(name = "WECHAT_PATH")
    public String getWechatPath() {
        return wechatPath;
    }

    public void setWechatPath(String wechatPath) {
        this.wechatPath = wechatPath;
    }

    @Basic
    @Column(name = "WECHAT_ORIGIN_ID")
    public String getWechatOriginId() {
        return wechatOriginId;
    }

    public void setWechatOriginId(String wechatOriginId) {
        this.wechatOriginId = wechatOriginId;
    }

    @Basic
    @Column(name = "URL_SCHEME", nullable = false)
    public String getUrlScheme() {
        return urlScheme;
    }

    public void setUrlScheme(String urlScheme) {
        this.urlScheme = urlScheme;
    }

    public void setRtaContentId(Long rtaContentId) {
        this.rtaContentId = rtaContentId;
    }

    @Basic
    @Column(name = "DIRECT_DELIVERY_STATUS", nullable = true)
    public Integer getDirectDeliveryStatus() {
        return directDeliveryStatus;
    }

    public void setDirectDeliveryStatus(Integer directDeliveryStatus) {
        this.directDeliveryStatus = directDeliveryStatus;
    }

    @Basic
    @Column(name = "SOURCE", nullable = false)
    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Basic
    @Column(name = "OPERATE_STATUS", nullable = false)
    public Integer getOperateStatus(){
        return operateStatus;
    }

    public void setOperateStatus(Integer operateStatus) {
        this.operateStatus = operateStatus;
    }


}
