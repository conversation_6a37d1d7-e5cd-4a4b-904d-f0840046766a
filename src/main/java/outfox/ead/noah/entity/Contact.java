package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Created by huanghuan on 16/4/25.
 */
@Entity
@Table(name = "Contact")
public class Contact {
    private long contactId;
    private String name;
    private String email;
    private String address;
    private String fax;
    private String imNum;
    private String imType;
    private String mobile;
    private String phone;
    private String phonePrefix;
    private String postCode;
    private Long createTime = 0L;
    private Long lastModTime = 0L;

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "CONTACT_ID", nullable = false, insertable = true, updatable = true)
    public long getContactId() {
        return contactId;
    }

    public void setContactId(long contactId) {
        this.contactId = contactId;
    }

    @Basic
    @Column(name = "NAME", nullable = true, insertable = true, updatable = true, length = 255)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "EMAIL", nullable = true, insertable = true, updatable = true, length = 255)
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Basic
    @Column(name = "ADDRESS", nullable = true, insertable = true, updatable = true, length = 255)
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Basic
    @Column(name = "FAX", nullable = true, insertable = true, updatable = true, length = 255)
    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    @Basic
    @Column(name = "IM_NUM", nullable = true, insertable = true, updatable = true, length = 255)
    public String getImNum() {
        return imNum;
    }

    public void setImNum(String imNum) {
        this.imNum = imNum;
    }

    @Basic
    @Column(name = "IM_TYPE", nullable = true, insertable = true, updatable = true, length = 255)
    public String getImType() {
        return imType;
    }

    public void setImType(String imType) {
        this.imType = imType;
    }

    @Basic
    @Column(name = "MOBILE", nullable = true, insertable = true, updatable = true, length = 255)
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @Basic
    @Column(name = "PHONE", nullable = true, insertable = true, updatable = true, length = 255)
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Basic
    @Column(name = "PHONE_PREFIX", nullable = true, insertable = true, updatable = true, length = 255)
    public String getPhonePrefix() {
        return phonePrefix;
    }

    public void setPhonePrefix(String phonePrefix) {
        this.phonePrefix = phonePrefix;
    }

    @Basic
    @Column(name = "POST_CODE", nullable = true, insertable = true, updatable = true, length = 255)
    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Contact contact = (Contact) o;

        if (contactId != contact.contactId) return false;
        if (name != null ? !name.equals(contact.name) : contact.name != null) return false;
        if (email != null ? !email.equals(contact.email) : contact.email != null) return false;
        if (address != null ? !address.equals(contact.address) : contact.address != null) return false;
        if (fax != null ? !fax.equals(contact.fax) : contact.fax != null) return false;
        if (imNum != null ? !imNum.equals(contact.imNum) : contact.imNum != null) return false;
        if (imType != null ? !imType.equals(contact.imType) : contact.imType != null) return false;
        if (mobile != null ? !mobile.equals(contact.mobile) : contact.mobile != null) return false;
        if (phone != null ? !phone.equals(contact.phone) : contact.phone != null) return false;
        if (phonePrefix != null ? !phonePrefix.equals(contact.phonePrefix) : contact.phonePrefix != null) return false;
        if (postCode != null ? !postCode.equals(contact.postCode) : contact.postCode != null) return false;
        if (createTime != null ? !createTime.equals(contact.createTime) : contact.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(contact.lastModTime) : contact.lastModTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (contactId ^ (contactId >>> 32));
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (email != null ? email.hashCode() : 0);
        result = 31 * result + (address != null ? address.hashCode() : 0);
        result = 31 * result + (fax != null ? fax.hashCode() : 0);
        result = 31 * result + (imNum != null ? imNum.hashCode() : 0);
        result = 31 * result + (imType != null ? imType.hashCode() : 0);
        result = 31 * result + (mobile != null ? mobile.hashCode() : 0);
        result = 31 * result + (phone != null ? phone.hashCode() : 0);
        result = 31 * result + (phonePrefix != null ? phonePrefix.hashCode() : 0);
        result = 31 * result + (postCode != null ? postCode.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        return result;
    }
}
