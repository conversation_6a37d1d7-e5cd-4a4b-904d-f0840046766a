package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * Created by lizai on 16/5/19.
 */
@Entity
public class SdkAppInfo {
    private long sdkAppId;
    private long sdkAppDeveloperId;
    private String sdkAppName;
    private String sdkAppPakageName;
    private String sdkAppVersion;
    private Integer sdkAppStatus;
    private Integer sdkAppOsType;
    private Integer sdkAppCategory;
    private Long sdkAppFlowAmount;
    private Long createTime;
    private Long lastModTime;
    private Integer synId;
    private String decryptionKey;
    private String iabCategories;
    private String iabBlockingCategories;
    private Integer trafficAllocateMediaType;


    /**
     * Property name constant for {@code lastModTime}.
     */
    public static final String PROPERTYNAME_LAST_MOD_TIME = "lastModTime";
    /**
     * Property name constant for {@code sdkAppVersion}.
     */
    public static final String PROPERTYNAME_SDK_APP_VERSION = "sdkAppVersion";
    /**
     * Property name constant for {@code sdkAppId}.
     */
    public static final String PROPERTYNAME_SDK_APP_ID = "sdkAppId";
    /**
     * Property name constant for {@code synId}.
     */
    public static final String PROPERTYNAME_SYN_ID = "synId";
    /**
     * Property name constant for {@code iabBlockingCategories}.
     */
    public static final String PROPERTYNAME_IAB_BLOCKING_CATEGORIES = "iabBlockingCategories";
    /**
     * Property name constant for {@code decryptionKey}.
     */
    public static final String PROPERTYNAME_DECRYPTION_KEY = "decryptionKey";
    /**
     * Property name constant for {@code sdkAppDeveloperId}.
     */
    public static final String PROPERTYNAME_SDK_APP_DEVELOPER_ID = "sdkAppDeveloperId";
    /**
     * Property name constant for {@code sdkAppCategory}.
     */
    public static final String PROPERTYNAME_SDK_APP_CATEGORY = "sdkAppCategory";
    /**
     * Property name constant for {@code sdkAppPakageName}.
     */
    public static final String PROPERTYNAME_SDK_APP_PAKAGE_NAME = "sdkAppPakageName";
    /**
     * Property name constant for {@code sdkAppName}.
     */
    public static final String PROPERTYNAME_SDK_APP_NAME = "sdkAppName";
    /**
     * Property name constant for {@code sdkAppFlowAmount}.
     */
    public static final String PROPERTYNAME_SDK_APP_FLOW_AMOUNT = "sdkAppFlowAmount";
    /**
     * Property name constant for {@code iabCategories}.
     */
    public static final String PROPERTYNAME_IAB_CATEGORIES = "iabCategories";
    /**
     * Property name constant for {@code createTime}.
     */
    public static final String PROPERTYNAME_CREATE_TIME = "createTime";
    /**
     * Property name constant for {@code sdkAppOsType}.
     */
    public static final String PROPERTYNAME_SDK_APP_OS_TYPE = "sdkAppOsType";
    /**
     * Property name constant for {@code sdkAppStatus}.
     */
    public static final String PROPERTYNAME_SDK_APP_STATUS = "sdkAppStatus";

    public static final String PROPERTYNAME_TRAFFIC_ALLOCATE_MEDIA_TYPE = "trafficAllocateMediaType";

    @Id
    @Column(name = "SDK_APP_ID", nullable = false, insertable = true, updatable = true)
    public long getSdkAppId() {
        return sdkAppId;
    }

    public void setSdkAppId(long sdkAppId) {
        this.sdkAppId = sdkAppId;
    }

    @Basic
    @Column(name = "SDK_APP_DEVELOPER_ID", nullable = false, insertable = true, updatable = true)
    public long getSdkAppDeveloperId() {
        return sdkAppDeveloperId;
    }

    public void setSdkAppDeveloperId(long sdkAppDeveloperId) {
        this.sdkAppDeveloperId = sdkAppDeveloperId;
    }

    @Basic
    @Column(name = "SDK_APP_NAME", nullable = true, insertable = true, updatable = true, length = 255)
    public String getSdkAppName() {
        return sdkAppName;
    }

    public void setSdkAppName(String sdkAppName) {
        this.sdkAppName = sdkAppName;
    }

    @Basic
    @Column(name = "SDK_APP_PAKAGE_NAME", nullable = true, insertable = true, updatable = true, length = 255)
    public String getSdkAppPakageName() {
        return sdkAppPakageName;
    }

    public void setSdkAppPakageName(String sdkAppPakageName) {
        this.sdkAppPakageName = sdkAppPakageName;
    }

    @Basic
    @Column(name = "SDK_APP_VERSION", nullable = true, insertable = true, updatable = true, length = 255)
    public String getSdkAppVersion() {
        return sdkAppVersion;
    }

    public void setSdkAppVersion(String sdkAppVersion) {
        this.sdkAppVersion = sdkAppVersion;
    }

    @Basic
    @Column(name = "SDK_APP_STATUS", nullable = true, insertable = true, updatable = true)
    public Integer getSdkAppStatus() {
        return sdkAppStatus;
    }

    public void setSdkAppStatus(Integer sdkAppStatus) {
        this.sdkAppStatus = sdkAppStatus;
    }

    @Basic
    @Column(name = "SDK_APP_OS_TYPE", nullable = true, insertable = true, updatable = true)
    public Integer getSdkAppOsType() {
        return sdkAppOsType;
    }

    public void setSdkAppOsType(Integer sdkAppOsType) {
        this.sdkAppOsType = sdkAppOsType;
    }

    @Basic
    @Column(name = "SDK_APP_CATEGORY", nullable = true, insertable = true, updatable = true)
    public Integer getSdkAppCategory() {
        return sdkAppCategory;
    }

    public void setSdkAppCategory(Integer sdkAppCategory) {
        this.sdkAppCategory = sdkAppCategory;
    }

    @Basic
    @Column(name = "SDK_APP_FLOW_AMOUNT", nullable = true, insertable = true, updatable = true)
    public Long getSdkAppFlowAmount() {
        return sdkAppFlowAmount;
    }

    public void setSdkAppFlowAmount(Long sdkAppFlowAmount) {
        this.sdkAppFlowAmount = sdkAppFlowAmount;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Basic
    @Column(name = "SYN_ID", nullable = true, insertable = true, updatable = true)
    public Integer getSynId() {
        return synId;
    }

    public void setSynId(Integer synId) {
        this.synId = synId;
    }

    @Basic
    @Column(name = "DecryptionKey", nullable = true, insertable = true, updatable = true, length = 255)
    public String getDecryptionKey() {
        return decryptionKey;
    }

    public void setDecryptionKey(String decryptionKey) {
        this.decryptionKey = decryptionKey;
    }

    @Basic
    @Column(name = "IAB_CATEGORIES", nullable = true, insertable = true, updatable = true, length = 1000)
    public String getIabCategories() {
        return iabCategories;
    }

    public void setIabCategories(String iabCategories) {
        this.iabCategories = iabCategories;
    }

    @Basic
    @Column(name = "IAB_BLOCKING_CATEGORIES", nullable = true, insertable = true, updatable = true, length = 1000)
    public String getIabBlockingCategories() {
        return iabBlockingCategories;
    }

    public void setIabBlockingCategories(String iabBlockingCategories) {
        this.iabBlockingCategories = iabBlockingCategories;
    }

    @Basic
    @Column(name = "TRAFFIC_ALLOCATE_MEDIA_TYPE")
    public Integer getTrafficAllocateMediaType() {
        return trafficAllocateMediaType;
    }

    public void setTrafficAllocateMediaType(Integer trafficAllocateOwn) {
        this.trafficAllocateMediaType = trafficAllocateOwn;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SdkAppInfo that = (SdkAppInfo) o;

        if (sdkAppId != that.sdkAppId) return false;
        if (sdkAppDeveloperId != that.sdkAppDeveloperId) return false;
        if (sdkAppName != null ? !sdkAppName.equals(that.sdkAppName) : that.sdkAppName != null) return false;
        if (sdkAppPakageName != null ? !sdkAppPakageName.equals(that.sdkAppPakageName) : that.sdkAppPakageName != null)
            return false;
        if (sdkAppVersion != null ? !sdkAppVersion.equals(that.sdkAppVersion) : that.sdkAppVersion != null)
            return false;
        if (sdkAppStatus != null ? !sdkAppStatus.equals(that.sdkAppStatus) : that.sdkAppStatus != null) return false;
        if (sdkAppOsType != null ? !sdkAppOsType.equals(that.sdkAppOsType) : that.sdkAppOsType != null) return false;
        if (sdkAppCategory != null ? !sdkAppCategory.equals(that.sdkAppCategory) : that.sdkAppCategory != null)
            return false;
        if (sdkAppFlowAmount != null ? !sdkAppFlowAmount.equals(that.sdkAppFlowAmount) : that.sdkAppFlowAmount != null)
            return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(that.lastModTime) : that.lastModTime != null) return false;
        if (synId != null ? !synId.equals(that.synId) : that.synId != null) return false;
        if (decryptionKey != null ? !decryptionKey.equals(that.decryptionKey) : that.decryptionKey != null)
            return false;
        if (iabCategories != null ? !iabCategories.equals(that.iabCategories) : that.iabCategories != null)
            return false;
        if (iabBlockingCategories != null ? !iabBlockingCategories.equals(that.iabBlockingCategories) : that.iabBlockingCategories != null)
            return false;

        if (trafficAllocateMediaType != null ? !trafficAllocateMediaType.equals(that.trafficAllocateMediaType) : that.trafficAllocateMediaType != null)
        return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (sdkAppId ^ (sdkAppId >>> 32));
        result = 31 * result + (int) (sdkAppDeveloperId ^ (sdkAppDeveloperId >>> 32));
        result = 31 * result + (sdkAppName != null ? sdkAppName.hashCode() : 0);
        result = 31 * result + (sdkAppPakageName != null ? sdkAppPakageName.hashCode() : 0);
        result = 31 * result + (sdkAppVersion != null ? sdkAppVersion.hashCode() : 0);
        result = 31 * result + (sdkAppStatus != null ? sdkAppStatus.hashCode() : 0);
        result = 31 * result + (sdkAppOsType != null ? sdkAppOsType.hashCode() : 0);
        result = 31 * result + (sdkAppCategory != null ? sdkAppCategory.hashCode() : 0);
        result = 31 * result + (sdkAppFlowAmount != null ? sdkAppFlowAmount.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        result = 31 * result + (synId != null ? synId.hashCode() : 0);
        result = 31 * result + (decryptionKey != null ? decryptionKey.hashCode() : 0);
        result = 31 * result + (iabCategories != null ? iabCategories.hashCode() : 0);
        result = 31 * result + (iabBlockingCategories != null ? iabBlockingCategories.hashCode() : 0);
        result = 31 * result + (trafficAllocateMediaType != null ? trafficAllocateMediaType.hashCode() : 0);
        return result;
    }
}
