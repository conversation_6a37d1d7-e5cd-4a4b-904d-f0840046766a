package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Objects;

@Entity
@Table(name = "TemplateElementMapping")
public class TemplateElementMapping {
    private long elementId;
    private Long templateElementId;

    /**
     * Property name constant for {@code templateElementId}.
     */
    public static final String PROPERTYNAME_TEMPLATE_ELEMENT_ID = "templateElementId";
    /**
     * Property name constant for {@code elementId}.
     */
    public static final String PROPERTYNAME_ELEMENT_ID = "elementId";

    @Id
    @Column(name = "element_id")
    public long getElementId() {
        return elementId;
    }

    public void setElementId(long elementId) {
        this.elementId = elementId;
    }

    @Basic
    @Column(name = "template_element_id")
    public Long getTemplateElementId() {
        return templateElementId;
    }

    public void setTemplateElementId(Long templateElementId) {
        this.templateElementId = templateElementId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TemplateElementMapping that = (TemplateElementMapping) o;
        return elementId == that.elementId &&
                Objects.equals(templateElementId, that.templateElementId);
    }

    @Override
    public int hashCode() {

        return Objects.hash(elementId, templateElementId);
    }
}
