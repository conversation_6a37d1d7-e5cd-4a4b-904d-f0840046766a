package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.util.Objects;

@Entity
public class ConvertTrackingCrossAccountPush {
    private Long id;
    private Long ownerSponsorId;
    private Long targetSponsorId;
    private String pushedConvertTrackingUid;

    public static String FIELD_TARGET_SPONSOR_ID = "targetSponsorId";
    public static String FIELD_PUSHED_CONVERT_TRACKING_UID = "pushedConvertTrackingUid";

    @Id
    @Column(name = "ID", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "OWNER_SPONSOR_ID", nullable = false)
    public Long getOwnerSponsorId() {
        return ownerSponsorId;
    }

    public void setOwnerSponsorId(Long ownerSponsorId) {
        this.ownerSponsorId = ownerSponsorId;
    }

    @Basic
    @Column(name = "TARGET_SPONSOR_ID", nullable = false)
    public Long getTargetSponsorId() {
        return targetSponsorId;
    }

    public void setTargetSponsorId(Long targetSponsorId) {
        this.targetSponsorId = targetSponsorId;
    }

    @Basic
    @Column(name = "PUSHED_CONVERT_TRACKING_UID", nullable = false, length = 255)
    public String getPushedConvertTrackingUid() {
        return pushedConvertTrackingUid;
    }

    public void setPushedConvertTrackingUid(String pushedConvertTrackingUid) {
        this.pushedConvertTrackingUid = pushedConvertTrackingUid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConvertTrackingCrossAccountPush that = (ConvertTrackingCrossAccountPush) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(ownerSponsorId, that.ownerSponsorId) &&
                Objects.equals(targetSponsorId, that.targetSponsorId) &&
                Objects.equals(pushedConvertTrackingUid, that.pushedConvertTrackingUid);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, ownerSponsorId, targetSponsorId, pushedConvertTrackingUid);
    }
}
