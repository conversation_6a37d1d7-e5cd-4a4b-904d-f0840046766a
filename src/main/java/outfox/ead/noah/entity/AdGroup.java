package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/4/27.
 */
@Entity
@Table(name = "AdGroup")
public class AdGroup {
    private long adGroupId;
    private String name;
    private Integer status = 0;
    private Integer impressionType = 0;
    private Long clickCount = 0L;
    private Double clickThroughRate = 0D;
    private Long impressionCount = 0L;
    private Integer expensate = 0;
    private Long createTime = 0L;
    private Long lastModTime = 0L;
    private Long sponsorId = 0L;
    private Integer editStatus = 0;
    private Long cpcId = 0L;
    private Integer productType = 0;
    private long adCampaignId;


    /**
     * Property name constant for {@code sponsorId}.
     */
    public static final String PROPERTYNAME_SPONSOR_ID = "sponsorId";
    /**
     * Property name constant for {@code expensate}.
     */
    public static final String PROPERTYNAME_EXPENSATE = "expensate";
    /**
     * Property name constant for {@code clickThroughRate}.
     */
    public static final String PROPERTYNAME_CLICK_THROUGH_RATE = "clickThroughRate";
    /**
     * Property name constant for {@code productType}.
     */
    public static final String PROPERTYNAME_PRODUCT_TYPE = "productType";
    /**
     * Property name constant for {@code status}.
     */
    public static final String PROPERTYNAME_STATUS = "status";
    /**
     * Property name constant for {@code editStatus}.
     */
    public static final String PROPERTYNAME_EDIT_STATUS = "editStatus";
    /**
     * Property name constant for {@code adGroupId}.
     */
    public static final String PROPERTYNAME_AD_GROUP_ID = "adGroupId";
    /**
     * Property name constant for {@code clickCount}.
     */
    public static final String PROPERTYNAME_CLICK_COUNT = "clickCount";
    /**
     * Property name constant for {@code name}.
     */
    public static final String PROPERTYNAME_NAME = "name";
    /**
     * Property name constant for {@code adCampaignId}.
     */
    public static final String PROPERTYNAME_AD_CAMPAIGN_ID = "adCampaignId";
    /**
     * Property name constant for {@code impressionCount}.
     */
    public static final String PROPERTYNAME_IMPRESSION_COUNT = "impressionCount";
    /**
     * Property name constant for {@code cpcId}.
     */
    public static final String PROPERTYNAME_CPC_ID = "cpcId";
    /**
     * Property name constant for {@code lastModTime}.
     */
    public static final String PROPERTYNAME_LAST_MOD_TIME = "lastModTime";
    /**
     * Property name constant for {@code impressionType}.
     */
    public static final String PROPERTYNAME_IMPRESSION_TYPE = "impressionType";
    /**
     * Property name constant for {@code createTime}.
     */
    public static final String PROPERTYNAME_CREATE_TIME = "createTime";

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "AD_GROUP_ID", nullable = false, insertable = true, updatable = true)
    public long getAdGroupId() {
        return adGroupId;
    }

    public void setAdGroupId(long adGroupId) {
        this.adGroupId = adGroupId;
    }

    @Basic
    @Column(name = "NAME", nullable = true, insertable = true, updatable = true, length = 255)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "STATUS", nullable = true, insertable = true, updatable = true)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "IMPRESSION_TYPE", nullable = true, insertable = true, updatable = true)
    public Integer getImpressionType() {
        return impressionType;
    }

    public void setImpressionType(Integer impressionType) {
        this.impressionType = impressionType;
    }

    @Basic
    @Column(name = "CLICK_COUNT", nullable = true, insertable = true, updatable = true)
    public Long getClickCount() {
        return clickCount;
    }

    public void setClickCount(Long clickCount) {
        this.clickCount = clickCount;
    }

    @Basic
    @Column(name = "CLICK_THROUGH_RATE", nullable = true, insertable = true, updatable = true, precision = 0)
    public Double getClickThroughRate() {
        return clickThroughRate;
    }

    public void setClickThroughRate(Double clickThroughRate) {
        this.clickThroughRate = clickThroughRate;
    }

    @Basic
    @Column(name = "IMPRESSION_COUNT", nullable = true, insertable = true, updatable = true)
    public Long getImpressionCount() {
        return impressionCount;
    }

    public void setImpressionCount(Long impressionCount) {
        this.impressionCount = impressionCount;
    }

    @Basic
    @Column(name = "EXPENSATE", nullable = true, insertable = true, updatable = true)
    public Integer getExpensate() {
        return expensate;
    }

    public void setExpensate(Integer expensate) {
        this.expensate = expensate;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Basic
    @Column(name = "SPONSOR_ID", nullable = true, insertable = true, updatable = true)
    public Long getSponsorId() {
        return sponsorId;
    }

    public void setSponsorId(Long sponsorId) {
        this.sponsorId = sponsorId;
    }

    @Basic
    @Column(name = "EDIT_STATUS", nullable = true, insertable = true, updatable = true)
    public Integer getEditStatus() {
        return editStatus;
    }

    public void setEditStatus(Integer editStatus) {
        this.editStatus = editStatus;
    }

    @Basic
    @Column(name = "AD_CAMPAIGN_ID", nullable = false, insertable = true, updatable = true)
    public long getAdCampaignId() {
        return adCampaignId;
    }

    public void setAdCampaignId(long adCampaignId) {
        this.adCampaignId = adCampaignId;
    }

    @Basic
    @Column(name = "CPC_ID", nullable = true, insertable = true, updatable = true)
    public Long getCpcId() {
        return cpcId;
    }

    public void setCpcId(Long cpcId) {
        this.cpcId = cpcId;
    }

    @Basic
    @Column(name = "PRODUCT_TYPE", nullable = true, insertable = true, updatable = true)
    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AdGroup adGroup = (AdGroup) o;

        if (adGroupId != adGroup.adGroupId) return false;
        if (adCampaignId != adGroup.adCampaignId) return false;
        if (name != null ? !name.equals(adGroup.name) : adGroup.name != null) return false;
        if (status != null ? !status.equals(adGroup.status) : adGroup.status != null) return false;
        if (impressionType != null ? !impressionType.equals(adGroup.impressionType) : adGroup.impressionType != null)
            return false;
        if (clickCount != null ? !clickCount.equals(adGroup.clickCount) : adGroup.clickCount != null) return false;
        if (clickThroughRate != null ? !clickThroughRate.equals(adGroup.clickThroughRate) : adGroup.clickThroughRate != null)
            return false;
        if (impressionCount != null ? !impressionCount.equals(adGroup.impressionCount) : adGroup.impressionCount != null)
            return false;
        if (expensate != null ? !expensate.equals(adGroup.expensate) : adGroup.expensate != null) return false;
        if (createTime != null ? !createTime.equals(adGroup.createTime) : adGroup.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(adGroup.lastModTime) : adGroup.lastModTime != null) return false;
        if (sponsorId != null ? !sponsorId.equals(adGroup.sponsorId) : adGroup.sponsorId != null) return false;
        if (editStatus != null ? !editStatus.equals(adGroup.editStatus) : adGroup.editStatus != null) return false;
        if (cpcId != null ? !cpcId.equals(adGroup.cpcId) : adGroup.cpcId != null) return false;
        if (productType != null ? !productType.equals(adGroup.productType) : adGroup.productType != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (adGroupId ^ (adGroupId >>> 32));
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (impressionType != null ? impressionType.hashCode() : 0);
        result = 31 * result + (clickCount != null ? clickCount.hashCode() : 0);
        result = 31 * result + (clickThroughRate != null ? clickThroughRate.hashCode() : 0);
        result = 31 * result + (impressionCount != null ? impressionCount.hashCode() : 0);
        result = 31 * result + (expensate != null ? expensate.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        result = 31 * result + (sponsorId != null ? sponsorId.hashCode() : 0);
        result = 31 * result + (editStatus != null ? editStatus.hashCode() : 0);
        result = 31 * result + (int) (adCampaignId ^ (adCampaignId >>> 32));
        result = 31 * result + (cpcId != null ? cpcId.hashCode() : 0);
        result = 31 * result + (productType != null ? productType.hashCode() : 0);
        return result;
    }
}
