package outfox.ead.noah.entity.models.complete;

import lombok.Data;
import outfox.ead.noah.entity.Agent;
import outfox.ead.noah.entity.Sponsor;

import java.util.List;

/**
 * Created by lizai on 16/5/11.
 */

@Data
public class CompleteAgent {

    protected Agent agent = new Agent();

    private List<Sponsor> sponsors;

    public CompleteAgent() {
    }

    public CompleteAgent(Agent agent) {
        this.agent = agent;
    }

    public CompleteAgent(Agent agent, List<Sponsor> sponsors) {
        this.agent = agent;
        this.sponsors = sponsors;
    }

}
