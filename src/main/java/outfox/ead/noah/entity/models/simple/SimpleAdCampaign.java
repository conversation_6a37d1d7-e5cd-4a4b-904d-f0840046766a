package outfox.ead.noah.entity.models.simple;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * Created by yuanzhch on 2017/4/25.
 */
@Data
@RequiredArgsConstructor(staticName = "of")
@NoArgsConstructor
public class SimpleAdCampaign {
    @NonNull
    long adCampaignId;
    @NonNull
    String adCampaignName;
    String landingType = "";
    Integer adPlatform;
    List<SimpleAdGroup> adGroups;
}
