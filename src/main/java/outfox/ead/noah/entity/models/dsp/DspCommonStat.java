package outfox.ead.noah.entity.models.dsp;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * Created by huanghuan on 16/8/9.
 */
@Data
public class DspCommonStat {

    /**
     * 总展示次数
     **/
    private long totalImpr;

    /**
     * 总点击次数
     **/
    private long totalClick;

    /**
     * 总点击率
     **/
    private double totalClickRate;

    /**
     * 总消费
     **/
    private double totalConsumption;

    /**
     * 总的平均点击单价
     */
    private double totalCostPerClick;

    /**
     * 总转化数
     */
    private long totalConv;

    /**
     * 总转化率
     */
    private double totalConvRate;

    /**
     * 转化成本=消费/转化数
     */
    private double totalConvCost;

    /**
     * 每天的统计数据
     */
    private List<DspCommonStatItem> dspCommonStatItems = new LinkedList<>();
}
