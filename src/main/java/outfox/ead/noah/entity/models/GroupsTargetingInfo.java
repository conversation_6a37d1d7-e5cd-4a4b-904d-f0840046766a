package outfox.ead.noah.entity.models;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

/**
 * 复制定向时，利用sql查询，与select的字段名对应
 */
@Data
@RequiredArgsConstructor(staticName = "of")
@NoArgsConstructor
public class GroupsTargetingInfo {
    @NonNull
    Long campaignId;
    String campaignName = "";
    Long groupId;
    String groupName = "";
    Integer landingType;
    Integer productType;
    Long lastModTime;

    public void setGroupId(Number groupId) {
        if(groupId != null) {
            this.groupId = groupId.longValue();
        }
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public void setLastModTime(Number lastModTime) {
        this.lastModTime = lastModTime.longValue();
    }

    public void setCampaignName(String campaignName) { this.campaignName =  campaignName; }

    public void setCampaignId(Number campaignId) {
        this.campaignId = campaignId.longValue();
    }

    public void setLandingType(Number landingType) {
        if(landingType != null) {
            this.landingType = landingType.intValue();
        }
    }

    public void setProductType(Number productType) {
        if(productType!=null){
            this.productType = productType.intValue();
        }
    }
}
