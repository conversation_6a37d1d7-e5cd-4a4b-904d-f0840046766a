package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * Created by wangmo on 2018/5/17.
 */
@Entity
public class LandingPageFormContent {
    private Long landingPageFormContentId;
    private Long landingPageId;
    private Long landingPageFormId;
    private Long adCampaignId;
    private Long adGroupId;
    private Long adContentId;
    private String sdkSlotUdid;
    private String os;
    private String formContent;
    private Long createTime;
    private Long lastModTime;

    @Id
    @Column(name = "LANDING_PAGE_FORM_CONTENT_ID", nullable = false)
    public Long getLandingPageFormContentId() {
        return landingPageFormContentId;
    }

    public void setLandingPageFormContentId(Long landingPageFormContentId) {
        this.landingPageFormContentId = landingPageFormContentId;
    }

    @Basic
    @Column(name = "LANDING_PAGE_ID", nullable = false)
    public Long getLandingPageId() {
        return landingPageId;
    }

    public void setLandingPageId(Long landingPageId) {
        this.landingPageId = landingPageId;
    }

    @Basic
    @Column(name = "LANDING_PAGE_FORM_ID", nullable = false)
    public Long getLandingPageFormId() {
        return landingPageFormId;
    }

    public void setLandingPageFormId(Long landingPageFormId) {
        this.landingPageFormId = landingPageFormId;
    }

    @Basic
    @Column(name = "AD_CAMPAIGN_ID", nullable = true)
    public Long getAdCampaignId() {
        return adCampaignId;
    }

    public void setAdCampaignId(Long adCampaignId) {
        this.adCampaignId = adCampaignId;
    }

    @Basic
    @Column(name = "AD_GROUP_ID", nullable = true)
    public Long getAdGroupId() {
        return adGroupId;
    }

    public void setAdGroupId(Long adGroupId) {
        this.adGroupId = adGroupId;
    }

    @Basic
    @Column(name = "AD_CONTENT_ID", nullable = true)
    public Long getAdContentId() {
        return adContentId;
    }

    public void setAdContentId(Long adContentId) {
        this.adContentId = adContentId;
    }

    @Basic
    @Column(name = "SDK_SLOT_UDID", nullable = true, length = 255)
    public String getSdkSlotUdid() {
        return sdkSlotUdid;
    }

    public void setSdkSlotUdid(String sdkSlotUdid) {
        this.sdkSlotUdid = sdkSlotUdid;
    }

    @Basic
    @Column(name = "OS", nullable = true)
    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    @Basic
    @Column(name = "FORM_CONTENT", nullable = false, length = -1)
    public String getFormContent() {
        return formContent;
    }

    public void setFormContent(String formContent) {
        this.formContent = formContent;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        LandingPageFormContent that = (LandingPageFormContent) o;

        if (landingPageFormContentId != null ? !landingPageFormContentId.equals(that.landingPageFormContentId) : that.landingPageFormContentId != null) return false;
        if (landingPageId != null ? !landingPageId.equals(that.landingPageId) : that.landingPageId != null) return false;
        if (landingPageFormId != null ? !landingPageFormId.equals(that.landingPageFormId) : that.landingPageFormId != null) return false;
        if (adCampaignId != null ? !adCampaignId.equals(that.adCampaignId) : that.adCampaignId != null) return false;
        if (adGroupId != null ? !adGroupId.equals(that.adGroupId) : that.adGroupId != null) return false;
        if (adContentId != null ? !adContentId.equals(that.adContentId) : that.adContentId != null) return false;
        if (sdkSlotUdid != null ? !sdkSlotUdid.equals(that.sdkSlotUdid) : that.sdkSlotUdid != null) return false;
        if (formContent != null ? !formContent.equals(that.formContent) : that.formContent != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(that.lastModTime) : that.lastModTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = landingPageFormContentId != null ? landingPageFormContentId.hashCode() : 0;
        result = 31 * result + (landingPageId != null ? landingPageId.hashCode() : 0);
        result = 31 * result + (landingPageFormId != null ? landingPageFormId.hashCode() : 0);
        result = 31 * result + (adCampaignId != null ? adCampaignId.hashCode() : 0);
        result = 31 * result + (adGroupId != null ? adGroupId.hashCode() : 0);
        result = 31 * result + (adContentId != null ? adContentId.hashCode() : 0);
        result = 31 * result + (sdkSlotUdid != null ? sdkSlotUdid.hashCode() : 0);
        result = 31 * result + (formContent != null ? formContent.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        return result;
    }
}
