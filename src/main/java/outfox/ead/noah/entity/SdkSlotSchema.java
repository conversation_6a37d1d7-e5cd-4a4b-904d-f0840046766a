package outfox.ead.noah.entity;

import javax.persistence.*;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2017/6/8.
 */
@Entity
@Table(name = "SdkSlotSchema")
public class SdkSlotSchema {
    private long sdkSlotSchemaId;
    private String sdkSlotSchemaSlotudid;
    private Long sdkSlotSchemaSchemaid;
    private Long createTime;
    private Long lastModTime;


    /**
     * Property name constant for {@code sdkSlotSchemaSlotudid}.
     */
    public static final String PROPERTYNAME_SDK_SLOT_SCHEMA_SLOTUDID = "sdkSlotSchemaSlotudid";
    /**
     * Property name constant for {@code sdkSlotSchemaId}.
     */
    public static final String PROPERTYNAME_SDK_SLOT_SCHEMA_ID = "sdkSlotSchemaId";
    /**
     * Property name constant for {@code lastModTime}.
     */
    public static final String PROPERTYNAME_LAST_MOD_TIME = "lastModTime";
    /**
     * Property name constant for {@code createTime}.
     */
    public static final String PROPERTYNAME_CREATE_TIME = "createTime";
    /**
     * Property name constant for {@code sdkSlotSchemaSchemaid}.
     */
    public static final String PROPERTYNAME_SDK_SLOT_SCHEMA_SCHEMAID = "sdkSlotSchemaSchemaid";

    @Id
    @Column(name = "SDK_SLOT_SCHEMA_ID", nullable = false)
    public long getSdkSlotSchemaId() {
        return sdkSlotSchemaId;
    }

    public void setSdkSlotSchemaId(long sdkSlotSchemaId) {
        this.sdkSlotSchemaId = sdkSlotSchemaId;
    }

    @Basic
    @Column(name = "SDK_SLOT_SCHEMA_SLOTUDID")
    public String getSdkSlotSchemaSlotudid() {
        return sdkSlotSchemaSlotudid;
    }

    public void setSdkSlotSchemaSlotudid(String sdkSlotSchemaSlotudid) {
        this.sdkSlotSchemaSlotudid = sdkSlotSchemaSlotudid;
    }

    @Basic
    @Column(name = "SDK_SLOT_SCHEMA_SCHEMAID")
    public Long getSdkSlotSchemaSchemaid() {
        return sdkSlotSchemaSchemaid;
    }

    public void setSdkSlotSchemaSchemaid(Long sdkSlotSchemaSchemaid) {
        this.sdkSlotSchemaSchemaid = sdkSlotSchemaSchemaid;
    }

    @Basic
    @Column(name = "CREATE_TIME")
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME")
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SdkSlotSchema that = (SdkSlotSchema) o;

        if (sdkSlotSchemaId != that.sdkSlotSchemaId) return false;
        if (sdkSlotSchemaSlotudid != null ? !sdkSlotSchemaSlotudid.equals(that.sdkSlotSchemaSlotudid) : that.sdkSlotSchemaSlotudid != null)
            return false;
        if (sdkSlotSchemaSchemaid != null ? !sdkSlotSchemaSchemaid.equals(that.sdkSlotSchemaSchemaid) : that.sdkSlotSchemaSchemaid != null)
            return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(that.lastModTime) : that.lastModTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (sdkSlotSchemaId ^ (sdkSlotSchemaId >>> 32));
        result = 31 * result + (sdkSlotSchemaSlotudid != null ? sdkSlotSchemaSlotudid.hashCode() : 0);
        result = 31 * result + (sdkSlotSchemaSchemaid != null ? sdkSlotSchemaSchemaid.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        return result;
    }
}
