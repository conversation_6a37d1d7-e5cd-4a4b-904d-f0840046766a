package outfox.ead.noah.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 描述: 广告商余额
 */
@Data
@Entity
@Table(name = "SPONSOR_BALANCE")
public class SponsorBalance {
    /**
     * 广告商id
     */
    @Id
    @Column(name = "SPONSOR_ID")
    private Long sponsorId;

    /**
     * 现金余额
     */
    @Column(name = "ACTUAL_BALANCE")
    private Long actualBalance;

    /**
     * 体验金余额
     */
    @Column(name = "VIRTUAL_BALANCE")
    private Long virtualBalance;

    /**
     * 信用额度
     */
    @Column(name = "CREDIT_LIMIT")
    private Long creditLimit;

    @Column(name = "LAST_MOD_TIME")
    private Long lastModTime;
}
