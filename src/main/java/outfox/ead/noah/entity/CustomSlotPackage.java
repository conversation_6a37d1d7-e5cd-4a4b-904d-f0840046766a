package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;
import outfox.ead.noah.util.code.StatusCode;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * 自定义流量包
 *
 * <AUTHOR>
 **/
@Entity
@Table(name = "CustomSlotPackage")
public class CustomSlotPackage {

    private Long customSlotPackageId;

    private Long sponsorId;

    /** 自定义流量包名称 */
    private String name;

    /** 流量包中包含的所有广告位的udid */
    private String slotUdids;

    /** 使用了当前流量包的广告组id */
    private String adGroupIds;

    /** 流量包状态，0已删除，1可使用 */
    private Integer status;

    public static final String FIELD_CUSTOM_SLOT_PACKAGE_ID = "customSlotPackageId";
    public static final String FIELD_SPONSOR_ID = "sponsorId";
    public static final String FIELD_NAME = "name";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_SLOT_NUM = "slotNum";
    public static final String FIELD_AD_GROUP_NUM = "adGroupNum";

    public CustomSlotPackage() {
        this.adGroupIds = null;
        this.status = StatusCode.CUSTOM_SLOT_PACKAGE_STATUS_ACTIVE;
    }

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "CUSTOM_SLOT_PACKAGE_ID", nullable = false)
    public Long getCustomSlotPackageId() {
        return customSlotPackageId;
    }

    public void setCustomSlotPackageId(Long customSlotPackageId) {
        this.customSlotPackageId = customSlotPackageId;
    }

    @Basic
    @Column(name = "SPONSOR_ID", nullable = false)
    public Long getSponsorId() {
        return sponsorId;
    }

    public void setSponsorId(Long sponsorId) {
        this.sponsorId = sponsorId;
    }

    @Basic
    @Column(name = "NAME", nullable = false)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "SLOT_UDIDS", nullable = true)
    public String getSlotUdids() {
        return slotUdids;
    }

    public void setSlotUdids(String slotUdids) {
        this.slotUdids = slotUdids;
    }

    @Basic
    @Column(name = "AD_GROUP_IDS", nullable = true)
    public String getAdGroupIds() {
        return adGroupIds;
    }

    public void setAdGroupIds(String adGroupIds) {
        this.adGroupIds = adGroupIds;
    }

    @Basic
    @Column(name = "STATUS", nullable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

}
