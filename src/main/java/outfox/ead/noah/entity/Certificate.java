package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Arrays;

/**
 * Created by yuanzhch on 2017/8/31.
 */
@Entity
@Table(name = "CERTIFICATE")
public class Certificate {
    private long certId;
    private long sponsorId;
    private String storeId;
    private int clientTypeId;
    private int promoTypeId;
    private int certTypeId;
    private String clientTypeRemark;
    private String promoTypeRemark;
    private String certTypeRemark;
    private String name;
    private int isMiss;
    private String missCertMsg;
    private String fileName;
    private byte[] thumbnail;
    private int auditReason;
    private int auditStatus;
    private Date issueDate;
    private Date expireDate;
    private Timestamp submitDate;
    private String auditor;
    private Timestamp auditTime;
    private int isDel;
    private long parentId;
    private int isCommit;
    private int isHis;

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "CERT_ID", nullable = false)
    public long getCertId() {
        return certId;
    }

    public void setCertId(long certId) {
        this.certId = certId;
    }

    @Basic
    @Column(name = "SPONSOR_ID", nullable = false)
    public long getSponsorId() {
        return sponsorId;
    }

    public void setSponsorId(long sponsorId) {
        this.sponsorId = sponsorId;
    }

    @Basic
    @Column(name = "STORE_ID", nullable = true, length = 40)
    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    @Basic
    @Column(name = "CLIENT_TYPE_ID", nullable = false)
    public int getClientTypeId() {
        return clientTypeId;
    }

    public void setClientTypeId(int clientTypeId) {
        this.clientTypeId = clientTypeId;
    }

    @Basic
    @Column(name = "PROMO_TYPE_ID", nullable = false)
    public int getPromoTypeId() {
        return promoTypeId;
    }

    public void setPromoTypeId(int promoTypeId) {
        this.promoTypeId = promoTypeId;
    }

    @Basic
    @Column(name = "CERT_TYPE_ID", nullable = false)
    public int getCertTypeId() {
        return certTypeId;
    }

    public void setCertTypeId(int certTypeId) {
        this.certTypeId = certTypeId;
    }

    @Basic
    @Column(name = "CLIENT_TYPE_REMARK", nullable = true, length = 256)
    public String getClientTypeRemark() {
        return clientTypeRemark;
    }

    public void setClientTypeRemark(String clientTypeRemark) {
        this.clientTypeRemark = clientTypeRemark;
    }

    @Basic
    @Column(name = "PROMO_TYPE_REMARK", nullable = true, length = 256)
    public String getPromoTypeRemark() {
        return promoTypeRemark;
    }

    public void setPromoTypeRemark(String promoTypeRemark) {
        this.promoTypeRemark = promoTypeRemark;
    }

    @Basic
    @Column(name = "CERT_TYPE_REMARK", nullable = true, length = 256)
    public String getCertTypeRemark() {
        return certTypeRemark;
    }

    public void setCertTypeRemark(String certTypeRemark) {
        this.certTypeRemark = certTypeRemark;
    }

    @Basic
    @Column(name = "NAME", nullable = true, length = 256)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "IS_MISS", nullable = false)
    public int getIsMiss() {
        return isMiss;
    }

    public void setIsMiss(int isMiss) {
        this.isMiss = isMiss;
    }

    @Basic
    @Column(name = "MISS_CERT_MSG", nullable = true, length = 256)
    public String getMissCertMsg() {
        return missCertMsg;
    }

    public void setMissCertMsg(String missCertMsg) {
        this.missCertMsg = missCertMsg;
    }

    @Basic
    @Column(name = "FILE_NAME", nullable = true, length = 256)
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Basic
    @Column(name = "THUMBNAIL", nullable = true)
    public byte[] getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(byte[] thumbnail) {
        this.thumbnail = thumbnail;
    }

    @Basic
    @Column(name = "AUDIT_REASON", nullable = false)
    public int getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(int auditReason) {
        this.auditReason = auditReason;
    }

    @Basic
    @Column(name = "AUDIT_STATUS", nullable = false)
    public int getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(int auditStatus) {
        this.auditStatus = auditStatus;
    }

    @Basic
    @Column(name = "ISSUE_DATE", nullable = true)
    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    @Basic
    @Column(name = "EXPIRE_DATE", nullable = true)
    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    @Basic
    @Column(name = "SUBMIT_DATE", nullable = true)
    public Timestamp getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(Timestamp submitDate) {
        this.submitDate = submitDate;
    }

    @Basic
    @Column(name = "AUDITOR", nullable = true, length = 256)
    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    @Basic
    @Column(name = "AUDIT_TIME", nullable = true)
    public Timestamp getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Timestamp auditTime) {
        this.auditTime = auditTime;
    }

    @Basic
    @Column(name = "IS_DEL", nullable = false)
    public int getIsDel() {
        return isDel;
    }

    public void setIsDel(int isDel) {
        this.isDel = isDel;
    }

    @Basic
    @Column(name = "PARENT_ID", nullable = false)
    public long getParentId() {
        return parentId;
    }

    public void setParentId(long parentId) {
        this.parentId = parentId;
    }

    @Basic
    @Column(name = "IS_COMMIT", nullable = false)
    public int getIsCommit() {
        return isCommit;
    }

    public void setIsCommit(int isCommit) {
        this.isCommit = isCommit;
    }

    @Basic
    @Column(name = "IS_HIS", nullable = false)
    public int getIsHis() {
        return isHis;
    }

    public void setIsHis(int isHis) {
        this.isHis = isHis;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Certificate that = (Certificate) o;

        if (certId != that.certId) return false;
        if (sponsorId != that.sponsorId) return false;
        if (clientTypeId != that.clientTypeId) return false;
        if (promoTypeId != that.promoTypeId) return false;
        if (certTypeId != that.certTypeId) return false;
        if (isMiss != that.isMiss) return false;
        if (auditReason != that.auditReason) return false;
        if (auditStatus != that.auditStatus) return false;
        if (isDel != that.isDel) return false;
        if (parentId != that.parentId) return false;
        if (isCommit != that.isCommit) return false;
        if (isHis != that.isHis) return false;
        if (storeId != null ? !storeId.equals(that.storeId) : that.storeId != null) return false;
        if (clientTypeRemark != null ? !clientTypeRemark.equals(that.clientTypeRemark) : that.clientTypeRemark != null)
            return false;
        if (promoTypeRemark != null ? !promoTypeRemark.equals(that.promoTypeRemark) : that.promoTypeRemark != null)
            return false;
        if (certTypeRemark != null ? !certTypeRemark.equals(that.certTypeRemark) : that.certTypeRemark != null)
            return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (missCertMsg != null ? !missCertMsg.equals(that.missCertMsg) : that.missCertMsg != null) return false;
        if (fileName != null ? !fileName.equals(that.fileName) : that.fileName != null) return false;
        if (!Arrays.equals(thumbnail, that.thumbnail)) return false;
        if (issueDate != null ? !issueDate.equals(that.issueDate) : that.issueDate != null) return false;
        if (expireDate != null ? !expireDate.equals(that.expireDate) : that.expireDate != null) return false;
        if (submitDate != null ? !submitDate.equals(that.submitDate) : that.submitDate != null) return false;
        if (auditor != null ? !auditor.equals(that.auditor) : that.auditor != null) return false;
        if (auditTime != null ? !auditTime.equals(that.auditTime) : that.auditTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (certId ^ (certId >>> 32));
        result = 31 * result + (int) (sponsorId ^ (sponsorId >>> 32));
        result = 31 * result + (storeId != null ? storeId.hashCode() : 0);
        result = 31 * result + clientTypeId;
        result = 31 * result + promoTypeId;
        result = 31 * result + certTypeId;
        result = 31 * result + (clientTypeRemark != null ? clientTypeRemark.hashCode() : 0);
        result = 31 * result + (promoTypeRemark != null ? promoTypeRemark.hashCode() : 0);
        result = 31 * result + (certTypeRemark != null ? certTypeRemark.hashCode() : 0);
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + isMiss;
        result = 31 * result + (missCertMsg != null ? missCertMsg.hashCode() : 0);
        result = 31 * result + (fileName != null ? fileName.hashCode() : 0);
        result = 31 * result + Arrays.hashCode(thumbnail);
        result = 31 * result + auditReason;
        result = 31 * result + auditStatus;
        result = 31 * result + (issueDate != null ? issueDate.hashCode() : 0);
        result = 31 * result + (expireDate != null ? expireDate.hashCode() : 0);
        result = 31 * result + (submitDate != null ? submitDate.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditTime != null ? auditTime.hashCode() : 0);
        result = 31 * result + isDel;
        result = 31 * result + (int) (parentId ^ (parentId >>> 32));
        result = 31 * result + isCommit;
        result = 31 * result + isHis;
        return result;
    }
}
