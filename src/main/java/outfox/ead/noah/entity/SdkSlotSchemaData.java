package outfox.ead.noah.entity;

import lombok.Data;
import lombok.Builder;

/**
 * Created by liza<PERSON> on 16/5/19.
 */
@Data
@Builder
public class SdkSlotSchemaData {
    private long sdkSlotSchemaId;
    private String sdkSlotSchemaSlotudid;
    private Long sdkSlotSchemaSchemaid;
    private Long createTime;
    private Long lastModTime;

    public static SdkSlotSchemaData buildSdkSlotSchemaData(SdkSlotSchema sdkSlotSchema) {
        if (sdkSlotSchema == null) {
            return null;
        }
        return SdkSlotSchemaData.builder()
                .sdkSlotSchemaId(sdkSlotSchema.getSdkSlotSchemaId())
                .sdkSlotSchemaSlotudid(sdkSlotSchema.getSdkSlotSchemaSlotudid())
                .sdkSlotSchemaSchemaid(sdkSlotSchema.getSdkSlotSchemaSchemaid())
                .createTime(sdkSlotSchema.getCreateTime())
                .lastModTime(sdkSlotSchema.getLastModTime()).build();
    }
}
