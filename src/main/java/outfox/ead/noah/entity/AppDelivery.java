package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

@Entity
public class AppDelivery {
    private long id;
    private long adGroupId;
    private int deliveryType;
    private String content;

    /**
     * Property name constant for {@code adGroupId}.
     */
    public static final String PROPERTYNAME_AD_GROUP_ID = "adGroupId";
    /**
     * Property name constant for {@code deliveryType}.
     */
    public static final String PROPERTYNAME_DELIVERY_TYPE = "deliveryType";
    /**
     * Property name constant for {@code id}.
     */
    public static final String PROPERTYNAME_ID = "id";
    /**
     * Property name constant for {@code content}.
     */
    public static final String PROPERTYNAME_CONTENT = "content";

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "ID", nullable = false)
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "AD_GROUP_ID", nullable = false)
    public long getAdGroupId() {
        return adGroupId;
    }

    public void setAdGroupId(long adGroupId) {
        this.adGroupId = adGroupId;
    }

    @Basic
    @Column(name = "DELIVERY_TYPE", nullable = false)
    public int getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(int deliveryType) {
        this.deliveryType = deliveryType;
    }

    @Basic
    @Column(name = "CONTENT", nullable = false, length = -1)
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AppDelivery that = (AppDelivery) o;

        if (id != that.id) return false;
        if (adGroupId != that.adGroupId) return false;
        if (deliveryType != that.deliveryType) return false;
        if (content != null ? !content.equals(that.content) : that.content != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (id ^ (id >>> 32));
        result = 31 * result + (int) (adGroupId ^ (adGroupId >>> 32));
        result = 31 * result + deliveryType;
        result = 31 * result + (content != null ? content.hashCode() : 0);
        return result;
    }
}
