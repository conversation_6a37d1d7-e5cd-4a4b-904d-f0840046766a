package outfox.ead.noah.entity;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Created by huanghuan on 16/5/23.
 */
@Entity
@Table(name = "Log_Daily_Budget")
public class LogDailyBudget {
    private long logDbId;
    private long logDbSponsorId;
    private Long logDbOpTime;
    private Integer logDbNum;
    private String logDbIp;
    private Long createTime;
    private Timestamp lastModeTime;


    /**
     * Property name constant for {@code logDbSponsorId}.
     */
    public static final String PROPERTYNAME_LOG_DB_SPONSOR_ID = "logDbSponsorId";
    /**
     * Property name constant for {@code lastModeTime}.
     */
    public static final String PROPERTYNAME_LAST_MODE_TIME = "lastModeTime";
    /**
     * Property name constant for {@code createTime}.
     */
    public static final String PROPERTYNAME_CREATE_TIME = "createTime";
    /**
     * Property name constant for {@code logDbIp}.
     */
    public static final String PROPERTYNAME_LOG_DB_IP = "logDbIp";
    /**
     * Property name constant for {@code logDbId}.
     */
    public static final String PROPERTYNAME_LOG_DB_ID = "logDbId";
    /**
     * Property name constant for {@code logDbOpTime}.
     */
    public static final String PROPERTYNAME_LOG_DB_OP_TIME = "logDbOpTime";
    /**
     * Property name constant for {@code logDbNum}.
     */
    public static final String PROPERTYNAME_LOG_DB_NUM = "logDbNum";

    @Id
    @Column(name = "LOG_DB_ID", nullable = false, insertable = true, updatable = true)
    public long getLogDbId() {
        return logDbId;
    }

    public void setLogDbId(long logDbId) {
        this.logDbId = logDbId;
    }

    @Basic
    @Column(name = "LOG_DB_SPONSOR_ID", nullable = false, insertable = true, updatable = true)
    public long getLogDbSponsorId() {
        return logDbSponsorId;
    }

    public void setLogDbSponsorId(long logDbSponsorId) {
        this.logDbSponsorId = logDbSponsorId;
    }

    @Basic
    @Column(name = "LOG_DB_OP_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLogDbOpTime() {
        return logDbOpTime;
    }

    public void setLogDbOpTime(Long logDbOpTime) {
        this.logDbOpTime = logDbOpTime;
    }

    @Basic
    @Column(name = "LOG_DB_NUM", nullable = true, insertable = true, updatable = true)
    public Integer getLogDbNum() {
        return logDbNum;
    }

    public void setLogDbNum(Integer logDbNum) {
        this.logDbNum = logDbNum;
    }

    @Basic
    @Column(name = "LOG_DB_IP", nullable = true, insertable = true, updatable = true, length = 45)
    public String getLogDbIp() {
        return logDbIp;
    }

    public void setLogDbIp(String logDbIp) {
        this.logDbIp = logDbIp;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MODE_TIME", nullable = false, insertable = true, updatable = true)
    public Timestamp getLastModeTime() {
        return lastModeTime;
    }

    public void setLastModeTime(Timestamp lastModeTime) {
        this.lastModeTime = lastModeTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        LogDailyBudget that = (LogDailyBudget) o;

        if (logDbId != that.logDbId) return false;
        if (logDbSponsorId != that.logDbSponsorId) return false;
        if (logDbOpTime != null ? !logDbOpTime.equals(that.logDbOpTime) : that.logDbOpTime != null) return false;
        if (logDbNum != null ? !logDbNum.equals(that.logDbNum) : that.logDbNum != null) return false;
        if (logDbIp != null ? !logDbIp.equals(that.logDbIp) : that.logDbIp != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (lastModeTime != null ? !lastModeTime.equals(that.lastModeTime) : that.lastModeTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (logDbId ^ (logDbId >>> 32));
        result = 31 * result + (int) (logDbSponsorId ^ (logDbSponsorId >>> 32));
        result = 31 * result + (logDbOpTime != null ? logDbOpTime.hashCode() : 0);
        result = 31 * result + (logDbNum != null ? logDbNum.hashCode() : 0);
        result = 31 * result + (logDbIp != null ? logDbIp.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModeTime != null ? lastModeTime.hashCode() : 0);
        return result;
    }
}
