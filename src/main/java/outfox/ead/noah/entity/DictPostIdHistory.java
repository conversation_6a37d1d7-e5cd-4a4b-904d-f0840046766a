package outfox.ead.noah.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "DictPostIdHistory")
public class DictPostIdHistory {
    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "ID")
    private Long id;
    @Basic
    @Column(name = "AD_CONTENT_ID")
    private Long adContentId;

    @Basic
    @Column(name = "SDK_SCHEMA_ID")
    private Long sdkSchemaId;

    @Basic
    @Column(name = "DICT_POST_ID")
    private String dictPostId;

    @Basic
    @Column(name = "CREATE_TIME")
    private Date createTime;

}
