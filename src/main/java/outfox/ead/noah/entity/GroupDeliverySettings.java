package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Created by l<PERSON><PERSON> on 16/5/12.
 */
@Entity
@Table(name = "GroupDeliverySettings", schema = "")
public class GroupDeliverySettings {
    private long groupDeliverySettingsId;
    private Long adGroupId = 0L;
    private Integer deliveryScopeType = 0;
    private String settingsContent = "";

    public static String DELIVERY_SCOPE_TYPE_STR = "deliveryScopeType";
    public static String SETTINGS_CONTENT_STR = "settingsContent";
    public static String GROUP_DELIVERY_SETTINGS_ID_STR = "groupDeliverySettingsId";

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "GROUP_DELIVERY_SETTINGS_ID", nullable = false, insertable = true, updatable = true)
    public long getGroupDeliverySettingsId() {
        return groupDeliverySettingsId;
    }

    public void setGroupDeliverySettingsId(long groupDeliverySettingsId) {
        this.groupDeliverySettingsId = groupDeliverySettingsId;
    }

    @Basic
    @Column(name = "AD_GROUP_ID", nullable = true, insertable = true, updatable = true)
    public Long getAdGroupId() {
        return adGroupId;
    }

    public void setAdGroupId(Long adGroupId) {
        this.adGroupId = adGroupId;
    }

    @Basic
    @Column(name = "DELIVERY_SCOPE_TYPE", nullable = true, insertable = true, updatable = true)
    public Integer getDeliveryScopeType() {
        return deliveryScopeType;
    }

    public void setDeliveryScopeType(Integer deliveryScopeType) {
        this.deliveryScopeType = deliveryScopeType;
    }

    @Basic
    @Column(name = "SETTINGS_CONTENT", nullable = true, insertable = true, updatable = true, length = 16777215)
    public String getSettingsContent() {
        return settingsContent;
    }

    public void setSettingsContent(String settingsContent) {
        this.settingsContent = settingsContent;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        GroupDeliverySettings that = (GroupDeliverySettings) o;

        if (groupDeliverySettingsId != that.groupDeliverySettingsId) return false;
        if (adGroupId != null ? !adGroupId.equals(that.adGroupId) : that.adGroupId != null) return false;
        if (deliveryScopeType != null ? !deliveryScopeType.equals(that.deliveryScopeType) : that.deliveryScopeType != null)
            return false;
        if (settingsContent != null ? !settingsContent.equals(that.settingsContent) : that.settingsContent != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (groupDeliverySettingsId ^ (groupDeliverySettingsId >>> 32));
        result = 31 * result + (adGroupId != null ? adGroupId.hashCode() : 0);
        result = 31 * result + (deliveryScopeType != null ? deliveryScopeType.hashCode() : 0);
        result = 31 * result + (settingsContent != null ? settingsContent.hashCode() : 0);
        return result;
    }
}
