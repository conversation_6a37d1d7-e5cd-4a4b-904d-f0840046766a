package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Created by liza<PERSON> on 16/5/12.
 */
@Entity
@Table(name = "AdContentExtendForSDK", schema = "")
public class AdContentExtendForSdk {
    private long sdkAdContentId;
    private long adContentId;
    private String sdkImageData;
    private String sdkIconimage;
    private String sdkMainimage;
    private String sdkTextData;
    private String sdkTitle;
    private String sdkSummary;
    private String sdkDescription;
    private String sdkSlotId;
    private Integer sdkDataType;
    private Long createTime;
    private Long lastModTime;
    private String sdkData;
    private String sdkSchemaSatisfied;
    private Integer sdkLandingpageType;
    /**
     * 青少年模式。
     *
     * 目前只支持在审核系统中修改为 false，在智选中 copy 变体即使此字段为 false，也需要设置为 true。
     */
    private Boolean youthMode = true;
    /**
     * 词典虚拟账户ID
     */
    private String dictUid;
    /**
     * 词典内容ID
     */
    private String dictPostId;

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "SDK_AD_CONTENT_ID", nullable = false, insertable = true, updatable = true)
    public long getSdkAdContentId() {
        return sdkAdContentId;
    }

    public void setSdkAdContentId(long sdkAdContentId) {
        this.sdkAdContentId = sdkAdContentId;
    }

    @Basic
    @Column(name = "AD_CONTENT_ID", nullable = false, insertable = true, updatable = true)
    public long getAdContentId() {
        return adContentId;
    }

    public void setAdContentId(long adContentId) {
        this.adContentId = adContentId;
    }

    @Basic
    @Column(name = "SDK_IMAGE_DATA", nullable = true, insertable = true, updatable = true, length = 65535)
    public String getSdkImageData() {
        return sdkImageData;
    }

    public void setSdkImageData(String sdkImageData) {
        this.sdkImageData = sdkImageData;
    }

    @Basic
    @Column(name = "SDK_ICONIMAGE", nullable = true, insertable = true, updatable = true, length = 1022)
    public String getSdkIconimage() {
        return sdkIconimage;
    }

    public void setSdkIconimage(String sdkIconimage) {
        this.sdkIconimage = sdkIconimage;
    }

    @Basic
    @Column(name = "SDK_MAINIMAGE", nullable = true, insertable = true, updatable = true, length = 1022)
    public String getSdkMainimage() {
        return sdkMainimage;
    }

    public void setSdkMainimage(String sdkMainimage) {
        this.sdkMainimage = sdkMainimage;
    }

    @Basic
    @Column(name = "SDK_TEXT_DATA", nullable = true, insertable = true, updatable = true, length = 65535)
    public String getSdkTextData() {
        return sdkTextData;
    }

    public void setSdkTextData(String sdkTextData) {
        this.sdkTextData = sdkTextData;
    }

    @Basic
    @Column(name = "SDK_TITLE", nullable = true, insertable = true, updatable = true, length = 1022)
    public String getSdkTitle() {
        return sdkTitle;
    }

    public void setSdkTitle(String sdkTitle) {
        this.sdkTitle = sdkTitle;
    }

    @Basic
    @Column(name = "SDK_SUMMARY", nullable = true, insertable = true, updatable = true, length = 1022)
    public String getSdkSummary() {
        return sdkSummary;
    }

    public void setSdkSummary(String sdkSummary) {
        this.sdkSummary = sdkSummary;
    }

    @Basic
    @Column(name = "SDK_DESCRIPTION", nullable = true, insertable = true, updatable = true, length = 1022)
    public String getSdkDescription() {
        return sdkDescription;
    }

    public void setSdkDescription(String sdkDescription) {
        this.sdkDescription = sdkDescription;
    }

    @Basic
    @Column(name = "SDK_SLOT_ID", nullable = true, insertable = true, updatable = true, length = 65535)
    public String getSdkSlotId() {
        return sdkSlotId;
    }

    public void setSdkSlotId(String sdkSlotId) {
        this.sdkSlotId = sdkSlotId;
    }

    @Basic
    @Column(name = "SDK_DATA_TYPE", nullable = true, insertable = true, updatable = true)
    public Integer getSdkDataType() {
        return sdkDataType;
    }

    public void setSdkDataType(Integer sdkDataType) {
        this.sdkDataType = sdkDataType;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Basic
    @Column(name = "SDK_DATA", nullable = true, insertable = true, updatable = true, length = 65535)
    public String getSdkData() {
        return sdkData;
    }

    public void setSdkData(String sdkData) {
        this.sdkData = sdkData;
    }

    @Basic
    @Column(name = "SDK_SCHEMA_SATISFIED", nullable = true, insertable = true, updatable = true, length = 255)
    public String getSdkSchemaSatisfied() {
        return sdkSchemaSatisfied;
    }

    public void setSdkSchemaSatisfied(String sdkSchemaSatisfied) {
        this.sdkSchemaSatisfied = sdkSchemaSatisfied;
    }

    @Basic
    @Column(name = "SDK_LANDINGPAGE_TYPE", nullable = true, insertable = true, updatable = true)
    public Integer getSdkLandingpageType() {
        return sdkLandingpageType;
    }

    public void setSdkLandingpageType(Integer sdkLandingpageType) {
        this.sdkLandingpageType = sdkLandingpageType;
    }

    @Basic
    @Column(name = "YOUTH_MODE")
    public Boolean getYouthMode() {
        return youthMode;
    }

    public void setYouthMode(Boolean youthMode) {
        this.youthMode = youthMode;
    }

    @Basic
    @Column(name = "DICT_UID")
    public String getDictUid() {
        return dictUid;
    }

    public void setDictUid(String dictUid) {
        this.dictUid = dictUid;
    }

    @Basic
    @Column(name = "DICT_POST_ID")
    public String getDictPostId() {
        return dictPostId;
    }

    public void setDictPostId(String dictPostId) {
        this.dictPostId = dictPostId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AdContentExtendForSdk that = (AdContentExtendForSdk) o;

        if (sdkAdContentId != that.sdkAdContentId) return false;
        if (adContentId != that.adContentId) return false;
        if (sdkImageData != null ? !sdkImageData.equals(that.sdkImageData) : that.sdkImageData != null) return false;
        if (sdkIconimage != null ? !sdkIconimage.equals(that.sdkIconimage) : that.sdkIconimage != null) return false;
        if (sdkMainimage != null ? !sdkMainimage.equals(that.sdkMainimage) : that.sdkMainimage != null) return false;
        if (sdkTextData != null ? !sdkTextData.equals(that.sdkTextData) : that.sdkTextData != null) return false;
        if (sdkTitle != null ? !sdkTitle.equals(that.sdkTitle) : that.sdkTitle != null) return false;
        if (sdkSummary != null ? !sdkSummary.equals(that.sdkSummary) : that.sdkSummary != null) return false;
        if (sdkDescription != null ? !sdkDescription.equals(that.sdkDescription) : that.sdkDescription != null)
            return false;
        if (sdkSlotId != null ? !sdkSlotId.equals(that.sdkSlotId) : that.sdkSlotId != null) return false;
        if (sdkDataType != null ? !sdkDataType.equals(that.sdkDataType) : that.sdkDataType != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(that.lastModTime) : that.lastModTime != null) return false;
        if (sdkData != null ? !sdkData.equals(that.sdkData) : that.sdkData != null) return false;
        if (sdkSchemaSatisfied != null ? !sdkSchemaSatisfied.equals(that.sdkSchemaSatisfied) : that.sdkSchemaSatisfied != null)
            return false;
        if (sdkLandingpageType != null ? !sdkLandingpageType.equals(that.sdkLandingpageType) : that.sdkLandingpageType != null)
            return false;

        return true;
    }



    @Override
    public int hashCode() {
        int result = (int) (sdkAdContentId ^ (sdkAdContentId >>> 32));
        result = 31 * result + (int) (adContentId ^ (adContentId >>> 32));
        result = 31 * result + (sdkImageData != null ? sdkImageData.hashCode() : 0);
        result = 31 * result + (sdkIconimage != null ? sdkIconimage.hashCode() : 0);
        result = 31 * result + (sdkMainimage != null ? sdkMainimage.hashCode() : 0);
        result = 31 * result + (sdkTextData != null ? sdkTextData.hashCode() : 0);
        result = 31 * result + (sdkTitle != null ? sdkTitle.hashCode() : 0);
        result = 31 * result + (sdkSummary != null ? sdkSummary.hashCode() : 0);
        result = 31 * result + (sdkDescription != null ? sdkDescription.hashCode() : 0);
        result = 31 * result + (sdkSlotId != null ? sdkSlotId.hashCode() : 0);
        result = 31 * result + (sdkDataType != null ? sdkDataType.hashCode() : 0);
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        result = 31 * result + (sdkData != null ? sdkData.hashCode() : 0);
        result = 31 * result + (sdkSchemaSatisfied != null ? sdkSchemaSatisfied.hashCode() : 0);
        result = 31 * result + (sdkLandingpageType != null ? sdkLandingpageType.hashCode() : 0);
        return result;
    }
}
