package outfox.ead.noah.entity;

import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/4/25
 */
@Entity
@EqualsAndHashCode
@Table(name = "AccelCampVisible")
public class AccelCampVisible {

    private Long id;
    private String campId;
    private Long sponsorId;

    public static final String ID = "id";
    public static final String CAMP_ID = "campId";
    public static final String SPONSOR_ID = "sponsorId";


    @Id
    @Column(name = "ID", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "CAMP_ID", nullable = false)
    public String getCampId() {
        return campId;
    }

    public void setCampId(String campId) {
        this.campId = campId;
    }

    @Basic
    @Column(name = "SPONSOR_ID", nullable = false)
    public Long getSponsorId() {
        return sponsorId;
    }

    public void setSponsorId(Long sponsorId) {
        this.sponsorId = sponsorId;
    }
}
