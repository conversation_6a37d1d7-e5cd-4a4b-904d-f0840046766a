package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Created by yuan<PERSON>hch on 2017/6/26.
 */
@Entity
public class MimeUploadHistory {

    private long id;
    private long sponsorId;
    private String mimeId;
    private String mimeType;
    private String mimeSrc;
    private int mimeWidth;
    private int mimeHeight;
    private String mimeName;
    private int status;
    private String createTime;
    private String modifyTime;

    /**
     * Property name constant for {@code createTime}.
     */
    public static final String PROPERTYNAME_CREATE_TIME = "createTime";
    /**
     * Property name constant for {@code mimeName}.
     */
    public static final String PROPERTYNAME_MIME_NAME = "mimeName";
    /**
     * Property name constant for {@code modifyTime}.
     */
    public static final String PROPERTYNAME_MODIFY_TIME = "modifyTime";
    /**
     * Property name constant for {@code status}.
     */
    public static final String PROPERTYNAME_STATUS = "status";
    /**
     * Property name constant for {@code mimeType}.
     */
    public static final String PROPERTYNAME_MIME_TYPE = "mimeType";
    /**
     * Property name constant for {@code sponsorId}.
     */
    public static final String PROPERTYNAME_SPONSOR_ID = "sponsorId";
    /**
     * Property name constant for {@code id}.
     */
    public static final String PROPERTYNAME_ID = "id";
    /**
     * Property name constant for {@code mimeWidth}.
     */
    public static final String PROPERTYNAME_MIME_WIDTH = "mimeWidth";
    /**
     * Property name constant for {@code mimeHeight}.
     */
    public static final String PROPERTYNAME_MIME_HEIGHT = "mimeHeight";
    /**
     * Property name constant for {@code mimeSrc}.
     */
    public static final String PROPERTYNAME_MIME_SRC = "mimeSrc";
    /**
     * Property name constant for {@code mimeId}.
     */
    public static final String PROPERTYNAME_MIME_ID = "mimeId";

    @Id
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "sponsorId")
    public long getSponsorId() {
        return sponsorId;
    }

    public void setSponsorId(long sponsorId) {
        this.sponsorId = sponsorId;
    }

    @Basic
    @Column(name = "mimeId")
    public String getMimeId() {
        return mimeId;
    }

    public void setMimeId(String mimeId) {
        this.mimeId = mimeId;
    }

    @Basic
    @Column(name = "mimeType")
    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    @Basic
    @Column(name = "mimeSrc")
    public String getMimeSrc() {
        return mimeSrc;
    }

    public void setMimeSrc(String mimeSrc) {
        this.mimeSrc = mimeSrc;
    }

    @Basic
    @Column(name = "mimeWidth")
    public int getMimeWidth() {
        return mimeWidth;
    }

    public void setMimeWidth(int mimeWidth) {
        this.mimeWidth = mimeWidth;
    }

    @Basic
    @Column(name = "mimeHeight")
    public int getMimeHeight() {
        return mimeHeight;
    }

    public void setMimeHeight(int mimeHeight) {
        this.mimeHeight = mimeHeight;
    }

    @Basic
    @Column(name = "mimeName")
    public String getMimeName() {
        return mimeName;
    }

    public void setMimeName(String mimeName) {
        this.mimeName = mimeName;
    }

    @Basic
    @Column(name = "status")
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    @Basic
    @Column(name = "createTime")
    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "modifyTime")
    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        MimeUploadHistory that = (MimeUploadHistory) o;

        if (id != that.id) return false;
        if (sponsorId != that.sponsorId) return false;
        if (mimeWidth != that.mimeWidth) return false;
        if (mimeHeight != that.mimeHeight) return false;
        if (status != that.status) return false;
        if (mimeId != null ? !mimeId.equals(that.mimeId) : that.mimeId != null) return false;
        if (mimeType != null ? !mimeType.equals(that.mimeType) : that.mimeType != null) return false;
        if (mimeSrc != null ? !mimeSrc.equals(that.mimeSrc) : that.mimeSrc != null) return false;
        if (mimeName != null ? !mimeName.equals(that.mimeName) : that.mimeName != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (modifyTime != null ? !modifyTime.equals(that.modifyTime) : that.modifyTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (id ^ (id >>> 32));
        result = 31 * result + (int) (sponsorId ^ (sponsorId >>> 32));
        result = 31 * result + (mimeId != null ? mimeId.hashCode() : 0);
        result = 31 * result + (mimeType != null ? mimeType.hashCode() : 0);
        result = 31 * result + (mimeSrc != null ? mimeSrc.hashCode() : 0);
        result = 31 * result + mimeWidth;
        result = 31 * result + mimeHeight;
        result = 31 * result + (mimeName != null ? mimeName.hashCode() : 0);
        result = 31 * result + status;
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (modifyTime != null ? modifyTime.hashCode() : 0);
        return result;
    }
}
