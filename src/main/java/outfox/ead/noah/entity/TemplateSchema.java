package outfox.ead.noah.entity;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Objects;

@Entity
@Table(name = "TemplateSchema")
public class TemplateSchema {
    private long id;
    private String name;
    private String templateElementIds;

    /**
     * Property name constant for {@code id}.
     */
    public static final String PROPERTYNAME_ID = "id";
    /**
     * Property name constant for {@code name}.
     */
    public static final String PROPERTYNAME_NAME = "name";
    /**
     * Property name constant for {@code templateElementIds}.
     */
    public static final String PROPERTYNAME_TEMPLATE_ELEMENT_IDS = "templateElementIds";

    @Id
    @GeneratedValue(generator = "nativeGenerator")
    @GenericGenerator(name = "nativeGenerator", strategy = "native")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "template_element_ids")
    public String getTemplateElementIds() {
        return templateElementIds;
    }

    public void setTemplateElementIds(String templateElementIds) {
        this.templateElementIds = templateElementIds;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TemplateSchema that = (TemplateSchema) o;
        return id == that.id &&
                Objects.equals(name, that.name) &&
                Objects.equals(templateElementIds, that.templateElementIds);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, name, templateElementIds);
    }
}
