package outfox.ead.noah.entity;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * Created by lizai on 16/5/19.
 */
@Entity
public class SdkElement {
    private long sdkElementId;
    private String sdkElementName;
    private String sdkElementKey;
    private Integer sdkElementLength;
    private Integer sdkElementHeight;
    private Integer sdkElementWidth;
    private int sdkElementType;
    private int sdkElementIsstandard;
    private int sdkElementStatus;
    private String sdkElementContent;
    private int dataAssetType;
    private Long createTime;
    private Long lastModTime;
    /**
     * 视频元素时长限制
     */
    private Long duration;

    @Id
    @Column(name = "SDK_ELEMENT_ID", nullable = false, insertable = true, updatable = true)
    public long getSdkElementId() {
        return sdkElementId;
    }

    public void setSdkElementId(long sdkElementId) {
        this.sdkElementId = sdkElementId;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_NAME", nullable = false, insertable = true, updatable = true, length = 255)
    public String getSdkElementName() {
        return sdkElementName;
    }

    public void setSdkElementName(String sdkElementName) {
        this.sdkElementName = sdkElementName;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_KEY", nullable = false, insertable = true, updatable = true, length = 255)
    public String getSdkElementKey() {
        return sdkElementKey;
    }

    public void setSdkElementKey(String sdkElementKey) {
        this.sdkElementKey = sdkElementKey;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_LENGTH", nullable = true, insertable = true, updatable = true)
    public Integer getSdkElementLength() {
        return sdkElementLength;
    }

    public void setSdkElementLength(Integer sdkElementLength) {
        this.sdkElementLength = sdkElementLength;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_HEIGHT", nullable = true, insertable = true, updatable = true)
    public Integer getSdkElementHeight() {
        return sdkElementHeight;
    }

    public void setSdkElementHeight(Integer sdkElementHeight) {
        this.sdkElementHeight = sdkElementHeight;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_WIDTH", nullable = true, insertable = true, updatable = true)
    public Integer getSdkElementWidth() {
        return sdkElementWidth;
    }

    public void setSdkElementWidth(Integer sdkElementWidth) {
        this.sdkElementWidth = sdkElementWidth;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_TYPE", nullable = false, insertable = true, updatable = true)
    public int getSdkElementType() {
        return sdkElementType;
    }

    public void setSdkElementType(int sdkElementType) {
        this.sdkElementType = sdkElementType;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_ISSTANDARD", nullable = false, insertable = true, updatable = true)
    public int getSdkElementIsstandard() {
        return sdkElementIsstandard;
    }

    public void setSdkElementIsstandard(int sdkElementIsstandard) {
        this.sdkElementIsstandard = sdkElementIsstandard;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_STATUS", nullable = false, insertable = true, updatable = true)
    public int getSdkElementStatus() {
        return sdkElementStatus;
    }

    public void setSdkElementStatus(int sdkElementStatus) {
        this.sdkElementStatus = sdkElementStatus;
    }

    @Basic
    @Column(name = "SDK_ELEMENT_CONTENT", nullable = true, insertable = true, updatable = true, length = 1022)
    public String getSdkElementContent() {
        return sdkElementContent;
    }

    public void setSdkElementContent(String sdkElementContent) {
        this.sdkElementContent = sdkElementContent;
    }

    @Basic
    @Column(name = "DATA_ASSET_TYPE", nullable = false, insertable = true, updatable = true)
    public int getDataAssetType() {
        return dataAssetType;
    }

    public void setDataAssetType(int dataAssetType) {
        this.dataAssetType = dataAssetType;
    }

    @Basic
    @Column(name = "CREATE_TIME", nullable = true, insertable = true, updatable = true)
    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    @Basic
    @Column(name = "LAST_MOD_TIME", nullable = true, insertable = true, updatable = true)
    public Long getLastModTime() {
        return lastModTime;
    }

    public void setLastModTime(Long lastModTime) {
        this.lastModTime = lastModTime;
    }

    @Basic
    @Column(name = "DURATION", nullable = true, insertable = true, updatable = true)
    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SdkElement that = (SdkElement) o;

        if (sdkElementId != that.sdkElementId) return false;
        if (sdkElementType != that.sdkElementType) return false;
        if (sdkElementIsstandard != that.sdkElementIsstandard) return false;
        if (sdkElementStatus != that.sdkElementStatus) return false;
        if (dataAssetType != that.dataAssetType) return false;
        if (sdkElementName != null ? !sdkElementName.equals(that.sdkElementName) : that.sdkElementName != null)
            return false;
        if (sdkElementKey != null ? !sdkElementKey.equals(that.sdkElementKey) : that.sdkElementKey != null)
            return false;
        if (sdkElementLength != null ? !sdkElementLength.equals(that.sdkElementLength) : that.sdkElementLength != null)
            return false;
        if (sdkElementHeight != null ? !sdkElementHeight.equals(that.sdkElementHeight) : that.sdkElementHeight != null)
            return false;
        if (sdkElementWidth != null ? !sdkElementWidth.equals(that.sdkElementWidth) : that.sdkElementWidth != null)
            return false;
        if (sdkElementContent != null ? !sdkElementContent.equals(that.sdkElementContent) : that.sdkElementContent != null)
            return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        if (lastModTime != null ? !lastModTime.equals(that.lastModTime) : that.lastModTime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = (int) (sdkElementId ^ (sdkElementId >>> 32));
        result = 31 * result + (sdkElementName != null ? sdkElementName.hashCode() : 0);
        result = 31 * result + (sdkElementKey != null ? sdkElementKey.hashCode() : 0);
        result = 31 * result + (sdkElementLength != null ? sdkElementLength.hashCode() : 0);
        result = 31 * result + (sdkElementHeight != null ? sdkElementHeight.hashCode() : 0);
        result = 31 * result + (sdkElementWidth != null ? sdkElementWidth.hashCode() : 0);
        result = 31 * result + sdkElementType;
        result = 31 * result + sdkElementIsstandard;
        result = 31 * result + sdkElementStatus;
        result = 31 * result + (sdkElementContent != null ? sdkElementContent.hashCode() : 0);
        result = 31 * result + dataAssetType;
        result = 31 * result + (createTime != null ? createTime.hashCode() : 0);
        result = 31 * result + (lastModTime != null ? lastModTime.hashCode() : 0);
        return result;
    }
}
