package outfox.ead.noah.controller.extracter;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.controller.validator.AdCampaignValidator;
import outfox.ead.noah.entity.AdCampaign;
import outfox.ead.noah.entity.AdCampaignExtendForDsp;
import outfox.ead.noah.entity.AdPlan;
import outfox.ead.noah.entity.Area;
import outfox.ead.noah.entity.Sponsor;
import outfox.ead.noah.util.DateTime;
import outfox.ead.noah.util.datautil.DataUtil;
import outfox.ead.noah.util.initialize.GlobalDataManager;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import static outfox.ead.noah.util.params.AdCampaignParams.*;

/**
 * Created by yuanzhch on 2017/7/4.
 */
@Slf4j
public class AdCampaignExtracter {

    private AdCampaignValidator adCampaignValidator;

    private Sponsor sponsor;

    public AdCampaignExtracter(AdCampaignValidator adCampaignValidator, Sponsor sponsor) {
        this.adCampaignValidator = adCampaignValidator;
        this.sponsor = sponsor;
    }

    private void inflateAdPlan(AdPlan adPlan) {
        adPlan.setAdOpenTime(DateTime.getDateFromString(adCampaignValidator.getOpenDate()));
        adPlan.setAdCloseTime(DateTime.getDateFromString(adCampaignValidator.getCloseDate()));
        String budget = adCampaignValidator.getBudget();
        if (budget == null) {
            adPlan.setFixDayBudget(Integer.MAX_VALUE);
            adPlan.setDayBudget(Integer.MAX_VALUE);
        } else {
            adPlan.setFixDayBudget(DataUtil.getMoneyX100(budget));
            adPlan.setDayBudget(DataUtil.getMoneyX100(budget));
        }
        adPlan.setAdNet(58);
        adPlan.setAdSchedule(adCampaignValidator.getSchedule());

        String internalDest = adCampaignValidator.getInternalDest();
        String overseasDest = adCampaignValidator.getOverseasDest();
        // 不限地域
        if (NO_REGION_LIMIT.equals(internalDest) || GlobalDataManager.deliveryProvinceList == null
                || GlobalDataManager.deliveryCityList == null) {
            adPlan.setDestProvince("0");
            adPlan.setDestCity("0");
        } else { // 限定地域
            if (Strings.isBlank(internalDest) && !Strings.isBlank(overseasDest)) {//国内没选择地域，海外定向地域
                adPlan.setDestProvince(NOT_SELECTED);
                adPlan.setDestCity(NOT_SELECTED);
            } else {
                String[] validProvinceAndCity = extractValidDomesticCity(internalDest);
                //如果有省id则获默认选中改省所有城市
                adPlan.setDestProvince(validProvinceAndCity[0]);
                adPlan.setDestCity(validProvinceAndCity[1]);
            }
        }
        adPlan.setDestOverseas(overseasDest);

    }


    public AdPlan extractAdPlan() {
        AdPlan adPlan = new AdPlan();
        inflateAdPlan(adPlan);
        adPlan.setStatus(0);
        adPlan.setCreateTime(System.currentTimeMillis());
        adPlan.setLastModTime(System.currentTimeMillis());
        return adPlan;
    }

    public AdCampaign extractAdCampaign() {
        AdCampaign adCampaign = new AdCampaign();
        adCampaign.setSponsorId(sponsor.getSponsorId());
        adCampaign.setName(adCampaignValidator.getAdCampaignName());
        if (adCampaignValidator.getAdPlatform().equals(Constants.PLATFORM_APP_STRING)) {
            adCampaign.setProductType(Constants.PRODUCT_TYPE_APP);
        } else {
            adCampaign.setProductType(Constants.PRODUCT_TYPE_DSPPUBLISH);
        }
        adCampaign.setLandingPageType(Integer.parseInt(adCampaignValidator.getLandingType()));
        adCampaign.setStatus(sponsor.getStatus());
        adCampaign.setCreateTime(System.currentTimeMillis());
        adCampaign.setLastModTime(System.currentTimeMillis());
        return adCampaign;
    }

    public AdCampaignExtendForDsp extractAdCampaignExtendForDsp() {
        AdCampaignExtendForDsp adCampaignExtendForDsp =
                new AdCampaignExtendForDsp();
        adCampaignExtendForDsp.setAdPlatform(Integer.parseInt(adCampaignValidator.getAdPlatform()));
        adCampaignExtendForDsp.setAdType(Constants.DB_CPC);
        return adCampaignExtendForDsp;
    }

    /**
     * 修改系列
     * @param
     * @return
     */
    public AdPlan extractModifiedAdPlan(AdPlan adPlan) {
        inflateAdPlan(adPlan);
        adPlan.setLastModTime(System.currentTimeMillis());
        return adPlan;
    }

    public AdCampaign extractModifiedAdCampaign(AdCampaign adCampaign) {
        adCampaign.setName(adCampaignValidator.getAdCampaignName());
        adCampaign.setLastModTime(System.currentTimeMillis());
        return adCampaign;
    }

    public AdCampaign extractCopyAdCampaign() {
        return extractAdCampaign();
    }

    public AdPlan extractCopyAdPlan() {
        return extractAdPlan();
    }

    public AdCampaignExtendForDsp extractCopyCampaignDsp() {
        return extractAdCampaignExtendForDsp();
    }

    public String[] extractValidDomesticCity(String internalDest) {
        StringBuilder provinceStr = new StringBuilder();
        StringBuilder cityStr = new StringBuilder();
        Set<Integer> provinceIds = new HashSet<>();
        Set<Integer> cityIds = new HashSet<>();
        for (Area province : GlobalDataManager.deliveryProvinceList) {
            provinceIds.add(province.getId());
        }
        for (Area city : GlobalDataManager.deliveryCityList) {
            cityIds.add(city.getId());
        }
        Set<Integer> areaIds = Arrays.stream(internalDest.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
        if (internalDest.equals(SELECT_ALL)) {
            areaIds.addAll(provinceIds);
            areaIds.addAll(cityIds);
        } else {
            Set<Integer> newAreaIds = new HashSet<>();
            for (Integer id : areaIds) {
                if (provinceIds.contains(id)) {
                    GlobalDataManager.deliveryProvinceToCities.get(GlobalDataManager.deliveryAreaMap.get(id)).forEach(city -> {
                        newAreaIds.add(city.getId());
                    });
                }
            }
            for (Integer id : areaIds) {
                if (cityIds.contains(id)) {
                    newAreaIds.add(GlobalDataManager.deliveryCityToProvince.get(id));
                }
            }
            areaIds.addAll(newAreaIds);
        }
        for (Integer id : areaIds) {
            if (provinceIds.contains(id)) {
                provinceStr.append(id).append(",");
            } else if (cityIds.contains(id)) {
                cityStr.append(id).append(",");
            }
        }

        if (provinceStr.length() > 0) {
            provinceStr.deleteCharAt(provinceStr.length() - 1);
        } else {
            provinceStr.append("0");
        }
        if (cityStr.length() > 0) {
            cityStr.deleteCharAt(cityStr.length() - 1);
        } else {
            cityStr.append("0");
        }

        return new String[]{provinceStr.toString(), cityStr.toString()};
    }
}
