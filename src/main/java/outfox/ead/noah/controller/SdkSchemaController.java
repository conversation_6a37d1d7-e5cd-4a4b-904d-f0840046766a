package outfox.ead.noah.controller;

import com.google.common.collect.Sets;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiImplicitParams;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.noah.annotation.CurrentUser;
import outfox.ead.noah.annotation.Log;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.entity.SdkSchema;
import outfox.ead.noah.entity.SdkSchemaData;
import outfox.ead.noah.entity.Sponsor;
import outfox.ead.noah.exception.ForbiddenException;
import outfox.ead.noah.service.AdContentService;
import outfox.ead.noah.service.AdGroupService;
import outfox.ead.noah.service.SdkSchemaService;
import outfox.ead.noah.util.ExceptionUtil;
import outfox.ead.noah.util.ValidationUtil;
import outfox.ead.noah.util.params.AdContentParams;
import outfox.ead.noah.util.params.AdGroupParams;
import outfox.ead.noah.util.web.RequestManager;

import java.util.HashSet;
import java.util.Set;

import static outfox.ead.noah.util.params.AuthParams.SPONSOR_ID;

/**
 * 样式相关接口
 * Created by huanghuan on 16/9/26.
 */
@RestController
@RequestMapping(value = "/${version}/sdkSchemas", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class SdkSchemaController {

    private static final Logger logger = LoggerFactory.getLogger(SdkSchemaController.class);

    @Autowired
    private SdkSchemaService sdkSchemaService;

    @Autowired
    private AdGroupService adGroupService;

    @Autowired
    private AdContentService adContentService;

    /**
     * 获取样式信息
     */
    @Log
    @RequestMapping(value = "/{sdkSchemaId}", method = RequestMethod.GET)
    public String getSdkSchema(@PathVariable("sdkSchemaId") Long sdkSchemaId) {
        RequestManager requestManager = new RequestManager();
        JSONObject result = new JSONObject();
        try {
            SdkSchema sdkSchema = sdkSchemaService.getSdkSchemaById(sdkSchemaId);
            result = JSONObject.fromObject(SdkSchemaData.buildSdkSchemaData(sdkSchema));
            logger.info("--- getSdkSchema action is ended!");
            return requestManager.printJsonString(result);
        } catch (Exception e) {
            logger.info("--- getSdkSchema exception", e);
            requestManager.setException(e);
            return requestManager.printJsonString(result);
        }
    }


    /**
     * 根据gropuid, contentid，用来提供"新建创意"时的预览
     */
    @Log
    @ApiOperation("根据groupid, contentid获取sdkSchema信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = SPONSOR_ID, value = SPONSOR_ID, required = true, dataType = "long", paramType = "header"),
    })
    @RequestMapping(value = "/schemaJSON", method = RequestMethod.GET)
    public String getSchemaJSON(
            @ApiParam("adGroupId, adContentId二选一填写")
            @RequestParam(value = AdGroupParams.AD_GROUP_ID, required = false) Long adGroupId,
            @RequestParam(value = AdContentParams.AD_CONTENT_ID, required = false) Long adContentId,
            @ApiParam("是否查询高质量元素")
            @RequestParam(value = "isQueryHqElement", defaultValue = "false") Boolean isQueryHqElement,
            @CurrentUser Sponsor sponsor) {
        logger.info("start with getSchemaJSON Action...");
        RequestManager requestManager = new RequestManager();
        JSONObject result = new JSONObject();
        try {
            ValidationUtil.validateOnlyOneAllowd(adGroupId, adContentId);

            if (adContentId != null) {
                adContentService.validateContentGroupCampaign(adContentId);
                ExceptionUtil.forbiddenIf(!adContentService.checkSponsorAccess(sponsor.getSponsorId(), Sets.newHashSet(adContentId)));
                result = JSONObject.fromObject(sdkSchemaService.getSchemaJson(Type.CONTENT, adContentId, isQueryHqElement));
            }

            if (adGroupId != null) {
                adGroupService.validateGroupCampaign(adGroupId);
                ExceptionUtil.forbiddenIf(!adGroupService.checkSponsorAccess(sponsor.getSponsorId(), Sets.newHashSet(adGroupId)));
                result = JSONObject.fromObject(sdkSchemaService.getSchemaJson(Type.GROUP, adGroupId, isQueryHqElement));
            }
            logger.info("Done with getSchemaJSON action...");
            return requestManager.printJsonString(result);
        } catch (Exception e) {
            logger.info("--- getSchemaJSON exception", e);
            requestManager.setException(e);
            return requestManager.printJsonString(result);
        }
    }
}
