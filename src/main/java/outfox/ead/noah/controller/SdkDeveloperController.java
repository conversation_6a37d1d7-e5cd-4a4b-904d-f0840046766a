package outfox.ead.noah.controller;

import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiImplicitParams;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import outfox.ead.noah.annotation.CurrentUser;
import outfox.ead.noah.annotation.Log;
import outfox.ead.noah.controller.validator.SdkDeveloperVaidator;
import outfox.ead.noah.entity.SdkDeveloper;
import outfox.ead.noah.entity.SdkDeveloperData;
import outfox.ead.noah.exception.BadRequestException;
import outfox.ead.noah.service.SdkDeveloperService;
import outfox.ead.noah.util.ValidationUtil;
import outfox.ead.noah.util.web.RequestManager;

import java.util.HashMap;
import java.util.Map;

import static outfox.ead.noah.constants.Constants.DEVELOPER_TYPE_CORP;
import static outfox.ead.noah.constants.Constants.DEVSTATUS_UNACTIVE;
import static outfox.ead.noah.constants.ResponseMessage.ACTIVE_FAILED;
import static outfox.ead.noah.constants.ResponseMessage.PARAMS_ERROR;
import static outfox.ead.noah.util.params.AuthParams.SDK_DEV_ID;
import static outfox.ead.noah.util.web.RequestUtil.currentUserIdCheck;

/**
 * SDK开发者相关接口
 * Created by huanghuan on 16/9/21
 */
@RestController
@RequestMapping(value = "/${version}/sdkDevelopers", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class SdkDeveloperController {

    private static final Logger logger = LoggerFactory.getLogger(SdkDeveloperController.class);

    @Autowired
    private SdkDeveloperService developerService;

    /**
     * 开发者的token获取
     */
    @Log
    @ApiOperation("获取开发者的token和DevId")
    @RequestMapping(value = "/{username}/token", method = RequestMethod.GET)
    public String getToken(@PathVariable("username") String userName) {
        RequestManager requestManager = new RequestManager();
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            SdkDeveloper sdkDeveloper = developerService.getSdkDeveloperByUserName(userName);
            if (sdkDeveloper == null) {
                throw new BadRequestException("用户名错误或不存在");
            }
            resultMap.put("sdkDevId", sdkDeveloper.getSdkDevId());
            resultMap.put("token", "this is a test token");
        } catch (Exception e) {
            logger.info("--- getToken exception", e);
            requestManager.setException(e);
        } finally {
            logger.info("--- getToken action ended!");
            return requestManager.printJsonString(resultMap);
        }
    }

    /**
     * 开发者的信息获取
     */
    @Log
    @ApiOperation("获取开发者的信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = SDK_DEV_ID, value = SDK_DEV_ID,
                    required = true, dataType = "long", paramType = "header")
    })
    @RequestMapping(value = "/{sdkDevId}", method = RequestMethod.GET)
    public String getSdkDeveloperInfo(
            @PathVariable("sdkDevId") Long sdkDevId,
            @CurrentUser SdkDeveloper sdkDeveloper) {
        RequestManager requestManager = new RequestManager();
        JSONObject result = new JSONObject();
        try {
            if (!currentUserIdCheck(sdkDevId, sdkDeveloper.getSdkDevId())) {
                throw new BadRequestException(PARAMS_ERROR);
            }
            result.put("sdkDeveloper", buildSdkDeveloperData(sdkDeveloper));
        } catch (Exception e) {
            logger.info("--- getSdkDeveloperInfo exception", e);
            requestManager.setException(e);
        } finally {
            logger.info("--- getSdkDeveloperInfo action ended!");
            return requestManager.printJsonString(result);
        }
    }

    /**
     * 构建返回结果
     */
    private SdkDeveloperData buildSdkDeveloperData(SdkDeveloper sdkDeveloper) {
        if (sdkDeveloper == null) {
            return null;
        }
        return SdkDeveloperData.builder()
                .userName(sdkDeveloper.getUserName())
                .type(sdkDeveloper.getType())
                .sdkDevName(sdkDeveloper.getSdkDevName())
                .sdkDevCompany(sdkDeveloper.getSdkDevCompany())
                .sdkDevEmail(sdkDeveloper.getSdkDevEmail())
                .sdkDevPhone(sdkDeveloper.getSdkDevPhone())
                .sdkDevAddr(sdkDeveloper.getSdkDevAddr()).build();
    }

    /**
     * 创建开发者
     */
    @Log
    @ApiOperation("创建开发者")
    @RequestMapping(method = RequestMethod.POST)
    public String createSdkDeveloper(
            @RequestParam("userName") String userName,
            @ApiParam("0表示个人用户，1表示公司用户")
            @RequestParam("type") Integer type,
            @RequestParam(value = "sdkDevName", required = false) String sdkDevName,
            @RequestParam(value = "sdkDevCompany", required = false) String sdkDevCompany,
            @RequestParam(value = "sdkDevEmail", required = false) String sdkDevEmail,
            @RequestParam(value = "sdkDevPhone", required = false) String sdkDevPhone,
            @RequestParam(value = "sdkDevAddr", required = false) String sdkDevAddr) {
        RequestManager requestManager = new RequestManager();
        JSONObject result = new JSONObject();
        try {
            /******************** 参数校验 **********************/
            SdkDeveloperVaidator validator = SdkDeveloperVaidator.builder()
                    .userName(userName).type(String.valueOf(type))
                    .sdkDevName(sdkDevName).sdkDevCompany(sdkDevCompany)
                    .sdkDevEmail(sdkDevEmail).sdkDevPhone(sdkDevPhone).sdkDevAddr(sdkDevAddr).build();
            Map<String, String> errorsMap = validation(validator);
            if (errorsMap.size() > 0) {
                throw new BadRequestException(errorsMap.toString());
            }

            /******************** 创建用户 **********************/
            SdkDeveloper sdkDeveloper = new SdkDeveloper();
            setSdkDeveloperInfo(
                    sdkDeveloper, userName, type, sdkDevName, sdkDevCompany,
                    sdkDevEmail, sdkDevPhone, sdkDevAddr);
            developerService.registerSdkDeveloper(sdkDeveloper);
            result.put("sdkDeveloper", buildSdkDeveloperData(sdkDeveloper));
        } catch (Exception e) {
            logger.error("--- createSdkDeveloper exception", e);
            requestManager.setException(e);
        } finally {
            logger.info("--- createSdkDeveloper action ended!");
            return requestManager.printJsonString(result);
        }
    }


    /**
     * 激活开发者
     */
    @Log
    @ApiOperation("激活开发者")
    @RequestMapping(value = "/{userName}/activation", method = RequestMethod.GET)
    public String activateSdkDeveloper(
            @PathVariable("userName") String userName,
            @RequestParam("validateCode") String validateCode) {
        RequestManager requestManager = new RequestManager();
        try {
            boolean activateSuccess = developerService.activateSdkDeveloper(userName, validateCode);
            if (!activateSuccess) {
                throw new BadRequestException(ACTIVE_FAILED);
            }
        } catch (Exception e) {
            logger.error("--- activateSdkDeveloper exception", e);
            requestManager.setException(e);
        } finally {
            logger.info("--- activateSdkDeveloper action ended!");
            return requestManager.printJsonString();
        }
    }

    /**
     * 参数校验
     *
     * @param validator 校验器
     * @return 包含校验结果的Map
     */
    private Map<String, String> validation(SdkDeveloperVaidator validator) throws Exception {
        Map<String, String> errorsMap = new HashMap<String, String>();
        if (validator == null) {
            return errorsMap;
        }
        /**************** 格式校验 **************/
        ValidationUtil.patternValidation(validator);

        /**************** 账户校验 **************/
        SdkDeveloper sdkDeveloper = developerService.getSdkDeveloperByUserName(validator.getUserName());
        if (sdkDeveloper != null) {
            errorsMap.put("userName", "用户名重复");
        }
        return errorsMap;
    }

    /**
     * 修改开发者信息
     */
    @Log
    @ApiOperation("修改开发者的信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = SDK_DEV_ID, value = SDK_DEV_ID,
                    required = true, dataType = "long", paramType = "header")
    })
    @RequestMapping(value = "/{sdkDevId}", method = RequestMethod.PUT)
    public String modSdkDeveloper(
            @PathVariable("sdkDevId") Long sdkDevId,
            @CurrentUser SdkDeveloper sdkDeveloper,
            @RequestParam("userName") String userName,
            @ApiParam("0表示个人用户，1表示公司用户")
            @RequestParam("type") Integer type,
            @RequestParam(value = "sdkDevName", required = false) String sdkDevName,
            @RequestParam(value = "sdkDevCompany", required = false) String sdkDevCompany,
            @RequestParam(value = "sdkDevEmail", required = false) String sdkDevEmail,
            @RequestParam(value = "sdkDevPhone", required = false) String sdkDevPhone,
            @RequestParam(value = "sdkDevAddr", required = false) String sdkDevAddr) {
        RequestManager requestManager = new RequestManager();
        JSONObject result = new JSONObject();
        try {
            /******************* 参数校验 ********************/
            if (!currentUserIdCheck(sdkDevId, sdkDeveloper.getSdkDevId()))
                return new RequestManager(1, PARAMS_ERROR).printJsonString();
            SdkDeveloperVaidator validator = SdkDeveloperVaidator.builder()
                    .userName(userName).type(String.valueOf(type))
                    .sdkDevName(sdkDevName).sdkDevCompany(sdkDevCompany)
                    .sdkDevEmail(sdkDevEmail).sdkDevPhone(sdkDevPhone).sdkDevAddr(sdkDevAddr).build();
            Map<String, String> errorsMap = validation(validator);
            if (errorsMap.size() > 0) {
                throw new BadRequestException(errorsMap.toString());
            }
            if (sdkDeveloper.getStatus() == DEVSTATUS_UNACTIVE) {
                throw new BadRequestException("未激活，不能修改");
            }
            /****************** 更新用户信息 *****************/
            setSdkDeveloperInfo(
                    sdkDeveloper, userName, type, sdkDevName, sdkDevCompany,
                    sdkDevEmail, sdkDevPhone, sdkDevAddr);
            developerService.saveOrUpdateSdkDeveloper(sdkDeveloper);
            result.put("sdkDeveloper", buildSdkDeveloperData(sdkDeveloper));
        } catch (Exception e) {
            logger.error("--- modSdkDeveloper exception", e);
            requestManager.setException(e);
        } finally {
            logger.info("--- modSdkDeveloper action is ended!");
            return requestManager.printJsonString(result);
        }
    }

    /**
     * 设置用户信息
     *
     * @param type 0表示个人用户，1表示公司用户
     */
    private void setSdkDeveloperInfo(
            SdkDeveloper sdkDeveloper, String userName, Integer type, String sdkDevName,
            String sdkDevCompany, String sdkDevEmail, String sdkDevPhone, String sdkDevAddr) {
        if (sdkDeveloper == null) {
            return;
        }
        sdkDeveloper.setUserName(userName);
        sdkDeveloper.setType(type);
        sdkDeveloper.setSdkDevName(sdkDevName);
        sdkDeveloper.setSdkDevEmail(sdkDevEmail);
        sdkDeveloper.setSdkDevPhone(sdkDevPhone);
        sdkDeveloper.setSdkDevAddr(sdkDevAddr);
        if (sdkDeveloper.getType() == DEVELOPER_TYPE_CORP) {
            sdkDeveloper.setSdkDevCompany(sdkDevCompany);
        } else {
            sdkDeveloper.setSdkDevCompany(null);
        }
    }
}
