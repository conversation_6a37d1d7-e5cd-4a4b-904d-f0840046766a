package outfox.ead.noah.controller.validator;

import outfox.ead.noah.util.ValidationUtil;

import java.util.Map;

/**
 * Created by yuanzhch on 2017/8/31.
 */
public abstract class BaseValidator implements BeanRepositoryHolder {

    public void validate(Map<String, Long> args) throws Exception {
        validateFormat();
        validateContent(args);
    }

    public void validateModified(Map<String, Long> args) throws Exception {
        validateFormat();
        validateModifiedContent(args);
    }

    private void validateFormat() throws Exception {
        ValidationUtil.patternValidation(this);
    }

    protected abstract void validateContent(Map<String, Long> args) throws Exception;

    protected abstract void validateModifiedContent(Map<String, Long> args) throws Exception;
}
