package outfox.ead.noah.controller.validator;

import lombok.Getter;
import org.hibernate.validator.constraints.NotBlank;
import outfox.ead.noah.controller.argument.AdContentArgument;
import outfox.ead.noah.controller.validator.extend.StringSize;
import outfox.ead.noah.entity.AdContent;
import outfox.ead.noah.util.ExceptionUtil;
import outfox.ead.noah.util.params.AdGroupParams;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

import static outfox.ead.noah.util.params.AdContentParams.*;

/**
 * Created by yuanzhch on 2017/6/22.
 */
public abstract class AdContentValidator extends BaseValidator {

    @NotNull
    @NotBlank(message = "推广变体名称为空")
    @StringSize(min = 1, max = 150, message = "推广变体名称在1到150个字符之间")
    @Getter
    private String adContentName;

    protected long adGroupId;

    public AdContentValidator(AdContentArgument adContentArgument) {
        this.adContentName = adContentArgument.getAdContentName();
    }

    @Override
    protected void validateContent(Map<String, Long> args) throws Exception {
        checkAdGroupId(args);
        validateContentNameExist(adGroupId);
    }

    @Override
    protected void validateModifiedContent(Map<String, Long> args) throws Exception {
        checkAdGroupId(args);
        long adContentId = args.get(AD_CONTENT_ID);
        Long needCheckNameExist = args.getOrDefault(IS_NEED_CHECK_NAME_EXIST, NEED_CHECK_NAME_EXIST);
        if (needCheckNameExist.equals(NEED_CHECK_NAME_EXIST)) {
            validateContentNameExist(adGroupId, adContentId);
        }
    }

    private void validateContentNameExist(long adGroupId) throws Exception {
        List<AdContent> adContents = beanRepository.getAdContentService()
                .getAdContentByName(adGroupId, adContentName);
        ExceptionUtil.conflictIf(adContents.size() > 0, "变体名称已存在");
    }

    private void validateContentNameExist(long adGroupId, long adContentId) throws Exception {
        List<AdContent> adContents = beanRepository.getAdContentService()
                .getAdContentByName(adGroupId, adContentName);
        ExceptionUtil.conflictIf(
                adContents.size() > 0 && adContents.get(0).getAdContentId() != adContentId,
                "变体名称已存在");
    }

    private void checkAdGroupId(Map<String, Long> args) {
        Long adGroupId = args.get(AdGroupParams.AD_GROUP_ID);
        ExceptionUtil.badRequestIf(adGroupId == null, "需要组id");
        this.adGroupId = adGroupId;
    }

    /**
     * 忽略对变体名称的检查并设置广告组ID
     */
    public void unsafeSetAdGroupId(long adGroupId) {
        this.adGroupId = adGroupId;
    }
}
