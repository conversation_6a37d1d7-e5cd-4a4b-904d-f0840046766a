package outfox.ead.noah.controller.argument;

import lombok.Data;
import lombok.Builder;

/**
 * Created by yuanzhch on 2017/9/8.
 */
@Builder
@Data
public class InvoiceArgument {
    double applyAmount;
    int invoiceType;
    int sponsorType;
    String taxpayerId;
    String corpAddress;
    String corpPhone;
    String bankName;
    String bankAccount;
    String contactName;
    String address;
    String postcode;
    String contactPhone;
    String contactEmail;
}
