package outfox.ead.noah.controller;


import com.google.gson.JsonObject;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiImplicitParams;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import outfox.ead.noah.annotation.CurrentUser;
import outfox.ead.noah.annotation.Log;
import outfox.ead.noah.entity.Sponsor;
import outfox.ead.noah.exception.BadRequestException;
import outfox.ead.noah.service.ImageService;
import outfox.ead.noah.util.ImageUploadResult;
import outfox.ead.noah.util.ValidationUtil;
import outfox.ead.noah.util.datautil.ImageUtil;
import outfox.ead.noah.util.stringutil.StringUtil;
import outfox.ead.noah.util.web.RequestManager;

import static outfox.ead.noah.util.params.AuthParams.SPONSOR_ID;

@RestController
@RequestMapping(value = "/${version}/common", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class CommonController {
    private static final Logger logger = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ImageService imageService;

    @Transactional
    @Log
    @ApiOperation(value = "接收前端发送图片数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = SPONSOR_ID, value = SPONSOR_ID, required = true, dataType = "long", paramType = "header")
    })
    @RequestMapping(value = "/oimage", method = RequestMethod.POST)
    @ResponseBody
    public String saveToOimage(
            @ApiParam("图片名称")
            @RequestParam("fileName") String fileName,
            @RequestParam("fileData") String fileData,
            @CurrentUser Sponsor sponsor) {
        logger.info("Dealing with save to Oimage Action...");
        RequestManager requestManager = new RequestManager();
        JsonObject jsonObject = new JsonObject();
        try {
            if (StringUtil.isNull(fileName) || StringUtil.isNull(fileData)) {
                throw new BadRequestException("参数为空");
            }
            ValidationUtil.validateStrLength(fileName, 100, "图片名");
            if (imageService.checkImageSize(fileName, fileData, ImageUtil.IMAGE_MAX_SIZE)) {
                throw new BadRequestException("图片尺寸应小于： " + ImageUtil.IMAGE_MAX_SIZE);
            }
            ImageUploadResult imageUploadResult = imageService.handleFileItem(sponsor.getSponsorId(), fileName, fileData);
            if (StringUtils.isNotEmpty(imageUploadResult.getError())) {
                throw new BadRequestException(imageUploadResult.getError());
            } else {
                jsonObject.addProperty("url", imageUploadResult.getSrc());
                jsonObject.addProperty("ratio", imageUploadResult.getRatio());
                jsonObject.addProperty("filename", imageUploadResult.getName());
            }
        } catch (Exception e) {
            requestManager.setException(e);
        } finally {
            logger.info("Done with save to Oimage Action...");
        }
        return requestManager.printGsonString(jsonObject);
    }
}
