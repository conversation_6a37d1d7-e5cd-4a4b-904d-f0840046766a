package outfox.ead.noah.metrics;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Created by yuanzhch on 2017/7/20.
 */
@Data
@ConfigurationProperties(prefix = "graphite")
@Component
public class GraphiteProperties {
    private String host;
    private int port;
    private String prefix;
    private int period = 1; // 上报至graphite时间间隔，单位为分钟
    private boolean company = false; // 为true表示同时将metric信息打印出来，用于debug
}
