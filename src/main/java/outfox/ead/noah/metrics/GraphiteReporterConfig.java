package outfox.ead.noah.metrics;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.graphite.Graphite;
import com.codahale.metrics.graphite.GraphiteReporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.MetricsDropwizardAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.net.InetSocketAddress;
import java.util.concurrent.TimeUnit;

/**
 * Created by yuanzhch on 2017/7/20.
 */
@Slf4j
@Configuration
@ComponentScan
@AutoConfigureAfter(MetricsDropwizardAutoConfiguration.class)
@ConditionalOnProperty(value = "graphite.enabled", havingValue = "true", matchIfMissing = false)
public class GraphiteReporterConfig {

    @Autowired
    GraphiteProperties graphiteProperties;

    @Autowired
    Filter filter;

    @Bean
    public GraphiteReporter graphiteReporter(MetricRegistry metricRegistry) {
        Graphite graphite = new Graphite(new InetSocketAddress(
                graphiteProperties.getHost(), graphiteProperties.getPort()));
        return GraphiteReporter.forRegistry(metricRegistry)
                .prefixedWith(graphiteProperties.getPrefix() + "." + Utils.getHostName())
                .convertRatesTo(TimeUnit.SECONDS)
                .convertDurationsTo(TimeUnit.MILLISECONDS)
                .filter(filter)
                .build(graphite);
    }

}
