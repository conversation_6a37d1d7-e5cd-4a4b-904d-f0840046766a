package outfox.ead.noah.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wordnik.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrUpdateAdGroupDTO {
    private Long adCampaignId;
    private String adGroupName;
    private String destLink;
    @ApiModelProperty("是否开启应用直达链接, 0 不开启， 1 开启")
    private int enableUrlScheme = 0;
    @ApiModelProperty("应用直达链接")
    private String urlScheme;
    @JsonProperty(value = "convertId")
    private String convertTrackingUid;
    @ApiModelProperty("0为正常消费，1为均匀消费")
    private String consumptionPattern;
    private String price;
    private String premiumCoefficient;
    @ApiModelProperty("100到10000000之间")
    private String dailyBudget;
    @ApiModelProperty("0为cpc,1为cpa")
    private String adType;
    @ApiModelProperty("用户自定义推广频率")
    private String displayFrequency;
    @ApiModelProperty("1为有道自有，2为今日头条，3位广点通，移动必填; pc选填，默认为1")
    private String flowType = "1";
    private String sdkApp;
    private String sdkSlot;
    @ApiModelProperty("0为iOS，1为Android,若系列为落地页型，会自动生成os定向, pc没有")
    private String appOs;
    @ApiModelProperty("1为WiFi，2为其他，3为3g，4为4g，可多选")
    private String network;
    private String slotPrice;
    @ApiModelProperty("app行为定向类型，可不填，0表示定向，1表示排除")
    private int deliveryAppType = -1;
    @ApiModelProperty("app行为定向id，以逗号分隔")
    private String deliveryApp;
    private int targetType;
    private int gender = 0;
    private String ages;
    private Integer age2Start;
    private Integer age2End;
    private String eduDegree;
    private String schoolType;
    private String profession;
    private String interests;
    private String templates;
    private String optimizationGoal;
    private String convertAction;
    private String cpaPrice;
    private Integer customAudiencePackageType;
    private String customAudiencePackages;
    @ApiModelProperty("场景ID参数，可为空，逗号分隔")
    private String scenarioIds;
    @ApiModelProperty("相关性参数。广告只能投放在与广告主相关性大于该值的位置上。使用相关性定向才有值。")
    private String minRelevancy;
    @ApiModelProperty("定制标签ID，传入已选定制标签ID,多个以英文逗号分隔，不传时代表不限")
    private String tagIds;
    @ApiModelProperty("自定义流量包类型，-1不使用，0定向, 1排除")
    private Integer customSlotPackageType = -1;
    @ApiModelProperty("自定义流量包ids，逗号分隔")
    private String customSlotPackageIds;
    @ApiModelProperty("冷启动流量包类型 0 不限，1 精选应用下载，2 精选销售线索")
    private Integer coldStartPackageType = 0;
    @ApiModelProperty("用户精准定向标签ID，多个ID用英文逗号分割")
    private String crowdOrientationIds;
    @ApiModelProperty("词典圈层定向，多个ID用英文逗号分割")
    private String dictCommunityIds;
    @ApiModelProperty("应用权限链接，适用于安卓下载型推广系列")
    private String appPermission;
    @ApiModelProperty("隐私政策链接，适用于安卓下载型推广系列")
    private String privacyPolicy;
    @ApiModelProperty("app开发者名称，适用于安卓下载型推广系列")
    private String developerName;
    @ApiModelProperty("应用名称，适用于安卓下载型推广系列")
    private String appTitle;
    @ApiModelProperty("应用的图标url，适用于安卓下载型的推广系列")
    private String appIconImage;
    @ApiModelProperty("应用版本号，适用于安卓下载型推广系列")
    private String appVersion;
    @ApiModelProperty("应用介绍URL，适用于安卓下载型的推广系列")
    private String appDescUrl;
    @ApiModelProperty("下载方式 0：直接下载；1：跳转应用市场下载，适用于安卓下载型推广系列")
    private Integer downloadType;
    @ApiModelProperty("应用包名，适用于安卓下载型推广系列")
    private String packageName;
    @ApiModelProperty("微信小程序的原始id")
    private String wechatOriginId;
    @ApiModelProperty("微信小程序的落地页路径")
    private String wechatPath;
    @ApiModelProperty("开启摇一摇")
    private Boolean shakable = false;
    @ApiModelProperty("开启滑动互动")
    private Boolean slideInteract = false;
    @ApiModelProperty("开启os主版本定向")
    private Boolean osVersionTarget = false;
    @ApiModelProperty("os主版本定向区间")
    private String osVersionInterval;
    @ApiModelProperty("是否开启搜索关键词")
    private Boolean openSearchKeywords;
    @ApiModelProperty("搜索关键词")
    private String searchKeywords;
    @ApiModelProperty("归因方式，1：点击归因 2：曝光归因")
    private Integer attributionType = 1;
    @ApiModelProperty("竞价策略，0-常规模式，1-最优成本")
    private Integer budgetClutchForConv = 0;

    @ApiModelProperty("流量分布 0-不限 1-自定义分布")
    private Integer trafficAllocateMethod = 0;

    @ApiModelProperty("自有流量分布区间")
    private Double ownTrafficRangeLow;

    @ApiModelProperty("自有流量分布区间")
    private Double ownTrafficRangeHigh;

    @ApiModelProperty("自有流量投放方式 0-不限;1-按广告位;2-按广告位类型")
    private Integer ownTrafficChoiceMode;

    @ApiModelProperty("自有流量已选范围，随自有流量投放类型变化，分别传递广告位ID和广告位类型")
    private Set<String> ownTrafficSelectedRange;

    @ApiModelProperty("联盟流量投放方式 0-不限;1-按广告位;2-按广告位类型")
    private Integer unionTrafficChoiceMode;

    @ApiModelProperty("联盟流量已选范围，随自有流量投放类型变化，分别传递广告位ID和广告位类型")
    private Set<String> unionTrafficSelectedRange;

    @ApiModelProperty("手机品牌,多个品牌用英文逗号分割")
    private String mobileBrands;

    @ApiModelProperty("是否开启ai自动探索")
    private Boolean openAiDelivery = false;

    @ApiModelProperty("排除的媒体包ID, 多个媒体包用英文逗号分隔")
    private String excludedBundlePackageIds;

    @ApiModelProperty("定向的媒体包ID，多个媒体包用英文逗号分隔")
    private String includedBundlePackageIds;

}
