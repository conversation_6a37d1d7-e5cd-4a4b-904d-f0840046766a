package outfox.ead.noah.dto;

import lombok.Data;
import outfox.ead.noah.entity.models.materiallibrary.TagItem;
import outfox.ead.noah.entity.models.materiallibrary.TagItemWithSelected;
import outfox.ead.noah.entity.models.materiallibrary.TagItemWithSelectedSub;
import outfox.ead.noah.entity.models.materiallibrary.TagItemWithSub;

import java.util.List;

/**
 * 带是否已选择的tag列表
 *
 * <AUTHOR>
 */
@Data
public class AllTagListWithSelectedDto {
    /**
     * 媒体分类
     */
    private List<TagItemWithSelectedSub> slotTags;
    /**
     * 素材类型分类
     */
    private List<TagItemWithSelected> templateTags;
    /**
     * 行业分类（暂时只有一级
     */
    private List<TagItemWithSelected> industryTags;
}
