package outfox.ead.noah.dto;

import lombok.Data;

/**
 * urs校验接口返回的对象
 *
 * <AUTHOR>
 **/
@Data
public class CookieVerifyResult {
    /** 状态码 */
    Integer retCode;

    /** 消息 */
    String msg;

    /** 校验成功后返回的数据对象 */
    InnerData data;

    @Data
    public static class InnerData {

        /** urs的主账号，ssn中不包含“@163”后缀 */
        String ssn;

        /** urs的别名账号，如手机号绑定邮箱后，手机号会成为别名账号，alias中不包含“@163” */
        String alias;

        /** 用户手机号 */
        String mobile;

        /** 帐号创建时间 */
        String createtime;

        /** cookie创建时间 */
        String cookieCreateTime;

        /** 是否自动登录，1=自动登录，0=不是自动登录 */
        String autologin;

        /** 用户ip,即用户登录urs时的客户端ip地址 */
        String userip;

        /** 扩展字段，以竖线|分割 */
        String misc;

    }
}
