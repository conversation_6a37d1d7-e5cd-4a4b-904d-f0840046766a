package outfox.ead.noah.dto;

import com.wordnik.swagger.annotations.ApiParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class EditAdGroupInitInfoDTO {
    @ApiParam("系列ID")
    @NotNull
    private Long champaignId;
    @ApiParam("广告组定向类型，1-广告位定向；2-广告模板定向")
    @NotNull
    private Integer type;

    @ApiParam("广告位ID")
    private Set<String> slotIds;

    @ApiParam("广告模板ID")
    private Set<Long> templateIds;

    @ApiParam("系统类型 -1 all, 0 iOS, 1 Android")
    private int appOs;
}
