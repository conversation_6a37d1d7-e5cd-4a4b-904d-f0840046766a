package outfox.ead.noah.dto.trafficallocate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TrafficAllocateSlotScopeDTO {
    private String appName;
    private Long appId;
    private Set<SlotWithPv> slotList;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SlotWithPv {
        private String slotUdid;
        private String slotName;
        private Long pv;
    }

}
