/**
 * @(#)DataUtil.java, 2007-7-3. Copyright 2007 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license
 * terms.
 */
package outfox.ead.noah.util.datautil;

import io.druid.data.input.Row;
import io.druid.query.topn.DimensionAndMetricValueExtractor;
import net.sf.json.JSONObject;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.eclipse.jetty.util.MultiMap;
import org.eclipse.jetty.util.UrlEncoded;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import outfox.ead.newstat.model.Column;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.druid.DruidBuilder;
import outfox.ead.noah.entity.*;
import outfox.ead.noah.entity.models.simple.SimpleElem;
import outfox.ead.noah.entity.models.simple.SimpleImageElem;
import outfox.ead.noah.util.MD5;
import outfox.ead.noah.util.code.NameCode;
import outfox.ead.noah.util.initialize.GlobalDataManager;
import outfox.ead.noah.util.logger.LogUtil;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static outfox.ead.noah.constants.Constants.RATE_INVALID;
import static outfox.ead.noah.util.code.NameCode.OPTIMIZATION_GOAL_CLICK;
import static outfox.ead.noah.util.code.NameCode.OPTIMIZATION_GOAL_CONV;

/**
 * 广告数据处理
 *
 * <AUTHOR>
 * @edit by huanghuan
 */
public class DataUtil {
    // logger
    private static final Logger logger = LoggerFactory.getLogger(DataUtil.class);

    /**
     * 处理电话号码：区号-电话号码-分机号
     *
     * @param phone
     * @return
     */
    public static String processPhone(String phone) {
        if (phone == null) {
            return null;
        }
        phone = phone.trim();
        String[] strs = phone.split("-");
        if ((strs == null) || (strs.length == 0)) {
            return null;
        }
        StringBuffer sb = new StringBuffer("");
        for (int i = 0; i < 2; i++) {
            if ((strs[i] != null) && (strs.length > 0)) {
                sb.append(strs[i]);
                if (i == 0) {
                    sb.append("-");
                }
            }
        }
        if ((strs.length > 2) && (strs[2] != null) && (strs[2].length() > 0)) {
            sb.append("-" + strs[2]);
        }

        return sb.toString();
    }

    public static String getHTMLUserInfo(User user, Sponsor sponsor,
                                         Contact contact) {
        StringBuffer sb = new StringBuffer("");
        sb.append("登录名：" + user.getName() + "<br>");
        sb.append("公司名称：" + sponsor.getName() + "<br>");
        sb.append("电子邮箱：" + sponsor.getEmail() + "<br>");
        sb.append("注册网址：" + sponsor.getWebUrl() + "<br>");
        String province = GlobalDataManager.getAreaNameById(sponsor
                .getProvince() + "");
        String city = GlobalDataManager.getAreaNameById(sponsor.getCity() + "");
        sb.append("所属地区：" + province + " " + city + "<br>");
        sb.append("联系人姓名：" + contact.getName() + "<br>");
        sb.append("联系人手机：" + contact.getMobile() + "<br>");
        sb.append("联系人电话：" + contact.getPhone() + "<br>");

        return sb.toString();
    }

    /**
     * 将Money转换成原始数值
     *
     * @param money 金额
     */
    public static double getMoneyDiv100(Integer money) {
        if (money == null) {
            return -1;
        }
        if (money == Integer.MAX_VALUE) {
            return -1;
        }
        return ((double) money) / Constants.MONEY_MULTIPLIER;
    }

    public static double getMoneyDiv100(Long money) {
        if (money == null) {
            return -1;
        }
        return ((double) money) / Constants.MONEY_MULTIPLIER;
    }

    public static double getMoney(Long money) {
        if (money == null) {
            return -1;
        }
        return (double) money / Constants.MONEY_MULTIPLIER;
    }

    public static Integer getMoneyX100(String money) {
        try {
            return (int) Math.round(Float.parseFloat(money)
                    * Constants.MONEY_MULTIPLIER);
        } catch (Exception e) {
            return null;
        }
    }

    public static Integer getConsumptionX110Percent(String consumption) {
        try {
            return Math.round(Float.parseFloat(consumption)
                    * Constants.CONSUMPTION_MULTIPLIER);
        } catch (Exception e) {
            return null;
        }
    }

    public static Integer getMoneyX100(double money) {
        try {
            return (int) Math.round(money * Constants.MONEY_MULTIPLIER);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 错误类
     */
    public static class Error {

    }

    /**
     * 检查为何种类型的列表，表中如果存在不一致的类型，会返回错误
     *
     * @param list    列表
     * @param classes 备选类列表
     * @return 表中元素的类型
     */
    public static Class typeCheck(List list, Class... classes) {
        if (list.size() == 0) {
            return Error.class;
        }
        Object first = list.get(0);
        Class listClass = null;
        for (Class tempClass : classes) {
            if (first.getClass().equals(tempClass)) {
                listClass = tempClass;
                break;
            }
        }
        if (listClass != null) {
            for (Object object : list) {
                if (!object.getClass().equals(listClass)) {
                    return Error.class;
                }
            }
            return listClass;
        }
        return Error.class;
    }

    /**
     * 生成开发者的验证Code
     *
     * @param sdkDeveloper 开发者
     * @return 验证Code
     */
    public static String gennerateValidateString(SdkDeveloper sdkDeveloper) {
        if (sdkDeveloper == null) {
            return null;
        }
        String validateString =
                sdkDeveloper.getSdkDevName() + sdkDeveloper.getSdkDevEmail() +
                        sdkDeveloper.getSdkDevPhone();
        validateString = MD5.encode2hex(validateString);
        return validateString;
    }

    public static Set<Long> convertStrArrayToLongSet(String[] strings) {
        Set<Long> resultSet = new HashSet<Long>();
        for (String str : strings) {
            resultSet.add(Long.parseLong(str));
        }
        return resultSet;
    }

    public static Set<Long> toLongs(String str) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptySet();
        }
        return convertStrArrayToLongSet(str.split(","));
    }

    public static Set<Integer> toIntegers(String str) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptySet();
        }
        return Arrays.stream(str.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
    }

    public static Set<String> toStrings(String str) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptySet();
        }
        return new HashSet<>(Arrays.asList(str.split(",")));
    }

    public static String collectToString(Collection<?> collection, String separator) {
        if (CollectionUtils.isEmpty(collection)) {
            return null;
        }
        return collection.stream().map(Object::toString).collect(Collectors.joining(separator));
    }
    /**
     * 填充定制数据，如广点通的图片大小要求
     *
     * @param json
     * @param sdkElement
     * @param flowType
     */
    public static void fillInSpecifiedContents(Map<String, Object> json, SdkElement sdkElement, int flowType) {
        if (flowType == 3) {
            /*********** 广点通图片大小要求 ***********/
            if (sdkElement.getSdkElementWidth()==null || sdkElement.getSdkElementHeight()==null) {
                return;
            }
            int width=sdkElement.getSdkElementWidth();
            int height=sdkElement.getSdkElementHeight();
            int gcd;
            while ((gcd=getGCD(width,height))!=1) {
                width=width/gcd;
                height=height/gcd;
            }
            ImageUtil.ThirdPartyConfig config = ImageUtil.GUANGDIANTONG_MAP.get(width + ":" + height);
            if (config!=null) {
                json.put("size", config.getSize());
                /******* 覆盖之前的建议分辨率 ******/
                if (config.getHeight() != -1 && config.getWidth() != -1) {
                    json.put("height", config.getHeight());
                    json.put("width", config.getWidth());
                }
            }
        }
    }

    public static void fillInSpecifiedContents(SimpleElem json, SdkElement sdkElement, int flowType) {
        if (flowType == 3) {
            /*********** 广点通图片大小要求 ***********/
            if (sdkElement.getSdkElementWidth()==null || sdkElement.getSdkElementHeight()==null) {
                return;
            }
            int width=sdkElement.getSdkElementWidth();
            int height=sdkElement.getSdkElementHeight();
            int gcd;
            while ((gcd=getGCD(width,height))!=1) {
                width=width/gcd;
                height=height/gcd;
            }
            ImageUtil.ThirdPartyConfig config = ImageUtil.GUANGDIANTONG_MAP.get(width + ":" + height);
            if (config!=null) {
                ((SimpleImageElem)json).setSize(config.getSize());
                /******* 覆盖之前的建议分辨率 ******/
                if (config.getHeight() != -1 && config.getWidth() != -1) {
                    ((SimpleImageElem)json).setHeight(config.getHeight());
                    ((SimpleImageElem)json).setWidth(config.getWidth());
                }
            }
        }
    }

    /**
     *  求最大公约数
     * @param a
     * @param b
     * @return
     */
    public static int getGCD(Integer a, Integer b) {
        BigInteger integerA = BigInteger.valueOf(a);
        BigInteger integerB = BigInteger.valueOf(b);
        BigInteger gcd = integerA.gcd(integerB);
        return gcd.intValue();
    }
     /**
     * 因为视频广告的样式是可以服用的，需要过滤重复的satisfiedId
     */
    public static void filterTheSameSatisfiedSchemaIds(AdContentExtendForSdk sdkContent) {
        if (sdkContent == null)
            return;
        String satisfiedIds = sdkContent.getSdkSchemaSatisfied();
        if (satisfiedIds == null || satisfiedIds.trim().length() <= 0)
            return;
        String[] satisfiedArray = satisfiedIds.split(",");
        if (satisfiedArray.length <= 0)
            return;
        Set<String> distinctIdsSet = new HashSet<String>();
        List<String> distinctIds = new ArrayList<String>();
        for (String id : satisfiedArray) {
            if (!distinctIdsSet.contains(id)) {
                distinctIds.add(id);
                distinctIdsSet.add(id);
            }
        }
        sdkContent.setSdkSchemaSatisfied(StringUtils.join(distinctIds.toArray(), ","));
    }

    /**
     * 将list或者set的内容转化为字符串，用逗号隔开，比如[1,2,3]变为1,2,3
     * @param collection
     * @return
     */
    public static <T> String convertCollectionToString(Collection<T> collection) {
        StringBuilder sb = new StringBuilder();
        if (!CollectionUtils.isEmpty(collection)) {
            for (T o : collection) {
                sb.append(o.toString());
                sb.append(",");
            }
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    public static String decode(String encodedStr) {
        try {
            return URLDecoder.decode(encodedStr, "utf8");
        } catch (Exception e) {
            LogUtil.errorLog(logger, e);
            return "";
        }
    }


    /**
     * 对应上述加密方法的字符串解密
     *
     * @param data
     * @return
     */
    public static String decodeData(String data) {
        StringBuffer sb = new StringBuffer("");
        if (null != data && !"".equals(data)) {
            for (int i = 0; i < data.length(); i++) {
                sb.append((char) ((int) data.charAt(i) - (i * 2 + 1) % 8));
            }
        }
        return sb.toString();
    }

    public static String getLandingTypeStr(Integer landingType) {
        String landingTypeStr = "";
        if (landingType == null) {
            landingTypeStr = "";
        } else if (landingType == NameCode.AD_LANDING_ANDROID_DOWNLOAD_VALUE) {
            landingTypeStr = NameCode.AD_LANDING_ANDROID_DOWNLOAD;
        } else if (landingType == NameCode.AD_LANDING_IOS_DOWNLOAD_VALUE) {
            landingTypeStr = NameCode.AD_LANDING_IOS_DOWNLOAD;
        } else if (landingType == NameCode.AD_LANDING_WEBPAGE_VALUE) {
            landingTypeStr = NameCode.AD_LANDING_WEBPAGE;
        }
        return landingTypeStr;
    }

    public static int getOptimizationGoalValue(String optimizationGoalStr) {
        if (NameCode.CLICK.equals(optimizationGoalStr)) {
            return OPTIMIZATION_GOAL_CLICK;
        } else if (NameCode.CONV.equals(optimizationGoalStr)) {
            return OPTIMIZATION_GOAL_CONV;
        }
        return -1;
    }

    public static int getLandingTypeValue(String landingTypeStr) {
        int landingTypeValue = -1;
        if (landingTypeStr != null) {
            if (landingTypeStr.equals(NameCode.AD_LANDING_ANDROID_DOWNLOAD)) {
                landingTypeValue = NameCode.AD_LANDING_ANDROID_DOWNLOAD_VALUE;
            } else if (landingTypeStr.equals(NameCode.AD_LANDING_IOS_DOWNLOAD)) {
                landingTypeValue = NameCode.AD_LANDING_IOS_DOWNLOAD_VALUE;
            } else if (landingTypeStr.equals(NameCode.AD_LANDING_WEBPAGE)) {
                landingTypeValue = NameCode.AD_LANDING_WEBPAGE_VALUE;
            }
        }
        return landingTypeValue;
    }

    public static String[] longSet2StringArray(Set<Long> idSet) {
        if (isNotEmpty(idSet)) {
            return idSet.stream().map(String::valueOf).toArray(x -> new String[idSet.size()]);
        } else {
            return new String[0];
        }
    }

    /**
     * 根据type获得newstat查询的列
     */
    public static Column getIdColumnByType(Type type) {
        Column column;
        switch (type) {
            case CAMPAIGN:
                column = Column.CAMPAIGN_ID;
                break;
            case GROUP:
                column = Column.GROUP_ID;
                break;
            case CONTENT:
                column = Column.VARIATION_ID;
                break;
            default:
                column = Column.SPONSOR_ID;
        }
        return column;
    }


    public static DruidBuilder.DruidCol getDruidColByType(Type type) {
        DruidBuilder.DruidCol column;
        switch (type) {
            case CAMPAIGN:
                column = DruidBuilder.DruidCol.AD_CAMPAIGN_ID;
                break;
            case GROUP:
                column = DruidBuilder.DruidCol.AD_GROUP_ID;
                break;
            case CONTENT:
                column = DruidBuilder.DruidCol.VARIATION_ID;
                break;
            default:
                column = DruidBuilder.DruidCol.SPONSOR_ID;
        }
        return column;
    }

    /**
     * 根据type获得druid查询的列
     */
    public static DruidBuilder.DruidCol getIdDruidColumnByType(Type type) {
        DruidBuilder.DruidCol column;
        switch (type) {
            case CAMPAIGN:
                column = DruidBuilder.DruidCol.AD_CAMPAIGN_ID;
                break;
            case GROUP:
                column = DruidBuilder.DruidCol.AD_GROUP_ID;
                break;
            case CONTENT:
                column = DruidBuilder.DruidCol.AD_CONTENT_ID;
                break;
            default:
                column = DruidBuilder.DruidCol.SPONSOR_ID;
        }
        return column;
    }

    public static <K> List<K> keys(K... keys) {
        return Arrays.asList(keys);
    }

    public static <V> List<V> values(V... values) {
        return Arrays.asList(values);
    }

    public static <K, V> Map<K, V> newHashMap(List<K> keys, List<V> values) {
        if (keys == null || values == null) {
            return null;
        }
        if (keys.size() != values.size()) {
            return null;
        }

        Map<K, V> map = new HashMap<>();
        for (int i = 0; i < keys.size(); ++i) {
            map.put(keys.get(i), values.get(i));
        }
        return map;
    }

    public static <K, V> Map<K, V> newHashMap(Pair<K, V>... entries) {
        if (entries == null) {
            return null;
        }
        Map<K, V> map = new HashMap<>();
        Arrays.stream(entries).forEach(entry -> map.put(entry.getLeft(), entry.getRight()));
        return map;
    }

    public static <E> List<E> newArrayList(E... elems) {
        return Arrays.asList(elems);
    }

    public static <E> Set<E> newHashSet(E... elems) {
        return new HashSet<>(Arrays.asList(elems));
    }

    public static <E> Set<E> newUnmodifiableSet(E... elems) {
        return Collections.unmodifiableSet(newHashSet(elems));
    }

    public static <E> Set<E> newHashSet(Collection<E>... collections) {
        Set<E> set = new HashSet<>();
        for (Collection collection : collections) {
            set.addAll(collection);
        }
        return set;
    }

    public static boolean bothOrNone(String one, String two) {
        if (one == null && two == null) {
            return true;
        }
        if (one != null && two != null) {
            return true;
        }
        return false;
    }

    public static <A, B> Set<B> sbMap(Collection<A> datas, Function<A, B> idFunc) {
        if (CollectionUtils.isEmpty(datas)) {
            return new HashSet<>();
        }

        return datas.stream().map(idFunc).collect(Collectors.toSet());
    }

    public static <A, B> List<B> apply(List<A> datas, Map<A, B> f) {
        List<B> values = new ArrayList<>();
        datas.forEach(x -> {
            B v = f.get(x);
            if (v != null) {
                values.add(v);
            }
        });
        return new ArrayList<>(new HashSet<>(values));
    }

    public static <A, B> List<B> applyFlat(List<A> datas, Map<A, List<B>> f) {
        List<B> values = new ArrayList<>();
        datas.forEach(x -> {
            List<B> v = f.get(x);
            if (v != null) {
                values.addAll(v);
            }
        });
        return new ArrayList<>(new HashSet<>(values));
    }

    public static <A, B> Map<A, List<B>> filterEmptyValue(Map<A, List<B>> map) {
        Map<A, List<B>> result = new HashMap<>();
        map.forEach((k, v) -> {
            if (isNotEmpty(v)) {
                result.put(k, v);
            }
        });
        return result;
    }
    public static <T> Set<T> allValues(Collection<List<T>> values) {
        return values.stream().flatMap(s -> s.stream()).collect(Collectors.toSet());
    }

    public static <T> Set<T> filter(Collection<T> datas, Predicate<T> cond) {
        return datas.stream().filter(cond).collect(Collectors.toSet());
    }

    public static Set<Long> jsonKeysLongSet(String json) {
        if (StringUtils.isBlank(json)) {
            return SetUtils.EMPTY_SET;
        }
        JSONObject jsonObject = JSONObject.fromObject(json);
        Iterable<String> iterable = () -> jsonObject.keys();
        return StreamSupport.stream(iterable.spliterator(), false)
                .map(Long::parseLong)
                .collect(Collectors.toSet());
    }

    /**
     * 获取druid列的值，如果是null，默认"null".
     * druid的{@link Row#getDimension(String)}中，如果不存在也返回"null"，这里保持一致。
     */
    public static String getDimension(Row row, DruidBuilder.DruidCol col) {
        return String.valueOf(row.getRaw(col.name));
    }

    /**
     * 获取TopNQuery druid列的值，如果是null，默认"null". 如果不存在也返回"null"，这里保持一致
     */
    public static String getTopNQueryDimension(DimensionAndMetricValueExtractor extractor, String key) {
        return String.valueOf(extractor.getBaseObject().get(key));
    }

    /**
     * 将用逗号间隔的id转化成list
     * @param str ids
     * @return list
     */
    public static List<Long> toLongList(String str) {
        if (StringUtils.isBlank(str)) {
            return Collections.emptyList();
        }
        return Arrays.stream(str.split(",")).map(Long::valueOf).collect(Collectors.toList());
    }

    public static Double calculateConvertCost(Long convNum, Double cost) {
        if (convNum == null || Objects.equals(convNum, 0L)) {
            return 0.00D;
        }
        cost = (cost == null ? 0D : cost);
        return BigDecimal.valueOf(cost).divide(BigDecimal.valueOf(convNum), 2, BigDecimal.ROUND_HALF_UP)
                .doubleValue();
    }

    /**
     * 获取最小比值，比如a为6，b为8，返回结果 3:4
     * @param a
     * @param b
     * @return
     */
    public static String minScale(int a, int b) {
        BigInteger first = new BigInteger(a + "");
        BigInteger second = new BigInteger(b + "");
        BigInteger gcd = first.gcd(second);
        return first.divide(gcd) + ":" + (second.divide(gcd));
    }

    public static String formatTime(Timestamp timestamp, String pattern) {
        DateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(timestamp);
    }

    public static String escapeWildcard(String keyword){
        String[] searchList = {"_", "%", "'", "\\"};
        String[]  replacementList = {"\\_", "\\%", "\\'", "\\\\"};
        return org.apache.commons.lang3.StringUtils.replaceEach(keyword, searchList,replacementList );
    }

    public static String getMimeId(String url) {
        MultiMap<String> values = new MultiMap<>();
        UrlEncoded.decodeTo(url.substring(url.indexOf('?') + 1, url.length()), values, "UTF-8", 1000);
        return values.getString("id");
    }

    /**
     * 词包长度为8
     * @param title
     * @return
     */
    public static long getTitleLength(String title) {
        if (org.apache.commons.lang3.StringUtils.contains(title, "{")) {
            //词包算4个字,8个字符
            return getTitleLengthWithoutWordPackage(org.apache.commons.lang3.StringUtils.replace(title, title.substring(title.indexOf('{'), title.indexOf('}') + 1), ""))
                    + 8;
        } else {
            return getTitleLengthWithoutWordPackage(title);
        }
    }

    /**
     * 一个英文字符或者数字长度为1，其余字符长度为2
     * @param title
     * @return
     */
    public static long getTitleLengthWithoutWordPackage(String title) {
        try {
            return title.length() + org.apache.commons.lang3.StringUtils.replacePattern(title, "[a-zA-Z0-9]", "").length();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static String getDailyBudgetErrorMsg(String type, Double minDailyBudget) {
        return "预算不得低于当日已消费金额的110%,当前" + type + "总预算最低设置为" + minDailyBudget + "元,请您重新设置";
    }

    /**
     * 返回a/b的百分比
     * @param a
     * @param b
     * @return
     */
    public static double getRate(Long a, Long b) {
        if (b <= 0L) {
            return RATE_INVALID;
        }
        return a == 0L ? 0 : Math.round((double) a / b * 10000.0d) / 100.0d;
    }

    public static Long divide(Long a, Long b) {
        if (b <= 0L) {
            return RATE_INVALID;
        }
        return a == 0L ? 0L : Math.round((double)a * 1.0d / b);
    }
}
