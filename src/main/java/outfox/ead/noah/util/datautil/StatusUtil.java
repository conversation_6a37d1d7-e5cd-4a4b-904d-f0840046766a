package outfox.ead.noah.util.datautil;

import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.util.code.DisplayStatusCode;
import outfox.ead.noah.util.code.MaskCode;
import outfox.ead.noah.util.code.NameCode;
import outfox.ead.noah.util.code.StatusCode;
import outfox.ead.util.status.SegmentStatusUtil;

/**
 * 状态类
 *
 * <AUTHOR>
 */
public class StatusUtil {

    /**
     * 获取用户，代理商，以及管理员段的组合状态
     *
     * @param status
     * @return
     */
    public static int getUAAStatus(int status) {
        int userStatus = SegmentStatusUtil.getUserStatus(status);
        int agentStatus = SegmentStatusUtil.getAgentStatus(status);
        int adminStatus = SegmentStatusUtil.getAdminStatus(status);

        if (userStatus > 0) {
            return userStatus;
        }
        if (agentStatus > 0) {
            return agentStatus;
        }
        if (adminStatus > 0) {
            return adminStatus;
        }
        return StatusCode.ACTIVE;
    }

    public static int getUserStatus(int status) {
        return SegmentStatusUtil.getUserStatus(status);
    }

    /**
     * 获取sponsor的状态
     *
     * @param status
     * @return
     */
    public static int getSponsorStatus(int status) {
        /******* 0表示正常状态 ********/
        if (status == 0)
            return StatusCode.ACTIVE;

        /******* 获取用户，代理商，管理员状态 15-4位 *****/
        int userStatus = getUAAStatus(status);
        /******* 获取审核段状态 3-0位 **********/
        int auditStatus = SegmentStatusUtil.getAuditStatus(status);

        if (userStatus > 0) {
            return userStatus;
        }
        if (auditStatus > 0) {
            return StatusCode.UNCHECKED;
        }
        /******* 获取运营状态 31-16位 ***********/
        int opStatus = SegmentStatusUtil.getOpStatus(status);
        if (opStatus > 0) {
            return getOpStatusCode(opStatus);
        }

        return StatusCode.INACTIVE;
    }

    /**
     * 获取运营段的状态码，前提是运营状态码大于0
     *
     * @param opStatus 运营段状态，也就是原始状态的31-16位
     * @return statusCode
     */
    private static int getOpStatusCode(int opStatus) {
        int topFour = SegmentStatusUtil.getStatus(opStatus, '\uf000', 12);
        if ((topFour & 4) == 4) {
            return DisplayStatusCode.OUT_OF_BALENCE;
        } else if ((topFour & 2) == 2) {
            return DisplayStatusCode.OUT_OF_BUDGET;
        } else {
            return StatusCode.INACTIVE;
        }
    }

    /**
     * 新智选中，获取系列状态名称
     * @param status
     * @return
     */
    public static String getAdCampaignStatusName(Integer status) {
        String statusName = "UNKNOWN";

        if (status == null) {
            return  statusName;
        }

        if (status == 0) {
            return StatusCode.STATUS_ACTIVE;
        }

        int userStatus = getUAAStatus(status);

        if (userStatus >= 2) {
            statusName = StatusCode.STATUS_DELETE;
        } else if (userStatus == 1) {
            statusName = StatusCode.STATUS_STOP;
        } else if (userStatus == 0) {
            if ((status & MaskCode.OUT_OF_BALANCE_UNMASK) != 0) {
                return StatusCode.STATUS_OUT_OF_BALANCE;
            } else if ((status & MaskCode.OUT_OF_BUDGET_UNMASK) != 0) {
                return StatusCode.STATUS_OUT_OF_BUDGET;
            } else if ((status & MaskCode.UP_TO_ENDDATE_UNMASK) != 0) {
                return StatusCode.STATUS_UP_TO_ENDDATE;
            }
        }

        return statusName;
    }

    /**
     * 新智选中，获取组状态名称，目前的需求是和系列一样
     * @param status
     * @return
     */
    public static String getAdGroupStatusName(Integer status) {
        return getAdCampaignStatusName(status);
    }

    /**
     * 新智选中，获取变体状态名称，因为暂停删除等用户段状态不会传递到变体级别
     * 所以，为了判断变体状态，需要知道其所在组状态
     *
     * 有效：变体有效
     * 暂停：变体uaa段暂停
     * 删除：组删除||变体uaa段删除
     * 待审核：变体uaa段为0&&待审核
     * 审核不通过：变体uaa段为0&&审核不通过
     * @param groupStatus
     * @param contentStatus
     * @return
     */
    public static String getAdContentStatusName(Integer groupStatus, Integer contentStatus) {
        String statusName = "UNKNOWN";

        if (groupStatus == null || contentStatus == null) {
            return statusName;
        }

        int groupUserStatus = getUAAStatus(groupStatus);
        int contentUserStatus = getUAAStatus(contentStatus);

        if (groupUserStatus >= 2 || contentUserStatus >= 2) {
            return StatusCode.STATUS_DELETE;
        }

        if (contentUserStatus == 1) {
            return StatusCode.STATUS_STOP;
        }

        if (contentStatus == 0) {
            return StatusCode.STATUS_ACTIVE;
        }

        int contentAuditStatus = SegmentStatusUtil.getAuditStatus(contentStatus);
        if (contentUserStatus == 0) {
            if (contentAuditStatus == 1) {
                return StatusCode.STATUS_TO_BE_AUDIT;
            } else if (contentAuditStatus >= 2) {
                return StatusCode.STATUS_INVALID;
            }
        }

        return statusName;
    }

    public static String getStatusStringWithAlias(String alias) {
        String status = "status";
        if (alias != null && alias.length() > 0) {
            status = alias + ".status";
        }
        return status;
    }
    /**
     * 获取有效状态的sql，status=0
     * @return
     */
    public static String getActiveStatusSql(String alias) {
        return " (" + getStatusStringWithAlias(alias) + "=0) ";
    }

    /**
     * 获取暂停状态的sql，user=1||(user=0&&agent=1)||(user=0&&agent=0&&admin=1)
     */
    public static String getStopStatusSql(String alias) {
        String status = getStatusStringWithAlias(alias);
        String userSeg = status + "&" + MaskCode.USER_UNMASK;
        String agentSeg = status + "&" + MaskCode.AGENT_UNMASK;
        String adminSeg = status + "&" + MaskCode.ADMIN_UNMASK;
        return " (" + userSeg + "=" + 0x100
                + " OR (" + userSeg + "=0 "
                + " AND " + agentSeg + "=" + 0x1000 + ") "
                + " OR (" + userSeg + "=0 "
                + " AND " + agentSeg + "=0 "
                + " AND " + adminSeg + "=" + 0x10 + ")) ";
    }

    /**
     * 获取删除状态的sql，user>=2||(user=0&&agent>=2)||(user=0&&agent=0&&admin>=2)
     */
    public static String getDeleteStatusSql(String alias) {
        String status = getStatusStringWithAlias(alias);
        String userSeg = status + "&" + MaskCode.USER_UNMASK;
        String agentSeg = status + "&" + MaskCode.AGENT_UNMASK;
        String adminSeg = status + "&" + MaskCode.ADMIN_UNMASK;
        return " (" + userSeg + ">=" + 0x200
                + " OR (" + userSeg + "=0 "
                + " AND " + agentSeg + ">=" + 0x2000 + ") "
                + " OR (" + userSeg + "=0 "
                + " AND " + agentSeg + "=0 "
                + " AND " + adminSeg + ">=" + 0x20 + ")) ";
    }

    /**
     * 获取余额不足状态的sql，uaa段为0，且op段第30位为1
     */
    public static String getOutOfBalanceStatusSql(String alias) {
        return " ((" + getStatusStringWithAlias(alias) + "&" + MaskCode.UAA_UNMASK + ")=0"
                + " AND (" + getStatusStringWithAlias(alias) + "&" + MaskCode.OUT_OF_BALANCE_UNMASK + ")!=0) ";
    }

    /**
     * 获取预算不足sql，uaa段为0，op段第30位为0，第29位为1
     */
    public static String getOutOfBudgetStatusSql(String alias) {
        String status = getStatusStringWithAlias(alias);
        return " ((" + status + "&" + MaskCode.UAA_UNMASK + ")=0"
                + " AND (" + status + "&" + MaskCode.OUT_OF_BALANCE_UNMASK + ")=0"
                + " AND (" + status + "&" + MaskCode.OUT_OF_BUDGET_UNMASK + ")!=0) ";
    }

    /**
     * 获取时间截止sql，uaa为0，op段第30，29位为0，第28位为1
     */
    public static String getUpToEndDateStatusSql(String alias) {
        String status = getStatusStringWithAlias(alias);
        return " ((" + status + "&" + MaskCode.UAA_UNMASK + ")=0"
                + " AND (" + status + "&" + MaskCode.OUT_OF_BALANCE_UNMASK + ")=0"
                + " AND (" + status + "&" + MaskCode.OUT_OF_BUDGET_UNMASK + ")=0"
                + " AND (" + status + "&" + MaskCode.UP_TO_ENDDATE_UNMASK + ")!=0) ";
    }

    /**
     * 获取uaa段未删除状态的sql，!(user>=2||(user=0&&agent>=2)||(user=0&&agent=0&&admin>=2))
     * 由德摩根律得 user<2&&(user!=0||agent<2)&&(user!=0||agent!=0||admin<2)
     */
    public static String getUAAUndeleteStatusSql(String alias) {
        String status = getStatusStringWithAlias(alias);
        String userSeg = status + "&" + MaskCode.USER_UNMASK;
        String agentSeg = status + "&" + MaskCode.AGENT_UNMASK;
        String adminSeg = status + "&" + MaskCode.ADMIN_UNMASK;
        return " (" + userSeg + "<0x200"
                + " AND (" + userSeg + "!=0 OR " + agentSeg + "<0x2000) "
                + " AND (" + userSeg + "!=0 OR " + agentSeg + "!=0 OR " + adminSeg + "<0x20)) ";
    }

    /**
     * 获取待审核状态的sql，uaa为0，审核段为1
     * @return
     */
    public static String getToBeAuditStatusSql(String alias) {
        return " ((" + getStatusStringWithAlias(alias) + "&" + MaskCode.UAA_UNMASK + ")=0"
                + " AND (" + getStatusStringWithAlias(alias) + "&" + MaskCode.AUDIT_UNMASK + ")=1) ";
    }

    /**
     * 获取审核不通过的sql，uaa为0，审核段大于等于2
     * @return
     */
    public static String getInvalidStatusSql(String alias) {
        return " ((" + getStatusStringWithAlias(alias) + "&" + MaskCode.UAA_UNMASK + ")=0 "
                + " AND (" + getStatusStringWithAlias(alias) + "&" + MaskCode.AUDIT_UNMASK + ")>=2) ";
    }

    public static String getStatusSql(String status) {

        String statusSql = Constants.DEFAULT_SQL;

        if (status != null) {
            switch (status) {
                case StatusCode.STATUS_DELETE:
                    statusSql = getDeleteStatusSql(null);
                    break;
                case StatusCode.STATUS_ACTIVE:
                    statusSql = getActiveStatusSql(null);
                    break;
                case StatusCode.STATUS_STOP:
                    statusSql = getStopStatusSql(null);
                    break;
                case StatusCode.STATUS_OUT_OF_BUDGET:
                    statusSql = getOutOfBudgetStatusSql(null);
                    break;
                case StatusCode.STATUS_OUT_OF_BALANCE:
                    statusSql = getOutOfBalanceStatusSql(null);
                    break;
                case StatusCode.STATUS_UP_TO_ENDDATE:
                    statusSql = getUpToEndDateStatusSql(null);
                    break;
                case StatusCode.STATUS_UNDELETE:
                    statusSql = getUAAUndeleteStatusSql(null);
                    break;
                case StatusCode.STATUS_TO_BE_AUDIT:
                    statusSql = getToBeAuditStatusSql(null);
                default:
                    break;
            }
        }

        return statusSql;
    }

    /**
     * 新智选中，系列界面有一个开启暂停的开关，
     * 当为删除状态时，开关不可用，
     * 当为暂停状态时，需要看agent和admin段是否为暂停，如果是，则开关不可用，否则，开关为off
     * 其他状态，开关为on
     */
    public static String getAdCampaignSwitchStatus(Integer status) {

        String switchStatus = NameCode.SWITCH_DISABLE;

        if (status != null) {
            String statusName = getAdCampaignStatusName(status);
            if (statusName.equals(StatusCode.STATUS_DELETE)) {
                switchStatus = NameCode.SWITCH_DISABLE;
            } else if (statusName.equals(StatusCode.STATUS_STOP)) {
                if (SegmentStatusUtil.getAgentStatus(status) == 1
                        || SegmentStatusUtil.getAdminStatus(status) == 1) {
                    switchStatus = NameCode.SWITCH_DISABLE;
                } else {
                    switchStatus = NameCode.SWITCH_OFF;
                }
            } else {
                switchStatus = NameCode.SWITCH_ON;
            }
        }

        return switchStatus;
    }

    public static String getAdGroupSwitchStatus(Integer status) {
        return getAdCampaignSwitchStatus(status);
    }

    /**
     * 相比于系列和组，变体的暂停状态下，要同时判断组和变体
     * @param groupStatus
     * @param contentStatus
     * @return
     */
    public static String getAdContentSwitchStatus(Integer groupStatus, Integer contentStatus) {
        String switchStatus = NameCode.SWITCH_DISABLE;

        if (groupStatus != null && contentStatus != null) {
            String statusName = getAdContentStatusName(groupStatus, contentStatus);
            if (statusName.equals(StatusCode.STATUS_DELETE)) {
                switchStatus = NameCode.SWITCH_DISABLE;
            } else if (statusName.equals(StatusCode.STATUS_STOP)) {
                if (SegmentStatusUtil.getAgentStatus(groupStatus) == 1
                        || SegmentStatusUtil.getAdminStatus(groupStatus) == 1
                        || SegmentStatusUtil.getAgentStatus(contentStatus) == 1
                        || SegmentStatusUtil.getAdminStatus(contentStatus) == 1) {
                    switchStatus = NameCode.SWITCH_DISABLE;
                } else {
                    switchStatus = NameCode.SWITCH_OFF;
                }
            } else {
                switchStatus = NameCode.SWITCH_ON;
            }
        }

        return switchStatus;
    }

    public static int outOfBudgetWithCampaign(int campaignStatus, int groupStatus) {
        return SegmentStatusUtil.getOpBudgetStatus(groupStatus, SegmentStatusUtil.getOpBudgetStatus(campaignStatus));
    }

    public static int resetOutOfBudget(int status) {
        return SegmentStatusUtil.getOpBudgetStatus(status, 0);
    }

    public static int userStop(int status) {
        return SegmentStatusUtil.getUserSegStatus(status, StatusCode.STOP);
    }

    public static int deadLineWithCampaign(int campaignStatus, int groupStatus) {
        return SegmentStatusUtil.getOpDeadLineStatus(groupStatus,
                SegmentStatusUtil.getOpDeadLineStatus(campaignStatus));
    }

    public static int deadLineWithCloseTime(int campaignStatus, long closeTime) {
        if (System.currentTimeMillis() < closeTime) {
            return SegmentStatusUtil.getOpDeadLineStatus(campaignStatus, 0);
        } else {
            return SegmentStatusUtil.getOpDeadLineStatus(campaignStatus, 1);
        }
    }
}