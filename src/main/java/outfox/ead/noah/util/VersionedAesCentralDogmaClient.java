package outfox.ead.noah.util;

import com.linecorp.centraldogma.client.CentralDogma;
import com.linecorp.centraldogma.client.Watcher;
import com.linecorp.centraldogma.client.armeria.legacy.LegacyCentralDogmaBuilder;
import com.linecorp.centraldogma.common.Query;
import com.linecorp.centraldogma.common.Revision;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class VersionedAesCentralDogmaClient {

    private final List<String> centralDogmaHosts;

    private final int centralDogmaPort;

    private CentralDogma dogmaClient;

    public VersionedAesCentralDogmaClient(String centralDogmaHostStr, int centralDogmaPort) {
        this.centralDogmaHosts = Arrays.stream(centralDogmaHostStr.split(",")).collect(Collectors.toList());
        this.centralDogmaPort = centralDogmaPort;
        initDogma();
    }

    /**
     * 初始化centraldogma
     */
    public void initDogma() {
        try {
            LegacyCentralDogmaBuilder dogmaBuilder = new LegacyCentralDogmaBuilder();
            for (String host : centralDogmaHosts) {
                dogmaBuilder.host(host, centralDogmaPort);
                log.info("registered centraldogma host {" + host + ":" + centralDogmaPort + "}");
            }
            dogmaClient = dogmaBuilder.build();
        } catch (Exception e) {
            log.error("error initialize centraldogma client!!", e);
            System.exit(1);
        }
    }

    /**
     * 监听文件变化并执行回调函数
     *
     * @param projectName 配置所在项目
     * @param repoName    配置所在的仓库
     * @param query       配置的文件名
     * @param call        配置更新时执行的回调函数
     */
    public <T> void watchConfig(String projectName, String repoName,
                            Query<T> query, BiConsumer<Revision, T> call) {
        try {
            if (dogmaClient != null) {
                Watcher<T> watcher = dogmaClient.fileWatcher(projectName, repoName, query);
                watcher.watch(call);
                watcher.awaitInitialValue();
            }
        } catch (Exception e) {
            log.error("centraldogma error register file watcher!", e);
        }
    }
}
