package outfox.ead.noah.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 第三方监测链接生成
 *
 * <AUTHOR>
 * @date 2022/10/18.
 */
@Component
public class ThirdPartyLinkCreator {

    @Value("${third_part_promotion_emi_monitor_host}")
    private String emiClickHost;

    @Value("${third_part_promotion_vivo_monitor_host}")
    private String vivoClickHost;

    @Value("${third_part_promotion_huawei_monitor_host}")
    private String huaweiClickHost;

    @Value("${third_part_promotion_gdt_monitor_host}")
    private String gdtClickHost;

    @Value("${third_part_promotion_wifi_monitor_host}")
    private String wifiClickHost;

    @Value("${third_part_promotion_honor_monitor_host}")
    private String honorClickHost;

    @Value("${third_part_promotion_oppo_monitor_host}")
    private String oppoClickHost;

    @Value("${third_part_promotion_baidu_monitor_host}")
    private String baiduClickHost;

    /**
     * 生成小米feedback url
     *
     * @return
     * @see <a href="https://api.e.mi.com/doc.html#/1.0.0-mdtag9b26f-omd/document-2bd1c4c260259b072818205a8ae20139">api.e.mi.com</a>
     */
    public String emiLink() {
        //host

        return emiClickHost +
                //第三方平台宏参数
                "imei=__IMEI__" +
                "&oaid=__OAID__" +
                "&ts=__TS__" +
                "&appId=__APPID__" +
                "&adId=__ADID__" +
                "&campaignId=__CAMPAIGNID__" +
                "&customerId=__CUSTOMERID__" +
                "&callback=__CALLBACK__" +
                "&ip=__IP__" +
                "&ua=__UA__" +
                "&androidId=__ANDROIDID__" +
                "&adName=__ADNAME__" +
                "&sign=__SIGN__";
    }

    public String vivoLink() {
        return vivoClickHost;
    }

    public String huaweiLink() {
        return huaweiClickHost;
    }

    public String gdtLink() {
        return gdtClickHost;
    }

    public String wifiLink() {
        return wifiClickHost;
    }

    public String honorLink() {
        return honorClickHost +
                //第三方平台宏参数
                "os=__OS__" +
                "&ip=__IP__" +
                "&oaid=__OAID__" +
                "&requestId=__REQUESTID__" +
                "&advertiserId=__ADVERTISER_ID__" +
                "&trackId=__TRACK_ID__" +
                "&creativeId=__CREATIVE_ID__";
    }

    public String oppoLink() {
        return oppoClickHost;
    }

    public String baiduLink() {
        return baiduClickHost +
                "click_id=__CLICK_ID__" +
                "&oaid=__OAID__" +
                "&oaid_md5=__OAID_MD5__" +
                "&mac_md5=__MAC_MD5__" +
                "&mac1=__MAC__" +
                "&imei_md5=__IMEI_MD5__" +
                "&idfa=__IDFA__" +
                "&caid=__CAID__" +
                "&os_type=__OS_TYPE__" +
                "&ip=__IP__" +
                "&ua=__UA__" +
                "&model=__MODEL__" +
                "&android_id=__ANDROID_ID__" +
                "&ts=__TS__" +
                "&mac=__MAC__"+
                "&mac_md5=__MAC1__"+
                "&size=__SIZE__"+
                "&userid=__USER_ID__" +
                "&pid=__PLAN_ID__" +
                "&uid=__UNIT_ID__" +
                "&aid=__IDEA_ID__" +
                "&ext_info=__EXT_INFO__" +
                "&callType=v2" +
                "&sign=__SIGN__";

    }
}
