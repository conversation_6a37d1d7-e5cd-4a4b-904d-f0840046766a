/**
 * @(#)BeanAdapter.java, 2011-8-14. Copyright 2011 Yodao, Inc. All rights
 *                       reserved. YODAO PROPRIETARY/CONFIDENTIAL. Use is
 *                       subject to license terms.
 */
package outfox.ead.noah.util;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @edit by mengxl
 */
public class BeanAdapter {

    /**
     * copy一个对象 根据get set方法来自动注入相应的属性
     * 
     * @param <T>
     * @param src
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T copy(T src) {
        Class clazz = src.getClass();
        try {
            Object dst = clazz.newInstance();
            Method[] ms = clazz.getMethods();
            for (Method m: ms) {
                try {
                    String mname = m.getName();
                    if (mname.startsWith("set")) {
                        Class<?>[] paramTypes = m.getParameterTypes();
                        if (paramTypes.length == 1) {
                            String getMethodName = "get" + mname.substring(3);
                            Method getMethod = clazz
                                    .getDeclaredMethod(getMethodName);
                            if (getMethod != null) {
                                Object value = getMethod.invoke(src,
                                        new Object[0]);
                                // System.out.println(m.getName());
                                m.invoke(dst, value);
                            }

                        }
                    }

                } catch (Exception e) {
                    // 这里什么都不要做！并且这里的异常必须这样写，不能抛出去。
                    // 如果这里的异常往外抛，则就不会执行clazz = clazz.getSuperclass(),
                    // 最后就不会进入到父类中了
                }
            }
            return (T) dst;

        } catch (InstantiationException e) {
            // e.printStackTrace();
            return null;
        } catch (IllegalAccessException e) {
            // e.printStackTrace();
            return null;
        }
    }

    public static <T> List<T> copy(List<T> src) {
        List<T> destList = new ArrayList<>();
        for (T o : src) {
            destList.add(copy(o));
        }
        return destList;
    }
    /**
     * copy一个对象，通常适用于E是T的子类，用于复制父类的所有数据 根据get set方法来自动注入相应的属性
     * 
     * @param <E, T>
     * @param src
     *            , dst
     * @return dst
     */
    @SuppressWarnings("unchecked")
    public static <E, T> E copy(T src, E dst) {
        if (null != dst) {
            Class clazz = src.getClass();
            for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
                Method[] ms = clazz.getMethods();
                for (Method m: ms) {
                    try {
                        String mname = m.getName();
                        if (mname.startsWith("set")) {
                            Class<?>[] paramTypes = m.getParameterTypes();
                            if (paramTypes.length == 1) {
                                String getMethodName = "get"
                                        + mname.substring(3);
                                Method getMethod = clazz
                                        .getDeclaredMethod(getMethodName);
                                if (getMethod != null) {
                                    Object value = getMethod.invoke(src,
                                            new Object[0]);
                                    m.invoke(dst, value);
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 这里什么都不要做！并且这里的异常必须这样写，不能抛出去。
                        // 如果这里的异常往外抛，则就不会执行clazz = clazz.getSuperclass(),
                        // 最后就不会进入到父类中了
                    }
                }
            }

        }
        return dst;
    }

}
