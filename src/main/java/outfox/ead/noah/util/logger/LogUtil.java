/**
 * @(#)LogUtil.java, 2007-5-18. Copyright 2007 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license
 * terms.
 */
package outfox.ead.noah.util.logger;

import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import outfox.ead.noah.controller.validator.BeanRepositoryHolder;
import outfox.ead.noah.core.Type;
import outfox.ead.noah.entity.Sponsor;
import outfox.ead.noah.util.DateTime;
import outfox.ead.noah.util.code.NameCode;
import outfox.ead.noah.util.code.StatusCode;
import outfox.ead.noah.util.params.AuthParams;
import outfox.ead.noah.util.web.RequestAnalyzer;
import outfox.ead.noah.util.web.RequestUtil;
import outfox.ead.noah.util.web.SessionManager;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;


/**
 * log util: dump the user log into db or front
 *
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class LogUtil implements BeanRepositoryHolder {

    private final static Logger logger = LoggerFactory.getLogger(LogUtil.class);

    private static final Logger dbadapterLogger = LoggerFactory.getLogger("dbadapterdata");

    private static long extId = System.currentTimeMillis();

    private static final Gson GSON = new Gson();

    public static void dbAdapterInfo(String text) {
        dbadapterLogger.info(DateTime.getCurrentDate() + "\n" + text);
    }

    public static void toKafka(UserType userType, String userName, long agentId,
                               long sponsorId, OpType opType, String context, String ip,
                               HttpServletRequest request) {
        if (request == null || request.getSession() == null) {
            logger.error("log to DB failed, request is {}", request);
            return;
        }
        String userIp = request.getHeader(AuthParams.USER_IP);
        if (StringUtils.isNotBlank(userIp)) {
            if (userIp.contains(":")) {
                userIp = userIp.substring(userIp.lastIndexOf(':') + 1);
            }
            ip = userIp;
        }
        send2Kafka(userName, opType, context, request, InterceptorType.MANUAL, sponsorId);
        logger.info(format(userType.intValue(), userName, agentId, sponsorId,
                opType.getCode(), context, ip, System.currentTimeMillis()));
    }

    @SuppressWarnings("static-access")
    private static void send2Kafka(String userName, OpType opType, String context,
                                   HttpServletRequest request, int interceptorType, long sponsorId) {
        RequestAnalyzer requestAnalyzer = new RequestAnalyzer(request);
        OperationLogItem operationLogItem = new OperationLogItem();
        operationLogItem.setBrowser(requestAnalyzer.getBrowserInfo());
        int randomInt = (int) (ThreadLocalRandom.current().nextDouble() * 100000000 + 1);
        extId = randomInt + extId;
        operationLogItem.setExtId(extId);
        // 判断用户登录发布系统途径
        String from = (String) request.getSession().getAttribute(NameCode.FROM);
        if (from == null) {
            from = "adpublish";
        }
        operationLogItem.setSession(from);
        if ("stat".equals(from)) {
            operationLogItem.setSystemType(StatusCode.USER_LOGIN_FROM_STAT);
        } else if ("adagent".equals(from)) {
            operationLogItem.setSystemType(StatusCode.USER_LOGIN_FROM_AGENT);
        } else if ("adagent-depracated".equals(from)) {
            operationLogItem
                    .setSystemType(StatusCode.USER_LOGIN_FROM_AGENT_DEPRECATED);
        } else {
            operationLogItem
                    .setSystemType(StatusCode.USER_LOGIN_FROM_ADPUBLISH);
        }
        operationLogItem.setIp(requestAnalyzer.getIP());
        operationLogItem.setOs(requestAnalyzer.getOSInfo());
        operationLogItem.setRefer(requestAnalyzer.getFromUrl());
        operationLogItem.setOpName(opType.getName());
        operationLogItem.setRequestUrl(requestAnalyzer.getRequestUrl());
        // 前台拦截
        operationLogItem.setInterceptorType(interceptorType);
        operationLogItem.setUserId(sponsorId);
        operationLogItem.setLogtime(new Date());
        operationLogItem.setUserName(userName);
        operationLogItem.setContext(context);
        operationLogItem.setRequest(request.getSession().toString());

        beanRepository.getLoggerPublisher().publish(GSON.toJson(operationLogItem));
    }


    /**
     * format log to string
     *
     * @param userType
     * @param userName
     * @param agentId
     * @param sponsorId
     * @param opType
     * @param context
     * @param ip
     * @return
     */
    private static String format(int userType, String userName, long agentId,
                                 long sponsorId, int opType, String context, String ip, long time) {
        StringBuffer sb = new StringBuffer("");
        String logUserName = (userName == null || userName.trim().isEmpty()) ? "unloginUser"
                : userName;
        String logContext = (context == null || context.trim().isEmpty()) ? "no message"
                : context;
        sb.append(userType).append("|").append(logUserName).append("|")
                .append(agentId).append("|").append(sponsorId).append("|")
                .append(opType).append("|").append(logContext).append("|")
                .append(ip).append("|").append(time).append("|");
        return sb.toString();
    }

    public static void toDb(Type type, long sponsorId, OpType opType, String context) {
        try {
            Sponsor sponsor = beanRepository.getSponsorDao().getSponsor(sponsorId);
            // 如果是异步调用的话，可以在开启子线程之前将RequestAttributes对象设置为子线程共享。
            RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            if (sponsor != null && request != null && request.getSession() != null) {
                toKafka(UserType.parseInt(SessionManager.getType()), sponsor.getUserName(),
                        sponsor.getAgentId(), sponsor.getSponsorId(), opType, context,
                        RequestUtil.getIp(request), request);
            } else {
                logger.error("log to DB failed, type: {}, request is {}", type, request);
            }
        } catch (Exception e) {
            warnLog(logger, e);
        }

    }

    public static void errorLog(Logger logger, Exception e) {
        logger.error(ExceptionUtils.getRootCauseMessage(e), e);
    }

    public static void warnLog(Logger logger, Exception e) {
        logger.warn(ExceptionUtils.getRootCauseMessage(e), e);
    }

    public static void infoLog(Logger logger, Exception e) {
        logger.info(ExceptionUtils.getRootCauseMessage(e), e);
    }
}
