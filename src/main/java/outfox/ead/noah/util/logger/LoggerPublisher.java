package outfox.ead.noah.util.logger;

import com.google.common.collect.Maps;
import com.youdao.quipu.kafka.producer.AtLeastOnceKafkaProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.configuration.ConfigurationConverter;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;

import java.util.Properties;

/**
 * <p>此类将用户的操作日志发送到Kafka中。</p>
 * <p><b>注意：</b>这个类的发送机制是至少一次发送，只保证不会丢失数据。需要数据重复，如果使用{@link OperationLogItem#extId}加以区分。</p>
 *
 * <AUTHOR>
 */
@Slf4j
public class LoggerPublisher {
    private final String adpublishLogKafkaTopic;
    private final AtLeastOnceKafkaProducer atLeastOnceKafkaProducer;

    public LoggerPublisher(String topic, String adpublishLogKafkaProducerConfigPath) throws ConfigurationException {
        this.adpublishLogKafkaTopic = topic;

        PropertiesConfiguration config = new PropertiesConfiguration();
        config.load(adpublishLogKafkaProducerConfigPath);
        Properties properties = ConfigurationConverter.getProperties(config);

        atLeastOnceKafkaProducer = AtLeastOnceKafkaProducer.getInstance(Maps.fromProperties(properties));
        log.info("at least once kafka producer init successfully.");
    }

    public void publish(String logMsg) {
        atLeastOnceKafkaProducer.send(adpublishLogKafkaTopic, logMsg);
    }

    public void destroy() {
        atLeastOnceKafkaProducer.close();
    }
}
