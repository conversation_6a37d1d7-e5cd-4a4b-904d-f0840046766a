package outfox.ead.noah.util;

import com.sun.jersey.api.uri.UriComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import outfox.ead.noah.dto.CommonInputStreamResource;
import outfox.ead.noah.dto.NosResponse;
import outfox.ead.noah.exception.BadRequestException;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;

/**
 * <AUTHOR>
 */
@Slf4j
public class FileUtil {
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    private static final String LUNA_NOS_URL = "https://luna-nos.youdao.com/backend/upload";

    public static String transferNosUrl(String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add("file", new CommonInputStreamResource(getInputStream(url)));
        param.add("userCdn", true);
        param.add("useHttps", true);
        param.add("product", "ad");
        Integer width = getWidth(url);
        Integer height = getHeight(url);
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(param, headers);
        ResponseEntity<NosResponse> response = REST_TEMPLATE.postForEntity(LUNA_NOS_URL, request, NosResponse.class);
        if (response.getStatusCode().is2xxSuccessful() && response.getBody().getCode().equals(0)) {
            return response.getBody().getData().get("url") + String.format("?_w=%d&_h=%d", width, height);
        } else {
            throw new BadRequestException("NOS图片上传失败");
        }
    }

    private static Integer getHeight(String url) {
        return Integer.valueOf(UriComponent.decodeQuery(URI.create(url), true).get("h").get(0));
    }

    private static Integer getWidth(String url) {
        return Integer.valueOf(UriComponent.decodeQuery(URI.create(url), true).get("w").get(0));

    }

    public static InputStream getInputStream(String url) {
        try {
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setReadTimeout(30000);
            conn.setConnectTimeout(30000);
            //设置应用程序要从网络连接读取数据
            conn.setDoInput(true);
            conn.setRequestMethod("GET");
            if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                return conn.getInputStream();
            } else {
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("File download ERROR!", e);
            throw new BadRequestException("图片文件获取失败");
        }
    }


    public static boolean isPlainTextFile(byte[] bytes) {
        try {
            Tika tika = new Tika();
            String detect = tika.detect(bytes);
            return detect.toLowerCase().startsWith("text/");
        } catch (Exception e) {
            log.warn("detect file type failed!", e);
            return false;
        }

    }
}
