package outfox.ead.noah.util.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 广告组定向设置-自定义人群
 *
 * <AUTHOR>
 * @create 2025-05-06 16:11
 **/
@AllArgsConstructor
public enum CustomAudiencePackageEnum {
    /**
     * 定向人群
     */
    INCLUDE(0),
    /**
     * 排除人群
     */
    EXCLUDE(1);

    @Getter
    private final int code;

    public static boolean isCustomAudiencePackageEnum(int code) {
        return Arrays.stream(CustomAudiencePackageEnum.values())
                .anyMatch(value -> value.getCode() == code);
    }
}
