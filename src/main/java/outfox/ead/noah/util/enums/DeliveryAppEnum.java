package outfox.ead.noah.util.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 广告组定向设置-APP行为
 *
 * <AUTHOR>
 * @create 2025-05-06 17:15
 **/
@AllArgsConstructor
public enum DeliveryAppEnum {
    /**
     * 定向
     */
    INCLUDE(0),
    /**
     * 排除
     */
    EXCLUDE(1);

    @Getter
    private final int code;

    public static boolean isDeliveryAppEnum(int code) {
        return Arrays.stream(DeliveryAppEnum.values())
                .anyMatch(value -> value.getCode() == code);
    }
}
