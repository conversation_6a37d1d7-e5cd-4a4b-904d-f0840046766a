package outfox.ead.noah.util.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/31
 */
public enum EduDegreeEnum {

    UNKNOWN_EDU_DEGREE(-1, ""),
    COLLEGE_AND_BELOW(0, "大专及以下"),
    UNDERGRADUATE (1, "本科"),
    GRADUATE(2, "研究生")
    ;

    @Getter
    private final int code;
    private final String desc;

    EduDegreeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Set<EduDegreeEnum> toEduDegreeEnum(String eduDegree) {
        Set<EduDegreeEnum> eduDegreeEnums = new HashSet<>();
        if(StringUtils.isBlank(eduDegree)){
            return eduDegreeEnums;
        }
        return Arrays.stream(eduDegree.split(",")).map(s -> reverseLookup.getOrDefault(Integer.parseInt(s), UNKNOWN_EDU_DEGREE)).collect(Collectors.toSet());
    }

    private final static Map<Integer, EduDegreeEnum> reverseLookup = Arrays.stream(EduDegreeEnum.values()).collect(Collectors.toMap(EduDegreeEnum::getCode, Function.identity()));

    public static boolean isEduDegreeEnum(int code) {
        return Arrays.stream(EduDegreeEnum.values())
                .anyMatch(value -> value.getCode() == code);
    }
}
