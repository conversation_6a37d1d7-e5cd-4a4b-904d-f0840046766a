package outfox.ead.noah.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * Created by yuanzhch on 2017/7/10.
 */
@Data
@Component
@PropertySource("classpath:applicationConfig.properties")
@ConfigurationProperties(prefix = "authfilter")
public class AuthFilterProperties {
    private String loginUrl;
    private String encoding;
    private String accountSupported;
    private String noLoginAllowd;
}
