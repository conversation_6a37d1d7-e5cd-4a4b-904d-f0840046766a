package outfox.ead.noah.util.params;

/**
 * Created by y<PERSON><PERSON><PERSON><PERSON> on 2017/7/10.
 */
public class SponsorParams {
    public static final String SPONSOR_ID = "sponsorId";
    public static final String USER_NAME = "userName";
    public static final String PASSWORD = "password";
    public static final String DIRECT = "direct"; // 是否为直销
    public static final String COMPANY = "company"; // 是否为广告公司
    public static final String PERMISSION_LIST = "permissionList"; //特殊权限列表

    public static final String SESSION_ID = "sessionId"; // session id, 用于定位是哪个用户的操作
    public static final String TRACE_ID = "traceId"; //trace id,用于跟踪用户行为
    public static final String BRAND_AD_SPONSOR = "brandAdSponsor";
    public static final String NO_OCPC_AUTO_COMPENSATION = "nooCPCAutoCompensation";
    public static final String SPONSOR_WITH_SLOT_TARGET = "sponsorWithSlotTarget"; //具有按广告位定向权限
    /**
     * 公司网络环境特有权限
     */
    public static final String INNER_COMPANY_PERMISSION = "innerCompanyPermission";
}
