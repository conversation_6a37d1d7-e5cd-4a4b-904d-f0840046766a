package outfox.ead.noah.util.params;

/**
 * Created by liza<PERSON> on 16/5/16.
 */
public class AdContentParams {

    /**
     * 查询变体ID参数
     */
    public static final String AD_CONTENT_ID = "adContentId";

    public static final String AD_CONTENT_IDS = "adContentIds";

    public static final String AD_CONTENT_NAME = "adContentName";
    /**
     * 查询推广变体的品类条件
     */
    public static final String CONDITION_CONTENT_CATEGORY = "adContentCategory";

    /**
     * 查询推广变体的DSP属性筛选条件
     */
    public static final String CONDITION_AD_CONTENT_DSP_EXTEND = "adContentExtendForDsp";

    /**
     * 查询推广变体的SDK属性筛选条件
     */
    public static final String CONDITION_AD_CONTENT_SDK_EXTEND = "adContentExtendForSdk";

    public static final String ADCONTENTS = "contents";

    public static final String IMPRESSION_LINK = "impressionLink";

    /**
     * 第三方监测曝光监测链接
     */
    public static final String EXPOSURE_MONITOR = "exposureMonitor";

    /**
     * 第三方监测点击监测链接
     */
    public static final String CLICK_MONITOR = "clickMonitor";

    public static final String IS_NEED_CHECK_NAME_EXIST = "needCheckNameExist";

    public static final Long NEED_CHECK_NAME_EXIST = 1L;

    public static final Long DO_NOT_CHECK_NAME_EXIST = 0L;

}
