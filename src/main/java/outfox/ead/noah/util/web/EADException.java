/**
 * @(#)B.java, 2007-5-18. Copyright 2007 Yodao, Inc. All rights reserved. YODAO
 *             PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.noah.util.web;

import outfox.ead.noah.util.resource.Resource;

/**
 * EAD exception
 * 
 * <AUTHOR> jian
 */
public class EADException extends Exception {

    private static final long serialVersionUID = 1L;

    private int type;

    private String name;

    private String desc;

    public EADException() {}

    public EADException(int type) {
        this.type = type;
        switch (type) {
            case ExceptionType.NO_SUCH_PAGE:
                this.name = Resource.getMessage("no_such_page_title");
                this.desc = Resource.getMessage("no_such_page_desc");
                break;
            case ExceptionType.SESSION_EXPIRED:
                this.name = Resource.getMessage("session_expired_title");
                this.desc = Resource.getMessage("session_expired_desc");
                break;
            case ExceptionType.SESSION_CONFLICT:
                this.name = Resource.getMessage("session_conflict_title");
                this.desc = Resource.getMessage("session_conflict_desc");
                break;
            case ExceptionType.SYSTEM_BUSY:
                this.name = Resource.getMessage("system_busy_title");
                this.desc = Resource.getMessage("system_busy_desc");
                break;
            case ExceptionType.SYSTEM_ERROR:
                this.name = Resource.getMessage("system_error_title");
                this.desc = Resource.getMessage("system_error_desc");
                break;
            case ExceptionType.NOPOWER:
                this.name = Resource.getMessage("nopower_title");
                this.desc = Resource.getMessage("nopower_desc");
                break;
            case ExceptionType.KEYWORD_ERROR1:
                this.name = Resource.getMessage("keyworderror.error_title");
                this.desc = Resource.getMessage("keyworderror.error1_desc");
                break;
            case ExceptionType.KEYWORD_ERROR2:
                this.name = Resource.getMessage("keyworderror.error_title");
                this.desc = Resource.getMessage("keyworderror.error2_desc");
                break;
        }

    }

    /**
     * @param type
     *            错误类型
     * @param name
     *            错误名称
     * @param desc
     *            错误描述
     */
    public EADException(int type, String name, String desc) {
        this.type = type;
        this.name = name;
        this.desc = desc;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return this.type;
    }

    public void setType(int type) {
        this.type = type;
    }

}
