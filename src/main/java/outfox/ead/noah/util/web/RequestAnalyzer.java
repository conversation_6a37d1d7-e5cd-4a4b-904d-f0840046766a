package outfox.ead.noah.util.web;

import nl.bitwalker.useragentutils.UserAgent;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import outfox.ead.noah.util.code.NameCode;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Enumeration;

import static outfox.ead.noah.util.params.AuthParams.USER_IP;

/**
 * HttpServletRequest解析
 * 
 * <AUTHOR>
 * @create 2011-2-23
 */
public class RequestAnalyzer {

    /**
     * 用于从request中获取客户端信息
     */
    private String WEB_USER_AGENT = "User-Agent";

    private HttpServletRequest httpServletRequest;

    public RequestAnalyzer(HttpServletRequest httpServletRequest) {
        super();
        this.httpServletRequest = httpServletRequest;
    }

    /**
     * 从request中取出session
     * 
     * @return HttpSession
     */
    public HttpSession getSession() {

        return this.httpServletRequest.getSession(true);
    }

    public String trimUrl(String url) {
        if (url != null && url.startsWith("http")) {
            int start = url.indexOf('/', 9);// http://, https://
            int end = url.indexOf('?');
            if (start < 0)
                return "/";
            if (end > 0)
                url = url.substring(start, end);
            else
                url = url.substring(start);
        } else
            return "/";
        if (url.endsWith("#"))
            url = url.replace("#", "");
        return url;

    }

    /**
     * 从request中取出RequestUrl
     * 
     * @return RequestUrl
     */
    public String getRequestUrl() {
        String requestUrl = this.httpServletRequest.getRequestURL().toString();
        return trimUrl(requestUrl);
    }

    public String getFromUrl() {
        String fromURL = RequestUtil
                .getParameter(httpServletRequest, "fromURL");
        return trimUrl(fromURL);
    }

    /**
     * 根据URL判断OPType
     * 
     * @return RequestUrl
     */
    // public int getOPType(){
    // int optype=OpType.UNKNOWN.intValue();
    // for(OpType op:OpType.values()){
    // if(getRequestUrl().indexOf(op.URl())>-1){
    // optype=op.intValue();
    // break;
    // }
    // }
    // return optype;
    // }

    /**
     * 从session中取出UserId
     * 
     * @return UserId
     */
    public long getUserId() {
        return getSession().getAttribute(NameCode.USER_ID) == null ? 0l
                : (Long) getSession().getAttribute(NameCode.USER_ID);
    }

    /**
     * 从session中取出UserName
     * 
     * @return UserName
     */
    public String getUserName() {
        return (String) getSession().getAttribute(NameCode.USER_NAME);
    }

    /**
     * 从request中取出Cookie
     * 
     * @return Cookie
     */
    public String getCookie() {
        if (this.httpServletRequest.getCookies() == null)
            return "";
        StringBuffer sb = new StringBuffer();
        for (Cookie cookie: httpServletRequest.getCookies()) {
            sb.append(cookie.toString()).append("\n");
        }
        return sb.toString();
    }

    /**
     * 从ClientInfo中取出浏览器信息
     *
     * @return BrowserInfo
     */
    public String getBrowserInfo() {
        String userAgent = this.httpServletRequest.getHeader(WEB_USER_AGENT);
        if (StringUtils.isEmpty(userAgent)) {
            return "";
        }
        UserAgent agent = new UserAgent(userAgent);
        if (agent != null && agent.getBrowser() != null) {
            return agent.getBrowser().toString();
        } else {
            return "";
        }

    }

    /**
     * 从ClientInfo中取出操作系统信息
     *
     * @return OSInfo
     */
    public String getOSInfo() {
        String userAgent = this.httpServletRequest.getHeader(WEB_USER_AGENT);
        UserAgent agent = new UserAgent(userAgent);
        return agent.getOperatingSystem().toString();
    }

    /**
     * 从request中获取原始IP地址
     * 
     * @return IP
     */
    public String getIP() {
        String userIp = this.httpServletRequest.getHeader(USER_IP);
        if (StringUtils.isNotEmpty(userIp)) {
            if (userIp.contains(":")) {
                userIp = userIp.substring(userIp.lastIndexOf(':') + 1);
            }
            return userIp;
        }
        if (this.httpServletRequest.getHeader("x-forwarded-for") == null) {
            return this.httpServletRequest.getRemoteAddr();
        }
        return this.httpServletRequest.getHeader("x-forwarded-for");
    }

    public String getReferer() {
        return this.httpServletRequest.getHeader(HttpHeaders.REFERER);
    }

    /**
     * request序列化
     * 
     * @return Request
     */
    public String getRequest() {
        Enumeration paramNames = this.httpServletRequest.getParameterNames();
        String requestStr = "";
        while (paramNames.hasMoreElements()) {
            String paramName = (String) paramNames.nextElement();
            requestStr += this.httpServletRequest.getParameter(paramName);
        }

        Enumeration attributeNames = this.httpServletRequest
                .getAttributeNames();
        while (attributeNames.hasMoreElements()) {
            String attributeName = (String) attributeNames.nextElement();
            requestStr += this.httpServletRequest.getAttribute(attributeName);
        }
        return requestStr;
    }
}
