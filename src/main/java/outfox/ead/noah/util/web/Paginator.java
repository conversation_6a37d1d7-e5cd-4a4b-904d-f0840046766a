/**
 * @(#)Paginator.java, Jun 21, 2007. Copyright 2007 Yodao, Inc. All rights
 * reserved. YODAO PROPRIETARY/CONFIDENTIAL. Use is
 * subject to license terms.
 */
package outfox.ead.noah.util.web;

import lombok.Data;
import outfox.ead.noah.controller.common.PagePara;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页工具
 *
 * <AUTHOR>
 */
public
@Data
class Paginator {

    // 每页显示总数
    private int pageNum = 10;

    // 当前页数
    private int page;

    // 当前页记录开始数
    private int startNum;

    // 总页数
    private int pageCount = 1;

    // 是否有下一页
    private boolean hasNext = true;

    public Paginator(Integer capacity, Integer number, int totalCount) {
        if (capacity != null) {
            pageNum = capacity;
        } else {
            pageNum = 10;
        }
        if (number == null) {
            page = 1;
        } else {
            page = number;
        }
        startNum = (page - 1) * pageNum;

        if (startNum < 0) {
            startNum = 0;
        }
        if (startNum >= totalCount) {
            startNum = startNum - pageNum;
        }

        if (totalCount > pageNum) {
            pageCount = (int) Math.ceil((double) totalCount / pageNum);
        }
        if (page >= pageCount) {
            hasNext = false;
        }
    }

    /**
     * 装载page结果
     *
     * @param list
     * @param <T>
     * @return
     */
    public <T> List<T> loadPageDate(List<T> list) {
        List<T> result = new ArrayList<T>();
        if (page > pageCount) {
            return result;
        }
        if (list != null && list.size() > pageNum) {
            for (int i = 0; i < pageNum; i++) {
                if ((startNum + i) == list.size()) {
                    break;
                }
                result.add(list.get(startNum + i));
            }
        } else {
            result = list;
        }
        return result;
    }

    public PagePara getPagePara() {
        PagePara para = new PagePara();
        para.setCurrentPage(page);
        para.setPageCount(pageCount);
        para.setHasNext(hasNext);
        para.setPageNum(pageNum);
        return para;
    }

}
