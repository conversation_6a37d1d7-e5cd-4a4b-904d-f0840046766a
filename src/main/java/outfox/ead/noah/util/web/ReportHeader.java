package outfox.ead.noah.util.web;

import java.util.ArrayList;
import java.util.List;

/**
 * 获取下载表单的表头
 * <p>
 * Created by huanghuan on 16/5/11.
 */
public class ReportHeader {

    /**
     * 财务记录表单
     *
     * @return
     */
    public static List<String> getFinanceHeader() {
        List<String> header = new ArrayList<String>();
        header.add("日期");
        header.add("入资类型");
        header.add("入资金额");
        //      header.add("撤资金额");
        return header;
    }

}

