package outfox.ead.noah.util.web;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.processors.DefaultDefaultValueProcessor;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.MediaType;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.entity.SponsorExtendForDsp;
import outfox.ead.noah.exception.BaseException;
import outfox.ead.noah.exception.ServerException;
import outfox.ead.noah.util.MetricsUtil;
import outfox.ead.noah.util.logger.LogUtil;
import outfox.ead.util.csv.CSVResultSet;
import outfox.ead.util.csv.CSVWriter;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.UndeclaredThrowableException;
import java.net.SocketTimeoutException;
import java.util.*;

import static outfox.ead.noah.constants.Constants.ERROR_CODE_SERVER_ERROR;

/**
 * Created by lizai on 15/6/13.
 */
@Data
@Slf4j
public class RequestManager {

    /* 错误代码 */
    private int ERROR_CODE = 0;

    /* 错误信息 */
    private String ERROR_MESSAGE = "";

    public static final String ERROR_CODE_STRING = "error_code";
    public static final String ERROR_MESSAGE_STRING = "error_message";
    private static final String DATA_STRING = "data";

    public RequestManager() {
        this.ERROR_CODE = 0;
        this.ERROR_MESSAGE = "";
    }

    /**
     * jsonConfig 避免如Integer为null时，json转化为0等问题
     **/
    public static final JsonConfig JSON_CONFIG = new JsonConfig();

    static {
        Class[] classes = {Integer.class, Double.class, Long.class, Short.class,
                Byte.class, Float.class, String.class, Boolean.class};
        for (Class targetClass : classes) {
            JSON_CONFIG.registerDefaultValueProcessor(targetClass, new DefaultDefaultValueProcessor() {
                @Override
                public Object getDefaultValue(Class type) {
                    return null;
                }
            });
        }
    }

    /**
     * 带错误代码和信息的构造器
     *
     * @param ERROR_CODE    代码，0表示正确，1表示错误
     * @param ERROR_MESSAGE 信息
     */
    public RequestManager(int ERROR_CODE, String ERROR_MESSAGE) {
        this.ERROR_CODE = ERROR_CODE;
        this.ERROR_MESSAGE = ERROR_MESSAGE;
    }

    public JSONObject printJson(Object data) {
        Map<String, Object> result = new LinkedHashMap<String, Object>();
        result.put(ERROR_CODE_STRING, ERROR_CODE);
        result.put(ERROR_MESSAGE_STRING, ERROR_MESSAGE);
        result.put(DATA_STRING, data);
        return JSONObject.fromObject(result, JSON_CONFIG);
    }

    public JSONObject printJson() {
        return printJson(null);
    }

    /**
     * 输出Json String结果
     * @param data Json数据
     */
    public String printJsonString(Object data) {
        Map<String, Object> result = new LinkedHashMap<String, Object>();
        result.put(ERROR_CODE_STRING, ERROR_CODE);
        result.put(ERROR_MESSAGE_STRING, ERROR_MESSAGE);
        result.put(DATA_STRING, data);
        return JSONObject.fromObject(result, JSON_CONFIG).toString();
    }

    /**
     * 使用Gson输出json string结果
     */
    public String printGsonString(JsonElement data){
        JsonObject result = new JsonObject();
        result.addProperty(ERROR_CODE_STRING, ERROR_CODE);
        result.addProperty(ERROR_MESSAGE_STRING, ERROR_MESSAGE);
        result.add(DATA_STRING, data);
        return result.toString();
    }

    public static void main(String[] args) {
        SponsorExtendForDsp sponsorExtendForDsp = new SponsorExtendForDsp();
        System.out.println(new RequestManager().printJsonString(sponsorExtendForDsp));
    }

    /**
     * 输出不带数据Json String结果
     */
    public String printJsonString() {
        return printJsonString(null);
    }

    /**
     * 设定错误信息
     */
    public RequestManager putErrorMessage(String error) {
        ERROR_CODE = 1;
        ERROR_MESSAGE = error;
        return this;
    }

    public void setException(Exception e) {
        Throwable t = e;
        if (e instanceof UndeclaredThrowableException) {
            LogUtil.errorLog(log, e);
            Throwable undeclaredThrowable = ((UndeclaredThrowableException) e).getUndeclaredThrowable();
            if (undeclaredThrowable.getCause() instanceof SocketTimeoutException) {
                t = undeclaredThrowable.getCause();
            }
        }

        if (t instanceof BaseException) {
            if (t instanceof ServerException) {
                LogUtil.errorLog(log, e);
            } else {
                LogUtil.infoLog(log, e);
            }
            ERROR_CODE = ((BaseException) e).getCode();
        } else if (t instanceof SocketTimeoutException) {
            LogUtil.errorLog(log, e);
            MetricsUtil.timeoutMeter();
            ERROR_CODE = Constants.ERROR_CODE_TIMEOUT;
        } else {
            LogUtil.errorLog(log, e);
            ERROR_CODE = ERROR_CODE_SERVER_ERROR;
        }
        ERROR_MESSAGE = ExceptionUtils.getRootCauseMessage(e);
    }

    /**
     * 在response中输出csv文件
     *
     * @param response     http response
     * @param csvResultSet csv结果集合
     * @param filename     csv文件的名称
     * @return 是否写入成功
     */
    public static boolean writeCSV(
            HttpServletResponse response, CSVResultSet csvResultSet, String filename) {
        if (response == null || csvResultSet == null) {
            return false;
        }
        response.reset();
        response.setContentType("application/csv;charset=GBK");
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        CSVWriter csvWriter = null;
        try {
            csvWriter = new CSVWriter(response.getWriter(), "GBK");

            /*************** write header ******************/
            String[] header = csvResultSet.getHeader();
            csvWriter.writeHeader(Arrays.asList(header));

            /*************** write resultSet ****************/
            while (csvResultSet.next()) {
                List<String> values = new ArrayList<String>(
                        csvResultSet.getWidth());
                for (int i = 0; i < csvResultSet.getWidth(); i++) {
                    values.add(csvResultSet.getValue(i));
                }
                csvWriter.writeFields(values);
            }
        } catch (IOException e) {
            return false;
        } finally {
            if (csvWriter != null) {
                csvWriter.close();
            }
        }
        return true;
    }

    /**
     * 通过response输出String信息
     */
    public static boolean writeString(HttpServletResponse response, String jsonString) {
        if (response == null) {
            return false;
        }
        response.reset();
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        PrintWriter printWriter = null;
        try {
            printWriter = response.getWriter();
            printWriter.write(jsonString);
            printWriter.flush();
        } catch (IOException e) {
            return false;
        } finally {
            if (printWriter != null) {
                printWriter.close();
            }
        }
        return true;
    }

    public static boolean writeExceptionMsgJson(HttpServletResponse response, Exception e, int errorCode) {
        if (e instanceof BaseException) {
            if (e instanceof ServerException) {
                LogUtil.errorLog(log, e);
            } else {
                LogUtil.infoLog(log, e);
            }
        }

        RequestManager requestManager = new RequestManager(errorCode,
                ExceptionUtils.getRootCauseMessage(e));
        return writeString(response, requestManager.printJson().toString());
    }

    public static boolean writeExceptionMsgJson(HttpServletResponse response, BaseException e) {
        return writeExceptionMsgJson(response, e, e.getCode());
    }
}
