package outfox.ead.noah.util;

import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 描述:
 * 页面相关工具栏
 * <p>
 * created by lishengle 2018-08-08 17:34
 */
public class PageUtil {
    /**
     * 将list分页方法，
     * 此处返回的是List的内部类SubList，不能强转曾ArrayList，这是原List的一个视图，对原List
     * 的增加或者删除后均会导致子列表视图的遍历、增加、删除产生ConcurrentModificationException
     * @param list 原list
     * @param offset 偏移量
     * @param limit 页大小
     * @throws Exception
     */
    public static final List pagingList(List list, int offset, int limit) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        if (offset < 0 || limit < 0) {
            throw new Exception("偏移量和大小不能为负数");
        }
        int size = list.size();
        if (offset > size) {
            return Collections.EMPTY_LIST;
        }
        return list.subList(offset, (limit + offset) > size ? size : (limit + offset));
    }
}
