package outfox.ead.noah.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.net.util.Base64;

import javax.net.ssl.*;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by wangmo on 2017/8/17.
 */
@Slf4j
public class HttpUtil {
    private static SSLSocketFactory init() throws Exception {

        class MyX509TrustManager implements X509TrustManager {

            private MyX509TrustManager() throws Exception {}

            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {}

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {}

            @Override
            public X509Certificate[] getAcceptedIssuers() {return new X509Certificate[] {};}
        }
        TrustManager[] tm = { new MyX509TrustManager() };
        System.setProperty("https.protocols", "TLSv1");
        SSLContext sslContext = SSLContext.getInstance("TLSv1","SunJSSE");
        sslContext.init(null, tm, new java.security.SecureRandom());
        return sslContext.getSocketFactory();
    }

    private static String generateContent(Map<String, Object> param) {
        if (param == null || param.size() == 0) {
            return "";
        }
        List<String> kvPairs = new ArrayList<>(param.size());
        for (Map.Entry<String, Object> entry : param.entrySet()) {
            try {
                kvPairs.add(entry.getKey() + '=' + URLEncoder.encode(entry.getValue().toString(), "utf-8"));
            } catch (Exception ignored) {
            }
        }
        StringBuilder content = new StringBuilder();
        if (kvPairs.size() == 1) {
            content = new StringBuilder(kvPairs.get(0));
        } else {
            for (String pair : kvPairs) {
                content.append(pair).append('&');
            }
            content = new StringBuilder(content.substring(0, content.length() - 1));
        }
        return content.toString();
    }

    public static String sendHttpsPost(String POST_URL, Map<String, Object> params) {
        String content = generateContent(params);
        StringBuilder re = new StringBuilder();
        try {
            URL myURL = new URL(POST_URL);
            HttpsURLConnection con = (HttpsURLConnection) myURL.openConnection();
            HostnameVerifier hostNameVerify = (urlHostName, session) -> {
                System.out.println(urlHostName);
                return true;
            };
            con.setHostnameVerifier(hostNameVerify);
            try {
                con.setSSLSocketFactory(init());
            } catch (Exception e1) {
                throw new IOException(e1);
            }

            con.setDoOutput(true);
            con.setDoInput(true);
            con.setRequestMethod("POST");
            con.setUseCaches(false);
            con.setInstanceFollowRedirects(true);
            con.setRequestProperty("Content-Type ", " application/x-www-form-urlencoded ");
            con.connect();
            DataOutputStream out = new DataOutputStream(con.getOutputStream());
            out.writeBytes(content);
            out.flush();
            out.close();

            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    con.getInputStream(), StandardCharsets.UTF_8));
            String line;

            while ((line = reader.readLine()) != null) {
                re.append(line);
            }
            reader.close();
            con.disconnect();
        } catch (Exception ignored) {
        }
        return re.toString();
    }

    private static String getBasicAuthHeader(String basicUser, String basicPassword){
        String auth = basicUser + ":" + basicPassword;
        byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.UTF_8));
        return new String(encodedAuth);
    }

    public static String sendHttpPostWithBasicAuth(String url,  Map<String, Object> params, String basicUser, String basicPassword) {
        String uri = generateContent(params);
        StringBuilder responseStr = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            //URI uriObj = new URI(uri);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Authorization", "Basic " + getBasicAuthHeader(basicUser, basicPassword));
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            try (PrintWriter out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8))) {
                out.print(uri);
                out.flush();
            }
            try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = in.readLine()) != null) {
                    responseStr.append(line);
                }
            }
            IOUtils.close(conn);
        } catch (Exception e) {
            log.error("http post request error!", e);
            throw new RuntimeException("http post request error!");
        }
        return responseStr.toString();
    }

    public static String sendHttpPost(String url,  Map<String, Object> params) {
        String uri = generateContent(params);
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder responseStr = new StringBuilder();
        try {
            URL realUrl = new URL(url);
            //URI uriObj = new URI(uri);
            URLConnection conn = realUrl.openConnection();
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            out = new PrintWriter(new OutputStreamWriter(conn.getOutputStream(), StandardCharsets.UTF_8));
            out.print(uri);
            out.flush();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                responseStr.append(line);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseStr.toString();
    }

}
