package outfox.ead.noah.util;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * Created by yuanzhch on 2017/7/24.
 */
@Component
public class ApplicationContextUtil implements ApplicationContextAware {
    @Getter @Setter
    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        setContext(applicationContext);
    }

    public static <T> T getBean(Class<T> clazz) {
        Map<String, T> beans = context.getBeansOfType(clazz);
        if (CollectionUtils.isEmpty(beans)) {
            return null;
        }
        for (String key : beans.keySet()) {
            return beans.get(key);
        }
        return null;
    }

    public static Object getBean(String name) {
        return context.getBean(name);
    }
}
