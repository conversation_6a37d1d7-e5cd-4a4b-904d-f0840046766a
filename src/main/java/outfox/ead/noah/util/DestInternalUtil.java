package outfox.ead.noah.util;


import org.apache.logging.log4j.util.Strings;
import outfox.ead.noah.entity.Area;
import outfox.ead.noah.util.initialize.GlobalDataManager;

import java.util.*;
import java.util.stream.Collectors;

import static outfox.ead.noah.util.params.AdCampaignParams.*;

public class DestInternalUtil {

    // 直辖市和不能定投子城市的id 包括(北京、上海、天津、重庆、香港、澳门、台湾)
    static Set<Integer> MunicipalityArea = new HashSet<>(Arrays.asList(1, 2, 3, 4, 5, 6, 10));

    public static String getDestInternalDest(String destProvince, String destCity) {
        Map<Integer, Set<Integer>> provinceIdToCityIds = GlobalDataManager.provinceIdToCityIds;
        Set<Integer> internalDestSet = new HashSet<>();
        //全为0代表不限定的情况,历史数据还有为null的值
        if ("0".equals(destCity) && "0".equals(destProvince) || Strings.isBlank(destCity) || Strings.isBlank(destProvince)) {
            return NO_REGION_LIMIT;
        }
        if (destProvince.equals(NOT_SELECTED) && destCity.equals(NOT_SELECTED)) {
            return "";
        }
        Set<Integer> provinceSet = Arrays.stream(destProvince.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        Set<Integer> citySet = Arrays.stream(destCity.split(",")).map(Integer::parseInt).collect(Collectors.toSet());
        for (Map.Entry<Integer, Set<Integer>> entry : provinceIdToCityIds.entrySet()) {
            if (!entry.getValue().isEmpty() && citySet.containsAll(entry.getValue())) {
                internalDestSet.add(entry.getKey());
                citySet.removeAll(entry.getValue());
            }
        }
        provinceSet.stream()
                .filter(MunicipalityArea::contains)
                .forEach(internalDestSet::add);
        //单独处理直辖市
        if (!"0".equals(destCity)) {
            internalDestSet.addAll(citySet);
        }
        if (internalDestSet.containsAll(provinceIdToCityIds.keySet())){
            return SELECT_ALL;
        }

        return internalDestSet.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }
}
