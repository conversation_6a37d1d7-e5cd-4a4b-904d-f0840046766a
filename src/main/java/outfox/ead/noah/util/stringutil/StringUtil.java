/**
 * @(#)StringUtil.java, 2007-7-3. Copyright 2007 Yodao, Inc. All rights
 * reserved. YODAO PROPRIETARY/CONFIDENTIAL. Use is subject
 * to license terms.
 */
package outfox.ead.noah.util.stringutil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.net.URI;

/**
 * 字符串转换工具
 *
 * <AUTHOR>
 */
@Slf4j
public class StringUtil {

    public static String getNakedURL(String url) {
        if (url == null) {
            return null;
        }

        if (url.startsWith("http://")) {
            return url.substring(7);
        }
        if (url.startsWith("https://")) {
            return url.substring(8);
        }
        return url;
    }

    public static String getDynamicUrl(String dynamicUrl) {
        if (dynamicUrl == null || dynamicUrl.length() == 0)
            return dynamicUrl;
        if (dynamicUrl.startsWith("https")) {
            dynamicUrl = StringUtil.getNakedURL(dynamicUrl);
            if (!dynamicUrl.startsWith("%%URL%%")) {
                dynamicUrl = "https://" + dynamicUrl;
            }
        }
        // 如果是以http或开头，去掉前缀
        else if (dynamicUrl.startsWith("http")) {
            dynamicUrl = StringUtil.getNakedURL(dynamicUrl);
            if (!dynamicUrl.startsWith("%%URL%%")) {
                dynamicUrl = "http://" + dynamicUrl;
            }
        }
        // 如果去掉前缀之后不是以%%URL%%开头，加上"http://"
        else {
            dynamicUrl = StringUtil.getNakedURL(dynamicUrl);
            if (!dynamicUrl.startsWith("%%URL%%")) {
                dynamicUrl = "http://" + dynamicUrl;
            }
        }
        return dynamicUrl;
    }

    /**
     * 功能：检查此字符串是否为空
     *
     * @return 如果为空，则返回true，否则则返回false
     */
    public static boolean isNull(String strSrouce) {
        if (strSrouce == null || strSrouce.trim().length() == 0)
            return true;
        else if (strSrouce.trim().toLowerCase().equals("null"))
            return true;
        else
            return false;
    }

    /**
     * generate a numerical string with a length
     */
    public static final String getRandomString(int length) {
        return RandomStringUtils.randomNumeric(length);
    }


    /**
     * 提取url对应域名，如：www.baidu.com -> baidu.com; 异常情况返回""
     *
     * @param url
     * @return
     */
    public static String extractDomain(String url) {
        if (StringUtils.isNotBlank(url)) {
            try {
                if (!url.startsWith("http://") && !url.startsWith("https://")) {
                    url = "http://" + url;
                }
                url = StringUtils.substringBefore(url, "?");
                URI uri = new URI(url);
                String host = uri.getHost();
                String domain = host.startsWith("www.") ? host.substring(4) : host;
                return domain;
            } catch (Throwable e) {
                log.error("extract domain error", e);
                return "";
            }
        } else {
            return "";
        }

    }

    /**
     * 检查字符串是否可以被Long.parseLong解析
     */
    public static boolean isLongParsable(String s) {
        try {
            if (NumberUtils.isParsable(s)) {
                Long.parseLong(s);
            } else {
                return false;
            }
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }
}