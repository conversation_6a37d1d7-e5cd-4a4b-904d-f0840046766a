package outfox.ead.noah.conf;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * 开发阶段加载的配置，便于不经校验既可访问swagger
 *
 * <AUTHOR>
 * @date 2018/12/5
 */
@Configuration
@EnableWebSecurity
@ConditionalOnProperty(value = "security.enabled", havingValue = "false")
public class SecurityConfigForScopeDevelop extends WebSecurityConfigurerAdapter {

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeRequests()
            .antMatchers("/**").permitAll()
            .anyRequest().authenticated();
    }

}