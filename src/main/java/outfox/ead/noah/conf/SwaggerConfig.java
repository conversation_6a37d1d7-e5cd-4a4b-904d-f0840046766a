package outfox.ead.noah.conf;

import com.mangofactory.swagger.configuration.SpringSwaggerConfig;
import com.mangofactory.swagger.models.dto.ApiInfo;
import com.mangofactory.swagger.plugin.EnableSwagger;
import com.mangofactory.swagger.plugin.SwaggerSpringMvcPlugin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.sql.Timestamp;

/**
 * swagger-ui的配置
 *
 * <AUTHOR>
 * @date 2016/5/10.
 */
@Configuration
@EnableSwagger
public class SwaggerConfig {

    private SpringSwaggerConfig springSwaggerConfig;

    @Value("${swagger.enable}")
    private boolean enableSwagger;

    @Autowired
    public void setSpringSwaggerConfig(SpringSwaggerConfig springSwaggerConfig) {
        this.springSwaggerConfig = springSwaggerConfig;
    }

    /**
     * API的描述
     **/
    private static final String description = "simpleTimePeroid都可填写：today,yesterday,last7days,last14days,last30days,thisweek,lastweek,thismonth,lastmonth,alltime";

    @Bean
    public SwaggerSpringMvcPlugin customImplementation() {
        return new SwaggerSpringMvcPlugin(this.springSwaggerConfig)
                .enable(enableSwagger)
                .apiInfo(new ApiInfo("noah api swagger",
                        description, null, null, null, null)).
                        //将Timestamp类型全部转为Long类型
                                directModelSubstitute(Timestamp.class, Long.class);
    }

}
