/**
 * @(#)IAdContentCategoryDao.java, 2013-6-17. Copyright 2013 Yodao, Inc. All rights
 *                          reserved. YODAO PROPRIETARY/CONFIDENTIAL. Use is
 *                          subject to license terms.
 */
package outfox.ead.noah.dao;

import outfox.ead.noah.entity.AdContentCategory;

/**
 * interface handling adContentCategory
 * 
 * <AUTHOR>
 */
public interface AdContentCategoryDao {

    /**
     * get AdContentCategory by adContentId
     * 
     * @param id
     * @return
     */
    public AdContentCategory getAdContentCategoryByAdContentId(long id);
    
    /**
     * saveOrUpdate an adContentCategory
     * 
     * @param adContentCategory
     * @return
     */
    public void saveOrUpdateAdContentCategory(AdContentCategory adContentCategory);

}
