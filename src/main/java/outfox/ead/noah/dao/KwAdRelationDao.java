package outfox.ead.noah.dao;

import outfox.ead.noah.entity.KwAdRelation;

import java.util.List;

/**
 * interface handling keyword-ad relation info
 * <p>
 * Created by huanghuan on 16/5/4.
 */
public interface KwAdRelationDao {

    /**
     * get kw-ad relation by id
     *
     * @param id
     * @return
     */
    public KwAdRelation getKwAdRelation(long id);

    /**
     * get kw-ad relation list by ad group id
     *
     * @param adGroupId
     * @return
     */
    public List<KwAdRelation> getKwAdRelationListByAdGroupId(long adGroupId);

    /**
     * change kw-ad relation status
     *
     * @param id
     * @param status
     */
    public void updateKwAdRelationStatus(long id, int status);
}
