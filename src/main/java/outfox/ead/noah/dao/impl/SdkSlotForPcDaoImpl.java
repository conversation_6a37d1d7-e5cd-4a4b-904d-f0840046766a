package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.SdkSlotForPcDao;
import outfox.ead.noah.entity.SdkSlotForPc;

import java.util.List;

@Repository
public class SdkSlotForPcDaoImpl extends GenericDaoHibernate<SdkSlotForPc, Long> implements SdkSlotForPcDao {

    @Autowired
    public SdkSlotForPcDaoImpl(@Qualifier("eadb1SessionFactory")SessionFactory sessionFactory) {
        super(SdkSlotForPc.class, sessionFactory);
    }

    @Override
    public SdkSlotForPc get(long id) {
        return super.get(id);
    }

    @Override
    public List<SdkSlotForPc> getAll() {
        return super.getAll();
    }
}
