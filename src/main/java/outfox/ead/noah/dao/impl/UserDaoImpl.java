package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.annotation.AntiXSS;
import outfox.ead.noah.dao.UserDao;
import outfox.ead.noah.entity.User;

import java.util.Calendar;
import java.util.List;

/**
 * Created by huanghuan on 16/4/25.
 */
@Repository
public class UserDaoImpl extends GenericDaoHibernate<User, Long> implements UserDao {

    @Autowired
    public UserDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(User.class, sessionFactory);
    }

    @Override
    @AntiXSS
    public void saveOrUpdateUser(User user) {
        if (user.getCreateTime() == null) {
            user.setCreateTime(Calendar.getInstance().getTimeInMillis());
        }
        user.setLastModTime(Calendar.getInstance().getTimeInMillis());
        saveOrUpdate(user);
    }

    @Override
    public User getUser(String name) {
        if (name == null) {
            return null;
        }
        DetachedCriteria criteria = DetachedCriteria.forClass(User.class);
        criteria.add(Restrictions.eq("name", name).ignoreCase());
        return findUniqueByDetachedCriteria(criteria);
    }

    @Override
    public User getUser(long id) {
        return get(id);
    }

    @Override
    public boolean isUserNameExists(String name) {
        if (name == null) {
            return false;
        }
        DetachedCriteria criteria = DetachedCriteria.forClass(User.class);
        criteria.add(Restrictions.eq("name", name).ignoreCase());
        List list = findByDetachedCriteria(criteria);
        if (list == null || list.size() < 1) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public void updateUserStatus(long id, int status) {
        User user = get(id);
        user.setStatus(status);
        update(user);
    }
}
