package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.ImageTemplateConfigurationDao;
import outfox.ead.noah.entity.ImageTemplateConfiguration;
import outfox.ead.noah.util.params.ImageTemplateConfigurationParams;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/2.
 */
@Repository
public class ImageTemplateConfigurationDaoImpl extends GenericDaoHibernate<ImageTemplateConfiguration, Long> implements ImageTemplateConfigurationDao {
    @Autowired
    public ImageTemplateConfigurationDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(ImageTemplateConfiguration.class, sessionFactory);
    }

    @Override
    public void saveConfiguration(ImageTemplateConfiguration configuration) {
        super.save(configuration);
    }

    @Override
    public ImageTemplateConfiguration getByTemplateId(Long templateId) {
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(ImageTemplateConfiguration.class);
        detachedCriteria.add(Restrictions.eq(ImageTemplateConfigurationParams.TEMPLATE_ID, templateId));
        return findUniqueByDetachedCriteria(detachedCriteria);
    }

    @Override
    public List<ImageTemplateConfiguration> getByTemplateIds(List<Long> templateIds) {
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(ImageTemplateConfiguration.class);
        detachedCriteria.add(Restrictions.in(ImageTemplateConfigurationParams.TEMPLATE_ID, templateIds));
        return findByDetachedCriteria(detachedCriteria);
    }
}
