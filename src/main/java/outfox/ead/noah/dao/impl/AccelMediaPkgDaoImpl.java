package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.AccelMediaPkgDao;
import outfox.ead.noah.entity.AccelMediaPkg;

import java.util.List;

import static outfox.ead.noah.entity.AccelMediaPkg.*;

/**
 * <AUTHOR>
 * @date 2023/11/2
 */
@Repository
public class AccelMediaPkgDaoImpl extends GenericDaoHibernate<AccelMediaPkg, String> implements AccelMediaPkgDao {
    @Autowired
    public AccelMediaPkgDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(AccelMediaPkg.class, sessionFactory);
    }


    @Override
    public List<AccelMediaPkg> getAllEnableMediaPkg() {
        DetachedCriteria criteria = DetachedCriteria.forClass(AccelMediaPkg.class);
        criteria.add(Restrictions.eq(ENABLE, true));
        return findByDetachedCriteria(criteria);
    }
}
