package outfox.ead.noah.dao.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.DictCommunityDao;
import outfox.ead.noah.entity.DictCommunity;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/5/20.
 */
@Repository
public class DictCommunityDaoImpl extends GenericDaoHibernate<DictCommunity, Integer> implements DictCommunityDao {

    @Autowired
    public DictCommunityDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(DictCommunity.class, sessionFactory);
    }

    @Override
    public List<DictCommunity> getDictCommunities(Set<Integer> communityIds) {
        DetachedCriteria criteria = DetachedCriteria.forClass(DictCommunity.class);
        if (CollectionUtils.isNotEmpty(communityIds)) {
            criteria.add(Restrictions.in("id", communityIds));
        }
        criteria.add(Restrictions.eq("status", 1));
        criteria.addOrder(Order.asc("displayOrder"));
        return findByDetachedCriteria(criteria);
    }


}
