package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import outfox.ead.noah.dao.TemplateElementMappingDao;
import outfox.ead.noah.entity.TemplateElementMapping;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Repository
public class TemplateElementMappingDaoImpl extends GenericDaoHibernate<TemplateElementMapping, Long> implements TemplateElementMappingDao {
    @Autowired
    public TemplateElementMappingDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(TemplateElementMapping.class, sessionFactory);
    }

    @Override
    public List<TemplateElementMapping> getListByTemplateIds(Collection<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        DetachedCriteria criteria = buildDetachedCritera();
        criteria.add(Restrictions.in(TemplateElementMapping.PROPERTYNAME_TEMPLATE_ELEMENT_ID, templateIds));
        return findByDetachedCriteria(criteria);
    }

}