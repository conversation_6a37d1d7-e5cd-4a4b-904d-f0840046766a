package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.CertificateDao;
import outfox.ead.noah.entity.Certificate;

import java.util.List;

@Repository
public class CertificateDaoImpl extends GenericDaoHibernate<Certificate, Long> implements CertificateDao {

    @Autowired
    public CertificateDaoImpl(@Qualifier("auditSessionFactory") SessionFactory sessionFactory) {
        super(Certificate.class, sessionFactory);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Certificate> listBySponsorId(long sponsorId) {
        DetachedCriteria dc=DetachedCriteria.forClass(Certificate.class);
        dc.add(Restrictions.eq("sponsorId", sponsorId));
        dc.add(Restrictions.eq("isDel", 0));
        dc.addOrder(Order.asc("submitDate"));
        return findByDetachedCriteria(dc);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Certificate getCertificate(long certId) {
        DetachedCriteria dc=DetachedCriteria.forClass(Certificate.class);
        dc.add(Restrictions.eq("certId", certId));
        List<Certificate> rs = findPageByDetachedCriteria(dc, 0, 1);
        if(rs.size()==1)
            return rs.get(0);
        return null;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Certificate getCertificate(long sponsorId, long certId) {
        DetachedCriteria dc=DetachedCriteria.forClass(Certificate.class);
        dc.add(Restrictions.eq("certId", certId)).add(Restrictions.eq("sponsorId", sponsorId));
        List<Certificate> rs=findPageByDetachedCriteria(dc, 0, 1);
        if(rs.size()==1)
            return rs.get(0);
        return null;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Certificate> listBySponsorId(long sponsorId, int status) {
        DetachedCriteria dc=DetachedCriteria.forClass(Certificate.class);
        dc.add(Restrictions.eq("sponsorId", sponsorId)).add(Restrictions.eq("auditStatus", status));
        dc.add(Restrictions.eq("isDel", 0));
        dc.addOrder(Order.asc("submitDate"));
        return findByDetachedCriteria(dc);
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<Certificate> listBySponsorId(long sponsorId, Integer[] status) {
        DetachedCriteria dc=DetachedCriteria.forClass(Certificate.class);
        dc.add(Restrictions.eq("sponsorId", sponsorId)).add(Restrictions.in("auditStatus", status));
        dc.add(Restrictions.eq("isDel", 0));
        dc.addOrder(Order.asc("submitDate"));
        return findByDetachedCriteria(dc);
    }

    @Override
    public void saveOrUpdate(Certificate cert) {
        super.saveOrUpdate(cert);
        
    }

    @Override
    public void delete(Certificate cert) {
       super.remove(cert.getCertId());
    }

}