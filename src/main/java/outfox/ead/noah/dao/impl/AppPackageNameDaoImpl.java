package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.AppPackageNameDao;
import outfox.ead.noah.entity.AppPackageName;

import java.util.Collection;
import java.util.List;

@Repository
public class AppPackageNameDaoImpl extends GenericDaoHibernate<AppPackageName, Long> implements AppPackageNameDao {

    @Autowired
    public AppPackageNameDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(AppPackageName.class, sessionFactory);
    }

    @Override
    public List<AppPackageName> getListByKeyword(String keyword) {
        DetachedCriteria criteria = buildDetachedCritera();
        criteria.add(Restrictions.like("appName", keyword, MatchMode.ANYWHERE));
        return findByDetachedCriteria(criteria);
    }

    @Override
    public List<AppPackageName> getListByIds(Collection<Long> ids) {
        DetachedCriteria criteria = buildDetachedCritera();
        criteria.add(Restrictions.in("id", ids));
        return findByDetachedCriteria(criteria);
    }

    @Override
    public List<AppPackageName> getListByPackageNames(Collection<String> packageNames) {
        DetachedCriteria criteria = buildDetachedCritera();
        criteria.add(Restrictions.in(AppPackageName.PROPERTYNAME_PACKAGE_NAME, packageNames));
        return findByDetachedCriteria(criteria);
    }
}
