package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.FeedbackMessageDao;
import outfox.ead.noah.entity.FeedbackMessage;

/**
 * 描述:
 * feedbackDao
 * <p>
 * created by lishengle 2018-09-25 20:57
 */
@Repository
public class FeedbackMessageDaoImpl extends GenericDaoHibernate implements FeedbackMessageDao {

    @Autowired
    public FeedbackMessageDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(FeedbackMessage.class, sessionFactory);
    }

    @Override
    public void saveFeedbackMessage(FeedbackMessage feedbackMessage) {
        save(feedbackMessage);
    }
}
