package outfox.ead.noah.dao.impl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.hibernate.transform.Transformers;
import org.hibernate.type.LongType;
import org.hibernate.type.StringType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.ImageLibraryTagsMappingDao;
import outfox.ead.noah.entity.ImageLibraryTagsMapping;
import outfox.ead.noah.entity.ImageTemplate;
import outfox.ead.noah.entity.ImageTemplateTag;
import outfox.ead.noah.entity.models.materiallibrary.TagItem;
import outfox.ead.noah.entity.models.materiallibrary.TagItemWithSub;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class ImageLibraryTagsMappingDaoImpl extends GenericDaoHibernate<ImageLibraryTagsMapping, Long> implements ImageLibraryTagsMappingDao {

    @Autowired
    public ImageLibraryTagsMappingDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(ImageLibraryTagsMapping.class, sessionFactory);
    }

    @Override
    public List<ImageTemplateTag> getAllSecLayer(Long sponsorId) {
        String sql = "select DISTINCT itt.ID        as id,\n" +
                "                itt.tag_name  as tagName,\n" +
                "                itt.parent_id as parentId\n" +
                "from ImageLibraryTagsMapping iltm\n" +
                "         left join ImageLibrary il on il.ID = iltm.image_id\n" +
                "         left join ImageTemplateTag itt on iltm.tag_id = itt.ID\n" +
                "where il.SPONSOR_ID = :sponsorId";
        Query query = getSession().createSQLQuery(sql)
                .addScalar("id", LongType.INSTANCE)
                .addScalar("tagName", StringType.INSTANCE)
                .addScalar("parentId", LongType.INSTANCE)
                .setResultTransformer(Transformers.aliasToBean(ImageTemplateTag.class));
        query.setParameter("sponsorId", sponsorId);
        return query.list();
    }

    @Override
    public Serializable save(ImageLibraryTagsMapping object) {
        return super.save(object);
    }

    @Override
    public void batchSave(List<ImageLibraryTagsMapping> imageLibraryTagsMappingList){
        batchInsertOrUpdate(imageLibraryTagsMappingList);
    }

    @Override
    public List<ImageLibraryTagsMapping> getTagsMappingByImageIds(List<Long> imageIds) {
        if (CollectionUtils.isEmpty(imageIds)){
            return Collections.emptyList();
        }
        DetachedCriteria criteria = DetachedCriteria.forClass(ImageLibraryTagsMapping.class);
        criteria.add(Restrictions.in("imageId", imageIds));
        return findByDetachedCriteria(criteria);
    }

    @Override
    public void removeByImageIds(List<Long> imageIds){
        if (CollectionUtils.isEmpty(imageIds)){
            return;
        }
        String sql = "delete from ImageLibraryTagsMapping where image_id in :imageIds";
        Query query = getSession().createSQLQuery(sql);
        query.setParameterList("imageIds", imageIds);
        query.executeUpdate();
    }
}
