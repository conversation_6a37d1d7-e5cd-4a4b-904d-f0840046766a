package outfox.ead.noah.dao.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.noah.constants.Constants;
import outfox.ead.noah.dao.ScenarioAppDao;
import outfox.ead.noah.dao.SdkAppInfoDao;
import outfox.ead.noah.dao.SdkSlotDao;
import outfox.ead.noah.entity.SdkAppInfo;

import java.util.*;
import java.util.stream.Collectors;

import static outfox.ead.noah.entity.SdkAppInfo.PROPERTYNAME_SDK_APP_FLOW_AMOUNT;
import static outfox.ead.noah.util.datautil.DataUtil.toLongs;

/**
 * Created by zhaoteng on 2017/5/27.
 */
@Repository
public class SdkAppInfoDaoImpl extends GenericDaoHibernate<SdkAppInfo, Long> implements SdkAppInfoDao {

    @Autowired
    private SdkSlotDao sdkSlotDao;

    @Autowired
    private ScenarioAppDao scenarioAppDao;

    @Autowired
    public SdkAppInfoDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(SdkAppInfo.class, sessionFactory);
    }

    @Override
    public SdkAppInfo getById(long appInfoId) {
        return get(appInfoId);
    }

    @Transactional
    @Override
    public List<SdkAppInfo> getSdkAppInfosByCondition(Set<Long> sdkAppIds, String keyword, int osType, List<Integer> flowType,
                                                      List<Integer> categories, Long minAmount, Long maxAmount, List<String> sdkSlotUdids,
                                                      Boolean isPcSlot, String scenarioIds, int position, int length) {
        List<Long> ids = constructSdkAppInfoRestrictions(sdkAppIds, keyword, osType, flowType, categories,
                minAmount, maxAmount, sdkSlotUdids, isPcSlot, scenarioIds, position, length, Constants.ORDER_TYPE_DESC, false);
        List<SdkAppInfo> result = getByIds(new HashSet<>(ids));
        result.sort((o1, o2) -> o1.getSdkAppId() < o2.getSdkAppId() ? 1 : -1);
        return result;
    }

    @Transactional
    @Override
    public long getSdkAppInfosCount(Set<Long> sdkAppIds, String keyword, int osType, List<Integer> flowType, List<Integer> categories,
                                    Long minAmount, Long maxAmount, List<String> sdkSlotUdids, Boolean isPcSlot, String scenarioIds) {
        List result = constructSdkAppInfoRestrictions(sdkAppIds, keyword, osType, flowType, categories,
                minAmount, maxAmount, sdkSlotUdids, isPcSlot, scenarioIds, -1, -1, Constants.ORDER_TYPE_DESC, true);

        if (result.size() > 0 && result.get(0) instanceof Number) {
            return ((Number) result.get(0)).longValue();
        }

        return 0;
    }

    public List<SdkAppInfo> getByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        DetachedCriteria criteria = buildDetachedCritera();
        criteria.add(Restrictions.in("sdkAppId", ids));
        return findByDetachedCriteria(criteria);
    }

    private List<Long> constructSdkAppInfoRestrictions(Set<Long> sdkAppIds, String keyword, int osType, List<Integer> flowType,
                                                       List<Integer> categories, Long minAmount, Long maxAmount, List<String> sdkSlotUdids,
                                                       Boolean isPcSlot, String scenarioIds, int position, int length, int orderType, boolean count) {
        final Criteria criteria = this.getSession().createCriteria(SdkAppInfo.class);
        try {
            if (count) {
                criteria.setProjection(Projections.countDistinct(SdkAppInfo.PROPERTYNAME_SDK_APP_ID));
            } else {
                criteria.setProjection(Projections.projectionList().add(Projections.groupProperty(SdkAppInfo.PROPERTYNAME_SDK_APP_ID)));
                criteria.setProjection(Projections.distinct(Projections.id()));
            }
            criteria.add(Restrictions.eq(SdkAppInfo.PROPERTYNAME_SDK_APP_STATUS, Constants.COMMON_STATUS_NORMAL));
            List<Long> validAppIds = sdkSlotDao.getValidAppIds(isPcSlot, sdkSlotUdids).get();
            criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_ID, validAppIds));
            if (!CollectionUtils.isEmpty(sdkAppIds)) {
                criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_ID, sdkAppIds));
            }
            if (!StringUtils.isEmpty(keyword)) {
                List<Long> appIdsBySlotNameKeyword = sdkSlotDao.getAppIdsBySlotNameKeyword(keyword);
                if (CollectionUtils.isNotEmpty(appIdsBySlotNameKeyword)) {
                    criteria.add(Restrictions.or(Restrictions.like(SdkAppInfo.PROPERTYNAME_SDK_APP_NAME, keyword, MatchMode.ANYWHERE),
                            Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_ID, sdkSlotDao.getAppIdsBySlotNameKeyword(keyword))));
                } else {
                    criteria.add(Restrictions.like(SdkAppInfo.PROPERTYNAME_SDK_APP_NAME, keyword, MatchMode.ANYWHERE));
                }
            }
            if (osType == 0 || osType == 1) {
                criteria.add(Restrictions.eq(SdkAppInfo.PROPERTYNAME_SDK_APP_OS_TYPE, osType));
            }
            if (!flowType.isEmpty()) {
                criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SYN_ID, flowType));
            }
            if (!CollectionUtils.isEmpty(categories)) {
                criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_CATEGORY, categories));
            }
            if ((minAmount >= 0) && (maxAmount >= 0) && (maxAmount >= minAmount)) {
                criteria.add(Restrictions.between(PROPERTYNAME_SDK_APP_FLOW_AMOUNT, minAmount, maxAmount));
            }
            if (StringUtils.isNotEmpty(scenarioIds)) {
                Set<Long> scenarioIdsInLong = toLongs(scenarioIds);
                criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_ID, scenarioAppDao.getAppIdByScenarioIds(scenarioIdsInLong)));
            }
            if (orderType == Constants.ORDER_TYPE_DESC) {
                criteria.addOrder(Order.desc(SdkAppInfo.PROPERTYNAME_SDK_APP_ID));
            } else if (orderType == Constants.ORDER_TYPE_ASC) {
                criteria.addOrder(Order.asc(SdkAppInfo.PROPERTYNAME_SDK_APP_ID));
            }
            if (position >= 0 && length > 0) {
                criteria.setFirstResult(position);
                criteria.setMaxResults(length);
            }
            return (List<Long>) criteria.list();
        } catch (Exception e) {
            log.error("Async Execution Failed", e);
            return Collections.emptyList();
        }
    }

    @Override
    public long getSdkAppInfoCountByIdFlowOSType(Set<Long> ids, List<Integer> flowType, int osType) {
        DetachedCriteria criteria = buildDetachedCritera();
        criteria.add(Restrictions.eq(SdkAppInfo.PROPERTYNAME_SDK_APP_STATUS, 0));
        if (!CollectionUtils.isEmpty(ids)) {
            criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_ID, ids));
        }
        criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SYN_ID, flowType));
        if (osType >= 0) {
            criteria.add(Restrictions.eq(SdkAppInfo.PROPERTYNAME_SDK_APP_OS_TYPE, osType));
        }
        criteria.setProjection(Projections.rowCount());
        Object count = findByDetachedCriteria(criteria).get(0);
        if (count instanceof Long) {
            return (Long) count;
        }
        return 0;
    }

    @Override
    public List<SdkAppInfo> getListByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        DetachedCriteria criteria = buildDetachedCritera();
        criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_ID, ids));
        return findByDetachedCriteria(criteria);
    }

    @Override
    public Map<Long, SdkAppInfo> getMapByIds(Collection<Long> ids) {
        List<SdkAppInfo> sdkAppInfos = getListByIds(ids);
        if (CollectionUtils.isEmpty(sdkAppInfos)) {
            return Collections.emptyMap();
        }
        return sdkAppInfos.stream().collect(Collectors.toMap(SdkAppInfo::getSdkAppId, a -> a));
    }

    @Override
    public List<SdkAppInfo> getSdkAppInfosByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        DetachedCriteria query = DetachedCriteria.forClass(SdkAppInfo.class);
        Criterion criterion = Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_ID, ids);
        query.add(criterion);
        return findByDetachedCriteria(query);
    }

    @Override
    public List getByDeveloperId(Long developerId) {
        DetachedCriteria query = DetachedCriteria.forClass(SdkAppInfo.class);
        Criterion criterion = Restrictions.eq(SdkAppInfo.PROPERTYNAME_SDK_APP_DEVELOPER_ID, developerId);
        query.add(criterion);
        List<SdkAppInfo> result = findByDetachedCriteria(query);
        return result;
    }

    @Override
    public void saveOrUpdateSdkAppInfo(SdkAppInfo sdkAppInfo) {
        if (sdkAppInfo.getCreateTime() == null) {
            sdkAppInfo.setCreateTime(Calendar.getInstance().getTimeInMillis());
        }
        sdkAppInfo.setLastModTime(Calendar.getInstance().getTimeInMillis());
        saveOrUpdate(sdkAppInfo);
    }

    @Override
    public List<SdkAppInfo> getValidSdkAppByConditions(Set<Integer> categoryIds,
                                                       Integer sdkAppOSType,
                                                       Integer mediaType) {
        DetachedCriteria criteria = DetachedCriteria.forClass(SdkAppInfo.class);
        criteria.add(Restrictions.eq(SdkAppInfo.PROPERTYNAME_SDK_APP_STATUS, Constants.COMMON_STATUS_NORMAL));
        if (Objects.nonNull(categoryIds)) {
            if (CollectionUtils.isEmpty(categoryIds)) {
                return Collections.emptyList();
            }
            criteria.add(Restrictions.in(SdkAppInfo.PROPERTYNAME_SDK_APP_CATEGORY, categoryIds));
        }
        if (Objects.nonNull(sdkAppOSType)) {
            criteria.add(Restrictions.eq(SdkAppInfo.PROPERTYNAME_SDK_APP_OS_TYPE, sdkAppOSType));
        }
        criteria.add(Restrictions.eq(SdkAppInfo.PROPERTYNAME_TRAFFIC_ALLOCATE_MEDIA_TYPE, mediaType));
        return findByDetachedCriteria(criteria);
    }
}
