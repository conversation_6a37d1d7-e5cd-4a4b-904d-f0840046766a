package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.ImageLibraryGroupDao;
import outfox.ead.noah.entity.ImageLibraryGroup;

import java.util.List;
import java.util.Set;

import static outfox.ead.noah.entity.ImageLibraryGroup.*;
import static outfox.ead.noah.util.code.NameCode.SPONSOR_ID;

@Repository
public class ImageLibraryGroupDaoImpl extends GenericDaoHibernate<ImageLibraryGroup, Long> implements ImageLibraryGroupDao {
    @Autowired
    public ImageLibraryGroupDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(ImageLibraryGroup.class, sessionFactory);
    }

    @Override
    public ImageLibraryGroup saveImageLibraryGroup(ImageLibraryGroup group) {
        super.saveOrUpdate(group);
        return group;
    }

    @Override
    public void deleteImageGroups(Long sponsorId, Set<Long> imageGroupIds) {
        removeAll(getByImageGroupIds(sponsorId, imageGroupIds));
    }

    @Override
    public ImageLibraryGroup getByImageGroupId(Long imageGroupId) {
        return super.get(imageGroupId);
    }

    @Override
    public List<ImageLibraryGroup> getByImageGroupIds(Long sponsorId, Set<Long> imageGroupIds) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ImageLibraryGroup.class);
        criteria.add(Restrictions.eq(SPONSOR_ID, sponsorId))
                .add(Restrictions.in(ImageLibraryGroup.IMAGE_GROUP_ID_STR, imageGroupIds));
        return findByDetachedCriteria(criteria);
    }

    @Override
    public List<ImageLibraryGroup> getAllBySponsorId(Long sponsorId) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ImageLibraryGroup.class);
        criteria.add(Restrictions.eq(SPONSOR_ID, sponsorId))
                .addOrder(Order.asc(CREATE_TIME_STR));
        return findByDetachedCriteria(criteria);
    }

    @Override
    public ImageLibraryGroup getOne(Long sponsorId, String imageGroupName) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ImageLibraryGroup.class);
        criteria.add(Restrictions.eq(SPONSOR_ID, sponsorId))
                .add(Restrictions.eq(IMAGE_GROUP_NAME,imageGroupName));
        return findUniqueByDetachedCriteria(criteria);
    }
}
