package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import outfox.ead.noah.dao.MessageContentDao;
import outfox.ead.noah.entity.MessageContent;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by yuanzhch on 2017/9/8.
 */
@Repository
public class MessageContentDaoImpl extends GenericDaoHibernate<MessageContent, Long> implements MessageContentDao {
    @Autowired
    public MessageContentDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(MessageContent.class, sessionFactory);
    }

    public MessageContent getMessageContent(Long menssageId) {
        return get(menssageId);
    }

    public MessageContent getMessageContentByTemplateId(int messageTemplateId) {
        StringBuffer sql = new StringBuffer(
                " from MessageContent where template = :messageTemplateId");
        List list = getSession().createQuery(sql.toString()).setInteger(
                "messageTemplateId", messageTemplateId).list();
        if (list.size() == 1) {
            return (MessageContent) list.get(0);
        }
        return null;
    }

    @Override
    public List<MessageContent> getListByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        DetachedCriteria criteria = buildDetachedCritera();
        criteria.add(Restrictions.in("messageId", ids));
        return findByDetachedCriteria(criteria);
    }

}
