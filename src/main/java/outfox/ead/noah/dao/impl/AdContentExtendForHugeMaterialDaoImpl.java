package outfox.ead.noah.dao.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.AdContentExtendForHugeMaterialDao;
import outfox.ead.noah.entity.AdContentExtendForHugeMaterial;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class AdContentExtendForHugeMaterialDaoImpl extends GenericDaoHibernate<AdContentExtendForHugeMaterial, Long> implements AdContentExtendForHugeMaterialDao {

    @Autowired
    public AdContentExtendForHugeMaterialDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(AdContentExtendForHugeMaterial.class, sessionFactory);
    }

    @Override
    public List<AdContentExtendForHugeMaterial> findByAdContentId(Long adContentId) {
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(AdContentExtendForHugeMaterial.class);
        Criterion criterion = Restrictions.eq("adContentId", adContentId);
        detachedCriteria.add(criterion);
        return findByDetachedCriteria(detachedCriteria);
    }

    @Override
    public List<AdContentExtendForHugeMaterial> findByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(AdContentExtendForHugeMaterial.class);
        Criterion criterion = Restrictions.in("id", ids);
        detachedCriteria.add(criterion);
        return findByDetachedCriteria(detachedCriteria);
    }
}
