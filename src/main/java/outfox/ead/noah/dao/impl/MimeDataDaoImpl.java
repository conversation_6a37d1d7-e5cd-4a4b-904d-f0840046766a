package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.MimeDataDao;
import outfox.ead.noah.entity.MimeData;

/**
 * Created by yuanzhch on 2017/6/27.
 */
@Repository
public class MimeDataDaoImpl extends GenericDaoHibernate<MimeData, String> implements MimeDataDao {
    @Autowired
    public MimeDataDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(MimeData.class, sessionFactory);
    }

    @Override
    public void insert(MimeData mimeData) {
        super.save(mimeData);
    }
}
