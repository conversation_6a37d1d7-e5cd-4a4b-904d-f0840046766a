package outfox.ead.noah.dao.impl;

import org.apache.commons.lang.StringUtils;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.CustomizedTagSettingsDao;
import outfox.ead.noah.entity.CustomizedTagSettings;
import outfox.ead.noah.util.StringUtil;
import outfox.ead.noah.util.datautil.DataUtil;
import outfox.ead.noah.util.params.CustomizedTagParams;

import java.util.Collections;
import java.util.List;

/**
 * @Date 2019/3/4 21:14
 * <AUTHOR>
 * @Description
 **/
@Repository
public class CustomizedTagSettingsDaoImpl extends GenericDaoHibernate<CustomizedTagSettings, Long> implements CustomizedTagSettingsDao {

    @Autowired
    public CustomizedTagSettingsDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(CustomizedTagSettings.class, sessionFactory);
    }

    @Override
    public void saveOrUpdateCustomizedTagSettings(CustomizedTagSettings customizedTagSettings) {
        saveOrUpdate(customizedTagSettings);
    }

    @Override
    public List<CustomizedTagSettings> getCustomizedTagSettingsByTagId(Long tagId) {
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(CustomizedTagSettings.class);
        detachedCriteria.add(Restrictions.eq(CustomizedTagParams.TAG_ID, tagId));
        return findByDetachedCriteria(detachedCriteria);
    }

    @Override
    public List<CustomizedTagSettings> getCustomizedTagSettingsByTagIds(String tagIds) {
        if (StringUtils.isBlank(tagIds)) {
            return Collections.emptyList();
        }
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(CustomizedTagSettings.class);
        detachedCriteria.add(Restrictions.in(CustomizedTagParams.TAG_ID, DataUtil.toLongList(tagIds)));
        return findByDetachedCriteria(detachedCriteria);
    }

    @Override
    public void delByTagId(Long tagId) {
        super.removeAll(getCustomizedTagSettingsByTagId(tagId));
    }
}
