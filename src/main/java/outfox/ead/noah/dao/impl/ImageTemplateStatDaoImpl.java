package outfox.ead.noah.dao.impl;

import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.ImageTemplateStatDao;
import outfox.ead.noah.entity.ImageTemplateStat;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/6.
 */
@Repository
public class ImageTemplateStatDaoImpl extends GenericDaoHibernate<ImageTemplateStat, Long> implements ImageTemplateStatDao {
    @Autowired
    public ImageTemplateStatDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(ImageTemplateStat.class, sessionFactory);
    }

    public void saveOrUpdate(List<ImageTemplateStat> templateStatList) {
        batchInsertOrUpdate(templateStatList);
    }
}
