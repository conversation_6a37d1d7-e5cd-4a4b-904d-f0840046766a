package outfox.ead.noah.dao.impl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import outfox.ead.noah.dao.ThirdPartPromotionInfoDao;
import outfox.ead.noah.entity.ThirdPartPromotionInfo;
import outfox.ead.noah.job.config.alg.AlgConfigManager;
import outfox.ead.noah.util.code.StatusCode;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static outfox.ead.noah.druid.DruidBuilder.DruidCol.CONVERT_ID;
import static outfox.ead.noah.entity.ThirdPartPromotionInfo.*;
import static outfox.ead.noah.util.code.SourceCode.FROM_ZHIXUAN;

/**
 * 描述:
 * 第三方推广活动DAO实现类
 *
 * <AUTHOR>
 * @create 2018-07-20 20:38
 */
@Repository
public class ThirdPartPromotionInfoDaoImpl
        extends GenericDaoHibernate<ThirdPartPromotionInfo, Long> implements ThirdPartPromotionInfoDao {

    @Autowired
    private AlgConfigManager algConfigManager;

    @Autowired
    public ThirdPartPromotionInfoDaoImpl(@Qualifier("eadb1SessionFactory") SessionFactory sessionFactory) {
        super(ThirdPartPromotionInfo.class, sessionFactory);
    }

    @Override
    public List<ThirdPartPromotionInfo> getThirdPartPromotionListByKeyword(Long sponsorId,
                                                                           Integer offset,
                                                                           Integer limit,
                                                                           String keyword) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ThirdPartPromotionInfo.class);
        criteria.add(Restrictions.eq(THIRD_PART_SPONSOR_ID, sponsorId));
        if (StringUtils.isNotEmpty(keyword)) {
            criteria.add(Restrictions.like(PROMOTION_NAME, "%" + keyword + "%"));
        }
        criteria.add(Restrictions.eq(SOURCE, FROM_ZHIXUAN));
        criteria.add(Restrictions.eq(PROMOTION_STATUS, StatusCode.NOT_DELETE_STATUS));
        criteria.addOrder(Order.desc("createTime"));
        if (offset != null && limit != null) {
            return findPageByDetachedCriteria(criteria, offset, limit);
        } else {
            return findByDetachedCriteria(criteria);
        }
    }

    @Override
    public ThirdPartPromotionInfo getThirdPartPromotionInfoByUID(Long sponsorId, String promotionUID) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ThirdPartPromotionInfo.class);
        criteria.add(Restrictions.eq(SOURCE, FROM_ZHIXUAN));
        criteria.add(Restrictions.eq(THIRD_PART_SPONSOR_ID, sponsorId));
        criteria.add(Restrictions.eq(PROMOTION_UID, promotionUID));
        criteria.add(Restrictions.eq(PROMOTION_STATUS, StatusCode.NOT_DELETE_STATUS));
        return findUniqueByDetachedCriteria(criteria);
    }

    @Override
    public void updateThirdPartPromotionInfo(ThirdPartPromotionInfo tppInfo) {
        tppInfo.setLastModTime(new Timestamp(System.currentTimeMillis()));
        super.update(tppInfo);
    }

    @Override
    public Boolean isPromotionNameUsed(Long sponsorId, String promotionName, String promotionUID) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ThirdPartPromotionInfo.class);
        criteria.add(Restrictions.eq(THIRD_PART_SPONSOR_ID, sponsorId));
        criteria.add(Restrictions.eq(PROMOTION_NAME, promotionName));
        if (StringUtils.isNotBlank(promotionUID)) {
            criteria.add(Restrictions.ne(PROMOTION_UID, promotionUID));
        }
        criteria.add(Restrictions.eq(PROMOTION_STATUS, StatusCode.NOT_DELETE_STATUS));
        return CollectionUtils.isNotEmpty(findByDetachedCriteria(criteria));
    }

    @Override
    public Set<String> getTdpCreativeIdsForTdpPlatformAndPromotionUidNotEqual(Integer tdpPlatform, String promotionUID) {
        List<ThirdPartPromotionInfo> infos = buildDetachedCriteriaAndExecute(tdpPlatform, promotionUID,
                Collections.singletonList(Restrictions.isNotNull(TDP_CREATIVE_IDS)));
        return infos.stream().flatMap(x-> Arrays.stream(x.getTdpCreativeIds().split(","))).collect(Collectors.toSet());
    }

    @Override
    public Set<String> getTdpSponsorIdsForTdpPlatformAndPromotionUidNotEqual(Integer tdpPlatform, String promotionUID) {
        List<ThirdPartPromotionInfo> infos = buildDetachedCriteriaAndExecute(tdpPlatform, promotionUID,
                Collections.singletonList(Restrictions.isNotNull(TDP_SPONSOR_IDS)));
        return infos.stream().flatMap(x-> Arrays.stream(x.getTdpSponsorIds().split(","))).collect(Collectors.toSet());
    }

    private List<ThirdPartPromotionInfo> buildDetachedCriteriaAndExecute(Integer tdpPlatform, String promotionUID, List<Criterion> otherCriterions) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ThirdPartPromotionInfo.class);
        criteria.add(Restrictions.eq(TDP_PLATFORM, tdpPlatform));
        if (promotionUID != null) {
            criteria.add(Restrictions.ne(PROMOTION_UID, promotionUID));
        }
        if (CollectionUtils.isNotEmpty(otherCriterions)) {
            otherCriterions.forEach(criteria::add);
        }
        criteria.add(Restrictions.eq(PROMOTION_STATUS, StatusCode.NOT_DELETE_STATUS));
        return findByDetachedCriteria(criteria);
    }

    @Override
    public List<ThirdPartPromotionInfo> getThirdPartPromotionListByStrategy(int strategy) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ThirdPartPromotionInfo.class);
        criteria.add(Restrictions.eq(STRATEGY, strategy));
        criteria.add(Restrictions.eq(PROMOTION_STATUS, StatusCode.NOT_DELETE_STATUS));
        criteria.add(Restrictions.eq(OPERATE_STATUS, StatusCode.OPERATE_STATUS_OPEN));
        List<Long> unableSponsorIds = algConfigManager.getAlgDataConfig().getUnableSponsorIds();
        if (CollectionUtils.isNotEmpty(unableSponsorIds)) {
            criteria.add(Restrictions.not(Restrictions.in(THIRD_PART_SPONSOR_ID, unableSponsorIds)));
        }
        return findByDetachedCriteria(criteria);
    }

    @Override
    public List<ThirdPartPromotionInfo> getThirdPartPromotionListByConvertId(String convertId) {
        DetachedCriteria criteria = DetachedCriteria.forClass(ThirdPartPromotionInfo.class);
        criteria.add(Restrictions.eq(CONVERT_TRACKING_UID, convertId));
        return findByDetachedCriteria(criteria);
    }

}
