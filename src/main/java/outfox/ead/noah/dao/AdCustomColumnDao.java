package outfox.ead.noah.dao;

import outfox.ead.noah.entity.AdCustomColumn;

/**
 * 自定义列
 *
 * <AUTHOR>
 **/
public interface AdCustomColumnDao {

    /**
     * 根据账号id和类型获取自定义列数据
     * @param sponsorId 账号id
     * @param customColumnModule 自定义列的应用模块，1表示推广管理，2表示数据报表
     * @return
     */
    AdCustomColumn getBySponsorIdAndModule(Long sponsorId, Integer customColumnModule);

    void saveOrUpdate(AdCustomColumn adCustomColumn);
}
