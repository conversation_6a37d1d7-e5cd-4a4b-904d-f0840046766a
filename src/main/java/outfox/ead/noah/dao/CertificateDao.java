package outfox.ead.noah.dao;

import outfox.ead.noah.entity.Certificate;

import java.util.List;

public interface CertificateDao {

    List<Certificate> listBySponsorId(long sponsorId);
    
    Certificate getCertificate(long cert_id);
    
    Certificate getCertificate(long sponsorId,long cert_id);
    
    void saveOrUpdate(Certificate cert);
    void delete( Certificate cert);
    
    List<Certificate> listBySponsorId(long sponsorId,int status);
    List<Certificate> listBySponsorId(long sponsorId,Integer[] status);
    
}