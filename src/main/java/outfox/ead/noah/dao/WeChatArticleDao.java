package outfox.ead.noah.dao;

import outfox.ead.noah.entity.WeChatArticle;

import java.util.List;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2018/5/22
 */
public interface WeChatArticleDao {

    /**
     * 根据文章Id获取文章信息
     */
    WeChatArticle getArticleById(Long articleId);

    /**
     * 保存文章
     */
    void saveOrUpdate(WeChatArticle weChatArticle);

    /**
     * 获取文章数目
     */
    Integer getWeChatArticleCount(Long sponsorId, String keyword, String startDate, String endDate);

    /**
     * 根据URL创建文章时，根据sponsorId,微信公众号和微信文章名字来判断该条文章是否存在
     */
    boolean doesArticleExist(Long sponsorId, String articleTitle, String weChatName, String articleDate, String nickName);

    /**
     * 分页根据关键词查询
     */
    List<WeChatArticle> getWeChatArticle(Long sponsorId, String keyword, Integer offset, Integer limit, String startDate, String endDate);
}
