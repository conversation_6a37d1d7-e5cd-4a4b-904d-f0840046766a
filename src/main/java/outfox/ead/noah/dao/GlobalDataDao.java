package outfox.ead.noah.dao;

import outfox.ead.noah.entity.AdPlan;
import outfox.ead.noah.entity.Area;
import outfox.ead.noah.entity.IndustryType;
import outfox.ead.noah.entity.OverseasArea;

import java.util.List;
import java.util.Set;

/**
 * 用于查询地理位置，行业等全局信息
 * <p>
 * Created by huanghuan on 16/4/27.
 */
public interface GlobalDataDao {

    /**
     * 获取省份，城市列表
     *
     * @return
     */
    public List<Area> getArea();

    /**
     * 获取行业列表
     *
     * @return
     */
    public List<IndustryType> getIndustryType();

    public List<OverseasArea> getOverseasArea();

}
