package outfox.ead.noah.dao;

import outfox.ead.noah.entity.AdPlan;

import java.util.List;
import java.util.Set;

/**
 * Created by l<PERSON><PERSON> on 16/5/13.
 */
public interface AdPlanDao {

    AdPlan getAdPlanById(Long adPlanId);

    void saveOrUpdateAdPlan(AdPlan adPlan);

    List<AdPlan> getAdPlanListByIds(Set<Long> adPlanIds);

    List<AdPlan> findByNextDayBudgetIsNotNull();

    /**
     * 非空次日预算覆盖今日预算后，次日预算置空
     */
    void nextDayBudgetCoverDayBudget();
}
