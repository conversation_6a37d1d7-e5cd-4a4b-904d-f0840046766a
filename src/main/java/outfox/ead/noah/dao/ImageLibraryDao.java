package outfox.ead.noah.dao;

import outfox.ead.noah.dto.ImageLibraryListDto;
import outfox.ead.noah.dto.ImagesQueryConditions;
import outfox.ead.noah.entity.ImageLibrary;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface ImageLibraryDao {

    /**
     * 保存或更新
     *
     * @param imageLibrary
     */
    void saveOrUpdateImage(ImageLibrary imageLibrary);

    /**
     * 获取图片列表
     *
     * @param mimeIds
     * @param sponsorId
     * @return
     */
    List<ImageLibrary> getImagesByMimeIds(List<String> mimeIds, Long sponsorId);

    /**
     * 根据图片宽高比获取
     *
     * @param sponsorId
     * @param aspectRatios
     * @return
     */
    List<ImageLibrary> getImagesByAspectRatios(Long sponsorId, List<String> aspectRatios);

    /**
     * 批量保存或更新
     *
     * @param images
     */
    void batchInsertOrUpdateImages(Collection<ImageLibrary> images);

    /**
     * 批量获取
     *
     * @param sponsorId
     * @param ids
     * @return
     */
    List<ImageLibrary> getByIds(Long sponsorId, Set<Long> ids);

    /**
     * 根据id获取
     *
     * @param id
     * @return
     */
    ImageLibrary getById(Long id);

    /**
     * 批量删除
     *
     * @param sponsorId
     * @param ids
     */
    void deleteByIds(Long sponsorId, Set<Long> ids);

    /**
     * 根据图片相关细节搜索
     *
     * @param sponsorId
     * @param conditions
     * @return
     */
    List<ImageLibraryListDto> pageByImageDetails(Long sponsorId, ImagesQueryConditions conditions);

    /**
     * 通过mimeid和广告主id获取记录
     *
     * @param mimeId
     * @param sponsorId
     * @return
     */
    ImageLibrary getByMimeIdAndSponsorId(String mimeId, Long sponsorId);
}
