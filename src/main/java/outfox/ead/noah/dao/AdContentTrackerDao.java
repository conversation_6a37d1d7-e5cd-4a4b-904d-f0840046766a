package outfox.ead.noah.dao;

import outfox.ead.noah.entity.AdContentTracker;

import java.util.List;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 18/5/9
 */
public interface AdContentTrackerDao {
    /**
     *根据Id获取第三方检测
     */
    public AdContentTracker getAdContentTracker(Long adContentId, int type);

    /**
     * 保存或者更新第三方检测
     */
    public void saveOrUpdateAdContentTracker(Long adContentId, String link, int type);

    /**
     * 删除第三方检测
     */
    public void deleteAdContentTracker(Long adContentId, int type);

    /**
     * get all adContentTrackers by adContentId,include monitor link and exposure link
     * @param adContentId Long
     * @return list
     */
    List<AdContentTracker> getAllAdContentTracker(Long adContentId);
}
