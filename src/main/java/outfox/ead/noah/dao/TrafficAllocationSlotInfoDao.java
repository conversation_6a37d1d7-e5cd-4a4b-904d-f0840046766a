package outfox.ead.noah.dao;

import outfox.ead.noah.entity.TrafficAllocationSlotInfo;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TrafficAllocationSlotInfoDao {

    void saveAll(Collection<TrafficAllocationSlotInfo> trafficAllocationSlotInfos);

    /**
     * 查询 <= date的记录
     */
    List<TrafficAllocationSlotInfo> findByDateLessThan(LocalDate date);

    void deleteAll(Collection<TrafficAllocationSlotInfo> trafficAllocationSlotInfos);

    List<TrafficAllocationSlotInfo> findLatestPv();

    List<TrafficAllocationSlotInfo> getByDate(LocalDate date);
}
