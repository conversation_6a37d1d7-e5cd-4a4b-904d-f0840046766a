package outfox.ead.noah.dao;

import outfox.ead.noah.entity.BundlePackageBundle;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface BundlePackageBundleDao {

    void saveOrUpdate(BundlePackageBundle bundleBundlePackage);

    void batchInsertOrUpdate(Collection<BundlePackageBundle> bundleBundlePackages);

    void removeAll(Collection<BundlePackageBundle> bundleBundlePackages);

    Map<Long, List<String>> getBundlePackageId2BundlesMapByBundlePackageIds(Collection<Long> bundlePackageIds);

    List<BundlePackageBundle> getListByBundlePackageId(Long bundlePackageId);
}
