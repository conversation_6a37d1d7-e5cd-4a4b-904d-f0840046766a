package outfox.ead.noah.dao;

import outfox.ead.noah.entity.OcpcCompensationRecord;
import outfox.ead.noah.dto.OcpcCompensationItem;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * oCPC自动赔付
 *
 **/
public interface OcpcCompensationRecordDao {

    /**
     * 获取oCPC赔付记录
     *
     * @param sponsorId             账号id，必填
     * @param compensationStatusSet 赔付状态，非必填，可以用null查询所有状态的记录
     * @param beginDate             开始时间，yyyy-MM-dd，查询结果包括beginDate，非必填
     * @param endDate               结果时间，yyyy-MM-dd，查询结果包括endDate，非必填
     */
    List<OcpcCompensationRecord> getRecordBySponsorId(Long sponsorId, Collection<Integer> compensationStatusSet, String beginDate, String endDate);

    /**
     * 批量广告组id对应的oCPC赔付状态
     *
     * @param groupIds 广告组id的集合
     * @return key为groupId，value为赔付状态，查不到赔付状态的groupId不会出现在结果map的key中
     */
    Map<Long, Integer> getCompensationStatusByGroupIds(Collection<Long> groupIds);

    void saveOrUpdateOcpcCompensationRecord(OcpcCompensationRecord ocpcCompensationRecord);

    void updateOcpcCompensationRecords(List<OcpcCompensationRecord> ocpcCompensationRecords);

    List<OcpcCompensationRecord> getByAdGroupId(Set<Long> adGroupIds, Integer compensationStatus);

    List<OcpcCompensationRecord> getByCompensationStatus(Integer status);

    /**
     * 根据组ID获取赔付邮件的相关明细
     *
     * @param adGroupIds
     * @return
     */
    List<OcpcCompensationItem> getOcpcCompensationItems(Set<Long> adGroupIds);

    /**
     * 将状态为fromStatus的状态(多个状态以英文逗号分隔)改为状态toStatus
     */
    void changeStatus(String fromStatus, String toStatus);
}
