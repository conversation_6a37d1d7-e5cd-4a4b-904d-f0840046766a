package outfox.ead.noah.dao;

import outfox.ead.noah.entity.LandingPageFormContent;
import outfox.ead.noah.service.auxiliary.LandingPageFormContentQueryAuxiliary;

import java.util.List;
import java.util.Map;

/**
 * Created by wangmo on 2017/8/15.
 */
public interface LandingPageFormContentDao {

    /**
     * 保存落地页表单内容，在ergate做了，不在智选做
     */
    @Deprecated
    boolean saveLandingPageFormContent(LandingPageFormContent landingPageFormContent);

    List<LandingPageFormContent> getLandingPageFormContentList(LandingPageFormContentQueryAuxiliary queryAuxiliary);

    List<LandingPageFormContent> getLandingPageFormContentListByFormIds(List<Long> landingPageFormIdList, long startDate, long endDate);

    long getLandingPageFormContentCount(LandingPageFormContentQueryAuxiliary queryAuxiliary);

    /**
     * 根据表单id查询最近提交表单的时间。如果没有提交，不会在map中出现。
     */
    Map<Long, Long> getLatestSubmitTimeById(List<Long> landingPageFormIdList);

}
