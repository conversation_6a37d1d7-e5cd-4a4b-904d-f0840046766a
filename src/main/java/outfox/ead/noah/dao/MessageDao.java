package outfox.ead.noah.dao;

import outfox.ead.noah.entity.Message;
import outfox.ead.noah.entity.MessageContent;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Created by yuanzhch on 2017/9/8.
 */
public interface MessageDao {
    public List<Message> getMessageList(Long agentId, Long sponsorId,
                                        int status, List<Integer> types, Long expireTime, int position, int length);

    public int getMessageCount(Long agentId, Long sponsorId,
                               int status, List<Integer> types, Long expireTime);

    public Message getMessage(long messageId);

    long getMessageCount(long sponsorId, Set<Integer> types);

    List<Message> getPagedMessage(long sponsorId, Set<Integer> types, int position, int length);

    public void addMessage(MessageContent content, Message message);

    public void changeMessageStatus(Long messageId, int status);

    public long getUnreadMessageCount(long sponsorId);

    /**
     * 批量修改消息状态
     * @param sponsorId sponsorId
     * @param msgIds 消息id列表
     */
    void batchUpdateMessageStatus(Long sponsorId, Collection<Long> msgIds, Integer status);

    /**
     * 根据ids获取用户消息
     * @param sponsorId 广告商id
     * @param msgIds 消息ids
     * @return list
     */
    List<Message> getMessagesByMessageIds(Long sponsorId, Collection<Long> msgIds);
}
