package outfox.ead.noah.dao;


import outfox.ead.noah.entity.SdkDeveloper;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SdkDeveloperDao {
    /**
     * 添加或修改一个SdkDeveloper
     */
    public void saveOrUpdateSdkDeveloper(SdkDeveloper sdkDeveloper);

    /**
     * 根据一个条件的value获取其SdkDeveloper数据
     *
     * @return List<SdkDeveloper>
     */
    public List<SdkDeveloper> getByAdContentByOneColumn(String columnName, String columnValue);

    /**
     * SdkDeveloper
     *
     * @return SdkDeveloper
     */
    public SdkDeveloper getById(long developerId);
}
