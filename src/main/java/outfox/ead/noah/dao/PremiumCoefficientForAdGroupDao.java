package outfox.ead.noah.dao;

import outfox.ead.noah.entity.PremiumCoefficientForAdGroup;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface PremiumCoefficientForAdGroupDao {

    /**
     * 保存或者更新一条记录
     *
     * @param premiumCoefficientForAdGroup
     */
    void saveOrUpdateItem(PremiumCoefficientForAdGroup premiumCoefficientForAdGroup);

    /**
     * 根据推广组ID获取一条记录
     *
     * @param adGroupId
     * @return
     */
    PremiumCoefficientForAdGroup getItemByAdGroupId(Long adGroupId);

    /**
     * 根据多个推广组ID获取一组记录
     *
     * @param adGroupIds
     * @return
     */
    List<PremiumCoefficientForAdGroup> getItemByAdGroupIds(Set<Long> adGroupIds);

    /**
     * 删除一条记录
     *
     * @param premiumCoefficientForAdGroup
     */
    void removeItem(PremiumCoefficientForAdGroup premiumCoefficientForAdGroup);

    /**
     * 批量删除
     *
     * @param items
     */
    void batchRemoveItems(Collection<PremiumCoefficientForAdGroup> items);

    /**
     * 批量保存或者更新
     *
     * @param items
     */
    void batchSaveOrUpdate(List<PremiumCoefficientForAdGroup> items);
}
