package outfox.ead.noah.dao;

import org.hibernate.ScrollableResults;
import outfox.ead.noah.entity.ImageLibrary;
import outfox.ead.noah.entity.ImageTemplateRel;
import outfox.ead.noah.entity.ImageTemplateStat;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/2.
 */
public interface ImageTemplateStatDao {

    /**
     * 批量保存
     *
     * @param templateStatList 模板点击率统计结果
     */
    void batchInsertOrUpdate(Collection<ImageTemplateStat> templateStatList);

}
