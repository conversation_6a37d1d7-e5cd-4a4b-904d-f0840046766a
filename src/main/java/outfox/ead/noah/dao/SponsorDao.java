package outfox.ead.noah.dao;

import org.joda.time.DateTime;
import outfox.ead.noah.entity.Sponsor;

import java.io.Serializable;
import java.util.List;

/**
 * Created by huanghuan on 16/4/11.
 */
public interface SponsorDao {
    /**
     * 获取一段连续的Sponsor记录
     *
     * @return
     */
    public List<Sponsor> getSponsorsByLimit(int start, int end);

    /**
     * save or update sponsor info
     *
     * @param sponsor
     */
    public void saveOrUpdateSponsor(Sponsor sponsor);

    /**
     * get sponsor info by id
     *
     * @param id
     * @return
     */
    public Sponsor getSponsor(long id);

    /**
     * check if the user input url is already registered by others
     *
     * @param url
     * @return
     */
    public boolean isRegisteredUrl(String url);

    /**
     * change sponsor status
     *
     * @param id
     * @param status
     */
    public void updateSponsorStatus(long id, int status);

    Serializable save(Sponsor sponsor);

    /**
     * 通过userName获取广告主
     * @param userName
     * @return Sponsor
     */
     Sponsor getSponsor(String userName);

    List<Sponsor> findSponsorDetailsBetweenDatesAndStatus(DateTime start, DateTime end, int status, int auditStatus);

    List<Sponsor> findSponsorDetailsByIds(List<Long> sponsorIds);
}
