package outfox.ead.noah.dao;

import outfox.ead.noah.entity.AdCampaign;
import outfox.ead.noah.entity.models.GroupsTargetingInfo;
import outfox.ead.noah.entity.models.TargetingConditions;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by huanghuan on 16/5/3.
 */
public interface AdCampaignDao {

    public List<AdCampaign> getAdCampaignByName(long sponsorId, String name);
    /**
     * 保存或更新广告系列
     *
     * @param adCampaign 广告系列
     */
    public void saveOrUpdateAdCampaign(AdCampaign adCampaign);

    /**
     * 根据ID获取广告系列
     *
     * @param id 广告系列ID
     * @return 广告系列
     */
    public AdCampaign getAdCampaign(long id);

    /**
     * 根据广告商ID获取广告系列
     *
     * @param sponsorId 广告商ID
     * @return 广告系列列表
     */
    public List getAdCampaignListBySponsorId(long sponsorId);

    public List<Long> getUndeletedIdListBySponsorId(long sponsorId);
    /**
     * 查询给定条件下，广告系列个数
     * @param sponsorId
     * @param keyword
     * @param status StatusCode.STATUS_UNDELETE，StatusCode.STATUS_DELETE，StatusCode.STATUS_ALL,
     *               StatusCode.STATUS_ACTIVE, StatusCode.STATUS_STOP, StatusCode.STATUS_OUT_OF_BUDGET,
     *               StatusCode.STATUS_OUT_OF_BALANCE, StatusCode.STATUS_UP_TO_ENDDATE
     * @param landingType
     * @return
     */
    public long getAdCampaignCount(long sponsorId, String keyword, String status, int landingType);

    /**
     * get ad campaign list turnover page
     *
     * 新智选中的广告系列的状态和老智选一样，只是将老智选中"无效"的状态放到外面了
     * 广告系列共有六种种状态，有效（投放中），暂停，已删除，预算不足，余额不足，时间截止
     * 当整个状态值为0时，有效
     * 暂停，已删除根据uaa段判断，1表示暂停，2表示删除
     * 预算不足，余额不足，时间截止根据op段判断，第28位为1表示时间截止，第29位为1表示预算不足，第30位为1表示余额不足
     *
     * 当op段与uaa段状态冲突时，优先显示uaa段状态；op段状态优先级，余额不足>预算不足>时间截止
     *
     * 还有一种未删除，表示除去已删除外的其他所有
     *
     * 更多状态相关参考<a>https://dev.corp.youdao.com/outfoxwiki/EADDataStatus</a>
     *
     * @param sponsorId
     * @param keyword
     * @param status StatusCode.STATUS_UNDELETE，StatusCode.STATUS_DELETE，StatusCode.STATUS_ALL,
     *               StatusCode.STATUS_ACTIVE, StatusCode.STATUS_STOP, StatusCode.STATUS_OUT_OF_BUDGET,
     *               StatusCode.STATUS_OUT_OF_BALANCE, StatusCode.STATUS_UP_TO_ENDDATE
     * @param position
     * @param length
     * @param landingType 可以为NameCode.AD_LANDING_ANDROID_DOWNLOAD_VALUE,
     *                    NameCode.AD_LANDING_IOS_DOWNLOAD_VALUE
     *                    NameCode.AD_LANDING_WEBPAGE_VALUE
     *                    ,也可以为-1，表示全部类型
     * @param orderType
     * @return
     */
    public List<AdCampaign> getAdCampaignList(
            long sponsorId, String keyword, String status, int position, int length, int landingType, int orderType);

    /**
     * 根据广告商ID，查询词获取广告系列列表
     *
     * @param sponsorId 广告商ID
     * @param word      查询词
     * @return 广告系列列表
     */
    public List getAdCampaignListWithKeyword(long sponsorId, String word);

    /**
     * 修改广告系列状态
     *
     * @param id     广告系列ID
     * @param status 状态
     */
    public void updateAdCampaignStatus(long id, int status);

    /**
     * 获取SponsorId对应的AdCampaign Ids
     *
     * @param sponsorId
     * @return
     */
    public List getCampaignIdsBySponsorId(long sponsorId);

    /**
     *
     * @param ids
     * @return
     */
    public List<AdCampaign> getAdCampaignListByIds(Set<Long> ids);

    public Map<Long, AdCampaign> getAdCampaignMapByIds(Set<Long> ids);


    /**
     * 分页根据关键词获取
     * @param sponsorId
     * @param keyword
     * @param position
     * @param length
     * @return
     */
    public List<AdCampaign> getAdCampaignNameAndLandingPageType(Long sponsorId, String keyword, Integer position, Integer length);


    /**
     * 根据sponsorId 和 landingPageType 获取推广系列
     * @param sponsorId
     * @param landingPageType
     * @return
     */
    public List<AdCampaign> getAdCampaignBySponsorIdAndLandingPageType(Long sponsorId, Integer landingPageType);

    /**
     * 根据conditions中的落地页型，推广类型，流量类型，平台类型分页获取系列及对应的组，支持对系列和组模糊搜索<br>
     * @param conditions: 包含了，落地页型，推广类型，平台类型，流量类型这四个条件
     */
    List<GroupsTargetingInfo> getCampaignsInCopyingGroupTargeting(String keyword, Integer offset, Integer limit, TargetingConditions conditions);

    /**
     * 根据conditions中的落地页型，推广类型，流量类型，平台类型分页获取系列及对应的组，支持组名模糊搜索<br>
     * 获取上述规则返回的系列总数
     * @param conditions: 包含了，落地页型，推广类型，平台类型，流量类型这四个条件
     */
    int countCampaignsInCopyingGroupTargeting(String keyword, Integer offset, Integer limit, TargetingConditions conditions);

    /**
     * 分页返回所有的未删除的系列及对应的组，支持对系列名和组名模糊搜索
     */
    List<GroupsTargetingInfo> pagingCampaignsWithGroups(Long sponsorId, String keyword, Integer offset, Integer limit);

    /**
     * 分页返回所有的未删除的系列及对应的组，支持对系列名和组名模糊搜索<br>
     * 获取上述规则返回的系列总数
     */
    Integer countCampaignsWithGroups(Long sponsorId, String keyword);

    List<AdCampaign> findAllByAdPlanIds(List<Long> adPlanIds);

    List<AdCampaign> findAllBySponsorIds(List<Long> sponsorIds);
}
