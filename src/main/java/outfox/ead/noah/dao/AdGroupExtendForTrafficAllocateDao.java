package outfox.ead.noah.dao;

import outfox.ead.noah.entity.AdGroupExtendForTrafficAllocate;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AdGroupExtendForTrafficAllocateDao {

    AdGroupExtendForTrafficAllocate findByAdGroupId(Long adGroupId);

    List<AdGroupExtendForTrafficAllocate> getAll();

    void batchInsertOrUpdate(Collection<AdGroupExtendForTrafficAllocate> entities);

    void saveOrUpdate(AdGroupExtendForTrafficAllocate entity);

    void remove(Long id);
}
