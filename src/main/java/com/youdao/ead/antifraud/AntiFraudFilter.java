package com.youdao.ead.antifraud;

import com.youdao.ead.dto.ReporterClickDTO;
import com.youdao.ead.entity.AntiFraudConfig;
import com.youdao.ead.entity.AntiFraudRule;
import com.youdao.ead.repository.AntiFraudRuleRepository;
import com.youdao.ead.antifraud.eval.EvaluatorFactory;
import com.youdao.ead.antifraud.eval.RuleEvaluator;
import com.youdao.ead.vo.request.ThirdRtaRequest;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import static com.youdao.ead.antifraud.AntiFraudContext.*;
import static com.youdao.ead.antifraud.AntiFraudTagEnum.PARAM_FILTER_IP;
import static com.youdao.ead.antifraud.eval.EvaluatorFactory.TAG_SOURCE_FILTER;
import static com.youdao.ead.constant.ChannelDidConstants.HUAWEI;

/**
 * 作弊点击过滤器
 *
 * <AUTHOR>
 * @date 2024/7/9
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AntiFraudFilter {

    private final AntiFraudRuleRepository antiFraudRuleRepository;

    private final EvaluatorFactory evaluatorFactory;

    @Getter
    private volatile Map<Long, List<String>> sourceId2Keys = new HashMap<>();

    private Map<Long, List<AntiFraudConfig>> antiFraudConfigMap;

    public static final Map<Integer, AntiFraudTagEnum> RUL_ID_2_TAG_ENUM_MAP = new HashMap<>();

    static {
        for (AntiFraudTagEnum tagEnum : AntiFraudTagEnum.values()) {
            RUL_ID_2_TAG_ENUM_MAP.put(tagEnum.getRuleId(), tagEnum);
        }
    }

    @Scheduled(initialDelay = 0, fixedDelay = 30 * 1000)
    public void reloadAntiFraudConfig() {
        try {
            List<AntiFraudRule> allAntiFraudRules = antiFraudRuleRepository.loadAllAntiFraudRule();
            reloadAntiFraudSourceConfig(allAntiFraudRules);
            antiFraudConfigMap = antiFraudRuleRepository.loadAllAntiFraudConfig(allAntiFraudRules);
        } catch (Exception e) {
            log.error("load anti fraud config error", e);
        }
    }

    private void reloadAntiFraudSourceConfig(List<AntiFraudRule> allAntiFraudRules) {
        try {
            Map<Long, List<String>> sourceId2KeysMap = new HashMap<>();
            for (AntiFraudRule antiFraudRule : allAntiFraudRules) {
                if (TAG_SOURCE_FILTER.equals(antiFraudRule.getTag())) {
                    sourceId2KeysMap.put(antiFraudRule.getId(), antiFraudRule.getExtendInfo().getOrDefault(TAG_SOURCE_FILTER, Collections.emptyList()));
                }
            }
            sourceId2Keys = sourceId2KeysMap;
        } catch (Exception e) {
            log.error("load anti fraud source config error", e);
        }

    }

    /**
     * RTA过滤
     *
     * @param contextPlus
     */
    public void filterRta(ThirdRtaConfigContext contextPlus) {
        ThirdRtaRequest request = contextPlus.thirdRtaRequest();
        String channelDid = request.getChannel();
        for (long aid : contextPlus.getMatchedAids()) {
            List<AntiFraudConfig> antiFraudConfigs = antiFraudConfigMap.get(aid);
            if (CollectionUtils.isEmpty(antiFraudConfigs)) {
                contextPlus.getAidsNotFraudFilter().add(aid);
                continue;
            }
            RtaItem rtaItem = contextPlus.rtaItemByAid(aid);
            if (rtaItem == null) {
                contextPlus.getAidsNotFraudFilter().add(aid);
                continue;
            }
            filterRtaCoreFunc(request, rtaItem, channelDid, antiFraudConfigs);
            if (rtaItem.filterResult()) {
                contextPlus.getAidsFraudFilter().add(aid);
            } else {
                contextPlus.getAidsNotFraudFilter().add(aid);
            }

        }
    }

    public void filterRtaCoreFunc(ThirdRtaRequest request, RtaItem rtaItem, String channelDid, List<AntiFraudConfig> antiFraudConfigs) {
        for (AntiFraudConfig antiFraudConfig : antiFraudConfigs) {
            if (CollectionUtils.isEmpty(antiFraudConfig.getAntiFraudRulesRta())) {
                // 反作弊配置rta请求的反作弊规则列表为空则跳过。
                continue;
            }
            List<String> effectChannelDids = antiFraudConfig.getEffectChannelDids();
            if (CollectionUtils.isNotEmpty(effectChannelDids) && !effectChannelDids.contains(channelDid)) {
                // 指定生效渠道时，如果渠道号不匹配，则对应规则不生效
                continue;
            }
            // 计算过滤结果和过滤原因
            boolean result = false;
            List<AntiFraudRule> antiFraudRules = new ArrayList<>();
            if (AntiFraudConfig.TRIGGER_MODE_ANY.equals(antiFraudConfig.getTriggerMode())) {
                for (AntiFraudRule antiFraudRule : antiFraudConfig.getAntiFraudRulesRta()) {
                    boolean match = evaluateRta(antiFraudRule, rtaItem, request);
                    // any_one 模式 有一个匹配就被过滤
                    if (match) {
                        result = true;
                        antiFraudRules.add(antiFraudRule);
                        break;
                    }
                }
            } else if (AntiFraudConfig.TRIGGER_MODE_ALL.equals(antiFraudConfig.getTriggerMode())) {
                result = true;
                for (AntiFraudRule antiFraudRule : antiFraudConfig.getAntiFraudRulesRta()) {
                    boolean match = evaluateRta(antiFraudRule, rtaItem, request);
                    // all 模式有一个不匹配就不过滤
                    if (!match) {
                        result = false;
                        break;
                    }
                }
                if (result) {
                    antiFraudRules.addAll(antiFraudConfig.getAntiFraudRules());
                }
            }
            if (result) {
                // 使用数据库的配置，满足过滤比例才会真正过滤
                double filterRatio = antiFraudConfig.getFilterRatio();
                double random = ThreadLocalRandom.current().nextDouble();
                if (random < filterRatio) {
                    rtaItem.setAntiFraudConfigId(antiFraudConfig.getId());
                    rtaItem.setAntiFraudRules(antiFraudRules);
                    break;
                }
            }
            // 如果没有真正过滤，需要重置以下标记
            rtaItem.setBlankParamFiltered(false);
            rtaItem.setModelFiltered(false);
            rtaItem.setSourceFiltered(false);
        }
    }

    /**
     * 点击过滤
     *
     * @param context
     */
    public void filterClick(AntiFraudContext context) {
        ReporterClickDTO clickDto = context.getReporterClickDto();
        Long aid = clickDto.getAid();
        String channelDid = clickDto.getChannelDid();
        List<AntiFraudConfig> antiFraudConfigs = antiFraudConfigMap.get(aid);
        filterCoreFunc(context, channelDid, antiFraudConfigs);
    }

    public void filterCoreFunc(AntiFraudContext context, String channelDid, List<AntiFraudConfig> antiFraudConfigs) {
        if (CollectionUtils.isNotEmpty(antiFraudConfigs)) {
            for (AntiFraudConfig antiFraudConfig : antiFraudConfigs) {
                if (CollectionUtils.isEmpty(antiFraudConfig.getAntiFraudRules())) {
                    // 反作弊配置点击的反作弊规则列表为空则跳过。
                    continue;
                }
                List<String> effectChannelDids = antiFraudConfig.getEffectChannelDids();
                if (CollectionUtils.isNotEmpty(effectChannelDids) && !effectChannelDids.contains(channelDid)) {
                    // 指定生效渠道时，如果渠道号不匹配，则对应规则不生效
                    continue;
                }
                // 计算过滤结果和过滤原因
                boolean result = false;
                List<AntiFraudRule> antiFraudRules = new ArrayList<>();
                if (AntiFraudConfig.TRIGGER_MODE_ANY.equals(antiFraudConfig.getTriggerMode())) {
                    for (AntiFraudRule antiFraudRule : antiFraudConfig.getAntiFraudRules()) {
                        boolean match = evaluate(antiFraudRule, context);
                        // any_one 模式 有一个匹配就被过滤
                        if (match) {
                            result = true;
                            antiFraudRules.add(antiFraudRule);
                            break;
                        }
                    }
                } else if (AntiFraudConfig.TRIGGER_MODE_ALL.equals(antiFraudConfig.getTriggerMode())) {
                    result = true;
                    for (AntiFraudRule antiFraudRule : antiFraudConfig.getAntiFraudRules()) {
                        boolean match = evaluate(antiFraudRule, context);
                        // all 模式有一个不匹配就不过滤
                        if (!match) {
                            result = false;
                            break;
                        }
                    }
                    if (result) {
                        antiFraudRules.addAll(antiFraudConfig.getAntiFraudRules());
                    }
                }
                if (result) {
                    // 使用数据库的配置，满足过滤比例才会真正过滤
                    double filterRatio = antiFraudConfig.getFilterRatio();
                    double random = ThreadLocalRandom.current().nextDouble();
                    if (random < filterRatio) {
                        context.setAntiFraudConfigId(antiFraudConfig.getId());
                        context.setAntiFraudRules(antiFraudRules);
                        break;
                    }
                }
                // 如果没有真正过滤，需要重置以下标记
                context.setBlankParamFiltered(false);
                context.setModelFiltered(false);
                context.setSourceFiltered(false);
            }
        }
    }

    /**
     * 根据推广活动id获取配置的反作弊规则列表
     * @param antiFraudType
     * @param aid
     * @return
     */
    public BitSet configRuleTags(int antiFraudType, Long aid) {
        BitSet result = new BitSet();
        if (aid == null) {
            return result;
        }
        List<AntiFraudConfig> antiFraudConfigs = antiFraudConfigMap.get(aid);
        if (antiFraudConfigs == null) {
            return result;
        }
        switch (antiFraudType) {
            case ANTI_FRAUD_TYPE_THIRD_PARTY_CLICK:
                // 多条配置匹配
                for (AntiFraudConfig antiFraudConfig : antiFraudConfigs) {
                    // 一条配置配置多个过滤规则id
                    for (AntiFraudRule antiFraudRule : antiFraudConfig.getAntiFraudRules()) {
                        int ruleId = (int) antiFraudRule.getId();
                        if (RUL_ID_2_TAG_ENUM_MAP.containsKey(ruleId)) {
                            result.set(ruleId);
                        }
                    }
                }
                break;
            case ANTI_FRAUD_TYPE_RTA_REQUEST:
                // 多条配置匹配
                for (AntiFraudConfig antiFraudConfig : antiFraudConfigs) {
                    // 一条配置配置多个过滤规则id
                    for (AntiFraudRule antiFraudRule : antiFraudConfig.getAntiFraudRulesRta()) {
                        int ruleId = (int) antiFraudRule.getId();
                        if (RUL_ID_2_TAG_ENUM_MAP.containsKey(ruleId)) {
                            result.set(ruleId);
                        }
                    }
                }
                break;
        }
        return result;
    }

    /**
     * 执行当前规则
     *
     * @return 规则执行结果，true=命中，false=未命中
     */
    private boolean evaluate(AntiFraudRule antiFraudRule, AntiFraudContext context) {
        String tag = antiFraudRule.getTag();
        RuleEvaluator evaluator = evaluatorFactory.getEvaluator(tag);
        Map<String, List<String>> extendInfo = antiFraudRule.getExtendInfo();
        List<String> subItems = extendInfo != null ? extendInfo.get(tag) : Collections.emptyList();
        return evaluator.evaluateClick(context, antiFraudRule.getId(), tag, subItems);

    }

    private boolean evaluateRta(AntiFraudRule antiFraudRule, AntiFraudContext.RtaItem rtaItem, ThirdRtaRequest request) {
        if (skipWhiteListRta(request, antiFraudRule)){
            return false;
        }
        String tag = antiFraudRule.getTag();
        RuleEvaluator evaluator = evaluatorFactory.getEvaluator(tag);
        Map<String, List<String>> extendInfo = antiFraudRule.getExtendInfo();
        List<String> subItems = extendInfo != null ? extendInfo.get(tag) : Collections.emptyList();
        return evaluator.evaluateRta(request, rtaItem, antiFraudRule.getId(), tag, subItems);
    }

    private static boolean skipWhiteListRta(ThirdRtaRequest request, AntiFraudRule antiFraudRule) {
        // 华为渠道白名单
        if (Objects.equals(HUAWEI, request.getChannel())) {
            if (Objects.equals(antiFraudRule.getId(), (long) PARAM_FILTER_IP.getRuleId())) {
                return true;
            }
        }
        return false;
    }

}
