package com.youdao.ead.antifraud.deal;

import com.youdao.ead.antifraud.AntiFraudContext;
import com.youdao.ead.antifraud.DealEnum;
import com.youdao.ead.antifraud.ThirdRtaConfigContext;
import com.youdao.ead.dto.ReporterClickDTO;
import com.youdao.ead.util.DeviceModelRenderUtils;
import com.youdao.ead.vo.request.ThirdRtaRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import outfox.ead.dsp.protocol.youdao.ClickDevicePool;

import java.util.BitSet;
import java.util.Objects;

import static com.youdao.ead.antifraud.AntiFraudTagEnum.*;
import static com.youdao.ead.antifraud.OriginParamKeyConstants.NEW_KEY_DEVICE_MODEL;
import static com.youdao.ead.antifraud.OriginParamKeyConstants.ORIGIN_KEY_DEVICE_MODEL;

/**
 *
 * <AUTHOR>
 * @date 2024/11/18
 */
@Order(value = 50)
@Service
public class TryFixModelDealer implements IFraudDealer {

    private static final BitSet CLICK_RESOLVED_TRY_UA = new BitSet();
    private static final BitSet CLICK_RESOLVED_EMPTY = new BitSet();
    private static final BitSet CLICK_UNSOLVED_EMPTY = new BitSet();
    static {

        CLICK_RESOLVED_TRY_UA.set(PARAM_FILTER_MODEL.getRuleId());

        CLICK_UNSOLVED_EMPTY.set(PARAM_FILTER_MODEL.getRuleId());

        CLICK_RESOLVED_EMPTY.set(RELATED_MULTI_MODEL.getRuleId());
        CLICK_RESOLVED_EMPTY.set(RELATED_RTA_MODEL.getRuleId());
        CLICK_RESOLVED_EMPTY.set(UNEXPECTED_MODEL.getRuleId());
        CLICK_RESOLVED_EMPTY.set(STANDARD_DIFF_MODEL.getRuleId());
    }

    @Override
    public DealEnum dealEnum() {
        return DealEnum.TRY_FIX_MODEL;
    }

    @Override
    public void dealClick(AntiFraudContext context, BitSet reasonDeal, BitSet dutyAllDeal, BitSet markFraudOnlyRead) {
        ReporterClickDTO clickDto = context.getReporterClickDto();
        context.getOriginParam().put(ORIGIN_KEY_DEVICE_MODEL, Objects.toString(clickDto.getOriginalModel(), ""));
        if (tryStandard(context, clickDto, markFraudOnlyRead)) {
            context.getDealResolvedFraudRuleIdSet().or(dutyAllDeal);
        } else if (tryUserAgent(clickDto, reasonDeal, context, markFraudOnlyRead)) {
            context.getDealResolvedFraudRuleIdSet().or(CLICK_RESOLVED_TRY_UA);
        } else {
            // 置为空
            clickDto.setOriginalModel("");
            context.getDealResolvedFraudRuleIdSet().or(CLICK_RESOLVED_EMPTY);
            context.getDealUnsolvedFraudRuleIdSet().or(CLICK_UNSOLVED_EMPTY);
        }
    }
    /**
     * 从标准池取值
     * @return
     */
    private boolean tryStandard(AntiFraudContext context, ReporterClickDTO clickDto, BitSet markFraud) {
        if (!canTryStandard(markFraud)) {
            return false;
        }
        ClickDevicePool.StandardDeviceInfo standardInfo = context.getStandardInfoFuture().getResult().orElse(null);
        if (standardInfo != null) {
            if (StringUtils.isNotBlank(standardInfo.getModel())) {
                clickDto.setOriginalModel(standardInfo.getModel());
                return true;
            }
        }
        return false;
    }

    /**
     * 是否能使用设备清洗池修正
     * true:能
     * false:不能
     */
    private boolean canTryStandard(BitSet markFraud) {
        if (markFraud.get(UNEXPECTED_DEVICE.getRuleId()) || markFraud.get(RELATED_RTA_DEVICE.getRuleId())) {
            return false;
        }
        return true;
    }

    /**
     * 从ua中解析
     * @return
     */
    private boolean tryUserAgent(ReporterClickDTO clickDto, BitSet reasonDeal, AntiFraudContext context, BitSet markFraudOnlyRead) {
        if (!canTryUserAgent(reasonDeal, context.getDealResolvedFraudRuleIdSet(), markFraudOnlyRead)) {
            return false;
        }
        String deviceModel = DeviceModelRenderUtils.parseBrandAndModelFromUa(clickDto.getUserAgentClient(), clickDto.getMobileBrand());
        if (StringUtils.isBlank(deviceModel)) {
            return false;
        }
        clickDto.setOriginalModel(deviceModel);
        return true;
    }

    /**
     * 是否能从ua中解析
     * @param reasonDeal
     * @param resolvedOnlyRead 只读
     * @param markFraudOnlyRead 只读
     * @return true 能
     */
    private boolean canTryUserAgent(BitSet reasonDeal, BitSet resolvedOnlyRead, BitSet markFraudOnlyRead) {
        if (!(reasonDeal.cardinality() == 1 && reasonDeal.get(PARAM_FILTER_MODEL.getRuleId()))) {
            return false;
        }
        if (markFraudOnlyRead.get(STANDARD_DIFF_UA.getRuleId())) {
            if (!resolvedOnlyRead.get(STANDARD_DIFF_UA.getRuleId())) {
                return false;
            }
        }
        if (markFraudOnlyRead.get(UNEXPECTED_UA.getRuleId())) {
            if (!resolvedOnlyRead.get(UNEXPECTED_UA.getRuleId())) {
                return false;
            }
        }
        if (markFraudOnlyRead.get(RELATED_MULTI_UA.getRuleId())) {
            if (!resolvedOnlyRead.get(RELATED_MULTI_UA.getRuleId())) {
                return false;
            }
        }
        if (markFraudOnlyRead.get(RELATED_RTA_UA.getRuleId())) {
            if (!resolvedOnlyRead.get(RELATED_RTA_UA.getRuleId())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void dealRta(ThirdRtaConfigContext contextPlus, BitSet reasonDeal, BitSet dutyAllDeal,BitSet markFraudOnlyRead) {
        contextPlus.getOriginParam().put(ORIGIN_KEY_DEVICE_MODEL, Objects.toString(contextPlus.thirdRtaRequest().getModel(), ""));
        if (tryStandardRta(contextPlus, markFraudOnlyRead)) {
            contextPlus.resolved(dutyAllDeal);
        } else if (tryUserAgentRta(contextPlus, reasonDeal, markFraudOnlyRead)) {
            contextPlus.resolved(CLICK_RESOLVED_TRY_UA);
        } else {
            // 置为空
            contextPlus.getFixedParams().put(NEW_KEY_DEVICE_MODEL, "");
            contextPlus.resolved(CLICK_RESOLVED_EMPTY);
            contextPlus.unsolved(CLICK_UNSOLVED_EMPTY);
        }
    }

    private boolean tryStandardRta(ThirdRtaConfigContext contextPlus, BitSet markFraud) {
        if (!canTryStandard(markFraud)) {
            return false;
        }
        ClickDevicePool.StandardDeviceInfo standardInfo = contextPlus.standardInfo();
        if (standardInfo != null) {
            if (StringUtils.isNotBlank(standardInfo.getModel())) {
                contextPlus.getFixedParams().put(NEW_KEY_DEVICE_MODEL, standardInfo.getModel());
                return true;
            }
        }
        return false;
    }

    private boolean tryUserAgentRta(ThirdRtaConfigContext contextPlus, BitSet reasonDeal, BitSet markFraudOnlyRead) {
        if (!canTryUserAgent(reasonDeal, contextPlus.getResolvedClone(), markFraudOnlyRead)) {
            return false;
        }
        ThirdRtaRequest request = contextPlus.thirdRtaRequest();
        String deviceModel = DeviceModelRenderUtils.parseBrandAndModelFromUa(request.getUserAgent(), request.getFixBrand());
        if (StringUtils.isBlank(deviceModel)) {
            return false;
        }
        contextPlus.getFixedParams().put(NEW_KEY_DEVICE_MODEL, deviceModel);
        return true;
    }

}
