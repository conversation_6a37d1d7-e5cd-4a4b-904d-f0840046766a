package com.youdao.ead.antifraud.deal;

import com.youdao.ead.antifraud.AntiFraudContext;
import com.youdao.ead.antifraud.DealEnum;
import com.youdao.ead.antifraud.ThirdRtaConfigContext;
import com.youdao.ead.dto.ReporterClickDTO;
import com.youdao.ead.mobilemodels.util.MobileModelProvider;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import outfox.ead.dsp.protocol.youdao.ClickDevicePool;

import java.util.BitSet;
import java.util.List;
import java.util.Objects;

import static com.youdao.ead.antifraud.AntiFraudTagEnum.*;
import static com.youdao.ead.antifraud.DealEnum.*;
import static com.youdao.ead.antifraud.OriginParamKeyConstants.NEW_KEY_UA;
import static com.youdao.ead.antifraud.OriginParamKeyConstants.ORIGIN_KEY_UA;

/**
 * <AUTHOR>
 * @date 2024/11/18
 */
@Order(value = 10)
@Service
@AllArgsConstructor
public class TryFixUaDealer implements IFraudDealer {

    private final MobileModelProvider mobileModelProvider;

    @Override
    public DealEnum dealEnum() {
        return TRY_FIX_UA;
    }

    private static final BitSet CLICK_RESOLVED_REWRITE = new BitSet();
    private static final BitSet CLICK_RESOLVED_EMPTY = new BitSet();
    private static final BitSet CLICK_UNSOLVED_EMPTY = new BitSet();
    static {

        CLICK_RESOLVED_REWRITE.set(VALUE_ERROR_UA.getRuleId());

        CLICK_RESOLVED_EMPTY.set(VALUE_ERROR_UA.getRuleId());
        CLICK_RESOLVED_EMPTY.set(FORMAT_ERROR_UA.getRuleId());
        CLICK_RESOLVED_EMPTY.set(RELATED_MULTI_UA.getRuleId());
        CLICK_RESOLVED_EMPTY.set(RELATED_RTA_UA.getRuleId());
        CLICK_RESOLVED_EMPTY.set(UNEXPECTED_UA.getRuleId());
        CLICK_RESOLVED_EMPTY.set(STANDARD_DIFF_UA.getRuleId());

        CLICK_UNSOLVED_EMPTY.set(PARAM_FILTER_UA.getRuleId());
    }

    @Override
    public void dealClick(AntiFraudContext context, BitSet reasonDeal, BitSet dutyAllDeal, BitSet markFraudOnlyRead) {
        ReporterClickDTO clickDto = context.getReporterClickDto();
        context.getOriginParam().put(ORIGIN_KEY_UA, Objects.toString(clickDto.getUa(), ""));
        if (tryStandard(context, clickDto, markFraudOnlyRead)) {
            context.getDealResolvedFraudRuleIdSet().or(dutyAllDeal);
        } else if (tryRewrite(context, clickDto, reasonDeal)) {
            context.getDealResolvedFraudRuleIdSet().or(CLICK_RESOLVED_REWRITE);
        } else {
            clickDto.setUa("");
            context.getDealResolvedFraudRuleIdSet().or(CLICK_RESOLVED_EMPTY);
            context.getDealUnsolvedFraudRuleIdSet().or(CLICK_UNSOLVED_EMPTY);
        }
        clickDto.updateUserAgentClientAndBrandModel(mobileModelProvider);
    }

    /**
     * 是否能使用设备清洗池修正
     * true:能
     * false:不能
     */
    private boolean canTryStandard(BitSet markFraud) {
        if (markFraud.get(UNEXPECTED_DEVICE.getRuleId()) || markFraud.get(RELATED_RTA_DEVICE.getRuleId())) {
            return false;
        }
        return true;
    }

    /**
     * 从标准池取值
     * @return
     */
    private boolean tryStandard(AntiFraudContext context, ReporterClickDTO clickDto, BitSet markFraud) {
        if (!canTryStandard(markFraud)) {
            return false;
        }
        ClickDevicePool.StandardDeviceInfo standardInfo = context.getStandardInfoFuture().getResult().orElse(null);
        if (standardInfo != null) {
            if (StringUtils.isNotBlank(standardInfo.getUa())) {
                clickDto.setUa(standardInfo.getUa());
                return true;
            }
        }
        return false;
    }

    /**
     * 规则改写
     * @return
     */
    private boolean tryRewrite(AntiFraudContext context, ReporterClickDTO clickDto, BitSet reasonDeal) {
        if (!canTryRewrite(context, reasonDeal)) {
            return false;
        }
        String ua = clickDto.getUa();
        ua = fix(context.getValueErrorUaReasons(), ua);
        clickDto.setUa(ua);
        return true;
    }

    /**
     * 是否能进行规则改写
     * @return true 能
     */
    private boolean canTryRewrite(AntiFraudContext context, BitSet reasonDeal) {
        if (!context.isValueErrorUaCanRewrite()) {
            return false;
        }
        return reasonDeal.cardinality() == 1 && reasonDeal.get(VALUE_ERROR_UA.getRuleId());
    }

    @NotNull
    private String fix(List<Integer> valueErrorUaReasons, String ua) {
        if (valueErrorUaReasons.contains(2)) {
            ua = ua.substring(0, ua.indexOf(")") + 1);
        }
        // 从第二次修正开始就要检查是否包含关键字了
        if (valueErrorUaReasons.contains(3)) {
            ua = tryKeepBefore(ua, " hap");
        }
        if (valueErrorUaReasons.contains(4)) {
            ua = tryKeepBefore(ua, " com.hihonor");
        }
        if (valueErrorUaReasons.contains(5)) {
            ua = tryKeepBefore(ua, " ;360appstore");
        }
        if (valueErrorUaReasons.contains(6)) {
            ua = tryKeepBefore(ua, " HeyTapBrowser");
        }
        if (valueErrorUaReasons.contains(7)) {
            ua = tryKeepBefore(ua, " XiaoMi/MiuiBrowser");
            ua = tryKeepBefore(ua, " swan-mibrowser");
        }
        if (valueErrorUaReasons.contains(8)) {
            if (ua.startsWith("OPPO AppMarket ")) {
                ua = ua.replaceAll("OPPO AppMarket ", "");
            } else {
                ua = tryKeepBefore(ua, " OPPO AppMarket");
            }
        }
        if (valueErrorUaReasons.contains(9)) {
            int i = ua.indexOf("dalvik");
            if (i != -1) {
                ua = ua.substring(i);
            }
            i = ua.indexOf("Dalvik");
            if (i != -1) {
                ua = ua.substring(i);
            }
            i = ua.indexOf("mozilla");
            if (i != -1) {
                ua = ua.substring(i);
            }
            i = ua.indexOf("Mozilla");
            if (i != -1) {
                ua = ua.substring(i);
            }
        }
        if (valueErrorUaReasons.contains(10)) {
            ua = tryKeepBefore(ua, " SP-engine");
            ua = tryKeepBefore(ua, " XWEbaiduboxappB");
        }
        if (valueErrorUaReasons.contains(11)) {
            ua = tryKeepBefore(ua, " XWEB");
            ua = tryKeepBefore(ua, " MMWEBSDK");
            ua = tryKeepBefore(ua, " MMWEBID");
            ua = tryKeepBefore(ua, " MicroMessenger");
        }
        if (valueErrorUaReasons.contains(12)) {
            ua = tryKeepBefore(ua, " GDTMobSDK");
        }
        if (valueErrorUaReasons.contains(13)) {
            ua = tryKeepBefore(ua, " tieba");
        }
        if (valueErrorUaReasons.contains(14)) {
            ua = tryKeepBefore(ua, " VivoBrowser");
        }
        if (valueErrorUaReasons.contains(15)) {
            ua = tryKeepBefore(ua, " BytedanceWebview");
            ua = tryKeepBefore(ua, " CJPay");
        }
        if (valueErrorUaReasons.contains(16)) {
            ua = tryKeepBefore(ua, " Mobile/Youdao");
        }
        if (valueErrorUaReasons.contains(18)) {
            ua = tryKeepBefore(ua, " UCBrowser");
            ua = tryKeepBefore(ua, " iflow");
        }
        if (valueErrorUaReasons.contains(21)) {
            ua = tryKeepBefore(ua, " Hutool");
        }
        if (valueErrorUaReasons.contains(22)) {
            ua = tryKeepBefore(ua, " CloudMusic");
            ua = tryKeepBefore(ua, " NeteaseMusic");
        }
        if (valueErrorUaReasons.contains(23)) {
            ua = tryKeepBefore(ua, " mojii");
            ua = tryKeepBefore(ua, " mj_session_id");
        }
        if (valueErrorUaReasons.contains(24)) {
            ua = tryKeepBefore(ua, " tcsdzz");
            ua = tryKeepBefore(ua, " tcsdzzChannel");
            ua = tryKeepBefore(ua, " tcsdzzSafeArea");
            ua = tryKeepBefore(ua, " wkwebview");
        }
        if (valueErrorUaReasons.contains(25)) {
            ua = tryKeepBefore(ua, " MQQBrowser");
        }
        if (valueErrorUaReasons.contains(26)) {
            ua = tryKeepBefore(ua, " WiFiKey");
        }
        if (valueErrorUaReasons.contains(27)) {
            ua = tryKeepBefore(ua, " ImgoTV");
        }

        ua = ua.trim();
        return ua;
    }

    private String tryKeepBefore(String str, String searchStr) {
        if (str == null || searchStr == null) {
            return "";
        }
        int index = str.indexOf(searchStr);
        return index != -1 ? str.substring(0, index) : str;
    }

    @Override
    public void dealRta(ThirdRtaConfigContext contextPlus, BitSet reasonDeal, BitSet dutyAllDeal, BitSet markFraudOnlyRead) {
        contextPlus.getOriginParam().put(ORIGIN_KEY_UA, Objects.toString(contextPlus.thirdRtaRequest().getUa(), ""));
        if (tryStandardRta(contextPlus, markFraudOnlyRead)) {
            contextPlus.resolved(dutyAllDeal);
        } else if (tryRewriteRta(contextPlus, reasonDeal)) {
            contextPlus.resolved(CLICK_RESOLVED_REWRITE);
        } else {
            contextPlus.getFixedParams().put(NEW_KEY_UA, "");
            contextPlus.resolved(CLICK_RESOLVED_EMPTY);
            contextPlus.unsolved(CLICK_UNSOLVED_EMPTY);
        }
        contextPlus.thirdRtaRequest().updateUserAgentClientAndBrandModel(mobileModelProvider, Objects.toString(contextPlus.getFixedParams().get(NEW_KEY_UA), ""));
    }

    private boolean tryStandardRta(ThirdRtaConfigContext contextPlus, BitSet markFraud) {
        if (!canTryStandard(markFraud)) {
            return false;
        }
        ClickDevicePool.StandardDeviceInfo standardInfo = contextPlus.standardInfo();
        if (standardInfo != null) {
            if (StringUtils.isNotBlank(standardInfo.getUa())) {
                contextPlus.getFixedParams().put(NEW_KEY_UA, standardInfo.getUa());
                return true;
            }
        }
        return false;
    }

    /**
     * 规则改写
     * @return
     */
    private boolean tryRewriteRta(ThirdRtaConfigContext contextPlus, BitSet reasonDeal) {
        AntiFraudContext context = contextPlus.getAntiFraudContext();
        if (!canTryRewrite(context, reasonDeal)) {
            return false;
        }
        String ua = contextPlus.thirdRtaRequest().getUa();
        ua = fix(context.getValueErrorUaReasons(), ua);
        contextPlus.getFixedParams().put(NEW_KEY_UA, ua);
        return true;
    }


}
