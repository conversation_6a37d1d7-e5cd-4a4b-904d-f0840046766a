package com.youdao.ead.antifraud.eval;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/14
 */
@Service
@RequiredArgsConstructor
public class EvaluatorFactory {

    public static final String TAG_SOURCE_FILTER = "source_filter";
    public static final String TAG_MODEL_FILTER = "model_filter";
    public static final String TAG_PARAM_FILTER = "param_filter";

    private final SourceFilterEvaluator sourceFilterEvaluator;

    private final ModelFilterEvaluator modelFilterEvaluator;

    private final ParamFilterEvaluator paramFilterEvaluator;

    private final TagRuleEvaluator tagRuleEvaluator;

    public RuleEvaluator getEvaluator(String tag) {
        RuleEvaluator ruleEvaluator;
        switch (tag) {
            case TAG_SOURCE_FILTER:
                ruleEvaluator = sourceFilterEvaluator;
                break;
            case TAG_PARAM_FILTER:
                ruleEvaluator = paramFilterEvaluator;
                break;
            case TAG_MODEL_FILTER:
                ruleEvaluator = modelFilterEvaluator;
                break;
            default:
                ruleEvaluator = tagRuleEvaluator;
                break;
        }
        return ruleEvaluator;
    }
}
