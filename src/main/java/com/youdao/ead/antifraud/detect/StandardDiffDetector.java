package com.youdao.ead.antifraud.detect;

import com.youdao.ead.antifraud.AntiFraudContext;
import com.youdao.ead.antifraud.AntiFraudTagEnum;
import com.youdao.ead.dto.ReporterClickDTO;
import com.youdao.ead.util.CommonUtils;
import com.youdao.ead.validator.ParamFilledValidator;
import com.youdao.ead.vo.request.ThirdRtaRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import outfox.ead.dsp.protocol.youdao.ClickDevicePool;

import java.util.Objects;

import static com.youdao.ead.validator.ParamFilledValidator.*;

/**
 * <AUTHOR>
 * @create 2025-04-09 18:58
 **/
@Order
@Service
@RequiredArgsConstructor
@Slf4j
public class StandardDiffDetector implements IFraudDetector {
    private final ParamFilledValidator paramFilledValidator;

    @Override
    public void detectClick(AntiFraudContext context) {
        ReporterClickDTO reporterClickDto = context.getReporterClickDto();
        String ua = reporterClickDto.getUa();
        String deviceModel = reporterClickDto.getOriginalModel();
        String osVersion = reporterClickDto.getOsVersion();

        detect(context, ua, deviceModel, osVersion);
    }

    @Override
    public void detectRta(AntiFraudContext context) {
        ThirdRtaRequest request = context.getThirdRtaRequest();
        String ua = request.getUa();
        String deviceModel = request.getModel();
        String osVersion = request.getOsVersion();

        detect(context, ua, deviceModel, osVersion);
    }

    private void detect(AntiFraudContext context, String ua, String deviceModel, String osVersion) {
        ClickDevicePool.StandardDeviceInfo standardDeviceInfo = context.getStandardInfoFuture().getResult().orElse(null);
        if (checkStandardDiffUa(standardDeviceInfo, ua)) {
            addFraudTag(context, AntiFraudTagEnum.STANDARD_DIFF_UA);
        }
        if (checkStandardDiffModel(standardDeviceInfo, deviceModel)) {
            addFraudTag(context, AntiFraudTagEnum.STANDARD_DIFF_MODEL);
        }
        if (checkStandardDiffOsv(standardDeviceInfo, osVersion)) {
            addFraudTag(context, AntiFraudTagEnum.STANDARD_DIFF_OSV);
        }
    }

    private boolean checkStandardDiffUa(ClickDevicePool.StandardDeviceInfo standardDeviceInfo, String ua) {
        if (standardDeviceInfo == null) {
            return false;
        }
        return !Objects.equals(standardDeviceInfo.getUa(), ua);
    }

    private boolean checkStandardDiffModel(ClickDevicePool.StandardDeviceInfo standardDeviceInfo, String model) {
        if (standardDeviceInfo == null) {
            return false;
        }
        return !Objects.equals(standardDeviceInfo.getModel(), model);
    }

    private boolean checkStandardDiffOsv(ClickDevicePool.StandardDeviceInfo standardDeviceInfo, String osv) {
        if (standardDeviceInfo == null) {
            return false;
        }
        if (StringUtils.isNotBlank(standardDeviceInfo.getOsv())) {
            if (StringUtils.isBlank(osv)) {
                return true;
            } else {
                //请求/点击的OSV低于标准池的OSV 才能判定为异常
                return CommonUtils.compareVersion(osv, standardDeviceInfo.getOsv()) < 0;
            }
        }
        return false;
    }

    private void addFraudTag(AntiFraudContext context, AntiFraudTagEnum antiFraudTagEnum) {
        context.getMarkFraudRuleIdSet().set(antiFraudTagEnum.getRuleId());
    }
}
