package com.youdao.ead.antifraud.detect;

import com.youdao.ead.antifraud.AntiFraudCentralDogmaConfig;
import com.youdao.ead.antifraud.AntiFraudContext;
import com.youdao.ead.antifraud.AntiFraudTagEnum;
import com.youdao.ead.dispatcher.ClickStrategy;
import com.youdao.ead.dto.ReporterClickDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import outfox.ead.data.DiffClickTimeType;

import java.util.List;
import java.util.Optional;

import static com.youdao.ead.constant.ChannelDidConstants.*;

/**
 * <AUTHOR>
 * @create 2024-12-04 14:52
 */
@Order
@Service
@RequiredArgsConstructor
@Slf4j
public class ActionFeaturesErrorDetector implements IFraudDetector {

    private final ClickStrategy clickStrategy;
    private final AntiFraudCentralDogmaConfig antiFraudCentralDogmaConfig;

    @Override
    public void detectClick(AntiFraudContext context) {
        ReporterClickDTO reporterClickDto = context.getReporterClickDto();
        //RTA点击时间异常
        //点击与rta请求间隔时间太近，则需要延迟上报
        Optional.ofNullable(reporterClickDto.getRtaBidTimestamp())
                .map(rtaBidTimestamp -> Optional.ofNullable(reporterClickDto.getTimestamp()).orElse(System.currentTimeMillis()) - rtaBidTimestamp)
                .filter(millsAfterRta -> millsAfterRta < 0 || millsAfterRta < clickStrategy.getMaxClickTimeDiffMills())
                .ifPresent(__ -> addFraudTag(context, AntiFraudTagEnum.RTA_CLICK_TIME_ERROR));

        AntiFraudCentralDogmaConfig.ConfigData configData = antiFraudCentralDogmaConfig.getConfigData();
        List<Long> v2List = configData.getDiffAidClickSponsorsOrActivityV2List();
        boolean diffAidClickV2 = configData.isDiffClick() &&
                (v2List.contains(reporterClickDto.getAid()) || v2List.contains(reporterClickDto.getSid()));
        if (diffAidClickV2) {
            //点击跨aid上报 - 新版
            String did = reporterClickDto.getChannelDid();
            if (StringUtils.isNotEmpty(did) && !did.equals(CHANNEL_DID_FOR_JSQ)) {
                if (context.getDiffClickFuture() != null) {
                    context.getDiffClickFuture().getResult().ifPresent(diffTsList -> {
                        if (diffTsList.size() == 4) {
                            if (diffTsList.get(0) <= antiFraudCentralDogmaConfig.getConfigData().getDiffAidClickTimeMs()) {
                                addFraudTag(context, AntiFraudTagEnum.ACTION_FEATURES_DIFF_SOURCE);
                            }
                        }
                    });
                }
            }
        } else {
            //点击跨aid上报
            Optional.of(reporterClickDto.getChannelDid())
                    .filter(channelDid -> !CHANNEL_DID_FOR_JSQ.equals(channelDid))
                    .filter(__ -> context.getActionFeaturesDiffSourceFuture().getResult().orElse(Boolean.FALSE))
                    .ifPresent(__ -> addFraudTag(context, AntiFraudTagEnum.ACTION_FEATURES_DIFF_SOURCE));
        }

        if (!CHANNEL_DID_FOR_JSQ.equals(reporterClickDto.getChannelDid())) {
            Optional<List<Long>> result = context.getDiffClickFuture().getResult();
            if (result.isPresent()) {
                List<Long> res = result.get();
                if (CollectionUtils.isNotEmpty(res) && res.size() == 4) {
                    AntiFraudContext.DiffClickTime diffClickTime = context.getDiffClickTime();
                    diffClickTime.setActivityIdTime(DiffClickTimeType.getDiffClickTime(res.get(0)));
                    diffClickTime.setSponsorIdTime(DiffClickTimeType.getDiffClickTime(res.get(1)));
                    diffClickTime.setChannelDidTime(DiffClickTimeType.getDiffClickTime(res.get(2)));
                    diffClickTime.setProductIdTime(DiffClickTimeType.getDiffClickTime(res.get(3)));
                }
            }
        }
    }

    @Override
    public void detectRta(AntiFraudContext context) {
        // 不涉及当前反作弊规则
    }

    private void addFraudTag(AntiFraudContext context, AntiFraudTagEnum antiFraudTagEnum) {
        context.getMarkFraudRuleIdSet().set(antiFraudTagEnum.getRuleId());
    }
}