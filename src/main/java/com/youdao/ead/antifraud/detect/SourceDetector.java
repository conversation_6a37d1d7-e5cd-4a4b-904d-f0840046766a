package com.youdao.ead.antifraud.detect;

import com.youdao.ead.antifraud.AntiFraudContext;
import com.youdao.ead.antifraud.AntiFraudFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/16
 */
@Order
@Service
@RequiredArgsConstructor
@Slf4j
public class SourceDetector implements IFraudDetector {

    @Autowired
    private AntiFraudFilter antiFraudFilter;

    @Override
    public void detectClick(AntiFraudContext context) {
        detectSource(context, context.getReporterClickDto().getUa());
    }

    @Override
    public void detectRta(AntiFraudContext context) {
        detectSource(context, context.getThirdRtaRequest().getUa());
    }


    public void detectSource(AntiFraudContext context, String ua) {
        if (StringUtils.isNotBlank(ua)) {
            for (Map.Entry<Long, List<String>> entry : antiFraudFilter.getSourceId2Keys().entrySet()) {
                for (String key : entry.getValue()) {
                    if (ua.contains(key)) {
                        context.setSourceId(entry.getKey());
                        return;
                    }
                }
            }
        }
    }
}
