package com.youdao.ead.antifraud;

import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.SharedMetricRegistries;
import com.codahale.metrics.Timer;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.TextFormat;
import com.youdao.ead.config.*;
import com.youdao.ead.dto.FutureWrapper;
import com.youdao.ead.dto.ReporterClickDTO;
import com.youdao.ead.service.MetricsService;
import com.youdao.ead.util.CommonUtils;
import com.youdao.ead.util.MetricUtils;
import com.youdao.ead.validator.ParamFilledValidator;
import com.youdao.ead.vo.request.ThirdRtaRequest;
import com.youdao.quipu.avro.schema.Caid;
import io.lettuce.core.KeyValue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.stereotype.Service;
import outfox.ead.dsp.protocol.youdao.Bid;
import outfox.ead.dsp.protocol.youdao.ClickDevicePool;
import outfox.ead.dsp.protocol.youdao.ThirdPartyClickStat;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;

import static com.youdao.ead.constant.ChannelDidConstants.*;
import static com.youdao.ead.service.MetricsService.REPORTER_METRICS;
import static com.youdao.ead.validator.ParamFilledValidator.PARAM_IP;

/**
 * 反作弊异步服务
 *
 * <AUTHOR>
 * @create 2024-12-10 15:57
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AntiFraudAsyncService {
    private final ParamFilledValidator paramFilledValidator;
    private final AntiFraudCentralDogmaConfig antiFraudCentralDogmaConfig;

    public static final String KEY_STANDARD_CLICK_KEROCKS = "STANDARD_CLICK_KVROCKS";
    public static final String KEY_STANDARD_RTA_KVROCKS = "STANDARD_RTA_KVROCKS";
    public static final String KEY_DEVICE_RELATED_KVROCKS = "DEVICE_RELATED_KVROCKS";
    public static final String KEY_DEVICE_RELATED_RTA_KVROCKS = "DEVICE_RELATED_RTA_KVROCKS";
    public static final String KEY_REPEAT_CLICK = "REPEAT_CLICK";
    public static final String KEY_DIFF_AID_CLICK = "DIFF_AID_CLICK";
    public static final String KEY_DIFF_CLICK = "DIFF_CLICK";
    public static final String KEY_IP_RATE_CLICK = "IP_RATE_CLICK";
    public static final FutureWrapper<ClickDevicePool.StandardDeviceInfo> STANDARD_INFO_WRAPPER_NULL = new FutureWrapper<>(null, 1, KEY_STANDARD_CLICK_KEROCKS);
    public static final FutureWrapper<List<ThirdPartyClickStat.DeviceRelatedInfo>> DEVICE_RELATED_INFO_NULL = new FutureWrapper<>(null, 1, KEY_DEVICE_RELATED_KVROCKS);
    public static final FutureWrapper<List<ThirdPartyClickStat.DeviceRelatedInfoRta>> DEVICE_RELATED_INFO_RTA_NULL = new FutureWrapper<>(null, 1, KEY_DEVICE_RELATED_RTA_KVROCKS);
    public static final FutureWrapper<Boolean> REPEAT_CLICK_NULL = new FutureWrapper<>(null, 1, KEY_REPEAT_CLICK);
    public static final FutureWrapper<Boolean> DIFF_AID_CLICK_NULL = new FutureWrapper<>(null, 1, KEY_DIFF_AID_CLICK);
    public static final FutureWrapper<List<Long>> DIFF_CLICK_NULL = new FutureWrapper<>(null, 1, KEY_DIFF_CLICK);
    public static final FutureWrapper<Boolean> IP_RATE_CLICK_NULL = new FutureWrapper<>(null, 1, KEY_IP_RATE_CLICK);

    private final AntiFraudRedisClient antiFraudRedisClient;
    private final DeviceForClickKvrocksClient deviceForClickKvrocksClient;
    private final DeviceForStandardKvrocksClient deviceForStandardKvrocksClient;
    private final DeviceForRtaKvrocksClient deviceForRtaKvrocksClient;
    private static final String DEVICE_FOR_CLICK_PREFIX_KEY = "p_";
    private static final String DEVICE_FOR_RTA_PREFIX_KEY = "r_";
    private static final String DEVICE_STANDARD_PREFIX_KEY = "s_";

    private final MetricRegistry metricRegistry = SharedMetricRegistries.getOrCreate(REPORTER_METRICS);
    private final Timer TIMER_STANDARD_CLICK_KVROCKS = metricRegistry.timer(MetricsService.name(AntiFraudAsyncService.class, KEY_STANDARD_CLICK_KEROCKS));
    private final Timer TIMER_STANDARD_RTA_KVROCKS = metricRegistry.timer(MetricsService.name(AntiFraudAsyncService.class, KEY_STANDARD_RTA_KVROCKS));
    private final Timer TIMER_DEVICE_RELATED_KVROCKS = metricRegistry.timer(MetricsService.name(AntiFraudAsyncService.class, KEY_DEVICE_RELATED_KVROCKS));
    private final Timer TIMER_DEVICE_RELATED_RTA_KVROCKS = metricRegistry.timer(MetricsService.name(AntiFraudAsyncService.class, KEY_DEVICE_RELATED_RTA_KVROCKS));
    private final Timer TIMER_REPEAT_CLICK = metricRegistry.timer(MetricsService.name(AntiFraudAsyncService.class, KEY_REPEAT_CLICK));
    private final Timer TIMER_DIFF_AID_CLICK = metricRegistry.timer(MetricsService.name(AntiFraudAsyncService.class, KEY_DIFF_AID_CLICK));
    private final Timer TIMER_DIFF_CLICK = metricRegistry.timer(MetricsService.name(AntiFraudAsyncService.class, KEY_DIFF_CLICK));
    private final Timer TIMER_IP_RATE_CLICK = metricRegistry.timer(MetricsService.name(AntiFraudAsyncService.class, KEY_IP_RATE_CLICK));

    private final Meter METER_STANDARD_CLICK_KVROCKS_ERROR = metricRegistry.meter(MetricsService.name(AntiFraudAsyncService.class, KEY_STANDARD_CLICK_KEROCKS + "_ERROR"));
    private final Meter METER_STANDARD_RTA_KVROCKS_ERROR = metricRegistry.meter(MetricsService.name(AntiFraudAsyncService.class, KEY_STANDARD_RTA_KVROCKS + "_ERROR"));
    private final Meter METER_DEVICE_RELATED_KVROCKS_ERROR = metricRegistry.meter(MetricsService.name(AntiFraudAsyncService.class, KEY_DEVICE_RELATED_KVROCKS + "_ERROR"));
    private final Meter METER_DEVICE_RELATED_RTA_KVROCKS_ERROR = metricRegistry.meter(MetricsService.name(AntiFraudAsyncService.class, KEY_DEVICE_RELATED_RTA_KVROCKS + "_ERROR"));
    private final Meter METER_REPEAT_CLICK_ERROR = metricRegistry.meter(MetricsService.name(AntiFraudAsyncService.class, KEY_REPEAT_CLICK + "_ERROR"));
    private final Meter METER_DIFF_AID_CLICK_ERROR = metricRegistry.meter(MetricsService.name(AntiFraudAsyncService.class, KEY_DIFF_AID_CLICK + "_ERROR"));
    private final Meter METER_DIFF_CLICK_ERROR = metricRegistry.meter(MetricsService.name(AntiFraudAsyncService.class, KEY_DIFF_CLICK + "_ERROR"));
    private final Meter METER_IP_RATE_CLICK_ERROR = metricRegistry.meter(MetricsService.name(AntiFraudAsyncService.class, KEY_IP_RATE_CLICK + "_ERROR"));

    /**
     * 全天平均每个实例qps大约一千。并发任务要翻四倍，1s处理4000个redis查询任务，交给200个并发处理，平均50ms处理完一个任务就足够了。
     */
    private final int corePoolSize = 400;

    private final int maxThreadPoolSize = 1600;

    private final int keepAliveMills = 60000;

    ThreadPoolExecutor executor = new ThreadPoolExecutor(
            corePoolSize, maxThreadPoolSize,
            keepAliveMills, TimeUnit.MILLISECONDS,
            new SynchronousQueue<>(),
            new BasicThreadFactory.Builder().namingPattern("click-service-load-data-%d").daemon(true).build(),
            new ThreadPoolExecutor.AbortPolicy());

    @PostConstruct
    public void init() {
        MetricUtils.threadPoolMetrics(AntiFraudAsyncService.class, "click-service-load-data-thread-pool", executor);
    }

    /**
     * 预加载数据
     *
     * @param context
     */
    public void preLoadDataRta(AntiFraudContext context) {
        if (!context.getThirdRtaRequest().isAntiFraudAsyncLoadDataOpen()) {
            return;
        }
        try {
            AntiFraudCentralDogmaConfig.ConfigData configData = antiFraudCentralDogmaConfig.getConfigData();
            tryPreLoadDataRta(context, configData);
        } catch (Exception e) {
            log.error("tryPreLoadDataRta failed. ", e);
        }

    }
    private void tryPreLoadDataRta(AntiFraudContext context, AntiFraudCentralDogmaConfig.ConfigData configData) {
        ThirdRtaRequest request = context.getThirdRtaRequest();
        Bid.BidRequest.Device.OS os = request.getOsEnum();
        List<Caid> caidList = request.getCaids();
        String idfa = request.getIdfa();
        String idfaMd5 = request.getIdfaMd5();
        String oaid = request.getOaid();
        String oaidMd5 = request.getOaidMd5();
        String imei = request.getImei();
        String imeiMd5 = request.getImeiMd5();
        Set<String> deviceIds = getAllDeviceIds(os, caidList, idfa, idfaMd5, oaid, oaidMd5, imei, imeiMd5);
        if (configData.isStandardInfoRta()) {
            context.setStandardInfoFuture(executeAsyncTask(() -> getStandardInfoRtaKvrocks(deviceIds), 80L, KEY_STANDARD_RTA_KVROCKS, STANDARD_INFO_WRAPPER_NULL));
        }
        if (configData.isDeviceRelatedInfoRta()) {
            context.setDeviceRelatedInfoRtaFuture(executeAsyncTask(() -> getDeviceRelatedInfoRtaKvrocks(deviceIds), 200L, KEY_DEVICE_RELATED_RTA_KVROCKS, DEVICE_RELATED_INFO_RTA_NULL));
        }

    }

    /**
     * 预加载数据
     *
     * @param antiFraudContext
     */
    public void preLoadData(AntiFraudContext antiFraudContext) {
        try {
            double random = ThreadLocalRandom.current().nextDouble();
            AntiFraudCentralDogmaConfig.ConfigData configData = antiFraudCentralDogmaConfig.getConfigData();
            if (random <= configData.getPreLoadDataRate()) {
                tryPreLoadData(antiFraudContext, configData);
            }
        } catch (Exception e) {
            log.error("tryPreLoadData failed. ", e);
        }
    }
    private void tryPreLoadData(AntiFraudContext antiFraudContext, AntiFraudCentralDogmaConfig.ConfigData configData) {
        ReporterClickDTO clickDto = antiFraudContext.getReporterClickDto();
        Bid.BidRequest.Device.OS os = clickDto.getOs();
        List<Caid> caidList = clickDto.getCaids();
        String idfa = clickDto.getIdfa();
        String idfaMd5 = clickDto.getIdfaMd5();
        String oaid = clickDto.getOaid();
        String oaidMd5 = clickDto.getOaidMd5();
        String imei = clickDto.getImei();
        Set<String> deviceIds = getAllDeviceIds(os, caidList, idfa, idfaMd5, oaid, oaidMd5, "", imei);
        if (configData.isStandardInfo()) {
            antiFraudContext.setStandardInfoFuture(executeAsyncTask(() -> getStandardInfoClickKvrocks(deviceIds), 80L, KEY_STANDARD_CLICK_KEROCKS, STANDARD_INFO_WRAPPER_NULL));
        }
        if (configData.isDeviceRelatedInfo()) {
            antiFraudContext.setDeviceRelatedInfoFuture(executeAsyncTask(() -> getDeviceRelatedInfoKvrocks(deviceIds), 80L, KEY_DEVICE_RELATED_KVROCKS, DEVICE_RELATED_INFO_NULL));
        }
        if (configData.isRepeatClick()) {
            antiFraudContext.setIsRepeatClickFuture(executeAsyncTask(() -> repeatClick(deviceIds, clickDto.getAid()), 40L, KEY_REPEAT_CLICK, REPEAT_CLICK_NULL));
        }
        if (configData.isActionFeaturesDiffSource()) {
            if (!CHANNEL_DID_FOR_JSQ.equals(clickDto.getChannelDid())) {
                antiFraudContext.setActionFeaturesDiffSourceFuture(
                        executeAsyncTask(() -> diffAidClick(deviceIds, clickDto.getAid()), 40L, KEY_DIFF_AID_CLICK, DIFF_AID_CLICK_NULL)
                );
            }
        }
        if (configData.isDiffClick()) {
            if (!CHANNEL_DID_FOR_JSQ.equals(clickDto.getChannelDid())) {
                Long aid = clickDto.getAid();
                Long sid = clickDto.getSid();
                String did = clickDto.getChannelDid();
                // todo 产品id
                String pid = "";
                antiFraudContext.setDiffClickFuture(
                        executeAsyncTask(() -> diffClick(deviceIds, aid, sid, did, pid), 40L, KEY_DIFF_CLICK, DIFF_CLICK_NULL)
                );
            }
        }
        if (configData.isActionRateIp()) {
            if (paramFilledValidator.checkParamFilled(PARAM_IP, clickDto.getIp())) {
                antiFraudContext.setActionRateIpFuture(
                        executeAsyncTask(() -> ipRateClick(clickDto.getIp(), clickDto.getAid()), 40L, KEY_IP_RATE_CLICK, IP_RATE_CLICK_NULL)
                );
            }
        }
    }

    private ClickDevicePool.StandardDeviceInfo getStandardInfoClickKvrocks(Set<String> deviceIds) {
        try (Timer.Context ignored = TIMER_STANDARD_CLICK_KVROCKS.time()) {
            return getStandardInfo(deviceIds, deviceForStandardKvrocksClient);
        } catch (Throwable e) {
            METER_STANDARD_CLICK_KVROCKS_ERROR.mark();
            return null;
        }
    }

    private ClickDevicePool.StandardDeviceInfo getStandardInfoRtaKvrocks(Set<String> deviceIds) {
        try (Timer.Context ignored = TIMER_STANDARD_RTA_KVROCKS.time()) {
            return getStandardInfo(deviceIds, deviceForStandardKvrocksClient);
        } catch (Throwable e) {
            METER_STANDARD_RTA_KVROCKS_ERROR.mark();
            return null;
        }
    }

    private ClickDevicePool.StandardDeviceInfo getStandardInfo(Set<String> deviceIds,DeviceForStandardAbstractClient deviceForStandardAbstractClient) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return null;
        }

        String[] keys = deviceIds.stream()
                .map(DEVICE_STANDARD_PREFIX_KEY::concat)
                .toArray(String[]::new);
        List<KeyValue<String, byte[]>> keyValues = deviceForStandardAbstractClient.getSyncCommands().mget(keys);

        return keyValues.stream()
                .filter(kv -> Objects.nonNull(kv) && Objects.nonNull(kv.getKey()) && kv.hasValue())
                .map(kv -> {
                    try {
                        ClickDevicePool.StandardDeviceInfo standardDeviceInfo = ClickDevicePool.StandardDeviceInfo.parseFrom(kv.getValue());
                        return standardDeviceInfo;
                    } catch (InvalidProtocolBufferException e) {
                        log.warn("Failed to parse proto buffer for key {}: ", kv.getKey(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .reduce((a, b) -> a.getTimestamp() > b.getTimestamp() ? a : b)
                .orElse(null);
    }

    private List<ThirdPartyClickStat.DeviceRelatedInfo> getDeviceRelatedInfoKvrocks(Set<String> deviceIds) {
        try (Timer.Context ignored = TIMER_DEVICE_RELATED_KVROCKS.time()) {
            if (CollectionUtils.isEmpty(deviceIds)) {
                return Collections.emptyList();
            }
            List<ThirdPartyClickStat.DeviceRelatedInfo> deviceRelatedInfos = new ArrayList<>();

            String[] keys = deviceIds.stream()
                    .map(DEVICE_FOR_CLICK_PREFIX_KEY::concat)
                    .toArray(String[]::new);
            List<KeyValue<String, byte[]>> keyValues = deviceForClickKvrocksClient.getSyncCommands().mget(keys);

            keyValues.stream()
                    .filter(kv -> Objects.nonNull(kv) && Objects.nonNull(kv.getKey()) && kv.hasValue())
                    .forEach(kv -> {
                        try {
                            deviceRelatedInfos.add(ThirdPartyClickStat.DeviceRelatedInfo.parseFrom(kv.getValue()));
                        } catch (InvalidProtocolBufferException e) {
                            log.error("Failed to parse proto buffer for key {}: ", kv.getKey());
                        }
                    });

            return deviceRelatedInfos;
        } catch (Throwable e) {
            METER_DEVICE_RELATED_KVROCKS_ERROR.mark();
            return Collections.emptyList();
        }
    }

    private List<ThirdPartyClickStat.DeviceRelatedInfoRta> getDeviceRelatedInfoRtaKvrocks(Set<String> deviceIds) {
        try (Timer.Context ignored = TIMER_DEVICE_RELATED_RTA_KVROCKS.time()) {
            if (CollectionUtils.isEmpty(deviceIds)) {
                return Collections.emptyList();
            }
            List<ThirdPartyClickStat.DeviceRelatedInfoRta> deviceRelatedInfoRtas = new ArrayList<>();

            String[] keys = deviceIds.stream()
                    .map(DEVICE_FOR_RTA_PREFIX_KEY::concat)
                    .toArray(String[]::new);
            List<KeyValue<String, byte[]>> keyValues = deviceForRtaKvrocksClient.getSyncCommands().mget(keys);

            keyValues.stream()
                    .filter(kv -> Objects.nonNull(kv) && Objects.nonNull(kv.getKey()) && kv.hasValue())
                    .forEach(kv -> {
                        try {
                            deviceRelatedInfoRtas.add(ThirdPartyClickStat.DeviceRelatedInfoRta.parseFrom(kv.getValue()));
                        } catch (InvalidProtocolBufferException e) {
                            log.error("Failed to parse proto buffer for key {}: ", kv.getKey());
                        }
                    });
            return deviceRelatedInfoRtas;
        } catch (Throwable e) {
            METER_DEVICE_RELATED_RTA_KVROCKS_ERROR.mark();
            return Collections.emptyList();
        }
    }

    private Boolean repeatClick(Set<String> deviceIds, Long aid) {
        try (Timer.Context ignored = TIMER_REPEAT_CLICK.time()) {
            return antiFraudRedisClient.isRepeatClick(deviceIds, aid);
        } catch (Throwable e) {
            METER_REPEAT_CLICK_ERROR.mark();
            return false;
        }
    }

    private Boolean diffAidClick(Set<String> deviceIds, Long aid) {
        try (Timer.Context ignored = TIMER_DIFF_AID_CLICK.time()) {
            return antiFraudRedisClient.isDiffAidClick(deviceIds, aid);
        } catch (Throwable e) {
            METER_DIFF_AID_CLICK_ERROR.mark();
            return false;
        }
    }

    private List<Long> diffClick(Set<String> deviceIds, Long aid, Long sponsorId, String channelDid, String productId) {
        try (Timer.Context ignored = TIMER_DIFF_CLICK.time()) {
            return antiFraudRedisClient.isDiffClick(deviceIds, aid, sponsorId, channelDid, productId);
        } catch (Throwable e) {
            METER_DIFF_CLICK_ERROR.mark();
            return Collections.emptyList();
        }
    }

    private Boolean ipRateClick(String ip, Long aid) {
        try (Timer.Context ignored = TIMER_IP_RATE_CLICK.time()) {
            return antiFraudRedisClient.isIpRateClick(ip, aid);
        } catch (Throwable e) {
            METER_IP_RATE_CLICK_ERROR.mark();
            return false;
        }
    }

    private <T> FutureWrapper<T> executeAsyncTask(Callable<T> task, long timeoutMs, String key, FutureWrapper<T> defaultFuture) {
        try {
            FutureTask<T> futureTask = new FutureTask<>(task);
            executor.submit(futureTask);
            return new FutureWrapper<>(futureTask, timeoutMs, key);
        } catch (Throwable e) {
            log.error("executeAsyncTask submit failed. key={}", key);
        }
        return defaultFuture;
    }

    /**
     * 获取此次请求携带的所有设备号（大写 MD5 值）
     */
    private Set<String> getAllDeviceIds(Bid.BidRequest.Device.OS os, List<Caid> caidList, String idfa, String idfaMd5, String oaid, String oaidMd5, String imei, String imeiMd5) {
        Set<String> deviceIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(caidList)) {
            Set<String> caidMd5s = CommonUtils.getCaidMd5s(caidList);
            for (String caidMd5 : caidMd5s) {
                if (paramFilledValidator.checkParamFilled(ParamFilledValidator.PARAM_CAID_MD5, caidMd5)) {
                    deviceIds.add(caidMd5.toUpperCase());
                }
            }
        }
        if (paramFilledValidator.checkParamFilled(ParamFilledValidator.PARAM_IDFA, idfa)) {
            deviceIds.add(DigestUtils.md5Hex(idfa).toUpperCase());
        }
        if (paramFilledValidator.checkParamFilled(ParamFilledValidator.PARAM_IDFA_MD5, idfaMd5)) {
            deviceIds.add(idfaMd5.toUpperCase());
        }
        if (paramFilledValidator.checkParamFilled(ParamFilledValidator.PARAM_OAID, oaid)) {
            deviceIds.add(DigestUtils.md5Hex(oaid).toUpperCase());
        }
        if (paramFilledValidator.checkParamFilled(ParamFilledValidator.PARAM_OAID_MD5, oaidMd5)) {
            deviceIds.add(oaidMd5.toUpperCase());
        }
        if (paramFilledValidator.checkParamFilled(ParamFilledValidator.PARAM_IMEI_MD5, imei)) {
            deviceIds.add(CommonUtils.generateMd5HexUpperForImei(imei));
        }
        if (paramFilledValidator.checkParamFilled(ParamFilledValidator.PARAM_IMEI_MD5, imeiMd5)) {
            deviceIds.add(CommonUtils.generateMd5HexUpperForImei(imeiMd5));
        }
        return deviceIds;
    }

}
