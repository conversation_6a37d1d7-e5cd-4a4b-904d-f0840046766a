package com.youdao.ead.antifraud;

import com.youdao.quipu.avro.schema.ThirdPartyClick;

import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2024/11/20
 */
public enum AntiFraudTagParentEnum {

    /**
     * 异常
     */
    EXCEPTION(127, 1, ThirdPartyClick.Builder::setExceptionMark, ThirdPartyClick.Builder::setExceptionDeal, ThirdPartyClick.Builder::setExceptionSend),
//
//    /**
//     * 异常参数
//     */
//    EXCEPTION_PARAM(2, ThirdPartyClick.Builder::setExceptionParamMark, ThirdPartyClick.Builder::setExceptionParamDeal, ThirdPartyClick.Builder::setExceptionParamSend),
//
//    /**
//     * 行为异常
//     */
//    EXCEPTION_ACTION(2, ThirdPartyClick.Builder::setExceptionActionMark, ThirdPartyClick.Builder::setExceptionActionDeal, ThirdPartyClick.Builder::setExceptionActionSend),
//
//    /**
//     * 参数间关联异常
//     */
//    EXCEPTION_RELATED_PARAM(2, ThirdPartyClick.Builder::setExceptionRelatedParamMark, ThirdPartyClick.Builder::setExceptionRelatedParamDeal, ThirdPartyClick.Builder::setExceptionRelatedParamSend),
//
//    /**
//     * 行为特征异常
//     */
//    ACTION_FEATURES_ERROR_MARK(3, ThirdPartyClick.Builder::setActionFeaturesErrorMark, ThirdPartyClick.Builder::setActionFeaturesErrorDeal, ThirdPartyClick.Builder::setActionFeaturesErrorSend),
//
//    /**
//     * 行为频次异常
//     */
//    ACTION_RATE_ERROR(3, ThirdPartyClick.Builder::setActionRateErrorMark, ThirdPartyClick.Builder::setActionRateErrorDeal, ThirdPartyClick.Builder::setActionRateErrorSend),
//
//    /**
//     * 设备参数间不一致
//     */
//    RELATED_PARAM_UNEXPECTED(3, ThirdPartyClick.Builder::setRelatedParamUnexpectedMark, ThirdPartyClick.Builder::setRelatedParamUnexpectedDeal, ThirdPartyClick.Builder::setRelatedParamUnexpectedSend),
//
//    /**
//     * 设备参数间关联异常
//     */
//    RELATED_MULTI_VALUE(3, ThirdPartyClick.Builder::setRelatedMultiValueMark, ThirdPartyClick.Builder::setRelatedMultiValueDeal, ThirdPartyClick.Builder::setRelatedMultiValueSend),
//
//    /**
//     * 参数取值异常
//     */
//    PARAM_VALUE_ERROR(3, ThirdPartyClick.Builder::setParamValueErrorMark, ThirdPartyClick.Builder::setParamValueErrorDeal, ThirdPartyClick.Builder::setParamValueErrorSend),
//
//    /**
//     * 参数格式异常
//     */
//    PARAM_FORMAT_ERROR(3, ThirdPartyClick.Builder::setParamFormatErrorMark, ThirdPartyClick.Builder::setParamFormatErrorDeal, ThirdPartyClick.Builder::setParamFormatErrorSend),
//
//    /**
//     * 请求和点击参数不一致
//     */
//    RELATED_RTA_CHANGE(3, ThirdPartyClick.Builder::setRelatedRtaChangeMark, ThirdPartyClick.Builder::setRelatedRtaChangeDeal, ThirdPartyClick.Builder::setRelatedRtaChangeSend)

    ;

    private final int id;
    private final int level;

    /**
     * 点击维度上 识别、处理、上报 分别对应的更新统计字段的方法，
     */
    private final BiConsumer<ThirdPartyClick.Builder, Integer> markSetter;
    private final BiConsumer<ThirdPartyClick.Builder, Integer> dealSetter;
    private final BiConsumer<ThirdPartyClick.Builder, Integer> sendSetter;

    AntiFraudTagParentEnum(int id,
                           int level,
                           BiConsumer<ThirdPartyClick.Builder, Integer> markSetter,
                           BiConsumer<ThirdPartyClick.Builder, Integer> dealSetter,
                           BiConsumer<ThirdPartyClick.Builder, Integer> sendSetter) {
        this.id = id;
        this.level = level;
        this.markSetter = markSetter;
        this.dealSetter = dealSetter;
        this.sendSetter = sendSetter;
    }

    public int getId() {
        return id;
    }

    public BiConsumer<ThirdPartyClick.Builder, Integer> getMarkSetter() {
        return markSetter;
    }

    public BiConsumer<ThirdPartyClick.Builder, Integer> getDealSetter() {
        return dealSetter;
    }

    public BiConsumer<ThirdPartyClick.Builder, Integer> getSendSetter() {
        return sendSetter;
    }
}
