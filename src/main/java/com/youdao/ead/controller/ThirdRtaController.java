package com.youdao.ead.controller;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.codahale.metrics.Timer;
import com.youdao.ead.constant.ErrorCodes;
import com.youdao.ead.constant.HonorRtaConstants;
import com.youdao.ead.exception.BizException;
import com.youdao.ead.service.BasicDataService;
import com.youdao.ead.service.rta.RtaApiService;
import com.youdao.ead.service.rta.ThirdRtaConfig;
import com.youdao.ead.service.rta.ThirdRtaService;
import com.youdao.ead.service.rta.selector.RtaApiConfig;
import com.youdao.ead.service.rta.selector.RtaApiEntry;
import com.youdao.ead.service.rta.selector.RtaSelectorHolder;
import com.youdao.ead.util.*;
import com.youdao.ead.vo.request.*;
import com.youdao.ead.vo.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import outfox.ead.data.rta.BaiduRtaMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ValidationException;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.youdao.ead.constant.BidStatus.BID_STATUS;
import static com.youdao.ead.constant.BidStatus.NO_BID_STATUS;
import static com.youdao.ead.constant.ChannelDidConstants.*;
import static com.youdao.ead.constant.PlatformCodes.*;
import static com.youdao.ead.constant.XiaomiRtaConstants.*;
import static com.youdao.ead.util.SentinelUtils.getChannelRtaResourceName;

/**
 * 第三方rta投放服务<br/>
 * RTA相关概念参见<a href="https://confluence.inner.youdao.com/pages/viewpage.action?pageId=281834992">rta梳理</a>
 *
 * <AUTHOR>
 * @date 2022/11/10.
 */
@RestController
@Slf4j
@RequestMapping("/rta-promotion")
@RequiredArgsConstructor
public class ThirdRtaController {

    private final RtaSelectorHolder rtaSelectorHolder;

    private final ThirdRtaService thirdRtaService;

    private final BasicDataService basicDataService;

    private final HuaweiRtaRequestUtils huaweiRtaRequestUtils;

    private final HonorRtaRequestUtils honorRtaRequestUtils;

    private final WifiRtaRequestUtils wifiRtaRequestUtils;

    private final RtaApiService rtaApiService;

    @Value("${oppo.rta.secretKey}")
    private String OPPO_RTA_SECRET_KEY;


    @RequestMapping(method = RequestMethod.GET)
    public ThirdRtaResponse rtaPromotion(ThirdRtaRequest request) {
        return this.rtaPromotion(DEFAULT, request);
    }

    @RequestMapping(method = RequestMethod.GET, path = "/api/{channel}")
    public ThirdRtaResponse rtaPromotion(@PathVariable("channel") String channel, ThirdRtaRequest request) {
        String generatedReqId = UUID.randomUUID().toString();
        try (Timer.Context ignore = MetricUtils.getThirdRtaTimer(channel).time(); Entry entry = SphU.entry(getChannelRtaResourceName(channel), EntryType.IN)) {
            if (!thirdRtaService.isChannelCodeValid(channel)) {
                throw new BizException("channel " + channel + " NOT VALID");
            }
            request.setUa(CommonUtils.standardizeUserAgent(request.getUa()));
            request.setChannel(channel);
            rtaSelectorHolder.checkActivity(request.getAids());
            if (basicDataService.shouldGenerateCaid(request.getAids())) {
                request.setCaids(CommonUtils.convAvroCaidList(request.getCaid(), request.getCaidMd5(), request.getDeviceInfo()));
            } else {
                request.setCaids(Collections.emptyList());
            }
            List<Long> bidAids = ListUtils.removeAll(request.getAids(), thirdRtaService.getFilteredAids(request, generatedReqId));
            ThirdRtaResponse.Result result = new ThirdRtaResponse.Result();
            result.setReqId(generatedReqId);
            result.setAids(new HashSet<>(bidAids));
            result.setBidStatus(CollectionUtils.isNotEmpty(bidAids) ? BID_STATUS : NO_BID_STATUS);
            return ThirdRtaResponse.of(result);
        } catch (DegradeException degradeException) {
            MetricUtils.getChannelDegradeMeter(channel).mark();
            ThirdRtaResponse.Result result = new ThirdRtaResponse.Result();
            result.setReqId(generatedReqId);
            result.setAids(Collections.emptySet());
            result.setBidStatus(NO_BID_STATUS);
            return ThirdRtaResponse.of(result);
        } catch (BlockException blockException) {
            MetricUtils.getChannelBlockMeter(channel).mark();
            return ThirdRtaResponse.exception(ErrorCodes.BIZ_ERROR, "FLOW CONTROL.");
        } catch (Exception e) {
            if (e instanceof BizException) {
                log.warn("rta request error: {}", e.getMessage());
                return ThirdRtaResponse.exception(ErrorCodes.BIZ_ERROR, e.getMessage());
            } else {
                log.error("rta request error!", e);
                return ThirdRtaResponse.exception(ErrorCodes.INTERNAL_ERROR, "server exception!");
            }
        }
    }

    @PostMapping("/mi/1.0")
    public XiaomiRtaResponse xiaomiRta(@RequestBody XiaomiRtaRequest xiaomiRtaRequest) {
        String generatedReqId = UUID.randomUUID().toString();
        try (Timer.Context ignore = MetricUtils.getThirdRtaTimer(XIAOMI).time(); Entry entry = SphU.entry(getChannelRtaResourceName(XIAOMI), EntryType.IN)) {
            XiaomiRtaRequestUtils.validate(xiaomiRtaRequest);
            Set<String> tokens = xiaomiRtaRequest.getAdReqs().stream()
                    .map(XiaomiRtaRequest.AdReq::getToken)
                    .collect(Collectors.toSet());
            Map<String, Set<Long>> token2Aids = rtaSelectorHolder.getAidsByXiaomiRtaToken(tokens);
            if (MapUtils.isEmpty(token2Aids)) {
                throw new BizException("token未配置");
            }
            Set<Long> aids = CommonUtils.flatCollections(token2Aids.values());
            List<ThirdRtaConfig> thirdRtaConfigs = rtaSelectorHolder.getThirdRtaConfig(new ArrayList<>(aids));
            if (CollectionUtils.isEmpty(thirdRtaConfigs)) {
                throw new BizException("所有token均未开启RTA");
            }
            ThirdRtaRequest request = XiaomiRtaRequestUtils.xiaomiRtaReq2ThirdRtaRequest(xiaomiRtaRequest, aids);
            Set<Long> filteredAids = new HashSet<>(thirdRtaService.getFilteredAids(thirdRtaConfigs, request, generatedReqId));
            thirdRtaService.bindReqId2Device(request, generatedReqId, filteredAids, thirdRtaConfigs);
            List<XiaomiRtaResponse.BidResponse.AdResp> bidResponses = new ArrayList<>();
            // 如果该token有AID被过滤了，那么不进行竞价
            tokens.forEach(item -> {
                if (token2Aids.containsKey(item) && RtaSelectorHolder.isAnyActivityEnableRta(thirdRtaConfigs, token2Aids.get(item))) {
                    bidResponses.add(new XiaomiRtaResponse.BidResponse.AdResp(item,
                            CollectionUtils.containsAny(filteredAids, token2Aids.get(item)) ? BID_REJECT : BID_ACCEPT)
                    );
                } else {
                    log.warn("token: {} from xiaomi not configuration in any actions or no actions configuration RTA, requestId: {}",
                            item, xiaomiRtaRequest.getId());
                    bidResponses.add(new XiaomiRtaResponse.BidResponse.AdResp(item, BID_REJECT));
                }
            });
            XiaomiRtaResponse.BidResponse bidResponse = XiaomiRtaResponse.BidResponse.builder()
                    .id(xiaomiRtaRequest.getId())
                    .bidId(generatedReqId)
                    .resType(RESPONSE_TYPE_FLOW_PICKING)
                    .adResps(bidResponses)
                    .build();
            return new XiaomiRtaResponse(NORMAL_RESPONSE, "success", bidResponse);
        } catch (DegradeException degradeException) {
            MetricUtils.getChannelDegradeMeter(XIAOMI).mark();
            XiaomiRtaResponse.BidResponse bidResponse = XiaomiRtaResponse.BidResponse.builder()
                    .id(xiaomiRtaRequest.getId())
                    .bidId(generatedReqId)
                    .resType(RESPONSE_TYPE_FLOW_PICKING)
                    .adResps(
                            xiaomiRtaRequest.getAdReqs()
                                    .stream()
                                    .map(req -> new XiaomiRtaResponse.BidResponse.AdResp(req.getToken(), BID_REJECT))
                                    .collect(Collectors.toList())
                    )
                    .build();
            return new XiaomiRtaResponse(NORMAL_RESPONSE, "success", bidResponse);
        } catch (BlockException blockException) {
            MetricUtils.getChannelBlockMeter(XIAOMI).mark();
            return XiaomiRtaResponse.exception("FLOW CONTROL.", xiaomiRtaRequest.getId());
        } catch (Exception e) {
            if (e instanceof BizException || e instanceof ValidationException) {
                log.warn("xiaomi rta api caught expected exception, {}", e.getMessage());
                return XiaomiRtaResponse.exception(e.getMessage(), xiaomiRtaRequest.getId());
            } else {
                log.error("xiaomi rta api ERROR!", e);
                return XiaomiRtaResponse.exception("system error.", xiaomiRtaRequest.getId());
            }
        }
    }

    @PostMapping({"/huawei", "/huawei/{api-code}"})
    public HuaweiRtaResponse huaweiRta(@PathVariable(name = "api-code", required = false) String apiCode, @RequestBody HuaweiRtaRequest huaweiRtaRequest) {
        try (Timer.Context ignore = MetricUtils.getThirdRtaTimer(HUAWEI).time(); Entry entry = SphU.entry(getChannelRtaResourceName(HUAWEI), EntryType.IN)) {
            // huawei支持动态添加rta接口, 映射上游接口, 处理不同产品投放的流量, 且在请求粒度上隔离
            checkApiCode(apiCode, TDP_PLATFORM_HUAWEI);
            HuaweiRtaRequestUtils.validate(huaweiRtaRequest);
            Set<String> rtaIdUnionUserList = HuaweiRtaRequestUtils.mergeRtaIdListAndUserList(huaweiRtaRequest.getRtaIdList(), huaweiRtaRequest.getUserList());
            Map<String, Set<Long>> rtaId2Aids = rtaSelectorHolder.getAidsByHuaweiRtaIds(rtaIdUnionUserList);
            if (MapUtils.isEmpty(rtaId2Aids)) {
                throw new BizException("所有rtaId均未配置");
            }
            Set<Long> aids = CommonUtils.flatCollections(rtaId2Aids.values());
            List<ThirdRtaConfig> thirdRtaConfigs = rtaSelectorHolder.getThirdRtaConfig(new ArrayList<>(aids));
            if (CollectionUtils.isEmpty(thirdRtaConfigs)) {
                throw new BizException("所有rtaId均未开启RTA");
            }
            ThirdRtaRequest thirdRtaRequest = huaweiRtaRequestUtils.huaweiRtaReq2ThirdRtaRequest(huaweiRtaRequest, aids);
            List<Long> filteredAids = thirdRtaService.getFilteredAids(thirdRtaConfigs, thirdRtaRequest, thirdRtaRequest.getReqId());
            Set<String> biddingRtaIds = new HashSet<>();
            Set<String> biddingUserList = new HashSet<>();
            extractRtaIdAndUserList(huaweiRtaRequest, rtaIdUnionUserList, rtaId2Aids, thirdRtaConfigs, filteredAids, biddingRtaIds, biddingUserList);
            return HuaweiRtaResponse.getNormalResponse(biddingRtaIds, biddingUserList);
        } catch (DegradeException degradeException) {
            MetricUtils.getChannelDegradeMeter(HUAWEI).mark();
            return HuaweiRtaResponse.getNormalResponse(Collections.emptySet(), Collections.emptySet());
        } catch (BlockException blockException) {
            MetricUtils.getChannelBlockMeter(HUAWEI).mark();
            return HuaweiRtaResponse.exception("FLOW CONTROL.");
        } catch (BizException | ValidationException e) {
            log.warn("huawei rta api caught expected exception,{}", e.getMessage());
            return HuaweiRtaResponse.exception(e.getMessage());
        } catch (Exception e) {
            log.error("huawei rta api ERROR!", e);
            return HuaweiRtaResponse.exception("system error.");
        }
    }

    private void checkApiCode(String apiCode, String tdpPlatForm) {
        if (StringUtils.isNotBlank(apiCode)) {
            RtaApiEntry rtaApiEntry = rtaApiService.getRtaApiEntry(apiCode, tdpPlatForm);
            if (rtaApiEntry == null) {
                throw new BizException("illegal api code: " + apiCode);
            }
        }
    }

    private static void extractRtaIdAndUserList(HuaweiRtaRequest huaweiRtaRequest,
                                                Set<String> rtaIdUnionUserList,
                                                Map<String, Set<Long>> rtaId2Aids,
                                                List<ThirdRtaConfig> thirdRtaConfig,
                                                List<Long> filteredAids,
                                                Set<String> biddingRtaIds,
                                                Set<String> biddingUserList) {
        rtaIdUnionUserList.forEach(rtaId -> {
            if (rtaId2Aids.containsKey(rtaId) && RtaSelectorHolder.isAnyActivityEnableRta(thirdRtaConfig, rtaId2Aids.get(rtaId))) {
                if (!CollectionUtils.containsAny(filteredAids, rtaId2Aids.get(rtaId))) {
                    if (CollectionUtils.isNotEmpty(huaweiRtaRequest.getRtaIdList()) && huaweiRtaRequest.getRtaIdList().contains(rtaId)) {
                        biddingRtaIds.add(rtaId);
                    }
                    if (CollectionUtils.isNotEmpty(huaweiRtaRequest.getUserList()) && huaweiRtaRequest.getUserList().contains(rtaId)) {
                        biddingUserList.add(rtaId);
                    }
                }
            } else {
                log.warn("rtaId: {} from huawei not configuration in any actions or no actions configuration RTA, requestId: {}",
                        rtaId, huaweiRtaRequest.getRequestId());
            }
        });
    }

    @PostMapping({"/honor","/honor/{api-code}"})
    public HonorRtaResponse honorRta(@PathVariable(name = "api-code", required = false) String apiCode,  @RequestBody HonorRtaRequest honorRtaRequest) {
        try (Timer.Context ignore = MetricUtils.getThirdRtaTimer(StringUtils.isNotBlank(apiCode) ? HONOR + "@" + apiCode : HONOR).time(); Entry entry = SphU.entry(getChannelRtaResourceName(HONOR), EntryType.IN)) {
            HonorRtaRequestUtils.validate(honorRtaRequest);
            checkApiCode(apiCode, TDP_PLATFORM_HONOR);
            Set<String> rtaIdSet = new HashSet<>(honorRtaRequest.getRtaIdList());
            Map<String, Set<Long>> rtaId2Aids = rtaSelectorHolder.getAidsByHonorRtaIds(rtaIdSet);
            if (MapUtils.isEmpty(rtaId2Aids)) {
                throw new BizException("所有rtaId均未配置");
            }
            Set<Long> aids = CommonUtils.flatCollections(rtaId2Aids.values());
            List<ThirdRtaConfig> thirdRtaConfig = rtaSelectorHolder.getThirdRtaConfig(new ArrayList<>(aids));
            if (CollectionUtils.isEmpty(thirdRtaConfig)) {
                throw new BizException("所有rtaId均未开启RTA");
            }
            ThirdRtaRequest thirdRtaRequest = honorRtaRequestUtils.honorRtaReq2ThirdRtaRequest(honorRtaRequest, aids);
            List<Long> filteredAids = thirdRtaService.getFilteredAids(thirdRtaConfig, thirdRtaRequest, thirdRtaRequest.getReqId());

            Set<String> biddingRtaIds = new HashSet<>();

            rtaIdSet.forEach(rtaId -> {
                // 如果该rtaId没有对应的aid 或者 有对应的aid，但这些aid没有所对应的广告主方的rta配置，那么不参与竞价
                if (rtaId2Aids.containsKey(rtaId) && RtaSelectorHolder.isAnyActivityEnableRta(thirdRtaConfig, rtaId2Aids.get(rtaId))) {
                    // 如果该rtaId有aid被过滤了，那么不参与竞价
                    if (!CollectionUtils.containsAny(filteredAids, rtaId2Aids.get(rtaId))) {
                        biddingRtaIds.add(rtaId);
                    }
                } else {
                    log.warn("rtaId: {} from honor not configuration in any actions or no actions configuration RTA, requestId: {}",
                            rtaId, honorRtaRequest.getRequestId());
                }
            });
            List<HonorRtaResponse.RtaInfo> rtaInfoList = new ArrayList<>();
            for (String biddingRtaId : biddingRtaIds) {
                HonorRtaResponse.RtaInfo rtaInfo = HonorRtaResponse.RtaInfo.builder().rtaId(biddingRtaId).priceRate(1).build();
                rtaInfoList.add(rtaInfo);
            }
            HonorRtaResponse.UserInfo userInfo = HonorRtaResponse
                    .UserInfo
                    .builder()
                    .rtaIdList(new ArrayList<>(biddingRtaIds))
                    .rtaInfoList(rtaInfoList)
                    .build();
            return HonorRtaResponse
                    .builder()
                    .code(HonorRtaConstants.NORMAL_RESPONSE)
                    .message(StringUtils.EMPTY)
                    // 是否对当前用户感兴趣，我的理解是，愿意出价就是感兴趣
                    .interested(CollectionUtils.isEmpty(biddingRtaIds) ? HonorRtaConstants.INTERESTED_N : HonorRtaConstants.INTERESTED_Y)
                    .userInfo(userInfo)
                    .build();
        } catch (DegradeException degradeException) {
            MetricUtils.getChannelDegradeMeter(HONOR).mark();
            return HonorRtaResponse
                    .builder()
                    .code(HonorRtaConstants.NORMAL_RESPONSE)
                    .message(StringUtils.EMPTY)
                    .interested(HonorRtaConstants.INTERESTED_N)
                    .userInfo(HonorRtaResponse
                            .UserInfo
                            .builder()
                            .rtaIdList(Collections.emptyList())
                            .build())
                    .build();
        } catch (BlockException blockException) {
            MetricUtils.getChannelBlockMeter(HONOR).mark();
            return HonorRtaResponse.exception("FLOW CONTROL.");
        } catch (Exception e) {
            if (e instanceof BizException || e instanceof ValidationException) {
                log.warn("Honor rta api caught expected exception, {}", e.getMessage());
                return HonorRtaResponse.exception(e.getMessage());
            } else {
                log.error("Honor rta api ERROR!", e);
                return HonorRtaResponse.exception("system error.");
            }
        }
    }

    @GetMapping("/wifi")
    public WifiRtaResponse wifiRta(WifiRtaRequest wifiRtaRequest) {
        try (Timer.Context ignore = MetricUtils.getThirdRtaTimer(WIFI).time(); Entry entry = SphU.entry(getChannelRtaResourceName(WIFI), EntryType.IN)) {
            WifiRtaRequestUtils.validate(wifiRtaRequest);
            // 根据WIFI自定义参数中的rtaId来定位到智选的aids
            Set<Long> aids = rtaSelectorHolder.getAidsByWifiRtaId(wifiRtaRequest.getRtaId());
            if (CollectionUtils.isEmpty(aids)) {
                throw new BizException("所有rtaId均未配置");
            }
            List<ThirdRtaConfig> thirdRtaConfig = rtaSelectorHolder.getThirdRtaConfig(new ArrayList<>(aids));
            if (CollectionUtils.isEmpty(thirdRtaConfig)) {
                throw new BizException("所有rtaId均未开启RTA");
            }
            ThirdRtaRequest thirdRtaRequest = wifiRtaRequestUtils.wifiRtaReq2ThirdRtaRequest(wifiRtaRequest, aids);
            Set<Long> filteredAids = new HashSet<>(thirdRtaService.getFilteredAids(thirdRtaConfig, thirdRtaRequest, thirdRtaRequest.getReqId()));
            thirdRtaService.bindReqId2Device(thirdRtaRequest, thirdRtaRequest.getReqId(), filteredAids, thirdRtaConfig);
            // 如果有任何一个aid被过滤则过滤当前流量
            boolean isTarget = !CollectionUtils.containsAny(aids, filteredAids);
            WifiRtaResponse.WifiRtaResponseBuilder successResponseBuilder = WifiRtaResponse.builder()
                    .code(WifiRtaResponse.CODE_SUCCESS)
                    .msg("success")
                    .result(isTarget);
            if (isTarget) {
                successResponseBuilder.rtaIdList(Collections.singletonList(wifiRtaRequest.getRtaId()));
            }
            return successResponseBuilder.build();
        } catch (DegradeException degradeException) {
            MetricUtils.getChannelDegradeMeter(WIFI).mark();
            return WifiRtaResponse.builder()
                    .code(WifiRtaResponse.CODE_SUCCESS)
                    .msg("success")
                    .result(false)
                    .build();
        } catch (BlockException blockException) {
            MetricUtils.getChannelBlockMeter(WIFI).mark();
            return WifiRtaResponse.exception("FLOW CONTROL.");
        } catch (Exception e) {
            if (e instanceof BizException || e instanceof ValidationException) {
                log.warn("Wifi rta api caught expected exception, {}", e.getMessage());
                return WifiRtaResponse.exception(e.getMessage());
            } else {
                log.error("Wifi rta api ERROR!", e);
                return WifiRtaResponse.exception("system error.");
            }

        }
    }

    /**
     * 经过代理转发的oppo rta 请求走这个接口
     * @param oppoRtaRequest
     * @return
     */
    @PostMapping({"/oppo/2.0", "/oppo/2.0/{api-code}"})
    public OppoRtaResponseV2 oppoRtaV2(@PathVariable(name = "api-code", required = false) String apiCode, @RequestBody OppoRtaRequestV2 oppoRtaRequest) {
        try (Timer.Context ignore = MetricUtils.getThirdRtaTimer(OPPO).time(); Entry entry = SphU.entry(getChannelRtaResourceName(OPPO), EntryType.IN)) {
            checkApiCode(apiCode, TDP_PLATFORM_OPPO);
            OppoRtaRequestV2Utils.validate(oppoRtaRequest);
            Set<Long> oppoAdIds = oppoRtaRequest.getAdTypeList();
            // 根据OPPO的 策略Id 定位到智选的 aid
            Map<Long, Set<Long>> oppoAdId2Aids = rtaSelectorHolder.getAidsByOppoAdIds(oppoRtaRequest.getAdTypeList());
            if (MapUtils.isEmpty(oppoAdId2Aids)) {
                throw new BizException("所有adId均未配置");
            }
            Set<Long> aids = CommonUtils.flatCollections(oppoAdId2Aids.values());
            List<ThirdRtaConfig> thirdRtaConfigs = rtaSelectorHolder.getThirdRtaConfig(new ArrayList<>(aids));
            if (CollectionUtils.isEmpty(thirdRtaConfigs)) {
                throw new BizException("所有adId均未开启RTA");
            }
            ThirdRtaRequest thirdRtaRequest = OppoRtaRequestV2Utils.oppoRtaReq2ThirdRtaRequest(oppoRtaRequest, aids);
            Set<Long> biddingOppoAdIds = getBiddingOppoAdIds(oppoRtaRequest.getRequestId(), thirdRtaConfigs, thirdRtaRequest, oppoAdIds, oppoAdId2Aids);
            long cacheTime = 0L;
            OppoRtaResponseV2.ResponseData responseData = OppoRtaResponseV2.ResponseData.builder()
                    .id(oppoRtaRequest.getRequestId())
                    .agreeAdList(biddingOppoAdIds)
                    .cacheTime(cacheTime)
                    .build();
            return OppoRtaResponseV2.normal(responseData);
        } catch (DegradeException degradeException) {
            MetricUtils.getChannelDegradeMeter(OPPO).mark();
            return OppoRtaResponseV2.normal(
                    OppoRtaResponseV2.ResponseData.builder()
                            .id(oppoRtaRequest.getRequestId())
                            .build()
            );
        } catch (BlockException blockException) {
            MetricUtils.getChannelBlockMeter(OPPO).mark();
            return OppoRtaResponseV2.exception("FLOW CONTROL.");
        } catch (Exception e) {
            if (e instanceof BizException || e instanceof ValidationException) {
                log.warn("Oppo rta api caught expected exception, {}", e.getMessage());
                return OppoRtaResponseV2.exception(e.getMessage());
            } else {
                log.error("Oppo rta api ERROR!", e);
                return OppoRtaResponseV2.exception("system error.");
            }
        }
    }

    private Set<Long> getBiddingOppoAdIds(String requestId, List<ThirdRtaConfig> thirdRtaConfigs, ThirdRtaRequest thirdRtaRequest, Set<Long> oppoAdIds, Map<Long, Set<Long>> oppoAdId2Aids) throws Exception {
        List<Long> filteredAids = thirdRtaService.getFilteredAids(thirdRtaConfigs, thirdRtaRequest, thirdRtaRequest.getReqId());
        Set<Long> biddingOppoAdIds = new HashSet<>();
        // 通过被过滤的智选 aid 获取到参与竞价的OPPO AdId
        oppoAdIds.forEach(oppoAdId -> {
            // 如果该OPPO AdId在智选中有对应的aid，并且所对应的aid开启了rta
            if (oppoAdId2Aids.containsKey(oppoAdId) && RtaSelectorHolder.isAnyActivityEnableRta(thirdRtaConfigs, oppoAdId2Aids.get(oppoAdId))) {
                // 如果该 OPPO AdId 所对应的 aids 没有被广告主过滤则参与竞价。不与上面的 if 合并的原因：如果是广告主选择过滤，则无需打印日志信息。
                if (!CollectionUtils.containsAny(filteredAids, oppoAdId2Aids.get(oppoAdId))) {
                    biddingOppoAdIds.add(oppoAdId);
                }
            } else {
                log.warn("adId: {} from oppo not configuration in any actions or no actions configuration RTA, requestId: {}",
                        oppoAdId, requestId);
            }
        });
        return biddingOppoAdIds;
    }

    /**
     * 和oppo 官方直接对接的接口
     * @param oppoRtaRequest
     * @return
     */
    @PostMapping({"/oppo", "/oppo/{api-code}"})
    public OppoRtaResponse oppoRta(@PathVariable(name = "api-code", required = false) String apiCode, @RequestBody OppoRtaRequest oppoRtaRequest) {
        try (Timer.Context ignore = MetricUtils.getThirdRtaTimer(OPPO).time(); Entry entry = SphU.entry(getChannelRtaResourceName(OPPO), EntryType.IN)) {
            checkApiCode(apiCode, TDP_PLATFORM_OPPO);
            OppoRtaRequestUtils.validate(oppoRtaRequest, OPPO_RTA_SECRET_KEY);
            Set<Long> oppoAdIds = new HashSet<>(oppoRtaRequest.getAdTypeList());
            // 根据OPPO的 AdId 定位到智选的 aid
            Map<Long, Set<Long>> oppoAdId2Aids = rtaSelectorHolder.getAidsByOppoAdIds(oppoAdIds);
            if (MapUtils.isEmpty(oppoAdId2Aids)) {
                throw new BizException("所有adId均未配置");
            }
            Set<Long> aids = CommonUtils.flatCollections(oppoAdId2Aids.values());
            List<ThirdRtaConfig> thirdRtaConfig = rtaSelectorHolder.getThirdRtaConfig(new ArrayList<>(aids));
            if (CollectionUtils.isEmpty(thirdRtaConfig)) {
                throw new BizException("所有adId均未开启RTA");
            }
            ThirdRtaRequest thirdRtaRequest = OppoRtaRequestUtils.oppoRtaReq2ThirdRtaRequest(oppoRtaRequest, aids);
            Set<Long> biddingOppoAdIds = getBiddingOppoAdIds(oppoRtaRequest.getRequestId(), thirdRtaConfig, thirdRtaRequest, oppoAdIds, oppoAdId2Aids);
            OppoRtaResponse.ResponseData responseData = OppoRtaResponse.ResponseData.builder()
                    .id(oppoRtaRequest.getRequestId())
                    .agreeTypeList(new ArrayList<>(biddingOppoAdIds))
                    .build();
            return OppoRtaResponse.normal(responseData);
        } catch (DegradeException degradeException) {
            MetricUtils.getChannelDegradeMeter(OPPO).mark();
            return OppoRtaResponse.normal(
                    OppoRtaResponse.ResponseData.builder()
                            .id(oppoRtaRequest.getRequestId())
                            .agreeTypeList(Collections.emptyList())
                            .build()
            );
        } catch (BlockException blockException) {
            MetricUtils.getChannelBlockMeter(OPPO).mark();
            return OppoRtaResponse.exception("FLOW CONTROL.");
        } catch (Exception e) {
            if (e instanceof BizException || e instanceof ValidationException) {
                log.warn("Oppo rta api caught expected exception, {}", e.getMessage());
                return OppoRtaResponse.exception(e.getMessage());
            } else {
                log.error("Oppo rta api ERROR!", e);
                return OppoRtaResponse.exception("system error.");
            }
        }
    }

    @RequestMapping(value = "/baidu/{api-code}", method = RequestMethod.POST)
    public void baiduRta(@PathVariable(value = "api-code") String apiCode, HttpServletRequest request, HttpServletResponse response) {
        try (Timer.Context ignore = MetricUtils.getThirdRtaTimer(BAIDU).time(); Entry entry = SphU.entry(getChannelRtaResourceName(BAIDU), EntryType.IN)) {
            BaiduRtaMessage.RtaApiRequest rtaApiRequest = BaiduRtaMessage.RtaApiRequest.parseFrom(request.getInputStream());
            RtaApiConfig rtaApiConfig = rtaSelectorHolder.getRtaApiConfig(apiCode, TDP_PLATFORM_BAIDU);
            if (rtaApiConfig.active()) {
                Map<Long, Set<Long>> baiduRtaId2ActivityIds = rtaApiConfig.getRtaId2ActivityIds();
                Set<Long> requestActivityIds = CommonUtils.flatCollections(baiduRtaId2ActivityIds.values());
                List<ThirdRtaConfig> thirdRtaConfigs = rtaApiConfig.getThirdRtaConfigs();
                ThirdRtaRequest thirdRtaRequest = BaiduRtaUtils.buildRtaRequest(requestActivityIds, rtaApiRequest);
                String generatedReqId = UUID.randomUUID().toString();
                List<Long> filteredAids = thirdRtaService.getFilteredAids(thirdRtaConfigs, thirdRtaRequest, generatedReqId);
                thirdRtaService.bindReqId2Device(thirdRtaRequest, generatedReqId, new HashSet<>(filteredAids), thirdRtaConfigs);
                BaiduRtaMessage.RtaApiResponse rtaApiResponse = BaiduRtaUtils.convert(rtaApiRequest, baiduRtaId2ActivityIds, filteredAids);
                writeProtoBufResponse(response, rtaApiResponse);
            } else {
                writeProtoBufResponse(response, BaiduRtaUtils.reject(rtaApiRequest));
            }
        } catch (DegradeException degradeException) {
            MetricUtils.getChannelDegradeMeter(BAIDU).mark();
            baiduReject(response, request);
        } catch (BlockException blockException) {
            MetricUtils.getChannelBlockMeter(BAIDU).mark();
            baiduReject(response, request);
        } catch (BizException | ValidationException e) {
            if (log.isDebugEnabled()) {
                log.debug("baidu rta api caught expected exception, {}", e.getMessage());
            }
            baiduReject(response, request);
        } catch (Exception e) {
            log.error("baidu rta api ERROR!", e);
            baiduReject(response, request);
        }
    }

    private void baiduReject(HttpServletResponse response, HttpServletRequest request) {
        try {
            writeProtoBufResponse(response, BaiduRtaUtils.reject(BaiduRtaMessage.RtaApiRequest.parseFrom(request.getInputStream())));
        } catch (Throwable e) {
            log.error("parse baidu rta request error", e);
        }
    }

    private void writeProtoBufResponse(HttpServletResponse response, BaiduRtaMessage.RtaApiResponse rtaApiResponse) throws IOException {
        try (OutputStream output = response.getOutputStream()) {
            response.setStatus(HttpServletResponse.SC_OK);
            response.setContentType("application/x-protobuf;charset=UTF-8");
            output.write(rtaApiResponse.toByteArray());
        }
    }
}
