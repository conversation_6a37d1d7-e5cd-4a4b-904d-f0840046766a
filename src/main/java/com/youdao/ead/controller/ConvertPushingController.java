package com.youdao.ead.controller;


import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.SharedMetricRegistries;
import com.codahale.metrics.Timer;
import com.youdao.ead.constant.ProcessCode;
import com.youdao.ead.data.CallbackMessage;
import com.youdao.ead.data.ConvertPushingInfo;
import com.youdao.ead.service.CallbackService;
import com.youdao.ead.util.ConvertTrackingUtils;
import com.youdao.ead.util.ThirdPartPromotionInfoCache;
import com.youdao.quipu.avro.schema.DspConv;
import com.youdao.quipu.avro.schema.SdkLandPage;
import com.youdao.quipu.avro.schema.ThirdPartyConv;
import com.youdao.quipu.kafka.producer.AtLeastOnceKafkaProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static com.youdao.ead.data.ConvertTrackingResponse.getResponse;
import static com.youdao.ead.service.MetricsService.REPORTER_METRICS;
import static com.youdao.ead.util.ConvertKafkaConfig.*;
import static com.youdao.ead.util.ConvertPushingUtils.*;

/**
 * 老转化 API 回调上报转化接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("api/v2")
public class ConvertPushingController {

    private static final String CONVERSION_ID = "youdao_conv_id";
    private static final String BID_ID = "bid";
    private static final String VARIANT_ID = "variantId";
    private static final String CONVERT_TYPE = "convertType";

    private static AtLeastOnceKafkaProducer producer = AtLeastOnceKafkaProducer.getInstance();

    @Autowired
    private CallbackService callbackService;
    
    @Autowired
    private ThirdPartPromotionInfoCache thirdPartPromotionInfoCache;

    private final MetricRegistry metricRegistry = SharedMetricRegistries.getOrCreate(REPORTER_METRICS);

    private final Timer timer = metricRegistry.timer(MetricRegistry.name(ConvertPushingController.class, "pushTimer"));

    @RequestMapping("push")
    Object convertPush(@RequestParam Map<String, String> requestParams) throws Exception {
        try (Timer.Context ignore = timer.time()) {
            ConvertPushingInfo convInfo = buildConvertPushingInfo(requestParams);

            if (StringUtils.isBlank(convInfo.getAttribute(CONVERSION_ID))) {
                return getResponse("fail", ProcessCode.SOURCE_ERROR);
            }

            try {
                if (convInfo.isActualConv()) {
                    // 转化类型是落地页的
                    if ("0".equals(convInfo.getAttribute(CONVERT_TYPE))) {
                        SdkLandPage landPage = buildSdkLandPage(convInfo);
                        if (landPage != null) {
                            producer.send(LANDPAGE_TOPIC, null, landPage);
                        }
                    } else if (convInfo.isThirdPartyRequest()) {

                        ThirdPartyConv thirdPartyConv = buildThirdPartyDspApiConv(convInfo, thirdPartPromotionInfoCache.judgeNeedCallback(getPromotionId(convInfo)));

                        if (thirdPartyConv != null) {
                            producer.send(THIRD_PARTY_TOPIC, null, thirdPartyConv);
                            if (ConvertTrackingUtils.validateLocalConv(thirdPartyConv)) {
                                ThirdPartyConv locConv = ConvertTrackingUtils.convert2LocalThirdPartyConv(thirdPartyConv);
                                producer.send(LOC_THIRD_PARTY_TOPIC, null, locConv);
                            } else {
                                log.warn("convTime outdated. aid:{}, convTime:{}", thirdPartyConv.getActivityId(), thirdPartyConv.getConvTime());
                            }
                            CallbackMessage callbackMessage = CallbackMessage.builder()
                                    .aid(String.valueOf(thirdPartyConv.getActivityId()))
                                    .callbackMd5(convInfo.getThirdPartyCallback())
                                    .convertAction(convInfo.getAttribute("conv_action"))
                                    .timestamp(System.currentTimeMillis())
                                    .type("pushing")
                                    .needCallback(thirdPartyConv.getNeedConvCallback())
                                    .retriedTimes(0)
                                    .filterNoBidConv(false)
                                    .retentionDays(0)
                                    .build();
                            callbackService.pushingCallback(callbackMessage);
                        }
                    } else {
                        DspConv dspConv = buildDspApiConv(convInfo);
                        if (dspConv != null) {
                            if (convInfo.isSdkRequest()) {
                                // 如果是SDK的请求, 发到单独 topic 不计入统计
                                producer.send(CONVERSION_FROM_SDK_TOPIC, null, dspConv);
                            } else {
                                producer.send(CONV_DSP_TOPIC, null, dspConv);
                                log.info("API DspConv { sponsorId: {}, convType:{}, convAction:{}, groupId:{} }",
                                        dspConv.getSponsorId(), dspConv.getConvType(), dspConv.getCtAction(), dspConv.getGroupId());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("other error", e);
                return getResponse("fail", ProcessCode.OTHER_ERROR);
            }
            return getResponse("success", ProcessCode.OK);
        }
    }

    /**
     * 重新构造请求，尽可能获取 ${@link #CONVERSION_ID} 属性值存在，但不一定存在
     *
     * @param requestParams 转化请求参数
     */
    private ConvertPushingInfo buildConvertPushingInfo(Map<String, String> requestParams) {
        String source = requestParams.get(CONVERSION_ID);
        Map<String, String> result = new HashMap<>(requestParams);

        if (StringUtils.isBlank(source)) {
            String bidSponsorId = requestParams.get("bid_sponsorid");
            if (!StringUtils.isEmpty(bidSponsorId)) {
                String[] tmp = bidSponsorId.split("[_]{1,2}");
                if (tmp.length == 2) {
                    source = tmp[1];
                    result.put(BID_ID, tmp[0]);
                    result.put(CONVERSION_ID, source);
                } else if (tmp.length == 6 || tmp.length == 7) {
                    // 第三方监测上报 bid_id 携带额外广告信息
                    source = tmp[tmp.length - 1];
                    result.put(BID_ID, bidSponsorId.replace("_" + source, ""));
                    result.put(CONVERSION_ID, source);
                }
            }
        }

        if (StringUtils.isNotBlank(source)) {
            result.put(CONVERSION_ID, source);
        } else {
            // 不再从数据库找 变体对应转化id，赋值成默认值 0
            if (StringUtils.isNotEmpty(requestParams.get(VARIANT_ID))) {
                result.put(CONVERSION_ID, "0");
            }
        }

        return new ConvertPushingInfo(result);
    }

}
