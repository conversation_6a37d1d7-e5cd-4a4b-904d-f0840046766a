package com.youdao.ead.util;

import org.apache.commons.collections4.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.ValidatorFactory;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/9.
 */
public class ValidationUtils {

    public static final ValidatorFactory VALIDATOR_FACTORY = Validation.buildDefaultValidatorFactory();

    public static <T> void validation(T data) {
        Set<ConstraintViolation<T>> result = VALIDATOR_FACTORY.getValidator().validate(data);
        List<String> message = result.stream().map(v -> v.getPropertyPath() + " " + v.getMessage() + ": " + v.getInvalidValue())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(message)) {
            throw new ValidationException(message.get(0));
        }
    }

    public static <T> boolean isNotValid(T data) {
        Set<ConstraintViolation<T>> result = VALIDATOR_FACTORY.getValidator().validate(data);
        return CollectionUtils.isNotEmpty(result);
    }
}
