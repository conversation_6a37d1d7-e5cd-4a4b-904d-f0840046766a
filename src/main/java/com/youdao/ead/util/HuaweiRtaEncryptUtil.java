package com.youdao.ead.util;

import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class HuaweiRtaEncryptUtil {
    private static final String SECRET_KEY = "HurciKZ2cg/w+umU/e8uqAdwPG3I1OY4li/EuPzqKTs=";

    private static String encrypt(String rawText) {
        HmacUtils hmacUtils = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, SECRET_KEY);
        return hmacUtils.hmacHex(rawText);
    }

    public static Boolean isSignValid(String requestId, Long requestTime, String oaid, String sign) {
        String rawText = requestId + requestTime;
        if (StringUtils.isNotBlank(oaid)) {
            rawText = rawText + oaid.toUpperCase();
        }
        return StringUtils.equals(encrypt(rawText), sign);
    }

}
