/*
 * @(#)JvmUtil.java, 2011-11-15.
 *
 * Copyright 2011 Yodao, Inc. All rights reserved.
 * YODAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.youdao.ead.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;

/**
 * <AUTHOR>
 */
@Slf4j
public class MachineUtils {
    /**
     * jvm 的pid
     */
    @Getter
    private static long pid = -1;

    @Getter
    private static String hostName = "";

    @Getter
    private static String userName;

    static {
        try {
            String name = ManagementFactory.getRuntimeMXBean().getName();
            pid = Long.parseLong(name.split("@")[0]);
            hostName = InetAddress.getLocalHost().getHostName();
            userName =  System.getProperty("user.name");
        } catch (Exception e) {
            log.error("cannot getHostName.", e);
        }
        log.info("pid = {}, hostName = {}, userName = {}", pid, hostName, userName);
    }
}
