package com.youdao.ead.util;

import com.youdao.ead.constant.AttributionType;
import com.youdao.ead.constant.ConvertActionConstants;
import com.youdao.ead.constant.ExtMapConstants;
import com.youdao.ead.constant.antifraud.AntiFraudStatEnum;
import com.youdao.ead.data.ConvertTrackingInfo;
import com.youdao.quipu.avro.schema.*;
import io.gromit.uaparser.Parser;
import io.gromit.uaparser.model.Device;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import outfox.ead.data.ConvClickTimeDifferenceType;
import outfox.ead.dsp.protocol.youdao.Bid;
import outfox.ead.dsp.protocol.youdao.ConvTracker;
import outfox.ead.dsp.protocol.youdao.TrackerCommonDimension;
import outfox.ead.ip.location.entity.AdvancedLocation;
import outfox.ead.ip.location.tools.AdvancedIpTool;

import javax.annotation.Nullable;
import java.io.IOException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 标准转化方案流程使用的工具类
 * <p>
 * Created by yangyu on 2018/2/27.
 */
@Slf4j
public class ConvertTrackingUtils {

    /**
     * to locate ip, for province and city
     */
    private static final AdvancedIpTool ADVANCED_IP_TOOL = initAdvanceIpTool();

    private static AdvancedIpTool initAdvanceIpTool() {
        try {
            return AdvancedIpTool.getInstance();
        } catch (Exception e) {
            throw new RuntimeException(e.toString(), e);
        }
    }

    /**
     * to parse UA, init with no_cache.
     */
    public static final Parser parser;

    static {
        try {
            parser = new Parser();
            log.info("Init ua-parser successfully.");
        } catch (IOException e) {
            log.error("Init ua-parser failed. ", e);
            throw new ExceptionInInitializerError(e);
        }
        // 对反作弊枚举的id做不重复校验，重复的话就抛出异常
        Set<Integer> ids = new HashSet<>();
        for (AntiFraudStatEnum value : AntiFraudStatEnum.values()) {
            if (ids.contains(value.getStatId())) {
                throw new RuntimeException("AntiFraudStatEnum more than two values contains the same statId:" + value.getStatId());
            } else {
                ids.add(value.getStatId());
            }
        }

    }

    /**
     * 日期格式
     */
    public static final DateTimeFormatter DAY_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static DspConv buildDspConv(ConvTracker.Conv conv, String convAction, Long orderAmount, Long conv_time, int retentionDays, LocalDateTime activateTime) {
        TrackerCommonDimension.CommonDimension commonDimension = conv.getCommonDimension();

        Map<CharSequence, CharSequence> ext = new HashMap<>();
        ext.put("platform", "sdk");
        ext.put("slot_id", _string(commonDimension.getSlotId()));
        ext.put("app_id", _string(commonDimension.getAppId()));
        ext.put("developer_id", _string(commonDimension.getDeveloperId()));
        ext.put("device_id", _string(commonDimension.getDeviceId()));
        ext.put("render_time", _string(commonDimension.getRenderTime()));
        ext.put("nsv", _string(commonDimension.getSdkVersion()));
        ext.put("osv", _string(commonDimension.getOsVersion()));
        ext.put("av", _string(commonDimension.getAppVersion()));
        ext.put("network_type", _string(commonDimension.getNetworkType()));
        if (LocalDateTime.MIN.equals(activateTime)) {
            ext.put(ExtMapConstants.EXCEPTION_WHEN_FETCH_CONV_TIME_FROM_REDIS, "true");
        }

        DspConv.Builder builder = DspConv.newBuilder();
        long currentTimeMills = System.currentTimeMillis();
        builder.setTimestamp(currentTimeMills)
                .setBid(commonDimension.getBidId())
                .setUrl("")
                .setDomain("")
                .setAgent("")
                .setAdSize("")
                .setGuid(UUID.randomUUID().toString())
                .setSyndId(_long(commonDimension.getSyndicationId(), 0))
                .setDevice(-1)
                .setContext(-1)
                .setOs(_int(commonDimension.getOs(), -1))
                .setCampaignId(_long(commonDimension.getCampaignId(), 0))
                .setGroupId(_long(commonDimension.getGroupId(), 0))
                .setVariationId(_long(commonDimension.getVariationId(), 0))
                .setImprPos(-1)
                .setMatchId(-1)
                .setQualityScore(-1)
                .setClickIp("")
                .setImprTimestamp(-1)
                .setSponsorId(_long(commonDimension.getSponsorId(), 0))
                .setIp(_string(commonDimension.getIp()))
                .setExt(ext)
                .setConv(0)
                .setConvCost(0)
                .setLocConv(0)
                .setLocConvCost(0)
                .setCtId(_string(conv.getCtId()))
                .setCtType(_int(conv.getCtType(), -1))
                .setCtAction(convAction)
                .setOrderAmount(_long(orderAmount, 0))
                .setRetentionDays(retentionDays)
                .setFirstDayOrderAmount(getFirstDayAmount(orderAmount, convAction, activateTime, LocalDateTime.now(), conv_time))
                .setIn24HoursOrderAmount(getIn24HoursAmount(orderAmount, convAction, activateTime, LocalDateTime.now(), conv_time))
                .setActivePurchaseIntervalDays(getActivePurchaseIntervalDays(convAction, activateTime, LocalDateTime.now(), conv_time))
                // 广告商没有回传conv_time，使用系统当前时间作为ct_time
                .setCtTime(_long(conv_time, currentTimeMills))
                .setDestLink(_string(conv.getDestLink()))
                .setConvParentType("api")
                // 这里设置conv_type = sdk 是为了兼容智选现有的逻辑
                // 并不表示这个转化真的是SDK接入的媒体带来的
                .setConvType("sdk");

        return builder.build();
    }

    public static SdkConv buildSdkConv(ConvTracker.Conv conv, String convAction, Long orderAmount, Long convTime, int retentionDays, LocalDateTime activeTime) {
        TrackerCommonDimension.CommonDimension commonDimension = conv.getCommonDimension();
        Map<CharSequence, CharSequence> ext = new HashMap<>();
        ext.put("platform", "sdk");
        ext.put("slot_id", _string(commonDimension.getSlotId()));
        ext.put("app_id", _string(commonDimension.getAppId()));
        ext.put("developer_id", _string(commonDimension.getDeveloperId()));
        ext.put("device_id", _string(commonDimension.getDeviceId()));
        ext.put("render_time", _string(commonDimension.getRenderTime()));
        ext.put("nsv", _string(commonDimension.getSdkVersion()));
        ext.put("osv", _string(commonDimension.getOsVersion()));
        ext.put("av", _string(commonDimension.getAppVersion()));
        ext.put("network_type", _string(commonDimension.getNetworkType()));
        ext.put("iid", _string(commonDimension.getIid()));
        if (LocalDateTime.MIN.equals(activeTime)) {
            ext.put(ExtMapConstants.EXCEPTION_WHEN_FETCH_CONV_TIME_FROM_REDIS, "true");
        }
        Bid.BidRequest.Device.OS os = Bid.BidRequest.Device.OS.valueOf(commonDimension.getOs());
        LocalDateTime now = LocalDateTime.now();
        Long convTimestamp = _long(convTime, now.toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        // 转化触点时间差，转化或触点时间<=0说明未获取到转化或触点时间，此时触点时间差为未知
        int convTouchpointTimeDifference = (convTime <= 0L || commonDimension.getRenderTime() <= 0L) ? -1
                : ConvClickTimeDifferenceType.getConvClickTimeDifference(convTime, commonDimension.getRenderTime());
        SdkConv.Builder sdkConvBuilder = SdkConv.newBuilder()
                // 转化使用收到转化的时间，区别于当日转化，当日转化取点击时间
                .setTimestamp(convTimestamp)
                .setSyndId(_long(commonDimension.getSyndicationId(), 0))
                .setOs(os.name())
                .setOsv(_string(commonDimension.getOsVersion()))
                .setAppId(_string(commonDimension.getAppId()))
                .setSlotId(_string(commonDimension.getSlotId()))
                .setMediaType(commonDimension.getMediaType())
                .setSlotStyleType(commonDimension.getSlotStyleType())
                .setBidId(commonDimension.getBidId())
                .setIp(_string(commonDimension.getIp()))
                .setDeviceName(_string(commonDimension.getDeviceName()))
                .setCarrierName(_string(commonDimension.getCarrierName()))
                .setNetworkType(_string(commonDimension.getNetworkType()))
                .setAgentId(-1)
                .setSponsorId(commonDimension.getSponsorId())
                .setCampaignId(commonDimension.getCampaignId())
                .setGroupId(commonDimension.getGroupId())
                .setVariationId(commonDimension.getVariationId())
                .setImprTimestamp(commonDimension.getRenderTime())
                .setConvDeviceId(commonDimension.getDeviceId())
                .setIdfa(_string(commonDimension.getIdfa()))
                .setAaid(_string(commonDimension.getAaid()))
                .setAndroidId(_string(commonDimension.getAndroidId()))
                .setImei(_string(commonDimension.getImei()))
                .setOaid(_string(commonDimension.getOaid()))
                .setDeveloperId(commonDimension.getDeveloperId())
                .setAv(_string(commonDimension.getAppVersion()))
                .setNsv(_string(commonDimension.getSdkVersion()))
                .setOsv(_string(commonDimension.getOsVersion()))
                .setCvrAlgId(commonDimension.getCvrAlgId())
                .setStrategyId(commonDimension.getStrategyId())
                .setMatchId(commonDimension.getMatchId())
                .setGuid(commonDimension.getBidId() + "-" + commonDimension.getSponsorId() + convAction)
                .setCtId(_string(conv.getCtId()))
                .setCtType(conv.getCtType())
                .setCtAction(convAction)
                .setConvType("sdk")
                .setConvParentType("api")
                .setExt(ext)
                .setAttributionType(AttributionType.VIEW_THROUGH_ATTRIBUTION)
                .setConv(1)
                .setImprConv(1)
                .setOriginalCpc(0)
                .setActualCpc(0)
                .setWinPrice(0)
                // 这里设置为 0 是为了防止统计的数据出现重复，因为多个 topic 都存在 bid_price 指标（前后几个字段类似）
                .setBidPrice(0)
                .setCharge(0)
                .setLocConv(0)
                .setLocOptConv(0)
                .setLocConvCost(0)
                .setOrderAmount(_long(orderAmount, 0L))
                .setFirstDayOrderAmount(getFirstDayAmount(_long(orderAmount, 0L), convAction, activeTime, now, convTime))
                .setIn24HoursOrderAmount(getIn24HoursAmount(_long(orderAmount, 0L), convAction, activeTime, now, convTime))
                .setActivePurchaseIntervalDays(getActivePurchaseIntervalDays(convAction, activeTime, now, convTime))
                .setRetentionDays(retentionDays)
                .setScenarioIds(defaultIfNull(commonDimension.getScenarioIdList(), Collections.emptyList()))
                .setPackageName(_string(commonDimension.getPackageName()))
                .setTouchpointTimestamp(commonDimension.getRenderTime())
                .setConvTouchpointTimeDifference(convTouchpointTimeDifference);
        if (StringUtils.isNotBlank(commonDimension.getIp())) {
            AdvancedLocation location = ADVANCED_IP_TOOL.locate(commonDimension.getIp());
            sdkConvBuilder.setCountry(location.getCountry());
            sdkConvBuilder.setProvince(location.getProvince());
            sdkConvBuilder.setCity(location.getCity());
        }
        return sdkConvBuilder.build();
    }

    /**
     * 第三方转化构造方法
     *
     * @param conv               Pb转化 {@link ConvTracker.Conv}
     * @param orderAmount
     * @param convAction         转化行为
     * @param convTime
     * @param activateActionTime 付费事件对应的激活时间
     * @return 第三方转化 ThirdPartyConv
     */
    public static ThirdPartyConv buildThirdPartyConv(ConvTracker.Conv conv, Long orderAmount, String convAction,
                                                     boolean needConvCallback, String orderId, int retentionDays, Long convTime, LocalDateTime activateActionTime) {
        TrackerCommonDimension.ThirdPartyDimension dimension = conv.getThirdPartyDimension();

        Map<CharSequence, CharSequence> ext = new HashMap<>(16);
        ext.put("platform", "sdk");
        ext.put("device_id", _string(dimension.getDeviceId()));
        ext.put("render_time", _string(dimension.getTimestamp()));
        if (LocalDateTime.MIN.equals(activateActionTime)) {
            ext.put(ExtMapConstants.EXCEPTION_WHEN_FETCH_CONV_TIME_FROM_REDIS, "true");
        }
        ext.put(ExtMapConstants.CONV_TIME_FROM_SPONSOR, _string(convTime));
        LocalDateTime now = LocalDateTime.now();
        long currentTimeMills = System.currentTimeMillis();
        // 查询日志发现，存在部分转化时间戳为10位，因此需要统一转换为13位
        long clickTimeMills = String.valueOf(dimension.getTimestamp()).length() == 10 ? dimension.getTimestamp() * 1000 : dimension.getTimestamp();

        ThirdPartyConv.Builder builder = ThirdPartyConv.newBuilder();
        // 字段名字是转化时间，实际上是点击的时间，wtf??
        builder.setConvTime(dimension.getTimestamp())
                .setTimestamp(now.toInstant(ZoneOffset.ofHours(8)).toEpochMilli())
                .setGuid(UUID.randomUUID().toString())
                .setClickGuid(_string(dimension.getGuid()))
                .setSponsorId(dimension.getSponsorId())
                .setGroupId(dimension.getGroupId())
                .setCampaignId(dimension.getCampaignId())
                .setVariationId(dimension.getVariationId())
                .setAppId(_string(dimension.getAppId()))
                .setActivityId(dimension.getActivityId())
                .setChannelDid(dimension.getChannelDid())
                .setChannelId(_string(dimension.getChannelId()))
                .setTdpPlatform(_string(dimension.getTdpPlatform()))
                .setTdpAdvertiserId(_string(dimension.getTdpAdvertiserId()))
                .setTdpCampaignId(_string(dimension.getTdpCampaignId()))
                .setTdpCreativeId(_string(dimension.getTdpCreativeId()))
                .setTdpRequestId(_string(dimension.getTdpRequestId()))

                .setIp(_string(dimension.getIp()))
                .setOs(Bid.BidRequest.Device.OS.valueOf(dimension.getOs()).name())
                .setOsv(_string(dimension.getOsVersion()))
                .setConvDeviceId(_string(dimension.getDeviceId()))
                .setUserAgent(_string(dimension.getUserAgent()))
                .setImei(_string(dimension.getImei()))
                .setOaid(_string(dimension.getOaid()))
                .setIdfa(_string(dimension.getIdfa()))
                .setCaid(_string(dimension.getCaid()))
                .setAlid(_string(dimension.getAlid()))
                .setAndroidId(_string(dimension.getAndroidId()))
                .setGaid(_string(dimension.getGaid()))

                .setCtId(_string(conv.getCtId()))
                .setCtType(_int(conv.getCtType(), -1))
                .setCtAction(convAction)
                .setDestLink(_string(conv.getDestLink()))
                .setExt(ext)
                .setOrderAmount(_long(orderAmount, 0))
                .setFirstDayOrderAmount(getFirstDayAmount(orderAmount, convAction, activateActionTime, now, convTime))
                .setIn24HoursOrderAmount(getIn24HoursAmount(orderAmount, convAction, activateActionTime, now, convTime))
                .setActivePurchaseIntervalDays(getActivePurchaseIntervalDays(convAction, activateActionTime, now, convTime))
                .setRetentionDays(retentionDays)
                .setOrderId(_string(orderId))
                .setConvParentType("api")
                // 这里设置conv_type = sdk 是为了兼容智选现有的逻辑
                // 并不表示这个转化真的是SDK接入的媒体带来的
                .setConvType("sdk")
                .setNeedConvCallback(needConvCallback)
                .setReqId(_string(dimension.getReqId()))
                .setOaidMd5(_string(dimension.getOaidMd5()))
                .setIdfaMd5(_string(dimension.getIdfaMd5()))
                .setCaidMd5(_string(dimension.getCaidMd5()))
                .setRtaEnabled(_bool(dimension.getRtaEnabled()))
                .setApiMode(_string(dimension.getApiMode()))
                .setFilterNoBidConv(_bool(dimension.getFilterNoBidConv()))
                .setRtaBidConv(_int(dimension.getRtaBid(), 0))
                .setCaids(DeviceIdUtils.convCaidList(dimension.getCaidsList()))
                .setAndroidIdMd5(_string(dimension.getAndroidIdMd5()))
                .setCallbackUrlMd5(_string(dimension.getCallback()))
                .setTargetCtAction(_string(conv.getCtAction()))
                .setJoinType(defaultJoinType(conv))
                .setMediaType(dimension.hasMediaType() ? String.valueOf(dimension.getMediaType()) : "-1")
                .setAppId(_string(dimension.getAppId()))
                .setSlotId(_string(dimension.getSlotId()))
                .setSlotStyleType(dimension.hasSlotStyleType() ? dimension.getSlotStyleType() : -1);

        // 转化加速器
        builder.setFromYdbc(_bool(dimension.getFromYdbc()))
                .setMediaPkgId(CollectionUtils.isEmpty(dimension.getMediaPkgIdList()) ? Collections.emptyList()
                        : dimension.getMediaPkgIdList())
                .setReyunCampId(CollectionUtils.isEmpty(dimension.getReyunCampIdList()) ? Collections.emptyList()
                        : new ArrayList<>(dimension.getReyunCampIdList()))
                .setIdentifies(_string(dimension.getIdentifies()))
                .setConvStrategyAppId(_string(dimension.getConvStrategyAppId()))
                .setAlgBucketId(dimension.getAlgBucketId())
                .setRetriever(new ArrayList<>(dimension.getRetrieverList()))
                .setRetrieveAlgId(dimension.getRetrieveAlgId())
                .setProductId(dimension.getProductId());

        // 反作弊
        builder.setConvTimeFollowClosely(ConvClickTimeDifferenceType.getConvTimeFollowClosely(currentTimeMills, clickTimeMills))
                .setConvClickTimeDifference(ConvClickTimeDifferenceType.getConvClickTimeDifference(currentTimeMills, clickTimeMills))
                .setSourceId(dimension.getSourceId());
        int antiFraudStatIdsCount = dimension.getAntiFraudStatIdsCount();
        if (antiFraudStatIdsCount > 0) {
            long[] array = dimension.getAntiFraudStatIdsList().stream().mapToLong(Long::longValue).toArray();
            BitSet bitSet = BitSet.valueOf(array);
            for (AntiFraudStatEnum antiFraudStatEnum : AntiFraudStatEnum.values()) {
                if (bitSet.get(antiFraudStatEnum.getStatId())) {
                    antiFraudStatEnum.getSendSetter().accept(builder, 1);
                }
            }
        }

        String ip = (String) builder.getIp();
        if (StringUtils.isNotBlank(ip)) {
            AdvancedLocation location = ADVANCED_IP_TOOL.locate(ip);
            builder.setProvince(location.getProvince());
            builder.setProvinceName(_string(location.getProvinceSimpleName()));
            builder.setCity(location.getCity());
            builder.setCityName(_string(location.getCitySimpleName()));
        }
        setBrandAndModel(builder, dimension);
        String formatDay = Instant.ofEpochMilli(builder.getTimestamp())
                .atZone(ZoneOffset.ofHours(8))
                .toLocalDate()
                .format(DAY_DATE_FORMAT);
        builder.setDay(formatDay);

        return builder.build();
    }

    /**
     * 获取激活当日付费金额，转化时间优先取广告主回传的转化时间，取不到时使用当前时间
     */
    public static long getFirstDayAmount(Long orderAmount, String convAction, @Nullable LocalDateTime activateActionTime, LocalDateTime now, Long convertTime) {
        if (isActivateActionTimeInvalid(activateActionTime) || !ConvertActionConstants.isPurchaseAction(convAction)) {
            return 0;
        }

        return activateActionTime.toLocalDate().equals(getConvTime(convertTime, now).toLocalDate()) ? _long(orderAmount, 0L) : 0;
    }

    /**
     * 获取激活24h内付费金额，转化时间优先取广告主回传的转化时间，取不到时使用当前时间
     */
    public static long getIn24HoursAmount(Long orderAmount, String convAction, @Nullable LocalDateTime activateActionTime, LocalDateTime now, Long convertTime) {
        if (isActivateActionTimeInvalid(activateActionTime) || !ConvertActionConstants.isPurchaseAction(convAction)) {
            return 0;
        }

        long durationInMillis = Duration.between(activateActionTime, getConvTime(convertTime, now)).toMillis();
        return durationInMillis >= 0 && durationInMillis <= TimeUnit.DAYS.toMillis(1) ? _long(orderAmount, 0L) : 0;
    }


    /**
     * 获取激活和付费的间隔天数，激活当日的付费记为第0天
     */
    public static int getActivePurchaseIntervalDays(String convEvent, @Nullable LocalDateTime activateActionTime, LocalDateTime now, Long convertTime) {
        if (isActivateActionTimeInvalid(activateActionTime) || !ConvertActionConstants.isPurchaseAction(convEvent)) {
            return -1;
        }
        try {
            return Math.toIntExact(ChronoUnit.DAYS.between(activateActionTime.toLocalDate(), getConvTime(convertTime, now).toLocalDate()));
        } catch (Exception e) {
            log.warn("get activate purchase interval days error, convEvent:{}, activateActionTime:{}, now:{}, convertTime:{}, will return default -1.",
                    convEvent, activateActionTime, now, convertTime, e);
            return -1;
        }
    }

    private static boolean isActivateActionTimeInvalid(LocalDateTime activateActionTime) {
        return Objects.isNull(activateActionTime) || LocalDateTime.MIN.equals(activateActionTime);
    }


    /**
     * 获取转化发生的时间，优先取广告主回传的转化时间，取不到时使用当前时间
     */
    private static LocalDateTime getConvTime(Long convertTime, LocalDateTime now) {
        return Objects.nonNull(convertTime) ? Instant.ofEpochMilli(convertTime).atZone(ZoneOffset.ofHours(8)).toLocalDateTime() : now;
    }


    /**
     * 设置品牌和型号，优先从dimension中获取，获取不到再用ua解析
     *
     * @param builder
     * @param dimension
     */
    private static void setBrandAndModel(ThirdPartyConv.Builder builder, TrackerCommonDimension.ThirdPartyDimension dimension) {
        String ua = (String) builder.getUserAgent();
        String mobileBrand = dimension.getBrand();
        String mobileModel = dimension.getModel();
        if (StringUtils.isNotBlank(mobileBrand)) {
            builder.setBrand(mobileBrand);
            builder.setModel(mobileModel);
        } else if (StringUtils.isNotBlank(ua)) {
            Device device = parser.parseDevice(ua);
            if (device != null) {
                builder.setBrand(_string(device.brand).toLowerCase());
                builder.setModel(_string(device.model));
            }
        }
    }

    /**
     * 校验转化时间在合法范围内
     *
     * @param conv
     * @return
     */
    public static boolean validateLocalConv(ThirdPartyConv conv) {
        long diffDays = (System.currentTimeMillis() - conv.getConvTime()) / 86400000;
        return diffDays <= 14;
    }

    /**
     * 转化成 点击时转化信息
     */
    public static ThirdPartyConv convert2LocalThirdPartyConv(ThirdPartyConv conv) {
        return ThirdPartyConv.newBuilder(conv)
                // 当日转化取点击时间，区别于转化，转化使用收到转化的时间
                .setTimestamp(conv.getConvTime())
                .setConv(0)
                .setLocConv(1)
                .setLocOrderAmount(conv.getOrderAmount())
                .setOrderAmount(0)
                .build();
    }

    public static List<String> RETENTION_CONV_ACTIONS = Arrays.asList("android_retention", "ios_retention", "wxMiniProgram_retention");

    public static List<String> ORDER_AMOUNT_CONV_ACTIONS = Arrays.asList("android_purchase", "ios_purchase", "wxMiniProgram_purchase", "landingpage_purchase");

    public static List<String> DAY1RETENTION_CONV_ACTIONS = Arrays.asList("android_day1retention", "ios_day1retention", "wxMiniProgram_day1retention");

    /**
     * 是否为次日留存转化
     *
     * @param convAction
     * @return
     */
    public static boolean isDay1RetentionType(String convAction) {
        return DAY1RETENTION_CONV_ACTIONS.contains(convAction);
    }

    /**
     * 是否为多日留存转化
     *
     * @param convAction
     * @return
     */
    public static boolean isMultiRetentionType(String convAction) {
        return RETENTION_CONV_ACTIONS.contains(convAction);
    }

    /**
     * 是否付费转化
     *
     * @param convAction
     * @return
     */
    public static boolean isOrderAmountType(String convAction) {
        return ORDER_AMOUNT_CONV_ACTIONS.contains(convAction);
    }

    public static String _string(Object object) {
        return object == null ? "" : object.toString();
    }

    public static Long _long(Object object, long defaultValue) {
        try {
            return object == null ? defaultValue : Long.parseLong(object.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Integer _int(Object object, int defaultValue) {
        try {
            return object == null ? defaultValue : Integer.parseInt(object.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Boolean _bool(Object object) {
        try {
            return object != null && Boolean.parseBoolean(object.toString());
        } catch (Exception e) {
            return false;
        }
    }

    public static <T> List<T> defaultIfNull(List<T> list, List<T> defaultList) {
        return list == null ? defaultList : list;
    }

    public static DspConvTest buildDspConvTest(String convId, String convAction,
                                               ConvertTrackingInfo convertTrackingInfo) {
        DspConvTest.Builder builder = DspConvTest.newBuilder();
        builder.setTimestamp(System.currentTimeMillis())
                .setCtId(_string(convId))
                .setCtAction(_string(convAction))
                .setGuid(UUID.randomUUID().toString())
                .setConv(1);

        if (convertTrackingInfo != null) {
            builder.setCtDebugStatus(convertTrackingInfo.getConvertTrackingDebugStatus());
        }

        return builder.build();
    }

    /**
     * 用于归因的设备类型，以设备号优先级判定
     * android: imei(imei_md5) > oaid(oaid_md5) > androidId
     * ios: idfa > caid > ip ua
     *
     * @param conv
     * @return
     */
    public static String defaultJoinType(ConvTracker.Conv conv) {
        TrackerCommonDimension.ThirdPartyDimension thirdPartyDimension = conv.getThirdPartyDimension();
        int os = thirdPartyDimension.getOs();
        if (os == Bid.BidRequest.Device.OS.ANDROID_VALUE) {
            if (StringUtils.isNotBlank(thirdPartyDimension.getImei())) {
                // 有道只能依靠设备号优先级判断归因方式，实际可能还和广告主是否获取到对应设备id有关
                return "imei";
            } else if (StringUtils.isNotBlank(thirdPartyDimension.getOaid()) || StringUtils.isNotBlank(thirdPartyDimension.getOaidMd5())) {
                return "oaid";
            } else if (StringUtils.isNotBlank(thirdPartyDimension.getAndroidId())) {
                return "android_id";
            } else if (StringUtils.isNotBlank(thirdPartyDimension.getIp()) && StringUtils.isNotBlank(thirdPartyDimension.getUserAgent())) {
                return "ip";
            }
        } else if (os == Bid.BidRequest.Device.OS.IOS_VALUE) {
            if (StringUtils.isNotBlank(thirdPartyDimension.getIdfa())) {
                return "idfa";
            } else if (StringUtils.isNotBlank(thirdPartyDimension.getCaid())) {
                return "caid";
            } else if (StringUtils.isNotBlank(thirdPartyDimension.getIp()) && StringUtils.isNotBlank(thirdPartyDimension.getUserAgent())) {
                return "ip";
            }
        }
        return "";
    }
}
