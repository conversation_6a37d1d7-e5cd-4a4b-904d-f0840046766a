package com.youdao.ead.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youdao.ead.constant.OsTypeTexts;
import com.youdao.ead.vo.request.BaiduClickRequest;
import com.youdao.quipu.avro.schema.Caid;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import outfox.ead.dsp.protocol.youdao.Bid;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/28.
 */
public class BaiduClickConverter {

    private static final String BAIDU_OS_TYPE_ANDROID = "2";
    private static final String BAIDU_OS_TYPE_IOS = "1";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    /**
     * 映射为有道osType
     *
     * @return
     */
    public static String transformOsType(String osType) {
        if (BAIDU_OS_TYPE_ANDROID.equals(osType)) {
            return OsTypeTexts.ANDROID;
        } else if (BAIDU_OS_TYPE_IOS.equals(osType)) {
            return OsTypeTexts.IOS;
        } else {
            return OsTypeTexts.UNKNOWN;
        }
    }

    public static Bid.BidRequest.Device.OS transformOs(String osType) {
        if (BAIDU_OS_TYPE_ANDROID.equals(osType)) {
            return Bid.BidRequest.Device.OS.ANDROID;
        } else if (BAIDU_OS_TYPE_IOS.equals(osType)) {
            return Bid.BidRequest.Device.OS.IOS;
        } else {
            return Bid.BidRequest.Device.OS.UNKONWN;
        }
    }

    public static List<Caid> transformCaid(String caidStr) {
        if (StringUtils.isNotBlank(caidStr)) {
            try {
                List<BaiduClickRequest.CaidVendor> caidVendors = OBJECT_MAPPER.readValue(caidStr, new TypeReference<List<BaiduClickRequest.CaidVendor>>() {
                });
                if (CollectionUtils.isNotEmpty(caidVendors)) {
                    List<Caid> caidList = new ArrayList<>();
                    for (BaiduClickRequest.CaidVendor caidVendor : caidVendors) {
                        List<BaiduClickRequest.Caid> caids = caidVendor.getCaid();
                        for (BaiduClickRequest.Caid baiduCaid : caids) {
                            Caid.Builder builder = Caid.newBuilder();
                            builder.setCaid(baiduCaid.getCaid());
                            builder.setVersion(baiduCaid.getVersion());
                            builder.setCaidMd5(DigestUtils.md5Hex(baiduCaid.getCaid()));
                            caidList.add(builder.build());
                        }
                    }
                    return caidList;
                }
            } catch (Exception e) {
                // ignore exception
            }
        }
        return Collections.emptyList();
    }
}
