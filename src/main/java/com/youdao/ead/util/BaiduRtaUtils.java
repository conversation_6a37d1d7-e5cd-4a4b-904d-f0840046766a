package com.youdao.ead.util;

import com.youdao.ead.constant.ChannelDidConstants;
import com.youdao.ead.vo.request.ThirdRtaRequest;
import com.youdao.quipu.avro.schema.Caid;
import org.apache.commons.collections4.CollectionUtils;
import outfox.ead.data.rta.BaiduRtaMessage;
import outfox.ead.dsp.protocol.youdao.Bid;

import java.util.*;

import static com.youdao.ead.constant.PlatformCodes.TDP_PLATFORM_BAIDU;
import static com.youdao.ead.util.CommonUtils.escapeBdDefaultValue;

/**
 * <AUTHOR>
 * @date 2024/3/26.
 */
public class BaiduRtaUtils {
    public static ThirdRtaRequest buildRtaRequest(Set<Long> aids, BaiduRtaMessage.RtaApiRequest rtaApiRequest) {
        ThirdRtaRequest.ThirdRtaRequestBuilder builder = ThirdRtaRequest.builder();
        builder.aids(new ArrayList<>(aids));
        builder.tdpPlatform(TDP_PLATFORM_BAIDU);
        builder.channel(ChannelDidConstants.BAIDU);
        builder.reqId(UUID.randomUUID().toString());
        BaiduRtaMessage.OsType osType = rtaApiRequest.getOsType();
        Bid.BidRequest.Device.OS os = Bid.BidRequest.Device.OS.UNKONWN;
        switch (osType) {
            case ANDROID:
                os = Bid.BidRequest.Device.OS.ANDROID;
                break;
            case IOS:
                os = Bid.BidRequest.Device.OS.IOS;
                break;
            case UNKNOWN:
                os = Bid.BidRequest.Device.OS.UNKONWN;
                break;
            default:
                break;
        }
        builder.os(os.getNumber());
        BaiduRtaMessage.DeviceInfo deviceInfo = rtaApiRequest.getDeviceInfo();
        builder.imeiMd5(escapeBdDefaultValue(new String(deviceInfo.getImeiMd5().toByteArray())));
        builder.oaidMd5(escapeBdDefaultValue(new String(deviceInfo.getOaidMd5().toByteArray())));
        builder.oaid(escapeBdDefaultValue(new String(deviceInfo.getOaid().toByteArray())));
        builder.idfa(escapeBdDefaultValue(new String(deviceInfo.getIdfa().toByteArray())));
        builder.idfaMd5(escapeBdDefaultValue(new String(deviceInfo.getIdfaMd5().toByteArray())));
        builder.androidIdMd5(escapeBdDefaultValue(new String(deviceInfo.getAndroidIdMd5().toByteArray())));
        List<BaiduRtaMessage.CaidInfo> caidInfoList = deviceInfo.getCaidInfoList();
        if (CollectionUtils.isNotEmpty(caidInfoList)) {
            List<Caid> caids = new ArrayList<>();
            for (BaiduRtaMessage.CaidInfo caidInfo : caidInfoList) {
                Caid.Builder caidBuilder = Caid.newBuilder();
                caidBuilder.setCaid(escapeBdDefaultValue(new String(caidInfo.getCaid().toByteArray())));
                caidBuilder.setVersion(escapeBdDefaultValue(new String(caidInfo.getCaidVersion().toByteArray())));
                caids.add(caidBuilder.build());
            }
            builder.caids(caids);
        }
        return builder.build();
    }

    /**
     * 将rta参竞结果转成百度响应的格式
     * @param filterActivityIds
     * @return
     */
    public static BaiduRtaMessage.RtaApiResponse convert(BaiduRtaMessage.RtaApiRequest rtaApiRequest,
                                                         Map<Long, Set<Long>> baiduRtaId2ActivityIds,
                                                         List<Long> filterActivityIds) {
        BaiduRtaMessage.RtaApiResponse.Builder builder = BaiduRtaMessage.RtaApiResponse.newBuilder();
        Set<Long> acceptRtaIds = extractAcceptRtaIds(baiduRtaId2ActivityIds, filterActivityIds);
        if (CollectionUtils.isNotEmpty(acceptRtaIds)) {
            builder.setRes(BaiduRtaMessage.ResType.PART);
            for (Long acceptRtaId : acceptRtaIds) {
                BaiduRtaMessage.RtaStrategyAdResult.Builder strategyAdResultBuilder = BaiduRtaMessage.RtaStrategyAdResult.newBuilder();
                strategyAdResultBuilder.setRtaId(acceptRtaId);
                builder.addStrategyResults(strategyAdResultBuilder.build());
            }
        } else {
            builder.setRes(BaiduRtaMessage.ResType.NONE);
        }
        builder.setQid(rtaApiRequest.getQid());
        return builder.build();
    }

    /**
     * @param rtaApiRequest
     * @return
     */
    public static BaiduRtaMessage.RtaApiResponse reject(BaiduRtaMessage.RtaApiRequest rtaApiRequest) {
        BaiduRtaMessage.RtaApiResponse.Builder builder = BaiduRtaMessage.RtaApiResponse.newBuilder();
        builder.setRes(BaiduRtaMessage.ResType.NONE);
        builder.setQid(rtaApiRequest.getQid());
        return builder.build();
    }

    /**
     * 提取百度参竞的rtaId
     * @param baiduRtaId2ActivityIds
     * @param filteredAids
     * @return
     */
    private static Set<Long> extractAcceptRtaIds(Map<Long, Set<Long>> baiduRtaId2ActivityIds, List<Long> filteredAids) {
        if (CollectionUtils.isEmpty(filteredAids)) {
            return baiduRtaId2ActivityIds.keySet();
        }
        Set<Long> acceptRtaIds = new HashSet<>();
        baiduRtaId2ActivityIds.forEach((baiduRtaId, aids) -> {
            // 百度rtaId对应的推广活动id均未被过滤
            if (!CollectionUtils.containsAny(aids, filteredAids)) {
                acceptRtaIds.add(baiduRtaId);
            }
        });
        return acceptRtaIds;
    }
}
