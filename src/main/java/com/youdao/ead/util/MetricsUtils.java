package com.youdao.ead.util;

import com.codahale.metrics.Meter;
import com.codahale.metrics.Timer;
import com.youdao.ead.service.HttpService;
import com.youdao.ead.service.MetricsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Slf4j
public class MetricsUtils {
    private static Map<String, Timer> callbackTimerMap = new ConcurrentHashMap<>();
    private static Map<String, Meter> callbackExceptionMeterMap = new ConcurrentHashMap<>();


    public static Timer getCallbackTimer(String name) {
        return callbackTimerMap.computeIfAbsent(name, x -> MetricsService.metricRegistry.timer(MetricsService.name(HttpService.class, name + ".callbackTimer")));
    }

    public static Meter getCallbackExceptionMeter(String name) {
        return callbackExceptionMeterMap.computeIfAbsent(name, x -> MetricsService.metricRegistry.meter(MetricsService.name(HttpService.class, name + ".callbackExceptionMeter")));
    }

    public static String extractDomain(String url) {
        if (StringUtils.isNotBlank(url)) {
            try {
                if(!url.startsWith("http://") && !url.startsWith("https://")){
                    url = "http://" + url;
                }
                url = StringUtils.substringBefore(url, "?");
                URI uri = new URI(url);
                String host = uri.getHost();
                String domain = host.startsWith("www.") ? host.substring(4) : host;
                return domain.replaceAll("\\.", "_");
            } catch (Throwable e) {
                log.error("extract domain error", e);
                return "unknown";
            }
        } else {
            return "unknown";
        }

    }



}
