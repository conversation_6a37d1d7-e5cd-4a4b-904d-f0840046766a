package com.youdao.ead.util;

import com.youdao.ead.constant.PlatformCodes;
import com.youdao.ead.constant.ChannelDidConstants;
import com.youdao.ead.exception.BizException;
import com.youdao.ead.vo.request.OppoRtaRequest;
import com.youdao.ead.vo.request.ThirdRtaRequest;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import outfox.ead.dsp.protocol.youdao.Bid;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Set;

public class OppoRtaRequestUtils {
    private static String CHANNEL = "OPPO";

    public static void validate(OppoRtaRequest oppoRtaRequest, String oppoRtaSecretKey) {
        ValidationUtils.validation(oppoRtaRequest);
        if (StringUtils.isAllBlank(oppoRtaRequest.getImeiMd5(), oppoRtaRequest.getOaid())) {
            throw new BizException("all device id empty");
        }
        if (!StringUtils.equals(CHANNEL, oppoRtaRequest.getChannelName())) {
            throw new BizException("channel error");
        }
    }

    private static void validateSign(OppoRtaRequest oppoRtaRequest, String oppoRtaSecretKey) {
        String sign = DigestUtils.md5Hex(oppoRtaRequest.getSignContent() + oppoRtaSecretKey);
        if (!StringUtils.equals(sign, oppoRtaRequest.getSign())) {
            throw new BizException("sign validate failed!");
        }
    }


    public static ThirdRtaRequest oppoRtaReq2ThirdRtaRequest(OppoRtaRequest oppoRtaRequest, Set<Long> aids) throws UnsupportedEncodingException {
        Bid.BidRequest.Device.OS os = Bid.BidRequest.Device.OS.ANDROID;
        ThirdRtaRequest.ThirdRtaRequestBuilder builder = ThirdRtaRequest.builder()
                .reqId(oppoRtaRequest.getRequestId())
                .aids(new ArrayList<>(aids))
                .os(os.getNumber())
                .ip(oppoRtaRequest.getClientIp())
                .channel(ChannelDidConstants.OPPO)
                .tdpPlatform(PlatformCodes.TDP_PLATFORM_OPPO);
        // IMEI设备号绝大多数情况下为数字组成，即使有字母，也是大写字母。WIKI:https://en.wikipedia.org/wiki/International_Mobile_Equipment_Identity
        // 因此，虽然oppo的规则为原始值->md5，和智选的imei->大写->md5->大写的规则不一致，但是也可以使用
        if (StringUtils.isNotBlank(oppoRtaRequest.getImeiMd5())) {
            builder.imeiMd5(oppoRtaRequest.getImeiMd5().toUpperCase());
        }
        if (StringUtils.isNotBlank(oppoRtaRequest.getOaid())) {
            builder.oaid(oppoRtaRequest.getOaid());
            builder.oaidMd5(CommonUtils.generateMd5HexUpper(oppoRtaRequest.getOaid()));
        }
        return builder.build();
    }
}
