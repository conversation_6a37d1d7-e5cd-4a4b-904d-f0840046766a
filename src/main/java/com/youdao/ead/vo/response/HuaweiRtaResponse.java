package com.youdao.ead.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.youdao.ead.constant.HuaweiRtaConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HuaweiRtaResponse {
    /**
     * 返回码，0表示正常，其他表示异常
     */
    private Integer retCode;
    /**
     * 返回的错误信息
     */
    private String errMsg;
    /**
     * yes/no, yes 请求中账户全部生效， no请求中账户全部不生效。 retCode=0, result/rtaIdList/userList/userInfoList 至少返回一个。
     */
    private String result;
    /**
     * 按照返回的rtaId生效。
     */
    private Set<String> rtaIdList;
    /**
     * 按照返回的账户id生效。
     */
    private Set<String> userList;
    /**
     * 用户质量分
     */
    private Double score;
    /**
     * 账户商品信息，包括商品ID、商品列 表、出价等。结构详见UserInfo
     */
    private List<UserInfo> userInfoList;
    /**
     * 实验id
     */
    private List<String> expId;

    @Data
    private static class UserInfo {
        /**
         * rtaId列 表
         * rtaIdList和userIdList如果都没有，则 platformId、productList、priceWeight也没 有
         */
        private List<String> rtaIdList;
        /**
         * userId 列表
         * 否，rtaIdList和userIdList如果都没有，则 platformId、productList、priceWeight也没 有
         */
        private List<String> userIdList;
        /**
         * 商品库ID，如需返回商品，商品库id则必填
         */
        private String platformId;
        /**
         * 商品库 的推荐 模式
         * 默认行为推荐 。1: LBS推荐 ，0: 行为推 荐
         */
        private Integer recMode;
        /**
         * 多个商品Id ，如带商品库信息productList和  productExtList至少携带一个
         */
        private List<String> productList;
        /**
         * 带分值 的商品 列表
         * 如带商品库信息productList和 productExtList至少携带一个。productExtList 优先级高于productList。
         */
        private List<Product> productExtList;
        /**
         * 出价比例
         */
        private Double priceWeight;
        /**
         * 出价
         */
        private PriceInfo price;
        /**
         * 指定的创意ID 如有返回值则只投放指定的创意，最多只支持100个，超出部分弃用
         */
        private List<Long> creativeIds;
        /**
         * ocpc出价列表
         */
        private List<OcpcPriceInfo> ocpcPriceInfoList;

    }

    @Data
    private static class Product {
        /**
         * 商品ID
         */
        private String productId;
        /**
         * 商品打分 0-4分，4分表 示推荐可靠度 高。
         */
        private Integer score;
    }

    @Data
    private static class PriceInfo {
        /**
         * 出价，出价范围与投放端一致
         */
        private Double cpm;
        /**
         * 出价，出价范围与投放端一致
         */
        private Double cpc;
        /**
         * 出价，出价范围与投放端一致
         */
        private Double cpd;
        /**
         * 出价，出价范围与投放端一致
         */
        private Double cpi;
        /**
         * 出价，出价范围与投放端一致
         */
        private Double cpa;
        /**
         * 币种，默认使用广告主币种(仅支持 CNY、USD、EUR、GBP、JPY等5种， 海外广告投放场景使用，国内不要填)
         */
        private String currency;
    }

    @Data
    private static class OcpcPriceInfo {
        /**
         * 目标转化类型(具体看convType数据字 典)
         */
        private Integer convType;
        /**
         * 可指定币种，不传默认使用任务币种
         */
        private String currency;
        /**
         * 出价，根据目标转化类型返回不同出价
         */
        private Double price;
        /**
         * 出价比例，根据目标转化类型返回不同出 价比例，取值需大于等于0
         */
        private Double priceWeight;
    }

    public static HuaweiRtaResponse exception(String errMsg) {
        return HuaweiRtaResponse.builder().retCode(HuaweiRtaConstants.ERROR_RESPONSE).errMsg(errMsg).build();
    }

    /**
     * 获取华为RTA正常响应情况下的返回体 <br/>
     * 华为要求只返回1种列表所以取 biddingRtaIds和biddingUserList中第一个不为空的set返回
     */
    public static HuaweiRtaResponse getNormalResponse(Set<String> biddingRtaIds, Set<String> biddingUserList) {
        HuaweiRtaResponseBuilder builder = HuaweiRtaResponse.builder();
        builder.retCode(HuaweiRtaConstants.NORMAL_RESPONSE);
        if (CollectionUtils.isNotEmpty(biddingRtaIds)) {
            builder.rtaIdList(biddingRtaIds);
        } else if (CollectionUtils.isNotEmpty(biddingUserList)) {
            builder.userList(biddingUserList);
        } else {
            builder.rtaIdList(Collections.emptySet());
        }
        return builder.build();
    }
}
