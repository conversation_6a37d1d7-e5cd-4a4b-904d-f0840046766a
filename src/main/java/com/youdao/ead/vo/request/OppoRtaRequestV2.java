package com.youdao.ead.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Set;

/**
 * OPPO RTA 请求类
 *
 * <AUTHOR>
 */
@Data
public class OppoRtaRequestV2 {
    /**
     * 请求 id
     */
    @NotBlank
    @JsonProperty("request_id")
    private String requestId;
    /**
     * imei 原始值直接进行 md5 处理，
     */
    @JsonProperty("imei_md5")
    private String imeiMd5;
    /**
     * oaid 明文
     */
    private String oaid;


    @JsonProperty("oaid_md5")
    private String oaidMd5;

    /**
     * 策略id列表
     */
    @JsonProperty("ad_type_list")
    private Set<Long> adTypeList;

    @JsonProperty("request_time")
    private Long requestTime;

}
