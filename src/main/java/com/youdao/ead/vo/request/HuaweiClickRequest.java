package com.youdao.ead.vo.request;

import com.youdao.ead.resolver.ParamName;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.net.URLEncoder;

/**@see <a href="https://developer.huawei.com/consumer/cn/doc/distribution/promotion/ads_gongju08-0000001469108293">huawei api</a>
 * <AUTHOR>
 * @date 2023/1/16.
 */
@Data
public class HuaweiClickRequest implements ConvExtAble {
    @NotBlank
    @ParamName("content_id")
    private String contentId;
    @NotBlank
    @ParamName("adgroup_id")
    private String adGroupId;
    @NotBlank
    @ParamName("campaign_id")
    private String campaignId;
    @ParamName("oaid")
    private String oaid;
    @NotBlank
    @ParamName("tracking_enabled")
    private String trackingEnabled;
    @ParamName("ip")
    private String ip;
    @ParamName("user_agent")
    private String userAgent;
    @ParamName("event_type")
    private String eventType;
    @ParamName("trace_time")
    private String traceTime;
    @NotBlank
    private String callback;
    @ParamName("corp_id")
    private String corpId;
    @ParamName("app_id")
    private String appId;
    @ParamName("campaign_name")
    private String campaignName;
    @ParamName("adgroup_name")
    private String adGroupName;
    @ParamName("content_name")
    private String contentName;
    @ParamName("deep_link")
    private String deepLink;
    @ParamName("os_version")
    private String osVersion;
    @ParamName("emui_version")
    private String emuiVersion;
    @ParamName("transunique_id")
    private String transUniqueId;
    @ParamName("id_type")
    private String idType;
    @ParamName("device_id")
    private String deviceId;
    @ParamName("publisher_type")
    private String publisherType;
    @ParamName("publisher_app_id")
    private String publisherAppId;
    @ParamName("log_id")
    private String logId;

    @Override
    public String suffixOfConvExtKey() {
        return this.getContentId();
    }

    @Override
    public String contextOfConvDebug() throws Exception {
        String callback = this.urlEncodeCallBack();
        return String.join("&", this.getCampaignId(), this.getContentId(), callback);
    }

    public String urlEncodeCallBack() throws Exception {
        return URLEncoder.encode(getCallback(), "UTF-8").replaceAll("\\+", "%20");
    }
}
