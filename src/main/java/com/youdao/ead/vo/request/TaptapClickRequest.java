package com.youdao.ead.vo.request;

import com.youdao.ead.resolver.ParamName;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class TaptapClickRequest implements ConvExtAble {
    /**
     * iOS 设备的 IDFA，使⽤原始的⼤写的值，不存在时传递空字符
     */
    @ParamName("idfa")
    private String idfa;

    @ParamName("idfaMd5")
    private String idfaMd5;

    /**
     * 时间戳，1970 年到当前时间的秒数
     */
    @ParamName("time")
    private String time;

    /**
     * iOS 设备中国广告协会互联网广告标识，包含最新两个版本的 CAID 和版本号。 URL Encode 后的 JSON 数组UrlEncode("[{\"caid\":\"81bcddc91ea559bb711cfaf0b55cae52\",\"version\":\"20211207\"},{\"caid\":\"0f1e07a50956d6af4a63c79106fa3e68\",\"version\":\"20220111\"}]")
     */
    @ParamName("caid")
    private String caid;

    /**
     * Android 设备的 IMEI, 使用原始的数据值，不存在时传递空字符串
     */
    @ParamName("imei")
    private String imei;

    /**
     * Android 设备的 OAID, 使用原始的数据值，不存在时传递空字符串
     */
    @ParamName("oaid")
    private String oaid;

    @ParamName("oaidMd5")
    private String oaidMd5;

    /**
     * 用户的 IP 地址
     */
    @ParamName("ip")
    private String ip;

    /**
     * 用户的 IPV6 地址
     */
    @ParamName("ipv6")
    private String ipv6;

    /**
     * 账户 ID
     */
    @ParamName("orgId")
    private String orgId;

    /**
     * 账户名称
     */
    @ParamName("orgName")
    private String orgName;

    /**
     * 游戏名称
     */
    @ParamName("gameName")
    private String gameName;

    /**
     * 广告计划 ID，补量默认为“999999”
     */
    @ParamName("adsetId")
    private String adsetId;

    /**
     * 广告计划名称，补量默认为“SupplementCampaign”
     */
    @ParamName("adsetName")
    private String adsetName;

    /**
     * 创意 ID，补量默认值为“9999”
     */
    @NotBlank
    @ParamName("creativeId")
    private String creativeId;

    /**
     * 下载类型，分广告下载“TapTapAd”和间接下载“TapTapNature”（目前仅下发广告下载）
     */
    @ParamName("conversionType")
    private String conversionType;

    /**
     * 设备类型，iOS 下发“1”，android 下发“0”
     */
    @ParamName("device")
    private String device;

    /**
     * 设备品牌
     */
    @ParamName("deviceBrand")
    private String deviceBrand;

    /**
     * 设备型号
     */
    @ParamName("deviceModel")
    private String deviceModel;

    /**
     * 系统版本号
     */
    @ParamName("osVersion")
    private String osVersion;

    /**
     * 浏览器 UA
     */
    @ParamName("webUa")
    private String webUa;

    /**
     * 广告归因 ID:用于深度回传服务
     */
    @ParamName("tapTrackId")
    private String tapTrackId;

    /**
     * 深度回传链接(激活、注册、付费、次留等)，按照要求拼接上相关参数回传深度事件
     */
    @NotBlank
    @ParamName("deepCallbackUrl")
    private String deepCallbackUrl;

    @Override
    public String suffixOfConvExtKey() { return getCreativeId(); }

    @Override
    public String contextOfConvDebug() {
        return this.getDeepCallbackUrl();
    }

    @Data
    public static class Caid {
        private String caid;
        private String version;
    }
}
