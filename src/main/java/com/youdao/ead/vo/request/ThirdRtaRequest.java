package com.youdao.ead.vo.request;

import com.youdao.ead.constant.PlatformCodes;
import com.youdao.ead.mobilemodels.util.MobileModelProvider;
import com.youdao.ead.util.DeviceModelRenderUtils;
import com.youdao.ead.validator.CombinedNotNull;
import com.youdao.quipu.avro.schema.Caid;
import io.gromit.uaparser.model.Client;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.caid.sdk.DeviceInfo;
import outfox.ead.data.protobuf.DataHolder;
import outfox.ead.dsp.protocol.youdao.Bid;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2022/11/10.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@CombinedNotNull(fields = {
        "imei",
        "imeiMd5",
        "androidIdMd5",
        "oaid",
        "oaidMd5",
        "idfa",
        "idfaMd5",
        "caid",
        "caidMd5"
})
public class ThirdRtaRequest {
    /**
     * 推广平台, 非接口字段
     */
    private String tdpPlatform;
    /**
     * 标记是否为apiRta请求
     */
    private boolean innerRta;

    private String reqId;
    private List<Long> aids;

    /**
     * 渠道标识
     */
    private String channel;

    private String imei;
    private String imeiMd5;
    private String androidIdMd5;
    private String oaid;
    private String oaidMd5;
    private String idfa;
    private String idfaMd5;
    private String caid;
    private String caidMd5;
    private Integer os;
    private String ip;
    private String ua;
    private String model;
    private String deviceStartSec;
    private String countryCode;
    private String language;
    private String deviceNameMd5;
    private String osVersion;
    private String deviceMachine;
    private String deviceModel;
    private String carrier;
    private String memory;
    private String disk;
    private String systemUpdateSec;
    private String timeZone;
    private String mntId;
    private String deviceInitSec;

    /**
     * 每个aid对应的广告位信息
     */
    private Map<Long, DataHolder.SlotAllocation> slotAllocationMap;
    /**
     * 参竞且需要记录相应的req缓存的数据，不会包含api-rta策略的rta请求
     * key=aid, value=rtaMappingType + "_" + rtaMappingId
     */
    private final Map<Long, String> aidBidLogMap = new ConcurrentHashMap<>();

    private Client userAgent;
    private String fixBrand;
    private Bid.BidRequest.Device.OS osEnum;

    private double threadLocalRandom = ThreadLocalRandom.current().nextDouble();
    private boolean antiFraudOpen = false;
    private boolean antiFraudAsyncLoadDataOpen = false;

    public DeviceInfo getDeviceInfo() {
        return DeviceInfo
                .builder()
                .boot_time_in_sec(deviceStartSec)
                .country_code(countryCode)
                .language(language)
                .device_name(deviceNameMd5)
                .system_version(osVersion)
                .machine(deviceMachine)
                .model(deviceModel)
                .carrier_info(carrier)
                .memory(memory)
                .disk(disk)
                .sys_file_time(systemUpdateSec)
                .time_zone(timeZone)
                .mnt_id(Objects.toString(mntId, ""))
                .device_init_time(Objects.toString(deviceInitSec, ""))
                .build();
    }

    @Builder.Default
    private List<Caid> caids = new ArrayList<>();


    /**
     * 排除不需要记录rta参竞记录的请求：部分直投平台 或者 api-rta 场景
     *
     * @return
     */
    public boolean excludePersistCase() {
        return PlatformCodes.RTA_REQUEST_PERSIST_EXCLUDE_PLATFORMS.contains(tdpPlatform) || innerRta;
    }

    public void updateUserAgentClientAndBrandModel(MobileModelProvider mobileModelProvider) {
        updateUserAgentClientAndBrandModel(mobileModelProvider, this.getUa());
    }
    public void updateUserAgentClientAndBrandModel(MobileModelProvider mobileModelProvider, String fixedUa) {
        Client userAgentClient = DeviceModelRenderUtils.getUserAgentClient(fixedUa);
        this.setUserAgent(userAgentClient);
        if (userAgentClient != null) {
            if (userAgentClient.device != null) {
                // 优先用model映射得到的brand，没有的话再用解析的brand替补。
                String tryFixedBrand = mobileModelProvider.getMobileBrand(userAgentClient.device.model);
                String parsedUaBrand = Objects.toString(userAgentClient.device.brand, "");
                tryFixedBrand = tryFixedBrand != null ? tryFixedBrand : parsedUaBrand;
                this.setFixBrand(tryFixedBrand);
            }
        }
    }
}
