package com.youdao.ead.data;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.SerializedName;
import com.youdao.ead.constant.ProcessCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 标准转化方案请求的响应信息
 * <p>
 * Created by ya<PERSON><PERSON> on 2018/2/27.
 */
@ToString
public class ConvertTrackingResponse {
    private static final String TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final Gson GSON = new GsonBuilder().disableHtmlEscaping().create();

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(TIME_PATTERN);

    /**
     * 处理请求时的时间，格式为 'yyyy-MM-dd HH:mm:ss'
     */
    @Setter
    @Getter
    private String time;

    /**
     * 响应代码，success 表示成功，fail 表示失败。
     * <p>
     * 注意：响应代码不要随意更改
     */
    @Setter
    @Getter
    private String code;

    /**
     * 响应消息, 会给出成功消息或者失败原因
     */
    @Setter
    @Getter
    private String msg;

    @Setter
    @Getter
    @SerializedName("process_code")
    private int processCode;

    public static String getResponse(String code, ProcessCode error) {
        ConvertTrackingResponse convertTrackingResponse = new ConvertTrackingResponse();

        convertTrackingResponse.setCode(code);
        convertTrackingResponse.setProcessCode(error.getProcessCode());
        convertTrackingResponse.setMsg(error.getMsg());
        convertTrackingResponse.setTime(dateTimeFormatter.format(LocalDateTime.now()));

        return GSON.toJson(convertTrackingResponse);
    }
}
