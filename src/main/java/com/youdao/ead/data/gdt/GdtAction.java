package com.youdao.ead.data.gdt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/22
 */
@Data
public class GdtAction {
    /**
     * 转化行为时间
     */
    @JsonProperty("action_time")
    @NotNull
    private String actionTime;

    /**
     * 转化行为类型（广点通平台枚举）
     */
    @JsonProperty("action_type")
    @NotNull
    private String actionType;

    /**
     * 设备信息
     */
    @JsonProperty("user_id")
    @NotNull
    private GdtUserId userId;

    /**
     * 转化行为的相关参数
     */
    @JsonProperty("action_param")
    private Map<String, String> actionParam;

    public void buildActionParam() {
        actionParam = Collections.emptyMap();
    }


}
