package com.youdao.ead.data;

import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * @see <a href =“https://open-ad.vivo.com.cn/doc/index?id=217”>接口文档地址 </a>
 * <AUTHOR>
 * @date 2023/1/4.
 */
@Data
@ToString
public class VivoConvCallback {
    private String srcType;
    private String pkgName;
    private String pageUrl;
    private String srcId;
    private List<CallbackData> dataList;

    @Data
    public static class CallbackData {
        private String userIdType;
        private String userId;
        private String cvType;
        private Long cvTime;
        private String cvParam;
        private String cvCustom;
        private String requestId;
        private String creativeId;
        private String dlrSrc;
        private String installReferer;
        private Map<String, String> extParam;

    }
}
