package com.youdao.ead.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 反作弊规则
 *
 * <AUTHOR>
 * @date 2024/7/9
 */
@Data
public class AntiFraudRule {
    /**
     * 规则唯一标识
     */
    private long id;
    /**
     * 规则名称
     */
    private String name;
    /**
     * 状态
     */
    private int status;

    /**
     * 异常流量标签，以前设计是tags支持多个tag，后来改为了就一个tag， 目的是 为了以后用ruleId来代表单独一种过滤规则来处理，如果tag有多个这个ruleId含义就不唯一了
     */
    private String tag;

    /**
     * 屏蔽子项
     */
    private Map<String, List<String>> extendInfo;


}
