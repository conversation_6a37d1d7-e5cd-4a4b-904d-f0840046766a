package com.youdao.ead.pacioli.entity.pacioli;

import com.youdao.ead.pacioli.core.converter.SetLongJpaConverter;
import com.youdao.ead.pacioli.core.converter.SetStringAllowEmptyConverter;
import com.youdao.ead.pacioli.core.converter.SetStringJpaConverter;
import com.youdao.ead.pacioli.core.converter.Sponsor2ChannelRelateDTOJpaConverter;
import com.youdao.ead.pacioli.dto.antifraud.Sponsor2ChannelRelationDTO;
import com.youdao.ead.pacioli.enums.antifraud.RelateActivityRefreshModeEnum;
import com.youdao.ead.pacioli.enums.antifraud.RelateChannelRefreshModeEnum;
import com.youdao.ead.pacioli.enums.antifraud.TriggerModeEnum;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

import javax.naming.Name;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Comment("反作弊配置")
@Table(name = "anti_fraud_config")
public class AntiFraudConfig extends BaseEntity {

    @Column(name = "name")
    @Comment("屏蔽规则名称")
    private String name;

    @Column(name = "status")
    @Comment("状态 0-生效中 1-暂停")
    private Integer status;

    @Column(name = "trigger_mode")
    @Enumerated(EnumType.STRING)
    @Comment("流量异常类型生效方式 ANY_ONE-触发任意一条 ALL-同时触发")
    private TriggerModeEnum triggerMode;

    @Comment("关联的规则ID")
    @Column(name = "rule_ids")
    @Convert(converter = SetLongJpaConverter.class)
    private Set<Long> ruleIds;

    @Comment("关联的规则ID")
    @Column(name = "rule_ids_rta")
    @Convert(converter = SetLongJpaConverter.class)
    private Set<Long> ruleIdsRta;

    @Comment("关联推广活动刷新模式 NORMAL-常规 AUTO-自动")
    @Enumerated(EnumType.STRING)
    @Column(name = "relate_activity_refresh_mode")
    private RelateActivityRefreshModeEnum relateActivityRefreshMode;

    @Comment("关联的推广活动ID")
    @Column(name = "relate_activity_ids")
    @Convert(converter = SetLongJpaConverter.class)
    private Set<Long> relateActivityIds;

    @Comment("关联的渠道ID, 其中-1代表未知渠道")
    @Column(name = "relate_channel_ids")
    @Convert(converter = SetLongJpaConverter.class)
    private Set<Long> relateChannelIds;

    @Comment("关联的广告主ID")
    @Column(name = "relate_sponsor_ids")
    @Convert(converter = SetLongJpaConverter.class)
    private Set<Long> relateSponsorIds;

    @Comment("广告主其下渠道，只有运营和管理员角色创建自动生效规则的时候需要用到")
    @Column(name = "sponsor_relate_channel")
    @Convert(converter = Sponsor2ChannelRelateDTOJpaConverter.class)
    private Set<Sponsor2ChannelRelationDTO> sponsorRelateChannel;

    @Column(name = "filter_ratio")
    @Comment("过滤比例，取值范围：[0.0001, 1]")
    private Double filterRatio;

    @Comment("关联渠道刷新模式 NORMAL-常规 AUTO-自动")
    @Enumerated(EnumType.STRING)
    @Column(name = "relate_channel_refresh_mode")
    private RelateChannelRefreshModeEnum relateChannelRefreshMode;

    @Comment("关联的渠道did")
    @Convert(converter = SetStringAllowEmptyConverter.class)
    @Column(name = "relate_channel_dids")
    private Set<String> relateChannelDids;

    @Comment("删除")
    @Column(name = "deleted")
    private Boolean deleted;
}
