package com.youdao.ead.pacioli.entity.pacioli;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
@Entity
@Getter
@Setter
@ToString
@Comment("子任务")
@RequiredArgsConstructor
@Table(name = "sub_task")
public class SubTask extends BaseEntity {
    @Size(max = 100)
    @Column(name = "name", nullable = false, length = 100, unique = true)
    @Comment("子任务名称")
    private String name;

    @Column(name = "task_id", nullable = false)
    @Comment("任务ID")
    private Long taskId;

    @Column(name = "advisor_id", nullable = false)
    @Comment("顾问ID")
    private Long advisorId;

    @Lob
    @Column(name = "promotion_link", nullable = false)
    @Comment("推广链接")
    private String promotionLink;

    @Column(name = "task_amount", nullable = false)
    @Comment("任务量")
    private Long taskAmount;

    @Column(name = "channel_id")
    @Comment("当前渠道ID")
    private Long channelId;

    @Column(name = "channel_cpa_unit_price")
    @Comment("当前渠道CPA单价，单位分")
    private Long channelCpaUnitPrice;

    @Column(name = "effective_date", nullable = false)
    @Comment("生效日期")
    private LocalDate effectiveDate;

    @Column(name = "state", nullable = false)
    @Comment("状态 0-已开启 1-已暂停")
    private Byte state;

    @Column(name = "bd_user_id")
    @Comment("反馈信息BD人员ID")
    private Long bdUserId;

    @Size(max = 200)
    @Column(name = "bd_feedback", length = 200)
    @Comment("BD反馈信息")
    private String bdFeedback;

    @Size(max = 500)
    @Column(name = "extend_conversion_action_ids", nullable = false, length = 500)
    @Comment("除激活外新增的转化列，json array格式，eg，[0,1,2]")
    @ColumnDefault("[]")
    private String extendConversionActionIds;

    @Column(name = "effective_conversion_action_id", nullable = false)
    @Comment("当前有效转化ID")
    private Long effectiveConversionActionId;

    @Column(name = "role_key_pause")
    @Comment("暂停子任务的角色值")
    private String roleKeyPause;

    @Column(name = "activity_id", nullable = false)
    @Comment("活动id，0为无效的活动id")
    private Long activityId;

}
