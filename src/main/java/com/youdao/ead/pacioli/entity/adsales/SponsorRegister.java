package com.youdao.ead.pacioli.entity.adsales;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @create 2024-11-14 10:28
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Entity
@Table(name = "SPONSOR_REGISTER")
public class SponsorRegister {
    @Id
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "SPONSOR_ID")
    private Long sponsorId;

    @Column(name = "USER_ID")
    private Long userId;

    /**
     * 0表示准客户，1表示系统客户
     */
    @Column(name = "STATUS")
    private Integer status;
}
