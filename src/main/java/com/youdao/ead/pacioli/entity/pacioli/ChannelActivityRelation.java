package com.youdao.ead.pacioli.entity.pacioli;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR>
 * @create 2025-02-25 17:02
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Comment("channel与aid关系表")
@Table(name = "channel_activity_relation")
public class ChannelActivityRelation extends BaseEntity {
    @NotNull
    @Comment("渠道did")
    @Column(name = "channel_did", nullable = false)
    private String channelDid;

    @Comment("推广活动id")
    @Column(name = "activity_id", nullable = false)
    private Long activityId;
}
