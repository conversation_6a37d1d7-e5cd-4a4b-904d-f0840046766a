package com.youdao.ead.pacioli.entity.pacioli;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR>
 * @create 2024-10-28 16:17
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Comment("用户与角色关系，所有的id")
@Table(name = "user_role_relation")
public class UserRoleRelation extends BaseEntity {
    @NotNull
    @Comment("用户id")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Comment("角色值")
    @Column(name = "role_key", nullable = false)
    private String roleKey;

    @ColumnDefault("0")
    @Comment("角色状态；0-启用；1-禁用")
    @Column(name = "state", nullable = false)
    private Byte state;

    @ColumnDefault("0")
    @Comment("逻辑删除;0-未删除；1-已删除")
    @Column(name = "deleted", nullable = false)
    private Boolean deleted;
}