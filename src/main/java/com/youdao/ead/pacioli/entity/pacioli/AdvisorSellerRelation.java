package com.youdao.ead.pacioli.entity.pacioli;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Comment("顾问与销售关系")
@Table(name = "advisor_seller_relation")
public class AdvisorSellerRelation extends BaseEntity {

    @Comment("顾问用户ID")
    @Column(name = "advisor_user_id")
    private Long advisorUserId;

    @Comment("销售用户ID")
    @Column(name = "seller_user_id")
    private Long sellerUserId;
}
