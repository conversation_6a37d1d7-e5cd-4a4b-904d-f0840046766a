package com.youdao.ead.pacioli.entity.eadb1;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Entity
@Getter
@Setter
@ToString
@Table(name = "Sponsor")
@RequiredArgsConstructor
public class Sponsor implements Serializable {

    @Id
    @Column(name = "SPONSOR_ID", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long sponsorId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "TYPE")
    private Integer type;

    /**
     * 32位int字段，对于后台运营系统而言，字段为0时表示广告有效，否则广告无效对于业务系统而言，不同的字段表示不同的含义，需要进行解析。
     * <p>
     * @see <a href="https://confluence.inner.youdao.com/pages/viewpage.action?pageId=2525264">Confulunce Wiki</a>
     */
    @Column(name = "STATUS")
    private Integer status;

    /**
     * 智选账号
     */
    @Column(name = "USER_NAME")
    private String userName;

    @Column(name = "AGENT_ID", nullable = false)
    private Long agentId;

}
