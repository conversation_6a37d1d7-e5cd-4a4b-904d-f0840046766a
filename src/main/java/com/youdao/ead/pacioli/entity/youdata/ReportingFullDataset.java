package com.youdao.ead.pacioli.entity.youdata;

import com.youdao.ead.pacioli.entity.pacioli.BaseEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Comment("渠道数据")
@Table(name = "reporting_full_dataset")
public class ReportingFullDataset implements Serializable {

    @Id
    @Comment("自增ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @CreatedDate
    @Comment("创建时间")

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Comment("最后一次修改时间")
    @Column(name = "last_modified_time")
    private LocalDateTime lastModifiedTime;

    @Comment("数据唯一key")
    @Column(name = "data_key", length = 100, nullable = false)
    private String dataKey;

    @Comment("结算日期")
    @Column(name = "settlement_date", nullable = false)
    private LocalDate settlementDate;

    @Comment("结算广告商账号")
    @Column(name = "sponsor_email", length = 50, nullable = false)
    private String sponsorEmail;

    @Comment("推广活动id")
    @Column(name = "aid")
    private Long aid;

    @Comment("结算转化事件名称")
    @Column(name = "settlement_events", length = 50, nullable = false)
    private String settlementEvents;

    @Comment("结算量级")
    @Column(name = "settlement_volume")
    private Long settlementVolume;

    @Comment("备注说明")
    @Column(name = "remark", length = 100)
    private String remark;

    @Comment("顾问填报的激活数")
    @Column(name = "activation_count")
    private Long activationCount;

    @Comment("激活率，若顾问填报，则以填报为准，若未填报，则计算所得，计算公式=激活数/点击*100%")
    @Column(name = "activation_rate")
    private Float activationRate;

    @Comment("次留数 顾问填报")
    @Column(name = "next_day_retention")
    private Long nextDayRetention;

    @Comment("次留率 若顾问填报，则以填报为准，若未填报，则计算所得，计算公式=次留数/激活数*100%")
    @Column(name = "next_day_retention_rate")
    private Float nextDayRetentionRate;

    @Comment("注册数")
    @Column(name = "register_count")
    private Long registerCount;

    @Comment("激活-注册率 若顾问填报，则以填报为准，若未填报，则计算所得，计算公式=注册数/激活数*100%")
    @Column(name = "register_rate")
    private Float registerRate;

    @Comment("付费数 顾问填报")
    @Column(name = "payment_count")
    private Long paymentCount;

    @Comment("激活-付费率 若顾问填报，则以填报为准，若未填报，则计算所得，计算公式=付费数/激活数*100%")
    @Column(name = "payment_rate")
    private Float paymentRate;

    @Comment("首日付费金额 顾问填报")
    @Column(name = "first_day_payment_amount")
    private Long firstDayPaymentAmount;

    @Comment("3日留存率")
    @Column(name = "three_day_retention_rate")
    private Float threeDayRetentionRate;

    @Comment("7日留存率")
    @Column(name = "seven_day_retention_rate")
    private Float sevenDayRetentionRate;

    @Comment("LTV1")
    @Column(name = "LTV1")
    private Float ltv1;

    @Comment("LTV2")
    @Column(name = "LTV2")
    private Float ltv2;

    @Comment("LTV3")
    @Column(name = "LTV3")
    private Float ltv3;

    @Comment("LTV7")
    @Column(name = "LTV7")
    private Float ltv7;

    @Comment("放款数")
    @Column(name = "loans_count")
    private Long loansCount;

    @Comment("注册-放款率 若顾问填报，则以填报为准，若未填报，则计算所得，计算公式=放款数/注册数*100%")
    @Column(name = "loans_rate")
    private Float loansRate;

    @Comment("授信数")
    @Column(name = "credit_count")
    private Long creditCount;

    @Comment("注册-授信率 若顾问填报，则以填报为准，若未填报，则计算所得，计算公式=授信数/注册数*100%")
    @Column(name = "credit_rate")
    private Float creditRate;

    @Comment("离线次留率 顾问填报")
    @Column(name = "offline_retention_rate")
    private Float offlineRetentionRate;

    @Comment("首日ROI")
    @Column(name = "first_day_roi")
    private Float firstDayRoi;

    @Comment("arpu")
    @Column(name = "arpu")
    private Float arpu;

    @Comment("商机转化率")
    @Column(name = "opportunity_conversion_rate")
    private Float opportunityConversionRate;

    @Comment("相关系数")
    @Column(name = "correlation_coefficient", nullable = false)
    private Float correlationCoefficient;

    @Comment("质量分")
    @Column(name = "quality_score")
    private Float qualityScore;
}