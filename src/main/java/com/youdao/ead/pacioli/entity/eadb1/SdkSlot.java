package com.youdao.ead.pacioli.entity.eadb1;

import jakarta.persistence.*;
import lombok.Data;

import java.io.Serializable;

@Data
@Entity
@Table(name = "SdkSlot")
public class SdkSlot implements Serializable {

    @Id
    @Column(name = "SDK_SLOT_ID", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long sdkSlotId;

    /**
     * 根据对应应用包名和自身ID的MD5码
     */
    @Column(name = "SDK_SLOT_UDID", nullable = false)
    private String sdkSlotUdid;

    @Column(name = "SDK_APP_ID", nullable = false)
    private Long sdkAppId;

    @Column(name = "SDK_SLOT_NAME")
    private String sdkSlotName;
}
