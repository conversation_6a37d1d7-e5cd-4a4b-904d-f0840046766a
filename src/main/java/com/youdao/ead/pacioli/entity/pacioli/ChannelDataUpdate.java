package com.youdao.ead.pacioli.entity.pacioli;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Builder
@ToString
@Comment("渠道")
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "channel_data_update")
public class ChannelDataUpdate {
    @Id
    @Comment("自增ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @CreatedDate
    @Comment("创建时间")
    @Column(name = "create_time")
    private LocalDateTime createTime;
    @Comment("最后一次修改时间")
    @Column(name = "last_modified_time")
    private LocalDateTime lastModifiedTime;

    @Column(name = "update_user", nullable = false, length = 50)
    @Comment("修改人")
    private String updateUser;

    @Column(name = "update_field", nullable = false, length = 50)
    @Comment("修改字段")
    private String updateField;

    @Column(name = "data_key", nullable = false, length = 50)
    @Comment("dataKey")
    private String dataKey;

}
