package com.youdao.ead.pacioli.repository.youdata;

import com.youdao.ead.pacioli.entity.youdata.ReportingFullDataset;
import com.youdao.ead.pacioli.entity.youdata.ReportingFullDataset_;
import com.youdao.ead.pacioli.enums.RoleEnum;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Selection;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.youdao.ead.pacioli.constants.CommonConstants.QUERY_EXCLUDE_FIELDS;

@Repository
public interface ReportingFullDatasetRepository extends JpaRepository<ReportingFullDataset, Long>, JpaSpecificationExecutor<ReportingFullDataset> {

    List<ReportingFullDataset> findReportingFullDatasetBySponsorEmailIn(Collection<String> sponsorEmails);

    List<ReportingFullDataset> findReportingFullDatasetBySponsorEmailInAndSettlementDateIn(Collection<String> sponsorEmails, Collection<LocalDate> settlementDates);


    default List<String> findAllBySponsorEmailLike(String searchCondition) {
        return findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            query.distinct(true);
            // 如果 searchCondition 不为空，添加模糊查询条件
            if (StringUtils.isNotBlank(searchCondition)) {
                predicates.add(criteriaBuilder.like(root.get(ReportingFullDataset_.SPONSOR_EMAIL), "%" + searchCondition + "%"));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        }).stream()
                .map(ReportingFullDataset::getSponsorEmail)
                .collect(Collectors.toList());
    }


    default List<ReportingFullDataset> pageByConditions(Set<Long> activityIds,
                                                        LocalDateTime startTime,
                                                        LocalDateTime endTime,
                                                        Set<String> displayMetrics,
                                                        String sortMetrics,
                                                        Boolean isAsc,
                                                        RoleEnum currentRole) {
        return this.findAll((root, query, criteriaBuilder) -> {
            List<Selection<?>> selections = new ArrayList<>();
            selections.add(root.get(ReportingFullDataset_.CREATE_TIME));
            selections.add(root.get(ReportingFullDataset_.DATA_KEY));
            selections.add(root.get(ReportingFullDataset_.AID));
            selections.add(root.get(ReportingFullDataset_.SPONSOR_EMAIL));
            selections.add(root.get(ReportingFullDataset_.SETTLEMENT_DATE));
            List<Predicate> predicateList = new ArrayList<>();

            // 按照推广活动筛选
            if (CollectionUtils.isNotEmpty(activityIds)) {
                predicateList.add(root.get(ReportingFullDataset_.AID).in(activityIds));
            }

            if (CollectionUtils.isNotEmpty(displayMetrics)) {
                for (String displayMetric : displayMetrics) {
                    if (displayMetric != null) {
                        if (StringUtils.equalsAny(displayMetric, QUERY_EXCLUDE_FIELDS))
                            continue;
                        try {
                            Path<?> metricPath = root.get(displayMetric);
                            selections.add(metricPath);
                        } catch (IllegalArgumentException e) {
                            throw new IllegalArgumentException("Invalid metric: " + displayMetric);
                        }
                    }
                }
            }
            query.multiselect(selections);

            predicateList.add(criteriaBuilder.between(root.get(ReportingFullDataset_.SETTLEMENT_DATE), startTime, endTime));

            Predicate[] predicates = new Predicate[predicateList.size()];
            return criteriaBuilder.and(predicateList.toArray(predicates));
        });
    }

}