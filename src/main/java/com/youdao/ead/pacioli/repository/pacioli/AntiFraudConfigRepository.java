package com.youdao.ead.pacioli.repository.pacioli;

import com.youdao.ead.pacioli.dto.antifraud.AntiFraudConfigQueryDTO;
import com.youdao.ead.pacioli.entity.pacioli.AntiFraudConfig;
import com.youdao.ead.pacioli.entity.pacioli.AntiFraudConfig_;
import com.youdao.ead.pacioli.enums.antifraud.RelateActivityRefreshModeEnum;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> Li
 */
@Repository
public interface AntiFraudConfigRepository extends JpaRepository<AntiFraudConfig, Long>, JpaSpecificationExecutor<AntiFraudConfig> {

    List<AntiFraudConfig> findByName(String name);

    default List<AntiFraudConfig> findBy(AntiFraudConfigQueryDTO dto) {
        return this.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            if (Objects.nonNull(dto.getStatus())) {
                if (dto.getStatus() < 0) {
                    predicateList.add(criteriaBuilder.equal(root.get(AntiFraudConfig_.DELETED), dto.getStatus().equals(-2)));
                } else {
                    predicateList.add(criteriaBuilder.equal(root.get(AntiFraudConfig_.STATUS), dto.getStatus()));
                }
            }
            if (Objects.nonNull(dto.getRuleName())) {
                predicateList.add(criteriaBuilder.like(root.get(AntiFraudConfig_.NAME), "%" + dto.getRuleName() + "%"));
            }
            if (Objects.nonNull(dto.getRuleId())) {
                predicateList.add(criteriaBuilder.equal(root.get(AntiFraudConfig_.ID), dto.getRuleId()));
            }
            return criteriaBuilder.and(predicateList.toArray(new Predicate[0]));
        });
    }

    List<AntiFraudConfig> findByDeleted(Boolean deleted);

    List<AntiFraudConfig> findByDeletedAndRelateActivityRefreshMode(Boolean deleted, RelateActivityRefreshModeEnum relateActivityRefreshMode);
}
