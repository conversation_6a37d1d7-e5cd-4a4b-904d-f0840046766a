package com.youdao.ead.pacioli.repository.adsales;

import com.youdao.ead.pacioli.entity.adsales.AdsalesUser;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024-10-26 12:22
 */
@Repository
public interface AdSalesUserRepository extends JpaRepository<AdsalesUser, Long>, JpaSpecificationExecutor<AdsalesUser> {
    Set<AdsalesUser> findAllByUsername(String username);
    Set<AdsalesUser> findAllByUsernameIn(Set<String> usernames);
}