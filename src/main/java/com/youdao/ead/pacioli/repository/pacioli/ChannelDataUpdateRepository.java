package com.youdao.ead.pacioli.repository.pacioli;

import com.youdao.ead.pacioli.entity.pacioli.ChannelDataUpdate;
import com.youdao.ead.pacioli.entity.pacioli.ChannelDataUpdate_;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public interface ChannelDataUpdateRepository extends JpaRepository<ChannelDataUpdate, Long>, JpaSpecificationExecutor<ChannelDataUpdate> {

    List<ChannelDataUpdate> findChannelDataUpdateByDataKeyIn(List<String> dataKeys);

    List<ChannelDataUpdate> findChannelDataUpdateByDataKey(String dataKey);

    default List<ChannelDataUpdate> findChannelDataUpdateByDataKeyAndUpdateField(String dataKey, String updateField) {
        return this.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // dataKey 条件
            if (dataKey != null && !dataKey.isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get(ChannelDataUpdate_.DATA_KEY), dataKey));
            }

            // updateField 条件
            if (updateField != null) {
                predicates.add(criteriaBuilder.equal(root.get(ChannelDataUpdate_.UPDATE_FIELD), updateField));
            }

            // 最终的查询条件使用 AND 连接
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });
    }
}