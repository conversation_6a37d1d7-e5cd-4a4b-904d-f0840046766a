package com.youdao.ead.pacioli.repository.pacioli;

/**
 * <AUTHOR>
 * @create 2024-10-28 16:22
 */

import com.youdao.ead.pacioli.entity.pacioli.UserRoleRelation;
import com.youdao.ead.pacioli.enums.UserStateEnum;
import io.sentry.protocol.User;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRoleRelationRepository extends JpaRepository<UserRoleRelation, Long>, JpaSpecificationExecutor<UserRoleRelation> {

    List<UserRoleRelation> findAllByRoleKeyInAndDeletedIsFalse(Collection<String> roleKeys);

    List<UserRoleRelation> findAllByRoleKeyInAndStateAndDeletedIsFalse(Collection<String> roleKeys, Byte stateByte);

    List<UserRoleRelation> findAllByUserIdAndDeletedIsFalse(Long userId);

    List<UserRoleRelation> findAllByUserIdInAndDeletedIsFalse(Collection<Long> userIds);

    default List<UserRoleRelation> findAllByUserIdInAndStateAndDeleted(Collection<Long> userIds, UserStateEnum stateEnum, Boolean deleted) {
        return this.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // User IDs in userIds
            if (userIds != null && !userIds.isEmpty()) {
                predicates.add(root.get("userId").in(userIds));
            }

            // State condition if stateEnum is not null
            if (stateEnum != null) {
                predicates.add(criteriaBuilder.equal(root.get("state"), stateEnum.getId()));
            }

            // Deleted condition
            if (deleted != null) {
                predicates.add(criteriaBuilder.equal(root.get("deleted"), deleted));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });
    }

    List<UserRoleRelation> findAllByIdIn(Collection<Long> ids);

    List<UserRoleRelation> findAllByUserIdIn(Collection<Long> userIds);

    Optional<UserRoleRelation> findByUserIdAndRoleKeyAndDeletedIsFalse(Long userId, String roleKey);
}