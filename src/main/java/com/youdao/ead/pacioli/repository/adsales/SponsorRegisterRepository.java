package com.youdao.ead.pacioli.repository.adsales;

import com.youdao.ead.pacioli.entity.adsales.SponsorRegister;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024-11-14 10:32
 */
@Repository
public interface SponsorRegisterRepository extends JpaRepository<SponsorRegister, Long>, JpaSpecificationExecutor<SponsorRegister> {
    Set<SponsorRegister> findAllByUserIdInAndStatus(Collection<Long> userIds, Integer status);

    List<SponsorRegister> findAllBySponsorIdInAndStatus(Collection<Long> sponsorIds, Integer status);
}