package com.youdao.ead.pacioli.repository.pacioli;


import com.youdao.ead.pacioli.entity.pacioli.Task;
import com.youdao.ead.pacioli.entity.pacioli.Task_;
import com.youdao.ead.pacioli.enums.IntegrationMethodEnum;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/10/24
 */
@Repository
public interface TaskRepository extends JpaRepository<Task, Long>, JpaSpecificationExecutor<Task> {

    /**
     * 超管的任务列表
     *
     * @param customerIdList
     * @param taskName
     * @param taskId
     * @param taskStateList
     * @param pageable
     * @return
     */
    default Page<Task> getByAdmin(List<Long> customerIdList,
                                  String taskName,
                                  String taskId,
                                  @NotNull List<Integer> taskStateList,
                                  Pageable pageable) {
        return this.findAll((root, query, ct) -> {
            List<Predicate> pList = new ArrayList<>();
            taskFilter(pList, customerIdList, taskName, taskId, taskStateList, root, ct);
            Predicate[] predicates = new Predicate[pList.size()];
            return query.where(pList.toArray(predicates))
                    .orderBy(ct.desc(root.get(Task_.CREATE_TIME)))
                    .getRestriction();
        }, pageable);
    }

    /**
     * 顾问的任务列表
     *
     * @param advisorId      顾问ID
     * @param customerIdList
     * @param taskName
     * @param taskId
     * @param taskStateList
     * @param pageable
     * @return
     */
    default Page<Task> getByAdvisor(@NotNull Long advisorId,
                                    List<Long> customerIdList,
                                    String taskName,
                                    String taskId,
                                    @NotNull List<Integer> taskStateList,
                                    Pageable pageable) {
        return this.findAll((root, query, ct) -> {
            List<Predicate> pList = new ArrayList<>();
            pList.add(ct.equal(root.get(Task_.ADVISOR_ID), advisorId));
            taskFilter(pList, customerIdList, taskName, taskId, taskStateList, root, ct);
            Predicate[] predicates = new Predicate[pList.size()];
            return query.where(pList.toArray(predicates))
                    .orderBy(ct.desc(root.get(Task_.CREATE_TIME)))
                    .getRestriction();
        }, pageable);
    }

    /**
     * 销售的任务列表
     *
     * @param sellerIds      销售ID
     * @param customerIdList
     * @param taskName
     * @param taskId
     * @param taskStateList
     * @param pageable
     * @return
     */
    default Page<Task> getBySales(List<Long> sellerIds,
                                  List<Long> customerIdList,
                                  String taskName,
                                  String taskId,
                                  @NotNull List<Integer> taskStateList,
                                  Pageable pageable) {
        return this.findAll((root, query, ct) -> {
            List<Predicate> pList = new ArrayList<>();
            CriteriaBuilder.In<Long> in = ct.in(root.get(Task_.SALES_ID));
            sellerIds.forEach(in::value);
            pList.add(in);
            taskFilter(pList, customerIdList, taskName, taskId, taskStateList, root, ct);
            Predicate[] predicates = new Predicate[pList.size()];
            return query.where(pList.toArray(predicates))
                    .orderBy(ct.desc(root.get(Task_.CREATE_TIME)))
                    .getRestriction();
        }, pageable);
    }

    /**
     * 运营的任务列表
     *
     * @param customerIdList 运营 -> 客户ID列表
     * @param taskName
     * @param taskId
     * @param taskStateList
     * @param pageable
     * @return
     */
    default Page<Task> getByOperator(List<Long> customerIdList,
                                     String taskName,
                                     String taskId,
                                     @NotNull List<Integer> taskStateList,
                                     Pageable pageable) {
        return this.findAll((root, query, ct) -> {
            List<Predicate> pList = new ArrayList<>();
            taskFilter(pList, customerIdList, taskName, taskId, taskStateList, root, ct);
            Predicate[] predicates = new Predicate[pList.size()];
            return query.where(pList.toArray(predicates))
                    .orderBy(ct.desc(root.get(Task_.CREATE_TIME)))
                    .getRestriction();
        }, pageable);
    }

    /**
     * BD的任务列表
     *
     * @param taskIdList     BD -> 渠道 -> 子任务 -> 任务ID列表
     * @param customerIdList
     * @param taskName
     * @param taskId
     * @param taskStateList
     * @param pageable
     * @return
     */
    default Page<Task> getByBd(@NotNull List<Long> taskIdList,
                               List<Long> customerIdList,
                               String taskName,
                               String taskId,
                               @NotNull List<Integer> taskStateList,
                               Pageable pageable) {
        return this.findAll((root, query, ct) -> {
            List<Predicate> pList = new ArrayList<>();
            CriteriaBuilder.In<Long> in = ct.in(root.get(Task_.ID));
            taskIdList.forEach(in::value);
            pList.add(in);
            taskFilter(pList, customerIdList, taskName, taskId, taskStateList, root, ct);
            Predicate[] predicates = new Predicate[pList.size()];
            return query.where(pList.toArray(predicates))
                    .orderBy(ct.desc(root.get(Task_.CREATE_TIME)))
                    .getRestriction();
        }, pageable);
    }

    /**
     * 条件查询
     *
     * @param pList         记录所有查询条件
     * @param taskName      任务名称模糊查询
     * @param taskId        任务ID模糊查询
     * @param taskStateList 支持的任务状态列表
     * @param root
     * @param ct
     */
    default void taskFilter(List<Predicate> pList,
                            List<Long> customerIdList,
                            String taskName,
                            String taskId,
                            @NotNull List<Integer> taskStateList,
                            Root<Task> root,
                            CriteriaBuilder ct
    ) {
        // 客户名称 -> 客户ID
        if (CollectionUtils.isNotEmpty(customerIdList)) {
            CriteriaBuilder.In<Long> in = ct.in(root.get(Task_.CUSTOMER_ID));
            customerIdList.forEach(in::value);
            pList.add(in);
        }
        // 任务名称
        if (StringUtils.isNotBlank(taskName)) {
            pList.add(ct.like(root.get(Task_.NAME), "%" + taskName + "%"));
        }
        // 任务ID
        if (StringUtils.isNotBlank(taskId)) {
            pList.add(ct.like(root.get(Task_.ID).as(String.class), "%" + taskId + "%"));
        }
        // 任务状态
        CriteriaBuilder.In<Object> in = ct.in(root.get(Task_.STATE));
        taskStateList.forEach(in::value);
        pList.add(in);
    }

    boolean existsByNameAndIdIsNot(String name, Long id);

    List<Task> findAllByCustomerIdIn(Set<Long> customerIds);

    List<Task> findAllByNameContaining(String name);

    List<Task> findAllBySalesId(Long salesId);

    List<Task> findAllBySalesIdIn(Set<Long> salesIds);

    List<Task> findAllByAdvisorIdIn(Set<Long> advisorIds);

    List<Task> findAllByNameIn(Set<String> names);

    default List<Task> findAllByIntegrationMethodIn(Set<IntegrationMethodEnum> integrationMethodEnums) {
        if (Objects.nonNull(integrationMethodEnums) && CollectionUtils.isEmpty(integrationMethodEnums)) {
            return Collections.emptyList();
        }
        return findAll((root, query, builder) -> builder.isTrue(
                builder.function(
                        "JSON_CONTAINS",
                        Boolean.class,
                        root.get(Task_.INTEGRATION_METHOD),
                        builder.function("JSON_ARRAY", String.class, integrationMethodEnums.stream()
                                .map(item -> builder.literal(item.name()))
                                .toList()
                                .toArray(new Expression[integrationMethodEnums.size()])
                        )
                )
        ));
    }

    default List<Task> findAllForChannelReport(Collection<Byte> osCollection,
                                               Collection<Integer> promotionCategoryIds,
                                               Collection<Long> customerIds) {
        return this.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicateList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(osCollection)) {
                predicateList.add(root.get(Task_.OS).in(osCollection));
            }
            if (CollectionUtils.isNotEmpty(promotionCategoryIds)) {
                predicateList.add(root.get(Task_.PROMOTION_CATEGORY_ID).in(promotionCategoryIds));
            }
//            if (CollectionUtils.isNotEmpty(zhixuanUsernames)) {
//                predicateList.add(root.get(Task_.ZHIXUAN_USERNAME).in(zhixuanUsernames));
//            }
            if (CollectionUtils.isNotEmpty(customerIds)) {
                predicateList.add(root.get(Task_.CUSTOMER_ID).in(customerIds));
            }
            Predicate[] predicates = new Predicate[predicateList.size()];
            return criteriaBuilder.and(predicateList.toArray(predicates));
        });
    }

    List<Task> findBySalesId(Long salesId);

    List<Task> findByAdvisorId(Long advisorId);

    List<Task> findByZhixuanUsernameIn(Collection<Set<String>> zhixuanUsername);

}
