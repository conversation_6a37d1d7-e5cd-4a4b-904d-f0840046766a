package com.youdao.ead.pacioli.repository.pacioli;

import com.youdao.ead.pacioli.entity.pacioli.ConversionAction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/26
 */
@Repository
public interface ConversionActionRepository extends JpaRepository<ConversionAction, Long>, JpaSpecificationExecutor<ConversionAction> {

    List<ConversionAction> findAllByOrderByRankAsc();

    List<ConversionAction> findAllByTypeInOrderByRankAsc(Collection<Integer> types);
}
