package com.youdao.ead.pacioli.repository.pacioli;

import com.youdao.ead.pacioli.entity.pacioli.ChannelActivityRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-02-25 17:08
 */
@Repository
public interface ChannelActivityRelationRepository extends JpaRepository<ChannelActivityRelation, Long>, JpaSpecificationExecutor<ChannelActivityRelation> {

    List<ChannelActivityRelation> findByChannelDidIn(Collection<String> channelDids);

    List<ChannelActivityRelation> findByActivityId(Long activityId);

    List<ChannelActivityRelation> findAllByActivityIdIn(Collection<Long> activityIds);

}
