package com.youdao.ead.pacioli.repository.crm;

import com.youdao.ead.pacioli.entity.crm.SponsorProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024-10-25 16:03
 */
@Repository
public interface SponsorProfileRepository extends JpaRepository<SponsorProfile, Long>, JpaSpecificationExecutor<SponsorProfile> {
    Set<SponsorProfile> findAllByCsLogonId(String csLogonId);

    Optional<SponsorProfile> findAllBySponsorLogonId(String sponsorLogonId);

    List<SponsorProfile> findBySponsorLogonIdIn(Collection<String> sponsorLogonIds);

}