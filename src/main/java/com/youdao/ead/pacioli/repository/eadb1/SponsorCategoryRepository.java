package com.youdao.ead.pacioli.repository.eadb1;

import com.youdao.ead.pacioli.entity.eadb1.SponsorCategory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface SponsorCategoryRepository extends JpaRepository<SponsorCategory, Long>, JpaSpecificationExecutor<SponsorCategory> {
    @Cacheable(cacheNames = "findBySponsorId", key = "#sponsorId")
    Optional<SponsorCategory> findBySponsorId(Long sponsorId);

    @Cacheable(cacheNames = "findAllBySponsorIdIn", key = "#sponsorIds")
    List<SponsorCategory> findAllBySponsorIdIn(Set<Long> sponsorIds);

    @Cacheable(cacheNames = "findAllByCategoryIdIn", key = "#categoryIds")
    List<SponsorCategory> findAllByCategoryIdIn(Set<Integer> categoryIds);
}
