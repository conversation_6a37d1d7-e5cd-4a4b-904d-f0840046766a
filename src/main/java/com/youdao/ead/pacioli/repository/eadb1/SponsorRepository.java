package com.youdao.ead.pacioli.repository.eadb1;

import com.youdao.ead.pacioli.entity.eadb1.Sponsor;
import com.youdao.ead.pacioli.entity.eadb1.Sponsor_;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface SponsorRepository extends JpaRepository<Sponsor, Long>, JpaSpecificationExecutor<Sponsor> {

    List<Sponsor> findAllByUserNameIn(Set<String> userNames);

    Optional<Sponsor> findByUserName(String username);

    List<Sponsor> findAllBySponsorIdIn(Set<Long> sponsorIds);

    List<Sponsor> findAllByUserNameIn(Collection<String> userNames);

    default List<Sponsor> findBySponsorIdContaining(Long sponsorId) {
        return this.findAll((root, query, criteriaBuilder) ->
                criteriaBuilder.like(root.get(Sponsor_.SPONSOR_ID), "%" + sponsorId + "%")
        );
    }
}