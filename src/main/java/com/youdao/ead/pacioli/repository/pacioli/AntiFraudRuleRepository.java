package com.youdao.ead.pacioli.repository.pacioli;

import com.youdao.ead.pacioli.entity.pacioli.AntiFraudRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface AntiFraudRuleRepository extends JpaRepository<AntiFraudRule, Long>, JpaSpecificationExecutor<AntiFraudRule> {
    List<AntiFraudRule> findByStatusAndScopeIn(Integer status, Collection<Integer> scopes);
    List<AntiFraudRule> findByScopeIn(Collection<Integer> scopes);
}
