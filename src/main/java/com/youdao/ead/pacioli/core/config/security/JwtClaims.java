package com.youdao.ead.pacioli.core.config.security;

import com.youdao.ead.pacioli.enums.RoleEnum;
import io.jsonwebtoken.impl.DefaultClaims;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class JwtClaims extends DefaultClaims {
    private static final String USER_ID = "id";
    private static final String USER_ROLE_RELATION_ID = "userRoleRelationId";
    private static final String USERNAME = "username";
    private static final String ROLE_ENUM = "roleEnum";

    public JwtClaims() {
    }

    public JwtClaims(Map<String, Object> map) {
        super(map);
    }

    public JwtClaims setUserId(Long userId) {
        setValue(USER_ID, userId);
        return this;
    }

    public Long getUserId() {
        return get(USER_ID, Long.class);
    }

    public JwtClaims setUserRoleRelationId(Long userRoleRelationId) {
        setValue(USER_ROLE_RELATION_ID, userRoleRelationId);
        return this;
    }

    public Long getUserRoleRelationId(){
        return get(USER_ROLE_RELATION_ID, Long.class);
    }

    public JwtClaims setUsername(String username) {
        setValue(USERNAME, username);
        return this;
    }

    public String getUsername() {
        return getString(USERNAME);
    }

    public JwtClaims setRoleEnum(RoleEnum roleEnum) {
        setValue(ROLE_ENUM, roleEnum);
        return this;
    }

    public String getRoleEnum() {
        return getString(ROLE_ENUM);
    }

}
