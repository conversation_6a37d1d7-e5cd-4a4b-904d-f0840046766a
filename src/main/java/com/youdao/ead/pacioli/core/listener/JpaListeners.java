package com.youdao.ead.pacioli.core.listener;

import com.youdao.ead.pacioli.entity.pacioli.BaseEntity;
import com.youdao.ead.pacioli.util.SecurityUtil;
import jakarta.persistence.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class JpaListeners {

    @PrePersist
    @PreUpdate
    public void prePersist(Object object) {
        if (object instanceof BaseEntity baseEntity) {
            // 这里暂时不对新建做处理
            if (Objects.isNull(baseEntity.getCreatorUserRoleRelationId())) {
                baseEntity.setCreatorUserRoleRelationId(SecurityUtil.getUserRoleRelationId());
                baseEntity.setCreateTime(LocalDateTime.now());
            }
            Long userId = Objects.isNull(SecurityUtil.getUserRoleRelationId()) ? baseEntity.getLastModifierUserRoleRelationId() : SecurityUtil.getUserRoleRelationId();
            baseEntity.setLastModifierUserRoleRelationId(userId);
            baseEntity.setLastModifiedTime(LocalDateTime.now());
        }
    }
}
