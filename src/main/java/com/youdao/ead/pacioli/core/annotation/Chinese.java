package com.youdao.ead.pacioli.core.annotation;

import com.youdao.ead.pacioli.core.annotation.validator.ChineseValidator;
import com.youdao.ead.pacioli.core.annotation.validator.CorpEmailValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ChineseValidator.class)
public @interface Chinese {

    /**
     * 校验不通过的message
     */
    String message() default "必须是中文";

    /**
     * 分组校验
     */
    Class<?>[] groups() default {};


    Class<? extends Payload>[] payload() default {};
}
