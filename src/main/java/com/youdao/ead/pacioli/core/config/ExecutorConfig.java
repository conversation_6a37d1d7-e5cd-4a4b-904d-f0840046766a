package com.youdao.ead.pacioli.core.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
public class ExecutorConfig {

    @Bean(name = "druidQueryExecutor", destroyMethod = "shutdown")
    public ExecutorService druidQueryExecutor() {
        return new ThreadPoolExecutor(
                20,
                512,
                10,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder()
                        .setNameFormat("druid-query-thread-%d")
                        .setDaemon(false)
                        .build()
        );
    }

}
