package com.youdao.ead.pacioli.core.druid;

import com.fasterxml.jackson.annotation.JsonInclude;
import in.zapr.druid.druidry.extractionFunctions.ExtractionFunction;
import lombok.Builder;
import lombok.Getter;

@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegisteredLookUpExtractionFunction extends ExtractionFunction {

    private final String lookup;
    private final Boolean retainMissingValue;
    private final Boolean injective;
    private final String replaceMissingValueWith;
    private final Boolean optimize;

    @Builder
    private RegisteredLookUpExtractionFunction(String lookup, Boolean retainMissingValue, Boolean injective,
                                               String replaceMissingValueWith, Boolean optimize) {
        this.type = ExtractionFunction.REGISTERED_LOOKUP_TYPE;
        this.lookup = lookup;
        this.retainMissingValue = retainMissingValue;
        this.injective = injective;
        this.replaceMissingValueWith = replaceMissingValueWith;
        this.optimize = optimize;
    }
}
