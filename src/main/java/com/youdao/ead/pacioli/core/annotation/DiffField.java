package com.youdao.ead.pacioli.core.annotation;

import com.youdao.ead.pacioli.core.differ.DefaultDiffer;
import com.youdao.ead.pacioli.core.differ.PopoContentFieldDiffer;
import com.youdao.ead.pacioli.enums.FieldNameEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DiffField {
    FieldNameEnum value();

    Class<? extends PopoContentFieldDiffer<?>> differ() default DefaultDiffer.class;


}
