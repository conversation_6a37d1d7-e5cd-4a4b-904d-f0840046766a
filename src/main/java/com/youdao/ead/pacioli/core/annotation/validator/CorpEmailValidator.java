package com.youdao.ead.pacioli.core.annotation.validator;

import com.youdao.ead.pacioli.constants.CommonConstants;
import com.youdao.ead.pacioli.core.annotation.CorpEmail;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;

/**
 * <AUTHOR>
 */
public class CorpEmailValidator implements ConstraintValidator<CorpEmail, String> {
    @Override
    public boolean isValid(String corpEmail, ConstraintValidatorContext context) {
        if (!StringUtils.isEmpty(corpEmail)) {
            return CommonConstants.CORP_EMAIL_PATTERN.matcher(corpEmail).matches();
        }
        return true;
    }
}
