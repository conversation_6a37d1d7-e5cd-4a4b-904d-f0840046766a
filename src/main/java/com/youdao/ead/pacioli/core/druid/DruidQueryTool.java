package com.youdao.ead.pacioli.core.druid;

import com.google.common.base.Functions;
import com.youdao.ead.pacioli.constants.ResponseType;
import com.youdao.ead.pacioli.core.druid.datasource.ThirdPartyStatV2;
import com.youdao.ead.pacioli.core.exception.CustomException;
import com.youdao.ead.pacioli.enums.TimeGranularityEnum;
import com.youdao.ead.pacioli.enums.ZhixuanCtActionEnum;
import com.youdao.ead.pacioli.enums.antifraud.FlowDashboardDimensionEnum;
import com.youdao.ead.pacioli.enums.antifraud.MetricsQueryIdEnum;
import in.zapr.druid.druidry.aggregator.DruidAggregator;
import in.zapr.druid.druidry.aggregator.LongSumAggregator;
import in.zapr.druid.druidry.dataSource.TableDataSource;
import in.zapr.druid.druidry.dimension.DefaultDimension;
import in.zapr.druid.druidry.dimension.DruidDimension;
import in.zapr.druid.druidry.dimension.ExtractionDimension;
import in.zapr.druid.druidry.dimension.enums.OutputType;
import in.zapr.druid.druidry.filter.*;
import in.zapr.druid.druidry.filter.havingSpec.GreaterThanHaving;
import in.zapr.druid.druidry.granularity.Granularity;
import in.zapr.druid.druidry.granularity.PeriodGranularity;
import in.zapr.druid.druidry.granularity.PredefinedGranularity;
import in.zapr.druid.druidry.granularity.SimpleGranularity;
import in.zapr.druid.druidry.postAggregator.ArithmeticFunction;
import in.zapr.druid.druidry.postAggregator.ArithmeticPostAggregator;
import in.zapr.druid.druidry.postAggregator.DruidPostAggregator;
import in.zapr.druid.druidry.postAggregator.FieldAccessPostAggregator;
import in.zapr.druid.druidry.query.DruidQuery;
import in.zapr.druid.druidry.query.aggregation.DruidGroupByQuery;
import in.zapr.druid.druidry.query.config.Interval;
import in.zapr.druid.druidry.query.config.Context;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static com.youdao.ead.pacioli.constants.AntiFraudConstants.*;
import static com.youdao.ead.pacioli.enums.ZhixuanCtActionEnum.*;
import static com.youdao.ead.pacioli.enums.antifraud.MetricsQueryIdEnum.CONV_TIME_FOLLOW;

/**
 * <AUTHOR> Li
 */
public class DruidQueryTool {
    private static final long DRUID_QUERY_CONTEXT_TIMEOUT_MS = 30000;
    private static final TableDataSource SDK_STAT_V2_DATA_SOURCE = new TableDataSource("sdk_stat_v2");
    private static final TableDataSource THIRD_PARTY_STAT_DATA_SOURCE = new TableDataSource("third_party_stat");
    private static final TableDataSource THIRD_PARTY_STAT_DATA_SOURCE_V2 = new TableDataSource("third_party_stat_v2");
    private static final String SLOT_ID = "slot_id";
    private static final String SPONSOR_ID = "sponsor_id";
    private static final String CLICK = "click";
    private static final String CHARGE = "charge";
    private static final String SPONSOR_EMAIL = "sponsorId_email";
    public static final String ASIA_SHANGHAI = "Asia/Shanghai";
    public static final DateTimeZone DATE_TIME_ZONE_SHANGHAI = DateTimeZone.forID(ASIA_SHANGHAI);
    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS+08:00";

    public static DruidQuery clickAndChargeQueryBuilder(Set<Long> sponsorIds,
                                                        Set<String> slotUdids,
                                                        TimeGranularityEnum timeGranularityEnum,
                                                        List<LocalDateTime> localDateTimes) {
        return DruidGroupByQuery.builder()
                .context(getDruidQueryContext())
                .dataSource(SDK_STAT_V2_DATA_SOURCE)
                .granularity(getSimpleGranularity(timeGranularityEnum))
                .intervals(getInterval(localDateTimes))
                .aggregators(getAggregators())
                .filter(getSponsorAndSlotFilter(sponsorIds, slotUdids))
                .dimensions(getDimensions())
                .build();
    }

    private static List<DruidDimension> getDimensions() {
        DefaultDimension sponsorIdDimension = DefaultDimension.builder().dimension(SPONSOR_ID)
                .outputName(SPONSOR_ID)
                .build();
        DefaultDimension slotIdDimension = DefaultDimension.builder()
                .dimension(SLOT_ID)
                .outputName(SLOT_ID)
                .build();
        ExtractionDimension sponsorEmailDimension = ExtractionDimension.builder()
                .dimension(SPONSOR_ID)
                .outputName(SPONSOR_EMAIL)
                .outputType(OutputType.STRING)
                .extractionFunction(RegisteredLookUpExtractionFunction
                        .builder()
                        .lookup(SPONSOR_EMAIL)
                        .retainMissingValue(true)
                        .injective(true)
                        .build()
                ).build();
        return Arrays.asList(sponsorIdDimension, slotIdDimension, sponsorEmailDimension);
    }


    private static Granularity getSimpleGranularity(TimeGranularityEnum timeGranularityEnum) {
        PeriodGranularity.PeriodGranularityBuilder periodGranularityBuilder = PeriodGranularity.builder().timeZone(DATE_TIME_ZONE_SHANGHAI);
        return switch (timeGranularityEnum) {
            case ALL -> new SimpleGranularity(PredefinedGranularity.ALL);
            case DAY -> periodGranularityBuilder.period("P1D").build();
            case WEEK -> periodGranularityBuilder.period("P1W").build();
            case MONTH -> periodGranularityBuilder.period("P1M").build();
            case HOUR -> periodGranularityBuilder.period("PT1H").build();
        };
    }

    private static DruidFilter getSponsorAndSlotFilter(Set<Long> sponsorIds, Set<String> slotUdids) {
        //DruidFilter 不能为空
        ArrayList<DruidFilter> druidFilters = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sponsorIds)) {
            DruidFilter sponsorFilter = new InFilter(SPONSOR_ID, sponsorIds.stream().map(Functions.toStringFunction()).collect(Collectors.toList()));
            druidFilters.add(sponsorFilter);
        }
        if (CollectionUtils.isNotEmpty(slotUdids)) {
            DruidFilter slotFilter = new InFilter(SLOT_ID, slotUdids.stream().map(Functions.toStringFunction()).collect(Collectors.toList()));
            druidFilters.add(slotFilter);
        }
        return new AndFilter(druidFilters);
    }

    private static List<Interval> getInterval(List<LocalDateTime> localDateTimes) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(TIMESTAMP_FORMAT);

        List<Interval> intervals = new ArrayList<>(localDateTimes.size());
        localDateTimes.forEach(item -> {
            DateTime date = DateTime.parse(dateTimeFormatter.print(new DateTime(item.toInstant(ZoneOffset.ofHours(8)).toEpochMilli())));
            intervals.add(
                    new Interval(date, date.plusDays(1))
            );
        });
        return intervals;
    }

    private static List<DruidAggregator> getAggregators() {
        return Arrays.asList(
                LongSumAggregator.builder()
                        .name(CLICK)
                        .fieldName(CLICK)
                        .build(),
                LongSumAggregator.builder()
                        .name(CHARGE)
                        .fieldName(CHARGE)
                        .build()
        );
    }


    public static DruidQuery conversionQueryBuilder(Long activityId,
                                                    TimeGranularityEnum timeGranularityEnum,
                                                    List<LocalDateTime> localDateTimes) {
        DruidFilter filter = new SelectorFilter("activity_id", activityId);
        List<DruidDimension> dimensions = Arrays.asList(
                new DefaultDimension("activity_id", "activity_id", OutputType.LONG),
                new DefaultDimension("ct_action", "ct_action", OutputType.STRING)
        );
        List<DruidAggregator> aggregators = List.of(LongSumAggregator.builder().name("conv").fieldName("conv").build());
        return DruidGroupByQuery.builder()
                .context(getDruidQueryContext())
                .dataSource(THIRD_PARTY_STAT_DATA_SOURCE)
                .granularity(getSimpleGranularity(timeGranularityEnum))
                .intervals(getInterval(localDateTimes))
                .aggregators(aggregators)
                .filter(filter)
                .dimensions(dimensions)
                .build();
    }


    private static DruidAggregator getAggregator(String name) {
        return LongSumAggregator.builder()
                .name(name)
                .fieldName(name)
                .build();
    }

    private static DruidPostAggregator getDivideArithmeticPostAggregator(String name, String molecule, String denominator) {
        return ArithmeticPostAggregator.builder()
                .name(name)
                .function(ArithmeticFunction.DIVIDE)
                .fields(List.of(new FieldAccessPostAggregator(molecule, molecule), new FieldAccessPostAggregator(denominator, denominator)))
                .build();
    }
    public static DruidQuery queryCallableBuilder(MetricsQueryIdEnum metricsQueryIdEnum, TimeGranularityEnum timeGranularityEnum, Set<String> aggStrings,
                                                  String os, Boolean filterSponsor, Set<Long> sponsorIds, Set<Long> activityIds,Set<String> channelDids,
                                                  Set<FlowDashboardDimensionEnum> dimensionSet, ZhixuanCtActionEnum ctActionSelector,
                                                  LocalDateTime startTime, LocalDateTime endTime) {
        if (CollectionUtils.isEmpty(aggStrings)) {
            throw new CustomException(ResponseType.SERVICE_ERROR, "QUERY CALLABLE ERROR.");
        }
        List<DruidAggregator> aggregators = aggStrings.stream().map(DruidQueryTool::getAggregator).collect(Collectors.toList());
        Granularity simpleGranularity = getSimpleGranularity(Objects.isNull(timeGranularityEnum) ? TimeGranularityEnum.ALL : timeGranularityEnum);
        List<Interval> interval = getInterval(startTime, endTime);
        List<DruidFilter> filters = new ArrayList<>();
        List<DruidDimension> dimensions = new ArrayList<>();
        initPublicDimensionsAndFilter(dimensionSet, dimensions, os, filterSponsor, sponsorIds, activityIds, channelDids, filters);
        switch (metricsQueryIdEnum) {
            case DISCARD_CLICK -> dimensions.add(new DefaultDimension("discard_reason", "discard_reason", OutputType.STRING));
            case EXCEPTION_FILTER_CLICK -> dimensions.add(new DefaultDimension("fraud_tags", "fraud_tags", OutputType.STRING));
            case CT_ACTION, RETENTION_CT_ACTION -> {
                dimensions.add(new DefaultDimension("ct_action", "ct_action", OutputType.STRING));
            }
            case RETENTION3_CT_ACTION, RETENTION7_CT_ACTION, RETENTION14_CT_ACTION, RETENTION30_CT_ACTION -> {
                dimensions.add(new DefaultDimension("ct_action", "ct_action", OutputType.STRING));
                dimensions.add(new DefaultDimension("retention_days", "retention_days", OutputType.STRING));
            }
            case ANTI_RULE ->
                    // 最早只有点击的数据，兼容历史统计数据，没有该维度的默认按点击算。所以不是rta请求且不是转化的就都是点击的
                    filters.add(new NotFilter(
                            new OrFilter(
                                    Arrays.asList(
                                            new SelectorFilter("fraud_stat_type", FRAUD_STAT_TYPE_RTA),
                                            new SelectorFilter("fraud_stat_type", FRAUD_STAT_TYPE_CONV)
                                    )
                            )
                    ));
            case ANTI_RULE_CONV, CONV_TIME_FOLLOW -> {
                filters.add(new SelectorFilter("fraud_stat_type", FRAUD_STAT_TYPE_CONV));
                if (ctActionSelector != null) {
                    if (_ALL != ctActionSelector) {
                        filters.add(new InFilter("ct_action", ctActionSelector.getVals()));
                        if (ctActionSelector.getRetentionDays() != null) {
                            filters.add(new SelectorFilter("retention_days", ctActionSelector.getRetentionDays()));
                        }
                    }
                }
                if (metricsQueryIdEnum == CONV_TIME_FOLLOW) {
                    dimensions.add(new DefaultDimension("conv_click_time_difference", "conv_click_time_difference", OutputType.LONG));
                }
            }
            case ANTI_RULE_RTA -> filters.add(new SelectorFilter("fraud_stat_type", FRAUD_STAT_TYPE_RTA));
            case OS -> {
                if (!dimensionSet.contains(FlowDashboardDimensionEnum.OS_TYPE)) {
                    dimensions.add(new DefaultDimension("os", "os", OutputType.STRING));
                }
            }
        }
        DruidGroupByQuery.DruidGroupByQueryBuilder queryBuilder = DruidGroupByQuery.builder()
                .context(getDruidQueryContext())
                .dataSource(THIRD_PARTY_STAT_DATA_SOURCE)
                .granularity(simpleGranularity)
                .intervals(interval)
                .aggregators(aggregators)
                .dimensions(dimensions);
        if (!filters.isEmpty()) {
            queryBuilder.filter(new AndFilter(filters));
        }
        return queryBuilder.build();
    }

    private static void initPublicDimensionsAndFilter(Set<FlowDashboardDimensionEnum> dimensionSet,
                                                           List<DruidDimension> dimensions,
                                                           String os, Boolean filterSponsor, Set<Long> sponsorIds, Set<Long> activityIds,Set<String> channelDids,
                                                           List<DruidFilter> filters) {
        dimensionSet.forEach(x -> dimensions.add(x.getDimension()));
        if (dimensionSet.contains(FlowDashboardDimensionEnum.OS_TYPE)) {
            // 这个unkonwn不是typo
            filters.add(new InFilter("os", List.of("IOS", "ANDROID", "UNKONWN")));
        }
        if (CollectionUtils.isNotEmpty(activityIds)) {
            filters.add(new InFilter("activity_id", activityIds.stream().map(String::valueOf).toList()));
        }
        if (CollectionUtils.isNotEmpty(channelDids)){
            filters.add(new InFilter("channel_did", channelDids.stream().map(String::valueOf).toList()));
        }
        if (StringUtils.isNotBlank(os)) {
            filters.add(new InFilter("os", Collections.singletonList(os)));
        }
        if (filterSponsor) {
            filters.add(new InFilter("sponsor_id", sponsorIds.stream().map(String::valueOf).toList()));
        }
    }

    private static List<Interval> getInterval(LocalDateTime startTime, LocalDateTime endTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(TIMESTAMP_FORMAT);
        DateTime start = DateTime.parse(dateTimeFormatter.print(new DateTime(startTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli())));
        DateTime end = DateTime.parse(dateTimeFormatter.print(new DateTime(endTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli())));
        return Collections.singletonList(new Interval(start, end.plusDays(1)));
    }

    private static Context getDruidQueryContext() {
        return Context.builder()
                .timeoutInMilliSeconds(DRUID_QUERY_CONTEXT_TIMEOUT_MS)
                .build();
    }

    public static DruidQuery channelDidAndAidQueryBuilder(TimeGranularityEnum timeGranularityEnum,
                                                          LocalDateTime startTime, LocalDateTime endTime, DruidFilter filter) {
        List<DruidAggregator> aggregators = List.of(LongSumAggregator.builder().name(CLICK).fieldName(CLICK).build());
        List<DruidDimension> dimensions = List.of(
                new DefaultDimension(ThirdPartyStatV2.ACTIVITY_ID, ThirdPartyStatV2.ACTIVITY_ID, OutputType.LONG),
                new DefaultDimension(ThirdPartyStatV2.CHANNEL_DID, ThirdPartyStatV2.CHANNEL_DID, OutputType.STRING)
        );
        List<Interval> interval = getInterval(startTime, endTime);
        DruidGroupByQuery.DruidGroupByQueryBuilder builder = DruidGroupByQuery.builder()
                .context(getDruidQueryContext())
                .dataSource(THIRD_PARTY_STAT_DATA_SOURCE_V2)
                .granularity(getSimpleGranularity(timeGranularityEnum))
                .intervals(interval)
                .aggregators(aggregators)
                .having(new GreaterThanHaving(CLICK, 0))
                .dimensions(dimensions);
        if (filter != null) {
            builder.filter(filter);
        }
        return builder.build();
    }

}
