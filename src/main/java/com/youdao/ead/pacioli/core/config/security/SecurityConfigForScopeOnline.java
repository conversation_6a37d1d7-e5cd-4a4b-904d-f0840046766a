package com.youdao.ead.pacioli.core.config.security;

import com.youdao.ead.pacioli.repository.pacioli.UserRepository;
import com.youdao.ead.pacioli.repository.pacioli.UserRoleRelationRepository;
import com.youdao.ead.pacioli.service.UserRoleRelationService;
import com.youdao.ead.pacioli.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * <AUTHOR> Li
 */
@Configuration
@EnableWebSecurity
@Profile("online")
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfigForScopeOnline {
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final UserRoleRelationRepository userRoleRelationRepository;
    private final UserRepository userRepository;

    @Bean
    SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable);
        http.exceptionHandling(httpSecurityExceptionHandlingConfigurer -> httpSecurityExceptionHandlingConfigurer.authenticationEntryPoint(jwtAuthenticationEntryPoint));
        http.sessionManagement(httpSecuritySessionManagementConfigurer -> httpSecuritySessionManagementConfigurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        http.authorizeHttpRequests((matcherRegistry) -> {
            matcherRegistry.requestMatchers(HttpMethod.OPTIONS).permitAll();
            matcherRegistry.requestMatchers(
                    "/**/login/**",
                    "/**/health/**").permitAll();
            matcherRegistry.requestMatchers("/**/dev/**").denyAll();
            matcherRegistry.anyRequest().authenticated();
        });
        http.addFilterBefore(new JwtAuthenticationTokenFilter(userRoleRelationRepository, userRepository), UsernamePasswordAuthenticationFilter.class);
        http.headers(httpSecurityHeadersConfigurer -> httpSecurityHeadersConfigurer.cacheControl(Customizer.withDefaults()));
        return http.build();
    }

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return (WebSecurityCustomizer) -> WebSecurityCustomizer.ignoring().requestMatchers(HttpMethod.OPTIONS)
                .requestMatchers(HttpMethod.GET,
                        "/favicon.ico",
                        "/**/*.html",
                        "/**/*.css",
                        "/**/*.js",
                        "/**/*.png",
                        "/**/*.gif",
                        "/**/*.ttf",
                        "/**/swagger-resources/**",
                        "/**/swagger-ui/**",
                        "/**/v3/api-docs*/**",
                        "/**/webjars/**");
    }

}
