package com.youdao.ead.pacioli.service.impl;

import com.youdao.ead.pacioli.entity.pacioli.ChannelDataUpdate;
import com.youdao.ead.pacioli.repository.pacioli.ChannelDataUpdateRepository;
import com.youdao.ead.pacioli.service.ChannelDataUpdateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ChannelDataUpdateServiceImpl implements ChannelDataUpdateService {
    private final ChannelDataUpdateRepository channelDataUpdateRepository;

    @Override
    public List<ChannelDataUpdate> getChannelDataUpdateList(List<String> dataKeyList) {
        return channelDataUpdateRepository.findChannelDataUpdateByDataKeyIn(dataKeyList);
    }
}
