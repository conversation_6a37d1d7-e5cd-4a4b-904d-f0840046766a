package com.youdao.ead.pacioli.service;

import com.youdao.ead.pacioli.dto.task.CategoryDTO;
import com.youdao.ead.pacioli.dto.task.CategoryWithSubDTO;
import com.youdao.ead.pacioli.dto.user.SponsorDTO;
import com.youdao.ead.pacioli.entity.eadb1.Sponsor;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> Li
 */
public interface SponsorService {
    CategoryWithSubDTO getBySponsorName(String sponsorName);

    Map<String, CategoryDTO> getSponsorName2CategoryMap(Set<String> sponsorNames);

    Integer getSubCategoryId(String sponsorName);

    List<Sponsor> getByCategoryIds(Set<Integer> categoryIds);

    Set<CategoryWithSubDTO> getBySponsorNameBatch(Set<String> sponsorNames);
}
