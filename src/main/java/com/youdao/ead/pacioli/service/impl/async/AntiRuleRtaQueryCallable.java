package com.youdao.ead.pacioli.service.impl.async;

import com.youdao.ead.pacioli.core.druid.DruidHttpClient;
import com.youdao.ead.pacioli.core.druid.FlowDashboardQueryResult;
import com.youdao.ead.pacioli.dto.antifraud.FlowDashboardQueryDTO;
import com.youdao.ead.pacioli.dto.antifraud.FlowDashboardResultDTO;
import com.youdao.ead.pacioli.enums.antifraud.MetricsQueryIdEnum;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * rta请求的反作弊指标查询任务
 * <AUTHOR>
 * @create 2024-12-03 17:13
 */
public class AntiRuleRtaQueryCallable extends AbstractQueryCallable {


    public AntiRuleRtaQueryCallable(ConcurrentHashMap<String, FlowDashboardResultDTO> publicSummaryMap, FlowDashboardQueryDTO flowDashboardQueryDTO, boolean filterSponsor, Set<Long> relateSponsorIds, Set<String> aggregators, DruidHttpClient druidHttpClient) {
        super(publicSummaryMap, flowDashboardQueryDTO, filterSponsor, relateSponsorIds, aggregators, druidHttpClient);
    }

    @Override
    protected MetricsQueryIdEnum metricsQueryIdEnum() {
        return MetricsQueryIdEnum.ANTI_RULE_RTA;
    }

    @Override
    protected void doOneRowSummary(FlowDashboardQueryResult.Event event, FlowDashboardResultDTO dto) throws Exception {
        updateFraudStatTypeRta(event, dto);
    }


    private void updateFraudStatTypeRta(FlowDashboardQueryResult.Event event, FlowDashboardResultDTO dto) {
        dto.setExceptionRtaMark(_long(dto.getExceptionRtaMark()) + event.getExceptionMark());
        dto.setExceptionRtaDeal(_long(dto.getExceptionRtaDeal()) + event.getExceptionDeal());
        dto.setFraudRtaFiltered(_long(dto.getFraudRtaFiltered()) + event.getFraudRtaFiltered());
        dto.setExceptionRtaSend(_long(dto.getExceptionRtaSend()) + event.getExceptionSend());

        dto.setUaFilledRtaMark(_long(dto.getUaFilledRtaMark()) + event.getUaFilledMark());
        dto.setIpFilledRtaMark(_long(dto.getIpFilledRtaMark()) + event.getIpFilledMark());
        dto.setAndroidOsFilledRtaMark(_long(dto.getAndroidOsFilledRtaMark()) + event.getAndroidOsFilledMark());
        dto.setIosOsFilledRtaMark(_long(dto.getIosOsFilledRtaMark()) + event.getIosOsFilledMark());
        dto.setDeviceModelFilledRtaMark(_long(dto.getDeviceModelFilledRtaMark()) + event.getDeviceModelFilledMark());
        dto.setOsVersionFilledRtaMark(_long(dto.getOsVersionFilledRtaMark()) + event.getOsVersionFilledMark());
        dto.setImeiV2FilledRtaMark(_long(dto.getImeiV2FilledRtaMark()) + event.getImeiV2FilledMark());
        dto.setImeiFilledRtaMark(_long(dto.getImeiFilledRtaMark()) + event.getImeiFilledMark());
        dto.setOaidFilledRtaMark(_long(dto.getOaidFilledRtaMark()) + event.getOaidFilledMark());
        dto.setOaidMd5FilledRtaMark(_long(dto.getOaidMd5FilledRtaMark()) + event.getOaidMd5FilledMark());
        dto.setAndroidIdMd5FilledRtaMark(_long(dto.getAndroidIdMd5FilledRtaMark()) + event.getAndroidIdMd5FilledMark());
        dto.setIdfaFilledRtaMark(_long(dto.getIdfaFilledRtaMark()) + event.getIdfaFilledMark());
        dto.setIdfaMd5FilledRtaMark(_long(dto.getIdfaMd5FilledRtaMark()) + event.getIdfaMd5FilledMark());
        dto.setCaidFilledRtaMark(_long(dto.getCaidFilledRtaMark()) + event.getCaidFilledMark());
        dto.setCaidMd5FilledRtaMark(_long(dto.getCaidMd5FilledRtaMark()) + event.getCaidMd5FilledMark());


        dto.setUaUnfilledRtaDeal(_long(dto.getUaUnfilledRtaDeal()) + event.getUaUnfilledDeal());
        dto.setOsUnfilledRtaDeal(_long(dto.getOsUnfilledRtaDeal()) + event.getOsUnfilledDeal());
        dto.setDeviceModelUnfilledRtaDeal(_long(dto.getDeviceModelUnfilledRtaDeal()) + event.getDeviceModelUnfilledDeal());
        dto.setOsVersionUnfilledRtaDeal(_long(dto.getOsVersionUnfilledRtaDeal()) + event.getOsVersionUnfilledDeal());

        dto.setParamNullRtaFilter(_long(dto.getParamNullRtaFilter()) + event.getBlankParamFiltered());
        dto.setBrandRtaFilter(_long(dto.getBrandRtaFilter()) + event.getModelFiltered());
        dto.setSourceRtaFilter(_long(dto.getSourceRtaFilter()) + event.getSourceFiltered());

        dto.setUaFilledRta(_long(dto.getUaFilledRta()) + event.getUaFilled());
        dto.setIpFilledRta(_long(dto.getIpFilledRta()) + event.getIpFilled());
        dto.setAndroidOsFilledRta(_long(dto.getAndroidOsFilledRta()) + event.getAndroidOsFilled());

        dto.setIosOsFilledRta(_long(dto.getIosOsFilledRta()) + event.getIosOsFilled());
        dto.setDeviceModelFilledRta(_long(dto.getDeviceModelFilledRta()) + event.getDeviceModelFilled());
        dto.setOsVersionFilledRta(_long(dto.getOsVersionFilledRta()) + event.getOsVersionFilled());
        dto.setImeiV2FilledRta(_long(dto.getImeiV2FilledRta()) + event.getImeiV2Filled());
        dto.setImeiFilledRta(_long(dto.getImeiFilledRta()) + event.getImeiFilled());
        dto.setOaidFilledRta(_long(dto.getOaidFilledRta()) + event.getOaidFilled());
        dto.setOaidMd5FilledRta(_long(dto.getOaidMd5FilledRta()) + event.getOaidMd5Filled());
        dto.setIdfaFilledRta(_long(dto.getIdfaFilledRta()) + event.getIdfaFilled());
        dto.setIdfaMd5FilledRta(_long(dto.getIdfaMd5FilledRta()) + event.getIdfaMd5Filled());
        dto.setCaidFilledRta(_long(dto.getCaidFilledRta()) + event.getCaidFilled());
        dto.setCaidMd5FilledRta(_long(dto.getCaidMd5FilledRta()) + event.getCaidMd5Filled());

        dto.setFormatErrorUaRtaMark(_long(dto.getFormatErrorUaRtaMark()) + event.getFormatErrorUaMark());
        dto.setFormatErrorDeviceIdRtaMark(_long(dto.getFormatErrorDeviceIdRtaMark()) + event.getFormatErrorDeviceIdMark());
        dto.setValueErrorIpRtaMark(_long(dto.getValueErrorIpRtaMark()) + event.getValueErrorIpMark());
        dto.setValueErrorDarkIpRtaMark(_long(dto.getValueErrorDarkIpRtaMark()) + event.getValueErrorDarkIpMark());
        dto.setValueErrorDarkDeviceIdRtaMark(_long(dto.getValueErrorDarkDeviceIdRtaMark()) + event.getValueErrorDarkDeviceIdMark());
        dto.setValueErrorIntranetIpRtaMark(_long(dto.getValueErrorIntranetIpRtaMark()) + event.getValueErrorIntranetIpMark());
        dto.setValueErrorUaRtaMark(_long(dto.getValueErrorUaRtaMark()) + event.getValueErrorUaMark());
        dto.setStandardDiffUaRtaMark(_long(dto.getStandardDiffUaRtaMark()) + event.getStandardDiffUaMark());
        dto.setStandardDiffOsvRtaMark(_long(dto.getStandardDiffOsvRtaMark()) + event.getStandardDiffOsvMark());
        dto.setStandardDiffModelRtaMark(_long(dto.getStandardDiffModelRtaMark()) + event.getStandardDiffModelMark());

        dto.setUnexpectedOsRtaMark(_long(dto.getUnexpectedOsRtaMark()) + event.getUnexpectedOsMark());
        dto.setUnexpectedDeviceRtaMark(_long(dto.getUnexpectedDeviceRtaMark()) + event.getUnexpectedDeviceMark());
        dto.setUnexpectedUaRtaMark(_long(dto.getUnexpectedUaRtaMark()) + event.getUnexpectedUaMark());
        dto.setUnexpectedModelRtaMark(_long(dto.getUnexpectedModelRtaMark()) + event.getUnexpectedModelMark());

        dto.setRelatedMultiUaRtaMark(_long(dto.getRelatedMultiUaRtaMark()) + event.getRelatedMultiUaMark());
        dto.setRelatedMultiModelRtaMark(_long(dto.getRelatedMultiModelRtaMark()) + event.getRelatedMultiModelMark());
        dto.setRelatedMultiOsvRtaMark(_long(dto.getRelatedMultiOsvRtaMark()) + event.getRelatedMultiOsvMark());
        dto.setActionRateDeviceIpRtaMark(_long(dto.getActionRateDeviceIpRtaMark()) + event.getActionRateDeviceIpMark());

        dto.setFormatErrorUaRtaDeal(_long(dto.getFormatErrorUaRtaDeal()) + event.getFormatErrorUaDeal());
        dto.setValueErrorIpRtaDeal(_long(dto.getValueErrorIpRtaDeal()) + event.getValueErrorIpDeal());
        dto.setValueErrorDarkIpRtaDeal(_long(dto.getValueErrorDarkIpRtaDeal()) + event.getValueErrorDarkIpDeal());
        dto.setValueErrorIntranetIpRtaDeal(_long(dto.getValueErrorIntranetIpRtaDeal()) + event.getValueErrorIntranetIpDeal());
        dto.setValueErrorUaRtaDeal(_long(dto.getValueErrorUaRtaDeal()) + event.getValueErrorUaDeal());
        dto.setStandardDiffUaRtaDeal(_long(dto.getStandardDiffUaRtaDeal()) + event.getStandardDiffUaDeal());
        dto.setStandardDiffOsvRtaDeal(_long(dto.getStandardDiffOsvRtaDeal()) + event.getStandardDiffOsvDeal());
        dto.setStandardDiffModelRtaDeal(_long(dto.getStandardDiffModelRtaDeal()) + event.getStandardDiffModelDeal());

        dto.setUnexpectedOsRtaDeal(_long(dto.getUnexpectedOsRtaDeal()) + event.getUnexpectedOsDeal());
        dto.setUnexpectedUaRtaDeal(_long(dto.getUnexpectedUaRtaDeal()) + event.getUnexpectedUaDeal());
        dto.setUnexpectedModelRtaDeal(_long(dto.getUnexpectedModelRtaDeal()) + event.getUnexpectedModelDeal());

        dto.setRelatedMultiUaRtaDeal(_long(dto.getRelatedMultiUaRtaDeal()) + event.getRelatedMultiUaDeal());
        dto.setRelatedMultiModelRtaDeal(_long(dto.getRelatedMultiModelRtaDeal()) + event.getRelatedMultiModelDeal());
        dto.setRelatedMultiOsvRtaDeal(_long(dto.getRelatedMultiOsvRtaDeal()) + event.getRelatedMultiOsvDeal());
        dto.setActionRateDeviceIpRtaDeal(_long(dto.getActionRateDeviceIpRtaDeal()) + event.getActionRateDeviceIpDeal());

        dto.setFormatErrorUaRtaSend(_long(dto.getFormatErrorUaRtaSend()) + event.getFormatErrorUaSend());
        dto.setFormatErrorDeviceIdRtaSend(_long(dto.getFormatErrorDeviceIdRtaSend()) + event.getFormatErrorDeviceIdSend());
        dto.setValueErrorIpRtaSend(_long(dto.getValueErrorIpRtaSend()) + event.getValueErrorIpSend());
        dto.setValueErrorDarkIpRtaSend(_long(dto.getValueErrorDarkIpRtaSend()) + event.getValueErrorDarkIpSend());
        dto.setValueErrorDarkDeviceIdRtaSend(_long(dto.getValueErrorDarkDeviceIdRtaSend()) + event.getValueErrorDarkDeviceIdSend());
        dto.setValueErrorIntranetIpRtaSend(_long(dto.getValueErrorIntranetIpRtaSend()) + event.getValueErrorIntranetIpSend());
        dto.setValueErrorUaRtaSend(_long(dto.getValueErrorUaRtaSend()) + event.getValueErrorUaSend());
        dto.setStandardDiffUaRtaSend(_long(dto.getStandardDiffUaRtaSend()) + event.getStandardDiffUaSend());
        dto.setStandardDiffOsvRtaSend(_long(dto.getStandardDiffOsvRtaSend()) + event.getStandardDiffOsvSend());
        dto.setStandardDiffModelRtaSend(_long(dto.getStandardDiffModelRtaSend()) + event.getStandardDiffModelSend());
        // 这里event.getUnexpectedOsClick() 名字有歧义, 实际数据是按fraud_stat_type区分了请求还是点击
        dto.setUnexpectedOsRta(_long(dto.getUnexpectedOsRta()) + event.getUnexpectedOsClick());
        dto.setUnexpectedDeviceRta(_long(dto.getUnexpectedDeviceRta()) + event.getUnexpectedDeviceClick());
        dto.setUnexpectedUaRta(_long(dto.getUnexpectedUaRta()) + event.getUnexpectedUaClick());
        dto.setUnexpectedModelRtaSend(_long(dto.getUnexpectedModelRtaSend()) + event.getUnexpectedModelSend());

        dto.setRelatedMultiUaRtaSend(_long(dto.getRelatedMultiUaRtaSend()) + event.getRelatedMultiUaSend());
        dto.setRelatedMultiModelRtaSend(_long(dto.getRelatedMultiModelRtaSend()) + event.getRelatedMultiModelSend());
        dto.setRelatedMultiOsvRtaSend(_long(dto.getRelatedMultiOsvRtaSend()) + event.getRelatedMultiOsvSend());
        dto.setActionRateDeviceIpRtaSend(_long(dto.getActionRateDeviceIpRtaSend()) + event.getActionRateDeviceIpSend());


    }
}
