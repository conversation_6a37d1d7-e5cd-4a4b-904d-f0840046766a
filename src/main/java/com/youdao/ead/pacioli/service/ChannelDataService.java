package com.youdao.ead.pacioli.service;


import com.youdao.ead.pacioli.bo.LoginUser;
import com.youdao.ead.pacioli.dto.channeldata.*;
import com.youdao.ead.pacioli.entity.pacioli.ChannelData;
import com.youdao.ead.pacioli.enums.RoleEnum;
import org.springframework.data.domain.Page;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ChannelDataService {

    Page<ChannelDataResultDTO> getChannelDataList(ChannelDataQueryDTO dto, LoginUser loginUser);

    Collection<String> getDownloadDashboardIgnoreField(ChannelDataQueryDTO dto, LoginUser loginUser);

    void updateChannelData(List<ChannelDataUpdateDTO> channelDataUpdateDTO, Long userRoleRelationId, RoleEnum currentRole);

    List<ChannelData> getChannelDataList(List<String> dataKeyList);

    Map<String, ChannelDataResultDTO> getChannelDataMapByDataKey(List<String> dataKeyList);

    List<LocalDate> getSponsorLockTimeList(ChannelDataSponsorLockDateQuery dto, LoginUser loginUser);

    void lockDateBySponsorAndTime(ChannelDataLockDTO dto, LoginUser loginUser);

    Set<LocalDate> getSponsorUnlockTimeList(ChannelDataSponsorLockDateQuery dto, LoginUser loginUser);

    void queryClickAndConv(ChannelDataQueryDTO queryDTO, Set<Long> relateSponsorIds, boolean filterSponsor, List<ChannelDataResultDTO> result);
}
