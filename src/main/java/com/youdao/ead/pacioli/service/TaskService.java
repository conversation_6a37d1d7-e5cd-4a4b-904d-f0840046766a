package com.youdao.ead.pacioli.service;

import com.youdao.ead.pacioli.dto.task.PageTaskReqDTO;
import com.youdao.ead.pacioli.dto.task.TaskAuditDTO;
import com.youdao.ead.pacioli.dto.task.TaskDetailDTO;
import com.youdao.ead.pacioli.dto.task.TaskItemDTO;
import com.youdao.ead.pacioli.entity.pacioli.Task;
import com.youdao.ead.pacioli.enums.RoleEnum;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
public interface TaskService {
    /**
     * 分页查询
     *
     * @param pageTaskReqDTO
     * @param curUserId
     * @param currentRole
     * @return
     */
    Page<TaskItemDTO> page(PageTaskReqDTO pageTaskReqDTO, Long curUserId, RoleEnum currentRole);

    /**
     * 更新任务状态
     *
     * @param taskId     任务id
     * @param isTakeDown 任务状态变更行为，true=下架，false=发布
     * @param userId
     * @param currentRole
     */
    void updateTaskState(Long taskId, boolean isTakeDown, Long userId, RoleEnum currentRole);

    /**
     * 获取任务详细信息
     *
     * @param taskId
     * @param isEdit            true=编辑，false=编辑复制
     * @return
     */
    TaskDetailDTO getTaskDetail(Long taskId, boolean isEdit);

    /**
     * 插入或者更新一条任务信息
     *
     * @param taskDetailDTO 填写的任务信息
     * @param isSave        true=保存，false=发布
     * @param roleEnum
     * @param userId
     */
    void insertUpdateTask(TaskDetailDTO taskDetailDTO, boolean isSave, RoleEnum roleEnum, Long userId);

    /**
     * 审核任务，需要先获取任务的详细信息
     *
     * @param taskId
     * @return
     */
    TaskAuditDTO auditByTaskId(Long taskId);

    /**
     * 保存审核结果
     *
     * @param taskAuditDTO 审核时的任务信息
     * @param operatorId   运营id
     * @param isPass       true=通过，false=不通过
     * @param roleEnum
     */
    void auditResult(TaskAuditDTO taskAuditDTO, Long operatorId, boolean isPass, RoleEnum roleEnum);

    /**
     * 不分页，全量查询
     *
     * @param pageTaskReqDTO    分页参数不生效，只使用查询条件参数
     * @param curUserId
     * @param currentRole
     * @return
     */
    List<TaskItemDTO> unPage(PageTaskReqDTO pageTaskReqDTO, Long curUserId, RoleEnum currentRole);

    /**
     * {@link com.youdao.ead.pacioli.dto.download.DownloadTaskDTO}
     *
     * @param response
     * @param list              下载的task item list数据
     * @param currentRole       当前登陆人员角色，不同角色下载文件的表头字段不一样
     */
    void downloadExcel(HttpServletResponse response, List<TaskItemDTO> list, RoleEnum currentRole) throws Exception;

    /**
     * 任务名称重复校验，（校验重复名称不会和任务当前的名称比较）
     *
     * @param taskId        当前任务ID
     * @param taskName      任务新名称
     * @return
     */
    boolean taskNameExisted(Long taskId, String taskName);

    /**
     * 判断某个任务的所有子任务是否需要aid
     *
     * @param taskId        当前任务ID
     * @param roleEnum      用户角色
     * @return
     */
    boolean subTaskNeedAid(Long taskId, RoleEnum roleEnum);

    List<Task> getBySalesId(Long salesUserId);

    List<Task> getBySalesIdIn(Collection<Long> salesUserId);


    List<Task> getByAdvisorId(Long advisorId);

}
