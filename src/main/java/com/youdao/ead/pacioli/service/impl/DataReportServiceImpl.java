package com.youdao.ead.pacioli.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.data.CommentData;
import com.alibaba.excel.metadata.data.RichTextStringData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.youdao.ead.pacioli.constants.CommonConstants;
import com.youdao.ead.pacioli.constants.ResponseType;
import com.youdao.ead.pacioli.core.druid.*;
import com.youdao.ead.pacioli.core.exception.CustomException;
import com.youdao.ead.pacioli.dto.customer.CustomerInfoDTO;
import com.youdao.ead.pacioli.dto.datareport.*;
import com.youdao.ead.pacioli.dto.datareport.channel.ChannelReportItemDTO;
import com.youdao.ead.pacioli.dto.datareport.channel.ChannelReportItemFlatDTO;
import com.youdao.ead.pacioli.dto.datareport.channel.PageChannelReportByConditionsDTO;
import com.youdao.ead.pacioli.dto.datareport.view.DataViewUploadFailInfoDTO;
import com.youdao.ead.pacioli.dto.datareport.view.DataViewUploadResBatchDTO;
import com.youdao.ead.pacioli.dto.datareport.view.DataViewUploadResDTO;
import com.youdao.ead.pacioli.dto.task.CategoryDTO;
import com.youdao.ead.pacioli.dto.task.PageTaskReqDTO;
import com.youdao.ead.pacioli.dto.task.TaskItemDTO;
import com.youdao.ead.pacioli.dto.upload.UploadFeedbackValidConvertNumBatchDTO;
import com.youdao.ead.pacioli.entity.eadb1.Sponsor;
import com.youdao.ead.pacioli.entity.pacioli.*;
import com.youdao.ead.pacioli.enums.*;
import com.youdao.ead.pacioli.mapper.ConversionActionMapeer;
import com.youdao.ead.pacioli.mapper.CustomerMapper;
import com.youdao.ead.pacioli.mapper.DataReportMapper;
import com.youdao.ead.pacioli.repository.eadb1.SponsorRepository;
import com.youdao.ead.pacioli.repository.pacioli.*;
import com.youdao.ead.pacioli.service.*;
import com.youdao.ead.pacioli.util.LocalDateUtil;
import com.youdao.ead.pacioli.util.UploadUtil;
import in.zapr.druid.druidry.query.DruidQuery;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.beans.PropertyDescriptor;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.youdao.ead.pacioli.constants.CommonConstants.ACTIVITY_ID_MIN;
import static com.youdao.ead.pacioli.constants.CommonConstants.EFFECTIVE_CONVERSION_ACTION_ID_DEFAULT;
import static com.youdao.ead.pacioli.constants.ResponseType.SERVICE_ERROR;
import static com.youdao.ead.pacioli.enums.ConversionActionTypeEnum.*;

/**
 * <AUTHOR> Li
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataReportServiceImpl implements DataReportService {

    private final DataReportRepository dataReportRepository;
    private final TaskRepository taskRepository;
    private final SubTaskRepository subTaskRepository;
    private final CustomerRepository customerRepository;
    private final DataReportMapper dataReportMapper;
    private final ChannelRepository channelRepository;
    private final UserRoleRelationService userRoleRelationService;
    private final ConversionActionRepository conversionActionRepository;
    private final ConversionActionMapeer conversionActionMapeer;
    private final DruidHttpClient druidHttpClient;
    private final CustomerMapper customerMapper;
    private final SponsorRepository sponsorRepository;
    private final BasicService basicService;
    private final TaskService taskService;
    private final CategoryService categoryService;
    private final SponsorService sponsorService;
    private final ExecutorService druidQueryExecutor;

    private final static BigDecimal FLOW_COST_RATE = new BigDecimal("0.035");

    private static final Integer THREADS_COUNT = 20;
    public static final Integer MAX_THREADS_COUNT = 512;


    private static final ExecutorService THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(
            THREADS_COUNT,
            MAX_THREADS_COUNT,
            2,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(MAX_THREADS_COUNT),
            new ThreadFactoryBuilder()
                    .setNameFormat("roi-data-report-combine-thread-%d")
                    .setDaemon(false)
                    .build()
    );

    @Override
    public Page<DataReportItemDTO> pageByConditions(PageDataReportByConditionsDTO dto, Long userId, RoleEnum role) {
        List<DataReport> dataReports = getByConditions(
                dto.getCustomerName(), dto.getSellerName(), dto.getAdvisorName(), dto.getOperatorName(),
                dto.getTaskName(), dto.getTaskId(), dto.getBeginDate(), dto.getEndDate(),
                dto.getIntegrationMethodEnums(), userId, role, false,
                dto.getBdName(), dto.getChannelName(), dto.getSubTaskName(), dto.getSubTaskId(), dto.getChannelCode()
        );
        if (CollectionUtils.isEmpty(dataReports)) {
            return new PageImpl<>(Collections.emptyList(), PageRequest.of(dto.getPageNum(), dto.getPageSize()), 0L);
        } else {
            return renderDataReportsToDTO(dataReports, dto.getTimeGranularityEnum(), dto.getConversionActionIdList(), dto.getPageNum(), dto.getPageSize(), role, false);
        }
    }

    private List<DataReport> getByConditions(String customerName, String sellerName, String advisorName, String operatorName,
                                             String taskName, String taskId, LocalDate beginDate, LocalDate endDate,
                                             Set<IntegrationMethodEnum> integrationMethodEnums, Long userId, RoleEnum role, boolean isForRoi,
                                             String bdName, String channelName, String subTaskName, String subTaskId, String channelCode) {
        PageTaskReqDTO reqDTO = new PageTaskReqDTO();
        reqDTO.setCustomerName(customerName);
        reqDTO.setTaskId(taskId);
        reqDTO.setTaskName(taskName);
        reqDTO.setTaskState(TaskStateEnum.ROLE_ALL_STATE.getId());
        Stream<TaskItemDTO> stream = taskService.unPage(reqDTO, userId, role).stream();
        if (Objects.nonNull(sellerName)) {
            stream = stream.filter(x -> x.getSalesName().contains(sellerName));
        }
        if (Objects.nonNull(advisorName)) {
            stream = stream.filter(x -> x.getAdvisorName().contains(advisorName));
        }
        if (Objects.nonNull(operatorName)) {
            stream = stream.filter(x -> x.getOperatorName().contains(operatorName));
        }
        if (CollectionUtils.isNotEmpty(integrationMethodEnums)) {
            Set<Long> filterTaskIdSet = taskRepository.findAllByIntegrationMethodIn(integrationMethodEnums).stream()
                    .map(BaseEntity::getId)
                    .collect(Collectors.toSet());
            stream = stream.filter(x -> filterTaskIdSet.contains(x.getTaskId()));
        }
        Set<Long> taskIdSet = stream.map(TaskItemDTO::getTaskId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(taskIdSet)) {
            return Collections.emptyList();
        }
        List<DataReport> dataReports = queryDataReports(taskIdSet, advisorName, beginDate, endDate,
                userId, role, isForRoi,
                bdName, channelName, subTaskName, subTaskId, channelCode);
        // 查询并添加“回传转化数”
        if (RoleEnum.identifyOperator(role)) {
            injectDruidData(dataReports);
        }
        return dataReports;
    }

    private void injectDruidData(List<DataReport> dataReports) {
        try {
            if (CollectionUtils.isNotEmpty(dataReports)) {
                List<SubTask> subTasks = subTaskRepository.findAllByActivityIdGreaterThanEqual(ACTIVITY_ID_MIN);
                Map<Long, Long> subTaskId2ActivityIdMap = subTasks.stream().collect(Collectors.toMap(BaseEntity::getId, SubTask::getActivityId));
                Map<Long, List<DataReport>> aid2DataMap = dataReports.stream()
                        .filter(x -> subTaskId2ActivityIdMap.containsKey(x.getSubTaskId()))
                        .collect(Collectors.groupingBy(x -> subTaskId2ActivityIdMap.get(x.getSubTaskId())));
                if (!aid2DataMap.isEmpty()) {
                    List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
                    for (Map.Entry<Long, List<DataReport>> entry : aid2DataMap.entrySet()) {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            try {
                                injectDruidDataForActivityId(entry.getKey(), entry.getValue());
                            } catch (Exception e) {
                                log.warn("unable query druid data for one subTask, subTaskId={}", entry.getKey(), e);
                            }
                        }, THREAD_POOL_EXECUTOR);
                        completableFutureList.add(future);
                    }
                    CompletableFuture<Void> future = CompletableFuture.allOf(
                            completableFutureList.toArray(new CompletableFuture[0])
                    ).orTimeout(20, TimeUnit.SECONDS);
                    future.get();
                }
            }
        } catch (Exception e) {
            log.error("inject druid data error, ", e);
            throw new CustomException(ResponseType.SERVICE_ERROR);
        }
    }

    /**
     * 根据activityId和dataReports的日期，从Druid查询对应的转化数据，并把转化数据合并到{@link DataReport#getQueryDruidData()} ()}
     *
     * @param activityId
     * @param dataReports
     */
    private void injectDruidDataForActivityId(Long activityId, List<DataReport> dataReports) {
        List<LocalDate> localDates = dataReports.stream().map(DataReport::getDate).collect(Collectors.toList());
        Map<LocalDate, DataReport> date2DataMap = dataReports.stream().collect(Collectors.toMap(DataReport::getDate, x -> x));
        DruidQuery druidQuery = DruidQueryTool.conversionQueryBuilder(activityId, TimeGranularityEnum.DAY, localDate2LocalDateTime(localDates));
        try {
            CompletableFuture<List<ThirdPartyStatQueryResult>> thirdPartyStatResultFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return druidHttpClient.query(druidQuery, ThirdPartyStatQueryResult.class);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }, druidQueryExecutor);

            dataReports.forEach(item -> item.setActivityId(activityId));

            CompletableFuture.allOf(thirdPartyStatResultFuture).get();

            for (ThirdPartyStatQueryResult result : thirdPartyStatResultFuture.get()) {
                DataReport dataReport = date2DataMap.get(result.getTimestamp().toLocalDate());
                if (dataReport != null) {
                    ThirdPartyStatQueryResult.Event event = result.getEvent();
                    CtActionEnum ctActionEnum = CtActionEnum.byCtActionText(event.getCtAction());
                    if (ctActionEnum != CtActionEnum.UNKNOWN) {
                        Long id = ctActionEnum.getId();
                        if (dataReport.getQueryDruidData() == null) {
                            dataReport.setQueryDruidData(new HashMap<>());
                        }
                        dataReport.getQueryDruidData().put(id, new BigDecimal(event.getConv()));
                    }
                }
            }
        } catch (Exception e) {
            log.error("druid query failed", e);
            throw new RuntimeException(e);
        }
    }

    private List<DataReport> queryDataReports(Set<Long> taskIdSet, String advisorName, LocalDate beginDate, LocalDate endDate,
                                              Long userId, RoleEnum role, boolean isForRoi,
                                              String bdName, String channelName, String subTaskName, String subTaskId, String channelCode) {
        // bd角色只能看自己渠道的转化数据, 但是要获取全部的转化数据来算ROI
        if (isForRoi) {
            return dataReportRepository.getByConditions(taskIdSet, null, null, null, beginDate, endDate);
        } else {
            // 即使isForRoi为false：bdName，channelName，subTaskName，subTaskId也可能为null
            Set<Long> channelIds = buildChannelIdConditions(bdName, channelName, userId, role, channelCode);
            if (ObjectUtils.anyNotNull(bdName, channelName) && CollectionUtils.isEmpty(channelIds)) {
                return Collections.emptyList();
            }
            Set<Long> subTaskIds = buildSubTaskIdConditions(subTaskName, subTaskId);
            if (ObjectUtils.anyNotNull(subTaskId, subTaskName) && CollectionUtils.isEmpty(subTaskIds)) {
                return Collections.emptyList();
            }
            Set<Long> advisorIds = buildAdvisorIdConditions(advisorName);
            if (Objects.nonNull(advisorName) && CollectionUtils.isEmpty(advisorIds)) {
                return Collections.emptyList();
            }
            return dataReportRepository.getByConditions(taskIdSet, subTaskIds, channelIds, advisorIds, beginDate, endDate);
        }
    }

    private Set<Long> buildAdvisorIdConditions(String advisorName) {
        if (Objects.nonNull(advisorName)) {
            return userRoleRelationService.getByRoleKeyInAndNameContaining(
                            RoleEnum.ADVISOR_LIST.stream().map(RoleEnum::getRoleKey).toList(), advisorName)
                    .stream().map(User::getUserRoleRelationId).collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    private Set<Long> buildSubTaskIdConditions(@Nullable String subTaskName, @Nullable String subTaskId) {
        if (Objects.nonNull(subTaskId)) {
            return parseIdInString(subTaskId);
        }
        if (Objects.nonNull(subTaskName)) {
            List<SubTask> nameContainingSubTasks = subTaskRepository.findAllByNameContaining(subTaskName);
            if (CollectionUtils.isNotEmpty(nameContainingSubTasks)) {
                return nameContainingSubTasks.stream().map(SubTask::getId).collect(Collectors.toSet());
            }
        }
        return Collections.emptySet();
    }

    private Set<Long> buildChannelIdConditions(String bdName, String channelName, Long userId, RoleEnum roleEnum, String channelCode) {
        List<Channel> matchedChannels = Collections.emptyList();
        if (RoleEnum.identifyBd(roleEnum)) {
            Set<Long> relIds = basicService.allRelationUserIdForPermission(userId, roleEnum);
            if (Objects.nonNull(bdName)) {
                relIds = userRoleRelationService.getByUserRelationIdInAndNameContaining(relIds, bdName)
                        .stream().map(User::getUserRoleRelationId).collect(Collectors.toSet());
            }
            if (Objects.nonNull(channelName) || Objects.nonNull(channelCode)) {
                Set<Channel> temp = new HashSet<>();
                if (Objects.nonNull(channelName)) {
                    temp.addAll(channelRepository.findAllByBdUserIdInAndNameContaining(relIds, channelName));
                }
                if (Objects.nonNull(channelCode)) {
                    temp.addAll(channelRepository.findAllByBdUserIdInAndCodeContaining(relIds, channelCode));
                }
                matchedChannels = List.copyOf(temp);
            } else {
                matchedChannels = channelRepository.findAllByBdUserIdIn(relIds);
            }
        } else {
            if (Objects.nonNull(channelCode)) {
                matchedChannels = channelRepository.findAllByCodeContaining(channelCode);
            }
        }
        return matchedChannels.stream().map(BaseEntity::getId).collect(Collectors.toSet());
    }

    private Page<DataReportItemDTO> renderDataReportsToDTO(List<DataReport> dataReports, TimeGranularityEnum timeGranularityEnum,
                                                           @Nullable Set<Long> conversionActionIds, int pageNum, int pageSize,
                                                           RoleEnum roleEnum, boolean isForRoi) {
        Map<Long, ConversionAction> id2ConversionAction = conversionActionRepository.findAll()
                .stream()
                .collect(Collectors.toMap(ConversionAction::getId, Function.identity()
                ));
        List<DataReport> combinedDataReports = combineDataByTimeGranularity(dataReports, timeGranularityEnum, id2ConversionAction);
        List<List<DataReport>> partition = pageAndSortDataReport(combinedDataReports, timeGranularityEnum, pageSize);
        if (partition.size() < pageNum) {
            return new PageImpl<>(Collections.emptyList(), PageRequest.of(pageNum, pageSize), combinedDataReports.size());
        }
        List<DataReportItemDTO> pagedDataReport = dataReportMapper.do2DataItemDTO(partition.get(pageNum));
        injectTaskInfo(pagedDataReport, roleEnum, isForRoi);
        if (!isForRoi) {
            injectSubTaskInfoAndRemoveInvisibleConversionNum(pagedDataReport);
            if (RoleEnum.identifyOperator(roleEnum) || RoleEnum.identifyBd(roleEnum) || RoleEnum.ADMIN == roleEnum) {
                injectChannelInfo(pagedDataReport, timeGranularityEnum, roleEnum);
            }
            if (RoleEnum.BD_LEADER == roleEnum) {
                injectBdInfo(pagedDataReport);
            }
        }
        boolean needInjectAdvisorInfo = isForRoi ? RoleEnum.ADVISOR != roleEnum
                : (RoleEnum.SELLER != roleEnum && RoleEnum.ADVISOR != roleEnum);
        if (needInjectAdvisorInfo) {
            injectAdvisorInfo(pagedDataReport);
        }
        eraseConversionActionsForDataReport(pagedDataReport, conversionActionIds);
        return new PageImpl<>(pagedDataReport, PageRequest.of(pageNum, pageSize), combinedDataReports.size());
    }

    private List<List<DataReport>> pageAndSortDataReport(List<DataReport> combinedDataReports, TimeGranularityEnum timeGranularityEnum, int pageSize) {
        sortDataReports(combinedDataReports, timeGranularityEnum);
        return ListUtils.partition(combinedDataReports, pageSize);
    }

    private void sortDataReports(List<DataReport> dataReports, TimeGranularityEnum timeGranularityEnum) {
        if (TimeGranularityEnum.ALL.equals(timeGranularityEnum)) {
            dataReports.sort(Comparator.comparing(DataReport::getTaskId).reversed());
        } else {
            dataReports.sort(Comparator.comparing(DataReport::getDate)
                    .thenComparing(DataReport::getTaskId).reversed());
        }
    }

    private void sortDataReportItems(List<DataReportItemDTO> dataReportItemDTOS, TimeGranularityEnum timeGranularityEnum) {
        if (TimeGranularityEnum.ALL.equals(timeGranularityEnum)) {
            dataReportItemDTOS.sort(Comparator.comparing(DataReportItemDTO::getTaskId).reversed());
        } else {
            dataReportItemDTOS.sort(Comparator.comparing(DataReportItemDTO::getDate)
                    .thenComparing(DataReportItemDTO::getTaskId).reversed());
        }
    }

    private void eraseConversionActionsForDataReport(List<DataReportItemDTO> dataReportItemDTOS, Set<Long> conversionActionIds) {
        if (CollectionUtils.isEmpty(conversionActionIds)) {
            return;
        }
        dataReportItemDTOS.forEach(item -> {
            removeKeyNotInList(item.getExtendConversionActionMap(), conversionActionIds);
        });
    }

    private void eraseConversionActionsForChannelReport(List<ChannelReportItemDTO> channelReportItemDTOS, Set<Long> conversionActionIds) {
        if (CollectionUtils.isEmpty(conversionActionIds)) {
            return;
        }
        channelReportItemDTOS.forEach(item -> {
            removeKeyNotInList(item.getExtendConversionActionMap(), conversionActionIds);
        });
    }

    private void removeKeyNotInList(Map<Long, BigDecimal> conversionActionId2Num, Set<Long> conversionActionIds) {
        conversionActionId2Num.keySet().removeIf((key) -> !conversionActionIds.contains(key));
    }

    private void injectAdvisorInfo(List<DataReportItemDTO> dataReportItemDTOS) {
        Set<Long> advisorIds = dataReportItemDTOS.stream()
                .map(DataReportItemDTO::getAdvisorId)
                .collect(Collectors.toSet());
        Map<Long, User> id2AdvisorUser = userRoleRelationService.getByUserRoleRelationId(advisorIds)
                .stream().collect(Collectors.toMap(User::getUserRoleRelationId, Function.identity()));
        dataReportItemDTOS.forEach(item -> {
            item.setAdvisorName(id2AdvisorUser.get(item.getAdvisorId()).getName());
        });
    }

    private void injectBdInfo(List<DataReportItemDTO> pagedDataReport) {
        List<Long> channelIds = pagedDataReport.stream().map(DataReportItemDTO::getChannelId).collect(Collectors.toList());
        List<Channel> channels = channelRepository.findAllById(channelIds);
        Map<Long, User> userMap = userRoleRelationService.getByUserRoleRelationId(channels.stream().map(Channel::getBdUserId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(User::getUserRoleRelationId, x -> x));
        Map<Long, String> channelId2BdNameMap = channels.stream().collect(Collectors.toMap(
                BaseEntity::getId, x -> userMap.get(x.getBdUserId()).getName()
        ));
        for (DataReportItemDTO dto : pagedDataReport) {
            dto.setBdName(channelId2BdNameMap.get(dto.getChannelId()));
        }
    }

    private void injectChannelInfo(List<DataReportItemDTO> dataReportItemDTOS, TimeGranularityEnum timeGranularityEnum, RoleEnum currentRole) {
        if (TimeGranularityEnum.DAY.equals(timeGranularityEnum)) {
            Set<Long> channelIds = dataReportItemDTOS.stream()
                    .map(DataReportItemDTO::getChannelId)
                    .collect(Collectors.toSet());
            Map<Long, Channel> id2Channel = channelRepository.findAllById(channelIds)
                    .stream()
                    .collect(Collectors.toMap(Channel::getId, Function.identity()));
            dataReportItemDTOS.forEach(item -> {
                if (RoleEnum.identifyBd(currentRole)) {
                    item.setChannelName(id2Channel.get(item.getChannelId()).getName());
                }
                item.setChannelCode(id2Channel.get(item.getChannelId()).getCode());
            });
        } else {
            dataReportItemDTOS.forEach(item -> {
                item.setChannelName("-");
            });
        }
    }

    /**
     * 注入客户名称/任务名称/返点/销售名称/运营名称
     */
    private void injectTaskInfo(List<DataReportItemDTO> dataReportItemDTOS, RoleEnum roleEnum, boolean isForRoi) {
        if (CollectionUtils.isEmpty(dataReportItemDTOS)) {
            return;
        }
        Set<Long> taskIds = dataReportItemDTOS.stream().map(DataReportItemDTO::getTaskId).collect(Collectors.toSet());
        List<Task> taskList = taskRepository.findAllById(taskIds);
        Map<Long, Task> id2Task = taskList.stream().collect(Collectors.toMap(Task::getId, Function.identity()));
        Map<Long, Customer> id2Customer = customerRepository.findAllById(
                        taskList.stream()
                                .map(Task::getCustomerId)
                                .collect(Collectors.toSet()))
                .stream()
                .collect(Collectors.toMap(Customer::getId, Function.identity()));
        dataReportItemDTOS.forEach(dataReportItemDTO -> {
            Task task = id2Task.get(dataReportItemDTO.getTaskId());
            Customer customer = id2Customer.get(task.getCustomerId());
            dataReportItemDTO.setCustomerId(customer.getId());
            dataReportItemDTO.setCustomerName(customer.getName());
            dataReportItemDTO.setRebate(task.getRebate());
            dataReportItemDTO.setIntegrationMethodEnums(task.getIntegrationMethod());
            dataReportItemDTO.setIntegrationMethodCustomText(task.getIntegrationMethodCustomText());
            if (RoleEnum.identifySeller(roleEnum)) {
                dataReportItemDTO.setZhixuanUsernames(String.join(",", task.getZhixuanUsername()));
            }
            dataReportItemDTO.setTaskName(task.getName());
        });
        boolean needInjectSellerInfo = isForRoi ? RoleEnum.SELLER != roleEnum
                : (RoleEnum.SELLER != roleEnum && RoleEnum.ADVISOR != roleEnum);
        if (needInjectSellerInfo) {
            injectSellerInfo(dataReportItemDTOS, id2Task);
        }
        boolean needInjectOperatorInfo = RoleEnum.identifyBd(roleEnum) || RoleEnum.OPERATOR_LEADER == roleEnum || RoleEnum.ADMIN == roleEnum;
        if (needInjectOperatorInfo) {
            injectOperatorInfo(dataReportItemDTOS, id2Customer, id2Task);
        }
    }

    private void injectOperatorInfo(List<DataReportItemDTO> dataReportItemDTOS, Map<Long, Customer> id2Customer, Map<Long, Task> id2Task) {
        Set<Long> operatorUserId = id2Customer.values().stream().map(Customer::getOperatorUserId).collect(Collectors.toSet());
        Map<Long, User> id2OperatorUser = userRoleRelationService.getByUserRoleRelationId(operatorUserId)
                .stream().collect(Collectors.toMap(User::getUserRoleRelationId, Function.identity()));
        dataReportItemDTOS.forEach(item -> item.setOperatorName(
                id2OperatorUser.get(
                        id2Customer.get(
                                id2Task.get(item.getTaskId()).getCustomerId()
                        ).getOperatorUserId()
                ).getName()
        ));
    }

    private void injectSellerInfo(List<DataReportItemDTO> dataReportItemDTOS, Map<Long, Task> id2Task) {
        Set<Long> sellerIds = id2Task.values().stream().map(Task::getSalesId).collect(Collectors.toSet());
        Map<Long, User> id2Sellers = userRoleRelationService.getByUserRoleRelationId(sellerIds)
                .stream().collect(Collectors.toMap(User::getUserRoleRelationId, Function.identity()));
        dataReportItemDTOS.forEach(item -> {
            item.setSellerName(id2Sellers.get(id2Task.get(item.getTaskId()).getSalesId()).getName());
        });
    }

    private void injectSubTaskInfoAndRemoveInvisibleConversionNum(List<DataReportItemDTO> dataReportItemDTOS) {
        if (CollectionUtils.isEmpty(dataReportItemDTOS)) {
            return;
        }
        Set<Long> subTaskIds = dataReportItemDTOS.stream().map(DataReportItemDTO::getSubTaskId).collect(Collectors.toSet());
        Map<Long, SubTask> id2SubTask = subTaskRepository.findAllById(subTaskIds).stream().collect(Collectors.toMap(SubTask::getId, Function.identity()));
        dataReportItemDTOS.forEach(dataReportItemDTO -> {
            dataReportItemDTO.setSubTaskName(id2SubTask.get(dataReportItemDTO.getSubTaskId()).getName());
            dataReportItemDTO.setPromotionLink(id2SubTask.get(dataReportItemDTO.getSubTaskId()).getPromotionLink());
        });
    }

    private List<DataReport> combineDataByTimeGranularity(List<DataReport> dataReports, TimeGranularityEnum timeGranularityEnum,
                                                          Map<Long, ConversionAction> id2ConversionAction) {
        List<DataReport> product = Collections.emptyList();
        Map<Long, List<DataReport>> subTaskId2DataReport = dataReports.stream()
                .collect(Collectors.groupingBy(DataReport::getSubTaskId));
        switch (timeGranularityEnum) {
            case DAY -> {
                conformForDay(dataReports);
                return dataReports;
            }
            case WEEK, MONTH -> {
                product = aggregationForDateRang(timeGranularityEnum, subTaskId2DataReport, id2ConversionAction);
            }
            case ALL -> {
                product = aggregationForAll(subTaskId2DataReport, id2ConversionAction);
            }
        }
        return product;
    }

    private List<DataReport> aggregationForAll(Map<Long, List<DataReport>> subTaskId2DataReport, Map<Long, ConversionAction> id2ConversionAction) {
        List<DataReport> product = new ArrayList<>();
        subTaskId2DataReport.forEach((subTaskId, singleSubTaskIdDataReports) -> {
            Map<Long, BigDecimal> conversionActionId2Total = sumConversionActionByIdForDataReport(singleSubTaskIdDataReports, id2ConversionAction);
            DataReport combinedDataReport = singleSubTaskIdDataReports.stream().min(Comparator.comparing(DataReport::getDate)).orElseThrow(() -> new CustomException(ResponseType.SERVICE_ERROR));
            try {
                combinedDataReport.setConversionActionId2TotalNum(conversionActionId2Total);
                combinedDataReport.setTotalActiveNum(
                        singleSubTaskIdDataReports.stream().map(DataReport::getActiveNum).reduce(
                                BigDecimal.ZERO, BigDecimal::add
                        ).longValue()
                );
                combinedDataReport.setTotalFeedbackValidConvertNum(
                        singleSubTaskIdDataReports.stream().map(DataReport::getFeedbackValidConvertNum).reduce(
                                BigDecimal.ZERO, BigDecimal::add
                        ).longValue()
                );
                combinedDataReport.setChannelCost(getChannelCost(singleSubTaskIdDataReports));
                combinedDataReport.setTotalEffectiveConversionNum(getEffectiveConversionActionsTotal(singleSubTaskIdDataReports));
                combinedDataReport.setEffectiveConversionActionId(null);
                combinedDataReport.setQueryDate(singleSubTaskIdDataReports.stream().map(DataReport::getDate).collect(Collectors.toList()));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            product.add(combinedDataReport);
        });
        return product;
    }

    private List<DataReport> aggregationForDateRang(TimeGranularityEnum timeGranularityEnum, Map<Long, List<DataReport>> subTaskId2DataReport,
                                                    Map<Long, ConversionAction> id2ConversionAction) {
        List<DataReport> product = new ArrayList<>();
        subTaskId2DataReport.forEach((subTaskId, singleSubTaskIdDataReports) -> {
            singleSubTaskIdDataReports.forEach(dataReport -> {
                dataReport.setQueryDate(Collections.singletonList(dataReport.getDate()));
                dataReport.setDate(LocalDateUtil.getFirstDay(dataReport.getDate(), timeGranularityEnum));
            });
            Map<LocalDate, List<DataReport>> firstDay2DataReports = singleSubTaskIdDataReports.stream()
                    .collect(Collectors.groupingBy(DataReport::getDate));
            combineByDate(product, firstDay2DataReports, id2ConversionAction);
        });
        return product;
    }

    private void conformForDay(List<DataReport> dataReports) {
        dataReports.forEach(item -> {
            item.setChannelCost(getChannelCost(Collections.singletonList(item)));
            item.setTotalActiveNum(item.getActiveNum().longValue());
            item.setTotalFeedbackValidConvertNum(item.getFeedbackValidConvertNum().longValue());
            item.setTotalEffectiveConversionNum(getEffectiveConversionActionsTotal(Collections.singletonList(item)));
            Map<Long, BigDecimal> conversionActionId2TotalNum = new HashMap<>(item.getExtendConversionData());
            if (item.getQueryDruidData() != null) {
                conversionActionId2TotalNum.putAll(item.getQueryDruidData());
            }
            item.setConversionActionId2TotalNum(conversionActionId2TotalNum);
            item.setQueryDate(Collections.singletonList(item.getDate()));
        });
    }

    private void combineByDate(List<DataReport> product, Map<LocalDate, List<DataReport>> unitFirstDay2DataReports, Map<Long, ConversionAction> id2ConversionAction) {
        unitFirstDay2DataReports.forEach((firstDayOfUnit, dataReportsOneUnit) -> {
            try {
                DataReport groupedDataReport = dataReportsOneUnit.stream().findAny().orElseThrow(() -> new CustomException(ResponseType.SERVICE_ERROR));
                groupedDataReport.setTotalActiveNum(
                        dataReportsOneUnit.stream().map(DataReport::getActiveNum).reduce(BigDecimal.ZERO, BigDecimal::add).longValue()
                );
                groupedDataReport.setTotalFeedbackValidConvertNum(
                        dataReportsOneUnit.stream().map(DataReport::getFeedbackValidConvertNum).reduce(BigDecimal.ZERO, BigDecimal::add).longValue()
                );
                Map<Long, BigDecimal> sumConversionAction = sumConversionActionByIdForDataReport(dataReportsOneUnit, id2ConversionAction);
                groupedDataReport.setConversionActionId2TotalNum(sumConversionAction);
                long effectiveConversionActionsTotal = getEffectiveConversionActionsTotal(dataReportsOneUnit);
                groupedDataReport.setChannelCost(getChannelCost(dataReportsOneUnit));
                groupedDataReport.setTotalEffectiveConversionNum(effectiveConversionActionsTotal);
                groupedDataReport.setQueryDate(dataReportsOneUnit.stream().map(DataReport::getQueryDate).toList().stream().flatMap(List::stream).collect(Collectors.toList()));
                groupedDataReport.setEffectiveConversionActionId(null);
                product.add(groupedDataReport);
            } catch (Exception e) {
                log.error("combine data report error", e);
                throw new CustomException(ResponseType.SERVICE_ERROR);
            }
        });
    }

    private long getChannelCost(List<DataReport> dataReports) {
        AtomicLong channelCost = new AtomicLong(0L);
        dataReports.forEach(dataReport -> {
            channelCost.addAndGet(dataReport.getChannelCpaUnitPrice() *
                    getEffectiveConversionNum(
                            dataReport.getEffectiveConversionActionId(),
                            dataReport.getActiveNum(),
                            dataReport.getExtendConversionData(),
                            dataReport.getQueryDruidData()).longValue()
            );
        });
        return channelCost.get();
    }

    private long getEffectiveConversionActionsTotal(List<DataReport> dataReports) {
        AtomicLong total = new AtomicLong(0L);
        dataReports.forEach(dataReport -> {
            total.addAndGet(getEffectiveConversionNum(
                    dataReport.getEffectiveConversionActionId(),
                    dataReport.getActiveNum(),
                    dataReport.getExtendConversionData(),
                    dataReport.getQueryDruidData()).longValue()
            );
        });
        return total.get();
    }

    private BigDecimal getEffectiveConversionNum(Long effectiveConversionActionId, BigDecimal activeNum,
                                                 Map<Long, BigDecimal> extendConversionActionMap,
                                                 Map<Long, BigDecimal> queryDruidData) {
        BigDecimal fromDataReport = EFFECTIVE_CONVERSION_ACTION_ID_DEFAULT.equals(effectiveConversionActionId) ? activeNum
                : extendConversionActionMap.getOrDefault(effectiveConversionActionId, new BigDecimal(0));
        CtActionEnum ctActionEnum = CtActionEnum.byEffectiveConversionActionId(effectiveConversionActionId);
        if (ctActionEnum != CtActionEnum.UNKNOWN && queryDruidData != null) {
            BigDecimal fromDruid = queryDruidData.get(ctActionEnum.getId());
            if (fromDruid != null && fromDruid.compareTo(BigDecimal.ZERO) > 0 && fromDruid.compareTo(fromDataReport) < 0) {
                return fromDruid;
            }
        }
        return fromDataReport;
    }

    /**
     * if list more than 1 conversion action that only type == 0 can add, others will ignore.
     */
    private Map<Long, BigDecimal> sumConversionActionByIdForDataReport(List<DataReport> dataReports,
                                                                       Map<Long, ConversionAction> id2ConversionAction) {
        List<Map<Long, BigDecimal>> extendConversionDataAndDruidDataList = new ArrayList<>();
        dataReports.forEach(dataReport -> {
            if (dataReport.getExtendConversionData() != null && !dataReport.getExtendConversionData().isEmpty()) {
                extendConversionDataAndDruidDataList.add(dataReport.getExtendConversionData());
            }
            if (dataReport.getQueryDruidData() != null && !dataReport.getQueryDruidData().isEmpty()) {
                extendConversionDataAndDruidDataList.add(dataReport.getQueryDruidData());
            }
        });
        return sumConversionActionById(extendConversionDataAndDruidDataList, id2ConversionAction);
    }

    /**
     * if list more than 1 conversion action that only type == 0 can add, others will ignore.
     */
    private Map<Long, BigDecimal> sumConversionActionByIdForChannelReport(List<ChannelReportItemDTO> channelReportItemDTOList,
                                                                          Map<Long, ConversionAction> id2ConversionAction) {
        List<Map<Long, BigDecimal>> extendConversionDataAndDruidDataList = new ArrayList<>();
        channelReportItemDTOList.forEach(dto -> {
            if (dto.getExtendConversionActionMap() != null && !dto.getExtendConversionActionMap().isEmpty()) {
                extendConversionDataAndDruidDataList.add(dto.getExtendConversionActionMap());
            }
        });
        return sumConversionActionById(extendConversionDataAndDruidDataList, id2ConversionAction);
    }

    private Map<Long, BigDecimal> sumConversionActionById(List<Map<Long, BigDecimal>> extendConversionDataAndDruidDataList,
                                                          Map<Long, ConversionAction> id2ConversionAction) {
        Map<Long, BigDecimal> conversionActionId2Valuecombined = new HashMap<>();
        Map<Long, List<BigDecimal>> id2SumRateValue = new HashMap<>();
        extendConversionDataAndDruidDataList.forEach(map -> {
            map.forEach((key, value) -> {
                if (ConversionActionTypeEnum.canAggregateCalculation(id2ConversionAction.get(key).getType())) {
                    if (conversionActionId2Valuecombined.containsKey(key)) {
                        conversionActionId2Valuecombined.computeIfPresent(key, (k, v) -> v.add(value));
                    } else {
                        conversionActionId2Valuecombined.put(key, value);
                    }
                } else if (ConversionActionTypeEnum.shouldAvg(id2ConversionAction.get(key).getType())) {
                    if (!id2SumRateValue.containsKey(key)) {
                        id2SumRateValue.put(key, new ArrayList<>());
                    }
                    id2SumRateValue.get(key).add(value);
                }
            });
        });
        id2SumRateValue.forEach((key, value) -> {
            BigDecimal totalRate = new BigDecimal(0);
            for (BigDecimal bigDecimal : value) {
                totalRate = totalRate.add(bigDecimal);
            }
            conversionActionId2Valuecombined.put(key, totalRate.divide(new BigDecimal(value.size()), 4, RoundingMode.HALF_UP).movePointRight(2));
        });
        // This var maybe use a new name?
        return conversionActionId2Valuecombined;
    }

    private Set<Long> parseIdInString(String id) {
        if (Objects.nonNull(id) && NumberUtils.isCreatable(id)) {
            return Collections.singleton(Long.valueOf(id));
        }
        return Collections.emptySet();
    }

    @Override
    public void downloadData(HttpServletResponse httpServletResponse, PageDataReportByConditionsDTO dto, Long userId, RoleEnum currentRole) {
        setPageDataReportByConditionsDTOPageParam(dto);
        List<DataReportItemDTO> dataReportItemDTOS = pageByConditions(dto, userId, currentRole).getContent();
        List<DataReportItemFlatDTO> dataReportItemFlatDTOS = flatDataReportItemDTO(dataReportItemDTOS, dto.getTimeGranularityEnum());
        try {
            httpServletResponse.setContentType("application/vnd.ms-excel");
            httpServletResponse.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(getDataReportExcelName(currentRole)
                    + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"))
                    + ".xlsx", StandardCharsets.UTF_8);
            httpServletResponse.setHeader("Content-disposition", "attachment;filename=" + fileName);
            EasyExcel.write(httpServletResponse.getOutputStream(), DataReportItemFlatDTO.class)
                    .sheet(getDataReportExcelName(currentRole))
                    .excludeColumnFieldNames(
                            Stream.concat(
                                            Stream.concat(
                                                    DataReportItemFlatDTO.getIgnoreColNameSetByTime(dto.getTimeGranularityEnum()).stream(),
                                                    DataReportItemFlatDTO.getIgnoreColNameSetByRole(currentRole).stream()),
                                            getIgnoreConversion(dto.getConversionActionIdList()).stream())
                                    .collect(Collectors.toSet())
                    )
                    .doWrite(dataReportItemFlatDTOS);
        } catch (Exception e) {
            log.error("download excel error", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "download excel error");
        }
    }

    private void setPageDataReportByConditionsDTOPageParam(PageDataReportByConditionsDTO dto) {
        dto.setPageSize(0);
        dto.setPageSize(99999999);
    }

    private String getDataReportExcelName(RoleEnum roleEnum) {
        return switch (roleEnum) {
            case ADVISOR, SELLER, SELLER_LEADER -> "数据报表";
            case OPERATOR, OPERATOR_LEADER, BD, BD_LEADER, ADMIN -> "转化数据报表";
            //渠道流量运营无法下载
            case CHANNEL_OPERATOR -> StringUtils.EMPTY;
            case UNKNOWN -> StringUtils.EMPTY;
        };
    }

    private List<DataReportItemFlatDTO> flatDataReportItemDTO(List<DataReportItemDTO> dtoList, TimeGranularityEnum timeGranularityEnum) {
        List<DataReportItemFlatDTO> dataReportItemFlatDTOS = new ArrayList<>(dtoList.size());
        List<ConversionAction> allConversionActions = conversionActionRepository.findAll();
        Map<String, ConversionAction> key2ConversionAction = allConversionActions.stream().collect(Collectors.toMap(ConversionAction::getReflectField, Function.identity()));
        List<Field> allFieldNeedSet = Arrays.stream(
                        DataReportItemFlatDTO.class.getDeclaredFields())
                .filter((field) -> key2ConversionAction.containsKey(field.getName())
                ).toList();
        dtoList.forEach(item -> {
            DataReportItemFlatDTO dataReportItemFlatDTO = dataReportMapper.dataReportItemDTO2FlatDTO(item);
            // process conversion action.
            flatMapData2DtoField(item.getExtendConversionActionMap(), dataReportItemFlatDTO, allFieldNeedSet, key2ConversionAction);
            // 导出的excel文件设置有效转化数单元格格式
            dataReportItemFlatDTO.setIntegrationMethodFlatText(getIntegrationMethodFlatText(
                    item.getIntegrationMethodEnums(), item.getIntegrationMethodCustomText()
            ));
            dataReportItemFlatDTOS.add(dataReportItemFlatDTO);
        });
        return setEffectiveConversionActionCommentIfNeed(dataReportItemFlatDTOS, timeGranularityEnum);
    }

    private String getIntegrationMethodFlatText(Set<IntegrationMethodEnum> integrationMethodEnums, String integrationMethodCustomText) {
        return integrationMethodEnums.stream().map(integrationMethodEnum -> {
            if (IntegrationMethodEnum.CUSTOM.equals(integrationMethodEnum)) {
                return integrationMethodCustomText;
            } else {
                return integrationMethodEnum.getText();
            }
        }).collect(Collectors.joining(";\n"));
    }

    private List<DataReportItemFlatDTO> setEffectiveConversionActionCommentIfNeed(List<DataReportItemFlatDTO> dataReportItemFlatDTOs, TimeGranularityEnum timeGranularityEnum) {
        if (TimeGranularityEnum.DAY.equals(timeGranularityEnum)) {
            List<ConversionAction> allConversionActions = conversionActionRepository.findAll();
            Map<Long, ConversionAction> id2ConversionAction = allConversionActions.stream().collect(Collectors.toMap(ConversionAction::getId, Function.identity()));
            dataReportItemFlatDTOs.forEach(item -> {
                if (EFFECTIVE_CONVERSION_ACTION_ID_DEFAULT.equals(item.getEffectiveConversionActionId())) {
                    item.getActiveNum().setCommentData(getEffectiveConversionActionComment());
                } else {
                    ConversionAction conversionAction = id2ConversionAction.get(item.getEffectiveConversionActionId());
                    try {
                        PropertyDescriptor propertyDescriptor = new PropertyDescriptor(conversionAction.getReflectField(), DataReportItemFlatDTO.class);
                        WriteCellData invoke = (WriteCellData) propertyDescriptor.getReadMethod().invoke(item);
                        invoke.setCommentData(getEffectiveConversionActionComment());
                    } catch (Exception e) {
                        log.error("rendering data report item flat error", e);
                        throw new CustomException(ResponseType.SERVICE_ERROR);
                    }
                }
            });
        }
        return dataReportItemFlatDTOs;
    }

    private CommentData getEffectiveConversionActionComment() {
        CommentData commentData = new CommentData();
        commentData.setRichTextStringData(new RichTextStringData(CommonConstants.EFFECTIVE_CONVERSION_COL_COMMENT));
        return commentData;
    }

    private Set<String> getIgnoreConversion(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptySet();
        } else {
            return conversionActionRepository.findAll().stream().filter((item) -> !ids.contains(item.getId())).map(ConversionAction::getReflectField).collect(Collectors.toSet());
        }
    }

    @Override
    public List<ConversionActionDTO> getConversionActions(Integer filter) {
        List<ConversionAction> list = Collections.emptyList();
        if (filter == null) {
            list = conversionActionRepository.findAllByTypeInOrderByRankAsc(ConversionActionTypeEnum.EDITABLE_ENUM_LIST);
        } else {
            if (filter == 0) {
                list = conversionActionRepository.findAllByTypeInOrderByRankAsc(ConversionActionTypeEnum.EFFECTIVE_ENUM_LIST);
            } else if (filter == 1) {
                list = conversionActionRepository.findAllByOrderByRankAsc();
            } else if (filter == 2) {
                list = conversionActionRepository.findAllByTypeInOrderByRankAsc(List.of(NORMAL.getCode(), DYNAMIC_INQUIRE.getCode(), RATE_IN_DRUID.getCode()));
            }
        }
        return conversionActionMapeer.do2DTO(list);
    }

    @Override
    public Page<RoiReportItemDTO> getPagedRoiReport(PagedRoiReportByConditionsDTO dto,
                                                    RoleEnum roleEnum, Long userId) {
        List<DataReport> dataReports = getByConditions(
                dto.getCustomerName(), dto.getSellerName(), dto.getAdvisorName(), dto.getOperatorName(),
                dto.getTaskName(), dto.getTaskId(), dto.getBeginDate(), dto.getEndDate(),
                dto.getIntegrationMethodEnums(), userId, roleEnum, true,
                null, null, null, null, null
        );
        if (CollectionUtils.isEmpty(dataReports)) {
            return getEmptyRoiReportPage(dto.getPageNum(), dto.getPageSize(), 0);
        }
        Page<DataReportItemDTO> dataReportItemDTOS = renderDataReportsToDTO(dataReports, dto.getTimeGranularityEnum(),
                null, 0, 9999999, roleEnum, true);
        return renderRoiReport(dataReportItemDTOS, dto.getTimeGranularityEnum(), dto.getPageNum(), dto.getPageSize());
    }

    private Page<RoiReportItemDTO> renderRoiReport(Page<DataReportItemDTO> dataReportItemDTOS, TimeGranularityEnum timeGranularityEnum, Integer pageNum, Integer pageSize) {
        List<DataReportItemDTO> content = dataReportItemDTOS.getContent();
        Map<Long, CustomerInfoDTO> id2CustomerInfoDTO = customerMapper.do2InfoDTO(customerRepository.findAllById(
                        content.stream()
                                .map(DataReportItemDTO::getCustomerId)
                                .collect(Collectors.toSet()))
                ).stream()
                .collect(Collectors.toMap(CustomerInfoDTO::getId, Function.identity()));
        List<DataReportItemDTO> combinedReportItemDTOS = combineRoiDataByTaskId(content, timeGranularityEnum);
        sortDataReportItems(combinedReportItemDTOS, timeGranularityEnum);
        List<List<DataReportItemDTO>> partition = ListUtils.partition(combinedReportItemDTOS, pageSize);
        if (partition.size() < pageNum) {
            return getEmptyRoiReportPage(pageNum, pageSize, combinedReportItemDTOS.size());
        }
        List<Future<RoiReportItemDTO>> futures = new ArrayList<>(content.size());
        for (DataReportItemDTO dataReportItemDTO : partition.get(pageNum)) {
            Future<RoiReportItemDTO> submit = THREAD_POOL_EXECUTOR.submit(() -> {
                Map<LocalDate, List<SdkStatFlatQueryResult>> druidResult =
                        queryDruidData(id2CustomerInfoDTO.get(dataReportItemDTO.getCustomerId()),
                                timeGranularityEnum,
                                dataReportItemDTO.getQueryDate());
                return buildRoiReportItemDTO(dataReportItemDTO, druidResult);
            });
            futures.add(submit);
        }
        List<RoiReportItemDTO> roiReportItemDTOS = new ArrayList<>(content.size());
        for (Future<RoiReportItemDTO> future : futures) {
            try {
                roiReportItemDTOS.add(future.get());
            } catch (Exception e) {
                log.error("rending roi report error", e);
                throw new CustomException(ResponseType.SERVICE_ERROR);
            }
        }
        return new PageImpl<>(roiReportItemDTOS, PageRequest.of(pageNum, pageSize, dataReportItemDTOS.getSort()), combinedReportItemDTOS.size());
    }

    private List<DataReportItemDTO> combineRoiDataByTaskId(List<DataReportItemDTO> dataReportItemDTOS, TimeGranularityEnum timeGranularityEnum) {
        Map<Long, Map<LocalDate, List<DataReportItemDTO>>> collect = dataReportItemDTOS.stream().collect(Collectors.groupingBy(DataReportItemDTO::getTaskId, Collectors.groupingBy(DataReportItemDTO::getDate)));
        List<DataReportItemDTO> product = new ArrayList<>(collect.size());
        collect.forEach((taskId, localDateId2DataReportItemMap) -> {
            if (TimeGranularityEnum.ALL.equals(timeGranularityEnum)) {
                List<DataReportItemDTO> combineAllByTask = localDateId2DataReportItemMap.values().stream().flatMap(List::stream).toList();
                product.add(combineDataReportItemList4Roi(combineAllByTask));
            } else {
                localDateId2DataReportItemMap.forEach((localDate, dataReportItemList) -> {
                    product.add(combineDataReportItemList4Roi(dataReportItemList));
                });
            }
        });
        return product;
    }

    private DataReportItemDTO combineDataReportItemList4Roi(List<DataReportItemDTO> dataReportItemDTOS) {
        DataReportItemDTO dataReportItemDTO = dataReportItemDTOS.stream().min(Comparator.comparing(DataReportItemDTO::getDate)).orElseThrow(() -> new CustomException(ResponseType.SERVICE_ERROR));
        dataReportItemDTO.setChannelCost(dataReportItemDTOS.stream().mapToLong(DataReportItemDTO::getChannelCost).sum());
        dataReportItemDTO.setTotalEffectiveConversionNum(dataReportItemDTOS.stream().mapToLong(DataReportItemDTO::getTotalEffectiveConversionNum).sum());
        dataReportItemDTO.setQueryDate(dataReportItemDTOS.stream().map(DataReportItemDTO::getQueryDate).flatMap(List::stream).distinct().collect(Collectors.toList()));
        return dataReportItemDTO;
    }

    /**
     * @return Date -> QueryResult
     */
    private Map<LocalDate, List<SdkStatFlatQueryResult>> queryDruidData(CustomerInfoDTO customerInfoDTO,
                                                                        TimeGranularityEnum timeGranularityEnum,
                                                                        List<LocalDate> localDates) {

        Set<String> zhixuanUsernames = customerInfoDTO.getZhixuanUsernames();
        Set<String> zhixuanSlotIds = customerInfoDTO.getZhixuanSlotIds();
        if (CollectionUtils.isEmpty(zhixuanUsernames) || CollectionUtils.isEmpty(zhixuanSlotIds)) {
            return Collections.emptyMap();
        }
        List<Sponsor> sponsors = sponsorRepository.findAllByUserNameIn(zhixuanUsernames);
        Set<Long> sponsorIds = sponsors.stream().map(Sponsor::getSponsorId).collect(Collectors.toSet());
        DruidQuery druidQuery = DruidQueryTool.clickAndChargeQueryBuilder(
                sponsorIds,
                zhixuanSlotIds,
                timeGranularityEnum,
                localDate2LocalDateTime(localDates)
        );
        try {
            return dataReportMapper.sdkStatQueryResult2Flat(druidHttpClient.query(druidQuery, SdkStatQueryResult.class)).stream().collect(
                    Collectors.groupingBy(SdkStatFlatQueryResult::getLocalDate)
            );
        } catch (Exception e) {
            log.error("druid query failed", e);
            throw new RuntimeException(e);
        }

    }

    private List<LocalDateTime> localDate2LocalDateTime(List<LocalDate> localDates) {
        List<LocalDateTime> localDateTimes = new ArrayList<>(localDates.size());
        for (LocalDate localDate : localDates) {
            localDateTimes.add(localDate.atTime(LocalTime.MIN));
        }
        return localDateTimes;
    }

    private RoiReportItemDTO buildRoiReportItemDTO(DataReportItemDTO dataReportItemDTO,
                                                   Map<LocalDate, List<SdkStatFlatQueryResult>> druidData) {
        RoiReportItemDTO roiReportItemDTO = dataReportMapper.dataReportItemDTO2RoiReportItem(dataReportItemDTO);
        if (MapUtils.isNotEmpty(druidData)) {
            List<SdkStatFlatQueryResult> sdkStatFlatQueryResult = druidData.
                    getOrDefault(roiReportItemDTO.getDate(), Collections.emptyList());
            long totalCharge = sdkStatFlatQueryResult.stream().mapToLong(SdkStatFlatQueryResult::getCharge).sum();
            long totalClick = sdkStatFlatQueryResult.stream().mapToLong(SdkStatFlatQueryResult::getClick).sum();
            roiReportItemDTO.setZhixuanIncome(getZhixuanIncome(totalCharge, roiReportItemDTO.getRebate()));
            roiReportItemDTO.setFlowCost(new BigDecimal(totalClick).multiply(FLOW_COST_RATE).movePointRight(2).longValue());
            roiReportItemDTO.setRoi(getRoi(getZhixuanIncome(totalCharge, roiReportItemDTO.getRebate()), roiReportItemDTO.getFlowCost(), roiReportItemDTO.getChannelCost()));
        }
        return roiReportItemDTO;
    }

    private BigDecimal getRoi(Long zhixuanIncome, Long flowCost, Long channelCost) {
        return new BigDecimal(zhixuanIncome).divide(new BigDecimal(flowCost).add(new BigDecimal(channelCost)), 2, RoundingMode.HALF_EVEN);
    }

    /**
     * （每日与该任务中客户绑定的所有智选账户中对应广告位的消费*（1/（1+返点）））之和
     */
    private long getZhixuanIncome(Long charge, BigDecimal rebate) {
        return new BigDecimal(charge).divide(rebate.add(new BigDecimal(1)), RoundingMode.CEILING).longValue();
    }

    private Page<RoiReportItemDTO> getEmptyRoiReportPage(int pageNum, int pageSize, int total) {
        return new PageImpl<>(Collections.emptyList(), PageRequest.of(pageNum, pageSize), total);
    }


    @Override
    public void downloadRoiReport(HttpServletResponse httpServletResponse, PagedRoiReportByConditionsDTO dto, RoleEnum roleEnum, Long userId) {
        setPagedRoiReportByConditionsDTOPageParam(dto);
        List<RoiReportItemDTO> pagedRoiReport = getPagedRoiReport(dto, roleEnum, userId).getContent();
        pagedRoiReport.forEach(item -> {
            item.setIntegrationMethodFlatText(getIntegrationMethodFlatText(item.getIntegrationMethodEnums(), item.getIntegrationMethodCustomText()));
        });
        try {
            httpServletResponse.setContentType("application/vnd.ms-excel");
            httpServletResponse.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("ROI报表"
                    + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"))
                    + ".xlsx", StandardCharsets.UTF_8);
            httpServletResponse.setHeader("Content-disposition", "attachment;filename=" + fileName);
            EasyExcel.write(httpServletResponse.getOutputStream(), RoiReportItemDTO.class)
                    .sheet("ROI报表")
                    .excludeColumnFieldNames(
                            Stream.concat(
                                    RoiReportItemDTO.getIgnoreColNameSetByTime(dto.getTimeGranularityEnum()).stream(),
                                    RoiReportItemDTO.getIgnoreColNameSetByRole(roleEnum).stream()
                            ).collect(Collectors.toSet())
                    )
                    .doWrite(pagedRoiReport);
        } catch (Exception e) {
            log.error("download excel error", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "download excel error");
        }
    }

    @Override
    public List<RoiReportItemDTO> getRoiReportForCheck(LocalDate beginDate, LocalDate endDate, TimeGranularityEnum timeGranularityEnum) {
        List<DataReport> dataReports = dataReportRepository.getAllByDateBetween(beginDate, endDate);
        if (CollectionUtils.isEmpty(dataReports)) {
            return Collections.emptyList();
        }
        injectDruidData(dataReports);
        Page<DataReportItemDTO> dataReportItemDTOS = renderDataReportsToDTO(dataReports, timeGranularityEnum,
                null, 0, 9999999, RoleEnum.UNKNOWN, true);
        return renderRoiReport(dataReportItemDTOS, timeGranularityEnum, 0, 9999999).getContent();
    }

    private void setPagedRoiReportByConditionsDTOPageParam(PagedRoiReportByConditionsDTO dto) {
        dto.setPageNum(0);
        dto.setPageSize(99999999);
    }

    @Override
    public Page<ChannelReportItemDTO> getPagedChannelReport(PageChannelReportByConditionsDTO dto, RoleEnum currentRole) {
        Page<ChannelReportItemDTO> emptyResult = new PageImpl<>(Collections.emptyList(), PageRequest.of(dto.getPageNum(), dto.getPageSize()), 0);
        // 筛选任务（客户）
        List<Task> tasks = Collections.emptyList();
        Set<Long> taskIds = Collections.emptySet();
        if (dto.needFilterTaskId()) {
            tasks = filterTasksForChannelReport(dto.getOsEnums(), dto.getPromotionCategoryEnums(), dto.getProductCategoryIds(), dto.getCustomerName());
            if (CollectionUtils.isEmpty(tasks)) {
                return emptyResult;
            }
            taskIds = tasks.stream().map(Task::getId).collect(Collectors.toSet());
        }


        // 筛选渠道
        List<Channel> channels = null;
        Set<Long> channelIds = new HashSet<>();
        if (StringUtils.isNotBlank(dto.getChannelName())) {
            String channelName = dto.getChannelName();
            channels = channelRepository.findAllByNameContaining(channelName);
            channelIds = channels.stream().map(Channel::getId).collect(Collectors.toSet());
        }
        if (StringUtils.isNotBlank(dto.getChannelCode())) {
            String channelCode = dto.getChannelCode();
            channels = channelRepository.findAllByCodeContaining(channelCode);
            channelIds.addAll(channels.stream().map(Channel::getId).collect(Collectors.toSet()));
        }
        if (!StringUtils.isAllBlank(dto.getChannelName(), dto.getChannelCode()) && channelIds.isEmpty()) {
            return emptyResult;
        }
        List<DataReport> dataReports = dataReportRepository.getByConditions(taskIds, null, channelIds, null, dto.getBeginDate(), dto.getEndDate());
        if (CollectionUtils.isEmpty(dataReports)) {
            return emptyResult;
        }
        injectDruidData(dataReports);
        return renderChannelReport(dataReports, tasks, channels, dto.getConversionActionIdList(), dto.getTimeGranularityEnum(),
                dto.getGroupByOs(), dto.getGroupByPromotionCategory(), dto.getGroupByProductCategory(), dto.getGroupByCustomerName(),
                dto.getPageNum(), dto.getPageSize(), dto.getGroupByActivityId(), currentRole);
    }

    private List<Task> filterTasksForChannelReport(@Nullable Set<OsEnum> osEnums, @Nullable Set<PromotionCategoryEnum> promotionCategoryEnums,
                                                   @Nullable Set<Integer> productCategoryIds, @Nullable String customerName) {
        Set<Byte> osSet = CollectionUtils.isEmpty(osEnums) ? Collections.emptySet()
                : osEnums.stream().map(OsEnum::idByte).collect(Collectors.toSet());
        Set<Integer> promotionCategoryIds = CollectionUtils.isEmpty(promotionCategoryEnums) ? Collections.emptySet()
                : promotionCategoryEnums.stream().map(PromotionCategoryEnum::getId).collect(Collectors.toSet());
        Set<String> sponsorUserNames = Collections.emptySet();
        if (Objects.nonNull(productCategoryIds)) {
            Set<Integer> subCategoryIds = categoryService.getSubCategoryIds(productCategoryIds);
            List<Sponsor> sponsors = sponsorService.getByCategoryIds(subCategoryIds);
            if (CollectionUtils.isEmpty(sponsors)) {
                return Collections.emptyList();
            } else {
                sponsorUserNames = sponsors.stream().map(Sponsor::getUserName).collect(Collectors.toSet());
            }
        }
        Set<Long> customerIds = null;
        if (StringUtils.isNotBlank(customerName)) {
            List<Customer> customers = customerRepository.getAllByNameContaining(customerName);
            if (CollectionUtils.isEmpty(customers)) {
                return Collections.emptyList();
            } else {
                customerIds = customers.stream().map(Customer::getId).collect(Collectors.toSet());
            }
        }
        List<Task> allForChannelReport = taskRepository.findAllForChannelReport(osSet, promotionCategoryIds, customerIds);
        if (CollectionUtils.isNotEmpty(sponsorUserNames)) {
            final Set<String> sponsorUserNamesFinal = sponsorUserNames;
            allForChannelReport = allForChannelReport.stream()
                    .filter(item -> !Collections.disjoint(item.getZhixuanUsername(), sponsorUserNamesFinal)).collect(Collectors.toList());
        }
        return allForChannelReport;
    }

    public Page<ChannelReportItemDTO> renderChannelReport(List<DataReport> dataReports, List<Task> tasks, List<Channel> channels,
                                                          Set<Long> conversionActionIds, TimeGranularityEnum timeGranularityEnum,
                                                          Boolean groupByOs, Boolean groupByPromotionCategory,
                                                          Boolean groupByProductCategory, Boolean groupByCustomerName,
                                                          Integer pageNum, Integer pageSize, Boolean groupByActivityId, RoleEnum currentRole) {
        // 查询所需的任务、客户、渠道、BD用户信息
        if (CollectionUtils.isEmpty(tasks)) {
            tasks = taskRepository.findAllById(dataReports.stream().map(DataReport::getTaskId).toList());
        }
        Map<String, CategoryDTO> sponsorUsername2Category = sponsorService.getSponsorName2CategoryMap(
                tasks.stream()
                        .map(Task::getZhixuanUsername)
                        .flatMap(Set::stream)
                        .collect(Collectors.toSet())
        );
        Map<Long, Task> taskMap = tasks.stream().collect(Collectors.toMap(Task::getId, task -> task));
        Map<Long, Customer> customerMap = customerRepository.findAllById(tasks.stream().map(Task::getCustomerId).toList())
                .stream().collect(Collectors.toMap(Customer::getId, customer -> customer));
        if (CollectionUtils.isEmpty(channels)) {
            channels = channelRepository.findAllById(dataReports.stream().map(DataReport::getChannelId).toList());
        }
        Map<Long, Channel> channelMap = channels.stream().collect(Collectors.toMap(Channel::getId, channel -> channel));
        Map<Long, User> bdUserMap = userRoleRelationService.getByUserRoleRelationId(channels.stream().map(Channel::getBdUserId).toList())
                .stream().collect(Collectors.toMap(User::getUserRoleRelationId, item -> item));
        // DataReport 一对一映射 ChannelReportItemDTO
        List<ChannelReportItemDTO> channelReportItemDTOList = dataReports.stream().map(dataReport -> {
            Task task = taskMap.get(dataReport.getTaskId());
            Customer customer = customerMap.get(task.getCustomerId());
            Channel channel = channelMap.get(dataReport.getChannelId());
            User user = bdUserMap.get(channel.getBdUserId());
            String productCategoryName = task.getZhixuanUsername().stream()
                    .filter(sponsorUsername2Category::containsKey)
                    .map(sponsorUsername2Category::get)
                    .map(CategoryDTO::getName)
                    .collect(Collectors.joining(","));
            ChannelReportItemDTO dto = new ChannelReportItemDTO(
                    dataReport, task, customer,
                    channel, user, timeGranularityEnum,
                    productCategoryName, currentRole
            );
            dto.concatGroupByKey(groupByOs, groupByPromotionCategory, groupByProductCategory, groupByCustomerName, groupByActivityId);
            return dto;
        }).toList();
        // 聚合组装数据，排序
        Map<String, List<ChannelReportItemDTO>> dtoGroupMap = channelReportItemDTOList.stream()
                .collect(Collectors.groupingBy(ChannelReportItemDTO::getGroupByKey));
        Map<Long, ConversionAction> id2ConversionAction = conversionActionRepository.findAll()
                .stream()
                .collect(Collectors.toMap(ConversionAction::getId, Function.identity()
                ));
        List<ChannelReportItemDTO> dtoCombinedList = dtoGroupMap.values().stream()
                .map(dtoList -> combineChannelReportItemDTO(dtoList, id2ConversionAction,
                        groupByOs, groupByPromotionCategory, groupByProductCategory, groupByCustomerName, groupByActivityId))
                .sorted().toList();
        // 分页
        List<ChannelReportItemDTO> content = dtoCombinedList.stream()
                .skip((long) pageNum * pageSize).limit(pageSize).collect(Collectors.toList());
        // 转化行为
        eraseConversionActionsForChannelReport(content, conversionActionIds);
        return new PageImpl<>(content, PageRequest.of(pageNum, pageSize), dtoCombinedList.size());
    }

    private ChannelReportItemDTO combineChannelReportItemDTO(List<ChannelReportItemDTO> dtoList, Map<Long, ConversionAction> id2ConversionAction,
                                                             Boolean groupByOs, Boolean groupByPromotionCategory,
                                                             Boolean groupByProductCategory, Boolean groupByCustomerName, Boolean groupByActivityId) {
        ChannelReportItemDTO dto = dtoList.get(0);
        if (!groupByOs) {
            dto.setOs(OsEnum.DEFAULT);
        }
        if (!groupByPromotionCategory) {
            dto.setPromotionCategoryEnum(PromotionCategoryEnum.DEFAULT);
        }
        if (!groupByProductCategory) {
            dto.setProductCategoryName("");
        }
        if (!groupByCustomerName) {
            dto.setCustomerName("");
        }
        if (!groupByActivityId) {
            dto.setActivityId(-1L);
        }
        dto.setExtendConversionActionMap(sumConversionActionByIdForChannelReport(dtoList, id2ConversionAction));
        if (dtoList.size() > 1) {
            dto.setChannelWordDayNum(dtoList.stream().map(ChannelReportItemDTO::getDate).distinct().toList().size());
            dto.setActiveNum(dtoList.stream().mapToLong(ChannelReportItemDTO::getActiveNum).sum());
        }
        dto.setDate(dto.getFirstDayOfGroup());
        return dto;
    }


    @Override
    public void downloadChannelReport(HttpServletResponse httpServletResponse, PageChannelReportByConditionsDTO dto, RoleEnum roleEnum) {
        dto.setPageNum(0);
        dto.setPageSize(99999999);
        List<ChannelReportItemDTO> dtoList = getPagedChannelReport(dto, roleEnum).getContent();
        List<ChannelReportItemFlatDTO> flatDTOList = flatChannelReportItemDTO(dtoList);
        try {
            httpServletResponse.setContentType("application/vnd.ms-excel");
            httpServletResponse.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("渠道数据报表"
                    + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"))
                    + ".xlsx", StandardCharsets.UTF_8);
            httpServletResponse.setHeader("Content-disposition", "attachment;filename=" + fileName);
            EasyExcel.write(httpServletResponse.getOutputStream(), ChannelReportItemFlatDTO.class)
                    .sheet("渠道数据报表")
                    .excludeColumnFieldNames(
                            Stream.concat(
                                    ChannelReportItemFlatDTO.getIgnoreColNameSet(dto.getGroupByOs(), dto.getGroupByPromotionCategory(),
                                            dto.getGroupByProductCategory(), dto.getGroupByCustomerName(), dto.getTimeGranularityEnum(), dto.getGroupByActivityId(), roleEnum).stream(),
                                    getIgnoreConversion(dto.getConversionActionIdList()).stream()
                            ).collect(Collectors.toSet())
                    ).doWrite(flatDTOList);
        } catch (Exception e) {
            log.error("download excel error", e);
            throw new CustomException(ResponseType.SERVICE_ERROR, "download excel error");
        }
    }

    public List<ChannelReportItemFlatDTO> flatChannelReportItemDTO(List<ChannelReportItemDTO> dtoList) {
        List<ChannelReportItemFlatDTO> flatDTOList = new ArrayList<>(dtoList.size());
        List<ConversionAction> allConversionActions = conversionActionRepository.findAll();
        Map<String, ConversionAction> key2ConversionAction = allConversionActions.stream().collect(Collectors.toMap(ConversionAction::getReflectField, Function.identity()));
        List<Field> allFieldNeedSet = Arrays.stream(
                        ChannelReportItemFlatDTO.class.getDeclaredFields())
                .filter((field) -> key2ConversionAction.containsKey(field.getName())
                ).toList();
        dtoList.forEach(item -> {
            ChannelReportItemFlatDTO flatDTO = dataReportMapper.channelReportItemDTO2FlatDTO(item);
            flatMapData2DtoField(item.getExtendConversionActionMap(), flatDTO, allFieldNeedSet, key2ConversionAction);
            flatDTOList.add(flatDTO);
        });
        return flatDTOList;
    }

    public <T> void flatMapData2DtoField(Map<Long, BigDecimal> extendConversionActionMap, T flatDTO,
                                         List<Field> allFieldNeedSet, Map<String, ConversionAction> key2ConversionAction) {
        allFieldNeedSet.forEach(field -> {
            try {
                PropertyDescriptor propertyDescriptor = new PropertyDescriptor(field.getName(), flatDTO.getClass());
                BigDecimal conversionActionValue = extendConversionActionMap.getOrDefault(key2ConversionAction.get(field.getName()).getId(), null);
                String stringValue;
                if (Objects.isNull(conversionActionValue)) {
                    stringValue = "-";
                } else {
                    stringValue = conversionActionValue.toString();
                    if (ConversionActionTypeEnum.isPercentage(key2ConversionAction.get(field.getName()).getType())) {
                        stringValue = stringValue + "%";
                    }
                }
                WriteCellData<String> cellValue = new WriteCellData<>(stringValue);
                propertyDescriptor.getWriteMethod().invoke(flatDTO, cellValue);
            } catch (Exception e) {
                log.error("flat DataReportItemDTO error", e);
                throw new CustomException(ResponseType.SERVICE_ERROR);
            }
        });
    }

    @Override
    public void downloadFeedbackValidConvertNumBatchTemplate(HttpServletResponse httpServletResponse) throws Exception {
        String fileName = "有效转化数批量模板.xlsx";
        String filePath = "/template/" + fileName;
        InputStream is;
        is = this.getClass().getResourceAsStream(filePath);
        if (is == null) {
            throw new CustomException(SERVICE_ERROR);
        }
        httpServletResponse.setContentType("application/vnd.ms-excel");
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        FileCopyUtils.copy(is, httpServletResponse.getOutputStream());
    }

    @Override
    public List<DataViewUploadResBatchDTO> uploadFeedbackValidConvertNumBatch(MultipartFile file, Long operator) throws Exception {

        List<UploadFeedbackValidConvertNumBatchDTO> dtos = UploadUtil.readUploadExcel(file, UploadFeedbackValidConvertNumBatchDTO.class);
        Set<String> taskNames = dtos.stream().map(UploadFeedbackValidConvertNumBatchDTO::getTaskName).collect(Collectors.toSet());
        List<Task> taskList = taskRepository.findAllByNameIn(taskNames);
        Map<String, Task> name2Task = taskList.stream().collect(Collectors.toMap(Task::getName, Function.identity()));
        Set<Long> taskIds = taskList.stream().map(Task::getId).collect(Collectors.toSet());
        List<SubTask> subTaskList = subTaskRepository.findAllByTaskIdIn(taskIds);
        Map<String, SubTask> name2SubTask = subTaskList.stream().collect(Collectors.toMap(SubTask::getName, Function.identity()));

        Set<Long> customerIds = taskList.stream().map(Task::getCustomerId).collect(Collectors.toSet());
        List<Customer> customers = customerRepository.findAllById(customerIds);
        Map<Long, Customer> id2Customer = customers.stream().collect(Collectors.toMap(Customer::getId, Function.identity()));

        Map<String, List<UploadFeedbackValidConvertNumBatchDTO>> taskName2Dtos = dtos.stream()
                .collect(Collectors.groupingBy(UploadFeedbackValidConvertNumBatchDTO::getTaskName));

        List<DataViewUploadResBatchDTO> dataViewUploadResBatchDTOS = new ArrayList<>(taskName2Dtos.size());
        List<DataReport> dataReportsWillSave = new ArrayList<>(dtos.size());
        for (Map.Entry<String, List<UploadFeedbackValidConvertNumBatchDTO>> entry : taskName2Dtos.entrySet()) {
            String taskName = entry.getKey();
            Task task = name2Task.get(taskName);
            if (Objects.isNull(task)) {
                dataViewUploadResBatchDTOS.add(getEntireTaskFailingMessage(entry.getValue(), taskName, "父任务不存在"));
                continue;
            }
            Customer customer = id2Customer.get(task.getCustomerId());
            if (ObjectUtils.notEqual(customer.getOperatorUserId(), operator)) {
                dataViewUploadResBatchDTOS.add(getEntireTaskFailingMessage(entry.getValue(), taskName, "当前任务绑定的客户不属于当前运营"));
                continue;
            }
            DataViewUploadResBatchDTO uploadResBatchDTO = new DataViewUploadResBatchDTO(taskName, (long) entry.getValue().size(), 0L, new ArrayList<>());
            Map<String, List<UploadFeedbackValidConvertNumBatchDTO>> subTaskName2Dtos = entry.getValue()
                    .stream()
                    .collect(Collectors.groupingBy(UploadFeedbackValidConvertNumBatchDTO::getSubTaskName));
            subTaskName2Dtos.forEach((k, v) -> {
                SubTask subTask = name2SubTask.get(k);
                if (Objects.isNull(subTask) || !Objects.equals(subTask.getTaskId(), task.getId())) {
                    uploadResBatchDTO.getSubTaskResList().add(getEntireSubTaskFailingMessage(v, k));
                    return;
                }
                List<DataReport> allBySubTaskIdAndDateIn = dataReportRepository.getAllBySubTaskIdAndDateIn(
                        subTask.getId(), v
                                .stream()
                                .map(UploadFeedbackValidConvertNumBatchDTO::getDate)
                                .collect(Collectors.toList())
                );
                Map<LocalDate, DataReport> date2Report = allBySubTaskIdAndDateIn.stream().collect(Collectors.toMap(DataReport::getDate, Function.identity()));
                DataViewUploadResDTO dataViewUploadResDTO = new DataViewUploadResDTO(k, v.size(), 0, new ArrayList<>());
                for (UploadFeedbackValidConvertNumBatchDTO uploadFeedbackValidConvertNumBatchDTO : v) {
                    if (uploadFeedbackValidConvertNumBatchDTO.getFeedbackValidConvertNum() < 0) {
                        dataViewUploadResDTO.getFailInfos().add(new DataViewUploadFailInfoDTO(
                                uploadFeedbackValidConvertNumBatchDTO.getDateStr(),
                                "有效转化数不能小于0"));
                        continue;
                    }
                    if (uploadFeedbackValidConvertNumBatchDTO.getDate().isAfter(LocalDate.now())) {
                        dataViewUploadResDTO.getFailInfos().add(new DataViewUploadFailInfoDTO(
                                uploadFeedbackValidConvertNumBatchDTO.getDateStr(),
                                "该日期在未来，不可录入未来日期的数据"));
                        continue;
                    }
                    if (!StringUtils.equals(uploadFeedbackValidConvertNumBatchDTO.getCustomerName(), customer.getName())) {
                        dataViewUploadResDTO.getFailInfos().add(new DataViewUploadFailInfoDTO(
                                uploadFeedbackValidConvertNumBatchDTO.getDateStr(),
                                "客户名字不匹配"));
                        continue;
                    }
                    if (!date2Report.containsKey(uploadFeedbackValidConvertNumBatchDTO.getDate())) {
                        dataViewUploadResDTO.getFailInfos().add(new DataViewUploadFailInfoDTO(
                                uploadFeedbackValidConvertNumBatchDTO.getDateStr(),
                                "当前日期没有转化数据，无法上传有效转化数"
                        ));
                        continue;
                    }
                    DataReport dataReport = date2Report.get(uploadFeedbackValidConvertNumBatchDTO.getDate());
                    dataReport.setFeedbackValidConvertNum(BigDecimal.valueOf(uploadFeedbackValidConvertNumBatchDTO.getFeedbackValidConvertNum()));
                    dataReportsWillSave.add(dataReport);
                    dataViewUploadResDTO.setSuccessNum(dataViewUploadResDTO.getSuccessNum() + 1);
                }
                uploadResBatchDTO.setSuccessNum(uploadResBatchDTO.getSuccessNum() + dataViewUploadResDTO.getSuccessNum());
                uploadResBatchDTO.getSubTaskResList().add(dataViewUploadResDTO);
            });
            dataViewUploadResBatchDTOS.add(uploadResBatchDTO);
        }
        dataReportRepository.saveAll(dataReportsWillSave);
        return dataViewUploadResBatchDTOS;
    }


    private DataViewUploadResDTO getEntireSubTaskFailingMessage(List<UploadFeedbackValidConvertNumBatchDTO> dtos, String subTaskName) {
        return new DataViewUploadResDTO(subTaskName,
                dtos.size(),
                0,
                dtos.stream()
                        .map(item -> new DataViewUploadFailInfoDTO(item.getDateStr(), "子任务不存在或与父任务不匹配"))
                        .collect(Collectors.toList())
        );
    }

    private DataViewUploadResBatchDTO getEntireTaskFailingMessage(List<UploadFeedbackValidConvertNumBatchDTO> uploadFeedbackValidConvertNumBatchDTOs, String taskName, String failInfo) {
        DataViewUploadResBatchDTO dataViewUploadResBatchDTO = new DataViewUploadResBatchDTO(taskName,
                (long) uploadFeedbackValidConvertNumBatchDTOs.size(),
                0L,
                new ArrayList<>());
        Map<String, List<UploadFeedbackValidConvertNumBatchDTO>> subTaskName2Dtos =
                uploadFeedbackValidConvertNumBatchDTOs.stream()
                        .collect(Collectors.groupingBy(UploadFeedbackValidConvertNumBatchDTO::getSubTaskName));
        subTaskName2Dtos.forEach((k, v) -> {
            dataViewUploadResBatchDTO.getSubTaskResList().add(
                    new DataViewUploadResDTO(k, v.size(), 0,
                            v.stream().map(item -> new DataViewUploadFailInfoDTO(item.getDateStr(), failInfo))
                                    .collect(Collectors.toList()))
            );
        });
        return dataViewUploadResBatchDTO;
    }
}