package com.youdao.ead.pacioli.service;

import com.youdao.ead.pacioli.bo.LoginUser;
import com.youdao.ead.pacioli.dto.login.PasswordRecoveryDTO;
import com.youdao.ead.pacioli.dto.user.PagedUserInfoDTO;
import com.youdao.ead.pacioli.dto.user.QueryReserveRelationUsersReq;
import com.youdao.ead.pacioli.dto.user.QueryReserveRelationUsersRes;
import com.youdao.ead.pacioli.dto.user.SellerInfoDTO;
import com.youdao.ead.pacioli.dto.user.SponsorDTO;
import com.youdao.ead.pacioli.dto.user.UserCreateDTO;
import com.youdao.ead.pacioli.dto.user.UserInfoDTO;
import com.youdao.ead.pacioli.dto.user.UserPageByConditionsDTO;
import com.youdao.ead.pacioli.dto.user.UserUpdateDTO;
import com.youdao.ead.pacioli.entity.eadb1.Sponsor;
import com.youdao.ead.pacioli.enums.RoleEnum;
import com.youdao.ead.pacioli.enums.UserStateEnum;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Li
 */
public interface UserService {

    /**
     * 仅用于开发环境
     */
    String loginById(Long id);

    void recoveryPassword(PasswordRecoveryDTO passwordRecoveryDTO);

    void sendSmsCaptcha(String phoneCipher);

    void verifySmsCaptcha(String phoneCipher, String verifyCode);

    Page<PagedUserInfoDTO> getUserPageByConditions(UserPageByConditionsDTO userPageByConditionsDTO);

    void updateUserState(Long userId, UserStateEnum userStateEnum);

    void deleteUser(Long userId);

    UserInfoDTO getUserDetail(Long userId);

    /**
     * 这个sellerId是用户角色id
     */
    List<SellerInfoDTO> getAdvisorListBySellerId(Long sellerId);

    Long createUser(UserCreateDTO userCreateDTO, Long adminUserRoleRelationId);

    Long updateUser(Long userId, UserUpdateDTO userUpdateDTO, Long adminUserRoleRelationId);

    void updateUserRelation(Long userRoleRelationId, RoleEnum roleEnum, UserUpdateDTO userUpdateDTO, Long adminUserRoleRelationId);

    boolean isNameExist(String name);

    boolean isPhoneExist(String phoneCipher);

    boolean isCorpEmailExist(String corpEmail);

    Set<Sponsor> getRelateSponsor(Long userRoleRelationId, RoleEnum role);

    List<RoleEnum> getRoles(String phoneCipher, String passwordCipher);

    String loginWithRole(String phoneCipher, String passwordCipher, RoleEnum role);

    Long addRole(Long userId, UserUpdateDTO userUpdateDTO, Long adminUserRoleRelationId);

    QueryReserveRelationUsersRes reserveRelationUsersBatch(QueryReserveRelationUsersReq queryReserveRelationUsersReq);

    /**
     * 这个sellerId是用户角色id
     */
    List<SponsorDTO> getSponsorListBySellerId(Long taskId,Long sellerId, String condition);
}
