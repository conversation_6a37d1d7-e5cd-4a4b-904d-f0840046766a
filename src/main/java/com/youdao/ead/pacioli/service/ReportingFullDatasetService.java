package com.youdao.ead.pacioli.service;

import com.youdao.ead.pacioli.bo.LoginUser;
import com.youdao.ead.pacioli.dto.channeldata.ChannelDataQueryDTO;
import com.youdao.ead.pacioli.dto.channeldata.ChannelDataResultDTO;
import com.youdao.ead.pacioli.dto.channeldata.ChannelDataSponsorQueryDTO;
import com.youdao.ead.pacioli.dto.channeldata.ChannelDataSponsorResultDTO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface ReportingFullDatasetService {

    Page<ChannelDataResultDTO>  getChannelDataList(ChannelDataQueryDTO dto, LoginUser loginUser);

    List<ChannelDataSponsorResultDTO> getChannelDataSponsorList(ChannelDataSponsorQueryDTO dto, LoginUser loginUser);
}
