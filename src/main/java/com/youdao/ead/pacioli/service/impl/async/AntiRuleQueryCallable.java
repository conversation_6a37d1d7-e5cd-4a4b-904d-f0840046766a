package com.youdao.ead.pacioli.service.impl.async;

import com.youdao.ead.pacioli.core.druid.DruidHttpClient;
import com.youdao.ead.pacioli.core.druid.FlowDashboardQueryResult;
import com.youdao.ead.pacioli.dto.antifraud.FlowDashboardQueryDTO;
import com.youdao.ead.pacioli.dto.antifraud.FlowDashboardResultDTO;
import com.youdao.ead.pacioli.enums.antifraud.MetricsQueryIdEnum;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 点击的反作弊指标查询任务
 * <AUTHOR>
 * @create 2024-12-03 17:13
 */
public class AntiRuleQueryCallable extends AbstractQueryCallable {

    public AntiRuleQueryCallable(ConcurrentHashMap<String, FlowDashboardResultDTO> publicSummaryMap, FlowDashboardQueryDTO flowDashboardQueryDTO, boolean filterSponsor, Set<Long> relateSponsorIds, Set<String> aggregators, DruidHttpClient druidHttpClient) {
        super(publicSummaryMap, flowDashboardQueryDTO, filterSponsor, relateSponsorIds, aggregators, druidHttpClient);
    }

    @Override
    protected MetricsQueryIdEnum metricsQueryIdEnum() {
        return MetricsQueryIdEnum.ANTI_RULE;
    }

    @Override
    protected void doOneRowSummary(FlowDashboardQueryResult.Event event, FlowDashboardResultDTO dto) throws Exception {
        updateFraudStatTypeClick(event, dto);
    }

    private void updateFraudStatTypeClick(FlowDashboardQueryResult.Event event, FlowDashboardResultDTO dto) {
        // 上报携带
        dto.setUaFilled(_long(dto.getUaFilled()) + event.getUaFilled());
        dto.setIpFilled(_long(dto.getIpFilled()) + event.getIpFilled());
        dto.setAndroidOsFilled(_long(dto.getAndroidOsFilled()) + event.getAndroidOsFilled());
        dto.setIosOsFilled(_long(dto.getIosOsFilled()) + event.getIosOsFilled());
        dto.setDeviceModelFilled(_long(dto.getDeviceModelFilled()) + event.getDeviceModelFilled());
        dto.setOsVersionFilled(_long(dto.getOsVersionFilled()) + event.getOsVersionFilled());
        dto.setImeiFilled(_long(dto.getImeiFilled()) + event.getImeiFilled());
        dto.setOaidFilled(_long(dto.getOaidFilled()) + event.getOaidFilled());
        dto.setOaidMd5Filled(_long(dto.getOaidMd5Filled()) + event.getOaidMd5Filled());
        dto.setIdfaFilled(_long(dto.getIdfaFilled()) + event.getIdfaFilled());
        dto.setIdfaMd5Filled(_long(dto.getIdfaMd5Filled()) + event.getIdfaMd5Filled());
        dto.setCaidFilled(_long(dto.getCaidFilled()) + event.getCaidFilled());
        dto.setCaidMd5Filled(_long(dto.getCaidMd5Filled()) + event.getCaidMd5Filled());
        dto.setAndroidIdFilled(_long(dto.getAndroidIdFilled()) + event.getAndroidIdFilled());
        dto.setAndroidIdMd5Filled(_long(dto.getAndroidIdMd5Filled()) + event.getAndroidIdMd5Filled());

        // 上报统计字段
        dto.setTsFilled(_long(dto.getTsFilled()) + event.getTsFilled());
        dto.setActivityIdFilled(_long(dto.getActivityIdFilled()) + event.getActivityIdFilled());
        dto.setAlidFilled(_long(dto.getAlidFilled()) + event.getAlidFilled());

        // 识别携带
        dto.setUaFilledMark(_long(dto.getUaFilledMark()) + event.getUaFilledMark());
        dto.setIpFilledMark(_long(dto.getIpFilledMark()) + event.getIpFilledMark());
        dto.setAndroidOsFilledMark(_long(dto.getAndroidOsFilledMark()) + event.getAndroidOsFilledMark());
        dto.setIosOsFilledMark(_long(dto.getIosOsFilledMark()) + event.getIosOsFilledMark());
        dto.setDeviceModelFilledMark(_long(dto.getDeviceModelFilledMark()) + event.getDeviceModelFilledMark());
        dto.setOsVersionFilledMark(_long(dto.getOsVersionFilledMark()) + event.getOsVersionFilledMark());
        dto.setImeiFilledMark(_long(dto.getImeiFilledMark()) + event.getImeiFilledMark());
        dto.setOaidFilledMark(_long(dto.getOaidFilledMark()) + event.getOaidFilledMark());
        dto.setOaidMd5FilledMark(_long(dto.getOaidMd5FilledMark()) + event.getOaidMd5FilledMark());
        dto.setIdfaFilledMark(_long(dto.getIdfaFilledMark()) + event.getIdfaFilledMark());
        dto.setIdfaMd5FilledMark(_long(dto.getIdfaMd5FilledMark()) + event.getIdfaMd5FilledMark());
        dto.setCaidFilledMark(_long(dto.getCaidFilledMark()) + event.getCaidFilledMark());
        dto.setCaidMd5FilledMark(_long(dto.getCaidMd5FilledMark()) + event.getCaidMd5FilledMark());
        dto.setAndroidIdFilledMark(_long(dto.getAndroidIdFilledMark()) + event.getAndroidIdFilledMark());
        dto.setAndroidIdMd5FilledMark(_long(dto.getAndroidIdMd5FilledMark()) + event.getAndroidIdMd5FilledMark());

        // 识别统计字段
        dto.setTsFilledMark(_long(dto.getTsFilledMark()) + event.getTsFilledMark());
        dto.setActivityIdFilledMark(_long(dto.getActivityIdFilledMark()) + event.getActivityIdFilledMark());
        dto.setAlidFilledMark(_long(dto.getAlidFilledMark()) + event.getAlidFilledMark());

        // 处理未携带
        dto.setUaUnfilledDeal(_long(dto.getUaUnfilledDeal()) + event.getUaUnfilledDeal());
        dto.setOsUnfilledDeal(_long(dto.getOsUnfilledDeal()) + event.getOsUnfilledDeal());
        dto.setDeviceModelUnfilledDeal(_long(dto.getDeviceModelUnfilledDeal()) + event.getDeviceModelUnfilledDeal());
        dto.setOsVersionUnfilledDeal(_long(dto.getOsVersionUnfilledDeal()) + event.getOsVersionUnfilledDeal());

        // 父类异常
        dto.setExceptionClickMark(_long(dto.getExceptionClickMark()) + event.getExceptionMark());
        dto.setExceptionClickDeal(_long(dto.getExceptionClickDeal()) + event.getExceptionDeal());
        dto.setExceptionClickSend(_long(dto.getExceptionClickSend()) + event.getExceptionSend());

        // 异常
        dto.setFormatErrorUaClickMark(_long(dto.getFormatErrorUaClickMark()) + event.getFormatErrorUaMark());
        dto.setFormatErrorUaClickDeal(_long(dto.getFormatErrorUaClickDeal()) + event.getFormatErrorUaDeal());
        dto.setFormatErrorUaClickSend(_long(dto.getFormatErrorUaClickSend()) + event.getFormatErrorUaSend());
        dto.setFormatErrorDeviceIdClickMark(_long(dto.getFormatErrorDeviceIdClickMark()) + event.getFormatErrorDeviceIdMark());
        dto.setFormatErrorDeviceIdClickSend(_long(dto.getFormatErrorDeviceIdClickSend()) + event.getFormatErrorDeviceIdSend());
        dto.setValueErrorIntranetIpClickMark(_long(dto.getValueErrorIntranetIpClickMark()) + event.getValueErrorIntranetIpMark());
        dto.setValueErrorIntranetIpClickDeal(_long(dto.getValueErrorIntranetIpClickDeal()) + event.getValueErrorIntranetIpDeal());
        dto.setValueErrorIntranetIpClickSend(_long(dto.getValueErrorIntranetIpClickSend()) + event.getValueErrorIntranetIpSend());
        dto.setValueErrorDarkIpClickMark(_long(dto.getValueErrorDarkIpClickMark()) + event.getValueErrorDarkIpMark());
        dto.setValueErrorDarkIpClickDeal(_long(dto.getValueErrorDarkIpClickDeal()) + event.getValueErrorDarkIpDeal());
        dto.setValueErrorDarkIpClickSend(_long(dto.getValueErrorDarkIpClickSend()) + event.getValueErrorDarkIpSend());
        dto.setValueErrorDarkDeviceIdClickMark(_long(dto.getValueErrorDarkDeviceIdClickMark()) + event.getValueErrorDarkDeviceIdMark());
        dto.setValueErrorDarkDeviceIdClickSend(_long(dto.getValueErrorDarkDeviceIdClickSend()) + event.getValueErrorDarkDeviceIdSend());
        dto.setValueErrorIpClickMark(_long(dto.getValueErrorIpClickMark()) + event.getValueErrorIpMark());
        dto.setValueErrorIpClickDeal(_long(dto.getValueErrorIpClickDeal()) + event.getValueErrorIpDeal());
        dto.setValueErrorIpClickSend(_long(dto.getValueErrorIpClickSend()) + event.getValueErrorIpSend());
        dto.setValueErrorUaClickMark(_long(dto.getValueErrorUaClickMark()) + event.getValueErrorUaMark());
        dto.setValueErrorUaClickDeal(_long(dto.getValueErrorUaClickDeal()) + event.getValueErrorUaDeal());
        dto.setValueErrorUaClickSend(_long(dto.getValueErrorUaClickSend()) + event.getValueErrorUaSend());
        dto.setStandardDiffUaClickMark(_long(dto.getStandardDiffUaClickMark()) + event.getStandardDiffUaMark());
        dto.setStandardDiffUaClickDeal(_long(dto.getStandardDiffUaClickDeal()) + event.getStandardDiffUaDeal());
        dto.setStandardDiffUaClickSend(_long(dto.getStandardDiffUaClickSend()) + event.getStandardDiffUaSend());
        dto.setStandardDiffOsvClickMark(_long(dto.getStandardDiffOsvClickMark()) + event.getStandardDiffOsvMark());
        dto.setStandardDiffOsvClickDeal(_long(dto.getStandardDiffOsvClickDeal()) + event.getStandardDiffOsvDeal());
        dto.setStandardDiffOsvClickSend(_long(dto.getStandardDiffOsvClickSend()) + event.getStandardDiffOsvSend());
        dto.setStandardDiffModelClickMark(_long(dto.getStandardDiffModelClickMark()) + event.getStandardDiffModelMark());
        dto.setStandardDiffModelClickDeal(_long(dto.getStandardDiffModelClickDeal()) + event.getStandardDiffModelDeal());
        dto.setStandardDiffModelClickSend(_long(dto.getStandardDiffModelClickSend()) + event.getStandardDiffModelSend());
        dto.setUnexpectedOsClickMark(_long(dto.getUnexpectedOsClickMark()) + event.getUnexpectedOsMark());
        dto.setUnexpectedOsClickDeal(_long(dto.getUnexpectedOsClickDeal()) + event.getUnexpectedOsDeal());
        dto.setUnexpectedOsClick(_long(dto.getUnexpectedOsClick()) + event.getUnexpectedOsClick());
        dto.setUnexpectedDeviceClickMark(_long(dto.getUnexpectedDeviceClickMark()) + event.getUnexpectedDeviceMark());
        dto.setUnexpectedDeviceClick(_long(dto.getUnexpectedDeviceClick()) + event.getUnexpectedDeviceClick());
        dto.setUnexpectedUaClickMark(_long(dto.getUnexpectedUaClickMark()) + event.getUnexpectedUaMark());
        dto.setUnexpectedUaClickDeal(_long(dto.getUnexpectedUaClickDeal()) + event.getUnexpectedUaDeal());
        dto.setUnexpectedUaClick(_long(dto.getUnexpectedUaClick()) + event.getUnexpectedUaClick());
        dto.setUnexpectedModelClickMark(_long(dto.getUnexpectedModelClickMark()) + event.getUnexpectedModelMark());
        dto.setUnexpectedModelClickDeal(_long(dto.getUnexpectedModelClickDeal()) + event.getUnexpectedModelDeal());
        dto.setUnexpectedModelClickSend(_long(dto.getUnexpectedModelClickSend()) + event.getUnexpectedModelSend());
        dto.setRelatedMultiUaClickMark(_long(dto.getRelatedMultiUaClickMark()) + event.getRelatedMultiUaMark());
        dto.setRelatedMultiUaClickDeal(_long(dto.getRelatedMultiUaClickDeal()) + event.getRelatedMultiUaDeal());
        dto.setRelatedMultiUaClickSend(_long(dto.getRelatedMultiUaClickSend()) + event.getRelatedMultiUaSend());
        dto.setRelatedMultiModelClickMark(_long(dto.getRelatedMultiModelClickMark()) + event.getRelatedMultiModelMark());
        dto.setRelatedMultiModelClickDeal(_long(dto.getRelatedMultiModelClickDeal()) + event.getRelatedMultiModelDeal());
        dto.setRelatedMultiModelClickSend(_long(dto.getRelatedMultiModelClickSend()) + event.getRelatedMultiModelSend());
        dto.setRelatedMultiOsvClickMark(_long(dto.getRelatedMultiOsvClickMark()) + event.getRelatedMultiOsvMark());
        dto.setRelatedMultiOsvClickDeal(_long(dto.getRelatedMultiOsvClickDeal()) + event.getRelatedMultiOsvDeal());
        dto.setRelatedMultiOsvClickSend(_long(dto.getRelatedMultiOsvClickSend()) + event.getRelatedMultiOsvSend());
        dto.setRelatedRtaDeviceClickMark(_long(dto.getRelatedRtaDeviceClickMark()) + event.getRelatedRtaDeviceMark());
        dto.setRelatedRtaDeviceClickSend(_long(dto.getRelatedRtaDeviceClickSend()) + event.getRelatedRtaDeviceSend());
        dto.setRelatedRtaUaClickMark(_long(dto.getRelatedRtaUaClickMark()) + event.getRelatedRtaUaMark());
        dto.setRelatedRtaUaClickDeal(_long(dto.getRelatedRtaUaClickDeal()) + event.getRelatedRtaUaDeal());
        dto.setRelatedRtaUaClickSend(_long(dto.getRelatedRtaUaClickSend()) + event.getRelatedRtaUaSend());
        dto.setRelatedRtaModelClickMark(_long(dto.getRelatedRtaModelClickMark()) + event.getRelatedRtaModelMark());
        dto.setRelatedRtaModelClickDeal(_long(dto.getRelatedRtaModelClickDeal()) + event.getRelatedRtaModelDeal());
        dto.setRelatedRtaModelClickSend(_long(dto.getRelatedRtaModelClickSend()) + event.getRelatedRtaModelSend());
        dto.setRelatedRtaIpClickMark(_long(dto.getRelatedRtaIpClickMark()) + event.getRelatedRtaIpMark());
        dto.setRelatedRtaIpClickDeal(_long(dto.getRelatedRtaIpClickDeal()) + event.getRelatedRtaIpDeal());
        dto.setRelatedRtaIpClickSend(_long(dto.getRelatedRtaIpClickSend()) + event.getRelatedRtaIpSend());
        dto.setRelatedRtaOsvClickMark(_long(dto.getRelatedRtaOsvClickMark()) + event.getRelatedRtaOsvMark());
        dto.setRelatedRtaOsvClickDeal(_long(dto.getRelatedRtaOsvClickDeal()) + event.getRelatedRtaOsvDeal());
        dto.setRelatedRtaOsvClickSend(_long(dto.getRelatedRtaOsvClickSend()) + event.getRelatedRtaOsvSend());
        dto.setRepeatClickMark(_long(dto.getRepeatClickMark()) + event.getRepeatClickMark());
        dto.setRepeatClick(_long(dto.getRepeatClick()) + event.getRepeatClick());
        dto.setActionRateClickClickMark(_long(dto.getActionRateClickClickMark()) + event.getActionRateClickMark());
        dto.setActionRateClickClickSend(_long(dto.getActionRateClickClickSend()) + event.getActionRateClickSend());
        dto.setActionRateIpClickMark(_long(dto.getActionRateIpClickMark()) + event.getActionRateIpMark());
        dto.setActionRateIpClickSend(_long(dto.getActionRateIpClickSend()) + event.getActionRateIpSend());
        dto.setActionRateDeviceIpClickMark(_long(dto.getActionRateDeviceIpClickMark()) + event.getActionRateDeviceIpMark());
        dto.setActionRateDeviceIpClickDeal(_long(dto.getActionRateDeviceIpClickDeal()) + event.getActionRateDeviceIpDeal());
        dto.setActionRateDeviceIpClickSend(_long(dto.getActionRateDeviceIpClickSend()) + event.getActionRateDeviceIpSend());
        dto.setRtaClickTimeErrorClickMark(_long(dto.getRtaClickTimeErrorClickMark()) + event.getRtaClickTimeErrorMark());
        dto.setRtaClickTimeErrorClickDeal(_long(dto.getRtaClickTimeErrorClickDeal()) + event.getRtaClickTimeErrorDeal());
        dto.setRtaClickTimeErrorClickSend(_long(dto.getRtaClickTimeErrorClickSend()) + event.getRtaClickTimeErrorSend());
        dto.setActionFeaturesDiffSourceClickMark(_long(dto.getActionFeaturesDiffSourceClickMark()) + event.getActionFeaturesDiffSourceMark());
        dto.setActionFeaturesDiffSourceClickSend(_long(dto.getActionFeaturesDiffSourceClickSend()) + event.getActionFeaturesDiffSourceSend());
    }
}
