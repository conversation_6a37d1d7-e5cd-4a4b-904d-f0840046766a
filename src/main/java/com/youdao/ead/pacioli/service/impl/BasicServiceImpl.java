package com.youdao.ead.pacioli.service.impl;

import com.youdao.ead.pacioli.core.exception.CustomException;
import com.youdao.ead.pacioli.entity.pacioli.*;
import com.youdao.ead.pacioli.enums.*;
import com.youdao.ead.pacioli.repository.pacioli.*;
import com.youdao.ead.pacioli.service.BasicService;
import com.youdao.ead.pacioli.util.JacksonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.youdao.ead.pacioli.constants.CommonConstants.EFFECTIVE_CONVERSION_ACTION_ID_DEFAULT;
import static com.youdao.ead.pacioli.constants.CommonConstants.EXTEND_CONVERSION_ACTION_IDS_DEFAULT;
import static com.youdao.ead.pacioli.constants.ResponseType.*;
import static com.youdao.ead.pacioli.enums.RoleEnum.identifyLeader;

/**
 * <AUTHOR>
 * @date 2022/11/4
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BasicServiceImpl implements BasicService {

    private final TaskRepository taskRepository;
    private final SubTaskRepository subTaskRepository;
    private final ChannelRepository channelRepository;
    private final DisableTimeIntervalRepository disableTimeIntervalRepository;
    private final ConversionActionRepository conversionActionRepository;
    private final UserLeaderRelationRepository userLeaderRelationRepository;

    @Override
    public void assertTaskState(Long taskId, TaskStateEnum taskStateEnum) {
        assertTaskState(taskRepository.getReferenceById(taskId), taskStateEnum);
    }

    @Override
    public void assertTaskState(Task task, TaskStateEnum taskStateEnum) {
        if (!Objects.equals(task.getState(), taskStateEnum.getId())) {
            throw new CustomException(TASK_STATE_UNUSUAL);
        }
    }

    @Override
    public void assertSubTaskState(Long subTaskId, SubTaskStateEnum stateEnum) {
        assertSubTaskState(subTaskRepository.getReferenceById(subTaskId), stateEnum);
    }

    @Override
    public void assertSubTaskState(SubTask subTask, SubTaskStateEnum stateEnum) {
        if (!Objects.equals(subTask.getState(), stateEnum.idByteValue())) {
            throw new CustomException(SUBTASK_STATE_UNUSUAL);
        }
    }

    @Override
    public void assertChannelState(Long channelId, ChannelStateEnum stateEnum) {
        assertChannelState(channelRepository.getReferenceById(channelId), stateEnum);
    }

    @Override
    public void assertChannelState(Channel channel, ChannelStateEnum stateEnum) {
        if (!Objects.equals(ChannelStateEnum.NORMAL.getId(), channel.getState())) {
            throw new CustomException(CHANNEL_STATE_UNUSUAL);
        }
    }

    @Override
    public void assertConversionActionTypeCanBeEffective(Long conversionActionId) {
        if (!EFFECTIVE_CONVERSION_ACTION_ID_DEFAULT.equals(conversionActionId)) {
            ConversionAction conversionAction = conversionActionRepository.getReferenceById(conversionActionId);
            if (!ConversionActionTypeEnum.canBeEffective(conversionAction.getType())) {
                throw new CustomException(CONVERSION_ACTION_TYPE_INVALID);
            }
        }
    }

    @Override
    public List<Long> subtaskExtendConversionActionIds(SubTask subTask) {
        String json = subTask.getExtendConversionActionIds();
        try {
            if (StringUtils.isBlank(json)) {
                json = EXTEND_CONVERSION_ACTION_IDS_DEFAULT;
            }
            return JacksonUtil.jsonToList(json, Long.class);
        } catch (Exception e) {
            log.error("JacksonUtil.jsonToList error", e);
            throw new CustomException(SERVICE_ERROR);
        }
    }

    @Override
    public void disableSubTaskBatch(List<Long> subTaskIds, LocalDate start) {
        if (CollectionUtils.isEmpty(subTaskIds)) {
            return;
        }
        List<DisableTimeInterval> list = disableTimeIntervalRepository.getAllBySubTaskIdInAndEndIsNull(subTaskIds);
        Map<Long, DisableTimeInterval> intervalMap = list.stream()
                .collect(Collectors.toMap(DisableTimeInterval::getSubTaskId, Function.identity()));
        List<DisableTimeInterval> insertList = new ArrayList<>();
        for (Long id : subTaskIds) {
            DisableTimeInterval interval = intervalMap.get(id);
            if (Objects.nonNull(interval)) {
                if (interval.getStart().isAfter(start)) {
                    interval.setStart(start);
                    insertList.add(interval);
                }
            } else {
                interval = new DisableTimeInterval();
                interval.setSubTaskId(id);
                interval.setStart(start);
                insertList.add(interval);
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            disableTimeIntervalRepository.saveAll(insertList);
        }
    }

    @Override
    public void tryAvailableSubTaskBatch(List<Long> subTaskIds, LocalDate end) {
        if (CollectionUtils.isEmpty(subTaskIds)) {
            return;
        }
        // 查数据
        List<DisableTimeInterval> disableTimeIntervals = disableTimeIntervalRepository.getAllBySubTaskIdInAndEndIsNull(subTaskIds);
        List<DisableTimeInterval> result = new ArrayList<>();
        for (DisableTimeInterval interval : disableTimeIntervals) {
            interval.setEnd(end);
            result.add(interval);
        }
        disableTimeIntervalRepository.saveAll(result);
    }

    /**
     * 选择某一天日期对应的渠道单价
     *
     * @param ascList
     * @param day
     * @return maybe null
     */
    @Override
    public SubTaskChannelLog chooseSubTaskChannelLog(List<SubTaskChannelLog> ascList, LocalDate day) {
        LocalDate preDay = day.minusDays(1);
        if (CollectionUtils.isEmpty(ascList)) {
            throw new CustomException(SERVICE_ERROR);
        } else if (preDay.isBefore(ascList.get(0).getCreateTime().toLocalDate())) {
            // 填写的日期 <= 最早的渠道和渠道单价记录日期
            SubTaskChannelLog dailyLastOne = ascList.get(0);
            LocalDate day0 = dailyLastOne.getCreateTime().toLocalDate();
            for (SubTaskChannelLog s : ascList) {
                if (day0.isEqual(s.getCreateTime().toLocalDate())) {
                    dailyLastOne = s;
                } else {
                    break;
                }
            }
            return dailyLastOne;
        } else {
            // 填写的日期 > 最早的渠道和渠道单价记录日期
            List<SubTaskChannelLog> descList = new ArrayList<>();
            for (int i = ascList.size() - 1; i >= 0; i--) {
                SubTaskChannelLog channelLog = ascList.get(i);
                if (channelLog.getCreateTime().toLocalDate().isEqual(day)) {
                    descList.add(channelLog);
                } else if (channelLog.getCreateTime().toLocalDate().isBefore(day)) {
                    descList.add(channelLog);
                    break;
                }
            }
            int size = descList.stream().map(SubTaskChannelLog::getChannelId).distinct().toList().size();
            // size == 1表示当天渠道未发生过变更，size > 1代表渠道变更过。不可能size < 1
            return size == 1 ? descList.get(descList.size() - 1) : descList.get(0);
        }

    }

    @Override
    public Set<Long> allRelationUserIdForPermission(Long curUserId, RoleEnum currentRole) {
        if (identifyLeader(currentRole)) {
            List<UserLeaderRelation> all = userLeaderRelationRepository.findAll();
            Map<Long, List<UserLeaderRelation>> leaderId2UserListMap = all.stream().collect(Collectors.groupingBy(UserLeaderRelation::getLeaderId));
            // 有向图深度遍历，而且还要排除环结构
            Set<Long> doneSet = new HashSet<>();
            Queue<Long> queue = new LinkedList<>();
            queue.offer(curUserId);
            doneSet.add(curUserId);
            while (!queue.isEmpty()) {
                Long nextUserId = queue.poll();
                if (leaderId2UserListMap.containsKey(nextUserId)) {
                    for (UserLeaderRelation relation : leaderId2UserListMap.get(nextUserId)) {
                        if (!doneSet.contains(relation.getUserId())) {
                            queue.offer(relation.getUserId());
                            doneSet.add(relation.getUserId());
                        }
                    }
                }
            }
            return doneSet;
        } else {
            return new HashSet<>(Collections.singleton(curUserId));
        }
    }

    @Override
    public Set<Long> allLeaderIdForUser(Set<Long> userIds) {
        Map<Long, List<UserLeaderRelation>> userId2LeaderListMap = userLeaderRelationRepository.findAll()
                .stream().collect(Collectors.groupingBy(UserLeaderRelation::getUserId));
        // 有向图深度遍历，而且还要排除环结构
        Set<Long> doneSet = new HashSet<>(userIds);
        Queue<Long> queue = new LinkedList<>(userIds);
        while (!queue.isEmpty()) {
            Long nextUserId = queue.poll();
            if (userId2LeaderListMap.containsKey(nextUserId)) {
                for (UserLeaderRelation relation : userId2LeaderListMap.get(nextUserId)) {
                    if (!doneSet.contains(relation.getLeaderId())) {
                        queue.offer(relation.getLeaderId());
                        doneSet.add(relation.getLeaderId());
                    }
                }
            }
        }
        return doneSet;
    }
}
