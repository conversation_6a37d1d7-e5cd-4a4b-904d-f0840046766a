package com.youdao.ead.pacioli.service.impl;

import com.youdao.ead.pacioli.bo.LoginUser;
import com.youdao.ead.pacioli.constants.CommonConstants;
import com.youdao.ead.pacioli.constants.ResponseType;
import com.youdao.ead.pacioli.core.druid.DruidHttpClient;
import com.youdao.ead.pacioli.core.exception.CustomException;
import com.youdao.ead.pacioli.dto.antifraud.*;
import com.youdao.ead.pacioli.dto.channel.PagedChannelInfoDTO;
import com.youdao.ead.pacioli.entity.eadb1.Sponsor;
import com.youdao.ead.pacioli.entity.eadb1.ThirdPartPromotionInfo;
import com.youdao.ead.pacioli.entity.pacioli.*;
import com.youdao.ead.pacioli.enums.RoleEnum;
import com.youdao.ead.pacioli.enums.TimeGranularityEnum;
import com.youdao.ead.pacioli.enums.antifraud.*;
import com.youdao.ead.pacioli.mapper.AntiFraudConfigMapper;
import com.youdao.ead.pacioli.mapper.AntiFraudRuleMapper;
import com.youdao.ead.pacioli.mapper.FlowDashboardMapper;
import com.youdao.ead.pacioli.repository.eadb1.SponsorRepository;
import com.youdao.ead.pacioli.repository.eadb1.ThirdPartPromotionInfoRepository;
import com.youdao.ead.pacioli.repository.pacioli.*;
import com.youdao.ead.pacioli.service.*;
import com.youdao.ead.pacioli.service.impl.async.AbstractQueryCallable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.LongValidator;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.youdao.ead.pacioli.constants.AntiFraudConstants.*;
import static com.youdao.ead.pacioli.constants.CommonConstants.HIDE_CHANNEL_NAME;
import static com.youdao.ead.pacioli.constants.CommonConstants.THIRD_PART_PROMOTION_STATUS_NORMAL;

/**
 * <AUTHOR> Li
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AntiFraudServiceImpl implements AntiFraudService {
    private final ChannelService channelService;
    private final UserRoleRelationService userRoleRelationService;
    private final ChannelRepository channelRepository;
    private final SubTaskRepository subTaskRepository;
    private final TaskRepository taskRepository;

    private final AntiFraudConfigRepository antiFraudConfigRepository;
    private final AntiFraudRuleRepository antiFraudRuleRepository;
    private final UserService userService;
    private final ThirdPartPromotionInfoRepository thirdPartPromotionInfoRepository;
    private final SponsorRepository sponsorRepository;
    private final BasicService basicService;

    private final AntiFraudRuleMapper antiFraudRuleMapper;
    private final AntiFraudConfigMapper antiFraudConfigMapper;
    private final FlowDashboardMapper flowDashboardMapper;
    private final DruidHttpClient druidHttpClient;
    private final ChannelActivityRelationRepository channelActivityRelationRepository;

    @Override
    public Long saveAntiFraudConfig(CreateAntiFraudConfigDTO dto, LoginUser loginUser) {
        AntiFraudConfig antiFraudConfigOld = savePreCheck(dto, loginUser);
        AntiFraudConfig antiFraudConfig = antiFraudConfigMapper.saveDto2Entity(dto);
        antiFraudConfig.setId(dto.getId());
        if (antiFraudConfigOld != null) {
            antiFraudConfig.setCreatorUserRoleRelationId(antiFraudConfigOld.getCreatorUserRoleRelationId());
            antiFraudConfig.setCreateTime(antiFraudConfigOld.getCreateTime());
            antiFraudConfig.setStatus(antiFraudConfigOld.getStatus());
            antiFraudConfig.setDeleted(antiFraudConfigOld.getDeleted());
        } else {
            antiFraudConfig.setStatus(1);
            antiFraudConfig.setDeleted(false);
        }
        if (RelateActivityRefreshModeEnum.AUTO.equals(dto.getRelateActivityRefreshMode())) {
            antiFraudConfig.setRelateActivityIds(getCurrentRelateAids(dto.getRelateSponsorIds()));
        }
        if (antiFraudConfig.getRelateChannelRefreshMode() == null) {
            antiFraudConfig.setRelateChannelRefreshMode(RelateChannelRefreshModeEnum.AUTO);
        }
        if (antiFraudConfig.getRelateChannelDids() == null) {
            antiFraudConfig.setRelateChannelDids(Collections.emptySet());
        }
        antiFraudConfigRepository.save(antiFraudConfig);
        return antiFraudConfig.getId();
    }

    @Override
    public Boolean isNameExist(String name, Long id) {
        List<AntiFraudConfig> nameQuery = antiFraudConfigRepository.findByName(name)
                .stream().filter(item -> !item.getId().equals(id))
                .toList();
        return CollectionUtils.isNotEmpty(nameQuery);
    }

    @Override
    public Set<Long> getCurrentRelateAids(Set<Long> relateSponsorIds) {
        List<ThirdPartPromotionInfo> thirdPartPromotionInfos = thirdPartPromotionInfoRepository.findBySponsorIdInAndPromotionStatus(relateSponsorIds, THIRD_PART_PROMOTION_STATUS_NORMAL);
        return thirdPartPromotionInfos.stream().map(ThirdPartPromotionInfo::getPromotionId).collect(Collectors.toSet());
    }


    private AntiFraudConfig savePreCheck(CreateAntiFraudConfigDTO dto, LoginUser loginUser) {
        AntiFraudConfig antiFraudConfig = null;
        if (isNameExist(dto.getName(), dto.getId())) {
            throw new CustomException(ResponseType.SERVICE_ERROR, "名字重复");
        }
        // 已经删除的规则不允许编辑
        if (dto.getId() != null) {
            Optional<AntiFraudConfig> antiFraudConfigOptional = antiFraudConfigRepository.findById(dto.getId());
            if (antiFraudConfigOptional.isEmpty()) {
                throw new CustomException(ResponseType.ANTI_FRAUD_CONFIG_NOT_EXIST);
            }
            antiFraudConfig = antiFraudConfigOptional.get();
            if (antiFraudConfig.getDeleted()) {
                throw new CustomException(ResponseType.ANTI_FRAUD_CONFIG_DELETE_CANT_EDIT);
            }
        }
        // 这里是否还需要对 运营角色的 渠道ID进行验证呢？
        if (!loginUser.getCurrentRole().equals(RoleEnum.ADMIN)) {
            Set<Sponsor> relateSponsor = userService.getRelateSponsor(loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
            Set<Long> relateSponsorIds = relateSponsor.stream().map(Sponsor::getSponsorId).collect(Collectors.toSet());
            if (RelateActivityRefreshModeEnum.NORMAL.equals(dto.getRelateActivityRefreshMode())) {
                List<ThirdPartPromotionInfo> thirdPartPromotionInfos = thirdPartPromotionInfoRepository.findBySponsorIdInAndPromotionStatus(relateSponsorIds, THIRD_PART_PROMOTION_STATUS_NORMAL);
                Set<Long> relateAids = thirdPartPromotionInfos.stream().map(ThirdPartPromotionInfo::getPromotionId).collect(Collectors.toSet());
                if (!CollectionUtils.containsAll(relateAids, dto.getRelateActivityIds())) {
                    throw new CustomException(ResponseType.ACCESS_DENIED, "当前规则被超管或主管增加了当前账号不可见的广告商范围，请联系超管处理");
                }
            } else {
                if (!CollectionUtils.containsAll(relateSponsorIds, dto.getRelateSponsorIds())) {
                    throw new CustomException(ResponseType.ACCESS_DENIED, "当前规则被超管或主管增加了当前账号不可见的广告商范围，请联系超管处理");
                }
            }
            if (canViewChannel(loginUser.getCurrentRole()) && RelateChannelRefreshModeEnum.NORMAL.equals(dto.getRelateChannelRefreshMode())) {
                //这里需要校验渠道的did，因为存在a选了did，然后b去修改了，然后提交了a选的did，但是没有权限
                List<PagedChannelInfoDTO> channelInfoDTOS = channelService.getChannelListByConditions(null, loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
                Set<String> relateChannelDids = channelInfoDTOS.stream().map(PagedChannelInfoDTO::getDid).collect(Collectors.toSet());
                if (!CollectionUtils.containsAll(relateChannelDids, dto.getRelateChannelDids())) {
                    throw new CustomException(ResponseType.ACCESS_DENIED, "当前规则被超管或主管增加了当前账号不可见的渠道范围，请联系超管处理");
                }
            }

            List<AntiFraudRule> clickRules = antiFraudRuleRepository.findByScopeIn(AntiRuleScopeEnum.CLICK.getScopes());
            if (CollectionUtils.isNotEmpty(clickRules)) {
                if (!CollectionUtils.containsAll(clickRules.stream().map(AntiFraudRule::getId).toList(), dto.getRuleIds())) {
                    throw new CustomException(ResponseType.PARAM_EXCEPTION);
                }
            }
            List<AntiFraudRule> rtaRules = antiFraudRuleRepository.findByScopeIn(AntiRuleScopeEnum.RTA_REQUEST.getScopes());
            if (CollectionUtils.isNotEmpty(rtaRules)) {
                if (!CollectionUtils.containsAll(rtaRules.stream().map(AntiFraudRule::getId).toList(), dto.getRuleIdsRta())) {
                    throw new CustomException(ResponseType.PARAM_EXCEPTION);
                }
            }
        }
        // 是不是要对管理员验证呢

        return antiFraudConfig;
    }

    @Override
    public AntiFraudRuleByScopeDTO getAntiFraudRulesByType(String name) {
        AntiFraudRuleByScopeDTO result = new AntiFraudRuleByScopeDTO();
        searchAndBuildRules(name, result.getRulesClick(), AntiRuleScopeEnum.CLICK);
        searchAndBuildRules(name, result.getRulesRta(), AntiRuleScopeEnum.RTA_REQUEST);
        return result;
    }

    private void searchAndBuildRules(String name, Set<AntiFraudRuleByTypeDTO> click, AntiRuleScopeEnum antiRuleScopeEnum) {
        List<AntiFraudRule> allRules = antiFraudRuleRepository.findByStatusAndScopeIn(RULE_STATUS_NORMAL, antiRuleScopeEnum.getScopes())
                .stream()
                .filter(item -> item.getName().contains(name))
                .toList();
        Map<String, List<AntiFraudRule>> type2Rules = allRules.stream().collect(Collectors.groupingBy(AntiFraudRule::getType));

        type2Rules.forEach((type, rules) -> {
            rules.sort(Comparator.comparingInt(AntiFraudRule::getSortNum));
            Collection<AntiFraudRuleDTO> dto = antiFraudRuleMapper.toDto(rules);
            click.add(AntiFraudRuleByTypeDTO.builder().type(type).rules(new TreeSet<>(dto)).build());
        });
    }

    @Override
    public List<RelatePromoteInfoCandidateDTO> getRelatePromoteInfoCandidate(String sponsorIdOrActivityId, LoginUser loginUser) {
        Set<Sponsor> relateSponsor = userService.getRelateSponsor(loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
        if (CollectionUtils.isEmpty(relateSponsor)) {
            return List.of();
        }
        List<ThirdPartPromotionInfo> result = getThirdPartPromotionInfoList(relateSponsor, sponsorIdOrActivityId);
        if (CollectionUtils.isEmpty(result)) {
            return List.of();
        }
        Set<RelatePromoteInfoCandidateDTO> dtoSet = new HashSet<>();
        Map<Long, Sponsor> sponsorId2Obj = relateSponsor.stream().collect(Collectors.toMap(Sponsor::getSponsorId, Function.identity()));
        // 目的是拿到关联的AID
        Map<Long, List<ThirdPartPromotionInfo>> sponsorId2ThirdPartPromotionInfo = result.stream().collect(Collectors.groupingBy(ThirdPartPromotionInfo::getSponsorId));
        sponsorId2ThirdPartPromotionInfo.forEach((sponsorId, promotionInfoList) -> {
            List<RelatePromoteInfoCandidateDTO.PromotionActivity> activitySet = promotionInfoList.stream().map(item -> RelatePromoteInfoCandidateDTO.PromotionActivity.builder()
                    .activityId(item.getPromotionId())
                    .name(item.getPromotionName())
                    .build()).collect(Collectors.toList());
            dtoSet.add(RelatePromoteInfoCandidateDTO.builder().sponsorId(sponsorId)
                    .sponsorName(sponsorId2Obj.get(sponsorId).getName())
                    .activityList(activitySet)
                    .build());
        });
        //排序
        return dtoSet.stream()
                .sorted(Comparator.comparing(RelatePromoteInfoCandidateDTO::getSponsorId, Comparator.reverseOrder())
                        .thenComparing((RelatePromoteInfoCandidateDTO dto) ->
                                dto.getActivityList().stream()
                                        .sorted(Comparator.comparing(RelatePromoteInfoCandidateDTO.PromotionActivity::getActivityId, Comparator.reverseOrder()))
                                        .collect(Collectors.toList()), (l1, l2) -> 0))
                .collect(Collectors.toList());
    }

    private List<ThirdPartPromotionInfo> getThirdPartPromotionInfoList(Set<Sponsor> relateSponsor, String sponsorOrActivityIdAndName) {
        if (StringUtils.isNotBlank(sponsorOrActivityIdAndName)) {
            sponsorOrActivityIdAndName = StringUtils.substring(sponsorOrActivityIdAndName, 0, 3000);
            String[] keywords = sponsorOrActivityIdAndName.split("[,\\s]+");
            Set<Long> sponsorIds = new HashSet<>();
            Set<Long> activityIds = new HashSet<>();
            for (String keyword : keywords) {
                sponsorIds.addAll(relateSponsor.stream()
                        .filter(o -> StringUtils.containsIgnoreCase(o.getName(), keyword)
                                || StringUtils.containsIgnoreCase(o.getUserName(), keyword))
                        .map(Sponsor::getSponsorId).collect(Collectors.toSet()));
                if (LongValidator.getInstance().isValid(keyword)) {
                    long sponsorOrActivityId = Long.parseLong(keyword.trim());
                    activityIds.add(sponsorOrActivityId);
                    sponsorIds.add(sponsorOrActivityId);
                }
            }
            return thirdPartPromotionInfoRepository.findBySponsorIdInAndPromotionStatus(
                    relateSponsor.stream().map(Sponsor::getSponsorId).collect(Collectors.toSet()), sponsorIds, activityIds, Arrays.asList(keywords), THIRD_PART_PROMOTION_STATUS_NORMAL);
        } else {
            return thirdPartPromotionInfoRepository.findBySponsorIdInAndPromotionStatus(
                    relateSponsor.stream().map(Sponsor::getSponsorId).collect(Collectors.toSet()),
                    THIRD_PART_PROMOTION_STATUS_NORMAL
            );
        }
    }

    @Override
    public Page<AntiFraudConfigPagedDTO> getAntiFraudConfigPage(AntiFraudConfigQueryDTO dto, LoginUser loginUser) {
        List<AntiFraudConfig> antiFraudConfigs = antiFraudConfigRepository.findBy(dto);
        Set<Long> userRoleRelationIds = basicService.allRelationUserIdForPermission(loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
        if (RoleEnum.ADMIN.equals(loginUser.getCurrentRole()) || RoleEnum.CHANNEL_OPERATOR.equals(loginUser.getCurrentRole())) {
        } else {
            Set<String> relateChannelDids = channelService.getChannelListByConditions(null, loginUser.getUserRoleRelationId(), loginUser.getCurrentRole())
                    .stream().map(PagedChannelInfoDTO::getDid).collect(Collectors.toSet());
            Set<Long> relateSponsorIds;
            if (RoleEnum.identifyBd(loginUser.getCurrentRole())) {
                //判断BD角色的广告商可见性: 渠道和aid的关系表
                Set<Long> relateActivityIds = channelActivityRelationRepository.findByChannelDidIn(relateChannelDids)
                        .stream().map(ChannelActivityRelation::getActivityId).collect(Collectors.toSet());
                relateSponsorIds = CollectionUtils.isEmpty(relateActivityIds) ? new HashSet<>() : thirdPartPromotionInfoRepository.findByPromotionStatusAndPromotionIdIn(THIRD_PART_PROMOTION_STATUS_NORMAL, relateActivityIds)
                        .stream().map(ThirdPartPromotionInfo::getSponsorId).collect(Collectors.toSet());
                antiFraudConfigs = antiFraudConfigs.stream().filter(antiFraudConfig -> {
                    //自己创建的和下属创建的规则
                    if (userRoleRelationIds.contains(antiFraudConfig.getCreatorUserRoleRelationId())) {
                        return true;
                    }
                    if (RelateChannelRefreshModeEnum.AUTO.equals(antiFraudConfig.getRelateChannelRefreshMode())) {
                        //自己名下广告商范围下的规则
                        if (CollectionUtils.containsAny(antiFraudConfig.getRelateSponsorIds(), relateSponsorIds)) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        //自己名下渠道范围下的规则
                        if (CollectionUtils.containsAny(antiFraudConfig.getRelateChannelDids(), relateChannelDids)) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                }).collect(Collectors.toList());
            } else {
                relateSponsorIds = userService.getRelateSponsor(loginUser.getUserRoleRelationId(), loginUser.getCurrentRole())
                        .stream().map(Sponsor::getSponsorId).collect(Collectors.toSet());
                antiFraudConfigs = antiFraudConfigs.stream().filter(antiFraudConfig -> {
                    //自己创建的和下属创建的规则
                    if (userRoleRelationIds.contains(antiFraudConfig.getCreatorUserRoleRelationId())) {
                        return true;
                    }
                    //自己名下广告商范围下的规则
                    if (CollectionUtils.containsAny(antiFraudConfig.getRelateSponsorIds(), relateSponsorIds)) {
                        return true;
                    } else {
                        return false;
                    }
                }).collect(Collectors.toList());
            }
        }
        if (Objects.nonNull(dto.getActivityIds())) {
            antiFraudConfigs = antiFraudConfigs.stream().filter(item ->
                    CollectionUtils.containsAny(item.getRelateActivityIds(), dto.getActivityIds())
            ).toList();
        }
        if (Objects.nonNull(dto.getChannelDids())) {
            antiFraudConfigs = antiFraudConfigs.stream().filter(item ->
                    RelateChannelRefreshModeEnum.AUTO.equals(item.getRelateChannelRefreshMode()) || CollectionUtils.containsAny(item.getRelateChannelDids(), dto.getChannelDids())
            ).toList();
        }
        Set<AntiFraudConfig> paged = antiFraudConfigs.stream()
                .sorted(Comparator.comparing(AntiFraudConfig::getId).reversed())
                .skip((long) dto.getPageNum() * dto.getPageSize())
                .limit(dto.getPageSize())
                .collect(Collectors.toCollection(LinkedHashSet::new));
        if (CollectionUtils.isEmpty(paged)) {
            return new PageImpl<>(Collections.emptyList(),
                    PageRequest.of(dto.getPageNum(), dto.getPageSize()),
                    antiFraudConfigs.size());
        }
        List<AntiFraudRule> rules = antiFraudRuleRepository.findAll();
        Map<Long, AntiFraudRule> id2AntiFraudRule = rules.stream().collect(Collectors.toMap(AntiFraudRule::getId, Function.identity()));
        Set<Long> relateAids = paged.stream().map(AntiFraudConfig::getRelateActivityIds).flatMap(Set::stream).collect(Collectors.toSet());
        List<ThirdPartPromotionInfo> relateThirdPartPromotions = thirdPartPromotionInfoRepository.findAllById(relateAids);
        Map<Long, Long> aid2SponsorId = relateThirdPartPromotions.stream()
                .collect(Collectors.toMap(ThirdPartPromotionInfo::getPromotionId, ThirdPartPromotionInfo::getSponsorId));
        Set<Long> relateSponsorIds = relateThirdPartPromotions.stream().map(ThirdPartPromotionInfo::getSponsorId).collect(Collectors.toSet());
        List<Sponsor> sponsors = sponsorRepository.findAllById(relateSponsorIds);
        Map<Long, String> sponsorId2Name = sponsors.stream().collect(Collectors.toMap(Sponsor::getSponsorId, Sponsor::getName));

        Set<Long> creatorIdSet = paged.stream().map(AntiFraudConfig::getCreatorUserRoleRelationId).collect(Collectors.toSet());
        Set<Long> modifyIdSet = paged.stream().map(AntiFraudConfig::getLastModifierUserRoleRelationId).collect(Collectors.toSet());
        creatorIdSet.addAll(modifyIdSet);
        Map<Long, String> userId2Name = userRoleRelationService.getByUserRoleRelationId(creatorIdSet)
                .stream().collect(Collectors.toMap(User::getUserRoleRelationId, User::getName));

        List<AntiFraudConfigPagedDTO> result = new LinkedList<>();
        paged.forEach(item -> {
            Set<Long> configRelateSponsorIds = new HashSet<>();
            item.getRelateActivityIds().forEach(aid -> {
                configRelateSponsorIds.add(aid2SponsorId.get(aid));
            });
            AntiFraudConfigPagedDTO.AntiFraudConfigPagedDTOBuilder builder = AntiFraudConfigPagedDTO.builder()
                    .id(item.getId())
                    .name(item.getName())
                    .status(item.getStatus())
                    .rules(item.getRuleIds().stream().map(ruleId ->
                            new AntiFraudRuleDTO(ruleId, id2AntiFraudRule.get(ruleId).getName(), id2AntiFraudRule.get(ruleId).getSortNum())
                    ).collect(Collectors.toSet()))
                    .rulesRta(item.getRuleIdsRta().stream().map(ruleId ->
                            new AntiFraudRuleDTO(ruleId, id2AntiFraudRule.get(ruleId).getName(), id2AntiFraudRule.get(ruleId).getSortNum())
                    ).collect(Collectors.toSet()))
                    .relateSponsorNames(
                            configRelateSponsorIds.stream().map(sponsorId2Name::get).collect(Collectors.toSet())
                    )
                    .deleted(item.getDeleted())
                    .relateActivityCount(item.getRelateActivityIds().size())
                    .creator(userId2Name.get(item.getCreatorUserRoleRelationId()))
                    .createTime(item.getCreateTime())
                    .updater(userId2Name.get(item.getLastModifierUserRoleRelationId()))
                    .updateTime(item.getLastModifiedTime())
                    .editAndDeleteEnAble(
                            RoleEnum.ADMIN.equals(loginUser.getCurrentRole())
                                    || RoleEnum.CHANNEL_OPERATOR.equals(loginUser.getCurrentRole())
                                    || userRoleRelationIds.contains(item.getCreatorUserRoleRelationId())
                    );
            if (canViewChannel(loginUser.getCurrentRole())) {
                builder.relateChannelCount(RelateChannelRefreshModeEnum.NORMAL.equals(item.getRelateChannelRefreshMode()) ? item.getRelateChannelDids().size() : -1);
            }
            result.add(builder.build());
        });
        return new PageImpl<>(result, PageRequest.of(dto.getPageNum(), dto.getPageSize()), antiFraudConfigs.size());
    }


    @Override
    public AntiFraudConfigDetailDTO getAntiFraudConfigDetail(Long id, LoginUser loginUser) {
        AntiFraudConfig antiFraudConfig = antiFraudConfigRepository.findById(id).orElseThrow(() -> new CustomException(ResponseType.SERVICE_ERROR, "NO RECORD"));
//        permissionCheck(antiFraudConfig.getCreatorUserRoleRelationId(), loginUser);
        Map<String, AntiFraudRuleByTypeDTO> type2Rule = rulesGroupByType(antiFraudConfig.getRuleIds());
        Map<String, AntiFraudRuleByTypeDTO> type2RuleRta = rulesGroupByType(antiFraudConfig.getRuleIdsRta());

        AntiFraudConfigDetailDTO.AntiFraudConfigDetailDTOBuilder resultBuilder = AntiFraudConfigDetailDTO.builder();
        resultBuilder.id(antiFraudConfig.getId())
                .name(antiFraudConfig.getName())
                .filterRatio(antiFraudConfig.getFilterRatio())
                .rules(Set.copyOf(type2Rule.values()))
                .rulesRta(Set.copyOf(type2RuleRta.values()))
                .triggerMode(antiFraudConfig.getTriggerMode())
                .relateActivityRefreshMode(antiFraudConfig.getRelateActivityRefreshMode())
                .relateChannelRefreshMode(antiFraudConfig.getRelateChannelRefreshMode());
        List<ThirdPartPromotionInfo> thirdPartPromotionList = thirdPartPromotionInfoRepository.findAllById(antiFraudConfig.getRelateActivityIds());
        Map<Long, ThirdPartPromotionInfo> id2ThirdPartPromotionInfo = thirdPartPromotionList.stream().collect(Collectors.toMap(ThirdPartPromotionInfo::getPromotionId, Function.identity()));
        List<Sponsor> relateSponsors = sponsorRepository.findAllById(thirdPartPromotionList.stream().map(ThirdPartPromotionInfo::getSponsorId).collect(Collectors.toSet()));
        Map<Long, Sponsor> id2Sponsor = relateSponsors.stream().collect(Collectors.toMap(Sponsor::getSponsorId, Function.identity()));
            Map<Long, RelatePromoteInfoCandidateDTO> sponsorId2DTO = new HashMap<>();
            antiFraudConfig.getRelateActivityIds().forEach(aid -> {
                ThirdPartPromotionInfo thirdPartPromotionInfo = id2ThirdPartPromotionInfo.get(aid);
                sponsorId2DTO.putIfAbsent(
                        thirdPartPromotionInfo.getSponsorId(),
                        new RelatePromoteInfoCandidateDTO(thirdPartPromotionInfo.getSponsorId(),
                                id2Sponsor.get(thirdPartPromotionInfo.getSponsorId()).getName(),
                                new ArrayList<>()));
                sponsorId2DTO.get(thirdPartPromotionInfo.getSponsorId())
                        .getActivityList()
                        .add(new RelatePromoteInfoCandidateDTO.PromotionActivity(thirdPartPromotionInfo.getPromotionId(), thirdPartPromotionInfo.getPromotionName()));
            });
            resultBuilder.relatePromoteInfos(Set.copyOf(sponsorId2DTO.values()));
            if (RelateChannelRefreshModeEnum.NORMAL.equals(antiFraudConfig.getRelateChannelRefreshMode())) {
                boolean isBdOrAdmin = RoleEnum.identifyBd(loginUser.getCurrentRole()) || RoleEnum.identifyAdmin(loginUser.getCurrentRole());
                List<Channel> channels = channelRepository.findAllByDidIn(antiFraudConfig.getRelateChannelDids());
                Set<RelateChannelInfoDTO> relateChannelInfoDTOS = channels.stream().map(item -> {
                    RelateChannelInfoDTO relateChannelInfoDTO = new RelateChannelInfoDTO();
                    relateChannelInfoDTO.setId(item.getId());
                    relateChannelInfoDTO.setDid(item.getDid());
                    relateChannelInfoDTO.setCode(item.getCode());
                    relateChannelInfoDTO.setName(isBdOrAdmin ? item.getName() : HIDE_CHANNEL_NAME);
                    return relateChannelInfoDTO;
                }).collect(Collectors.toSet());
                resultBuilder.relateChannels(relateChannelInfoDTOS);
            }
        return resultBuilder.build();
    }

    private @NotNull Map<String, AntiFraudRuleByTypeDTO> rulesGroupByType(Set<Long> ruleIds) {
        List<AntiFraudRule> antiFraudRules = antiFraudRuleRepository.findAll().stream().filter(item -> ruleIds.contains(item.getId())).toList();
        Map<String, AntiFraudRuleByTypeDTO> type2Rule = new HashMap<>();
        antiFraudRules.forEach(item -> {
            type2Rule.putIfAbsent(item.getType(), new AntiFraudRuleByTypeDTO(item.getType(), new HashSet<>()));
            type2Rule.get(item.getType()).getRules().add(new AntiFraudRuleDTO(item.getId(), item.getName(), item.getSortNum()));
        });
        return type2Rule;
    }

    private boolean canViewChannel(RoleEnum roleEnum) {
        return RoleEnum.identifyOperator(roleEnum) || RoleEnum.identifyBd(roleEnum) || RoleEnum.ADMIN.equals(roleEnum) || RoleEnum.identifyChannelOperator(roleEnum);
    }

    @Override
    public void delete(Long id, LoginUser loginUser) {
        AntiFraudConfig antiFraudConfig = antiFraudConfigRepository.findById(id)
                .orElseThrow(() -> new CustomException(ResponseType.SERVICE_ERROR, "NO SUCH RECORD"));
        permissionCheck(antiFraudConfig.getCreatorUserRoleRelationId(), loginUser);
        antiFraudConfig.setDeleted(true);
        antiFraudConfigRepository.save(antiFraudConfig);
    }

    @Override
    public void updateStatus(Long id, Integer status, LoginUser loginUser) {
        AntiFraudConfig antiFraudConfig = antiFraudConfigRepository.findById(id)
                .orElseThrow(() -> new CustomException(ResponseType.SERVICE_ERROR, "NO SUCH RECORD"));
        permissionCheck(antiFraudConfig.getCreatorUserRoleRelationId(), loginUser);
        if (!Set.of(0, 1).contains(status)) {
            throw new CustomException(ResponseType.PARAM_EXCEPTION);
        }
        if (!antiFraudConfig.getStatus().equals(status)) {
            antiFraudConfig.setStatus(status);
            antiFraudConfigRepository.save(antiFraudConfig);
        }
    }

    private void permissionCheck(Long creatorId, LoginUser currentUser) {
        if (!RoleEnum.ADMIN.equals(currentUser.getCurrentRole()) && !RoleEnum.CHANNEL_OPERATOR.equals(currentUser.getCurrentRole())) {
            Set<Long> relateUserId = basicService.allRelationUserIdForPermission(currentUser.getUserRoleRelationId(), currentUser.getCurrentRole());
            if (!relateUserId.contains(creatorId)) {
                throw new CustomException(ResponseType.ACCESS_DENIED);
            }
        }
    }

    @Override
    public Page<FlowDashboardResultDTO> getFlowDashboard(FlowDashboardQueryDTO queryDTO, LoginUser loginUser) {
        queryDTO.initDimensionSet();
        Long userId = loginUser.getUserRoleRelationId();
        RoleEnum currentRole = loginUser.getCurrentRole();
        // 查询广告商过滤条件
        boolean filterSponsor = !RoleEnum.identifyAdmin(currentRole);
        Set<Long> relateSponsorIds = Collections.emptySet();
        if (filterSponsor) {
            Set<Sponsor> relateSponsor = userService.getRelateSponsor(userId, currentRole);
            if (CollectionUtils.isEmpty(relateSponsor)) {
                return new PageImpl<>(List.of(), PageRequest.of(queryDTO.getPageNum(), queryDTO.getPageSize()), 0L);
            }
            relateSponsorIds = relateSponsor.stream().map(Sponsor::getSponsorId).collect(Collectors.toSet());
        }
        // 因为BD会查询出所有的广告商，所以对于BD，如果没有传渠道，则需要加上名下所有的渠道的一个限制
        if (RoleEnum.identifyBd(loginUser.getCurrentRole()) && CollectionUtils.isEmpty(queryDTO.getChannelDids())) {
            List<PagedChannelInfoDTO> channelInfoDTOS = channelService.getChannelListByConditions(null, loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
            Set<String> relateChannelDids = channelInfoDTOS.stream().map(PagedChannelInfoDTO::getDid).collect(Collectors.toSet());
            queryDTO.setChannelDids(relateChannelDids);
        }
        // 异步并行查询所需数据
        Map<MetricsQueryIdEnum, Set<String>> aggMaps = new HashMap<>();
        Set<FlowDashboardMetricsEnum> metricsEnumSet = new HashSet<>();
        queryDTO.getDisplayMetricsSet().forEach(x -> {
            FlowDashboardMetricsEnum metricsEnum = FlowDashboardMetricsEnum.fieldOf(x);
            metricsEnumSet.add(metricsEnum);
            Map<MetricsQueryIdEnum, Set<String>> queryId2Aggregators = metricsEnum.getQueryId2Aggregators();
            queryId2Aggregators.forEach((key, value) -> {
                Set<String> aggSet = aggMaps.computeIfAbsent(key, k -> new HashSet<>());
                aggSet.addAll(value);
            });
        });
        ConcurrentHashMap<String, FlowDashboardResultDTO> publicSummaryMap = new ConcurrentHashMap<>();
        List<Future<Boolean>> futureList = AbstractQueryCallable.asyncQuery(queryDTO, filterSponsor, relateSponsorIds, aggMaps, publicSummaryMap, druidHttpClient);
        // 等待数据查询结束
        int undoCount = blockedWaitForResult(futureList);
        // 处理汇总过数据的查询结果
        if (undoCount != 0) {
            throw new CustomException(ResponseType.SERVICE_ERROR, "查询超时失败");
        }
        if (publicSummaryMap.isEmpty()) {
            return new PageImpl<>(List.of(), PageRequest.of(queryDTO.getPageNum(), queryDTO.getPageSize()), 0L);
        }
        Set<FlowDashboardResultDTO> dashboardResultDTOS = new HashSet<>(publicSummaryMap.values());
//        if (!dto.getDisplayAllZeroLine()) {
            // 如果不展示全为0的数据，需要先计算rates
            calRates(dashboardResultDTOS, aggMaps.keySet());
//        }
        Set<FlowDashboardResultDTO> filtered = dashboardResultDTOS.stream()
                .filter(item -> queryDTO.getDisplayAllZeroLine() || !isAllMetricsZero(item, metricsEnumSet))
                .sorted((o1, o2) -> {
                    String sortMetrics = queryDTO.getSortMetrics();
                    if (StringUtils.isNotBlank(sortMetrics) && queryDTO.getIsAsc() != null && queryDTO.getSortMetrics().contains(sortMetrics)) {
                        try {
                            Field field1 = o1.getClass().getDeclaredField(sortMetrics);
                            field1.setAccessible(true);
                            Object field1Obj = field1.get(o1);

                            Field field2 = o2.getClass().getDeclaredField(sortMetrics);
                            field2.setAccessible(true);
                            Object field2Obj = field2.get(o2);
                            if (ObjectUtils.allNull(field1Obj, field2Obj)) {
                                return 0;
                            }
                            if (Objects.isNull(field1Obj)) {
                                return queryDTO.getIsAsc() ? -1 : 1;
                            } else if (Objects.isNull(field2Obj)) {
                                return queryDTO.getIsAsc() ? 1 : -1;
                            }
                            BigDecimal val1 = new BigDecimal(field1Obj.toString());
                            BigDecimal val2 = new BigDecimal(field2Obj.toString());
                            return queryDTO.getIsAsc() ? val1.compareTo(val2) : val2.compareTo(val1);
                        } catch (Exception e) {
                            log.warn("getFlowChart sort by metric got exception, ", e);
                            return 0;
                        }
                    }
                    return o1.getDateTime().compareTo(o2.getDateTime());
                })
                .collect(Collectors.toCollection(LinkedHashSet::new));
        LinkedHashSet<FlowDashboardResultDTO> paged = filtered.stream().skip((long) queryDTO.getPageNum() * queryDTO.getPageSize())
                .limit(queryDTO.getPageSize()).collect(Collectors.toCollection(LinkedHashSet::new));
        if (CollectionUtils.isEmpty(paged)) {
            return new PageImpl<>(List.of(), PageRequest.of(queryDTO.getPageNum(), queryDTO.getPageSize()), filtered.size());
        }
//        if (dto.getDisplayAllZeroLine()) {
//            // 如果展示全为0的数据，可以后计算rates
//            calRates(paged, aggMaps.keySet());
//        }
        setZeroForDisplayFields(paged, metricsEnumSet);
        setNullForIgnoreFields(paged, queryDTO.getDisplayMetricsSet());
        injectNames(paged, loginUser.getCurrentRole(), queryDTO.getDimensionSet());
        return new PageImpl<>(List.copyOf(paged), PageRequest.of(queryDTO.getPageNum(), queryDTO.getPageSize()), filtered.size());
    }

    /**
     * 阻塞等待所有异步任务执行结束
     *
     * @param futureList 异步任务列表
     * @return 未成功执行完的数量，超时或者异常都属于未成功执行完
     */
    private int blockedWaitForResult(List<Future<Boolean>> futureList) {
        int undoCount = futureList.size();
        long startTs = System.currentTimeMillis();
        for (Future<Boolean> future : futureList) {
            try {
                // 查询超时时间29s，前端接口超时30s
                long timeOutMs = 39000 + System.currentTimeMillis() - startTs;
                if (timeOutMs > 0) {
                    Boolean isSuccess = future.get(timeOutMs, TimeUnit.MILLISECONDS);
                    if (isSuccess) {
                        undoCount--;
                    }
                } else {
                    if (future.isDone()) {
                        undoCount--;
                    } else {
                        log.warn("getFlowDashboard query future timeout. ");
                        future.cancel(true);
                    }
                }
            } catch (Exception e) {
                future.cancel(true);
                log.error("getFlowDashboard query future get error. ", e);
            }
        }
        return undoCount;
    }

    private boolean isAllMetricsZero(FlowDashboardResultDTO dto, Set<FlowDashboardMetricsEnum> metricsEnumSet) {
        try {
            for (FlowDashboardMetricsEnum flowDashboardMetricsEnum : metricsEnumSet) {
                Field declaredField = dto.getClass().getDeclaredField(flowDashboardMetricsEnum.getMetrics());
                declaredField.setAccessible(true);
                // 现在指标是异步并行查询的了，是有可能为null的
                Object value = declaredField.get(dto);
                if (value != null) {
                    if (value instanceof Long) {
                        if (((Long) value) > 0) {
                            return false;
                        }
                    } else if (value instanceof BigDecimal) {
                        if (((BigDecimal) value).doubleValue() > 0) {
                            return false;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("judge all metrics zero failed.", e);
            throw new CustomException(ResponseType.SERVICE_ERROR);
        }
        return true;
    }

    private void injectNames(Set<FlowDashboardResultDTO> items, RoleEnum currentRole, Set<FlowDashboardDimensionEnum> dimensionSet) {
        if (dimensionSet.contains(FlowDashboardDimensionEnum.OS_TYPE)) {
            items.forEach(item -> {
                if (StringUtils.isBlank(item.getOsType())) {
                    item.setOsType(UNKNOWN_COLUMN);
                }
            });
        }
        if (dimensionSet.contains(FlowDashboardDimensionEnum.SPONSOR)) {
            List<Sponsor> sponsors = sponsorRepository.findAllById(items.stream().map(FlowDashboardResultDTO::getSponsorId).toList());
            Map<Long, String> sponsorId2Name = sponsors.stream().collect(Collectors.toMap(Sponsor::getSponsorId, Sponsor::getName));
            items.forEach(item -> item.setSponsorName(sponsorId2Name.get(item.getSponsorId())));
        }
        if (dimensionSet.contains(FlowDashboardDimensionEnum.CHANNEL)) {
            List<Channel> channels = channelRepository.findAllByDidIn(items.stream().map(FlowDashboardResultDTO::getChannelDid).collect(Collectors.toSet()));
            Map<String, Channel> did2Channel = channels.stream().collect(Collectors.toMap(Channel::getDid, x -> x));
            items.forEach(item -> {
                String did = item.getChannelDid();
                // 未知did单独处理，非空的但是找不到名字的就把名字展示为did
                if (UNKNOWN_CHANNEL_DID.equals(item.getChannelDid())) {
                    item.setChannelName(UNKNOWN_CHANNEL_NAME);
                    item.setChannelCode(UNKNOWN_CHANNEL_CODE);
                } else {
                    Channel channel = did2Channel.get(did);
                    // 限制账号角色对渠道名称可见
                    boolean canSeeChannelName = RoleEnum.identifyBd(currentRole) || RoleEnum.identifyAdmin(currentRole);
                    if (canSeeChannelName) {
                        item.setChannelName(channel == null ? item.getChannelDid() : channel.getName());
                    }
                    item.setChannelCode(Objects.isNull(channel) ? UNKNOWN_CHANNEL_CODE : channel.getCode());
                }
            });
        }
        if (dimensionSet.contains(FlowDashboardDimensionEnum.AID)) {
            List<ThirdPartPromotionInfo> thirdPartPromotionInfos = thirdPartPromotionInfoRepository.findAllById(items.stream().map(FlowDashboardResultDTO::getActivityId).toList());
            Map<Long, String> aid2Name = thirdPartPromotionInfos.stream().collect(Collectors.toMap(ThirdPartPromotionInfo::getPromotionId, ThirdPartPromotionInfo::getPromotionName));
            items.forEach(item -> item.setActivityName(aid2Name.get(item.getActivityId())));
        }
        if (dimensionSet.contains(FlowDashboardDimensionEnum.BRAND)) {
            items.forEach(item -> {
                // 为空则未知手机品牌
                item.setBrand(StringUtils.isBlank(item.getBrand()) ? UNKNOWN_BRAND_COLUMN : BRAND_NAME.getOrDefault(item.getBrand().toLowerCase(), item.getBrand()));
            });
        }
        if (dimensionSet.contains(FlowDashboardDimensionEnum.TDP_PLATFORM)) {
            items.forEach(item -> {
                // 为空则未知，非空时，有名称则用名称，没有名称则用原值不处理
                item.setTdpPlatform(StringUtils.isBlank(item.getTdpPlatform()) ? UNKNOWN_COLUMN
                        : TDP_PLATFORM_NAME.getOrDefault(item.getTdpPlatform(), item.getTdpPlatform()));
            });
        }
        if (dimensionSet.contains(FlowDashboardDimensionEnum.PROVINCE)) {
            items.forEach(item -> {
                // 有名称则用名称，没有则用原值不处理
                item.setProvinceName(StringUtils.isBlank(item.getProvince()) ? UNKNOWN_COLUMN
                        : PROVINCE_CITY_NAME.getOrDefault(item.getProvince(), item.getProvince()));
            });
        }
        if (dimensionSet.contains(FlowDashboardDimensionEnum.CITY)) {
            items.forEach(item -> {
                // 有名称则用名称，没有则用原值不处理
                item.setCityName(StringUtils.isBlank(item.getCity()) ? UNKNOWN_COLUMN
                        : PROVINCE_CITY_NAME.getOrDefault(item.getCity(), item.getCity()));
            });
        }
        if (dimensionSet.contains(FlowDashboardDimensionEnum.RTA_ENABLE)) {
            items.forEach(item -> {
                // 该维度为空时有一个默认显示文本提示
                String displayRtaEnable = "--";
                String queryRtaEnable = item.getRtaEnable();
                if (Objects.equals(CommonConstants.EVENT_RTA_ENABLE_TRUE, queryRtaEnable)) {
                    displayRtaEnable = "是";
                } else if (Objects.equals(CommonConstants.EVENT_RTA_ENABLE_FALSE, queryRtaEnable)) {
                    displayRtaEnable = "否";
                }
                // 为空则未知手机品牌
                item.setRtaEnable(displayRtaEnable);
            });
        }
    }


    private void setNullForIgnoreFields(Set<FlowDashboardResultDTO> items, Set<String> displayMetricsSet) {
        Collection<String> metricsShouldRemove = CollectionUtils.removeAll(FlowDashboardMetricsEnum.getAllMetrics(), displayMetricsSet);
        try {
            for (FlowDashboardResultDTO item : items) {
                for (String metrics : metricsShouldRemove) {
                    Field declaredField = item.getClass().getDeclaredField(metrics);
                    declaredField.setAccessible(true);
                    declaredField.set(item, null);
                }
            }
        } catch (Exception e) {
            log.error("process ignore fields failed", e);
        }
    }

    private void setZeroForDisplayFields(Set<FlowDashboardResultDTO> items, Set<FlowDashboardMetricsEnum> metricsEnumSet) {
        try {
            for (FlowDashboardResultDTO item : items) {
                for (FlowDashboardMetricsEnum metricsEnum : metricsEnumSet) {
                    Integer type = metricsEnum.getType();
                    String metrics = metricsEnum.getMetrics();
                    Field declaredField = item.getClass().getDeclaredField(metrics);
                    declaredField.setAccessible(true);
                    // displayMetrics 值为空的就补充为默认值
                    if (declaredField.get(item) == null) {
                        if (Objects.equals(type, FlowDashboardMetricsEnum.TYPE_COUNT) && !"orderAmount".equals(metrics)) {
                            declaredField.set(item, FlowDashboardMetricsEnum.TYPE_COUNT_DEFAULT);
                        } else {
                            declaredField.set(item, FlowDashboardMetricsEnum.TYPE_RATE_DEFAULT);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("process display fields failed", e);
        }
    }

    /**
     * 处理 FlowDashboardMetricsEnum type=2的比例类型的数据，根据查询的 MetricsQueryIdEnum 来填充相关的数据
     *
     * @param items
     * @param metricsQueryIdEnums
     */
    private void calRates(Set<FlowDashboardResultDTO> items, Set<MetricsQueryIdEnum> metricsQueryIdEnums) {
        items.forEach(item -> {
            BigDecimal click = item.getClick() != null ? BigDecimal.valueOf(item.getClick()) : BigDecimal.ZERO;
            BigDecimal fraudClickFiltered = item.getFraudClickFiltered() != null ? BigDecimal.valueOf(item.getFraudClickFiltered()) : BigDecimal.ZERO;
            BigDecimal clickAddFraudClickFiltered = click.add(fraudClickFiltered);
            BigDecimal rtaRequestCount = item.getRtaRequestCount() != null ? BigDecimal.valueOf(item.getRtaRequestCount()) : BigDecimal.ZERO;
            BigDecimal fraudRtaFiltered = item.getFraudRtaFiltered() !=null ? BigDecimal.valueOf(item.getFraudRtaFiltered()) : BigDecimal.ZERO;
            BigDecimal rtaRequestTotal = rtaRequestCount.add(fraudRtaFiltered);
            BigDecimal rtaReallySend = item.getRtaFraudMarkSend() != null ? BigDecimal.valueOf(item.getRtaFraudMarkSend()) : BigDecimal.ZERO;
            BigDecimal conv = item.getConv() != null ? BigDecimal.valueOf(item.getConv()) : BigDecimal.ZERO;
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.NORMAL)) {
                item.setReqIdFilledRate(divideMetrics(item.getReqIdFilled(), click));
                item.setFraudClickFilteredRate(divideMetrics(item.getFraudClickFiltered(), clickAddFraudClickFiltered));
                item.setParamNullClickFilterRate(divideMetrics(item.getParamNullClickFilter(), clickAddFraudClickFiltered));
                item.setBrandClickFilterRate(divideMetrics(item.getBrandClickFilter(), clickAddFraudClickFiltered));
                item.setFraudRtaFilteredRate(divideMetrics(item.getFraudRtaFiltered(), rtaRequestTotal));
                item.setRtaBidRate(divideMetrics(item.getRtaBidCount(), rtaRequestTotal));
                item.setRtaBidConvRate(divideMetrics(item.getRtaBidConv(), conv));
                item.setRtaBidClickLoseRate(Objects.equals(CommonConstants.EVENT_RTA_ENABLE_TRUE, item.getRtaEnable()) ? BigDecimal.ONE.subtract(divideMetrics(item.getRtaBidClick(), click)) : BigDecimal.ZERO);
                item.setRtaBidConvLoseRate(Objects.equals(CommonConstants.EVENT_RTA_ENABLE_TRUE, item.getRtaEnable()) ? BigDecimal.ONE.subtract(divideMetrics(item.getRtaBidConv(), conv)) : BigDecimal.ZERO);
                item.setRtaExceptionRate(divideMetrics(item.getRtaExceptionCount(), rtaRequestTotal));
                item.setRtaBidClickRate(divideMetrics(item.getRtaBidClick(), click));
                item.setConvRate(divideMetrics(item.getConv(), click));
                item.setParamNullRtaFilterRate(divideMetrics(item.getParamNullRtaFilter(), rtaRequestTotal));
                item.setBrandRtaFilterRate(divideMetrics(item.getBrandRtaFilter(), rtaRequestTotal));

            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.DISCARD_CLICK)) {
                // nothing
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.EXCEPTION_FILTER_CLICK)) {
                item.setFormatErrorDeviceIdClickFilterRate(divideMetrics(item.getFormatErrorDeviceIdClickFilter(), clickAddFraudClickFiltered));
                item.setValueErrorDarkDeviceIdClickFilterRate(divideMetrics(item.getValueErrorDarkDeviceIdClickFilter(), clickAddFraudClickFiltered));
                item.setUnexpectedOsClickFilterRate(divideMetrics(item.getUnexpectedOsClickFilter(), clickAddFraudClickFiltered));
                item.setUnexpectedDeviceClickFilterRate(divideMetrics(item.getUnexpectedDeviceClickFilter(), clickAddFraudClickFiltered));
                item.setUnexpectedUaClickFilterRate(divideMetrics(item.getUnexpectedUaClickFilter(), clickAddFraudClickFiltered));
                item.setUnexpectedModelClickFilterRate(divideMetrics(item.getUnexpectedModelClickFilter(), clickAddFraudClickFiltered));
                item.setRepeatClickFilterRate(divideMetrics(item.getRepeatClickFilter(), clickAddFraudClickFiltered));
                item.setRelatedRtaDeviceClickFilterRate(divideMetrics(item.getRelatedRtaDeviceClickFilter(), clickAddFraudClickFiltered));
                item.setActionRateClickFilterRate(divideMetrics(item.getActionRateClickFilter(), clickAddFraudClickFiltered));
                item.setActionRateIpClickFilterRate(divideMetrics(item.getActionRateIpClickFilter(), clickAddFraudClickFiltered));
                item.setActionFeaturesDiffSourceClickFilterRate(divideMetrics(item.getActionFeaturesDiffSourceClickFilter(), clickAddFraudClickFiltered));
                item.setValueErrorDarkDeviceIdRtaFilterRate(divideMetrics(item.getValueErrorDarkDeviceIdClickFilter(), rtaRequestTotal));
                item.setUnexpectedDeviceRtaFilterRate(divideMetrics(item.getUnexpectedDeviceRtaFilter(), rtaRequestTotal));
                item.setFormatErrorDeviceIdRtaFilterRate(divideMetrics(item.getFormatErrorDeviceIdRtaFilter(), rtaRequestTotal));
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.CT_ACTION)) {
                item.setDownloadRate(divideMetrics(item.getDownload(), click));
                item.setActivateRate(divideMetrics(item.getActivate(), click));
                item.setRegisterRate(divideMetrics(item.getRegister(), click));
                item.setAddtocartRate(divideMetrics(item.getAddtocart(), click));
                item.setPurchaseRate(divideMetrics(item.getPurchase(), click));
                item.setCreditRate(divideMetrics(item.getCredit(), click));
                item.setCustomRate(divideMetrics(item.getCustom(), click));
            }
            BigDecimal ctActionActivate = item.getActivate() == null ? BigDecimal.ZERO : new BigDecimal(item.getActivate());
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.RETENTION_CT_ACTION)) {
                item.setDay1retentionRate(divideMetrics(item.getDay1retention(), ctActionActivate));
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.RETENTION3_CT_ACTION)) {
                item.setRetention3Rate(divideMetrics(item.getRetention3(), ctActionActivate));
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.RETENTION7_CT_ACTION)) {
                item.setRetention7Rate(divideMetrics(item.getRetention7(), ctActionActivate));
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.RETENTION14_CT_ACTION)) {
                item.setRetention14Rate(divideMetrics(item.getRetention14(), ctActionActivate));
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.RETENTION30_CT_ACTION)) {
                item.setRetention30Rate(divideMetrics(item.getRetention30(), ctActionActivate));
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.ANTI_RULE)) {
                BigDecimal androidClick = item.getAndroidClick() != null ? BigDecimal.valueOf(item.getAndroidClick()) : BigDecimal.ZERO;
                BigDecimal iosClick = item.getIosClick() != null ? BigDecimal.valueOf(item.getIosClick()) : BigDecimal.ZERO;
                BigDecimal fraudAndroidClickFiltered = item.getFraudAndroidClickFiltered() != null ? BigDecimal.valueOf(item.getFraudAndroidClickFiltered()) : BigDecimal.ZERO;
                BigDecimal fraudIosClickFiltered = item.getFraudIosClickFiltered() != null ? BigDecimal.valueOf(item.getFraudIosClickFiltered()) : BigDecimal.ZERO;

                item.setUaFilledRate(divideMetrics(item.getUaFilled(), click));
                item.setIpFilledRate(divideMetrics(item.getIpFilled(), click));
                item.setAndroidOsFilledRate(divideMetrics(item.getAndroidOsFilled(), androidClick));
                item.setIosOsFilledRate(divideMetrics(item.getIosOsFilled(), iosClick));
                item.setDeviceModelFilledRate(divideMetrics(item.getDeviceModelFilled(), click));
                item.setOsVersionFilledRate(divideMetrics(item.getOsVersionFilled(), click));
                item.setImeiFilledRate(divideMetrics(item.getImeiFilled(), click));
                item.setOaidFilledRate(divideMetrics(item.getOaidFilled(), click));
                item.setOaidMd5FilledRate(divideMetrics(item.getOaidMd5Filled(), click));
                item.setIdfaFilledRate(divideMetrics(item.getIdfaFilled(), click));
                item.setIdfaMd5FilledRate(divideMetrics(item.getIdfaMd5Filled(), click));
                item.setCaidFilledRate(divideMetrics(item.getCaidFilled(), click));
                item.setCaidMd5FilledRate(divideMetrics(item.getCaidMd5Filled(), click));
                item.setAndroidIdFilledRate(divideMetrics(item.getAndroidIdFilled(), click));
                item.setAndroidIdMd5FilledRate(divideMetrics(item.getAndroidIdMd5Filled(), click));
                item.setTsFilledRate(divideMetrics(item.getTsFilled(), click));
                item.setActivityIdFilledRate(divideMetrics(item.getActivityIdFilled(), click));
                item.setAlidFilledRate(divideMetrics(item.getAlidFilled(), click));

                item.setUaFilledMarkRate(divideMetrics(item.getUaFilledMark(), clickAddFraudClickFiltered));
                item.setIpFilledMarkRate(divideMetrics(item.getIpFilledMark(), clickAddFraudClickFiltered));
                item.setAndroidOsFilledMarkRate(divideMetrics(item.getAndroidOsFilledMark(), fraudAndroidClickFiltered.add(androidClick)));
                item.setIosOsFilledMarkRate(divideMetrics(item.getIosOsFilledMark(), fraudIosClickFiltered.add(iosClick)));
                item.setDeviceModelFilledMarkRate(divideMetrics(item.getDeviceModelFilledMark(), clickAddFraudClickFiltered));
                item.setOsVersionFilledMarkRate(divideMetrics(item.getOsVersionFilledMark(), clickAddFraudClickFiltered));
                item.setImeiFilledMarkRate(divideMetrics(item.getImeiFilledMark(), clickAddFraudClickFiltered));
                item.setOaidFilledMarkRate(divideMetrics(item.getOaidFilledMark(), clickAddFraudClickFiltered));
                item.setOaidMd5FilledMarkRate(divideMetrics(item.getOaidMd5FilledMark(), clickAddFraudClickFiltered));
                item.setIdfaFilledMarkRate(divideMetrics(item.getIdfaFilledMark(), clickAddFraudClickFiltered));
                item.setIdfaMd5FilledMarkRate(divideMetrics(item.getIdfaMd5FilledMark(), clickAddFraudClickFiltered));
                item.setCaidFilledMarkRate(divideMetrics(item.getCaidFilledMark(), clickAddFraudClickFiltered));
                item.setCaidMd5FilledMarkRate(divideMetrics(item.getCaidMd5FilledMark(), clickAddFraudClickFiltered));
                item.setAndroidIdFilledMarkRate(divideMetrics(item.getAndroidIdFilledMark(), clickAddFraudClickFiltered));
                item.setAndroidIdMd5FilledMarkRate(divideMetrics(item.getAndroidIdMd5FilledMark(), clickAddFraudClickFiltered));
                item.setTsFilledMarkRate(divideMetrics(item.getTsFilledMark(), click));
                item.setActivityIdFilledMarkRate(divideMetrics(item.getActivityIdFilledMark(), click));
                item.setAlidFilledMarkRate(divideMetrics(item.getAlidFilledMark(), click));

                item.setExceptionClickMarkRate(divideMetrics(item.getExceptionClickMark(), clickAddFraudClickFiltered));
                item.setFormatErrorUaClickMarkRate(divideMetrics(item.getFormatErrorUaClickMark(), clickAddFraudClickFiltered));
                item.setFormatErrorDeviceIdClickMarkRate(divideMetrics(item.getFormatErrorDeviceIdClickMark(), clickAddFraudClickFiltered));
                item.setValueErrorIntranetIpClickMarkRate(divideMetrics(item.getValueErrorIntranetIpClickMark(), clickAddFraudClickFiltered));
                item.setValueErrorDarkIpClickMarkRate(divideMetrics(item.getValueErrorDarkIpClickMark(), clickAddFraudClickFiltered));
                item.setValueErrorDarkDeviceIdClickMarkRate(divideMetrics(item.getValueErrorDarkDeviceIdClickMark(), clickAddFraudClickFiltered));
                item.setValueErrorIpClickMarkRate(divideMetrics(item.getValueErrorIpClickMark(), clickAddFraudClickFiltered));
                item.setValueErrorUaClickMarkRate(divideMetrics(item.getValueErrorUaClickMark(), clickAddFraudClickFiltered));
                item.setStandardDiffUaClickMarkRate(divideMetrics(item.getStandardDiffUaClickMark(), clickAddFraudClickFiltered));
                item.setStandardDiffOsvClickMarkRate(divideMetrics(item.getStandardDiffOsvClickMark(), clickAddFraudClickFiltered));
                item.setStandardDiffModelClickMarkRate(divideMetrics(item.getStandardDiffModelClickMark(), clickAddFraudClickFiltered));
                item.setUnexpectedOsClickMarkRate(divideMetrics(item.getUnexpectedOsClickMark(), clickAddFraudClickFiltered));
                item.setUnexpectedUaClickMarkRate(divideMetrics(item.getUnexpectedUaClickMark(), clickAddFraudClickFiltered));
                item.setUnexpectedDeviceClickMarkRate(divideMetrics(item.getUnexpectedDeviceClickMark(), clickAddFraudClickFiltered));
                item.setUnexpectedModelClickMarkRate(divideMetrics(item.getUnexpectedModelClickMark(), clickAddFraudClickFiltered));
                item.setRelatedMultiUaClickMarkRate(divideMetrics(item.getRelatedMultiUaClickMark(), clickAddFraudClickFiltered));
                item.setRelatedMultiModelClickMarkRate(divideMetrics(item.getRelatedMultiModelClickMark(), clickAddFraudClickFiltered));
                item.setRelatedMultiOsvClickMarkRate(divideMetrics(item.getRelatedMultiOsvClickMark(), clickAddFraudClickFiltered));
                item.setRelatedRtaDeviceClickMarkRate(divideMetrics(item.getRelatedRtaDeviceClickMark(), clickAddFraudClickFiltered));
                item.setRelatedRtaUaClickMarkRate(divideMetrics(item.getRelatedRtaUaClickMark(), clickAddFraudClickFiltered));
                item.setRelatedRtaModelClickMarkRate(divideMetrics(item.getRelatedRtaModelClickMark(), clickAddFraudClickFiltered));
                item.setRelatedRtaIpClickMarkRate(divideMetrics(item.getRelatedRtaIpClickMark(), clickAddFraudClickFiltered));
                item.setRelatedRtaOsvClickMarkRate(divideMetrics(item.getRelatedRtaOsvClickMark(), clickAddFraudClickFiltered));
                item.setRepeatClickMarkRate(divideMetrics(item.getRepeatClickMark(), clickAddFraudClickFiltered));
                item.setActionRateClickClickMarkRate(divideMetrics(item.getActionRateClickClickMark(), clickAddFraudClickFiltered));
                item.setActionRateIpClickMarkRate(divideMetrics(item.getActionRateIpClickMark(), clickAddFraudClickFiltered));
                item.setActionRateDeviceIpClickMarkRate(divideMetrics(item.getActionRateDeviceIpClickMark(), clickAddFraudClickFiltered));
                item.setRtaClickTimeErrorClickMarkRate(divideMetrics(item.getRtaClickTimeErrorClickMark(), clickAddFraudClickFiltered));
                item.setActionFeaturesDiffSourceClickMarkRate(divideMetrics(item.getActionFeaturesDiffSourceClickMark(), clickAddFraudClickFiltered));

                item.setExceptionClickSendRate(divideMetrics(item.getExceptionClickSend(), click));
                item.setFormatErrorUaClickSendRate(divideMetrics(item.getFormatErrorUaClickSend(), click));
                item.setFormatErrorDeviceIdClickSendRate(divideMetrics(item.getFormatErrorDeviceIdClickSend(), click));
                item.setValueErrorIntranetIpClickSendRate(divideMetrics(item.getValueErrorIntranetIpClickSend(), click));
                item.setValueErrorDarkIpClickSendRate(divideMetrics(item.getValueErrorDarkIpClickSend(), click));
                item.setValueErrorDarkDeviceIdClickSendRate(divideMetrics(item.getValueErrorDarkDeviceIdClickSend(), click));
                item.setValueErrorIpClickSendRate(divideMetrics(item.getValueErrorIpClickSend(), click));
                item.setValueErrorUaClickSendRate(divideMetrics(item.getValueErrorUaClickSend(), click));
                item.setStandardDiffUaClickSendRate(divideMetrics(item.getStandardDiffUaClickSend(), click));
                item.setStandardDiffOsvClickSendRate(divideMetrics(item.getStandardDiffOsvClickSend(), click));
                item.setStandardDiffModelClickSendRate(divideMetrics(item.getStandardDiffModelClickSend(), click));
                item.setUnexpectedOsClickRate(divideMetrics(item.getUnexpectedOsClick(), click));
                item.setUnexpectedUaClickRate(divideMetrics(item.getUnexpectedUaClick(), click));
                item.setUnexpectedDeviceClickRate(divideMetrics(item.getUnexpectedDeviceClick(), click));
                item.setUnexpectedModelClickSendRate(divideMetrics(item.getUnexpectedModelClickSend(), click));
                item.setRelatedMultiUaClickSendRate(divideMetrics(item.getRelatedMultiUaClickSend(), click));
                item.setRelatedMultiModelClickSendRate(divideMetrics(item.getRelatedMultiModelClickSend(), click));
                item.setRelatedMultiOsvClickSendRate(divideMetrics(item.getRelatedMultiOsvClickSend(), click));
                item.setRelatedRtaDeviceClickSendRate(divideMetrics(item.getRelatedRtaDeviceClickSend(), click));
                item.setRelatedRtaUaClickSendRate(divideMetrics(item.getRelatedRtaUaClickSend(), click));
                item.setRelatedRtaModelClickSendRate(divideMetrics(item.getRelatedRtaModelClickSend(), click));
                item.setRelatedRtaIpClickSendRate(divideMetrics(item.getRelatedRtaIpClickSend(), click));
                item.setRelatedRtaOsvClickSendRate(divideMetrics(item.getRelatedRtaOsvClickSend(), click));
                item.setRepeatClickRate(divideMetrics(item.getRepeatClick(), click));
                item.setActionRateClickClickSendRate(divideMetrics(item.getActionRateClickClickSend(), click));
                item.setActionRateIpClickSendRate(divideMetrics(item.getActionRateIpClickSend(), click));
                item.setActionRateDeviceIpClickSendRate(divideMetrics(item.getActionRateDeviceIpClickSend(), click));
                item.setRtaClickTimeErrorClickSendRate(divideMetrics(item.getRtaClickTimeErrorClickSend(), click));
                item.setActionFeaturesDiffSourceClickSendRate(divideMetrics(item.getActionFeaturesDiffSourceClickSend(), click));
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.ANTI_RULE_CONV)) {
                item.setExceptionConvSendRate(divideMetrics(item.getExceptionConvSend(), conv));

                item.setTsConvFilledRate(divideMetrics(item.getTsConvFilled(), conv));
                item.setActivityIdConvFilledRate(divideMetrics(item.getActivityIdConvFilled(), conv));
                item.setUaConvFilledRate(divideMetrics(item.getUaConvFilled(), conv));
                item.setIpConvFilledRate(divideMetrics(item.getIpConvFilled(), conv));
                item.setAndroidOsConvFilledRate(divideMetrics(item.getAndroidOsConvFilled(), conv));
                item.setIosOsConvFilledRate(divideMetrics(item.getIosOsConvFilled(), conv));
                item.setDeviceModelConvFilledRate(divideMetrics(item.getDeviceModelConvFilled(), conv));
                item.setOsVersionConvFilledRate(divideMetrics(item.getOsVersionConvFilled(), conv));
                item.setImeiConvFilledRate(divideMetrics(item.getImeiConvFilled(), conv));
                item.setOaidConvFilledRate(divideMetrics(item.getOaidConvFilled(), conv));
                item.setOaidMd5ConvFilledRate(divideMetrics(item.getOaidMd5ConvFilled(), conv));
                item.setAndroidIdConvFilledRate(divideMetrics(item.getAndroidIdConvFilled(), conv));
                item.setAndroidIdMd5ConvFilledRate(divideMetrics(item.getAndroidIdMd5ConvFilled(), conv));
                item.setIdfaConvFilledRate(divideMetrics(item.getIdfaConvFilled(), conv));
                item.setIdfaMd5ConvFilledRate(divideMetrics(item.getIdfaMd5ConvFilled(), conv));
                item.setCaidConvFilledRate(divideMetrics(item.getCaidConvFilled(), conv));
                item.setCaidMd5ConvFilledRate(divideMetrics(item.getCaidMd5ConvFilled(), conv));
                item.setAlidConvFilledRate(divideMetrics(item.getAlidConvFilled(), conv));

                item.setFormatErrorUaConvSendRate(divideMetrics(item.getFormatErrorUaConvSend(), conv));
                item.setFormatErrorDeviceIdConvSendRate(divideMetrics(item.getFormatErrorDeviceIdConvSend(), conv));
                item.setValueErrorIpConvSendRate(divideMetrics(item.getValueErrorIpConvSend(), conv));
                item.setValueErrorDarkIpConvSendRate(divideMetrics(item.getValueErrorDarkIpConvSend(), conv));
                item.setValueErrorDarkDeviceIdConvSendRate(divideMetrics(item.getValueErrorDarkDeviceIdConvSend(), conv));
                item.setValueErrorIntranetIpConvSendRate(divideMetrics(item.getValueErrorIntranetIpConvSend(), conv));
                item.setValueErrorUaConvSendRate(divideMetrics(item.getValueErrorUaConvSend(), conv));
                item.setStandardDiffUaConvSendRate(divideMetrics(item.getStandardDiffUaConvSend(), conv));
                item.setStandardDiffOsvConvSendRate(divideMetrics(item.getStandardDiffOsvConvSend(), conv));
                item.setStandardDiffModelConvSendRate(divideMetrics(item.getStandardDiffModelConvSend(), conv));
                item.setUnexpectedOsConvRate(divideMetrics(item.getUnexpectedOsConv(), conv));
                item.setUnexpectedDeviceConvRate(divideMetrics(item.getUnexpectedDeviceConv(), conv));
                item.setUnexpectedUaConvRate(divideMetrics(item.getUnexpectedUaConv(), conv));
                item.setUnexpectedModelConvSendRate(divideMetrics(item.getUnexpectedModelConvSend(), conv));
                item.setRelatedMultiUaConvSendRate(divideMetrics(item.getRelatedMultiUaConvSend(), conv));
                item.setRelatedMultiModelConvSendRate(divideMetrics(item.getRelatedMultiModelConvSend(), conv));
                item.setRelatedMultiOsvConvSendRate(divideMetrics(item.getRelatedMultiOsvConvSend(), conv));
                item.setRelatedRtaDeviceConvSendRate(divideMetrics(item.getRelatedRtaDeviceConvSend(), conv));
                item.setRelatedRtaUaConvSendRate(divideMetrics(item.getRelatedRtaUaConvSend(), conv));
                item.setRelatedRtaModelConvSendRate(divideMetrics(item.getRelatedRtaModelConvSend(), conv));
                item.setRelatedRtaIpConvSendRate(divideMetrics(item.getRelatedRtaIpConvSend(), conv));
                item.setRelatedRtaOsvConvSendRate(divideMetrics(item.getRelatedRtaOsvConvSend(), conv));
                item.setRepeatClickConvRate(divideMetrics(item.getRepeatClickConv(), conv));
                item.setActionRateClickConvSendRate(divideMetrics(item.getActionRateClickConvSend(), conv));
                item.setActionRateIpConvSendRate(divideMetrics(item.getActionRateIpConvSend(), conv));
                item.setActionRateDeviceIpConvSendRate(divideMetrics(item.getActionRateDeviceIpConvSend(), conv));
                item.setRtaClickTimeErrorConvSendRate(divideMetrics(item.getRtaClickTimeErrorConvSend(), conv));
                item.setActionFeaturesDiffSourceConvSendRate(divideMetrics(item.getActionFeaturesDiffSourceConvSend(), conv));

                BigDecimal convByCtAction = item.getConvByCtAction() != null ? BigDecimal.valueOf(item.getConvByCtAction()) : BigDecimal.ZERO;
            }
            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.ANTI_RULE_RTA)) {
                BigDecimal androidRtaRequest = item.getAndroidRtaRequest() != null ? BigDecimal.valueOf(item.getAndroidRtaRequest()) : BigDecimal.ZERO;
                BigDecimal iosRtaRequest = item.getIosRtaRequest() != null ? BigDecimal.valueOf(item.getIosRtaRequest()) : BigDecimal.ZERO;
                BigDecimal fraudAndroidRtaFiltered = item.getFraudAndroidRtaFiltered() != null ? BigDecimal.valueOf(item.getFraudAndroidRtaFiltered()) : BigDecimal.ZERO;
                BigDecimal fraudIosRtaFiltered = item.getFraudIosRtaFiltered() != null ? BigDecimal.valueOf(item.getFraudIosRtaFiltered()) : BigDecimal.ZERO;
                BigDecimal androidRtaRequestReallySend = item.getAndroidRtaRequestReallySend() != null ? BigDecimal.valueOf(item.getAndroidRtaRequestReallySend()) : BigDecimal.ZERO;
                BigDecimal iosRtaRequestReallySend = item.getIosRtaRequestReallySend() != null ? BigDecimal.valueOf(item.getIosRtaRequestReallySend()) : BigDecimal.ZERO;

                item.setUaFilledRtaRate(divideMetrics(item.getUaFilledRta(), rtaReallySend));
                item.setIpFilledRtaRate(divideMetrics(item.getIpFilledRta(), rtaReallySend));
                item.setAndroidOsFilledRtaRate(divideMetrics(item.getAndroidOsFilledRta(), androidRtaRequestReallySend));
                item.setIosOsFilledRtaRate(divideMetrics(item.getIosOsFilledRta(), iosRtaRequestReallySend));
                item.setDeviceModelFilledRtaRate(divideMetrics(item.getDeviceModelFilledRta(), rtaReallySend));
                item.setOsVersionFilledRtaRate(divideMetrics(item.getOsVersionFilledRta(), rtaReallySend));
                item.setImeiV2FilledRtaRate(divideMetrics(item.getImeiV2FilledRta(), rtaReallySend));
                item.setImeiFilledRtaRate(divideMetrics(item.getImeiFilledRta(), rtaReallySend));
                item.setOaidFilledRtaRate(divideMetrics(item.getOaidFilledRta(), rtaReallySend));
                item.setOaidMd5FilledRtaRate(divideMetrics(item.getOaidMd5FilledRta(), rtaReallySend));
                item.setIdfaFilledRtaRate(divideMetrics(item.getIdfaFilledRta(), rtaReallySend));
                item.setIdfaMd5FilledRtaRate(divideMetrics(item.getIdfaMd5FilledRta(), rtaReallySend));
                item.setCaidFilledRtaRate(divideMetrics(item.getCaidFilledRta(), rtaReallySend));
                item.setCaidMd5FilledRtaRate(divideMetrics(item.getCaidMd5FilledRta(), rtaReallySend));
                item.setAndroidIdMd5FilledRtaRate(divideMetrics(item.getAndroidIdMd5FilledRta(), rtaReallySend));

                item.setUaFilledRtaMarkRate(divideMetrics(item.getUaFilledRtaMark(), rtaRequestTotal));
                item.setIpFilledRtaMarkRate(divideMetrics(item.getIpFilledRtaMark(), rtaRequestTotal));
                item.setAndroidOsFilledRtaMarkRate(divideMetrics(item.getAndroidOsFilledRtaMark(), fraudAndroidRtaFiltered.add(androidRtaRequest)));
                item.setIosOsFilledRtaMarkRate(divideMetrics(item.getIosOsFilledRtaMark(), fraudIosRtaFiltered.add(iosRtaRequest)));
                item.setDeviceModelFilledRtaMarkRate(divideMetrics(item.getDeviceModelFilledRtaMark(), rtaRequestTotal));
                item.setOsVersionFilledRtaMarkRate(divideMetrics(item.getOsVersionFilledRtaMark(), rtaRequestTotal));
                item.setImeiV2FilledRtaMarkRate(divideMetrics(item.getImeiV2FilledRtaMark(), rtaRequestTotal));
                item.setImeiFilledRtaMarkRate(divideMetrics(item.getImeiFilledRtaMark(), rtaRequestTotal));
                item.setOaidFilledRtaMarkRate(divideMetrics(item.getOaidFilledRtaMark(), rtaRequestTotal));
                item.setOaidMd5FilledRtaMarkRate(divideMetrics(item.getOaidMd5FilledRtaMark(), rtaRequestTotal));
                item.setIdfaFilledRtaMarkRate(divideMetrics(item.getIdfaFilledRtaMark(), rtaRequestTotal));
                item.setIdfaMd5FilledRtaMarkRate(divideMetrics(item.getIdfaMd5FilledRtaMark(), rtaRequestTotal));
                item.setCaidFilledRtaMarkRate(divideMetrics(item.getCaidFilledRtaMark(), rtaRequestTotal));
                item.setCaidMd5FilledRtaMarkRate(divideMetrics(item.getCaidMd5FilledRtaMark(), rtaRequestTotal));
                item.setAndroidIdMd5FilledRtaMarkRate(divideMetrics(item.getAndroidIdMd5FilledRtaMark(), rtaRequestTotal));

                item.setExceptionRtaMarkRate(divideMetrics(item.getExceptionRtaMark(), rtaRequestTotal));
                item.setFormatErrorUaRtaMarkRate(divideMetrics(item.getFormatErrorUaRtaMark(), rtaRequestTotal));
                item.setFormatErrorDeviceIdRtaMarkRate(divideMetrics(item.getFormatErrorDeviceIdRtaMark(), rtaRequestTotal));
                item.setValueErrorIntranetIpRtaMarkRate(divideMetrics(item.getValueErrorIntranetIpRtaMark(), rtaRequestTotal));
                item.setValueErrorDarkIpRtaMarkRate(divideMetrics(item.getValueErrorDarkIpRtaMark(), rtaRequestTotal));
                item.setValueErrorDarkDeviceIdRtaMarkRate(divideMetrics(item.getValueErrorDarkDeviceIdClickMark(), rtaRequestTotal));
                item.setValueErrorIpRtaMarkRate(divideMetrics(item.getValueErrorIpRtaMark(), rtaRequestTotal));
                item.setValueErrorUaRtaMarkRate(divideMetrics(item.getValueErrorUaRtaMark(), rtaRequestTotal));
                item.setStandardDiffUaRtaMarkRate(divideMetrics(item.getStandardDiffUaRtaMark(), rtaRequestTotal));
                item.setStandardDiffOsvRtaMarkRate(divideMetrics(item.getStandardDiffOsvRtaMark(), rtaRequestTotal));
                item.setStandardDiffModelRtaMarkRate(divideMetrics(item.getStandardDiffModelRtaMark(), rtaRequestTotal));
                item.setUnexpectedOsRtaMarkRate(divideMetrics(item.getUnexpectedOsRtaMark(), rtaRequestTotal));
                item.setUnexpectedUaRtaMarkRate(divideMetrics(item.getUnexpectedUaRtaMark(), rtaRequestTotal));
                item.setUnexpectedDeviceRtaMarkRate(divideMetrics(item.getUnexpectedDeviceRtaMark(), rtaRequestTotal));
                item.setUnexpectedModelRtaMarkRate(divideMetrics(item.getUnexpectedModelRtaMark(), rtaRequestTotal));
                item.setRelatedMultiUaRtaMarkRate(divideMetrics(item.getRelatedMultiUaRtaMark(), rtaRequestTotal));
                item.setRelatedMultiModelRtaMarkRate(divideMetrics(item.getRelatedMultiModelRtaMark(), rtaRequestTotal));
                item.setRelatedMultiOsvRtaMarkRate(divideMetrics(item.getRelatedMultiOsvRtaMark(), rtaRequestTotal));
                item.setActionRateDeviceIpRtaMarkRate(divideMetrics(item.getActionRateDeviceIpRtaMark(), rtaRequestTotal));

                item.setExceptionRtaSendRate(divideMetrics(item.getExceptionRtaSend(), rtaReallySend));
                item.setFormatErrorUaRtaSendRate(divideMetrics(item.getFormatErrorUaRtaSend(), rtaReallySend));
                item.setFormatErrorDeviceIdRtaSendRate(divideMetrics(item.getFormatErrorDeviceIdRtaSend(), rtaReallySend));
                item.setValueErrorIntranetIpRtaSendRate(divideMetrics(item.getValueErrorIntranetIpRtaSend(), rtaReallySend));
                item.setValueErrorDarkIpRtaSendRate(divideMetrics(item.getValueErrorDarkIpRtaSend(), rtaReallySend));
                item.setValueErrorDarkDeviceIdRtaSendRate(divideMetrics(item.getValueErrorDarkDeviceIdRtaSend(), rtaReallySend));
                item.setValueErrorIpRtaSendRate(divideMetrics(item.getValueErrorIpRtaSend(), rtaReallySend));
                item.setValueErrorUaRtaSendRate(divideMetrics(item.getValueErrorUaRtaSend(), rtaReallySend));
                item.setStandardDiffUaRtaSendRate(divideMetrics(item.getStandardDiffUaRtaSend(), rtaReallySend));
                item.setStandardDiffOsvRtaSendRate(divideMetrics(item.getStandardDiffOsvRtaSend(), rtaReallySend));
                item.setStandardDiffModelRtaSendRate(divideMetrics(item.getStandardDiffModelRtaSend(), rtaReallySend));
                item.setUnexpectedOsRtaRate(divideMetrics(item.getUnexpectedOsRta(), rtaReallySend));
                item.setUnexpectedUaRtaRate(divideMetrics(item.getUnexpectedUaRta(), rtaReallySend));
                item.setUnexpectedDeviceRtaRate(divideMetrics(item.getUnexpectedDeviceRta(), rtaReallySend));
                item.setUnexpectedModelRtaSendRate(divideMetrics(item.getUnexpectedModelRtaSend(), rtaReallySend));
                item.setRelatedMultiUaRtaSendRate(divideMetrics(item.getRelatedMultiUaRtaSend(), rtaReallySend));
                item.setRelatedMultiModelRtaSendRate(divideMetrics(item.getRelatedMultiModelRtaSend(), rtaReallySend));
                item.setRelatedMultiOsvRtaSendRate(divideMetrics(item.getRelatedMultiOsvRtaSend(), rtaReallySend));
                item.setActionRateDeviceIpRtaSendRate(divideMetrics(item.getActionRateDeviceIpRtaSend(), rtaReallySend));
            }

            if (metricsQueryIdEnums.contains(MetricsQueryIdEnum.CONV_TIME_FOLLOW)) {
                item.setConvTimeFollowCloselySendRate1s(divideMetrics(item.getConvTimeFollowCloselySend1s(), conv));
                item.setConvTimeFollowCloselySendRate(divideMetrics(item.getConvTimeFollowCloselySend(), conv));
                item.setConvTimeFollowCloselySendRate10s(divideMetrics(item.getConvTimeFollowCloselySend10s(), conv));
                item.setConvTimeFollowCloselySendRate20s(divideMetrics(item.getConvTimeFollowCloselySend20s(), conv));
                item.setConvTimeFollowCloselySendRate30s(divideMetrics(item.getConvTimeFollowCloselySend30s(), conv));
                item.setConvTimeFollowCloselySendRate1m(divideMetrics(item.getConvTimeFollowCloselySend1m(), conv));
                item.setConvTimeFollowCloselySendRate2m(divideMetrics(item.getConvTimeFollowCloselySend2m(), conv));
                item.setConvTimeFollowCloselySendRate5m(divideMetrics(item.getConvTimeFollowCloselySend5m(), conv));
                item.setConvTimeFollowCloselySendRate10m(divideMetrics(item.getConvTimeFollowCloselySend10m(), conv));
                item.setConvTimeFollowCloselySendRate30m(divideMetrics(item.getConvTimeFollowCloselySend30m(), conv));
                item.setConvTimeFollowCloselySendRate1h(divideMetrics(item.getConvTimeFollowCloselySend1h(), conv));
                item.setConvTimeFollowCloselySendRate2h(divideMetrics(item.getConvTimeFollowCloselySend2h(), conv));
                item.setConvTimeFollowCloselySendRate4h(divideMetrics(item.getConvTimeFollowCloselySend4h(), conv));
                item.setConvTimeFollowCloselySendRate8h(divideMetrics(item.getConvTimeFollowCloselySend8h(), conv));
                item.setConvTimeFollowCloselySendRate12h(divideMetrics(item.getConvTimeFollowCloselySend12h(), conv));
                item.setConvTimeFollowCloselySendRate24h(divideMetrics(item.getConvTimeFollowCloselySend24h(), conv));
                item.setConvTimeFollowCloselySendRate2d(divideMetrics(item.getConvTimeFollowCloselySend2d(), conv));
                item.setConvTimeFollowCloselySendRate4d(divideMetrics(item.getConvTimeFollowCloselySend4d(), conv));
                item.setConvTimeFollowCloselySendRate7d(divideMetrics(item.getConvTimeFollowCloselySend7d(), conv));
                item.setConvTimeFollowCloselySendRate14d(divideMetrics(item.getConvTimeFollowCloselySend14d(), conv));
                item.setConvTimeFollowCloselySendRate30d(divideMetrics(item.getConvTimeFollowCloselySend30d(), conv));
                item.setConvTimeFollowCloselySendRateMoreThan30d(divideMetrics(item.getConvTimeFollowCloselySendMoreThan30d(), conv));
            }
        });
    }

    private BigDecimal divideMetrics(Long metrics, BigDecimal click) {
        if (click == null || metrics == null) {
            return BigDecimal.ZERO;
        }
        return click.longValue() > 0 ? BigDecimal.valueOf(metrics).divide(click, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;
    }

    @Override
    public Set<String> getDownloadDashboardIgnoreField(FlowDashboardQueryDTO dto, LoginUser loginUser) {
        RoleEnum currentRole = loginUser.getCurrentRole();
        Set<String> allMetrics = FlowDashboardMetricsEnum.getAllMetrics();
        Collection<String> ignoreMetrics = CollectionUtils.removeAll(allMetrics, dto.getDisplayMetricsSet());
        Set<String> ignoreFields = new HashSet<>(ignoreMetrics);
        for (FlowDashboardDimensionEnum dimensionEnum : dto.getDimensionExceptSet()) {
            ignoreFields.addAll(dimensionEnum.getMappingFields());
        }
        if (dto.getTimeGranularity().equals(TimeGranularityEnum.ALL)) {
            ignoreFields.add("dateTime");
        }
        boolean canSeeChannelName = RoleEnum.identifyBd(currentRole) || RoleEnum.identifyAdmin(currentRole);
        if (!canSeeChannelName) {
            ignoreFields.addAll(List.of("channelName", "channelCode"));
        }
        // 下载时不显示id
        ignoreFields.add("province");
        ignoreFields.add("city");
        return ignoreFields;
    }

    @Override
    public Page<FlowDashboardResultDTO> getFlowChart(FlowChartQueryDTO chartQueryDTO, LoginUser loginUser) {
        FlowDashboardQueryDTO dashboardQueryDTO = new FlowDashboardQueryDTO();
        dashboardQueryDTO.setPageNum(0);
        dashboardQueryDTO.setPageSize(999999);
        dashboardQueryDTO.setStartTime(chartQueryDTO.getStartTime());
        dashboardQueryDTO.setEndTime(chartQueryDTO.getEndTime());
        if ("time".equals(chartQueryDTO.getDimension())) {
            dashboardQueryDTO.setTimeGranularity(chartQueryDTO.getTimeGranularity());
        } else {
            dashboardQueryDTO.setTimeGranularity(TimeGranularityEnum.ALL);
            switch (chartQueryDTO.getDimension()) {
                case "groupByOsType" -> dashboardQueryDTO.setGroupByOsType(true);
                case "groupBySponsor" -> dashboardQueryDTO.setGroupBySponsor(true);
                case "groupByChannel" -> dashboardQueryDTO.setGroupByChannel(true);
                case "groupByAid" -> dashboardQueryDTO.setGroupByAid(true);
                case "groupByBrand" -> dashboardQueryDTO.setGroupByBrand(true);
                case "groupByTdpPlatform" -> dashboardQueryDTO.setGroupByTdpPlatform(true);
                case "groupByProvince" -> dashboardQueryDTO.setGroupByProvince(true);
                case "groupByCity" -> dashboardQueryDTO.setGroupByCity(true);
                case "groupByRtaEnable" -> dashboardQueryDTO.setGroupByRtaEnable(true);
            }
        }
        //当用户勾选rta点击偷跑率 or rta转化偷跑率中的任一个活多个指标时，查询时需要带上“RTA是否启动”的维度
        if (chartQueryDTO.getDisplayMetricsSet().contains("rtaBidClickLoseRate") || chartQueryDTO.getDisplayMetricsSet().contains("rtaBidConvLoseRate")) {
            dashboardQueryDTO.setGroupByRtaEnable(true);
        }
        dashboardQueryDTO.setOs(chartQueryDTO.getOs());
        dashboardQueryDTO.setActivityIds(chartQueryDTO.getActivityIds());
        dashboardQueryDTO.setChannelDids(chartQueryDTO.getChannelDids());
        dashboardQueryDTO.setDisplayAllZeroLine(chartQueryDTO.getDisplayAllZeroLine());
        dashboardQueryDTO.setDisplayMetricsSet(Set.copyOf(chartQueryDTO.getDisplayMetricsSet()));
        dashboardQueryDTO.setCtActionSelector(chartQueryDTO.getCtActionSelector());
        Page<FlowDashboardResultDTO> flowDashboard = getFlowDashboard(dashboardQueryDTO, loginUser);
        if (!"time".equals(chartQueryDTO.getDimension())) {
            // 按照第一个指标排序，Page<FlowDashboardResultDTO>.getContent()方法本身是一个不可变的集合
            String metrics = chartQueryDTO.getDisplayMetricsSet().get(0);
            List<FlowDashboardResultDTO> sorted = new ArrayList<>(flowDashboard.getContent());
            sorted.sort((o1, o2) -> {
                try {
                    Field field1 = o1.getClass().getDeclaredField(metrics);
                    field1.setAccessible(true);
                    BigDecimal val1 = new BigDecimal(field1.get(o1).toString());
                    Field field2 = o2.getClass().getDeclaredField(metrics);
                    field2.setAccessible(true);
                    BigDecimal val2 = new BigDecimal(field2.get(o2).toString());
                    return val2.compareTo(val1);
                } catch (Exception e) {
                    log.warn("getFlowChart sort by metric got exception, ", e);
                    return 0;
                }
            });
            flowDashboard = new PageImpl<>(sorted, flowDashboard.getPageable(), flowDashboard.getTotalElements());
        }
        return flowDashboard;
    }

}
