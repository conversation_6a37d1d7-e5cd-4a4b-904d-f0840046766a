package com.youdao.ead.pacioli.service.impl.async;

import com.youdao.ead.pacioli.core.druid.DruidHttpClient;
import com.youdao.ead.pacioli.core.druid.FlowDashboardQueryResult;
import com.youdao.ead.pacioli.dto.antifraud.FlowDashboardQueryDTO;
import com.youdao.ead.pacioli.dto.antifraud.FlowDashboardResultDTO;
import com.youdao.ead.pacioli.enums.antifraud.MetricsQueryIdEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static com.youdao.ead.pacioli.enums.ZhixuanCtActionEnum._RETENTION_3;

/**
 * <AUTHOR>
 * @date 2024/8/23
 */
public class Retention3CtActionQueryCallable extends AbstractQueryCallable {
    public Retention3CtActionQueryCallable(ConcurrentHashMap<String, FlowDashboardResultDTO> publicSummaryMap, FlowDashboardQueryDTO flowDashboardQueryDTO, boolean filterSponsor, Set<Long> relateSponsorIds, Set<String> aggregators, DruidHttpClient druidHttpClient) {
        super(publicSummaryMap, flowDashboardQueryDTO, filterSponsor, relateSponsorIds, aggregators, druidHttpClient);
    }

    @Override
    protected MetricsQueryIdEnum metricsQueryIdEnum() {
        return MetricsQueryIdEnum.RETENTION3_CT_ACTION;
    }

    @Override
    protected int retentionDaysDiff() {
        return 3;
    }
    @Override
    protected void doOneRowSummary(FlowDashboardQueryResult.Event event, FlowDashboardResultDTO dto) throws Exception {
        String ctAction = event.getCtAction();
        long retentionDays = event.getRetentionDays();
        long conv = event.getConv();
        if (StringUtils.isNotBlank(ctAction)) {
            if (_RETENTION_3.getVals().contains(ctAction)) {
                if (retentionDays == 3) {
                    dto.setRetention3(_long(dto.getRetention3()) + conv);
                }
            }
        }
    }
}
