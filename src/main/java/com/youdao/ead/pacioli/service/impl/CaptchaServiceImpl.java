package com.youdao.ead.pacioli.service.impl;

import com.youdao.ead.pacioli.constants.ResponseType;
import com.youdao.ead.pacioli.core.exception.CustomException;
import com.youdao.ead.pacioli.dto.login.CaptchaResponseDTO;
import com.youdao.ead.pacioli.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

/**
 * <AUTHOR>
 * @see <a href="https://confluence.inner.youdao.com/pages/viewpage.action?pageId=2525142">短信验证服务接口文档</a>
 */
@Service
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {

    private final RestTemplate restTemplate;
    @Value("${captcha.url}")
    private String captchaHost;
    private final static String SEND_CODE_API = "/captcha/msg/";
    private final static String VALIDATE_CODE_API = "/captcha/validate/msg";
    private final static int API_OK_CODE = 200;
    private final static String API_OK_STATUS = "success";
    private final static String VALIDATE_API_MOBILE_PARAM_NAME = "mobile";
    private final static String VALIDATE_API_CONTENT_PARAM_NAME = "content";

    @Override
    public String sendCaptchaCode(String phoneNumber) {
        ResponseEntity<CaptchaResponseDTO> response = restTemplate.getForEntity(captchaHost + SEND_CODE_API + phoneNumber, CaptchaResponseDTO.class);
        if (isRequestOk(response)) {
            return response.getBody().getData().getText();
        } else {
            if (response.getBody().getCode() == API_OK_CODE) {
                throw new CustomException(ResponseType.CAPTCHA_SERVER_ERROR, response.getBody().getData().getText());
            } else {
                throw new CustomException(ResponseType.CAPTCHA_SERVER_ERROR, response.getBody().getMessage());
            }
        }
    }

    @Override
    public boolean verifyCaptchaCode(String phoneNumber, String captchaCode) {
        MultiValueMap<String, String> requestParams = new LinkedMultiValueMap<>();
        requestParams.add(VALIDATE_API_MOBILE_PARAM_NAME, phoneNumber);
        requestParams.add(VALIDATE_API_CONTENT_PARAM_NAME, captchaCode);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(requestParams, httpHeaders);
        ResponseEntity<CaptchaResponseDTO> response = restTemplate.postForEntity(captchaHost + VALIDATE_CODE_API, httpEntity, CaptchaResponseDTO.class);
        return isRequestOk(response);
    }

    private boolean isRequestOk(ResponseEntity<CaptchaResponseDTO> response) {
        return response.getStatusCode().is2xxSuccessful()
                && Objects.nonNull(response.getBody())
                && isApiOk(response.getBody());
    }

    private boolean isApiOk(CaptchaResponseDTO captchaResponseDTO) {
        return Objects.nonNull(captchaResponseDTO.getData())
                && API_OK_CODE == captchaResponseDTO.getCode()
                && API_OK_STATUS.equals(captchaResponseDTO.getData().getStatus());
    }

}
