package com.youdao.ead.pacioli.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @date 2023/4/4
 */
public class BIgDecimalUtil {

    public static final DecimalFormat PERCENT_DECIMAL_FORMAT = new DecimalFormat("0.00%");

    /**
     * 百分数（%前的数字）转为BigDecimal
     * @param percentNum
     * @return
     */
    public static BigDecimal percentNum2Decimal(BigDecimal percentNum) {
        return percentNum.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
    }
}
