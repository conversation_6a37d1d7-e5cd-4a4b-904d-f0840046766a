package com.youdao.ead.pacioli.util;

import com.youdao.ead.pacioli.bo.LoginUser;
import com.youdao.ead.pacioli.constants.ResponseType;
import com.youdao.ead.pacioli.core.exception.CustomException;
import com.youdao.ead.pacioli.enums.RoleEnum;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class SecurityUtil {
    /**
     * 用户角色ID
     **/
    public static Long getUserRoleRelationId() {
        try {
            return getLoginUser().getUserRoleRelationId();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getName();
        } catch (Exception e) {
            throw new CustomException(ResponseType.TOKEN_INVALID, "获取用户账户异常");
        }
    }

    /**
     * 必须在controller层调用该方法，获取登录用户信息<p>
     * service可能是由内部服务直接调用，导致该值为空，发生错误
     *
     * @return 登录用户信息
     */
    public static RoleEnum getCurrentRole() {
        return getLoginUser().getCurrentRole();
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new CustomException(ResponseType.TOKEN_INVALID, "用户未登录");
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }


    public static Boolean checkCurrentRole(RoleEnum roleEnum) {
        return roleEnum.getRoleKey().equals(SecurityUtil.getCurrentRole().getRoleKey());
    }

    public static Boolean checkCurrentRole(RoleEnum[] roles) {
        LoginUser loginUser = SecurityUtil.getLoginUser();
        if (roles.length == 0 || Objects.isNull(loginUser)) {
            return false;
        }
        return Arrays.stream(roles)
                .anyMatch(roleEnum -> roleEnum.equals(SecurityUtil.getCurrentRole() == null ? null : SecurityUtil.getCurrentRole()));
    }

    public static boolean isLogin() {
        return Objects.nonNull(getAuthentication());
    }
}
