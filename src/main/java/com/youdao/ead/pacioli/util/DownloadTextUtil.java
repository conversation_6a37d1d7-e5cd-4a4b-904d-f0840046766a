package com.youdao.ead.pacioli.util;

import com.youdao.ead.pacioli.dto.channel.PagedChannelInfoDTO;
import com.youdao.ead.pacioli.dto.datareport.view.DataViewItemDTO;
import com.youdao.ead.pacioli.dto.subtask.SubTaskItemDTO;
import com.youdao.ead.pacioli.dto.task.TaskItemDTO;
import com.youdao.ead.pacioli.entity.pacioli.ConversionAction;
import com.youdao.ead.pacioli.enums.OsEnum;
import com.youdao.ead.pacioli.enums.TaskStateEnum;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static com.youdao.ead.pacioli.constants.CommonConstants.DOWNLOAD_DATE_PATTERN;
import static com.youdao.ead.pacioli.constants.CommonConstants.EFFECTIVE_CONVERSION_ACTION_FIELD_DEFAULT;

/**
 * <AUTHOR>
 * @date 2022/11/2
 */
public class DownloadTextUtil {

    public static String downloadNullableText(Number num, String suffix) {
        return num != null ? num + suffix : "--";
    }

    public static String downloadNullableText(String nullableText) {
        return nullableText != null ? nullableText : "--";
    }

    public static String taskNameWithIdText(TaskItemDTO dto) {
        return dto.getTaskName() + "\nID：" + dto.getTaskId();
    }

    public static String promotionProductNameWithOsText(TaskItemDTO dto) {
        return dto.getPromotionProductName() + "\n" + osText(dto);
    }

    public static String osText(TaskItemDTO dto) {
        return OsEnum.withId(dto.getOs()).getText();
    }

    public static String viewZhixuanText(TaskItemDTO dto) {
        return dto.getViewZhixuan() ? "是" : "否";
    }

    public static String taskStateText(TaskItemDTO dto) {
        return TaskStateEnum.withId(TaskStateEnum.convertId2WebId(dto.getTaskState())).getText();
    }

    public static String effectiveDateText(TaskItemDTO dto) {
        return dto.getEffectiveDate().format(DOWNLOAD_DATE_PATTERN);
    }

    public static String taskNameWithIdText(SubTaskItemDTO dto) {
        return dto.getTaskName() + "\nID：" + dto.getTaskId();
    }

    public static String subTaskNameWithIdText(SubTaskItemDTO dto) {
        return dto.getSubTaskName() + "\nID：" + dto.getSubTaskId();
    }

    public static String effectiveDateText(SubTaskItemDTO dto) {
        return dto.getEffectiveDate().format(DOWNLOAD_DATE_PATTERN);
    }

    public static String stateText(SubTaskItemDTO dto) {
        return dto.getState().getText();
    }

    public static String dateText(DataViewItemDTO dto) {
        return localDateText(dto.getDate());
    }

    private static String localDateText(LocalDate date) {
        if (date != null) {
            return date.format(DOWNLOAD_DATE_PATTERN);
        } else {
            return "--";
        }
    }

    public static String subTaskNameWithIdText(DataViewItemDTO dto) {
        return dto.getSubTaskName() + "\nID：" + dto.getSubTaskId();
    }

    public static String effectiveConversionActionName(DataViewItemDTO dto, List<ConversionAction> list) {
        return effectiveConversionActionName(dto.getEffectiveConversionActionId(), list);
    }

    private static String effectiveConversionActionName(Long conversionActionId, List<ConversionAction> list) {
        ConversionAction cur = null;
        for (ConversionAction conversionAction : list) {
            if (conversionAction.getId().equals(conversionActionId)) {
                cur = conversionAction;
                break;
            }
        }
        return cur == null ? EFFECTIVE_CONVERSION_ACTION_FIELD_DEFAULT : cur.getReflectField();
    }

    public static String gradeText(PagedChannelInfoDTO dto) {
        return dto.getGrade().name();
    }

    public static String stateText(PagedChannelInfoDTO dto) {
        return dto.getState().getText();
    }
}
