package com.youdao.ead.pacioli.enums;

/**
 * <AUTHOR>
 */
public enum FieldNameEnum {
    /**
     * 任务
     */
    TASK_NAME("任务名称"),
    TASK_ZHIXUAN_USERNAME("智选账号"),
    TASK_PROMOTION_PRODUCT_NAME("推广产品"),
    TASK_OS("推广产品客户端"),
    TASK_REBATE("返点"),
    TASK_PREVIEW_LINK("预览链接"),
    TASK_CONVERSION_TYPE("转化类型"),
    TASK_KPI("后端KPI"),
    TASK_ORIENTATION_INFO("地域定向"),
    TASK_VIEW_ZHIXUAN("查看智选后台"),
    TASK_CUSTOMER_CPA("客户CPA价格"),
    TASK_EFFECTIVE_DATE("预计上线日期"),
    TASK_STATE("状态"),
    TASK_CUSTOMER_PAYMENT("客户款项"),
    TASK_INTEGRATION_METHOD("对接方式"),
    TASK_INTEGRATION_METHOD_CUSTOM_TEXT("自定义对接方式"),
    SUB_TASK_NAME("子任务名称"),
    SUB_TASK_PROMOTION_LINK("推广链接"),
    SUB_TASK_TASK_AMOUNT("任务量"),
    SUB_TASK_EFFECTIVE_DATE("生效日期"),
    SUB_TASK_CHANNEL_NAME("渠道"),
    SUB_TASK_CHANNEL_CPA_UNIT_PRICE("CPA渠道单价"),
    SUB_TASK_BD("BD人员"),
    SUB_TASK_BD_FEEDBACK("BD反馈信息"),
    SUB_TASK_STATE("状态"),
    DATA_REPORT_DATE("日期"),
    DATA_REPORT_ACTIVE_NUM("激活"),
    DATA_REPORT_ADVISOR_FEEDBACK("顾问反馈"),
    DATA_REPORT_OPERATOR_FEEDBACK("运营反馈"),
    CHANNEL_NAME("渠道名称"),
    CHANNEL_CODE("渠道代号"),
    CHANNEL_ANNUAL_INCOME_SCALE("年收入规模"),
    CHANNEL_CORE_RESOURCE("核心资源组成列举"),
    CHANNEL_HISTORY_COOPERATION_CASE("过往合作过的优势客户以及规模"),
    CHANNEL_HOPE_COOPERATION_TYPE("希望合作类型"),
    CHANNEL_GRADE("评级"),
    CHANNEL_REASON("原因"),
    CHANNEL_STATE("状态");


    private final String name;


    FieldNameEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
