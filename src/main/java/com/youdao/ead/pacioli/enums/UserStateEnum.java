package com.youdao.ead.pacioli.enums;

import com.youdao.ead.pacioli.constants.ResponseType;
import com.youdao.ead.pacioli.core.exception.CustomException;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum UserStateEnum {

    NORMAL((byte) 0),

    DISABLED((byte) 1);

    private final Byte id;

    UserStateEnum(Byte id) {
        this.id = id;
    }

    public static UserStateEnum withId(Byte id) {
        for (UserStateEnum userStateEnum : UserStateEnum.values()) {
            if (userStateEnum.id.equals(id)) {
                return userStateEnum;
            }
        }
        throw new CustomException(ResponseType.ENUM_PARAM_ERROR);
    }

    public Byte getId() {
        return id;
    }
}
