package com.youdao.ead.pacioli.enums.antifraud;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
public enum AntiRuleScopeEnum {
    CLICK(Set.of(1,3)),
    RTA_REQUEST(Set.of(2,3)),
    ;

    /**
     * 可见范围：1点击类，2请求类，3点击+请求
     */
    private final Set<Integer> scopes;

    AntiRuleScopeEnum(Set<Integer> scopes) {
        this.scopes = scopes;
    }

    public Set<Integer> getScopes() {
        return scopes;
    }
}
