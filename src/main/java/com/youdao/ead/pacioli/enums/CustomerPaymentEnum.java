package com.youdao.ead.pacioli.enums;

import com.youdao.ead.pacioli.core.exception.CustomException;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.youdao.ead.pacioli.constants.ResponseType.ENUM_PARAM_ERROR;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
@Getter
public enum CustomerPaymentEnum {
    /**
     * 客户款项枚举值
     */
    PREPAID("预付"),
    POSTPAID("后付"),
    MONTHLY("月结");

    /**
     * 前端字段
     */
    private final String text;

    CustomerPaymentEnum(String text) {
        this.text = text;
    }

    public static CustomerPaymentEnum withText(String text) {
        for (CustomerPaymentEnum customerPaymentEnum : CustomerPaymentEnum.values()) {
            if (Objects.equals(customerPaymentEnum.text, text)) {
                return customerPaymentEnum;
            }
        }
        throw new CustomException(ENUM_PARAM_ERROR);
    }

    public static String getName(String text) {
        return withText(text).name();
    }

    public static CustomerPaymentEnum withName(String name) {
        for (CustomerPaymentEnum customerPaymentEnum : CustomerPaymentEnum.values()) {
            if (Objects.equals(customerPaymentEnum.name(), name)) {
                return customerPaymentEnum;
            }
        }
        throw new CustomException(ENUM_PARAM_ERROR);
    }

    public static String getText(String name) {
        return withName(name).getText();
    }

    public static List<String> textList(){
        return Arrays.stream(CustomerPaymentEnum.values()).map(CustomerPaymentEnum::getText).collect(Collectors.toList());
    }
}
