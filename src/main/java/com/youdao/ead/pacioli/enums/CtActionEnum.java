package com.youdao.ead.pacioli.enums;

import com.youdao.ead.pacioli.entity.pacioli.ConversionAction;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/7
 */
public enum CtActionEnum {


    ACTIVATE(67, 0, List.of("android_activate","ios_activate")),
    REGISTER(69, 1, List.of("android_register","ios_register")),
    DAY1RETENTION(71, 3, List.of("android_day1retention","ios_day1retention")),
    ADDTOCART(73, 5, List.of("android_addtocart","ios_addtocart")),
    PURCHASE(75, 7, List.of("android_purchase","ios_purchase")),
    CREDIT(77, 13, List.of("android_credit","ios_credit")),
    CUSTOM(79, 15, List.of("android_custom","ios_custom")),
    UNKNOWN(-1, -1, Collections.emptyList())
    ;

    /**
     * 对应的回传转化行为类型id，{@link ConversionAction#getId()}
     */
    private final long id;

    /**
     * 对应的转化行为类型id，{@link ConversionAction#getId()}
     */
    private final long effectiveConversionActionId;

    /**
     *
     */
    private final List<String> textList;

    CtActionEnum(long id, long effectiveConversionActionId, List<String> textList) {
        this.id = id;
        this.effectiveConversionActionId = effectiveConversionActionId;
        this.textList = textList;
    }


    public long getId() {
        return id;
    }

    public long getEffectiveConversionActionId() {
        return effectiveConversionActionId;
    }

    public static CtActionEnum byCtActionText(String text) {
        if (text != null) {
            for (CtActionEnum ctActionEnum : CtActionEnum.values()) {
                if (ctActionEnum.textList.contains(text)) {
                    return ctActionEnum;
                }
            }
        }
        return UNKNOWN;
    }

    public static CtActionEnum byEffectiveConversionActionId(Long effectiveConversionActionId) {
        if (effectiveConversionActionId != null) {
            for (CtActionEnum ctActionEnum : CtActionEnum.values()) {
                if (ctActionEnum.effectiveConversionActionId == effectiveConversionActionId.intValue()) {
                    return ctActionEnum;
                }
            }
        }
        return UNKNOWN;
    }
}
