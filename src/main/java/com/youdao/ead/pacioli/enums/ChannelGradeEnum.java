package com.youdao.ead.pacioli.enums;

/**
 * <AUTHOR>
 * @date 2023/1/13
 */
public enum ChannelGradeEnum {
    /**
     * 渠道评级类型
     */
    A((byte) 0),
    B((byte) 1),
    C((byte) 2),
    D((byte) 3),
    UNKNOWN((byte) -1);

    private final Byte id;

    ChannelGradeEnum(Byte id) {
        this.id = id;
    }

    public static ChannelGradeEnum withId(Byte id) {
        for (ChannelGradeEnum channelGradeEnum : ChannelGradeEnum.values()) {
            if (channelGradeEnum.id.equals(id)) {
                return channelGradeEnum;
            }
        }
        return UNKNOWN;
    }

    public static ChannelGradeEnum withName(String name) {
        for (ChannelGradeEnum channelGradeEnum : ChannelGradeEnum.values()) {
            if (channelGradeEnum.name().equals(name)) {
                return channelGradeEnum;
            }
        }
        return UNKNOWN;
    }

    public Byte getId() {
        return id;
    }
}
