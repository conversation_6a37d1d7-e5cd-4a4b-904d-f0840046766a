package com.youdao.ead.pacioli.dto.login;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
public class SmsCaptchaValidateDTO {
    @NotNull
    @Schema(description = "rsa后的手机号")
    private String phone;
    @NotNull
    @Length(min = 4, max = 4, message = "验证码不合法")
    @Schema(description = "验证码")
    private String verifyCode;
}
