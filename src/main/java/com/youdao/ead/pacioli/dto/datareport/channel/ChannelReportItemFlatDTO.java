package com.youdao.ead.pacioli.dto.datareport.channel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.youdao.ead.pacioli.core.converter.excel.LocalDateConverter;
import com.youdao.ead.pacioli.enums.RoleEnum;
import com.youdao.ead.pacioli.enums.TimeGranularityEnum;
import lombok.Data;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/3
 */
@Data
@ExcelIgnoreUnannotated
public class ChannelReportItemFlatDTO {
    @ExcelProperty(value = "日期", converter = LocalDateConverter.class)
    private LocalDate date;
    @ExcelProperty(value = "操作系统")
    private String osText;
    @ExcelProperty(value = "推广类别")
    private String promotionCategoryText;
    @ExcelProperty(value = "推广活动ID")
    private Long activityId;
    @ExcelProperty(value = "产品类别")
    private String productCategoryName;
    @ExcelProperty(value = "客户名称")
    private String customerName;
    @ExcelProperty(value = "渠道名称")
    private String channelName;
    @ExcelProperty(value = "渠道代号")
    private String channelCode;
    @ExcelProperty(value = "BD")
    private String bdName;
    @ExcelProperty(value = "渠道投放时长")
    private Integer channelWordDayNum;

    @ExcelProperty(value = "激活")
    private WriteCellData<String> activeNum;
    @ExcelProperty(value = "回传激活")
    private WriteCellData<String> activeCallBack;
    @ExcelProperty(value = "安装")
    private WriteCellData<String> install;
    @ExcelProperty(value = "注册")
    private WriteCellData<String> register;
    @ExcelProperty(value = "回传注册")
    private WriteCellData<String> registerCallBack;
    @ExcelProperty(value = "次日留存")
    private WriteCellData<String> morrowRetention;
    @ExcelProperty(value = "回传次日留存")
    private WriteCellData<String> morrowRetentionCallBack;
    @ExcelProperty(value = "3日留存")
    private WriteCellData<String> threeDayRetention;
    @ExcelProperty(value = "7日留存")
    private WriteCellData<String> sevenDayRetention;
    @ExcelProperty(value = "加入购物车")
    private WriteCellData<String> addCart;
    @ExcelProperty(value = "回传加入购物车")
    private WriteCellData<String> addCartCallBack;
    @ExcelProperty(value = "下单")
    private WriteCellData<String> placeOrder;
    @ExcelProperty(value = "购买")
    private WriteCellData<String> buy;
    @ExcelProperty(value = "回传购买")
    private WriteCellData<String> buyCallBack;
    @ExcelProperty(value = "完件")
    private WriteCellData<String> complete;
    @ExcelProperty(value = "预授信")
    private WriteCellData<String> preCredit;
    @ExcelProperty(value = "授信")
    private WriteCellData<String> credit;
    @ExcelProperty(value = "回传授信")
    private WriteCellData<String> creditCallBack;
    @ExcelProperty(value = "创角")
    private WriteCellData<String> characterCreate;
    @ExcelProperty(value = "充值数")
    private WriteCellData<String> rechargeAmount;
    @ExcelProperty(value = "播放数")
    private WriteCellData<String> playbackAmount;
    @ExcelProperty(value = "目标付费数")
    private WriteCellData<String> targetPaymentAmount;
    @ExcelProperty(value = "首购")
    private WriteCellData<String> firstTimeBrought;
    @ExcelProperty(value = "唤端")
    private WriteCellData<String> wakeUpClient;
    @ExcelProperty(value = "首唤")
    private WriteCellData<String> firstTimeWakeUpClient;
    @ExcelProperty(value = "自定义行为1")
    private WriteCellData<String> customAction;
    @ExcelProperty(value = "回传自定义行为")
    private WriteCellData<String> customActionCallBack;
    @ExcelProperty(value = "自定义行为2")
    private WriteCellData<String> customAction2;

    public static Set<String> getIgnoreColNameSet(Boolean groupByOs, Boolean groupByPromotionCategory,
                                                  Boolean groupByProductCategory, Boolean groupByCustomerName,
                                                  TimeGranularityEnum timeGranularityEnum, Boolean groupByActivityId, RoleEnum roleEnum) {
        Set<String> set = new HashSet<>();
        if (!groupByOs) {
            set.add("osText");
        }
        if (!groupByPromotionCategory) {
            set.add("promotionCategoryText");
        }
        if (!groupByProductCategory) {
            set.add("productCategoryName");
        }
        if (!groupByCustomerName) {
            set.add("customerName");
        }
        if (!groupByActivityId) {
            set.add("activityId");
        }
        if (TimeGranularityEnum.ALL.equals(timeGranularityEnum)) {
            set.add("date");
        }
        if (!(RoleEnum.identifyBd(roleEnum) || RoleEnum.identifyAdmin(roleEnum))) {
            set.add("channelName");
        }
        return set;
    }
}
