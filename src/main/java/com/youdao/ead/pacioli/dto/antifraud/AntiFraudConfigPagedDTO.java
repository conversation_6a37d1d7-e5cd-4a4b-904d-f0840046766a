package com.youdao.ead.pacioli.dto.antifraud;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AntiFraudConfigPagedDTO {
    private Long id;
    private String name;
    private Integer status;
    private Boolean deleted;
    private Set<AntiFraudRuleDTO> rules;
    private Set<AntiFraudRuleDTO> rulesRta;
    private Integer relateChannelCount;
    private Integer relateActivityCount;
    private Set<String> relateSponsorNames;
    private String creator;
    private LocalDateTime createTime;
    private String updater;
    private LocalDateTime updateTime;
    private boolean editAndDeleteEnAble;
}
