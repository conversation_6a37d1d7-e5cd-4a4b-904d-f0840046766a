package com.youdao.ead.pacioli.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
@Data
public abstract class BasePageDTO {
    @NotNull
    @Schema(description = "页码，从0开始", defaultValue = "0")
    private Integer pageNum;
    @NotNull
    @Schema(description = "每页大小",defaultValue = "10")
    private Integer pageSize;
}
