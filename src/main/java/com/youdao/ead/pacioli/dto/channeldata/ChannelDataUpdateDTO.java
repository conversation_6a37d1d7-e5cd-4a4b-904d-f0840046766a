package com.youdao.ead.pacioli.dto.channeldata;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ChannelDataUpdateDTO {

    @Schema(description = "data_key")
    private String dataKey;

    @Schema(description = "结算量级")
    private Long settlementVolume;

    @Schema(description = "激活数")
    private Long activationCount;

    @Schema(description = "激活率")
    private BigDecimal activationRate;

    @Schema(description = "次留数")
    private Long nextDayRetention;

    @Schema(description = "次留率")
    private BigDecimal nextDayRetentionRate;

    @Schema(description = "注册数")
    private Long registerCount;

    @Schema(description = "激活-注册率")
    private BigDecimal registerRate;

    @Schema(description = "付费数")
    private Long paymentCount;

    @Schema(description = "激活-付费率")
    private BigDecimal paymentRate;

    @Schema(description = "首日付费金额")
    private Long firstDayPaymentAmount;

    @Schema(description = "3日留存率")
    private BigDecimal threeDayRetentionRate;

    @Schema(description = "7日留存率")
    private BigDecimal sevenDayRetentionRate;

    @Schema(description = "LTV1")
    private Float ltv1;

    @Schema(description = "LTV2")
    private Float ltv2;

    @Schema(description = "LTV3")
    private Float ltv3;

    @Schema(description = "LTV7")
    private Float ltv7;

    @Schema(description = "放款数")
    private Long loansCount;

    @Schema(description = "注册-放款率")
    private BigDecimal loansRate;

    @Schema(description = "授信数")
    private Long creditCount;

    @Schema(description = "注册-授信率")
    private BigDecimal creditRate;

    @Schema(description = "离线次留率")
    private BigDecimal offlineRetentionRate;

    @Schema(description = "首日ROI")
    private Float firstDayRoi;

    @Schema(description = "arpu")
    @ExcelProperty(value = "arpu")
    private Float arpu;

    @Schema(description = "商机转化率")
    private BigDecimal opportunityConversionRate;

    @Schema(description = "相关系数")
    private Float correlationCoefficient;

    @Schema(description = "质量分")
    private Float qualityScore;
}
