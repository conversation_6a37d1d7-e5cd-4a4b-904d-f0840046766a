package com.youdao.ead.pacioli.dto.tool;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChannelAllocateResponseDTO {
    @Schema(description = "渠道名称和渠道任务分配数")
    private Map<String, Integer> allocateResultMap;
    @Schema(description = "分配结果的预测转化率，key=指标名，value=百分数转化率")
    private Map<String, String> predictRateMap;
}
