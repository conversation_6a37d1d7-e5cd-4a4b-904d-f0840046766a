package com.youdao.ead.pacioli.dto.datareport.view;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/3
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataViewUploadResDTO {
    @Schema(description = "子任务名称")
    private String subTaskName;
    @Schema(description = "上传总数")
    private Integer totalNum;
    @Schema(description = "添加成功数量")
    private Integer successNum;
    @Schema(description = "添加失败的日期与提示内容")
    private List<DataViewUploadFailInfoDTO> failInfos;
}
