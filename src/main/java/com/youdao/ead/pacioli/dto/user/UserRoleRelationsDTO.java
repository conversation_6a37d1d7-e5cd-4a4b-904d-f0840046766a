package com.youdao.ead.pacioli.dto.user;

import com.youdao.ead.pacioli.entity.pacioli.User;
import com.youdao.ead.pacioli.entity.pacioli.UserRoleRelation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-10-28 19:34
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class UserRoleRelationsDTO {
    private User user;
    private List<UserRoleRelation> userRoleRelations;
}