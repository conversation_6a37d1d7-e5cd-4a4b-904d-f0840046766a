package com.youdao.ead.pacioli.dto.channel;

import com.youdao.ead.pacioli.enums.ChannelGradeEnum;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ChannelCreateDTO {
    private String name;
    @Parameter(description = "渠道代号")
    private String code;
    private String annualIncomeScale;
    private String coreResource;
    private String historyCooperationCase;
    private String hopeCooperationType;
    private ChannelGradeEnum grade;
    private String reason;
}
