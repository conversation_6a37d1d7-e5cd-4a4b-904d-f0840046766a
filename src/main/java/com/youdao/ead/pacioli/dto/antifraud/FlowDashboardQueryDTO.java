package com.youdao.ead.pacioli.dto.antifraud;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.youdao.ead.pacioli.dto.BasePageDTO;
import com.youdao.ead.pacioli.enums.TimeGranularityEnum;
import com.youdao.ead.pacioli.enums.ZhixuanCtActionEnum;
import com.youdao.ead.pacioli.enums.antifraud.FlowDashboardDimensionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import static com.youdao.ead.pacioli.enums.antifraud.FlowDashboardDimensionEnum.*;

/**
 * <AUTHOR>
 */
@Data
public class FlowDashboardQueryDTO extends BasePageDTO {
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    @Schema(description = "时间聚合维度")
    private TimeGranularityEnum timeGranularity;
    @Schema(description = "操作系统 IOS/ANDROID")
    private String os;
    @Schema(description = "需要查询的推广活动ID列表")
    private Set<Long> activityIds;
    @Schema(description = "需要查询的渠道DID列表")
    private Set<String> channelDids;
    @Schema(description = "按OS聚合")
    private Boolean groupByOsType = false;
    @Schema(description = "按广告主聚合")
    private Boolean groupBySponsor = false;
    @Schema(description = "按渠道聚合")
    private Boolean groupByChannel = false;
    @Schema(description = "按AID聚合")
    private Boolean groupByAid = false;
    @Schema(description = "按设备品牌聚合")
    private Boolean groupByBrand = false;
    @Schema(description = "按应用市场聚合")
    private Boolean groupByTdpPlatform = false;
    @Schema(description = "按省聚合")
    private Boolean groupByProvince = false;
    @Schema(description = "按市聚合")
    private Boolean groupByCity = false;
    @Schema(description = "按rta投放开启聚合")
    private Boolean groupByRtaEnable = false;
    @Schema(description = "按流量来源聚合")
    private Boolean groupBySource = false;
    @Schema(description = "显示数据为0的行")
    private Boolean displayAllZeroLine = true;
    @Schema(description = "需要查询的指标")
    private Set<String> displayMetricsSet;
    @Schema(description = "需要排序的指标")
    private String sortMetrics;
    @Schema(description = "是否为升序排序")
    private Boolean isAsc = true;
    @Schema(description = "反作弊相关指标-转化数据的限定条件")
    private ZhixuanCtActionEnum ctActionSelector;
    @JsonIgnore
    private Set<FlowDashboardDimensionEnum> dimensionSet = new HashSet<>();
    @JsonIgnore
    private Set<FlowDashboardDimensionEnum> dimensionExceptSet = new HashSet<>();

    public void initDimensionSet() {
        if (groupByOsType) {
            dimensionSet.add(OS_TYPE);
        } else {
            dimensionExceptSet.add(OS_TYPE);
        }
        if (groupBySponsor) {
            dimensionSet.add(SPONSOR);
        } else {
            dimensionExceptSet.add(SPONSOR);
        }
        if (groupByChannel) {
            dimensionSet.add(CHANNEL);
        } else {
            dimensionExceptSet.add(CHANNEL);
        }
        if (groupByAid) {
            dimensionSet.add(AID);
        } else {
            dimensionExceptSet.add(AID);
        }
        if (groupByBrand) {
            dimensionSet.add(BRAND);
        } else {
            dimensionExceptSet.add(BRAND);
        }
        if (groupByTdpPlatform) {
            dimensionSet.add(TDP_PLATFORM);
        } else {
            dimensionExceptSet.add(TDP_PLATFORM);
        }
        if (groupByProvince) {
            dimensionSet.add(PROVINCE);
        } else {
            dimensionExceptSet.add(PROVINCE);
        }
        if (groupByCity) {
            dimensionSet.add(CITY);
        } else {
            dimensionExceptSet.add(CITY);
        }
        if (groupByRtaEnable) {
            dimensionSet.add(RTA_ENABLE);
        } else {
            dimensionExceptSet.add(RTA_ENABLE);
        }
        if (groupBySource) {
            dimensionSet.add(SOURCE);
        } else {
            dimensionExceptSet.add(SOURCE);
        }
    }
}
