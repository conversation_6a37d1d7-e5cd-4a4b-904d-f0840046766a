package com.youdao.ead.pacioli.dto.download;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.youdao.ead.pacioli.enums.RoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.alibaba.excel.enums.BooleanEnum.TRUE;

/**
 * <AUTHOR>
 * @date 2022/10/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ColumnWidth(20)
public class DownloadSubTaskDTO {
    @Schema(description = "任务ID")
    @ExcelProperty(value = "任务ID")
    private Long taskId;

    @ContentStyle(wrapped = TRUE)
    @Schema(description = "任务名称")
    @ExcelProperty(value = "任务名称")
    private String taskName;

    @Schema(description = "客户名称")
    @ExcelProperty(value = "客户名称")
    private String customerName;

    @Schema(description = "子任务ID")
    @ExcelProperty(value = "子任务ID")
    private Long subTaskId;

    @ContentStyle(wrapped = TRUE)
    @Schema(description = "子任务名称")
    @ExcelProperty(value = "子任务名称")
    private String subTaskName;

    @Schema(description = "推广链接")
    @ExcelProperty(value = "推广链接")
    private String promotionLink;

    @Schema(description = "aid")
    @ExcelProperty(value = "aid")
    private Long activityId;

    @Schema(description = "任务量")
    @ExcelProperty(value = "任务量")
    private Long taskAmount;

    @Schema(description = "渠道DID")
    @ExcelProperty(value = "渠道DID")
    private String channelDid;

    @Schema(description = "渠道")
    @ExcelProperty(value = "渠道")
    private String channelName;

    @Schema(description = "渠道代号")
    @ExcelProperty(value = "渠道代号")
    private String channelCode;

    @Schema(description = "渠道CPA单价，单位元")
    @ExcelProperty(value = "渠道CPA单价")
    private BigDecimal channelCpaUnitPrice;

    @Schema(description = "生效日期")
    @ExcelProperty(value = "生效日期")
    private String effectiveDateText;

    @Schema(description = "状态")
    @ExcelProperty(value = "状态")
    private String stateText;

    @Schema(description = "BD")
    @ExcelProperty(value = "BD")
    private String bdName;

    @Schema(description = "BD反馈信息")
    @ExcelProperty(value = "BD反馈信息")
    private String bdFeedback;

    public static List<String> getTitleListByRole(RoleEnum roleEnum, boolean isSubTaskNeedAid) {
        Field[] fields = DownloadSubTaskDTO.class.getDeclaredFields();
        List<String> titleList = new ArrayList<>();
        for (Field field : fields) {
            titleList.add(field.getName());
        }
        switch (roleEnum) {
            case ADVISOR :
            case SELLER :
            case SELLER_LEADER :
                titleList.remove("channelName");
                titleList.remove("channelCode");
                titleList.remove("channelCpaUnitPrice");
                titleList.remove("channelDid");
                break;
            case BD :
                titleList.remove("bdName");
                break;
            case OPERATOR:
            case OPERATOR_LEADER:
                titleList.remove("channelName");
                break;
            default:
                break;
        }
        if (!isSubTaskNeedAid) {
            titleList.remove("activityId");
        }
        return titleList;
    }

}
