package com.youdao.ead.pacioli.dto.datareport.view;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/11/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataViewUploadFailInfoDTO {
    @Schema(description = "添加失败的日期")
    private String day;
    @Schema(description = "失败原因提示内容")
    private String failInfo;
}
