package com.youdao.ead.pacioli.dto.user;

import com.youdao.ead.pacioli.dto.TinyCustomerDTO;
import com.youdao.ead.pacioli.enums.RoleEnum;
import com.youdao.ead.pacioli.enums.UserStateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserInfoDTO {
    private Long id;
    private String name;
    private List<RoleEnum> role;
    private String phone;
    private String corpEmail;
    private UserStateEnum state;
    private String crmEmail;
}
