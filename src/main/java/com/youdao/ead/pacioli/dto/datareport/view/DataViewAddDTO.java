package com.youdao.ead.pacioli.dto.datareport.view;

import com.youdao.ead.pacioli.entity.pacioli.DataReport;
import com.youdao.ead.pacioli.entity.pacioli.SubTaskChannelLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/31
 */
@Data
public class DataViewAddDTO {
    @Schema(description = "子任务id")
    private Long subTaskId;
    @Schema(description = "日期")
    private LocalDate date;
    @Schema(description = "有效转化列Id")
    private Long effectiveConversionActionId;
    @Schema(description = "激活")
    private BigDecimal activeNum;
    @Schema(description = "自定义转化列，key是转化列id，value是数量")
    private Map<Long, BigDecimal> extendConversionActionMap;
    @Schema(description = "顾问反馈")
    private String advisorFeedback;

    /**
     * 新增数据的信息转为dataReport
     *
     * @param dto
     * @param channelLog
     * @param taskId
     * @param advisorId
     */
    public static DataReport convert2DataReport(DataViewAddDTO dto, SubTaskChannelLog channelLog, Long taskId, Long advisorId) {
        DataReport dataReport = new DataReport();
        dataReport.setDate(dto.getDate());
        dataReport.setTaskId(taskId);
        dataReport.setSubTaskId(dto.getSubTaskId());
        dataReport.setAdvisorId(advisorId);
        dataReport.setEffectiveConversionActionId(dto.getEffectiveConversionActionId());
        dataReport.setActiveNum(dto.getActiveNum());
        // 对有效转化列的数据，如果缺少这一列转化数，需要默认补充为0
        dataReport.setExtendConversionData(DataReport.putEffectiveIdIfNeed(dto.getExtendConversionActionMap(), dto.getEffectiveConversionActionId()));
        dataReport.setFeedbackValidConvertNum(BigDecimal.ZERO);
        dataReport.setAdvisorFeedback(dto.getAdvisorFeedback());
        dataReport.setChannelId(channelLog.getChannelId());
        dataReport.setChannelCpaUnitPrice(channelLog.getChannelCpaUnitPrice());
        return dataReport;
    }
}
