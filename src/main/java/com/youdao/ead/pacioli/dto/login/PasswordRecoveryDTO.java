package com.youdao.ead.pacioli.dto.login;

import com.youdao.ead.pacioli.core.annotation.Phone;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
public class PasswordRecoveryDTO {
    @NotNull(message = "phoneNumber不能为空")
    @Schema(description = "RSA加密后的手机号")
    private String phoneNumber;
    @NotNull
    @Length(min = 4, max = 4, message = "验证码不合法")
    @Schema(description = "验证码")
    private String verifyCode;
    @NotNull
    @Schema(description = "RSA加密后的密码")
    private String password;
    @NotNull
    @Schema(description = "RSA加密后的重复密码")
    private String replacePassword;
}
