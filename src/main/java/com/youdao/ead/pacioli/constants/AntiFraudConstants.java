package com.youdao.ead.pacioli.constants;

import java.util.Map;

/**
 * <AUTHOR> Li
 */
public final class AntiFraudConstants {

    /**
     * 流量屏蔽配置状态
     */
    public static final Integer CONFIG_STATUS_NORMAL = 0;
    public static final Integer CONFIG_STATUS_PAUSE = 1;


    /**
     * 流量屏蔽中定义的未知渠道
     */
    public static final Long UNKNOWN_CHANNEL_ID = -1L;
    public static final String UNKNOWN_CHANNEL_DID = "-1";
    public static final String UNKNOWN_CHANNEL_NAME = "未知渠道";
    public static final String UNKNOWN_CHANNEL_CODE = "-1";


    /**
     * 流量屏蔽规则状态
     */
    public static final Integer RULE_STATUS_NORMAL = 0;
    public static final Integer RULE_STATUS_PAUSE = 1;

    /**
     * 某些维度列为空时展示的内容
     */
    public static final String UNKNOWN_COLUMN = "未知";
    public static final String UNKNOWN_BRAND_COLUMN = "未知手机品牌";
    public static final String UNKNOWN_SOURCE_COLUMN = "无法识别来源";

    /**
     * 推广平台id -> 展示名称的映射
     */
    public static final Map<String, String> TDP_PLATFORM_NAME = Map.of("0", "cpa渠道",
            "1", "小米",
            "2", "VIVO",
            "3", "华为",
            "4", "广点通",
            "5", "WIFI万能钥匙",
            "6", "华为荣耀",
            "7", "OPPO",
            "8", "百度"
    );

    /**
     * 手机品牌 -> 展示名称的映射
     */
    public static final Map<String, String> BRAND_NAME = Map.ofEntries(
            Map.entry("", "未知手机品牌"),
            Map.entry("generic", "未知手机品牌"),
            Map.entry("generic_android", "未知手机品牌"),
            Map.entry("generic_android_tablet", "未知手机品牌"),
            Map.entry("unknown", "未知手机品牌"),
            Map.entry("apple", "Apple"),
            Map.entry("huawei", "华为"),
            Map.entry("honor", "荣耀"),
            Map.entry("oppo", "OPPO"),
            Map.entry("vivo", "vivo"),
            Map.entry("xiaomi", "小米"),
            Map.entry("lenovo", "联想"),
            Map.entry("meizu", "魅族"),
            Map.entry("samsung", "三星"),
            Map.entry("360", "360"),
            Map.entry("acer", "宏碁"),
            Map.entry("amazon", "亚马逊"),
            Map.entry("aoson", "爱立顺"),
            Map.entry("archos", "爱可视"),
            Map.entry("asus", "华硕"),
            Map.entry("bedove", "贝多芬"),
            Map.entry("blackshark", "黑鲨"),
            Map.entry("coolpad", "酷派"),
            Map.entry("doov", "朵唯"),
            Map.entry("gionee", "金立"),
            Map.entry("google", "Google"),
            Map.entry("haipad", "海尔pad"),
            Map.entry("htc", "HTC"),
            Map.entry("infinix", "Infinix"),
            Map.entry("jiayu", "佳域"),
            Map.entry("karbonn", "Karbonn"),
            Map.entry("letv", "乐视"),
            Map.entry("lg", "LG"),
            Map.entry("meta", "Meta"),
            Map.entry("micromax", "Micromax"),
            Map.entry("mitv", "小米tv"),
            Map.entry("motorola", "摩托罗拉"),
            Map.entry("mpman", "Mpman"),
            Map.entry("nokia", "诺基亚"),
            Map.entry("nothing", "Nothing"),
            Map.entry("nubia", "努比亚"),
            Map.entry("oneplus", "一加"),
            Map.entry("philips", "飞利浦"),
            Map.entry("qmobile", "Qmobile"),
            Map.entry("realme", "realme"),
            Map.entry("sharp", "夏普"),
            Map.entry("smartisan", "坚果"),
            Map.entry("sony", "索尼"),
            Map.entry("sonyericsson", "索尼爱立信"),
            Map.entry("spider", "Spider"),
            Map.entry("tecno", "Tecno"),
            Map.entry("thl", "糖葫芦"),
            Map.entry("umeox", "Umeox"),
            Map.entry("walton", "沃尔顿"),
            Map.entry("xolo", "Xolo"),
            Map.entry("zhixuan", "华为智选"),
            Map.entry("zte", "中兴")
    );

    public static final String OVERSEA_DEFAULT_ID = "99999999";
    /**
     * 国内省市id -> 省市名称的映射
     */
    public static final Map<String, String> PROVINCE_CITY_NAME = Map.ofEntries(
            Map.entry("1", "北京市"),
            Map.entry("2", "上海市"),
            Map.entry("3", "天津市"),
            Map.entry("4", "重庆市"),
            Map.entry("5", "香港特别行政区"),
            Map.entry("6", "澳门特别行政区"),
            Map.entry("10", "台湾省"),
            Map.entry("11", "广东省"),
            Map.entry("12", "福建省"),
            Map.entry("13", "浙江省"),
            Map.entry("14", "江苏省"),
            Map.entry("15", "山东省"),
            Map.entry("16", "辽宁省"),
            Map.entry("17", "江西省"),
            Map.entry("18", "四川省"),
            Map.entry("19", "陕西省"),
            Map.entry("20", "湖北省"),
            Map.entry("21", "河南省"),
            Map.entry("22", "河北省"),
            Map.entry("23", "山西省"),
            Map.entry("24", "内蒙古自治区"),
            Map.entry("25", "吉林省"),
            Map.entry("26", "黑龙江省"),
            Map.entry("27", "安徽省"),
            Map.entry("28", "湖南省"),
            Map.entry("29", "广西壮族自治区"),
            Map.entry("30", "海南省"),
            Map.entry("31", "云南省"),
            Map.entry("32", "贵州省"),
            Map.entry("33", "西藏自治区"),
            Map.entry("34", "甘肃省"),
            Map.entry("35", "宁夏回族自治区"),
            Map.entry("36", "青海省"),
            Map.entry("37", "新疆维吾尔自治区"),
            Map.entry("38", "未知"),
            Map.entry("40", "东城区"),
            Map.entry("41", "西城区"),
            Map.entry("42", "崇文区"),
            Map.entry("43", "宣武区"),
            Map.entry("44", "朝阳区"),
            Map.entry("45", "海淀区"),
            Map.entry("46", "通州区"),
            Map.entry("47", "丰台区"),
            Map.entry("48", "石景山区"),
            Map.entry("49", "门头沟区"),
            Map.entry("50", "房山区"),
            Map.entry("51", "顺义区"),
            Map.entry("52", "昌平区"),
            Map.entry("53", "大兴区"),
            Map.entry("54", "平谷县"),
            Map.entry("55", "怀柔县"),
            Map.entry("56", "密云县"),
            Map.entry("57", "延庆县"),
            Map.entry("60", "黄浦区"),
            Map.entry("61", "卢湾区"),
            Map.entry("62", "徐汇区"),
            Map.entry("63", "长宁区"),
            Map.entry("64", "静安区"),
            Map.entry("65", "普陀区"),
            Map.entry("66", "闸北区"),
            Map.entry("67", "虹口区"),
            Map.entry("68", "杨浦区"),
            Map.entry("70", "闵行区"),
            Map.entry("71", "嘉定区"),
            Map.entry("72", "浦东新区"),
            Map.entry("73", "松江区"),
            Map.entry("74", "金山区"),
            Map.entry("75", "青浦区"),
            Map.entry("76", "南汇区"),
            Map.entry("77", "奉贤区"),
            Map.entry("78", "崇明县"),
            Map.entry("79", "宝山区"),
            Map.entry("80", "和平区"),
            Map.entry("81", "河东区"),
            Map.entry("82", "河西区"),
            Map.entry("83", "南开区"),
            Map.entry("84", "河北区"),
            Map.entry("85", "红桥区"),
            Map.entry("86", "塘沽区"),
            Map.entry("87", "汉沽区"),
            Map.entry("88", "大港区"),
            Map.entry("89", "东丽区"),
            Map.entry("90", "西青区"),
            Map.entry("91", "北辰区"),
            Map.entry("92", "津南区"),
            Map.entry("93", "武清区"),
            Map.entry("94", "宝坻区"),
            Map.entry("95", "静海县"),
            Map.entry("96", "宁河县"),
            Map.entry("97", "蓟县"),
            Map.entry("100", "渝中区"),
            Map.entry("101", "大渡口区"),
            Map.entry("102", "江北区"),
            Map.entry("103", "沙坪坝区"),
            Map.entry("104", "九龙坡区"),
            Map.entry("105", "南岸区"),
            Map.entry("106", "北碚区"),
            Map.entry("107", "万盛区"),
            Map.entry("108", "双桥区"),
            Map.entry("109", "渝北区"),
            Map.entry("110", "巴南区"),
            Map.entry("111", "万州区"),
            Map.entry("112", "涪陵区"),
            Map.entry("113", "黔江区"),
            Map.entry("114", "永川市"),
            Map.entry("115", "合川市"),
            Map.entry("116", "江津市"),
            Map.entry("117", "南川市"),
            Map.entry("118", "长寿县"),
            Map.entry("119", "綦江县"),
            Map.entry("120", "潼南县"),
            Map.entry("121", "荣昌县"),
            Map.entry("122", "璧山县"),
            Map.entry("123", "大足县"),
            Map.entry("124", "铜梁县"),
            Map.entry("125", "梁平县"),
            Map.entry("126", "城口县"),
            Map.entry("127", "垫江县"),
            Map.entry("128", "武隆县"),
            Map.entry("129", "丰都县"),
            Map.entry("130", "奉节县"),
            Map.entry("131", "开县"),
            Map.entry("132", "云阳县"),
            Map.entry("133", "忠县"),
            Map.entry("134", "巫溪县"),
            Map.entry("135", "巫山县"),
            Map.entry("136", "石柱县"),
            Map.entry("137", "秀山县"),
            Map.entry("138", "酉阳县"),
            Map.entry("139", "彭水县"),
            Map.entry("141", "广州市"),
            Map.entry("142", "深圳市"),
            Map.entry("143", "珠海市"),
            Map.entry("144", "汕头市"),
            Map.entry("145", "韶关市"),
            Map.entry("146", "河源市"),
            Map.entry("147", "梅州市"),
            Map.entry("148", "惠州市"),
            Map.entry("149", "汕尾市"),
            Map.entry("150", "东莞市"),
            Map.entry("151", "中山市"),
            Map.entry("152", "江门市"),
            Map.entry("153", "佛山市"),
            Map.entry("154", "阳江市"),
            Map.entry("155", "湛江市"),
            Map.entry("156", "茂名市"),
            Map.entry("157", "肇庆市"),
            Map.entry("158", "清远市"),
            Map.entry("159", "潮州市"),
            Map.entry("160", "揭阳市"),
            Map.entry("161", "云浮市"),
            Map.entry("165", "福州市"),
            Map.entry("166", "厦门市"),
            Map.entry("167", "三明市"),
            Map.entry("168", "莆田市"),
            Map.entry("169", "泉州市"),
            Map.entry("170", "漳州市"),
            Map.entry("171", "南平市"),
            Map.entry("172", "龙岩市"),
            Map.entry("173", "宁德市"),
            Map.entry("181", "杭州市"),
            Map.entry("182", "宁波市"),
            Map.entry("183", "温州市"),
            Map.entry("184", "嘉兴市"),
            Map.entry("185", "湖州市"),
            Map.entry("186", "绍兴市"),
            Map.entry("187", "金华市"),
            Map.entry("188", "衢州市"),
            Map.entry("189", "舟山市"),
            Map.entry("190", "台州市"),
            Map.entry("191", "丽水市"),
            Map.entry("195", "南京市"),
            Map.entry("196", "徐州市"),
            Map.entry("197", "连云港市"),
            Map.entry("198", "淮安市"),
            Map.entry("199", "宿迁市"),
            Map.entry("200", "盐城市"),
            Map.entry("201", "扬州市"),
            Map.entry("202", "泰州市"),
            Map.entry("203", "南通市"),
            Map.entry("205", "镇江市"),
            Map.entry("206", "常州市"),
            Map.entry("207", "无锡市"),
            Map.entry("208", "苏州市"),
            Map.entry("211", "济南市"),
            Map.entry("212", "青岛市"),
            Map.entry("213", "淄博市"),
            Map.entry("214", "枣庄市"),
            Map.entry("215", "东营市"),
            Map.entry("216", "潍坊市"),
            Map.entry("217", "烟台市"),
            Map.entry("218", "威海市"),
            Map.entry("219", "济宁市"),
            Map.entry("220", "泰安市"),
            Map.entry("221", "日照市"),
            Map.entry("222", "莱芜市"),
            Map.entry("223", "德州市"),
            Map.entry("224", "临沂市"),
            Map.entry("225", "聊城市"),
            Map.entry("226", "滨州市"),
            Map.entry("227", "菏泽市"),
            Map.entry("231", "沈阳市"),
            Map.entry("232", "大连市"),
            Map.entry("233", "鞍山市"),
            Map.entry("234", "抚顺市"),
            Map.entry("235", "本溪市"),
            Map.entry("236", "丹东市"),
            Map.entry("237", "锦州市"),
            Map.entry("238", "葫芦岛市"),
            Map.entry("239", "营口市"),
            Map.entry("240", "盘锦市"),
            Map.entry("241", "阜新市"),
            Map.entry("242", "辽阳市"),
            Map.entry("243", "铁岭市"),
            Map.entry("244", "朝阳市"),
            Map.entry("251", "南昌市"),
            Map.entry("252", "九江市"),
            Map.entry("253", "上饶市"),
            Map.entry("254", "景德镇市"),
            Map.entry("255", "萍乡市"),
            Map.entry("256", "新余市"),
            Map.entry("257", "鹰潭市"),
            Map.entry("258", "吉安市"),
            Map.entry("259", "赣州市"),
            Map.entry("260", "宜春市"),
            Map.entry("261", "抚州市"),
            Map.entry("265", "成都市"),
            Map.entry("266", "自贡市"),
            Map.entry("267", "攀枝花市"),
            Map.entry("268", "泸州市"),
            Map.entry("269", "德阳市"),
            Map.entry("270", "绵阳市"),
            Map.entry("271", "广元市"),
            Map.entry("272", "遂宁市"),
            Map.entry("273", "内江市"),
            Map.entry("274", "乐山市"),
            Map.entry("275", "南充市"),
            Map.entry("276", "宜宾市"),
            Map.entry("277", "广安市"),
            Map.entry("278", "达州市"),
            Map.entry("279", "巴中市"),
            Map.entry("280", "雅安市"),
            Map.entry("281", "眉山市"),
            Map.entry("282", "资阳市"),
            Map.entry("283", "阿坝州"),
            Map.entry("284", "甘孜州"),
            Map.entry("285", "凉山州"),
            Map.entry("288", "西安市"),
            Map.entry("289", "铜川市"),
            Map.entry("290", "宝鸡市"),
            Map.entry("291", "咸阳市"),
            Map.entry("292", "渭南市"),
            Map.entry("293", "延安市"),
            Map.entry("294", "汉中市"),
            Map.entry("295", "榆林市"),
            Map.entry("296", "安康市"),
            Map.entry("297", "商洛市"),
            Map.entry("301", "武汉市"),
            Map.entry("302", "黄石市"),
            Map.entry("303", "襄樊市"),
            Map.entry("304", "十堰市"),
            Map.entry("305", "荆州市"),
            Map.entry("306", "宜昌市"),
            Map.entry("307", "荆门市"),
            Map.entry("308", "鄂州市"),
            Map.entry("309", "孝感市"),
            Map.entry("310", "黄冈市"),
            Map.entry("311", "咸宁市"),
            Map.entry("312", "随州市"),
            Map.entry("313", "仙桃市"),
            Map.entry("314", "天门市"),
            Map.entry("315", "潜江市"),
            Map.entry("316", "神农架林区"),
            Map.entry("317", "恩施市"),
            Map.entry("321", "郑州市"),
            Map.entry("322", "开封市"),
            Map.entry("323", "洛阳市"),
            Map.entry("324", "平顶山市"),
            Map.entry("325", "焦作市"),
            Map.entry("326", "鹤壁市"),
            Map.entry("327", "新乡市"),
            Map.entry("328", "安阳市"),
            Map.entry("329", "濮阳市"),
            Map.entry("330", "许昌市"),
            Map.entry("331", "漯河市"),
            Map.entry("332", "三门峡市"),
            Map.entry("333", "南阳市"),
            Map.entry("334", "商丘市"),
            Map.entry("335", "信阳市"),
            Map.entry("336", "周口市"),
            Map.entry("337", "驻马店市"),
            Map.entry("338", "济源市"),
            Map.entry("341", "石家庄市"),
            Map.entry("342", "唐山市"),
            Map.entry("343", "秦皇岛市"),
            Map.entry("344", "邯郸市"),
            Map.entry("345", "邢台市"),
            Map.entry("346", "保定市"),
            Map.entry("347", "张家口市"),
            Map.entry("348", "承德市"),
            Map.entry("349", "沧州市"),
            Map.entry("350", "廊坊市"),
            Map.entry("351", "衡水市"),
            Map.entry("356", "太原市"),
            Map.entry("357", "大同市"),
            Map.entry("358", "阳泉市"),
            Map.entry("359", "长治市"),
            Map.entry("360", "晋城市"),
            Map.entry("361", "忻州市"),
            Map.entry("362", "临汾市"),
            Map.entry("363", "运城市"),
            Map.entry("364", "朔州市"),
            Map.entry("365", "晋中市"),
            Map.entry("366", "吕梁市"),
            Map.entry("371", "呼和浩特市"),
            Map.entry("372", "包头市"),
            Map.entry("373", "乌海市"),
            Map.entry("374", "赤峰市"),
            Map.entry("375", "通辽市"),
            Map.entry("376", "鄂尔多斯市"),
            Map.entry("377", "乌兰察布市"),
            Map.entry("378", "锡林郭勒盟"),
            Map.entry("379", "呼伦贝尔市"),
            Map.entry("380", "巴彦淖尔市"),
            Map.entry("381", "阿拉善盟"),
            Map.entry("382", "兴安盟"),
            Map.entry("386", "长春市"),
            Map.entry("387", "吉林市"),
            Map.entry("388", "四平市"),
            Map.entry("389", "辽源市"),
            Map.entry("390", "通化市"),
            Map.entry("391", "白山市"),
            Map.entry("392", "松原市"),
            Map.entry("393", "白城市"),
            Map.entry("394", "延边朝鲜族自治州"),
            Map.entry("396", "哈尔滨市"),
            Map.entry("397", "齐齐哈尔市"),
            Map.entry("398", "鹤岗市"),
            Map.entry("399", "双鸭山市"),
            Map.entry("400", "鸡西市"),
            Map.entry("401", "大庆市"),
            Map.entry("402", "伊春市"),
            Map.entry("403", "牡丹江市"),
            Map.entry("404", "佳木斯市"),
            Map.entry("405", "七台河市"),
            Map.entry("406", "黑河市"),
            Map.entry("407", "绥化市"),
            Map.entry("408", "大兴安岭地区"),
            Map.entry("411", "合肥市"),
            Map.entry("412", "芜湖市"),
            Map.entry("413", "蚌埠市"),
            Map.entry("414", "淮南市"),
            Map.entry("415", "马鞍山市"),
            Map.entry("416", "淮北市"),
            Map.entry("417", "铜陵市"),
            Map.entry("418", "安庆市"),
            Map.entry("419", "黄山市"),
            Map.entry("420", "滁州市"),
            Map.entry("421", "阜阳市"),
            Map.entry("422", "宿州市"),
            Map.entry("423", "巢湖市"),
            Map.entry("424", "六安市"),
            Map.entry("425", "亳州市"),
            Map.entry("426", "宣城市"),
            Map.entry("427", "池州市"),
            Map.entry("431", "长沙市"),
            Map.entry("432", "株州市"),
            Map.entry("433", "湘潭市"),
            Map.entry("434", "衡阳市"),
            Map.entry("435", "邵阳市"),
            Map.entry("436", "岳阳市"),
            Map.entry("437", "常德市"),
            Map.entry("438", "张家界市"),
            Map.entry("439", "益阳市"),
            Map.entry("440", "郴州市"),
            Map.entry("441", "永州市"),
            Map.entry("442", "怀化市"),
            Map.entry("443", "娄底市"),
            Map.entry("444", "湘西土家族苗族自治州"),
            Map.entry("451", "南宁市"),
            Map.entry("452", "柳州市"),
            Map.entry("453", "桂林市"),
            Map.entry("454", "梧州市"),
            Map.entry("455", "北海市"),
            Map.entry("456", "防城港市"),
            Map.entry("457", "钦州市"),
            Map.entry("458", "贵港市"),
            Map.entry("459", "玉林市"),
            Map.entry("460", "南宁地区"),
            Map.entry("461", "柳州地区"),
            Map.entry("462", "贺州市"),
            Map.entry("463", "百色市"),
            Map.entry("464", "河池地区"),
            Map.entry("471", "海口市"),
            Map.entry("472", "三亚市"),
            Map.entry("473", "五指山市"),
            Map.entry("474", "琼海市"),
            Map.entry("475", "儋州市"),
            Map.entry("476", "琼山市"),
            Map.entry("477", "文昌市"),
            Map.entry("478", "万宁市"),
            Map.entry("479", "东方市"),
            Map.entry("480", "澄迈县"),
            Map.entry("481", "定安县"),
            Map.entry("482", "屯昌县"),
            Map.entry("483", "临高县"),
            Map.entry("484", "白沙县"),
            Map.entry("485", "昌江县"),
            Map.entry("486", "乐东县"),
            Map.entry("487", "陵水县"),
            Map.entry("488", "保亭县"),
            Map.entry("489", "琼中县"),
            Map.entry("491", "昆明市"),
            Map.entry("492", "曲靖市"),
            Map.entry("493", "玉溪市"),
            Map.entry("494", "保山市"),
            Map.entry("495", "昭通市"),
            Map.entry("496", "思茅地区"),
            Map.entry("497", "临沧市"),
            Map.entry("498", "丽江市"),
            Map.entry("499", "文山州"),
            Map.entry("500", "红河县"),
            Map.entry("501", "西双版纳傣族自治州"),
            Map.entry("502", "楚雄市"),
            Map.entry("503", "大理市"),
            Map.entry("504", "德宏傣族景颇族自治州"),
            Map.entry("505", "怒江傈僳族自治州"),
            Map.entry("506", "迪庆藏族自治州"),
            Map.entry("511", "贵阳市"),
            Map.entry("512", "六盘水市"),
            Map.entry("513", "遵义市"),
            Map.entry("514", "安顺市"),
            Map.entry("515", "铜仁地区"),
            Map.entry("516", "毕节地区"),
            Map.entry("517", "黔西南布依族苗族自治州"),
            Map.entry("518", "黔东南苗族侗族自治州"),
            Map.entry("519", "黔南布依族苗族自治州"),
            Map.entry("521", "拉萨市"),
            Map.entry("522", "那曲地区"),
            Map.entry("523", "昌都地区"),
            Map.entry("524", "山南地区"),
            Map.entry("525", "日喀则地区"),
            Map.entry("526", "阿里地区"),
            Map.entry("527", "林芝地区"),
            Map.entry("531", "兰州市"),
            Map.entry("532", "金昌市"),
            Map.entry("533", "白银市"),
            Map.entry("534", "天水市"),
            Map.entry("535", "嘉峪关市"),
            Map.entry("536", "武威市"),
            Map.entry("537", "定西市"),
            Map.entry("538", "平凉市"),
            Map.entry("539", "庆阳市"),
            Map.entry("540", "陇南市"),
            Map.entry("541", "张掖市"),
            Map.entry("542", "酒泉市"),
            Map.entry("543", "甘南藏族自治州"),
            Map.entry("544", "临夏市"),
            Map.entry("546", "银川市"),
            Map.entry("547", "石嘴山市"),
            Map.entry("548", "吴忠市"),
            Map.entry("549", "固原市"),
            Map.entry("551", "西宁市"),
            Map.entry("552", "海东地区"),
            Map.entry("553", "海北藏族自治州"),
            Map.entry("554", "黄南藏族自治州"),
            Map.entry("555", "海南藏族自治州"),
            Map.entry("556", "果洛藏族自治州"),
            Map.entry("557", "玉树藏族自治州"),
            Map.entry("558", "海西蒙古族藏族自治州"),
            Map.entry("561", "乌鲁木齐市"),
            Map.entry("562", "克拉玛依市"),
            Map.entry("563", "石河子市"),
            Map.entry("564", "吐鲁番市"),
            Map.entry("565", "哈密市"),
            Map.entry("566", "和田地区"),
            Map.entry("567", "阿克苏地区"),
            Map.entry("568", "喀什市"),
            Map.entry("569", "克孜勒苏"),
            Map.entry("570", "巴音郭楞蒙古自治州"),
            Map.entry("571", "昌吉市"),
            Map.entry("572", "博尔塔拉蒙古自治州"),
            Map.entry("573", "伊犁哈萨克自治州"),
            Map.entry("581", "各地"),
            Map.entry("591", "中西区"),
            Map.entry("592", "东区"),
            Map.entry("593", "九龙城区"),
            Map.entry("594", "观塘区"),
            Map.entry("595", "南区"),
            Map.entry("596", "深水埗区"),
            Map.entry("597", "湾仔区"),
            Map.entry("598", "黄大仙区"),
            Map.entry("599", "油尖旺区"),
            Map.entry("600", "离岛区"),
            Map.entry("601", "葵青区"),
            Map.entry("602", "北区"),
            Map.entry("603", "西贡区"),
            Map.entry("604", "沙田区"),
            Map.entry("605", "屯门区"),
            Map.entry("606", "大埔区"),
            Map.entry("607", "荃湾区"),
            Map.entry("608", "元朗区"),
            Map.entry("612", "澳门"),
            Map.entry("621", "台北市"),
            Map.entry("622", "高雄市"),
            Map.entry("623", "基隆市"),
            Map.entry("624", "台中市"),
            Map.entry("625", "台南市"),
            Map.entry("626", "新竹市"),
            Map.entry("627", "嘉义市"),
            Map.entry("628", "台北县"),
            Map.entry("629", "宜兰县"),
            Map.entry("630", "新竹县"),
            Map.entry("631", "桃园县"),
            Map.entry("632", "苗栗县"),
            Map.entry("633", "台中县"),
            Map.entry("634", "彰化县"),
            Map.entry("635", "南投县"),
            Map.entry("636", "嘉义县"),
            Map.entry("637", "云林县"),
            Map.entry("638", "台南县"),
            Map.entry("639", "高雄县"),
            Map.entry("640", "屏东县"),
            Map.entry("641", "台东县"),
            Map.entry("642", "花莲县"),
            Map.entry("643", "澎湖县"),
            Map.entry("644", "东胜市"),
            Map.entry("645", "临河市"),
            Map.entry("647", "延吉市"),
            Map.entry("648", "恩施市"),
            Map.entry("649", "梅河口市"),
            Map.entry("650", "榆次市"),
            Map.entry("651", "海拉尔市"),
            Map.entry("652", "潮阳市"),
            Map.entry("653", "荷泽市"),
            Map.entry("654", "锡林浩特市"),
            Map.entry("655", "随枣市"),
            Map.entry("656", "顺德市"),
            Map.entry("657", "马尔康市"),
            Map.entry("664", "乌兰浩特市"),
            Map.entry("665", "珲春市"),
            Map.entry("684", "博乐市"),
            Map.entry("685", "和田市"),
            Map.entry("686", "喀什市"),
            Map.entry("687", "康定市"),
            Map.entry("688", "昌吉市"),
            Map.entry("689", "淮阴市"),
            Map.entry("690", "离石市"),
            Map.entry("691", "荆沙市"),
            Map.entry("692", "西昌市"),
            Map.entry("693", "贵池市"),
            Map.entry("694", "达川市"),
            Map.entry("695", "阿拉善左旗市"),
            Map.entry("696", "集宁市"),
            Map.entry("704", "河池市"),
            Map.entry("724", "伊客昭盟"),
            Map.entry("725", "新界"),
            Map.entry("726", "大屿山"),
            Map.entry("727", "上水"),
            Map.entry("728", "香港岛"),
            Map.entry("729", "梅窝"),
            Map.entry("730", "东涌"),
            Map.entry("731", "大澳"),
            Map.entry("732", "中甸"),
            Map.entry("733", "六库"),
            Map.entry("734", "潞西"),
            Map.entry("735", "个旧"),
            Map.entry("736", "景洪"),
            Map.entry("737", "阿勒泰市"),
            Map.entry("738", "塔城地区"),
            Map.entry("739", "库尔勒"),
            Map.entry("740", "阿图什市"),
            Map.entry("741", "通什"),
            Map.entry("742", "氹仔"),
            Map.entry("743", "路环岛"),
            Map.entry("744", "珠穆朗玛峰"),
            Map.entry(OVERSEA_DEFAULT_ID, "海外")
    );

    public static final int FRAUD_STAT_TYPE_CLICK = 1;
    public static final int FRAUD_STAT_TYPE_RTA = 2;
    public static final int FRAUD_STAT_TYPE_CONV = 3;

}
