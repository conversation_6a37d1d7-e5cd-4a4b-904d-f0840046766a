package com.youdao.ead.pacioli.mapper;

import com.youdao.ead.pacioli.dto.antifraud.FlowDashboardQueryDTO;
import com.youdao.ead.pacioli.dto.antifraud.FlowDashboardResultDTO;
import com.youdao.ead.pacioli.dto.channel.ChannelDataFlowDashboardResultDTO;
import com.youdao.ead.pacioli.dto.channeldata.ChannelDataQueryDTO;
import com.youdao.ead.pacioli.dto.channeldata.ChannelDataResultDTO;
import com.youdao.ead.pacioli.entity.pacioli.ChannelData;
import com.youdao.ead.pacioli.entity.youdata.ReportingFullDataset;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface ChannelDataMapper {

    @Mapping(target = "channelName", ignore = true)
    @Mapping(target = "channelDid", ignore = true)
    @Mapping(target = "channelCode", ignore = true)
    ChannelDataResultDTO do2PagedInfoDTO(ChannelData channelData);

    @Mapping(target = "locking", ignore = true)
    @Mapping(target = "sponsor", ignore = true)
    @Mapping(target = "sponsorId", ignore = true)
    @Mapping(target = "channelName", ignore = true)
    @Mapping(target = "channelDid", ignore = true)
    @Mapping(target = "channelCode", ignore = true)
    @Mapping(target = "sale", ignore = true)
    @Mapping(target = "consultant", ignore = true)
    @Mapping(target = "bd", ignore = true)
    @Mapping(target = "operations", ignore = true)
    @Mapping(target = "clickCount", ignore = true)
    @Mapping(target = "returnLevel", ignore = true)
    @Mapping(target = "magnitudeDifference", ignore = true)
    @Mapping(target = "settlementPointConversionRate", ignore = true)
    ChannelDataResultDTO convertPagedInfoDTO(ReportingFullDataset reportingFullDataset);

    @Mapping(target = "sponsorId", ignore = true)
    @Mapping(target = "sponsor", ignore = true)
    @Mapping(target = "consultant", ignore = true)
    @Mapping(target = "sale", ignore = true)
    @Mapping(target = "operations", ignore = true)
    @Mapping(target = "channel", ignore = true)
    @Mapping(target = "bd", ignore = true)
    @Mapping(target = "clickCount", ignore = true)
    @Mapping(target = "returnLevel", ignore = true)
    @Mapping(target = "magnitudeDifference", ignore = true)
    @Mapping(target = "settlementPointConversionRate", ignore = true)
    @Mapping(target = "locking", ignore = true)
    ChannelData convertChannelData(ReportingFullDataset reportingFullDataset);

    @Mapping(target = "os", ignore = true)
    @Mapping(target = "groupByOsType", ignore = true)
    @Mapping(target = "groupByAid", ignore = true)
    @Mapping(target = "groupByBrand", ignore = true)
    @Mapping(target = "groupByTdpPlatform", ignore = true)
    @Mapping(target = "groupByProvince", ignore = true)
    @Mapping(target = "groupByCity", ignore = true)
    @Mapping(target = "groupByRtaEnable", ignore = true)
    @Mapping(target = "displayAllZeroLine", ignore = true)
    @Mapping(target = "ctActionSelector", ignore = true)
    @Mapping(target = "dimensionSet", ignore = true)
    @Mapping(target = "dimensionExceptSet", ignore = true)
    FlowDashboardQueryDTO convertFlowDashboardQueryDTO(ChannelDataQueryDTO channelDataResultDTO);

    ChannelDataFlowDashboardResultDTO convertFlowDashboardResultDTO(FlowDashboardResultDTO channelDataResultDTO);


}
