package com.youdao.ead.pacioli.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youdao.ead.pacioli.constants.ResponseType;
import com.youdao.ead.pacioli.core.exception.CustomException;
import com.youdao.ead.pacioli.dto.download.DownloadTaskDTO;
import com.youdao.ead.pacioli.dto.user.SponsorDTO;
import com.youdao.ead.pacioli.dto.task.TaskDiffDTO;
import com.youdao.ead.pacioli.dto.task.TaskItemDTO;
import com.youdao.ead.pacioli.entity.eadb1.Sponsor;
import com.youdao.ead.pacioli.entity.pacioli.Task;
import com.youdao.ead.pacioli.enums.IntegrationMethodEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/10/25
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface TaskMapper {

    final static ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    final static TypeReference<Set<String>> STRING_SET_REFERENCE = new TypeReference<Set<String>>() {
    };

    @Mapping(target = "salesName", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.downloadNullableText(item.getSalesName()))")
    @Mapping(target = "advisorName", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.downloadNullableText(item.getAdvisorName()))")
    @Mapping(target = "operatorName", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.downloadNullableText(item.getOperatorName()))")
    @Mapping(target = "osText", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.osText(item))")
    @Mapping(target = "viewZhixuanText", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.viewZhixuanText(item))")
    @Mapping(target = "taskStateText", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.taskStateText(item))")
    @Mapping(target = "effectiveDateText", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.effectiveDateText(item))")
    @Mapping(target = "zhixuanUsername", expression = "java(java.lang.String.join(\",\", item.getZhixuanUsername()))")
    DownloadTaskDTO convertTaskItemDTO2DownloadTaskDTO(TaskItemDTO item);

    TaskDiffDTO do2TaskDiffDTO(Task task);

    default Set<String> mapIntegrationMethod(String jsonArray) {
        try {
            Set<String> strings = OBJECT_MAPPER.readValue(jsonArray, STRING_SET_REFERENCE);
            return strings.stream().map(item -> {
                return IntegrationMethodEnum.valueOf(item).getText();
            }).collect(Collectors.toSet());
        } catch (Exception e) {
            throw new CustomException(ResponseType.SERVICE_ERROR);
        }
    }

}
