package com.youdao.ead.pacioli.mapper;

import com.youdao.ead.pacioli.dto.download.DownloadSubTaskDTO;
import com.youdao.ead.pacioli.dto.subtask.SubTaskDiffDTO;
import com.youdao.ead.pacioli.dto.subtask.SubTaskItemDTO;
import com.youdao.ead.pacioli.entity.pacioli.SubTask;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @date 2022/11/2
 */
@Mapper(componentModel = "spring")
@SuppressWarnings(value = "all")
public interface SubTaskMapper {
    @Mapping(target = "effectiveDateText", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.effectiveDateText(item))")
    @Mapping(target = "stateText", expression = "java(com.youdao.ead.pacioli.util.DownloadTextUtil.stateText(item))")
    DownloadSubTaskDTO convertSubTaskItemDTO2DownloadSubTaskDTO(SubTaskItemDTO item);

    SubTaskDiffDTO do2DiffDTO(SubTask subTask);
}
