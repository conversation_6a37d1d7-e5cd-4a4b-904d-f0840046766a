package com.youdao.ead.pacioli.bo;

import com.youdao.ead.pacioli.entity.pacioli.DataReport;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/3
 */
@Data
public class DataReportBO {
    @Schema(description = "id")
    private Long id;
    @Schema(description = "日期")
    private LocalDate date;
    @Schema(description = "任务ID")
    private Long taskId;
    @Schema(description = "子任务ID")
    private Long subTaskId;
    @Schema(description = "顾问ID")
    private Long advisorId;
    @Schema(description = "反馈有效转化数")
    private Long feedbackValidConvertNum;
    @Schema(description = "激活数")
    private Long activeNum;
    @Schema(description = "扩展转化类型列")
    private Map<Long, BigDecimal> extendConversionData;
    @Schema(description = "顾问反馈")
    private String advisorFeedback;
    @Schema(description = "运营反馈")
    private String operatorFeedback;
    @Schema(description = "当日所属的渠道ID")
    private Long channelId;
    @Schema(description = "当日的渠道CPA单价")
    private BigDecimal channelCpaUnitPrice;
    @Schema(description = "当日有效转化列")
    private Long effectiveConversionActionId;

    public static BigDecimal channelCpaUnitPriceMapping(DataReport dataReport) {
        return new BigDecimal(dataReport.getChannelCpaUnitPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }

    /**
     * 根据传入的转化行为idList，重置extendConversionData的数据，只保留idList包含的key
     *
     * @param idList        转化行为id列表
     */
    public void resetDataReportNum(List<Long> idList) {
        if (MapUtils.isEmpty(extendConversionData)) {
            return;
        }
        Map<Long, BigDecimal> map = new HashMap<>();
        for (Map.Entry<Long, BigDecimal> entry : extendConversionData.entrySet()) {
            Long key = entry.getKey();
            if (idList.contains(key)) {
                map.put(key, entry.getValue());
            }
        }
        extendConversionData = map;
    }

    /**
     * 数据相加
     *
     * @param forAdd        需要相加的DataReportBO
     * @param idList        转化行为id列表
     */
    public void plusDataReportNum(DataReportBO forAdd, List<Long> idList) {
        activeNum += forAdd.getActiveNum();
        feedbackValidConvertNum += forAdd.getFeedbackValidConvertNum();
        if (extendConversionData == null || extendConversionData.isEmpty()) {
            forAdd.resetDataReportNum(idList);
            extendConversionData = forAdd.getExtendConversionData();
        }
        for (Map.Entry<Long, BigDecimal> entry : forAdd.getExtendConversionData().entrySet()) {
            Long key = entry.getKey();
            if (idList.contains(key)) {
                extendConversionData.put(key,
                        extendConversionData.getOrDefault(key, BigDecimal.ZERO).add(entry.getValue())
                );
            }
        }
    }

}
