package com.youdao.ead.pacioli.controller;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.youdao.ead.pacioli.bo.LoginUser;
import com.youdao.ead.pacioli.constants.OperateLogConstants;
import com.youdao.ead.pacioli.core.annotation.BaseResponse;
import com.youdao.ead.pacioli.core.annotation.Permission;
import com.youdao.ead.pacioli.dto.subtask.*;
import com.youdao.ead.pacioli.enums.RoleEnum;
import com.youdao.ead.pacioli.service.SubTaskService;
import com.youdao.ead.pacioli.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.youdao.ead.pacioli.enums.SubTaskStateEnum.OPEN;
import static com.youdao.ead.pacioli.enums.SubTaskStateEnum.PAUSE;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
@Slf4j
@Validated
@BaseResponse
@RestController
@RequiredArgsConstructor
@Tag(name = "子任务接口")
@RequestMapping("/sub-task")
public class SubTaskController {

    private final SubTaskService subTaskService;

    @GetMapping("/page")
    @Operation(summary = "子任务列表分页查询")
    @LogRecord(success = "查询子任务列表，参数{{#dto}}",
            type = OperateLogConstants.SUB_TASK,
            bizNo = "{{#dto.taskId}}")
    public Page<SubTaskItemDTO> page(@ParameterObject PageSubTaskReqDTO dto) {
        LoginUser loginUser = SecurityUtil.getLoginUser();
        return subTaskService.page(dto, loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
    }

    @GetMapping("/download")
    @Operation(summary = "子任务列表下载")
    @LogRecord(success = "下载子任务列表，参数{{#dto}}",
            type = OperateLogConstants.SUB_TASK,
            bizNo = "{{#dto.taskId}}")
    public void download(HttpServletResponse response, @ParameterObject PageSubTaskReqDTO dto) throws Exception {
        LoginUser loginUser = SecurityUtil.getLoginUser();
        List<SubTaskItemDTO> itemDTOList = subTaskService.unPage(dto, loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
        subTaskService.downloadExcel(response, itemDTOList, loginUser.getCurrentRole(), dto.getTaskId());
    }

    @Permission(roles = {RoleEnum.ADVISOR})
    @GetMapping("/name-existed")
    @Operation(summary = "检验子任务名称重复，true=重复，false=不重复")
    public Boolean nameExisted(@RequestParam("id") Long id, @RequestParam("name") String name) {
        return subTaskService.nameExisted(id, name);
    }

    @Permission(roles = {RoleEnum.ADVISOR})
    @GetMapping("/aid-existed")
    @Operation(summary = "检验子任务aid重复，true=重复，false=不重复")
    public Boolean aidExisted(@RequestParam("id") Long id, @RequestParam("activityId") Long activityId) {
        return subTaskService.aidExisted(id, activityId);
    }

    @Permission(roles = {RoleEnum.ADVISOR})
    @PostMapping("/add")
    @Operation(summary = "新增（顾问-新增子任务-确定）")
    @LogRecord(success = "新增子任务，参数：{{#dto}}", type = OperateLogConstants.SUB_TASK, bizNo = "{{#dto.taskId}}")
    public Long add(@RequestBody SubTaskAddDTO dto) {
        return subTaskService.add(dto, SecurityUtil.getUserRoleRelationId(), SecurityUtil.getCurrentRole());
    }

    @Permission(roles = {RoleEnum.ADVISOR})
    @PostMapping("/edit")
    @Operation(summary = "编辑（顾问-编辑后-确定）")
    @LogRecord(success = "顾问编辑子任务，参数：{{#dto}}",
            type = OperateLogConstants.SUB_TASK,
            bizNo = "{{#dto.subTaskId}}")
    public void edit(@RequestBody SubTaskEditDTO dto) {
        subTaskService.edit(dto, SecurityUtil.getUserRoleRelationId(), SecurityUtil.getCurrentRole());
    }

    @Permission(roles = {RoleEnum.OPERATOR, RoleEnum.OPERATOR_LEADER})
    @PostMapping("/edit-channel")
    @Operation(summary = "编辑（运营-编辑后-确定）")
    @LogRecord(success = "运营编辑子任务，参数：{{#dto}}",
            type = OperateLogConstants.SUB_TASK,
            bizNo = "{{#dto.subTaskId}}")
    public void editChannel(@RequestBody SubTaskChannelDTO dto) {
        subTaskService.editChannel(dto, SecurityUtil.getUserRoleRelationId(), SecurityUtil.getCurrentRole());
    }

    @Permission(roles = {RoleEnum.BD, RoleEnum.BD_LEADER})
    @PostMapping("/edit-bd-feedback")
    @Operation(summary = "编辑（BD-编辑后）")
    @LogRecord(success = "BD编辑子任务，参数：{{#dto}}",
            type = OperateLogConstants.SUB_TASK,
            bizNo = "{{#dto.subTaskId}}")
    public void editBdFeedback(@RequestBody SubTaskBdFeedbackDTO dto) {
        subTaskService.editBdFeedback(dto, SecurityUtil.getCurrentRole(), SecurityUtil.getUserRoleRelationId());
    }

    @Permission(roles = {RoleEnum.BD, RoleEnum.BD_LEADER, RoleEnum.ADVISOR})
    @PutMapping("/{subTaskId}/up")
    @Operation(summary = "顾问/BD 开启")
    @LogRecord(success = "开启子任务，ID：{{#subTaskId}}",
            type = OperateLogConstants.SUB_TASK,
            bizNo = "{{#subTaskId}}")
    public void up(@PathVariable("subTaskId") Long subTaskId) {
        subTaskService.changeState(subTaskId, OPEN, SecurityUtil.getCurrentRole(), SecurityUtil.getUserRoleRelationId());
    }

    @Permission(roles = {RoleEnum.BD, RoleEnum.BD_LEADER, RoleEnum.ADVISOR})
    @PutMapping("/{subTaskId}/down")
    @Operation(summary = "顾问/BD 暂停")
    @LogRecord(success = "暂停子任务，ID：{{#subTaskId}}",
            type = OperateLogConstants.SUB_TASK,
            bizNo = "{{#subTaskId}}")
    public void down(@PathVariable("subTaskId") Long subTaskId) {
        subTaskService.changeState(subTaskId, PAUSE, SecurityUtil.getCurrentRole(), SecurityUtil.getUserRoleRelationId());
    }

}
