package com.youdao.ead.pacioli.controller;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.youdao.ead.pacioli.bo.LoginUser;
import com.youdao.ead.pacioli.constants.OperateLogConstants;
import com.youdao.ead.pacioli.core.annotation.BaseResponse;
import com.youdao.ead.pacioli.core.annotation.Permission;
import com.youdao.ead.pacioli.dto.task.*;
import com.youdao.ead.pacioli.enums.RoleEnum;
import com.youdao.ead.pacioli.service.SponsorService;
import com.youdao.ead.pacioli.service.TaskService;
import com.youdao.ead.pacioli.util.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
@Slf4j
@Validated
@BaseResponse
@RestController
@RequiredArgsConstructor
@Tag(name = "任务接口")
@RequestMapping("/task")
public class TaskController {

    private final TaskService taskService;

    private final SponsorService sponsorService;

    @GetMapping("/page")
    @Operation(summary = "任务列表分页查询")
    @LogRecord(success = "任务列表分页查询，参数{{#pageTaskReqDTO}}",
            type = OperateLogConstants.TASK,
            bizNo = OperateLogConstants.BIZ_NO_PLACEHOLDER)
    public Page<TaskItemDTO> page(@ParameterObject PageTaskReqDTO pageTaskReqDTO) {
        LoginUser loginUser = SecurityUtil.getLoginUser();
        return taskService.page(pageTaskReqDTO, loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
    }

    @GetMapping("/{taskId}")
    @Operation(summary = "任务编辑（获取任务详细信息）")
    @LogRecord(success = "获取任务详细信息",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskId}}")
    public TaskDetailDTO editByTaskId(@PathVariable("taskId") Long taskId) {
        return taskService.getTaskDetail(taskId, true);
    }

    @Permission(roles = {RoleEnum.SELLER, RoleEnum.SELLER_LEADER})
    @GetMapping("/{taskId}/copy")
    @Operation(summary = "任务复制")
    @LogRecord(success = "任务复制",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskId}}")
    public TaskDetailDTO copyByTaskId(@PathVariable("taskId") Long taskId) {
        return taskService.getTaskDetail(taskId, false);
    }

    @Permission(roles = {RoleEnum.SELLER, RoleEnum.SELLER_LEADER})
    @PutMapping("/{taskId}/publish")
    @Operation(summary = "任务发布（任务列表页面）")
    @LogRecord(success = "任务发布",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskId}}")
    public void publish(@PathVariable("taskId") Long taskId) {
        taskService.updateTaskState(taskId, false, SecurityUtil.getUserRoleRelationId(), SecurityUtil.getCurrentRole());
    }

    @Permission(roles = {RoleEnum.SELLER, RoleEnum.SELLER_LEADER})
    @PutMapping("/{taskId}/take-down")
    @Operation(summary = "任务下架")
    @LogRecord(success = "任务下架",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskId}}")
    public void takeDown(@PathVariable("taskId") Long taskId) {
        taskService.updateTaskState(taskId, true, SecurityUtil.getUserRoleRelationId(), SecurityUtil.getCurrentRole());
    }

    @Permission(roles = {RoleEnum.SELLER, RoleEnum.SELLER_LEADER})
    @PostMapping("/save")
    @Operation(summary = "任务保存")
    @LogRecord(success = "任务保存，参数{{#taskDetailDTO}}",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskDetailDTO.taskId}}")
    public void save(@RequestBody TaskDetailDTO taskDetailDTO) {
        taskService.insertUpdateTask(taskDetailDTO, true, SecurityUtil.getCurrentRole(), SecurityUtil.getUserRoleRelationId());
    }

    @Permission(roles = {RoleEnum.SELLER, RoleEnum.SELLER_LEADER})
    @PostMapping("/publish")
    @LogRecord(success = "任务发布，参数{{#taskDetailDTO}}",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskDetailDTO.taskId}}")
    @Operation(summary = "任务发布（任务详情页面）")
    public void publish(@RequestBody TaskDetailDTO taskDetailDTO) {
        taskService.insertUpdateTask(taskDetailDTO, false, SecurityUtil.getCurrentRole(), SecurityUtil.getUserRoleRelationId());
    }

    @Permission(roles = {RoleEnum.OPERATOR, RoleEnum.OPERATOR_LEADER})
    @GetMapping("/audit")
    @Operation(summary = "任务审核获取任务详细信息")
    @LogRecord(success = "任务审核获取任务详细信息",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskId}}")
    public TaskAuditDTO auditByTaskId(@RequestParam("taskId") Long taskId) {
        return taskService.auditByTaskId(taskId);
    }

    @Permission(roles = {RoleEnum.OPERATOR, RoleEnum.OPERATOR_LEADER})
    @PostMapping("/audit-pass")
    @Operation(summary = "任务审核通过")
    @LogRecord(success = "任务审核通过，参数:{{#taskAuditDTO}}",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskAuditDTO.taskId}}")
    public void auditPass(@RequestBody TaskAuditDTO taskAuditDTO) {
        taskService.auditResult(taskAuditDTO, SecurityUtil.getUserRoleRelationId(), true, SecurityUtil.getCurrentRole());
    }

    @Permission(roles = {RoleEnum.OPERATOR, RoleEnum.OPERATOR_LEADER})
    @PostMapping("/audit-fail")
    @Operation(summary = "任务审核不通过")
    @LogRecord(success = "任务审核不通过，参数:{{#taskAuditDTO}}",
            type = OperateLogConstants.TASK,
            bizNo = "{{#taskAuditDTO.taskId}}")
    public void auditFail(@RequestBody TaskAuditDTO taskAuditDTO) {
        taskService.auditResult(taskAuditDTO, SecurityUtil.getUserRoleRelationId(), false, SecurityUtil.getCurrentRole());
    }

    @GetMapping("/download")
    @Operation(summary = "任务列表下载")
    @LogRecord(success = "任务列表下载，参数:{{#pageTaskReqDTO}}",
            type = OperateLogConstants.TASK,
            bizNo = OperateLogConstants.BIZ_NO_PLACEHOLDER)
    public void download(HttpServletResponse response, @ParameterObject PageTaskReqDTO pageTaskReqDTO) throws Exception {
        LoginUser loginUser = SecurityUtil.getLoginUser();
        List<TaskItemDTO> dtoList = taskService.unPage(pageTaskReqDTO, loginUser.getUserRoleRelationId(), loginUser.getCurrentRole());
        taskService.downloadExcel(response, dtoList, loginUser.getCurrentRole());
    }

    @Permission(roles = {RoleEnum.SELLER, RoleEnum.SELLER_LEADER, RoleEnum.OPERATOR, RoleEnum.OPERATOR_LEADER})
    @GetMapping("/task-name-existed")
    @Operation(summary = "任务名称是否存在，true=已存在，false=不存在")
    public Boolean taskNameExisted(@RequestParam("taskId") Long taskId, @RequestParam("taskName") String taskName) {
        return taskService.taskNameExisted(taskId, taskName);
    }

    @GetMapping("/sponsor-category")
    @Operation(summary = "获取广告主的产品类别")
    public CategoryWithSubDTO getBySponsorName(String sponsorName) {
        CategoryWithSubDTO categoryWithSubDTO = sponsorService.getBySponsorName(sponsorName);
        return Objects.isNull(categoryWithSubDTO) ? new CategoryWithSubDTO() : categoryWithSubDTO;
    }

    @PostMapping("/sponsor-category-batch")
    @Operation(summary = "批量获取广告主的产品类别")
    public Set<CategoryWithSubDTO> getBySponsorNameBatch(@RequestBody GetCategoryWithSubDTOReq req) {
        return sponsorService.getBySponsorNameBatch(req.getSponsorNames());
    }

}
