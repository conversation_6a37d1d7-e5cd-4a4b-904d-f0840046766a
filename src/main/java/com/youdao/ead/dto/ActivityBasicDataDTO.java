package com.youdao.ead.dto;

import com.youdao.ead.constant.PlatformCodes;
import com.youdao.ead.constant.PromotionTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/8.
 */
@Data
public class ActivityBasicDataDTO {
    private Long activityId;
    /**
     * value of {@link PromotionTypeEnum#getType()}
     */
    private Integer promotionType;
    /**
     * 删除状态
     */
    private Boolean deleted;
    /**
     * 投放状态
     */
    private Integer operateStatus;
    /**
     * 直投平台
     * @see PlatformCodes
     */
    private String tdpPlatformId;

    private String tdpCreativeIds;

    private String tdpSponsorIds;
}
