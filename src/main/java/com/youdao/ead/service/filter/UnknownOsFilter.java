package com.youdao.ead.service.filter;

import com.youdao.ead.dto.ReporterClickDTO;
import com.youdao.quipu.avro.schema.ThirdPartyClick;
import com.youdao.quipu.kafka.producer.AtLeastOnceKafkaProducer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import outfox.ead.dsp.protocol.youdao.Bid;

import static com.youdao.ead.constant.ClickResponseMsgConstants.MSG_UNKNOWN_OS;
import static com.youdao.ead.constant.DiscardConstants.DISCARD_REASON_UNKNOWN_OS;
import static com.youdao.ead.controller.ReporterController.INVALID_CLICK_TOPIC;

/**
 * <AUTHOR>
 * @date 2024/7/12
 */
@Service
@RequiredArgsConstructor
public class UnknownOsFilter implements DiscardFilter {

    @Override
    public boolean discard(ReporterClickDTO reporterClickDto) {
        if (Boolean.TRUE.equals(reporterClickDto.getRedirect())) {
            return false;
        }
        return reporterClickDto.getOs() == Bid.BidRequest.Device.OS.UNKONWN;
    }

    @Override
    public String getResponseMsg() {
        return MSG_UNKNOWN_OS;
    }

    @Override
    public String getDiscardReason() {
        return DISCARD_REASON_UNKNOWN_OS;
    }

}
