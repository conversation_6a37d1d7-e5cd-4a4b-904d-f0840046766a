package com.youdao.ead.service;

import com.codahale.metrics.Timer;
import com.youdao.ead.config.RedisConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Redis 客户端
 *
 * <AUTHOR> ligd
 * @see <a href="https://infracloud.inner.youdao.com/workbench/project/180/redis/6668/3455df/detail?tabType=overview&pageOrigin=service">百川Redis</a>
 */
@Slf4j
@Service("redisClient")
@RequiredArgsConstructor
public class RedisClient {

    private final RedisConfig redisConfig;

    /**
     * TTL always 30 days.
     */
    @Retryable(value = Exception.class, maxAttempts = 5, backoff = @Backoff(value = 50))
    public void upload(String key, String data) {
        try (Timer.Context ignore = MetricsService.getRedisWriteTimer().time()) {
            redisConfig.getSyncCommands().setex(
                    key,
                    TimeUnit.DAYS.toSeconds(30),
                    Objects.requireNonNull(data)
            );
        } catch (Exception e) {
            log.warn("upload key={} to redis error", key, e);
            MetricsService.getRedisExceptionWriteMeter().mark();
            throw e;
        }
    }

    /**
     * 下载redis数据到本地
     */
    public String download(String key) {
        try (Timer.Context ignore = MetricsService.getRedisTimer().time()) {
            return StringUtils.isNotBlank(key) ? redisConfig.getSyncCommands().get(key) : StringUtils.EMPTY;
        } catch (Exception e) {
            log.error("download key={} from redis error", key, e);
            MetricsService.getRedisExceptionReadMeter().mark();
            return StringUtils.EMPTY;
        }
    }

    /**
     * 获取redis中存储的转化回传时间
     */
    @Retryable(value = Exception.class, maxAttempts = 5, backoff = @Backoff(value = 50))
    public LocalDateTime getConvertTime(String convUniqueKey) {
        try (Timer.Context ignore = MetricsService.getRedisTimer().time()) {
            String timestamp = StringUtils.isNotBlank(convUniqueKey) ? redisConfig.getSyncCommands().get(convUniqueKey) : StringUtils.EMPTY;
            if (StringUtils.isNotBlank(timestamp)) {
                return LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(timestamp)), ZoneOffset.ofHours(8));
            }
            return null;
        } catch (Exception e) {
            log.warn("download conv time by convUniqueKey: {} failed, ", convUniqueKey, e);
            MetricsService.getRedisExceptionReadMeter().mark();
            throw e;
        }
    }

}
