package com.youdao.ead.service.rta;

import lombok.Data;
import outfox.ead.data.rta.RtaMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/30.
 */
@Data
public class RtaResult {
    private boolean bid = false;
    private List<String> sponsorIds;
    private List<String> rtaIds;
    private boolean exception = false;
    private long ttl;
    private String reqId;

    /**
     * 正常响应状态
     * @param rtaResponse
     * @return
     */
    public static RtaResult of(RtaMessage.RtaResponse rtaResponse) {
        RtaResult rtaResult = new RtaResult();
        rtaResult.setRtaIds(rtaResponse.getRtaIdsList());
        rtaResult.setSponsorIds(rtaResponse.getSponsorIdsList());
        rtaResult.setBid(rtaResponse.getBidStatus() == 1);
        rtaResult.setTtl(rtaResult.getTtl());
        rtaResult.setReqId(rtaResult.getReqId());
        return rtaResult;
    }

    public static RtaResult of(String reqId, boolean bid, List<String> rtaIds, List<String> sponsorIds) {
        RtaResult rtaResult = new RtaResult();
        rtaResult.setRtaIds(rtaIds);
        rtaResult.setSponsorIds(sponsorIds);
        rtaResult.setBid(bid);
        rtaResult.setReqId(reqId);
        return rtaResult;
    }

    /**
     * 异常状态
     * @return
     */
    public static RtaResult exceptionOf() {
        RtaResult rtaResult = new RtaResult();
        rtaResult.setException(true);
        return rtaResult;
    }

    /**
     * 初始化化状态
     * @return
     */
    public static RtaResult nullOf(){
        RtaResult rtaResult = new RtaResult();
        rtaResult.setBid(false);
        return rtaResult;
    }
}
