package com.youdao.ead.service.rta.selector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youdao.ead.bo.*;
import com.youdao.ead.config.CentralDogmaClient;
import com.youdao.ead.exception.BizException;
import com.youdao.ead.service.rta.RtaApiService;
import com.youdao.ead.service.rta.ThirdRtaConfig;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.youdao.ead.constant.PlatformCodes.TDP_PLATFORM_BAIDU;

/**
 * <AUTHOR>
 * @date 2023/1/18
 */
@Service
@RequiredArgsConstructor
public class RtaSelectorHolder {

    private final RtaApiService rtaApiService;

    private final CentralDogmaClient centralDogmaClient;

    private final NamedParameterJdbcTemplate jdbcTemplate;

    private final List<AbstractRtaSelector> selectorList;

    private final AtomicReference<Set<Long>> validAids = new AtomicReference<>(new HashSet<>());

    private final AtomicReference<Map<String, Set<Long>>> xiaomiToken2AidsMapRef = new AtomicReference<>(new HashMap<>());

    private final AtomicReference<Map<String, Set<Long>>> huaweiRtaId2AidsMapRef = new AtomicReference<>(new HashMap<>());

    private final AtomicReference<Map<String, Set<Long>>> honorRtaId2AidsMapRef = new AtomicReference<>(new HashMap<>());

    private final AtomicReference<Map<String, Set<Long>>> wifiRtaId2AidsMapRef = new AtomicReference<>(new HashMap<>());

    private final AtomicReference<Map<Long, Set<Long>>> oppoAdId2AidsMapRef = new AtomicReference<>(new HashMap<>());

    private final AtomicReference<Map<Long, Set<Long>>> baiduRtaId2AidsMapRef = new AtomicReference<>(new HashMap<>());

    private final ScheduledExecutorService executor = new ScheduledThreadPoolExecutor(2,
            new BasicThreadFactory.Builder().namingPattern("base-third-rta-config-schedule").daemon(true).build());

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static final TypeReference<List<XiaomiRtaTokenPair>> XIAOMI_RTA_TOKEN_PAIR_TYPE_REF = new TypeReference<List<XiaomiRtaTokenPair>>() {
    };

    private static final TypeReference<List<HuaweiRtaIdPair>> HUAWEI_RTA_ID_PAIR_TYPE_REF = new TypeReference<List<HuaweiRtaIdPair>>() {
    };

    private static final TypeReference<List<HonorRtaIdPair>> HONOR_RTA_ID_PAIR_TYPE_REF = new TypeReference<List<HonorRtaIdPair>>() {
    };

    private static final TypeReference<List<WifiRtaIdPair>> WIFI_RTA_ID_PAIR_TYPE_REF = new TypeReference<List<WifiRtaIdPair>>() {
    };

    private static final TypeReference<List<OppoAdIdPair>> OPPO_AD_ID_PAIR_TYPE_REF = new TypeReference<List<OppoAdIdPair>>() {
    };

    private static final Logger CONF_RELOAD_LOG = LoggerFactory.getLogger("confReloadLog");

    @PostConstruct
    public void init() {
        CONF_RELOAD_LOG.info("load selectorList size = {}", selectorList.size());
        // centralDogma监听注册
        for (AbstractRtaSelector selector : selectorList) {
            if (selector.isWatchCentralDogmaRepoAllFile()) {
                centralDogmaClient.watchRepository(selector.getConfigProj(), selector.getConfigRepo(),
                        // 将获取目录下所有文件逻辑，提交到新的线程中异步执行，防止造成watcher的线程阻塞
                        (revision) -> executor.execute(
                                () -> {
                                    centralDogmaClient.getConfigs(selector.getConfigProj(), selector.getConfigRepo(), revision, selector::reloadConfig);
                                    loadValidActivity();
                                })
                );
            } else {
                centralDogmaClient.watchConfigFile(selector.getConfigProj(), selector.getConfigRepo(), selector.getConfigFile(),
                        (revision, conf) -> {
                            selector.reloadConfig(conf);
                            loadValidActivity();
                        });
            }
        }
        // 定时任务初始化
        executor.scheduleAtFixedRate(() -> {
            try {
                for (AbstractRtaSelector selector : selectorList) {
                    selector.extractThirdConfig();
                    selector.extractExtraThirdRtaConfig();
                }
                loadValidActivity();
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("schedule load rta config failed!", e);
            }
        }, 0, 30, TimeUnit.SECONDS);

        initLoadXiaomiToken();
        initLoadHuaweiRtaId();
        initLoadHonorRtaId();
        initLoadWifiRtaId();
        initLoadOppoAdId();
        initLoadBaiduRtaId();
    }

    private void initLoadXiaomiToken() {
        executor.scheduleAtFixedRate(() -> {
            try {
                loadXiaomiToken();
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("execute load xiaomi rta token failed!", e);
            }
        }, 0, 30, TimeUnit.SECONDS);
    }

    private void initLoadHuaweiRtaId() {
        executor.scheduleAtFixedRate(() -> {
            try {
                loadHuaweiRtaIds();
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("execute load huawei rtaIds failed!", e);
            }
        }, 0, 30, TimeUnit.SECONDS);
    }

    private void initLoadHonorRtaId() {
        executor.scheduleAtFixedRate(() -> {
            try {
                loadHonorRtaIds();
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("execute load honor rtaIds failed!", e);
            }
        }, 0, 30, TimeUnit.SECONDS);
    }

    private void initLoadWifiRtaId() {
        executor.scheduleAtFixedRate(() -> {
            try {
                loadWifiRtaIds();
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("execute load wifi rtaIds failed!", e);
            }
        }, 0, 30, TimeUnit.SECONDS);
    }

    private void initLoadOppoAdId() {
        executor.scheduleAtFixedRate(() -> {
            try {
                loadOppoAdIds();
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("execute load oppo adIds failed!", e);
            }
        }, 0, 30, TimeUnit.SECONDS);
    }

    private void initLoadBaiduRtaId() {
        executor.scheduleAtFixedRate(() -> {
            try {
                loadBaiduRtaConfig();
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("execute load oppo adIds failed!", e);
            }
        }, 0, 30, TimeUnit.SECONDS);
    }

    @PreDestroy
    public void preDestroy() {
        try {
            executor.shutdown();
        } catch (Exception e) {
            CONF_RELOAD_LOG.error("executor shutdown occurred error!", e);
        }
    }

    /**
     * 查询数据库，获取activity信息
     */
    private void loadValidActivity() {
        try {
            List<Long> aidsAdContentIdNotNull = getAidsAdContentIdNotNull();
            validAids.getAndSet(new HashSet<>(aidsAdContentIdNotNull));
        } catch (Exception e) {
            CONF_RELOAD_LOG.error("load valid activity error", e);
            throw e;
        }
    }

    private List<Long> getAidsAdContentIdNotNull() {
        String sql = "select promotion_id " +
                "from ThirdPartPromotionInfo t " +
                " where t.PROMOTION_STATUS = 0 and t.RELATE_AD_CONTENT_ID<>'' ";
        List<Long> list = jdbcTemplate.queryForList(sql, Collections.EMPTY_MAP, Long.class);
        return CollectionUtils.isNotEmpty(list) ? list : Collections.emptyList();
    }

    /**
     * load生效的关联了创意的小米的token
     */
    private void loadXiaomiToken() {
        // 将数据整理成 token -> List<AID> 的格式
        // 其中 LENGTH(XIAOMI_RTA_TOKEN_PAIR) > 3 是为了过滤 null/''/' '/{}/{ }
        String sql = "SELECT PROMOTION_ID, XIAOMI_RTA_TOKEN_PAIR " +
                "FROM ThirdPartPromotionInfo " +
                "WHERE ThirdPartPromotionInfo.PROMOTION_STATUS = 0 " +
                "AND RELATE_AD_CONTENT_ID <> '' " +
                "AND TDP_PLATFORM = 1 " +
                "AND LENGTH(XIAOMI_RTA_TOKEN_PAIR) > 3 " +
                "AND USE_RTA = 1";
        Map<String, Set<Long>> token2AidsMap = new HashMap<>();
        jdbcTemplate.query(sql, (resultSet, i) -> {
            Long promotionId = null;
            String tokenPairs = null;
            try {
                promotionId = resultSet.getLong("PROMOTION_ID");
                tokenPairs = resultSet.getString("XIAOMI_RTA_TOKEN_PAIR");
                List<XiaomiRtaTokenPair> tokenPairList = OBJECT_MAPPER.readValue(tokenPairs, XIAOMI_RTA_TOKEN_PAIR_TYPE_REF);
                Set<String> tokens = tokenPairList.stream().map(XiaomiRtaTokenPair::getToken).collect(Collectors.toSet());
                for (String token : tokens) {
                    token2AidsMap.computeIfAbsent(token, mapping -> new HashSet<>()).add(promotionId);
                }
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("read json failed, promotionId = {} include token: {}, will ignored.", promotionId, tokenPairs, e);
            }
            return null;
        });
        xiaomiToken2AidsMapRef.getAndSet(token2AidsMap);
        CONF_RELOAD_LOG.info("xiaomi rta token was update, new token to aids map: {}", token2AidsMap);
    }

    private void loadHuaweiRtaIds() {
        // 将数据整理成 rtaId -> List<AID> 的格式
        // 其中 LENGTH(HUAWEI_RTA_ID_PAIR) > 3 是为了过滤 null/''/' '/{}/{ }
        String sql = "SELECT PROMOTION_ID, HUAWEI_RTA_ID_PAIR " +
                "FROM ThirdPartPromotionInfo " +
                "WHERE ThirdPartPromotionInfo.PROMOTION_STATUS = 0 " +
                "AND RELATE_AD_CONTENT_ID <> '' " +
                "AND TDP_PLATFORM = 3 " +
                "AND LENGTH(HUAWEI_RTA_ID_PAIR) > 3 " +
                "AND USE_RTA = 1";
        Map<String, Set<Long>> rtaId2Aid = new HashMap<>();
        jdbcTemplate.query(sql, (resultSet, i) -> {
            Long promotionId = null;
            String rtaIdPairs = null;
            try {
                promotionId = resultSet.getLong("PROMOTION_ID");
                rtaIdPairs = resultSet.getString("HUAWEI_RTA_ID_PAIR");
                List<HuaweiRtaIdPair> rtaIdPairList = OBJECT_MAPPER.readValue(rtaIdPairs, HUAWEI_RTA_ID_PAIR_TYPE_REF);
                Set<String> tokens = rtaIdPairList.stream().map(HuaweiRtaIdPair::getRtaId).collect(Collectors.toSet());
                for (String token : tokens) {
                    rtaId2Aid.computeIfAbsent(token, mapping -> new HashSet<>()).add(promotionId);
                }
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("read json failed, promotionId = {} include token: {}, will ignored.", promotionId, rtaIdPairs, e);
            }
            return null;
        });
        huaweiRtaId2AidsMapRef.getAndSet(rtaId2Aid);
        CONF_RELOAD_LOG.info("huawei rta rta id was update, new rtaId to aids map: {}", rtaId2Aid);
    }

    private void loadHonorRtaIds() {
        // 将数据整理成 rtaId -> List<AID> 的格式
        // 其中 LENGTH(HONOR_RTA_ID_PAIR) > 3 是为了过滤 null/''/' '/{}/{ }
        String sql = "SELECT PROMOTION_ID, HONOR_RTA_ID_PAIR " +
                "FROM ThirdPartPromotionInfo " +
                "WHERE ThirdPartPromotionInfo.PROMOTION_STATUS = 0 " +
                "AND RELATE_AD_CONTENT_ID <> '' " +
                "AND TDP_PLATFORM = 6 " +
                "AND LENGTH(HONOR_RTA_ID_PAIR) > 3 " +
                "AND USE_RTA = 1";
        Map<String, Set<Long>> rtaId2Aid = new HashMap<>();
        jdbcTemplate.query(sql, (resultSet, i) -> {
            Long promotionId = null;
            String rtaIdPairs = null;
            try {
                promotionId = resultSet.getLong("PROMOTION_ID");
                rtaIdPairs = resultSet.getString("HONOR_RTA_ID_PAIR");
                List<HonorRtaIdPair> rtaIdPairList = OBJECT_MAPPER.readValue(rtaIdPairs, HONOR_RTA_ID_PAIR_TYPE_REF);
                Set<String> rtaIdSet = rtaIdPairList.stream().map(HonorRtaIdPair::getRtaId).collect(Collectors.toSet());
                for (String rtaId : rtaIdSet) {
                    rtaId2Aid.computeIfAbsent(rtaId, mapping -> new HashSet<>()).add(promotionId);
                }
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("read json failed, promotionId = {} include rtaIdPairs: {}, will ignored.", promotionId, rtaIdPairs, e);
            }
            return null;
        });
        honorRtaId2AidsMapRef.getAndSet(rtaId2Aid);
        CONF_RELOAD_LOG.info("Honor rta id was update, new rtaId to aids map: {}", rtaId2Aid);
    }

    private void loadWifiRtaIds() {
        // 将数据整理成 rtaId -> List<AID> 的格式
        // 其中 LENGTH(WIFI_RTA_ID_PAIR) > 3 是为了过滤 null/''/' '/{}/{ }
        String sql = "SELECT PROMOTION_ID, WIFI_RTA_ID_PAIR " +
                "FROM ThirdPartPromotionInfo " +
                "WHERE ThirdPartPromotionInfo.PROMOTION_STATUS = 0 " +
                "AND RELATE_AD_CONTENT_ID <> '' " +
                "AND TDP_PLATFORM = 5 " +
                "AND LENGTH(WIFI_RTA_ID_PAIR) > 3 " +
                "AND USE_RTA = 1";
        Map<String, Set<Long>> rtaId2Aid = new HashMap<>();
        jdbcTemplate.query(sql, (resultSet, i) -> {
            Long promotionId = null;
            String rtaIdPairs = null;
            try {
                promotionId = resultSet.getLong("PROMOTION_ID");
                rtaIdPairs = resultSet.getString("WIFI_RTA_ID_PAIR");
                List<WifiRtaIdPair> rtaIdPairList = OBJECT_MAPPER.readValue(rtaIdPairs, WIFI_RTA_ID_PAIR_TYPE_REF);
                Set<String> rtaIdSet = rtaIdPairList.stream().map(WifiRtaIdPair::getRtaId).collect(Collectors.toSet());
                for (String rtaId : rtaIdSet) {
                    rtaId2Aid.computeIfAbsent(rtaId, mapping -> new HashSet<>()).add(promotionId);
                }
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("read json failed, promotionId = {} include rtaIdPairs: {}, will ignored.", promotionId, rtaIdPairs, e);
            }
            return null;
        });
        wifiRtaId2AidsMapRef.getAndSet(rtaId2Aid);
        CONF_RELOAD_LOG.info("wifi rta id was update, new rtaId to aids map: {}", rtaId2Aid);
    }

    private void loadOppoAdIds() {
        // 将数据整理成 rtaId -> List<AID> 的格式
        // 其中 LENGTH(OPPO_AD_ID_PAIR) > 3 是为了过滤 null/''/' '/{}/{ }
        String sql = "SELECT PROMOTION_ID, OPPO_AD_ID_PAIR " +
                "FROM ThirdPartPromotionInfo " +
                "WHERE ThirdPartPromotionInfo.PROMOTION_STATUS = 0 " +
                "AND RELATE_AD_CONTENT_ID <> '' " +
                "AND TDP_PLATFORM = 7 " +
                "AND LENGTH(OPPO_AD_ID_PAIR) > 3 " +
                "AND USE_RTA = 1";
        Map<Long, Set<Long>> oppoAdId2Aid = new HashMap<>();
        jdbcTemplate.query(sql, (resultSet, i) -> {
            Long promotionId = null;
            String oppoAdIdPairs = null;
            try {
                promotionId = resultSet.getLong("PROMOTION_ID");
                oppoAdIdPairs = resultSet.getString("OPPO_AD_ID_PAIR");
                List<OppoAdIdPair> oppoAdIdPairList = OBJECT_MAPPER.readValue(oppoAdIdPairs, OPPO_AD_ID_PAIR_TYPE_REF);
                Set<Long> adIdSet = oppoAdIdPairList.stream().map(OppoAdIdPair::getAdId).collect(Collectors.toSet());
                for (Long adId : adIdSet) {
                    oppoAdId2Aid.computeIfAbsent(adId, mapping -> new HashSet<>()).add(promotionId);
                }
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("read json failed, promotionId = {} include adIdPairs: {}, will ignored.", promotionId, oppoAdIdPairs, e);
            }
            return null;
        });
        oppoAdId2AidsMapRef.getAndSet(oppoAdId2Aid);
        CONF_RELOAD_LOG.info("oppo ad id was update, new adId to aids map: {}", oppoAdId2Aid);
    }

    private void loadBaiduRtaConfig() {
        String sql = "SELECT PROMOTION_ID, BAIDU_RTA_ID_PAIR " +
                "FROM ThirdPartPromotionInfo " +
                "WHERE ThirdPartPromotionInfo.PROMOTION_STATUS = 0 " +
                "AND RELATE_AD_CONTENT_ID <> '' " +
                "AND TDP_PLATFORM = 8 " +
                "AND LENGTH(BAIDU_RTA_ID_PAIR) > 3 " +
                "AND USE_RTA = 1";
        Map<Long, Set<Long>> baiduRtaId2Aid = new HashMap<>();
        jdbcTemplate.query(sql, (resultSet, i) -> {
            Long promotionId = null;
            String baiduRtaIdPairsJson = null;
            try {
                promotionId = resultSet.getLong("PROMOTION_ID");
                baiduRtaIdPairsJson = resultSet.getString("BAIDU_RTA_ID_PAIR");
                List<RtaIdPair> baiduRtaIdPairs = OBJECT_MAPPER.readValue(baiduRtaIdPairsJson, new TypeReference<List<RtaIdPair>>(){});
                Set<Long> rtaIdSet = baiduRtaIdPairs.stream().map(RtaIdPair::getRtaId).collect(Collectors.toSet());
                for (Long rtaId : rtaIdSet) {
                    baiduRtaId2Aid.computeIfAbsent(rtaId, mapping -> new HashSet<>()).add(promotionId);
                }
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("read json failed, promotionId = {} include rtaIdPairs: {}, will ignored.", promotionId, baiduRtaIdPairsJson, e);
            }
            return null;
        });
        baiduRtaId2AidsMapRef.getAndSet(baiduRtaId2Aid);
        CONF_RELOAD_LOG.info("baidu rta id was update, new rtaId to aids map: {}", baiduRtaId2Aid);
    }

    /**
     * 校验活动id是否关联了智选创意
     *
     * @param requestAids
     */
    public void checkActivity(List<Long> requestAids) {
        if (CollectionUtils.isEmpty(requestAids)) {
            throw new BizException("aids 不能为空！");
        }
        for (Long requestAid : requestAids) {
            // 数据库是否包含该aid
            if(!validAids.get().contains(requestAid)){
                throw new BizException(String.format("包含不合法的活动id:%d", requestAid));
            }
            // 是否开启了该aid
            boolean isAidClosed = true;
            for (AbstractRtaSelector selector : selectorList) {
                if (selector.containAidForOpen(requestAid)) {
                    isAidClosed = false;
                    break;
                }
            }
            if (isAidClosed) {
                throw new BizException(String.format("当前活动id:%d 未开启rta", requestAid));
            }
        }
    }

    /**
     * 根据活动id，匹配智选对接的rta平台配置
     *
     * @param aids@return
     */
    public List<ThirdRtaConfig> getThirdRtaConfig(List<Long> aids) {
        List<ThirdRtaConfig> list = new ArrayList<>();
        selectorList.forEach(x->{
            List<ThirdRtaConfig> configs = x.getThirdRtaConfig(aids);
            if (CollectionUtils.isNotEmpty(configs)) {
                list.addAll(configs);
            }
        });
        return list;
    }

    /**
     * 根据活动id，匹配智选对接的rta平台配置
     *
     * @param aids@return
     */
    public List<ThirdRtaConfig> getExtraThirdRtaConfig(List<Long> aids) {
        List<ThirdRtaConfig> list = new ArrayList<>();
        selectorList.forEach(x -> {
            List<ThirdRtaConfig> configs = x.getExtraThirdRtaConfig(aids);
            if (CollectionUtils.isNotEmpty(configs)) {
                list.addAll(configs);
            }
        });
        return list;
    }

    public Map<String, Set<Long>> getAidsByXiaomiRtaToken(Set<String> tokens) {
        Map<String, Set<Long>> result = new HashMap<>();
        for (Map.Entry<String, Set<Long>> entry : xiaomiToken2AidsMapRef.get().entrySet()) {
            if (tokens.contains(entry.getKey())) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }

    /**
     * 是否为rta推广活动
     * @param aid
     * @return
     */
    public boolean hasRtaConfig(Long aid) {
        List<ThirdRtaConfig> rtaConfigs = getThirdRtaConfig(Collections.singletonList(aid));
        return CollectionUtils.isNotEmpty(rtaConfigs);
    }

    /**
     * 校验推广活动是否开启了rta结果持久化
     *
     * @param aid
     * @return
     */
    public boolean isRtaPersistEnabled(Long aid) {
        List<ThirdRtaConfig> rtaConfigs = getThirdRtaConfig(Collections.singletonList(aid));
        if (CollectionUtils.isNotEmpty(rtaConfigs)) {
            for (ThirdRtaConfig rtaConfig : rtaConfigs) {
                if (rtaConfig.isPersistEnabled()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验请求id是否必传
     * @param aid
     * @return
     */
    public boolean requiredRequestId(Long aid) {
        List<ThirdRtaConfig> rtaConfigs = getThirdRtaConfig(Collections.singletonList(aid));
        if (CollectionUtils.isNotEmpty(rtaConfigs)) {
            for (ThirdRtaConfig rtaConfig : rtaConfigs) {
                if (rtaConfig.isRequiredReqId()) {
                    return true;
                }
            }
        }
        return false;
    }

    public Map<String, Set<Long>> getAidsByHuaweiRtaIds(Set<String> rtaIds) {
        Map<String, Set<Long>> result = new HashMap<>();
        for (Map.Entry<String, Set<Long>> entry : huaweiRtaId2AidsMapRef.get().entrySet()) {
            if (rtaIds.contains(entry.getKey())) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }

    public static Boolean isAnyActivityEnableRta(List<ThirdRtaConfig> config, Set<Long> aids) {
        for (ThirdRtaConfig thirdRtaConfig : config) {
            if (org.apache.commons.collections4.CollectionUtils.containsAny(thirdRtaConfig.getActivityConfigMap().keySet(), aids)) {
                return true;
            }
        }
        return false;
    }

    public Map<String, Set<Long>> getAidsByHonorRtaIds(Set<String> rtaIds) {
        return honorRtaId2AidsMapRef.get().entrySet().stream()
                .filter(entry -> rtaIds.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public Set<Long> getAidsByWifiRtaId(String rtaId) {
        return wifiRtaId2AidsMapRef.get().getOrDefault(rtaId, Collections.emptySet());
    }

    /**
     * 获取oppo策略id 和 智选推广活动id的映射
     *
     * @param oppoAdIds 这里接口升级改成了策略id
     * @return
     */
    public Map<Long, Set<Long>> getAidsByOppoAdIds(Set<Long> oppoAdIds) {
        if (CollectionUtils.isEmpty(oppoAdIds)) {
            return Collections.emptyMap();
        }
        return oppoAdId2AidsMapRef.get().entrySet().stream()
                .filter(entry -> oppoAdIds.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private Map<Long, Set<Long>> getRtaId2AidsMap(String tdpPlatForm) {
        Map<Long, Set<Long>> rtaId2AidsMap = new HashMap<>();
        if (TDP_PLATFORM_BAIDU.equals(tdpPlatForm)) {
            rtaId2AidsMap = baiduRtaId2AidsMapRef.get();
        }
        return rtaId2AidsMap;
    }

    /**
     * 根据apiCode 和 第三方平台 查找第三方rtaId和关联的aid的映射
     * @param apiCode
     * @param tdpPlatForm
     * @return
     */
    public RtaApiConfig getRtaApiConfig(String apiCode, String tdpPlatForm) {
        RtaApiConfig rtaApiConfig = new RtaApiConfig();
        RtaApiEntry rtaApiEntry = rtaApiService.getRtaApiEntry(apiCode, tdpPlatForm);
        if (rtaApiEntry != null) {
            Map<Long, Set<Long>> rtaId2AidsMap = getRtaId2AidsMap(tdpPlatForm);
            rtaId2AidsMap.forEach((rtaId, aids) -> {
                List<ThirdRtaConfig> thirdRtaConfigs = getThirdRtaConfig(new ArrayList<>(aids));
                for (ThirdRtaConfig thirdRtaConfig : thirdRtaConfigs) {
                    boolean active = false;
                    // 匹配客户接口
                    if (rtaApiEntry.getRtaServers().contains(thirdRtaConfig.getName())) {
                        for (Long aid : aids) {
                            // 匹配推广活动id
                            if (thirdRtaConfig.getActivityConfigMap().containsKey(aid)) {
                                rtaApiConfig.getRtaId2ActivityIds().computeIfAbsent(rtaId, k -> new HashSet<>()).add(aid);
                                active = true;
                            }
                        }
                    }
                    if (active) {
                        rtaApiConfig.getThirdRtaConfigs().add(thirdRtaConfig);
                    }
                }
            });
        }
        return rtaApiConfig;
    }
}
