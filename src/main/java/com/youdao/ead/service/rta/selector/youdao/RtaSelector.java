package com.youdao.ead.service.rta.selector.youdao;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.linecorp.centraldogma.common.Entry;
import com.youdao.ead.constant.RtaMappingType;
import com.youdao.ead.service.rta.RtaEnum;
import com.youdao.ead.service.rta.ThirdRtaConfig;
import com.youdao.ead.service.rta.selector.AbstractRtaSelector;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2022/11/10.
 */
@Service
@RequiredArgsConstructor
public class RtaSelector extends AbstractRtaSelector {

    @Value("${central-dogma-config-rta-proj}")
    private String configProj;

    @Value("${central-dogma-config-rta-repo}")
    private String configRepo;

    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private AtomicReference<List<RtaConfig>> rtaConfigRef = new AtomicReference<>(new ArrayList<>());

    @Override
    public void reloadConfig(Map<String, Entry<?>> confMap) {
        try {
            CONF_RELOAD_LOG.info("rta config files reloaded from centralDogma {} {}", getConfigProj(), getConfigRepo());
            List<RtaConfig> rtaConfigList = new ArrayList<>();
            for (Map.Entry<String, Entry<?>> confEntry : confMap.entrySet()) {
                Entry<?> value = confEntry.getValue();
                String contentAsText = value.contentAsText();
                CONF_RELOAD_LOG.info("rta config content:{}", contentAsText);
                RtaConfig rtaConfig = objectMapper.readValue(contentAsText, RtaConfig.class);
                rtaConfigList.add(rtaConfig);
            }
            if (CollectionUtils.isEmpty(rtaConfigList)) {
                CONF_RELOAD_LOG.error("alert: rta config list is empty!");
            }
            rtaConfigRef.getAndSet(rtaConfigList);
            extractThirdConfig();
            extractExtraThirdRtaConfig();
            CONF_RELOAD_LOG.info("load rta config success!");
        } catch (Exception e) {
            CONF_RELOAD_LOG.error("load rta config failed!", e);
        }
        CONF_RELOAD_LOG.info("after reload rta config, thirdRtaConfigRef={}", thirdRtaConfigRef.get());
    }

    @Override
    public void extractThirdConfig() {
        try {
            List<ThirdRtaConfig> thirdRtaConfigs = loadRtaConfig(this::getActivityIdsByAdContentId);
            thirdRtaConfigRef.getAndSet(thirdRtaConfigs);
            Set<Long> tempRtaAids = new HashSet<>();
            thirdRtaConfigs.forEach(thirdRtaConfig -> {
                tempRtaAids.addAll(thirdRtaConfig.getActivityConfigMap().keySet());
            });
            rtaAidsRef.getAndSet(tempRtaAids);
        } catch (Exception e) {
            CONF_RELOAD_LOG.error("extract third rta config error", e);
            throw e;
        }
    }

    @Override
    public void extractExtraThirdRtaConfig() {
        try {
            List<ThirdRtaConfig> thirdRtaConfigs = loadRtaConfig(this::getExtraActivity);
            extraThirdRtaConfigRef.getAndSet(thirdRtaConfigs);
        } catch (Exception e) {
            CONF_RELOAD_LOG.error("extract third rta config error", e);
            throw e;
        }
    }

    private List<ThirdRtaConfig>  loadRtaConfig(Function<List<Long>, List<Activity>> activityFunction) {
        List<RtaConfig> rtaConfigList = rtaConfigRef.get();
        List<ThirdRtaConfig> thirdRtaConfigs = new ArrayList<>();
        for (RtaConfig rtaConfig : rtaConfigList) {
            Map<Long, ThirdRtaConfig.ActivityConfig> activityConfigMap = new HashMap<>();
            if (rtaConfig.isEnable()) {
                Set<RtaConfig.RtaMapping> sponsorRtaMappings = rtaConfig.getSponsorIds();
                for (RtaConfig.RtaMapping rtaMapping : sponsorRtaMappings) {
                    if (rtaMapping.checkEnable()) {
                        List<Long> groupIds = getGroupIdsBySponsorId(rtaMapping.getId());
                        loadThirdRtaConfig(RtaMappingType.SPONSOR, rtaMapping, groupIds, activityConfigMap, activityFunction);
                    }
                }

                Set<RtaConfig.RtaMapping> campaignRtaMappings = rtaConfig.getCampaignIds();
                for (RtaConfig.RtaMapping rtaMapping : campaignRtaMappings) {
                    if (rtaMapping.checkEnable()) {
                        List<Long> groupIds = getGroupIdsByCampaignId(rtaMapping.getId());
                        loadThirdRtaConfig(RtaMappingType.CAMPAIGN, rtaMapping, groupIds, activityConfigMap, activityFunction);
                    }
                }

                Set<RtaConfig.RtaMapping> groupRtaMappings = rtaConfig.getGroupIds();
                for (RtaConfig.RtaMapping rtaMapping : groupRtaMappings) {
                    if (rtaMapping.checkEnable()) {
                        loadThirdRtaConfig(RtaMappingType.GROUP, rtaMapping, Collections.singletonList(rtaMapping.getId()), activityConfigMap, activityFunction);
                    }
                }
            }
            ThirdRtaConfig thirdRtaConfig = new ThirdRtaConfig();
            switch (rtaConfig.getProtocol()) {
                case "youdao":
                    thirdRtaConfig.setRtaEnum(RtaEnum.YOUDAO_RTA);
                    break;
                case "vip":
                    thirdRtaConfig.setRtaEnum(RtaEnum.VIP_RTA);
                    break;
                default:
                    thirdRtaConfig.setRtaEnum(RtaEnum.YOUDAO_RTA);
                    break;
            }
            thirdRtaConfig.setName(rtaConfig.getName());
            thirdRtaConfig.setHost(rtaConfig.getHost());
            thirdRtaConfig.setIgnoreRtaIds(rtaConfig.isIgnoreRtaIds());
            thirdRtaConfig.setRequiredReqId(rtaConfig.isRequiredReqId());
            thirdRtaConfig.setPersistEnabled(rtaConfig.isPersistEnabled());
            thirdRtaConfig.setType(rtaConfig.getType());
            thirdRtaConfig.setExceptionBid(rtaConfig.isExceptionBid());
            thirdRtaConfig.setTrafficAllocationEnabled(rtaConfig.isTrafficAllocationEnabled());
            thirdRtaConfig.setOwnFlowRatioLow(rtaConfig.getOwnFlowRatioLow());
            thirdRtaConfig.setOwnFlowRatioHigh(rtaConfig.getOwnFlowRatioHigh());
            thirdRtaConfig.setActivityConfigMap(activityConfigMap);
            thirdRtaConfigs.add(thirdRtaConfig);
        }
        return thirdRtaConfigs;
    }

    private void loadThirdRtaConfig(Integer rtaMappingType, RtaConfig.RtaMapping rtaMapping, List<Long> groupIds, Map<Long, ThirdRtaConfig.ActivityConfig> thirdRtaConfigMap, Function<List<Long>, List<Activity>> activityFunction) {
        List<Activity> activities = activityFunction.apply(groupIds);
        for (Activity activity : activities) {
            ThirdRtaConfig.ActivityConfig activityConfig = new ThirdRtaConfig.ActivityConfig();
            activityConfig.setRtaIds(rtaMapping.getRtaIds());
            activityConfig.setSponsorId(activity.getSponsorId());
            activityConfig.setCampaignId(activity.getCampaignId());
            activityConfig.setGroupId(activity.getGroupId());
            activityConfig.setAid(activity.getAid());
            activityConfig.setTdpPlatform(activity.getTdpPlatform());
            activityConfig.setRtaMappingType(rtaMappingType);
            thirdRtaConfigMap.put(activity.getAid(), activityConfig);
        }
    }

    @Override
    public String getConfigProj() {
        return configProj;
    }

    @Override
    public String getConfigRepo() {
        return configRepo;
    }

    @Override
    public String getConfigFile() {
        return null;
    }

}
