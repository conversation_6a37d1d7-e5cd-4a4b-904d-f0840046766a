package com.youdao.ead.service.rta;

import com.codahale.metrics.Timer;
import com.google.protobuf.InvalidProtocolBufferException;
import com.youdao.ead.antifraud.ThirdRtaConfigContext;
import com.youdao.ead.constant.CacheStrategyEnum;
import com.youdao.ead.util.CommonUtils;
import com.youdao.ead.util.MachineUtils;
import com.youdao.ead.util.MetricUtils;
import com.youdao.ead.vo.request.ThirdRtaRequest;
import com.youdao.quipu.avro.schema.ActivityFraudItem;
import com.youdao.quipu.avro.schema.Caid;
import com.youdao.quipu.avro.schema.RtaRecord;
import com.youdao.quipu.kafka.producer.AtLeastOnceKafkaProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import outfox.ead.dsp.protocol.tanx.rta.TanxRta;
import outfox.ead.dsp.protocol.youdao.Bid;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/1/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TanxRtaService extends AbstractRtaService {

    private AtLeastOnceKafkaProducer quipuKafkaProducer = AtLeastOnceKafkaProducer.getInstance();

    private final SamplingLogService samplingLogService;

    private final CommonUtils commonUtils;

    public static final String SOCKET_TIMEOUT_EXCEPTION_READ_TIMED_OUT = "java.net.SocketTimeoutException: Read timed out";
    public static final int STATUS_CODE_SUCCESS = 0;

    @Value("${tanx.rta.apiVersion}")
    private String apiVersion;

    @Value("${tanx.rta.androidPid}")
    private String androidPid;

    @Value("${tanx.rta.iosPid}")
    private String iosPid;

    @Value("${tanx.rta.recordTopic}")
    private String rtaRecordTopic;

    @Value("${tanx.rta.maxConnect}")
    private int maxConnect;

    @Value("${tanx.rta.maxPerRoute}")
    private int maxPerRoute;

    @Value("${tanx.rta.connectTimeout}")
    private int connectTimeout;

    @Value("${third-party.rta.apiRtaConnectTimeout}")
    private int apiRtaConnectTimeout;

    @Value("${tanx.rta.readTimeout}")
    private int readTimeout;

    @Value("${third-party.rta.apiRtaReadTimeout}")
    private int apiRtaReadTimeout;

    private static CloseableHttpClient httpClient = null;

    private RequestConfig defaultCustomRequestConfig;

    @PostConstruct
    public void init() throws Exception {
        defaultCustomRequestConfig = buildRequestConfig(readTimeout, connectTimeout);
        httpClient = httpClient(maxConnect, maxPerRoute, defaultCustomRequestConfig);
    }

    @PreDestroy
    public void destroy() throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
    }

    private TanxRta.RtaRequest buildRequest(ThirdRtaConfigContext contextPlus, String generatedReqId, ThirdRtaConfig config) {
        ThirdRtaRequest request = contextPlus.thirdRtaRequest();
        List<Long> aidsNotFraudFilter = contextPlus.getAidsNotFraudFilter();
        Map<Long, ThirdRtaConfig.ActivityConfig> activityConfigMap = config.getActivityConfigMap();
        Set<String> rtaIds = new HashSet<>();
        for (Long matchAid : aidsNotFraudFilter) {
            rtaIds.addAll(activityConfigMap.get(matchAid).getRtaIds());
        }
        TanxRta.RtaRequest.Device device = TanxRta.RtaRequest.Device.newBuilder()
                .setIp(contextPlus.fixedIp())
                .setUserAgent(contextPlus.fixedUa())
                .setOsType(mappingOsType(contextPlus.fixedOsEnum()))
                .setIdfa(Objects.toString(request.getIdfa(), ""))
                .setImeiMd5(Objects.toString(request.getImeiMd5(), "").toLowerCase())
                .setOaid(Objects.toString(request.getOaid(), ""))
                .addAllCaids(extractTanxCaids(request))
                .build();
        return TanxRta.RtaRequest.newBuilder()
                .setVersion(apiVersion)
                .setId(generatedReqId)
                .setPid(getPid(contextPlus.fixedOsEnum()))
                .addAllRtaIds(rtaIds)
                .setDevice(device)
                .build();
    }

    private Iterable<? extends TanxRta.RtaRequest.Device.CAID> extractTanxCaids(ThirdRtaRequest request) {
        List<TanxRta.RtaRequest.Device.CAID> caids = new ArrayList<>();
        for (Caid ydCaid : request.getCaids()) {
            caids.add(
                    TanxRta.RtaRequest.Device.CAID.newBuilder()
                            .setVer(Objects.toString(ydCaid.getVersion(), ""))
                            .setCaid(Objects.toString(ydCaid.getCaid(), ""))
                            .build()
            );
        }
        return caids;
    }

    private TanxRta.RtaRequest.Device.OsType mappingOsType(Bid.BidRequest.Device.OS os) {
        switch (os) {
            case ANDROID:
                return TanxRta.RtaRequest.Device.OsType.OS_ANDROID;
            case IOS:
                return TanxRta.RtaRequest.Device.OsType.OS_IOS;
            case MAC:
            case WINDOWS:
                return TanxRta.RtaRequest.Device.OsType.OS_PC;
            default:
                return TanxRta.RtaRequest.Device.OsType.OS_UNKNOWN;
        }
    }

    private String getPid(Bid.BidRequest.Device.OS os) {
        if (os == Bid.BidRequest.Device.OS.ANDROID) {
            return androidPid;
        } else if (os == Bid.BidRequest.Device.OS.IOS) {
            return iosPid;
        }
        return "";
    }

    private RtaResult getResult(String name,
                                    String host,
                                    ThirdRtaConfigContext contextPlus,
                                    TanxRta.RtaRequest rtaRequest,
                                    int connectTimeout,
                                    int requestTimeout) throws Exception {
        if (null == httpClient) {
            log.error("closeableHttpAsyncClient is null");
            return null;
        }
        try (Timer.Context ignore = MetricUtils.getRtaTimer(name).time()) {
            HttpResponse httpResponse;
            HttpPost post = new HttpPost(host);
            // httpClient4 在请求级别重写超时时间的设置，需要保证readTimeout, connectTimeout, connectionRequestTimeout均被正确配置
            RequestConfig requestConfig = defaultCustomRequestConfig != null
                    ? RequestConfig.copy(defaultCustomRequestConfig).setSocketTimeout(requestTimeout).setConnectTimeout(connectTimeout).build()
                    : buildRequestConfig(requestTimeout, connectTimeout);
            post.setConfig(requestConfig);
            post.setHeader(org.apache.http.HttpHeaders.CONTENT_TYPE, "application/x-protobuf");
            ByteArrayEntity entity = new ByteArrayEntity(rtaRequest.toByteArray());
            post.setEntity(entity);
            httpResponse = httpClient.execute(post);
            TanxRta.RtaResponse resp = convertResponse(httpResponse);
            if (resp == null) {
                throw new IOException(String.format("got empty response from %s", host));
            }
            if (resp.getStatus() != STATUS_CODE_SUCCESS) {
                log.error("got error_status response from :{}, response :{}", host, resp);
                return RtaResult.nullOf();
            }
            boolean bid = resp.getRtaIdsCount() > 0;

            boolean logEnabled = samplingLogService.shouldSampling(name, contextPlus.getMatchedAids());
            if (logEnabled) {
                sendRecord(name, contextPlus, rtaRequest, resp, bid);
            }
            if (bid) {
                MetricUtils.getRtaBidMeter(name).mark();
            }
            return RtaResult.of(rtaRequest.getId(), bid, resp.getRtaIdsList(), Collections.emptyList());
        } catch (Exception e) {
            MetricUtils.getRtaExceptionMeter(name).mark();
            if (log.isDebugEnabled()) {
                log.debug("tanx rta service error", e);
            }
            if (CacheStrategyEnum.FALLBACK == contextPlus.getCacheStrategyEnum()) {
                throw e;
            } else {
                return RtaResult.exceptionOf();
            }
        }
    }

    private void sendRecord(String name,
                            ThirdRtaConfigContext contextPlus,
                            TanxRta.RtaRequest rtaRequest,
                            TanxRta.RtaResponse rtaResponse,
                            boolean bid) {
        ThirdRtaRequest request = contextPlus.thirdRtaRequest();
        RtaRecord.Builder builder = RtaRecord.newBuilder();
        builder.setTimestamp(System.currentTimeMillis());
        builder.setReqId(rtaRequest.getId());
        builder.setAids(contextPlus.getMatchedAids());
        builder.setAppId("");
        builder.setSlotIds(Collections.emptyList());
        builder.setRtaName(name);
        builder.setUrl("");
        builder.setChannelDid(contextPlus.thirdRtaRequest().getChannel());
        builder.setOs(contextPlus.fixedOsEnum().getNumber());
        builder.setImeiMd5(Objects.toString(request.getImeiMd5(), ""));
        builder.setAndroidIdMd5(Objects.toString(request.getAndroidIdMd5(), ""));
        builder.setOaid(Objects.toString(request.getOaid(), ""));
        builder.setIdfa(Objects.toString(request.getIdfa(), ""));
        if (rtaRequest.hasDevice()) {
            TanxRta.RtaRequest.Device device = rtaRequest.getDevice();
            builder.setUa(device.getUserAgent());
            builder.setIp(device.getIp());
        }
        builder.setRtaIds(new ArrayList<>(rtaResponse.getRtaIdsList()));
        Map<CharSequence, CharSequence> moreInfo = new HashMap<>();
        if (request.getDeviceInfo() != null) {
            CommonUtils.deviceInfo2Ext(moreInfo, request.getDeviceInfo());
        }
        moreInfo.put("hostName", MachineUtils.getHostName());
        builder.setExt(moreInfo);
        if (CollectionUtils.isNotEmpty(request.getCaids())) {
            builder.setCaids(request.getCaids());
        }
        builder.setSystem("third-party");
        builder.setFromCache(false);
        builder.setResult(bid ? 1 : 0);
        Map<CharSequence, ActivityFraudItem> fraudItemMap = enrichAntiFraudInfo(contextPlus);
        builder.setActivityFraudItemMap(fraudItemMap);
        builder.setOrigin(contextPlus.getOriginParam());
        builder.setSourceId(contextPlus.getAntiFraudContext().getSourceId());
        quipuKafkaProducer.send(rtaRecordTopic, builder.build());
    }

    @Override
    public RtaResult callRtaRequest(ThirdRtaConfigContext contextPlus, String generatedReqId, int requestTimeout) throws Exception {
        ThirdRtaRequest request = contextPlus.thirdRtaRequest();
        ThirdRtaConfig config = contextPlus.getThirdRtaConfig();
        TanxRta.RtaRequest rtaRequest = this.buildRequest(contextPlus, generatedReqId, config);
        if (request.isInnerRta()) {
            return this.getResult(config.getName(), config.getHost(), contextPlus, rtaRequest, apiRtaConnectTimeout, apiRtaReadTimeout);
        } else {
            int timeout = requestTimeout > 0 ? requestTimeout : this.readTimeout;
            return this.getResult(config.getName(), config.getHost(), contextPlus, rtaRequest, connectTimeout, timeout);
        }
    }

    public static TanxRta.RtaResponse convertResponse(HttpResponse httpResponse)
            throws IOException {
        org.apache.http.HttpEntity httpEntity = httpResponse.getEntity();
        if (httpEntity != null) {
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            byte[] responseContentBytes = EntityUtils.toByteArray(httpEntity);
            if (statusCode == HttpStatus.SC_OK) {
                try {
                    return TanxRta.RtaResponse.parseFrom(responseContentBytes);
                } catch (InvalidProtocolBufferException e) {
                    log.error("error parse rta response.", e);
                    throw e;
                }
            }
        }
        return null;
    }
}
