package com.youdao.ead.service;

import com.youdao.ead.config.KafkaDecoders;
import com.youdao.quipu.avro.schema.ThirdPartyConv;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.youdao.ead.util.ConvertKafkaConfig.THIRD_PARTY_TOPIC;

/**
 * <AUTHOR>
 * @date 2024/10/12
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CallbackConsumer {
    private final KafkaDecoders kafkaDecoders;
    private final CallbackService callbackService;

    private static final List<Integer> REQ_ID_CONV_CT_TYPES = Arrays.asList(5, 6, 7);

    @KafkaListener(topics = THIRD_PARTY_TOPIC, groupId = "${conv.callback.group.id}")
    public void consume(List<ConsumerRecord<byte[], byte[]>> records) {
        for (ConsumerRecord<byte[], byte[]> record : records) {
            try {
                ThirdPartyConv thirdPartyConv = kafkaDecoders.getTopicDecoder(record.topic()).decode(ThirdPartyConv.getClassSchema(), record.value());
                Integer ctType = thirdPartyConv.getCtType();
                if (REQ_ID_CONV_CT_TYPES.contains(ctType)) {
                    try {
                        // 按reqId回传的转化，需要异步处理渠道的转化callback
                        callbackService.handle(thirdPartyConv);
                    } catch (Exception e) {
                        log.error("process third_party_conv callback error! msg:{}", thirdPartyConv, e);
                    }
                }
            } catch (Exception e) {
                log.error("decode third_party_conv callback msg error!", e);
            }
        }
    }
}
