package com.youdao.ead.service;

import com.codahale.metrics.*;
import com.codahale.metrics.graphite.Graphite;
import com.codahale.metrics.graphite.GraphiteReporter;
import com.codahale.metrics.jmx.JmxReporter;
import com.youdao.ead.metrics.CustomGraphite;
import com.youdao.ead.util.MachineUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.management.MalformedObjectNameException;
import javax.management.ObjectName;
import java.net.InetSocketAddress;
import java.util.Hashtable;
import java.util.concurrent.TimeUnit;

/**
 * metrics 监控服务类
 *
 * <AUTHOR>
 */
@Service
@Lazy(false)
@Slf4j
public class MetricsService {

    /**
     * name for global metric registry
     */
    public static final String REPORTER_METRICS = "third-party-reporter-metric-registry";

    /**
     * name for jmx domain
     */
    public static final String JMX_DOMAIN_NAME = "com.youdao.ead";

    public static final String GRAPHITE_DOMAIN_NAME = "third-party-reporter";

    public static final String GRAPHITE_HOST = "graphite-d1f58f78-write.graphite.baichuan.ynode.cn";

    public static final int GRAPHITE_PORT = 8083;

    private GraphiteReporter graphiteReporter;

    @Value("${server.env}")
    private String env;

    @PostConstruct
    private void init() {
        log.info("init metrics...");
        initMetrics();
        log.info("init metrics done");
    }

    /**
     * 初始化 metrics 监控
     */
    private void initMetrics() {
        try {
            MetricRegistry metricRegistry = SharedMetricRegistries.getOrCreate(REPORTER_METRICS);
            JmxReporter jmxReporter = JmxReporter.forRegistry(metricRegistry)
                    .inDomain(JMX_DOMAIN_NAME)
                    .convertRatesTo(TimeUnit.SECONDS)
                    .convertDurationsTo(TimeUnit.MILLISECONDS)
                    .filter((s, metric) -> s.contains("="))
                    .createsObjectNamesWith((type, domain, name) -> {
                        Hashtable<String, String> jmxObjNameProps = extractTypeAndName(name);
                        try {
                            return ObjectName.getInstance(domain, jmxObjNameProps);
                        } catch (MalformedObjectNameException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .build();
            jmxReporter.start();
            log.info("success init metrics jmx reporter");

            Slf4jReporter reporter = Slf4jReporter.forRegistry(metricRegistry)
                    .outputTo(LoggerFactory.getLogger("performanceLog"))
                    .convertRatesTo(TimeUnit.SECONDS)
                    .convertDurationsTo(TimeUnit.MILLISECONDS)
                    .build();
            reporter.start(30, TimeUnit.SECONDS);

            log.info("success init metrics console reporter");

            final Graphite graphite = new CustomGraphite(new InetSocketAddress(GRAPHITE_HOST, GRAPHITE_PORT));
            graphiteReporter = GraphiteReporter.forRegistry(metricRegistry)
                    .prefixedWith(GRAPHITE_DOMAIN_NAME + "." + env + "." + MachineUtils.getHostName().replaceAll("\\.", "_"))
                    .convertRatesTo(TimeUnit.SECONDS)
                    .convertDurationsTo(TimeUnit.MILLISECONDS)
                    .filter(MetricFilter.ALL)
                    .build(graphite);
            graphiteReporter.start(15, TimeUnit.SECONDS);

            log.info("success init metrics graphite reporter");
        } catch (Exception e) {
            log.error("init metrics reporter got error", e);
        }
    }

    private Hashtable<String, String> extractTypeAndName(@NotNull String name) {
        Hashtable<String, String> objNameProperties = new Hashtable<>();
        String[] properties = name.split(",");
        if (properties.length > 0) {
            for (String kv : properties) {
                String[] kvArray = kv.split("=");
                if (kvArray.length == 2 && kvArray[0] != null && kvArray[1] != null) {
                    objNameProperties.put(kvArray[0], kvArray[1]);
                }
            }
        }
        return objNameProperties;
    }

    /**
     * 获取 metric 指标名
     *
     * @param kClass 指标所在类
     * @param name   指标名，不能包含 ","
     * @return JMX Object Name
     * @see "https://docs.oracle.com/javase/7/docs/api/javax/management/ObjectName.html"
     * @see "http://www.oracle.com/technetwork/java/javase/tech/best-practices-jsp-136021.html?ssSourceSiteId=ocomen#mozTocId434075"
     */
    public static String name(@NotNull Class<?> kClass, @NotNull String name) {
        return String.format("type=%s,name=%s", kClass.getSimpleName(), name);
    }
}
