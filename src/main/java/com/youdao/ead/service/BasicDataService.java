package com.youdao.ead.service;

import com.youdao.ead.constant.PromotionTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import outfox.ead.data.ActivityContextInfo;
import outfox.ead.data.ActivityStatistics;
import outfox.ead.dataserv2.client.DataReadProxy;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.youdao.ead.constant.PlatformCodes.TDP_PLATFORM_DEFAULT;

/**
 * <AUTHOR>
 * @date 2024/5/7.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BasicDataService {

    private static final int OPEN = 0;

    private static final int STOP = 1;

    private final DataReadProxy<ActivityContextInfo, Long> activityContextReadProxy;

    private final DataReadProxy<ActivityStatistics, Long> activityStatisticReadProxy;

    private final AtomicReference<Map<String, Long>> tdpPlatformId2ActivityId = new AtomicReference<>(new HashMap<>());

    @PostConstruct
    public void init() {
        loadTdpActivityIds();
    }

    @PreDestroy
    public void shutdown() {
        if (Objects.nonNull(activityContextReadProxy)) {
            activityContextReadProxy.stop();
        }
    }


    /**
     * 直投平台广告id和智选推广活动id的映射
     */
    @Scheduled(initialDelay = 0, fixedDelay = 30 * 1000)
    public void loadTdpActivityIds() {
        try {
            log.info("load tdp activity ids start.");
            Map<String, Long> activityIdMap = new HashMap<>();
            Collection<Long> activityIds = activityContextReadProxy.listKeys();
            for (Long activityId : activityIds) {
                ActivityContextInfo basicData = activityContextReadProxy.getData(activityId);
                if (!TDP_PLATFORM_DEFAULT.equals(String.valueOf(basicData.getTdpPlatformId()))) {
                    // 智选创意id需要映射到第三方平台的id，可能是第三方创意id，也可能是第三方广告主id（广点通在用）
                    String tdpCreativeIds = basicData.getTdpCreativeIds();
                    String tdpSponsorIds = basicData.getTdpSponsorIds();
                    Integer tdpPlatform = basicData.getTdpPlatformId();
                    String suffixes = StringUtils.isNotBlank(tdpCreativeIds) ? tdpCreativeIds
                            : (StringUtils.isNotBlank(tdpSponsorIds) ? tdpSponsorIds : null);
                    if (suffixes != null) {
                        for (String suffix : suffixes.split(",")) {
                            String dataKey = tdpPlatform + ":" + suffix;
                            activityIdMap.put(dataKey, activityId);
                        }
                    }
                }
            }
            tdpPlatformId2ActivityId.getAndSet(activityIdMap);
            log.info("load tdp activity ids end, size:{}.", tdpPlatformId2ActivityId.get().size());
        } catch (Exception e) {
            log.error("load tdp activity ids error", e);
        }
    }

    /**
     * 判断推广活动状态是否关闭
     *
     * @param activityId
     * @return if activity offline
     */
    public boolean isActivityOffline(long activityId) {
        ActivityContextInfo activityBasicDataDTO = getActivityContextInfo(activityId);
        if (activityBasicDataDTO != null) {
            return activityBasicDataDTO.getOperateStatus() == STOP || activityBasicDataDTO.getDeleted();
        } else {
            return false;
        }
    }

    /**
     * 任一aid可以生成caid
     *
     * @param aids
     * @return
     */
    public boolean shouldGenerateCaid(List<Long> aids) {
        if (CollectionUtils.isEmpty(aids)) {
            return false;
        }
        for (Long aid : aids) {
            if (shouldGenerateCaid(aid)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 只有ios下载类型的推广活动可以生成caid
     *
     * @param activityId
     * @return
     */
    public boolean shouldGenerateCaid(long activityId) {
        ActivityContextInfo activityBasicDataDTO = getActivityContextInfo(activityId);
        if (activityBasicDataDTO != null) {
            return !activityBasicDataDTO.getDeleted() && activityBasicDataDTO.getPromotionType() == PromotionTypeEnum.IOS_DOWNLOAD.getType();
        } else {
            return false;
        }
    }

    public PromotionTypeEnum getPromotionType(long activityId) {
        ActivityContextInfo activityContextInfo = getActivityContextInfo(activityId);
        if (activityContextInfo != null) {
            return PromotionTypeEnum.valueOf(activityContextInfo.getPromotionType());
        } else {
            return PromotionTypeEnum.UNKNOWN;
        }
    }

    public ActivityContextInfo getActivityContextInfo(long activityId) {
        return activityContextReadProxy.getData(activityId);
    }

    public ActivityStatistics getActivityStatistic(long activityId) {
        return activityStatisticReadProxy.getData(activityId);
    }

    /**
     * 获取直投平台的推广活动投放参数
     *
     * @param tdpPlatForm
     * @param tdpAdvertisementId
     * @return
     */
    public ActivityContextInfo getTdpActivityContextInfo(String tdpPlatForm, String tdpAdvertisementId) {
        String key = tdpPlatForm + ":" + tdpAdvertisementId;
        Long activityId = tdpPlatformId2ActivityId.get().get(key);
        if(activityId == null){
            log.error("no mapping activityId for tdpPlatform:{} and tdpAdvertisementId:{}", tdpPlatForm, tdpAdvertisementId);
            return null;
        }
        return getActivityContextInfo(activityId);
    }
}
