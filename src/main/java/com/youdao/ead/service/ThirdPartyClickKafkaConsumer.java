package com.youdao.ead.service;

import com.google.common.net.InetAddresses;
import com.google.protobuf.InvalidProtocolBufferException;
import com.youdao.ead.config.AbstractDeviceKvrocksClusterClient;
import com.youdao.ead.config.AvroDecoders;
import com.youdao.ead.config.ParamFilledValidator;
import com.youdao.ead.utils.ActiveProfiles;
import com.youdao.ead.utils.HashUtils;
import com.youdao.ead.utils.TimeUtils;
import com.youdao.quipu.avro.schema.ThirdPartyClick;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.util.Utf8;
import org.apache.avro.Schema;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import outfox.ead.dsp.protocol.youdao.ThirdPartyClickStat;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.youdao.quipu.avro.examples.StringToAvro._string;

/**
 * <AUTHOR>
 * @create 2024-12-07 16:31
 */
@Service
@Slf4j
@Profile(ActiveProfiles.PROFILE_GUIZHOU_CLICK)
public class ThirdPartyClickKafkaConsumer extends AbstractKafkaConsumer<ThirdPartyClick, ThirdPartyClickStat.DeviceRelatedInfo> {

    private static final String PREFIX_KEY = "p_";

    protected static final int TTL_MILLIS = 1000 * 60 * 60 * 24;

    @Autowired
    public ThirdPartyClickKafkaConsumer(
            ParamFilledValidator paramFilledValidator,
            AvroDecoders avroDecoders,
            AbstractDeviceKvrocksClusterClient deviceKvrocksClusterClient) {
        super(avroDecoders, paramFilledValidator, deviceKvrocksClusterClient);
    }

    @KafkaListener(topics = "${thirdPartyKafka.topic.click}", id = "${thirdPartyKafka.groupId.click_guizhou}")
    public void consumeClick(List<ConsumerRecord<byte[], byte[]>> records) {
        processRecords(records);
    }

    @Override
    protected Schema getRecordSchema() {
        return ThirdPartyClick.getClassSchema();
    }

    @Override
    protected Set<String> getDeviceIds(ThirdPartyClick thirdPartyClick) {
        return getDeviceIds(thirdPartyClick.getIdfa(), thirdPartyClick.getOaid(), "", thirdPartyClick.getCaid(), thirdPartyClick.getCaidMd5(), thirdPartyClick.getIdfaMd5(), thirdPartyClick.getOaidMd5(), thirdPartyClick.getImei());
    }

    @Override
    protected Map<String, ThirdPartyClickStat.DeviceRelatedInfo> buildDeviceRelatedInfos(ThirdPartyClick thirdPartyClick, Set<String> deviceIds, Map<String, ThirdPartyClickStat.DeviceRelatedInfo> oldDeviceRelatedInfos) {
        long nowTimestamp = System.currentTimeMillis();
        return deviceIds.stream()
                .collect(Collectors.toMap(
                        deviceId -> deviceId,
                        deviceId -> {
                            ThirdPartyClickStat.DeviceRelatedInfo oldDeviceRelatedInfo = oldDeviceRelatedInfos.get(PREFIX_KEY + deviceId);
                            return ThirdPartyClickStat.DeviceRelatedInfo.newBuilder()
                                    .addAllUas(buildUas(thirdPartyClick, oldDeviceRelatedInfo, nowTimestamp))
                                    .addAllModels(buildModels(thirdPartyClick, oldDeviceRelatedInfo, nowTimestamp))
                                    .addAllOsvs(buildOsvs(thirdPartyClick, oldDeviceRelatedInfo, nowTimestamp))
                                    .addAllIps(buildIps(thirdPartyClick, oldDeviceRelatedInfo, nowTimestamp))
                                    .addAllClickCounts(buildClickCounts(thirdPartyClick, oldDeviceRelatedInfo, nowTimestamp))
                                    .build();
                        }
                ));
    }

    @Override
    protected byte[] serializeDeviceRelatedInfo(ThirdPartyClickStat.DeviceRelatedInfo deviceRelatedInfo) {
        return deviceRelatedInfo.toByteArray();
    }

    @Override
    protected ThirdPartyClickStat.DeviceRelatedInfo deserializeDeviceRelatedInfo(byte[] bytes) throws InvalidProtocolBufferException {
        return ThirdPartyClickStat.DeviceRelatedInfo.parseFrom(bytes);
    }

    @Override
    protected String getRedisPrefixKey() {
        return PREFIX_KEY;
    }

    @Override
    protected int getRedisKeyExpireTime() {
        return TTL_MILLIS;
    }

    @Override
    protected boolean discard(ThirdPartyClick event) {
        return event.getTimestamp() + getRedisKeyExpireTime() < System.currentTimeMillis();
    }

    private List<ThirdPartyClickStat.ClickCount> buildClickCounts(ThirdPartyClick thirdPartyClick
            , ThirdPartyClickStat.DeviceRelatedInfo oldDeviceRelatedInfo, long nowTimestamp) {
        int curDay = (int) TimeUtils.getDayFromTimestamp(nowTimestamp);

        ThirdPartyClickStat.ClickCount newClickCount = ThirdPartyClickStat.ClickCount.newBuilder()
                .setAid(thirdPartyClick.getActivityId())
                .setDay(curDay)
                .setClickCount(1)
                .build();

        // 如果是新设备
        if (oldDeviceRelatedInfo == null) {
            return Collections.singletonList(newClickCount);
        }

        // 处理已有设备的点击记录
        List<ThirdPartyClickStat.ClickCount> result = new ArrayList<>();
        AtomicBoolean exist = new AtomicBoolean(false);
        oldDeviceRelatedInfo.getClickCountsList().stream()
                .filter(clickCount -> clickCount.getDay() == curDay)
                .forEach(clickCount -> {
                    if (clickCount.getAid() == thirdPartyClick.getActivityId()) {
                        exist.set(true);
                        // 同一aid，添加更新后的记录
                        result.add(clickCount.toBuilder()
                                .setClickCount(clickCount.getClickCount() + 1)
                                .build());
                    } else {
                        // 不同aid，保持原样
                        result.add(clickCount);
                    }
                });
        if (!exist.get()) {
            result.add(newClickCount);
        }

        return result;
    }

    private List<ThirdPartyClickStat.Ip> buildIps(ThirdPartyClick thirdPartyClick
            , ThirdPartyClickStat.DeviceRelatedInfo oldDeviceRelatedInfo, long nowTimestamp) {
        List<ThirdPartyClickStat.Ip> ips = oldDeviceRelatedInfo == null ? new ArrayList<>() : oldDeviceRelatedInfo.getIpsList();
        ThirdPartyClickStat.Ip ip = Optional.ofNullable(thirdPartyClick.getOrigin().get(new Utf8("ip")))
                .or(() -> Optional.ofNullable(thirdPartyClick.getIp()))
                .filter(ipValue -> InetAddresses.isInetAddress(_string(ipValue)))
                .map(ipValue -> ThirdPartyClickStat.Ip.newBuilder()
                        .setIp(_string(ipValue).hashCode())
                        .setTimestamp(thirdPartyClick.getTimestamp())
                        .setAid(thirdPartyClick.getActivityId())
                        .build())
                .orElse(null);
        return buildElements(
                ips,
                ip,
                (oldIp, newIp) -> oldIp.getAid() == newIp.getAid() && oldIp.getIp() == newIp.getIp(),
                ThirdPartyClickStat.Ip::getTimestamp,
                (oldIp, timestamp) -> oldIp.toBuilder().setTimestamp(timestamp).build(),
                nowTimestamp,
                IP_EXPIRE_SECONDS
        );
    }

    private List<ThirdPartyClickStat.Osv> buildOsvs(ThirdPartyClick thirdPartyClick
            , ThirdPartyClickStat.DeviceRelatedInfo oldDeviceRelatedInfo, long nowTimestamp) {
        List<ThirdPartyClickStat.Osv> osvs = oldDeviceRelatedInfo == null ? new ArrayList<>() : oldDeviceRelatedInfo.getOsvsList();
        ThirdPartyClickStat.Osv osv = Optional.ofNullable(thirdPartyClick.getOrigin().get(new Utf8("osv")))
                .or(() -> Optional.ofNullable(thirdPartyClick.getOsVersion()))
                .filter(StringUtils::isNotEmpty)
                .map(osvValue -> ThirdPartyClickStat.Osv.newBuilder()
                        .setOsv(_string(osvValue))
                        .setTimestamp(thirdPartyClick.getTimestamp())
                        .build())
                .orElse(null);
        return buildElements(
                osvs,
                osv,
                (oldOsv, newOsv) -> oldOsv.getOsv().equals(newOsv.getOsv()),
                ThirdPartyClickStat.Osv::getTimestamp,
                (oldOsv, timestamp) -> oldOsv.toBuilder().setTimestamp(timestamp).build(),
                nowTimestamp,
                OSV_EXPIRE_SECONDS
        );
    }

    private List<ThirdPartyClickStat.Model> buildModels(ThirdPartyClick thirdPartyClick
            , ThirdPartyClickStat.DeviceRelatedInfo oldDeviceRelatedInfo, long nowTimestamp) {
        List<ThirdPartyClickStat.Model> models = oldDeviceRelatedInfo == null ? new ArrayList<>() : oldDeviceRelatedInfo.getModelsList();
        ThirdPartyClickStat.Model model = Optional.ofNullable(thirdPartyClick.getOrigin().get(new Utf8("deviceModel")))
                .or(() -> Optional.ofNullable(thirdPartyClick.getDeviceModel()))
                .filter(StringUtils::isNotEmpty)
                .map(modelValue -> ThirdPartyClickStat.Model.newBuilder()
                        .setModel(_string(modelValue).toUpperCase())
                        .setTimestamp(thirdPartyClick.getTimestamp())
                        .build())
                .orElse(null);
        return buildElements(
                models,
                model,
                (oldModel, newModel) -> oldModel.getModel().equals(newModel.getModel()),
                ThirdPartyClickStat.Model::getTimestamp,
                (oldModel, timestamp) -> oldModel.toBuilder().setTimestamp(timestamp).build(),
                nowTimestamp,
                MODEL_EXPIRE_SECONDS
        );
    }

    private List<ThirdPartyClickStat.Ua> buildUas(ThirdPartyClick thirdPartyClick
            , ThirdPartyClickStat.DeviceRelatedInfo oldDeviceRelatedInfo, long nowTimestamp) {
        List<ThirdPartyClickStat.Ua> uas = oldDeviceRelatedInfo == null ? new ArrayList<>() : oldDeviceRelatedInfo.getUasList();
        ThirdPartyClickStat.Ua ua = Optional.ofNullable(thirdPartyClick.getOrigin().get(new Utf8("ua")))
                .or(() -> Optional.ofNullable(thirdPartyClick.getUserAgent()))
                .filter(StringUtils::isNotEmpty)
                .map(uaValue -> ThirdPartyClickStat.Ua.newBuilder()
                        .setUa(HashUtils.hashBucket(_string(uaValue)))
                        .setTimestamp(thirdPartyClick.getTimestamp())
                        .build())
                .orElse(null);
        return buildElements(
                uas,
                ua,
                (oldUa, newUa) -> oldUa.getUa() == newUa.getUa(),
                ThirdPartyClickStat.Ua::getTimestamp,
                (oldUa, timestamp) -> oldUa.toBuilder().setTimestamp(timestamp).build(),
                nowTimestamp,
                UA_EXPIRE_SECONDS
        );
    }
}