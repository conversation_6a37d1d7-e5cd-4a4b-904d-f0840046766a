package com.youdao.ead.service;

import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.SharedMetricRegistries;
import com.codahale.metrics.Timer;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.youdao.ead.config.CallbackStorageConfig;
import com.youdao.ead.controller.ReporterController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.youdao.ead.service.MetricsService.REPORTER_METRICS;

/**
 * <AUTHOR>
 * @date 2022/5/13.
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "third-party-reporter.callback.consumer", havingValue = "enable")
public class CallbackService {

    @Value("${third-party-reporter.callback.topic}")
    private String topic;

    @Value("${hbase_table_name}")
    private String callbackTableName;

    private final HbaseService hbaseService;

    private final RedisService redisService;

    private final CallbackStorageConfig callbackStorageConfig;

    private final MetricRegistry metricRegistry = SharedMetricRegistries.getOrCreate(REPORTER_METRICS);

    private final Timer consumerTimer = metricRegistry.timer(MetricsService.name(ReporterController.class, "consumerTimer"));
    private final Meter writeHbaseFailedMeter = metricRegistry.meter(MetricsService.name(ReporterController.class, "write_hbase_failed_meter"));

    /**
     * 线程池处理不过来时，则交给caller线程执行
     */
    ExecutorService executor = new ThreadPoolExecutor(
            500, 1000,
            60000, TimeUnit.MILLISECONDS,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("callback-service-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    log.warn("thread pool rejected execution! execute in CallerRunsPolicy");
                    super.rejectedExecution(r, executor);
                }
            });

    @KafkaListener(topics = "${third-party-reporter.callback.topic}", groupId = "${third-party-reporter.callback.group.id}")
    public void consume(@Header(KafkaHeaders.RECEIVED_MESSAGE_KEY) List<String> keys, @Payload List<String> callbackUrls) {
        try (Timer.Context ignore = consumerTimer.time()) {
            // zip key and url
            List<Pair<String, String>> collect = IntStream.range(0, keys.size())
                    .mapToObj(i -> Pair.of(keys.get(i), callbackUrls.get(i))).collect(Collectors.toList());
            // parallel execute
            executor.submit(() -> saveCallBackUrl(collect));
        } catch (Exception e) {
            log.error("consumer exception!", e);
        }
    }

    private void saveCallBackUrl(List<Pair<String, String>> dataList) {
        if (callbackStorageConfig.canWriteHbase()) {
            try {
                List<Pair<String, String>> failRows = hbaseService.bulkWrite(callbackTableName, "All", "url", dataList);
                if (CollectionUtils.isNotEmpty(failRows)) {
                    fallbackToRedis(failRows);
                }
            } catch (IOException e) {
                fallbackToRedis(dataList);
            }
        } else {
            saveCallbackUrl2Redis(dataList);
        }
    }

    private void fallbackToRedis(List<Pair<String, String>> fallbackDataList) {
        writeHbaseFailedMeter.mark();
        log.warn("hbase write failed, failover to redis!");
        saveCallbackUrl2Redis(fallbackDataList);
    }

    private void saveCallbackUrl2Redis(List<Pair<String, String>> fallbackDataList) {
        for (Pair<String, String> rowData : fallbackDataList) {
            try {
                redisService.upload(rowData.getLeft(), rowData.getRight());
            } catch (Exception e) {
                log.error("write url to redis failed! key:{},url:{}", rowData.getLeft(), rowData.getRight());
            }
        }
    }

    @PreDestroy
    public void destroy() {
        executor.shutdown();
    }

}
