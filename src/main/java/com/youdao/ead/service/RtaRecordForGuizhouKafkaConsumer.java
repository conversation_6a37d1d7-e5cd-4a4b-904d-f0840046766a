package com.youdao.ead.service;

import com.youdao.ead.config.AbstractDeviceKvrocksClusterClient;
import com.youdao.ead.config.AvroDecoders;
import com.youdao.ead.config.ParamFilledValidator;
import com.youdao.ead.utils.ActiveProfiles;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:59
 **/
@Service
@Slf4j
@Profile(ActiveProfiles.PROFILE_GUIZHOU_RTA)
public class RtaRecordForGuizhouKafkaConsumer extends AbstractRtaRecordKafkaConsumer {

    private static final String PREFIX_KEY = "r_";

    protected static final int TTL_MILLIS = 1000 * 60 * 60 * 2;

    @Autowired
    public RtaRecordForGuizhouKafkaConsumer(
            AvroDecoders avroDecoders,
            ParamFilledValidator paramFilledValidator,
            AbstractDeviceKvrocksClusterClient deviceKvrocksClusterClient) {
        super(avroDecoders, paramFilledValidator, deviceKvrocksClusterClient);
    }

    @KafkaListener(topics = "${thirdPartyKafka.topic.rta}", id = "${thirdPartyKafka.groupId.rta_guizhou}")
    public void consumeRta(List<ConsumerRecord<byte[], byte[]>> records) {
        processRecords(records);
    }

    @Override
    protected String getRedisPrefixKey() {
        return PREFIX_KEY;
    }

    @Override
    protected int getRedisKeyExpireTime() {
        return TTL_MILLIS;
    }

}
