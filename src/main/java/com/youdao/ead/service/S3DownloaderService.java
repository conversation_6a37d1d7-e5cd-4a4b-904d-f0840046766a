package com.youdao.ead.service;

import com.google.common.net.InetAddresses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

import static com.youdao.ead.constant.S3ObjectNameConstants.*;

/**
 * <AUTHOR>
 * @date 2024/12/9
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class S3DownloaderService {

    private final S3Service s3Service;

    public volatile Set<Integer> darkIps = new HashSet<>();

    public volatile Map<String, Set<String>> darkDeviceIds = new HashMap<>();

    @PostConstruct
    public void init() {
        readDarkIps();
        readDarkDeviceIds();
    }

    @Scheduled(cron = "0 0 14 * * ?")
    public void readDarkIps() {
        try (InputStream inputStream = s3Service.getObject(DARK_IP_FILE_NAME);
             BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
            Set<Integer> darkIps = new HashSet<>();
            String line;
            while ((line = br.readLine()) != null) {
                int index = line.indexOf(':');
                if (index != -1) {
                    int ipv4Int = line.substring(0, index).hashCode();
                    darkIps.add(ipv4Int);
                }
            }
            this.darkIps = darkIps;
        } catch (Throwable e) {
            log.error("Error occurred while reading the darkIp data", e);
        }
    }

    @Scheduled(cron = "0 0 14 * * ?")
    public void readDarkDeviceIds() {
        try (InputStream inputStream = s3Service.getObject(DARK_DEVICE_ID_FILE_NAME);
             BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
            Map<String, Set<String>> darkDeviceIds = new HashMap<>();
            String line;
            while ((line = br.readLine()) != null) {
                String[] parts = line.split("\t");
                if (parts.length >= 2) {
                    darkDeviceIds.compute(parts[1].toLowerCase(), (key, value) -> {
                        if (value == null) {
                            return new HashSet<>(Collections.singleton(parts[0].toLowerCase()));
                        } else {
                            value.add(parts[0].toLowerCase());
                            return value;
                        }
                    });
                }
            }
            this.darkDeviceIds = darkDeviceIds;
        } catch (Throwable e) {
            log.error("Error occurred while reading the darkDeviceId data", e);
        }
    }

    public Set<String> getDarkIdsByType(String type) {
        return darkDeviceIds.getOrDefault(type, Collections.emptySet());
    }
}
