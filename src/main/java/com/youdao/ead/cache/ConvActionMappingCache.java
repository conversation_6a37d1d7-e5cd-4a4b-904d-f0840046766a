package com.youdao.ead.cache;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
@RequiredArgsConstructor
public class ConvActionMappingCache {

    private final JdbcTemplate jdbcTemplate;

    private final ObjectMapper objectMapper;

    public AtomicReference<Map<String, Map<String, String>>> defaultConvActionMappingMapRef = new AtomicReference<>();

    public AtomicReference<Map<String, Map<String, String>>> promotionConvActionMappingMapRef = new AtomicReference<>();

    public static final String PROMOTION_CONV_ACTION_MAPPING_QUERY_SQL = "select PROMOTION_ID, CONV_ACTION_MAPPING from ThirdPartPromotionExtendForSpider where OP_STATUS = 1 and CONV_ACTION_MAPPING <> ''";
    public static final String DEFAULT_CONV_ACTION_MAPPING_QUERY_SQL = "select TDP_PLATFORM, YD_CONV_ACTION, TDP_CONV_ACTION from DefaultConvActionMapping where OP_STATUS = 1 and YD_CONV_ACTION <> '' and TDP_CONV_ACTION <> ''";

    @PostConstruct
    public void init() {
        try {
            updatePromotionConvActionMappingCache();
            updateDefaultConvActionMappingCache();
        } catch (Exception e) {
            log.error("Update ConvActionMappingCache failed, caused by", e);
            throw e;
        }
    }

    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 60 * 1000)
    public void updatePromotionConvActionMappingCache() {
        log.info("Start updating promotionConvActionMappingCache");
        Map<String, Map<String, String>> promotionConvActionMappingMap = new ConcurrentHashMap<>();
        jdbcTemplate.query(PROMOTION_CONV_ACTION_MAPPING_QUERY_SQL, rs -> {
            try {
                String promotionId = rs.getString("PROMOTION_ID");
                Map<String, String> convertActionMapping = objectMapper.readValue(rs.getString("CONV_ACTION_MAPPING"), new TypeReference<ConcurrentHashMap<String, String>>() {
                });
                promotionConvActionMappingMap.put(promotionId, convertActionMapping);
            } catch (IOException e) {
                log.warn("parse conv action mapping error", e);
            }
        });
        promotionConvActionMappingMapRef.getAndSet(promotionConvActionMappingMap);
        log.info("Update promotionConvActionMappingCache finished");
    }

    @Scheduled(fixedDelay = 15 * 60 * 1000, initialDelay = 15 * 60 * 1000)
    public void updateDefaultConvActionMappingCache() {
        log.info("Start updating defaultConvActionMappingCache");
        Map<String, Map<String, String>> defaultConvActionMappingMap = new ConcurrentHashMap<>();
        jdbcTemplate.query(DEFAULT_CONV_ACTION_MAPPING_QUERY_SQL, rs -> {
            String tdpPlatform = rs.getString("TDP_PLATFORM");
            Map<String, String> mapping = defaultConvActionMappingMap.getOrDefault(tdpPlatform, new ConcurrentHashMap<>());
            mapping.put(rs.getString("YD_CONV_ACTION"), rs.getString("TDP_CONV_ACTION"));
            defaultConvActionMappingMap.put(tdpPlatform, mapping);
        });
        defaultConvActionMappingMapRef.getAndSet(defaultConvActionMappingMap);
        log.info("Updating defaultConvActionMappingCache finished");
    }

    public Map<String, Map<String, String>> getPromotionConvActionMappingCache() {
        return promotionConvActionMappingMapRef.get();
    }

    public Map<String, Map<String, String>> getDefaultConvActionMappingCache() {
        return defaultConvActionMappingMapRef.get();
    }

}
