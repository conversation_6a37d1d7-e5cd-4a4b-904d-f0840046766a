package com.youdao.ead.dispatcher;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youdao.ead.config.CentralDogmaClient;
import com.youdao.ead.dto.ConvExtDTO;
import com.youdao.ead.entity.AdContent;
import com.youdao.ead.entity.ThirdPartyPromotionInfo;
import com.youdao.ead.repository.ThirdPartyPromotionRepository;
import com.youdao.ead.service.BasicDataService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import outfox.ead.data.ActivityContextInfo;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/15.
 */
@Service
@RequiredArgsConstructor
public class ClickRtaStrategy {
    public static final int CONV_SPEED_STRATEGY = 1;
    private final CentralDogmaClient centralDogmaClient;

    private final ThirdPartyPromotionRepository thirdPartyPromotionRepository;
    private final BasicDataService basicDataService;

    @Value("${central-dogma-config-click-rta}")
    private String configFile;

    private final AtomicReference<Map<Long, ActivityStrategy>> configMapRef = new AtomicReference<>(new HashMap<>());

    private final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private final AtomicReference<Map<Pair<Long, Long>, ConvExtDTO>> convExtMapRef = new AtomicReference<>(new HashMap<>());

    protected static final Logger CONF_RELOAD_LOG = LoggerFactory.getLogger("confReloadLog");

    private final AtomicReference<ConfigData> configDataRef = new AtomicReference<>(new ConfigData());

    private final ScheduledExecutorService configReloadExecutor = new ScheduledThreadPoolExecutor(2,
            new BasicThreadFactory.Builder().namingPattern("click-rta-strategy-%d-config-schedule").daemon(true).build());

    @PostConstruct
    public void init() {
        centralDogmaClient.watchJsonConfig(configFile, (revision, conf) -> reloadConfig(conf));
        // load immediately after startup
        reloadRtaStrategy();

        // 3分钟执行一次
        configReloadExecutor.scheduleAtFixedRate(() -> {
            try {
                reloadRtaStrategy();
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("schedule load click-rta-strategy config failed!", e);
            }
        }, 30, 180, TimeUnit.SECONDS);
    }

    private void reloadConfig(JsonNode conf) {
        try {
            CONF_RELOAD_LOG.info("click rta conf:{}", conf);
            if (conf == null) {
                configDataRef.getAndSet(new ConfigData());
            } else {
                ConfigData configData = OBJECT_MAPPER.convertValue(conf, ConfigData.class);
                fixWrongConfig(configData);
                configDataRef.getAndSet(configData);
            }
            CONF_RELOAD_LOG.info("reload click rta config success!");
        } catch (Exception e) {
            CONF_RELOAD_LOG.error("update click rta conf failed:{}, exception: {}", conf, e.getMessage());
        }
    }

    private void reloadRtaStrategy() {
        List<ActivityStrategy> strategies = thirdPartyPromotionRepository.findAllRtaStrategy();
        Map<Long, ActivityStrategy> configDataMap = strategies.stream().collect(
                Collectors.toMap(ActivityStrategy::getActivityId, Function.identity(), (config1, config2) -> config2));
        configMapRef.getAndSet(configDataMap);
        reloadConvExtMap(configMapRef.get());
    }

    private void fixWrongConfig(ConfigData configData) {
        if (configData.maxDelayMillis < configData.minDelayMillis) {
            configData.maxDelayMillis = 1500;
            configData.minDelayMillis = 1000;
            CONF_RELOAD_LOG.error("maxDelayMillis must greater than minDelayMillis, fallback to default:[1000,1500]");
        }
    }

    /**
     * 配置更新后，为activityId+contentId生成新的convExt
     *
     * @param configDataMap 配置数据
     */
    private void reloadConvExtMap(Map<Long, ActivityStrategy> configDataMap) {
        Map<Pair<Long, Long>, ConvExtDTO> convExtMap = new HashMap<>();
        configDataMap.forEach((key, activityStrategy) -> {
            try {
                // 推广活动绑定新的非rta账户下的广告创意id
                if (activityStrategy.apiRtaEnabled && CollectionUtils.isNotEmpty(activityStrategy.getNonRtaAdContentIds())) {
                    ThirdPartyPromotionInfo promotionInfo = thirdPartyPromotionRepository.findThirdPartyPromotionInfo(activityStrategy.getActivityId());
                    if (promotionInfo != null) {
                        activityStrategy.getNonRtaAdContentIds().forEach(nonRtaAdContentId -> {
                            AdContent nonRtaAdContent = thirdPartyPromotionRepository.findAdContentById(nonRtaAdContentId);
                            if (nonRtaAdContent != null && nonRtaAdContent.getConvertTrackingUid() != null) {
                                // 绑定新的创意后，更新创意id，以及对应的转化跟踪id
                                promotionInfo.setAdContentId(nonRtaAdContentId);
                                promotionInfo.setConvertTrackingUID(nonRtaAdContent.getConvertTrackingUid());
                                promotionInfo.setSponsorId(nonRtaAdContent.getSponsorId());
                                ConvExtDTO convExt = thirdPartyPromotionRepository.createConvExt(promotionInfo);
                                convExtMap.put(Pair.of(activityStrategy.activityId, nonRtaAdContentId), convExt);
                            } else {
                                CONF_RELOAD_LOG.warn("invalid rta strategy, activityConfig:{}", activityStrategy);
                            }
                       });

                    } else {
                        CONF_RELOAD_LOG.error("reload click rta convExt failed, promotionInfo is null, activityConfig:{}", activityStrategy);
                    }
                }
                // 推广活动绑定新的rta账户下的广告创意id
                if (activityStrategy.apiRtaEnabled && !activityStrategy.isRtaActivity() && activityStrategy.getRtaAdContentId() > 0) {
                    ThirdPartyPromotionInfo promotionInfo = thirdPartyPromotionRepository.findThirdPartyPromotionInfo(activityStrategy.getActivityId());
                    if (promotionInfo != null) {
                        AdContent rtaAdContent = thirdPartyPromotionRepository.findAdContentById(activityStrategy.getRtaAdContentId());
                        // 绑定新的创意后，更新创意id，以及对应的转化跟踪id
                        promotionInfo.setAdContentId(activityStrategy.getRtaAdContentId());
                        promotionInfo.setConvertTrackingUID(rtaAdContent.getConvertTrackingUid());
                        promotionInfo.setSponsorId(rtaAdContent.getSponsorId());
                        ConvExtDTO convExt = thirdPartyPromotionRepository.createConvExt(promotionInfo);
                        convExtMap.put(Pair.of(activityStrategy.activityId, activityStrategy.getRtaAdContentId()), convExt);
                    } else {
                        CONF_RELOAD_LOG.error("reload click rta convExt failed, promotionInfo is null, activityConfig:{}", activityStrategy);
                    }
                }
            } catch (Exception e) {
                CONF_RELOAD_LOG.error("reload click rta convExt failed, activityConfig:{}", activityStrategy, e);
            }
        });
        CONF_RELOAD_LOG.info("reload click rta convExtMap:{}", convExtMap);
        convExtMapRef.getAndSet(convExtMap);
    }

    @Data
    public static class ConfigData {

        /**
         * 最大延迟发送时间，单位毫秒；
         */
        private long maxDelayMillis = 1500;

        /**
         * 最小延迟发送时间，单位毫秒；
         */
        private long minDelayMillis = 1000;

        /**
         * 转化加速器所有aid默认开启rta过滤
         */
        private boolean convSpeedAllEnabled = true;

        /**
         * 转化加速器开启rta过滤的aid列表
         */
        private List<Long> convSpeedEnabledAids = new ArrayList<>();

    }

    @Data
    public static class ActivityStrategy {
        /**
         * 推广活动id
         */
        private long activityId;
        /**
         * 是否为rta推广活动，默认true
         */
        private boolean rtaActivity = true;
        /**
         * 非rta 广告创意id
         */
        private List<Long> nonRtaAdContentIds;
        /**
         * rta 广告创意id
         */
        private Long rtaAdContentId;
        /**
         * 推广活动上是否开启了api-rta策略，默认关闭
         */
        private boolean apiRtaEnabled = false;

    }

    private static final ActivityStrategy DEFAULT_CONFIG_DATA = new ActivityStrategy();

    public ActivityStrategy getActivityStrategy(long activityId) {
        Map<Long, ActivityStrategy> configDataMap = configMapRef.get();
        if (MapUtils.isNotEmpty(configDataMap) && configDataMap.containsKey(activityId)) {
            return configDataMap.get(activityId);
        }
        return DEFAULT_CONFIG_DATA;
    }

    /**
     * 判断aid是否为启用rta过滤的转化加速器推广活动
     *
     * @param activityId
     * @return
     */
    public boolean isConvSpeedAndRtaEnabled(Long activityId) {
        ActivityContextInfo activityContextInfo = basicDataService.getActivityContextInfo(activityId);
        Integer strategy = activityContextInfo.getStrategy();
        if (strategy == CONV_SPEED_STRATEGY) {
            return configDataRef.get().isConvSpeedAllEnabled() || configDataRef.get().convSpeedEnabledAids.contains(activityId);
        } else {
            return false;
        }
    }


    /**
     * 获取新策略下开启的rta推广活动id
     *
     * @return
     */
    public Map<Long, Long> getExtraRtaActivityMap() {
        Map<Long, ActivityStrategy> configDataMap = configMapRef.get();
        Map<Long, Long> activity2RtaAdContentIdMap = new HashMap<>();
        configDataMap.forEach((activityId, activityStrategy) -> {
            if (!activityStrategy.isRtaActivity() && activityStrategy.getRtaAdContentId() > 0) {
                activity2RtaAdContentIdMap.put(activityId, activityStrategy.getRtaAdContentId());
            }
        });
        return activity2RtaAdContentIdMap;
    }

    /**
     * 获取延迟发送时间，单位毫秒
     *
     * @return
     */
    public long getDelayMillis() {
        return ThreadLocalRandom.current().nextLong(configDataRef.get().getMinDelayMillis(), configDataRef.get().getMaxDelayMillis());
    }

    /**
     * 获取重构后的广告创意id的conv_ext
     *
     * @param activityId
     * @param adContentId
     * @return
     */
    public ConvExtDTO getConvExt(long activityId, long adContentId) {
        return convExtMapRef.get().get(Pair.of(activityId, adContentId));
    }

    public long getRandomAdContentId(List<Long> adContentIds){
        if (CollectionUtils.isNotEmpty(adContentIds)) {
            return adContentIds.get(ThreadLocalRandom.current().nextInt(adContentIds.size()));
        } else {
            return 0L;
        }
    }

}
