package com.youdao.ead.dao;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ConvertTrackingInfoDao {
    private final JdbcTemplate jdbcTemplate;

    public static final String SET_DEBUG_CHECK_FLAG_SQL = "UPDATE ConvertTrackingInfo SET DEBUG_CHECK_FLAG = 1 WHERE CONVERT_TRACKING_UID=?";


    public void setDebugCheckFlag(String convertTrackingUid) {
        jdbcTemplate.update(SET_DEBUG_CHECK_FLAG_SQL, convertTrackingUid);
        log.info("{}'s debug check flag was set = 1", convertTrackingUid);
    }
}
