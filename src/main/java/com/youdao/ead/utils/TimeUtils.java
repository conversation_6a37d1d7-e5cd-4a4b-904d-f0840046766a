package com.youdao.ead.utils;

import java.time.Instant;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @create 2024-12-17 15:51
 */
public class TimeUtils {

    private static final String CHINESE_ZEITZONE = "Asia/Shanghai";

    /**
     * 从时间戳获取天数（从1970-01-01开始）- 时区为东八区
     * <p>
     * 因为 System.currentTimeMillis() 返回的是 UTC（协调世界时）时间的毫秒数，从 1970 年 1 月 1 日 00:00:00 UTC 开始计算。它返回的是一个绝对的时间戳，与时区无关
     * 但是我们当前属于哪一天，是基于东八区的时间来算的
     */
    public static long getDayFromTimestamp(long timestamp) {
        return Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.of(CHINESE_ZEITZONE))
                .toLocalDate()
                .toEpochDay();
    }
}