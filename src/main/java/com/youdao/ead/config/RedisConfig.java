package com.youdao.ead.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.RedisURI;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import io.lettuce.core.codec.RedisCodec;
import io.lettuce.core.codec.StringCodec;
import io.lettuce.core.resource.DefaultClientResources;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/9/19.
 */
@Slf4j
@Component
public class RedisConfig {

    @Value("${redis.uri}")
    private String redisUri;
    @Value("${redis.timeout}")
    private long timeout;

    private DefaultClientResources sharedClientResourcesBuilder;
    private RedisClusterClient redisClusterClient;
    private StatefulRedisClusterConnection<String, String> statefulRedisClusterConnection;
    @Getter
    private RedisAdvancedClusterCommands<String, String> syncCommands;

    private static final ClusterClientOptions.Builder clusterClientOptionsBuilder;

    private static final ClusterTopologyRefreshOptions.Builder clusterTopologyRefreshOptionsBuilder
            = ClusterTopologyRefreshOptions
            .builder()
            .enablePeriodicRefresh(Duration.ofHours(1L))
            .enableAllAdaptiveRefreshTriggers();

    static {
        clusterClientOptionsBuilder = ClusterClientOptions.builder()
                .requestQueueSize(Integer.MAX_VALUE)
                .maxRedirects(1)
                .disconnectedBehavior(ClientOptions.DisconnectedBehavior.REJECT_COMMANDS)
                .topologyRefreshOptions(clusterTopologyRefreshOptionsBuilder.build());
    }

    @PostConstruct
    public void init() throws Exception {
        initRedisClusterClient();
    }

    private void initRedisClusterClient() {
        RedisURI redisClusterUri = RedisURI.create(redisUri);
        this.sharedClientResourcesBuilder = DefaultClientResources.builder().build();
        this.redisClusterClient = RedisClusterClient.create(this.sharedClientResourcesBuilder, Collections.singletonList(redisClusterUri));
        this.redisClusterClient.setOptions(clusterClientOptionsBuilder.build());
        this.statefulRedisClusterConnection = this.redisClusterClient.connect(RedisCodec.of(new StringCodec(), new StringCodec()));
        this.statefulRedisClusterConnection.setReadFrom(ReadFrom.MASTER);
        this.syncCommands = this.statefulRedisClusterConnection.sync();
        this.syncCommands.setTimeout(Duration.ofMillis(timeout));
        log.info("redis client init success, using: {}", redisUri);
    }


    @PreDestroy
    public void shutdown() {
        if (Objects.nonNull(this.statefulRedisClusterConnection) && this.statefulRedisClusterConnection.isOpen()) {
            this.statefulRedisClusterConnection.close();
        }
        if (Objects.nonNull(this.redisClusterClient)) {
            this.redisClusterClient.shutdown();
        }
        if (Objects.nonNull(this.sharedClientResourcesBuilder)) {
            this.sharedClientResourcesBuilder.shutdown();
        }
        log.info("redis client was shutdown.");
    }


}
