package com.youdao.ead.config;

import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.SharedMetricRegistries;
import com.codahale.metrics.Timer;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2024-12-11 10:13
 */
public class MetricUtils {
    public static final String THIRD_PARTY_DATA_POOL_METRIC_REGISTRY = "third-party-data-pool-metric-registry";
    private static final MetricRegistry REGISTRY = SharedMetricRegistries.getOrCreate(THIRD_PARTY_DATA_POOL_METRIC_REGISTRY);
    private static final Map<String, Timer> processThirdPartyTimerMap = new ConcurrentHashMap<>();
    private static final Map<String, Timer> realProcessThirdPartyTimerMap = new ConcurrentHashMap<>();
    private static final Map<String, Meter> consumerExceptionMeterMap = new ConcurrentHashMap<>();
    private static final Map<String, Meter> processThirdPartyErrorMeterMap = new ConcurrentHashMap<>();
    private static final Map<String, Meter> noDeviceIdMeterMap = new ConcurrentHashMap<>();
    private static final Map<String, Meter> invalidProtoBufMeterMap = new ConcurrentHashMap<>();
    private static final Map<String, Meter> kvrocksExceptionMeterMap = new ConcurrentHashMap<>();
    private static final Map<String, Meter> kvrocksTimeoutExceptionMeterMap = new ConcurrentHashMap<>();
    private static final Map<String, Meter> discardMeterMap = new ConcurrentHashMap<>();

    public static Timer getProcessThirdPartyTimer(Class<?> kclass) {
        return processThirdPartyTimerMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.timer(name(kclass, "processThirdPartyTimer")));
    }

    public static Timer getRealProcessThirdPartyTimer(Class<?> kclass) {
        return realProcessThirdPartyTimerMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.timer(name(kclass, "realProcessThirdPartyTimer")));
    }

    public static Meter getConsumerExceptionMeter(Class<?> kclass) {
        return consumerExceptionMeterMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.meter(name(kclass, "consumerExceptionMeter")));
    }

    public static Meter getProcessThirdPartyErrorMeter(Class<?> kclass) {
        return processThirdPartyErrorMeterMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.meter(name(kclass, "processThirdPartyErrorMeter")));
    }

    public static Meter getNoDeviceIdMeter(Class<?> kclass) {
        return noDeviceIdMeterMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.meter(name(kclass, "noDeviceIdMeter")));
    }

    public static Meter getInvalidProtoBufMeter(Class<?> kclass) {
        return invalidProtoBufMeterMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.meter(name(kclass, "invalidProtoBufMeter")));
    }

    public static Meter getKvrocksExceptionMeter(Class<?> kclass) {
        return kvrocksExceptionMeterMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.meter(name(kclass, "kvrocksExceptionMeter")));
    }

    public static Meter getKvrocksTimeoutExceptionMeter(Class<?> kclass) {
        return kvrocksTimeoutExceptionMeterMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.meter(name(kclass, "kvrocksTimeoutExceptionMeter")));
    }

    public static Meter getDiscardMeter(Class<?> kclass) {
        return discardMeterMap.computeIfAbsent(kclass.getSimpleName(), x -> REGISTRY.meter(name(kclass, "discardMeter")));
    }

    public static String name(@NotNull Class<?> kclass, @NotNull String name) {
        return String.format("%s.%s", kclass.getSimpleName(), name);
    }
}