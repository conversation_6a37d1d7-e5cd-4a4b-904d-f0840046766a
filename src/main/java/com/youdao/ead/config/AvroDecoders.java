package com.youdao.ead.config;

import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageDecoder;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageEncoder;
import com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;

import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2024-12-07 16:55
 */
@EnableKafka
@Configuration
public class AvroDecoders {

    @Value("${thirdPartyKafka.schema-repository}")
    private String schemaRepository;

    private final Map<String, KafkaAvroMessageDecoder> decoderMap = new ConcurrentHashMap<>();


    public KafkaAvroMessageDecoder getDecoder(String topic) {
        Properties properties = new Properties();
        properties.put(KafkaAvroMessageEncoder.KAFKA_MESSAGE_CODER_SCHEMA_REGISTRY_CLASS, AvroRestSchemaRegistry
                .class.getCanonicalName());
        properties.put(AvroRestSchemaRegistry.ETL_SCHEMA_REGISTRY_URL, schemaRepository);
        return decoderMap.computeIfAbsent(topic, k -> {
            KafkaAvroMessageDecoder decoder = new KafkaAvroMessageDecoder();
            decoder.init(properties, topic);
            return decoder;
        });
    }

}