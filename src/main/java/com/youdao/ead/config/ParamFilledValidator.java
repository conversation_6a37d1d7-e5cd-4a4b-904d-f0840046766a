package com.youdao.ead.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 无效设备号校验
 *
 * <AUTHOR>
 * @date 2024/8/14
 */
@Service
@RequiredArgsConstructor
public class ParamFilledValidator {

    private final CentralDogmaClient centralDogmaClient;

    @Value("${central_dogma.cd_project}")
    private String cdProject;

    @Value("${central_dogma.cd_repo}")
    private String cdRepo;

    @Value("${central_dogma.cd_conf}")
    private String cdConfFile;

    private final AtomicReference<Map<String, List<String>>> invalidParamValuesRef = new AtomicReference<>(new HashMap<>());

    private final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    protected static final Logger CONF_RELOAD_LOG = LoggerFactory.getLogger("confReloadLog");

    public static final String PARAM_OAID = "oaid";

    public static final String PARAM_OAID_MD5 = "oaid_md5";

    public static final String PARAM_IDFA = "idfa";

    public static final String PARAM_IDFA_MD5 = "idfa_md5";

    public static final String PARAM_IMEI_MD5 = "imei_md5";

    public static final String PARAM_CAID = "caid";

    public static final String PARAM_CAID_MD5 = "caid_md5";

    public static final String PARAM_ANDROID_ID = "android_id";

    public static final String PARAM_ANDROID_ID_MD5 = "android_id_md5";

    public static final String PARAM_IP = "ip";

    public static final String PARAM_UA = "ua";

    public static final String PARAM_OS = "os";

    public static final String PARAM_OS_VERSION = "os_version";

    public static final String PARAM_MODEL = "model";

    public static final String PARAM_TS = "ts";

    public static final String PARAM_REQ_ID = "req_id";

    public static final String PARAM_ALID = "alid";

    @PostConstruct
    public void init() {
        centralDogmaClient.watchJsonConfig(cdProject, cdRepo, cdConfFile, (revision, conf) -> reload(conf));
    }

    private void reload(JsonNode conf) {
        try {
            CONF_RELOAD_LOG.info("reload invalidParamValues conf:{}", conf);
            if (conf == null) {
                invalidParamValuesRef.getAndSet(new HashMap<>());
            } else {
                Map<String, List<String>> invalidParamValues = OBJECT_MAPPER.convertValue(conf, new TypeReference<>() {
                });
                invalidParamValuesRef.getAndSet(invalidParamValues);
            }
            CONF_RELOAD_LOG.info("reload invalidParamValues config success!");
        } catch (Exception e) {
            CONF_RELOAD_LOG.info("reload invalidParamValues config failed! conf:{}.", conf, e);
        }
    }

    /**
     * 判断点击参数是否填充
     *
     * @param paramName  参数名称
     * @param paramValue 参数值
     * @return
     */
    public boolean checkParamFilled(String paramName, String paramValue) {
        Map<String, List<String>> invalidParamValues = invalidParamValuesRef.get();
        List<String> invalidValues = invalidParamValues.containsKey(paramName) ? invalidParamValues.get(paramName) : invalidParamValues.getOrDefault("default", Collections.emptyList());
        return StringUtils.isNotBlank(paramValue) && !invalidValues.contains(paramValue);
    }

}
