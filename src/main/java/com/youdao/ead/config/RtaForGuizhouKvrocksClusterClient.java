package com.youdao.ead.config;

import com.youdao.ead.utils.ActiveProfiles;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.RedisURI;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import io.lettuce.core.codec.ByteArrayCodec;
import io.lettuce.core.codec.RedisCodec;
import io.lettuce.core.codec.StringCodec;
import io.lettuce.core.resource.DefaultClientResources;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Profile(ActiveProfiles.PROFILE_GUIZHOU_RTA)
public class RtaForGuizhouKvrocksClusterClient implements AbstractDeviceKvrocksClusterClient {
    @Value("${deviceKvrocks.guizhou-rta.uri}")
    private String redisUri;
    @Value("${deviceKvrocks.guizhou-rta.timeout}")
    private long timeout;

    //设置小一点的数，是为了让数据在kafka中堆积，不在jvm内存中堆积（需要多一次redis的序列化），提高性能
    //写redis是很快的，理论上不会有数据的堆积
    private static final Integer REQUEST_QUEUE_SIZE = 1000;

    private DefaultClientResources sharedClientResources;
    private RedisClusterClient redisClusterClient;
    private StatefulRedisClusterConnection<String, byte[]> statefulRedisClusterConnection;
    @Getter
    private RedisAdvancedClusterCommands<String, byte[]> syncCommands;


    private static final ClusterTopologyRefreshOptions.Builder clusterTopologyRefreshOptionsBuilder
            = ClusterTopologyRefreshOptions
            .builder()
            .enablePeriodicRefresh(Duration.ofHours(1L))
            .enableAllAdaptiveRefreshTriggers();

    private static final ClusterClientOptions.Builder clusterClientOptionsBuilder = ClusterClientOptions.builder()
            .requestQueueSize(REQUEST_QUEUE_SIZE)
            .maxRedirects(1)
            .disconnectedBehavior(ClientOptions.DisconnectedBehavior.REJECT_COMMANDS)
            .topologyRefreshOptions(clusterTopologyRefreshOptionsBuilder.build());

    @PostConstruct
    public void init() throws Exception {
        initRedisClusterClient();
    }

    private void initRedisClusterClient() {
        List<RedisURI> clusterNodes = Arrays.stream(redisUri.split(","))
                .map(String::trim)
                .map(RedisURI::create)
                .collect(Collectors.toList());
        this.sharedClientResources = DefaultClientResources.builder().build();
        this.redisClusterClient = RedisClusterClient.create(this.sharedClientResources, clusterNodes);
        this.redisClusterClient.setOptions(clusterClientOptionsBuilder.build());
        this.statefulRedisClusterConnection = this.redisClusterClient.connect(RedisCodec.of(new StringCodec(), ByteArrayCodec.INSTANCE));
        this.statefulRedisClusterConnection.setReadFrom(ReadFrom.MASTER);
        this.syncCommands = this.statefulRedisClusterConnection.sync();
        this.syncCommands.setTimeout(Duration.ofMillis(timeout));
        log.info("redis client init success, using: {}", redisUri);
    }

    @PreDestroy
    public void shutdown() {
        if (Objects.nonNull(this.statefulRedisClusterConnection) && this.statefulRedisClusterConnection.isOpen()) {
            this.statefulRedisClusterConnection.close();
        }
        if (Objects.nonNull(this.redisClusterClient)) {
            this.redisClusterClient.shutdown();
        }
        if (Objects.nonNull(this.sharedClientResources)) {
            this.sharedClientResources.shutdown();
        }
        log.info("redis client was shutdown.");
    }


}
