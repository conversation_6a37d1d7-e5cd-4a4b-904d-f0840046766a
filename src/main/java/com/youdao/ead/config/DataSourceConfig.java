package com.youdao.ead.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2024/7/11
 */
@Configuration
public class DataSourceConfig {

    @Primary
    @Bean("eadb1DataSourceProperties")
    @ConfigurationProperties("spring.datasource.eadb1")
    public DataSourceProperties eadb1DataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean("channelDataSourceProperties")
    @ConfigurationProperties("spring.datasource.channel")
    public DataSourceProperties channelDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean("eadb1DataSource")
    public DataSource eadb1DataSource(@Qualifier("eadb1DataSourceProperties") DataSourceProperties eadb1DataSourceProperties) {
        return eadb1DataSourceProperties.initializeDataSourceBuilder().build();
    }

    @Bean("channelDataSource")
    public DataSource channelDataSource(@Qualifier("channelDataSourceProperties") DataSourceProperties channelDataSourceProperties) {
        return channelDataSourceProperties.initializeDataSourceBuilder().build();
    }

    @Primary
    @Bean("eadb1JdbcTemplate")
    public NamedParameterJdbcTemplate eadb1JdbcTemplate(@Qualifier("eadb1DataSource") DataSource eadb1DataSource) {
        return new NamedParameterJdbcTemplate(eadb1DataSource);
    }

    @Bean("channelJdbcTemplate")
    public NamedParameterJdbcTemplate channelJdbcTemplate(@Qualifier("channelDataSource") DataSource channelDataSource) {
        return new NamedParameterJdbcTemplate(channelDataSource);
    }


}
