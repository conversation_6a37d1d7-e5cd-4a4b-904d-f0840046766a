package com.youdao.ead.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.linecorp.centraldogma.client.CentralDogma;
import com.linecorp.centraldogma.client.Watcher;
import com.linecorp.centraldogma.client.armeria.legacy.LegacyCentralDogmaBuilder;
import com.linecorp.centraldogma.common.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("centralDogmaClient")
public class CentralDogmaClient {

    @Value("#{'${central-dogma-hosts}'.split(',')}")
    private List<String> centralDogmaHosts;

    @Value("${central-dogma-port}")
    private int centralDogmaPort = -1;

    @Value("${central-dogma-proj}")
    private String configProj;

    @Value("${central-dogma-repo}")
    private String configRepo;

    private CentralDogma dogmaClient;

    private static final long INITIAL_VALUE_WAITING_TIME_SECONDS = 30;

    /**
     * 初始化centraldogma
     */
    @PostConstruct
    public void initDogma() {
        try {
            LegacyCentralDogmaBuilder dogmaBuilder = new LegacyCentralDogmaBuilder();
            for (String host : centralDogmaHosts) {
                dogmaBuilder.host(host, centralDogmaPort);
                log.info("registered centraldogma host {" + host + ":" + centralDogmaPort + "}");
            }
            dogmaClient = dogmaBuilder.build();
        } catch (Exception e) {
            log.error("error initialize centraldogma client!!", e);
            System.exit(1);
        }
    }

    /**
     * 监听文件变化并执行回调函数
     *
     * @param fileName 配置的文件名
     * @param call     配置更新时执行的回调函数
     */
    public void watchConfig(String fileName, BiConsumer<Revision, String> call) {
        try {
            if (dogmaClient != null) {
                Watcher<String> watcher = dogmaClient.fileWatcher(configProj, configRepo, Query.ofText(fileName));
                watcher.watch(call);
                watcher.awaitInitialValue(INITIAL_VALUE_WAITING_TIME_SECONDS, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("central dogma error register file watcher!", e);
        }
    }


    /**
     * 监听文件变化并执行回调函数
     *
     * @param fileName    配置的文件名
     * @param call        配置更新时执行的回调函数
     */
    public void watchJsonConfig(String fileName, BiConsumer<Revision, JsonNode> call) {
        try {
            Watcher<JsonNode> watcher = dogmaClient.fileWatcher(configProj, configRepo, Query.ofJson(fileName));
            watcher.watch(call);
            watcher.awaitInitialValue(INITIAL_VALUE_WAITING_TIME_SECONDS, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("centraldogma error register file watcher!", e);
        }
    }

    /**
     * 监听指定仓库下的配置变化，变化时执行回调函数
     *
     * @param projectName
     * @param repositoryName
     * @param call 需要注意回调函数是在watcher中同步执行，可能阻塞eventLoop的线程，导致其他配置变更无法同步，dogma后续针对这里做了更新，见：https://github.com/line/centraldogma/releases/tag/centraldogma-0.50.0
     */
    public void watchRepository(String projectName, String repositoryName, Consumer<Revision> call) {
        try {
            if (dogmaClient != null) {
                log.info("watchRepository from proj:{}, repo:{} start", configProj, configRepo);
                Watcher<Revision> watcher = dogmaClient.repositoryWatcher(projectName, repositoryName, "/*");
                watcher.watch(call);
                watcher.awaitInitialValue(INITIAL_VALUE_WAITING_TIME_SECONDS, TimeUnit.SECONDS);
                log.info("watchRepository from proj:{}, repo:{} success", configProj, configRepo);
            }
        } catch (Exception e) {
            log.error("central dogma register repository watcher failed. project: {}, repository: {}", projectName, repositoryName, e);
        }
    }

    /**
     * 获取指定版本对应仓库下的配置
     *
     * @param configProj
     * @param configRepo
     * @param revision
     * @param call
     */
    public void getConfigs(String configProj, String configRepo, Revision revision, Consumer<Map<String, Entry<?>>> call) {
        CompletableFuture<Map<String, Entry<?>>> future = dogmaClient.getFiles(configProj, configRepo, revision, "/*");
        Map<String, Entry<?>> entryMap = future.join();
        call.accept(entryMap);
    }

    /**
     * 有限时间内等待初始值，并监听文件变化，变化时执行回调函数。
     * <p>
     * 服务启动时，等待初始值最多{@link #INITIAL_VALUE_WAITING_TIME_SECONDS}，超时可能是服务挂了或者节点不存在，
     *
     * @param projectName 配置所在项目
     * @param repoName    配置所在的仓库
     * @param fileName    配置的文件名
     * @param call        配置更新时执行的回调函数
     */
    public void watchConfigFile(String projectName, String repoName,
                                String fileName, BiConsumer<Revision, String> call) {
        try {
            Watcher<String> watcher = dogmaClient.fileWatcher(projectName, repoName, Query.ofText(fileName));
            watcher.watch(call);
            watcher.awaitInitialValue(INITIAL_VALUE_WAITING_TIME_SECONDS, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("central dogma error register file watcher!", e);
        }
    }
}
