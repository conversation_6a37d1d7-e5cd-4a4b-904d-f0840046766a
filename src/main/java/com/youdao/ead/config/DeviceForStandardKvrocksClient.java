package com.youdao.ead.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.RedisURI;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import io.lettuce.core.codec.ByteArrayCodec;
import io.lettuce.core.codec.RedisCodec;
import io.lettuce.core.codec.StringCodec;
import io.lettuce.core.resource.DefaultClientResources;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-12-10 16:21
 */
@Slf4j
@Component
public class DeviceForStandardKvrocksClient extends DeviceForStandardAbstractClient{

    @Value("${device-standard-info.kvrocks.uri}")
    private String redisUri;
    @Value("${device-standard-info.kvrocks.timeout}")
    private long timeout;

    private DefaultClientResources sharedClientResources;
    private io.lettuce.core.cluster.RedisClusterClient redisClusterClient;
    private StatefulRedisClusterConnection<String, byte[]> statefulRedisClusterConnection;
    @Getter
    private RedisAdvancedClusterCommands<String, byte[]> syncCommands;


    private static final ClusterTopologyRefreshOptions.Builder clusterTopologyRefreshOptionsBuilder
            = ClusterTopologyRefreshOptions
            .builder()
            .enablePeriodicRefresh(Duration.ofHours(1L))
            .enableAllAdaptiveRefreshTriggers();

    private static final ClusterClientOptions.Builder clusterClientOptionsBuilder = ClusterClientOptions.builder()
            .requestQueueSize(Integer.MAX_VALUE)
            .maxRedirects(1)
            .disconnectedBehavior(ClientOptions.DisconnectedBehavior.REJECT_COMMANDS)
            .topologyRefreshOptions(clusterTopologyRefreshOptionsBuilder.build());

    @PostConstruct
    public void init() throws Exception {
        initRedisClusterClient();
    }

    private void initRedisClusterClient() {
        List<RedisURI> clusterNodes = Arrays.stream(redisUri.split(","))
                .map(String::trim)
                .map(RedisURI::create)
                .collect(Collectors.toList());
        this.sharedClientResources = DefaultClientResources.builder().build();
        this.redisClusterClient = io.lettuce.core.cluster.RedisClusterClient.create(this.sharedClientResources, clusterNodes);
        this.redisClusterClient.setOptions(clusterClientOptionsBuilder.build());
        this.statefulRedisClusterConnection = this.redisClusterClient.connect(RedisCodec.of(new StringCodec(), ByteArrayCodec.INSTANCE));
        this.statefulRedisClusterConnection.setReadFrom(ReadFrom.MASTER);
        this.syncCommands = this.statefulRedisClusterConnection.sync();
        this.syncCommands.setTimeout(Duration.ofMillis(timeout));
        log.info("redis client init success, using: {}", redisUri);
    }


    @PreDestroy
    public void shutdown() {
        if (Objects.nonNull(this.statefulRedisClusterConnection) && this.statefulRedisClusterConnection.isOpen()) {
            this.statefulRedisClusterConnection.close();
        }
        if (Objects.nonNull(this.redisClusterClient)) {
            this.redisClusterClient.shutdown();
        }
        if (Objects.nonNull(this.sharedClientResources)) {
            this.sharedClientResources.shutdown();
        }
        log.info("redis client was shutdown.");
    }


}
