package com.youdao.ead.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.ScriptOutputType;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import io.lettuce.core.cluster.models.partitions.RedisClusterNode;
import io.lettuce.core.codec.ByteArrayCodec;
import io.lettuce.core.codec.RedisCodec;
import io.lettuce.core.codec.StringCodec;
import io.lettuce.core.event.connection.ConnectionActivatedEvent;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AntiFraudRedisClient {

    @Value("${anti-fraud.redis.uri}")
    private String redisUri;
    @Value("${anti-fraud.redis.timeout}")
    private long redisTimeout;

    @Value("${anti-fraud.repeat-click.expire.sec}")
    private long repeatClickExpireSec;
    @Value("${anti-fraud.diff-aid-click.expire.sec}")
    private long diffAidClickExpireSec;
    @Value("${anti-fraud.diff-click.expire.sec}")
    private long diffClickExpireSec;
    @Value("${anti-fraud.ip-rate-click.expire.sec}")
    private long ipRateClickExpireSec;

    private ClientResources clientResources;
    private RedisClusterClient redisClusterClient;
    private StatefulRedisClusterConnection<String, byte[]> statefulRedisClusterConnection;
    @Getter
    private RedisAdvancedClusterCommands<String, byte[]> syncCommands;
    private Disposable subscribe;


    private byte[] luaScriptBytesRepeatClick;
    private String luaScriptShaRepeatClick;
    private byte[] luaScriptBytesDiffAidClick;
    private String luaScriptShaDiffAidClick;
    private byte[] luaScriptBytesDiffClick;
    private String luaScriptShaDiffClick;
    private byte[] luaScriptBytesIpRateClick;
    private String luaScriptShaIpRateClick;

    @PostConstruct
    public void init() throws IOException {
        initLuaScriptBytes();
        initRedisClusterClient();
        initLuaScriptSha();
    }

    private void initLuaScriptBytes() throws IOException {
        try {
            luaScriptBytesRepeatClick = IOUtils.toByteArray(Objects.requireNonNull(this.getClass().getResourceAsStream("/lua/repeat-click.lua")));
        } catch (Exception e) {
            log.error("read repeat-click lua script failed", e);
            throw e;
        }
        try {
            luaScriptBytesDiffAidClick = IOUtils.toByteArray(Objects.requireNonNull(this.getClass().getResourceAsStream("/lua/diff-aid-click.lua")));
        } catch (Exception e) {
            log.error("read diff-aid-click lua script failed", e);
            throw e;
        }
        try {
            luaScriptBytesDiffClick = IOUtils.toByteArray(Objects.requireNonNull(this.getClass().getResourceAsStream("/lua/diff-click.lua")));
        } catch (Exception e) {
            log.error("read diff-click lua script failed", e);
            throw e;
        }
        try {
            luaScriptBytesIpRateClick = IOUtils.toByteArray(Objects.requireNonNull(this.getClass().getResourceAsStream("/lua/ip-rate-click.lua")));
        } catch (Exception e) {
            log.error("read ip-rate-click lua script failed", e);
            throw e;
        }
    }

    private void initRedisClusterClient() {
        clientResources = DefaultClientResources.builder().build();
        ClusterTopologyRefreshOptions clusterTopologyRefreshOptions = ClusterTopologyRefreshOptions
                .builder()
                .enablePeriodicRefresh(Duration.ofHours(1L))
                .enableAllAdaptiveRefreshTriggers()
                .build();
        ClusterClientOptions clusterClientOptions = ClusterClientOptions.builder()
                .requestQueueSize(Integer.MAX_VALUE)
                .maxRedirects(1)
                .disconnectedBehavior(ClientOptions.DisconnectedBehavior.REJECT_COMMANDS)
                .topologyRefreshOptions(clusterTopologyRefreshOptions)
                .build();
        redisClusterClient = RedisClusterClient.create(clientResources, redisUri);
        redisClusterClient.setOptions(clusterClientOptions);
        statefulRedisClusterConnection = redisClusterClient.connect(RedisCodec.of(new StringCodec(), new ByteArrayCodec()));
        statefulRedisClusterConnection.setReadFrom(ReadFrom.MASTER);
        syncCommands = statefulRedisClusterConnection.sync();
        syncCommands.setTimeout(Duration.ofMillis(redisTimeout));
        subscribe = registerLuaScriptUpdaterWhenConnectionActivated();
        log.info("init anti-fraud redis client success");
    }

    private void initLuaScriptSha() {
        for (RedisClusterNode node : statefulRedisClusterConnection.getPartitions()) {
            try {
                luaScriptShaRepeatClick = statefulRedisClusterConnection.getConnection(node.getNodeId()).sync().scriptLoad(luaScriptBytesRepeatClick);
            } catch (Exception e) {
                log.warn("load repeat-click lua script to {}:{} failed!", node.getUri().getHost(), node.getUri().getPort(), e);
                throw e;
            }
            try {
                luaScriptShaDiffAidClick = statefulRedisClusterConnection.getConnection(node.getNodeId()).sync().scriptLoad(luaScriptBytesDiffAidClick);
            } catch (Exception e) {
                log.warn("load diff-aid-click lua script to {}:{} failed!", node.getUri().getHost(), node.getUri().getPort(), e);
                throw e;
            }
            try {
                luaScriptShaDiffClick = statefulRedisClusterConnection.getConnection(node.getNodeId()).sync().scriptLoad(luaScriptBytesDiffClick);
            } catch (Exception e) {
                log.warn("load diff-click lua script to {}:{} failed!", node.getUri().getHost(), node.getUri().getPort(), e);
                throw e;
            }
            try {
                luaScriptShaIpRateClick = statefulRedisClusterConnection.getConnection(node.getNodeId()).sync().scriptLoad(luaScriptBytesIpRateClick);
            } catch (Exception e) {
                log.warn("load ip-rate-click lua script to {}:{} failed!", node.getUri().getHost(), node.getUri().getPort(), e);
                throw e;
            }
        }
        log.info("init repeat-click luaScripSha to: {}", luaScriptShaRepeatClick);
        log.info("init diff-aid-click luaScripSha to: {}", luaScriptShaDiffAidClick);
        log.info("init diff-click luaScripSha to: {}", luaScriptShaDiffClick);
        log.info("init ip-rate-click luaScripSha to: {}", luaScriptShaIpRateClick);
    }

    /**
     * 当集群中有新的节点连接成功时，将推送lua script到其上，并更新当前的lua script sha。
     */
    private Disposable registerLuaScriptUpdaterWhenConnectionActivated() {
        return redisClusterClient.getResources().eventBus().get()
                .filter(ConnectionActivatedEvent.class::isInstance)
                .cast(ConnectionActivatedEvent.class)
                .map(it -> it.remoteAddress())
                .filter(InetSocketAddress.class::isInstance)
                .cast(InetSocketAddress.class)
                .doOnNext(remote -> {
                    final Optional<RedisClusterNode> redisClusterNode = redisClusterClient.getPartitions().stream()
                            .filter(node -> {
                                String host = node.getUri().getHost();
                                int port = node.getUri().getPort();
                                return remote.getPort() == port
                                        && (remote.getAddress().getHostAddress().equals(host) || remote.getHostName().equals(host));
                            }).findFirst();

                    if (redisClusterNode.isPresent()) {
                        RedisClusterNode rcn = redisClusterNode.get();
                        try {
                            String newLuaScriptSha = statefulRedisClusterConnection.getConnection(rcn.getNodeId()).sync().scriptLoad(luaScriptBytesRepeatClick);
                            if (StringUtils.isNotBlank(newLuaScriptSha)) {
                                luaScriptShaRepeatClick = newLuaScriptSha;
                                log.info("load repeat-click lua script to {}:{} and get sha {}", rcn.getUri().getHost(), rcn.getUri().getPort(), luaScriptShaRepeatClick);
                            }
                        } catch (Exception e) {
                            log.error("upload repeat-click lua script to {}:{} failed", rcn.getUri().getHost(), rcn.getUri().getPort());
                        }
                        try {
                            String newLuaScriptSha = statefulRedisClusterConnection.getConnection(rcn.getNodeId()).sync().scriptLoad(luaScriptBytesDiffAidClick);
                            if (StringUtils.isNotBlank(newLuaScriptSha)) {
                                luaScriptShaDiffAidClick = newLuaScriptSha;
                                log.info("load diff-aid-click lua script to {}:{} and get sha {}", rcn.getUri().getHost(), rcn.getUri().getPort(), luaScriptShaDiffAidClick);
                            }
                        } catch (Exception e) {
                            log.error("upload diff-aid-click lua script to {}:{} failed", rcn.getUri().getHost(), rcn.getUri().getPort());
                        }
                        try {
                            String newLuaScriptSha = statefulRedisClusterConnection.getConnection(rcn.getNodeId()).sync().scriptLoad(luaScriptBytesDiffClick);
                            if (StringUtils.isNotBlank(newLuaScriptSha)) {
                                luaScriptShaDiffClick = newLuaScriptSha;
                                log.info("load diff-click lua script to {}:{} and get sha {}", rcn.getUri().getHost(), rcn.getUri().getPort(), luaScriptShaDiffClick);
                            }
                        } catch (Exception e) {
                            log.error("upload diff-click lua script to {}:{} failed", rcn.getUri().getHost(), rcn.getUri().getPort());
                        }
                        try {
                            String newLuaScriptSha = statefulRedisClusterConnection.getConnection(rcn.getNodeId()).sync().scriptLoad(luaScriptBytesIpRateClick);
                            if (StringUtils.isNotBlank(newLuaScriptSha)) {
                                luaScriptShaIpRateClick = newLuaScriptSha;
                                log.info("load ip-rate-click lua script to {}:{} and get sha {}", rcn.getUri().getHost(), rcn.getUri().getPort(), luaScriptShaIpRateClick);
                            }
                        } catch (Exception e) {
                            log.error("upload ip-rate-click lua script to {}:{} failed", rcn.getUri().getHost(), rcn.getUri().getPort());
                        }
                    } else {
                        log.warn("remote address{}|{}:{} connection activated, but can't find from cluster partitions by address and port.",
                                remote.getAddress().getHostAddress(), remote.getHostName(), remote.getPort());
                    }
                }).subscribe();
    }

    @PreDestroy
    public void close() {
        if (subscribe != null && subscribe.isDisposed()) {
            subscribe.dispose();
        }
        if (statefulRedisClusterConnection != null && statefulRedisClusterConnection.isOpen()) {
            statefulRedisClusterConnection.close();
        }
        if (redisClusterClient != null) {
            redisClusterClient.shutdown();
        }
        if (clientResources != null) {
            clientResources.shutdown();
        }
        log.info("close anti-fraud redis client success");
    }

    /**
     * 重复点击的定义为：同一设备号在{@link this#repeatClickExpireSec}时间内重复点击同一活动 ID（Aid）。
     *
     * @param deviceIds     请求中包含的所有支持的设备号 IDs
     * @param activityId    活动 ID
     * @return              true 表示有重复点击，false 表示没有重复点击
     */
    public boolean isRepeatClick(Set<String> deviceIds, long activityId) {
        for (String deviceId : deviceIds) {
            boolean isRepeat = syncCommands.evalsha(luaScriptShaRepeatClick, ScriptOutputType.BOOLEAN,
                    new String[]{deviceId},
                    String.valueOf(activityId).getBytes(StandardCharsets.UTF_8),
                    String.valueOf(repeatClickExpireSec).getBytes(StandardCharsets.UTF_8));
            if (isRepeat) {
                return true;
            }
        }
        return false;
    }

    /**
     * 点击跨渠道上报定义：同一设备号在{@link this#diffAidClickExpireSec}时间内，在不同的推广活动ID（Aid）间进行重复上报
     *
     * @param deviceIds  请求中包含的所有支持的设备号 IDs
     * @param activityId 活动 ID
     * @return true 表示跨渠道上报，false 表示没有跨渠道上报
     */
    public boolean isDiffAidClick(Set<String> deviceIds, long activityId) {
        for (String deviceId : deviceIds) {
            boolean isDiffAidClick = syncCommands.evalsha(luaScriptShaDiffAidClick, ScriptOutputType.BOOLEAN,
                    new String[]{deviceId},
                    String.valueOf(activityId).getBytes(StandardCharsets.UTF_8),
                    String.valueOf(diffAidClickExpireSec).getBytes(StandardCharsets.UTF_8));
            if (isDiffAidClick) {
                return true;
            }
        }
        return false;
    }

    public List<Long> isDiffClick(Set<String> deviceIds, long activityId, long sponsorId, String channelDid, String productId) {
        List<Long> result = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            result.add(Long.MAX_VALUE);
        }
        for (String deviceId : deviceIds) {
            List<Long> diffs = syncCommands.evalsha(luaScriptShaDiffClick, ScriptOutputType.MULTI,
                    new String[]{deviceId},
                    String.valueOf(diffClickExpireSec).getBytes(StandardCharsets.UTF_8),
                    String.valueOf(activityId).getBytes(StandardCharsets.UTF_8),
                    String.valueOf(sponsorId).getBytes(StandardCharsets.UTF_8),
                    String.valueOf(channelDid).getBytes(StandardCharsets.UTF_8),
                    String.valueOf(productId).getBytes(StandardCharsets.UTF_8)
            );
            if (CollectionUtils.isNotEmpty(diffs) && diffs.size() == 4) {
                for (int i = 0; i < diffs.size(); i++) {
                    if (diffs.get(i) > 0) {
                        result.set(i, Math.min(result.get(i), diffs.get(i)));
                    }
                }
            }
        }
        return result;
    }

    /**
     * IP分钟级点击量级过大定义：同一有效IP在{@link this#ipRateClickExpireSec}时间内，在同一推广活动ID（Aid）上报超过阈值次数
     *
     * @param ip  请求中的合法的ip
     * @param activityId 活动 ID
     * @return true IP分钟级点击量级过大，false 表示没有触发IP分钟级点击量级过大的异常
     */
    public boolean isIpRateClick(String ip, long activityId) {
        return syncCommands.evalsha(luaScriptShaIpRateClick, ScriptOutputType.BOOLEAN,
                new String[]{"ip_" + ip},
                String.valueOf(activityId).getBytes(StandardCharsets.UTF_8),
                String.valueOf(ipRateClickExpireSec).getBytes(StandardCharsets.UTF_8));
    }

}
