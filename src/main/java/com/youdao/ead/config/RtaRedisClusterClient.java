package com.youdao.ead.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.RedisURI;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import io.lettuce.core.codec.ByteArrayCodec;
import io.lettuce.core.codec.RedisCodec;
import io.lettuce.core.codec.StringCodec;
import io.lettuce.core.resource.DefaultClientResources;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2023/8/9.
 */
@Slf4j
@Component
public class RtaRedisClusterClient {
    @Value("${rta.redis.uri}")
    @Setter
    private String redisUri;
    @Value("${rta.redis.timeout}")
    @Setter
    private long timeout;

    private DefaultClientResources sharedClientResourcesBuilder;
    private RedisClusterClient redisClusterClient;
    private StatefulRedisClusterConnection<String, byte[]> statefulRedisClusterConnection;
    @Getter
    private RedisAdvancedClusterCommands<String, byte[]> syncCommands;

    private static final ClusterClientOptions.Builder CLUSTER_CLIENT_OPTIONS_BUILDER;

    private static final ClusterTopologyRefreshOptions.Builder CLUSTER_TOPOLOGY_REFRESH_OPTIONS_BUILDER
            = ClusterTopologyRefreshOptions
            .builder()
            .enablePeriodicRefresh(Duration.ofHours(1L))
            .enableAllAdaptiveRefreshTriggers();

    static {
        CLUSTER_CLIENT_OPTIONS_BUILDER = ClusterClientOptions.builder()
                .requestQueueSize(Integer.MAX_VALUE)
                .maxRedirects(1)
                .disconnectedBehavior(ClientOptions.DisconnectedBehavior.REJECT_COMMANDS)
                .topologyRefreshOptions(CLUSTER_TOPOLOGY_REFRESH_OPTIONS_BUILDER.build());
    }

    @PostConstruct
    public void init() throws Exception {
        initRedisClusterClient();
        log.info("init rta redis client successfully!");
    }

    private void initRedisClusterClient() {
        RedisURI redisClusterUri = RedisURI.create(redisUri);
        this.sharedClientResourcesBuilder = DefaultClientResources.builder().build();

        this.redisClusterClient = io.lettuce.core.cluster.RedisClusterClient.create(this.sharedClientResourcesBuilder, Collections.singletonList(redisClusterUri));
        this.redisClusterClient.setOptions(CLUSTER_CLIENT_OPTIONS_BUILDER.build());
        this.statefulRedisClusterConnection = this.redisClusterClient.connect(RedisCodec.of(new StringCodec(), new ByteArrayCodec()));
        this.statefulRedisClusterConnection.setReadFrom(ReadFrom.MASTER);
        this.syncCommands = this.statefulRedisClusterConnection.sync();
        this.syncCommands.setTimeout(Duration.ofMillis(timeout));
    }



    @PreDestroy
    public void close() {

        if (this.statefulRedisClusterConnection != null && this.statefulRedisClusterConnection.isOpen()) {
            this.statefulRedisClusterConnection.close();
        }

        if (this.redisClusterClient != null) {
            this.redisClusterClient.shutdown();
        }


        if (this.sharedClientResourcesBuilder != null) {
            this.sharedClientResourcesBuilder.shutdown();
        }

        log.info("rta redis client closed.");
    }


}
