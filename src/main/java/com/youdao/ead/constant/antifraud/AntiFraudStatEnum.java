package com.youdao.ead.constant.antifraud;

import com.youdao.quipu.avro.schema.ThirdPartyClick;
import com.youdao.quipu.avro.schema.ThirdPartyConv;

import java.util.Collections;
import java.util.function.BiConsumer;


/**
 * 异常流量标签枚举
 *
 * <AUTHOR>
 * @date 2024/7/9
 */
public enum AntiFraudStatEnum {

    /**
     * UA格式错误点击
     */
    FORMAT_ERROR_UA(76, ThirdPartyConv.Builder::setFormatErrorUaSend),

    /**
     * 设备号格式错误
     */
    FORMAT_ERROR_DEVICE_ID(77, ThirdPartyConv.Builder::setFormatErrorDeviceIdSend),

    /**
     * 内网IP
     */
    VALUE_ERROR_INTRANET_IP(78, ThirdPartyConv.Builder::setValueErrorIntranetIpSend),

    /**
     * IP黑名单点击
     */
    VALUE_ERROR_DARK_IP(79, ThirdPartyConv.Builder::setValueErrorDarkIpSend),

    /**
     * 设备id黑名单点击
     */
    VALUE_ERROR_DARK_DEVICE_ID(80, ThirdPartyConv.Builder::setValueErrorDarkDeviceIdSend),

    /**
     * IP取值异常
     */
    VALUE_ERROR_IP(81, ThirdPartyConv.Builder::setValueErrorIpSend),

    /**
     * UA取值异常
     */
    VALUE_ERROR_UA(82, ThirdPartyConv.Builder::setValueErrorUaSend),

    /**
     * UA取值与设备清洗池不一致
     */
    STANDARD_DIFF_UA(97, ThirdPartyConv.Builder::setStandardDiffUaSend),

    /**
     * OSV取值与设备清洗池不一致
     */
    STANDARD_DIFF_OSV(98, ThirdPartyConv.Builder::setStandardDiffOsvSend),

    /**
     * 机型取值与设备清洗池不一致
     */
    STANDARD_DIFF_MODEL(99, ThirdPartyConv.Builder::setStandardDiffModelSend),

    /**
     * 参数为空异常
     */
    PARAM_FILTER_OAID(4, ThirdPartyConv.Builder::setOaidFilled),
    PARAM_FILTER_OAID_MD5(6, ThirdPartyConv.Builder::setOaidMd5Filled),
    PARAM_FILTER_IDFA(7, ThirdPartyConv.Builder::setIdfaFilled),
    PARAM_FILTER_IDFA_MD5(8, ThirdPartyConv.Builder::setIdfaMd5Filled),
    PARAM_FILTER_IMEI_MD5(9, ThirdPartyConv.Builder::setImeiFilled),
    PARAM_FILTER_CAID(10, ThirdPartyConv.Builder::setCaidFilled),
    PARAM_FILTER_CAID_MD5(11, ThirdPartyConv.Builder::setCaidMd5Filled),
    PARAM_FILTER_ANDROID_ID(12, ThirdPartyConv.Builder::setAndroidIdFilled),
    PARAM_FILTER_ANDROID_ID_MD5(13, ThirdPartyConv.Builder::setAndroidIdMd5Filled),
    PARAM_FILTER_UA(14, ThirdPartyConv.Builder::setUaFilled),
    PARAM_FILTER_IP(15, ThirdPartyConv.Builder::setIpFilled),
    // os携带率又专门两个参数代替： PARAM_ANDROID_OS_FILLED PARAM_IOS_OS_FILLED
//    PARAM_FILTER_OS(16, null),
    PARAM_FILTER_MODEL(17, ThirdPartyConv.Builder::setDeviceModelFilled),
    PARAM_FILTER_OS_VERSION(18, ThirdPartyConv.Builder::setOsVersionFilled),

    // 参数和推广类型不一致
    /**
     * 操作系统与推广类型不匹配
     */
    UNEXPECTED_OS(20, ThirdPartyConv.Builder::setUnexpectedOsClick),
    /**
     * 设备号与推广类型不匹配
     */
    UNEXPECTED_DEVICE(2, ThirdPartyConv.Builder::setUnexpectedDeviceClick),
    /**
     * ua与推广类型不匹配
     */
    UNEXPECTED_UA(3, ThirdPartyConv.Builder::setUnexpectedUaClick),
    /**
     * 手机机型与推广类型不一致点击
     */
    UNEXPECTED_MODEL_CLICK(83, ThirdPartyConv.Builder::setUnexpectedModelSend),

    /**
     * 点击和请求按数不一致
     */
    RELATED_RTA_DEVICE(84, ThirdPartyConv.Builder::setRelatedRtaDeviceSend),
    RELATED_RTA_UA(85, ThirdPartyConv.Builder::setRelatedRtaUaSend),
    RELATED_RTA_MODEL(86, ThirdPartyConv.Builder::setRelatedRtaModelSend),
    RELATED_RTA_IP(87, ThirdPartyConv.Builder::setRelatedRtaIpSend),
    RELATED_RTA_OSV(88, ThirdPartyConv.Builder::setRelatedRtaOsvSend),

    //===设备间参数关联异常===
    /**
     * 同一设备号关联多个UA
     */
    RELATED_MULTI_UA(89, ThirdPartyConv.Builder::setRelatedMultiUaSend),
    /**
     * 同一设备号关联多个机型
     */
    RELATED_MULTI_MODEL(90, ThirdPartyConv.Builder::setRelatedMultiModelSend),
    /**
     * 同一设备关联多个osv
     */
    RELATED_MULTI_OSV(91, ThirdPartyConv.Builder::setRelatedMultiOsvSend),

    //===行为频次异常===
    /**
     * 重复点击
     */
    DUPLICATE_CLICK(1, ThirdPartyConv.Builder::setRepeatClick),
    /**
     * IP分钟级点击量过大
     */
    ACTION_RATE_IP(92, ThirdPartyConv.Builder::setActionRateIpSend),
    /**
     * IP高频变化
     */
    ACTION_RATE_DEVICE_IP(93, ThirdPartyConv.Builder::setActionRateDeviceIpSend),
    /**
     * 设备号日点击量级过大
     */
    ACTION_RATE_CLICK(94, ThirdPartyConv.Builder::setActionRateClickSend),

    //===行为特征异常===
    /**
     * RTA点击时间异常
     */
    RTA_CLICK_TIME_ERROR(95, ThirdPartyConv.Builder::setRtaClickTimeErrorSend),
    /**
     * 点击跨渠道上报
     */
    ACTION_FEATURES_DIFF_SOURCE(96, ThirdPartyConv.Builder::setActionFeaturesDiffSourceSend),

    //===其他统计===
    EXCEPTION(127, ThirdPartyConv.Builder::setExceptionSend),
    PARAM_TS_FILLED(126, ThirdPartyConv.Builder::setTsFilled),
    PARAM_ACTIVITY_ID_FILLED(125, ThirdPartyConv.Builder::setActivityIdFilled),
    PARAM_ALID_FILLED(124, ThirdPartyConv.Builder::setAlidFilled),
    PARAM_ANDROID_OS_FILLED(123, ThirdPartyConv.Builder::setAndroidOsFilled),
    PARAM_IOS_OS_FILLED(122, ThirdPartyConv.Builder::setIosOsFilled),
    ;


    /**
     * 转化反作弊统计指标id
     */
    private final int statId;

    /**
     * 转化维度上看 异常分别对应的更新统计字段的方法，
     */
    private final BiConsumer<ThirdPartyConv.Builder, Integer> sendSetter;


    AntiFraudStatEnum(int statId, BiConsumer<ThirdPartyConv.Builder, Integer> sendSetter) {
        this.statId = statId;
        this.sendSetter = sendSetter;
    }

    public int getStatId() {
        return statId;
    }

    public BiConsumer<ThirdPartyConv.Builder, Integer> getSendSetter() {
        return sendSetter;
    }
}
