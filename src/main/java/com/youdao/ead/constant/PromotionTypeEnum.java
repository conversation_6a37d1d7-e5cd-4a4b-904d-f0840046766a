package com.youdao.ead.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
public enum PromotionTypeEnum {
    /**
     * 落地页
     */
    LANDING_PAGE(0),
    /**
     * 安卓下载
     */
    ANDROID_DOWNLOAD(1),
    /**
     * ios下载
     */
    IOS_DOWNLOAD(2),

    UNKNOWN(-1);

    /**
     * 推广活动类型
     */
    @Getter
    private final int type;

    PromotionTypeEnum(int type) {
        this.type = type;
    }

    public static PromotionTypeEnum valueOf(int type) {
        for (PromotionTypeEnum value : PromotionTypeEnum.values()) {
            if (value.type == type) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
