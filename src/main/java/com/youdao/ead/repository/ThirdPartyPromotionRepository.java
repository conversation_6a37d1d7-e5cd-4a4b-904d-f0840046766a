package com.youdao.ead.repository;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youdao.ead.dispatcher.ClickRtaStrategy;
import com.youdao.ead.dto.ConvExtDTO;
import com.youdao.ead.entity.AdContent;
import com.youdao.ead.entity.ConvertTrackingInfo;
import com.youdao.ead.entity.ThirdPartyPromotionInfo;
import com.youdao.ead.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;
import outfox.ead.dsp.protocol.youdao.ConvTracker;
import outfox.ead.dsp.protocol.youdao.TrackerCommonDimension;

import java.util.*;
import java.util.stream.Collectors;

import static com.youdao.ead.constant.ActivityTypes.ACTIVITY_TYPE_RTA;

/**
 * <AUTHOR>
 * @date 2023/8/16.
 */
@Slf4j
@Repository("thirdPartyPromotionRepository")
public class ThirdPartyPromotionRepository {

    @Qualifier("eadb1JdbcTemplate")
    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    public static final String CONVERT_TRACKING_QUERY = "select CONVERT_TRACKING_UID, CONVERT_TRACKING_TYPE, CONVERT_TRACKING_DEBUG_STATUS, CLICK_TRACKING_LINK " +
            "from ConvertTrackingInfo " +
            "where (CONVERT_TRACKING_UID = :convertTrackingUid " +
            "  and SPONSOR_ID = :sponsorId) or" +
            " CONVERT_TRACKING_UID in (select PUSHED_CONVERT_TRACKING_UID" +
            "                               from ConvertTrackingCrossAccountPush " +
            "                               where TARGET_SPONSOR_ID = :sponsorId " +
            "                                 and PUSHED_CONVERT_TRACKING_UID = :convertTrackingUid)";

    public static final String AD_CONTENT_QUERY = "select g.AD_CAMPAIGN_ID as adCampaignId, g.AD_GROUP_ID, g.SPONSOR_ID, c.DEST_LINK, c.AD_CONTENT_ID, g_extend.CONVERT_TRACKING_UID " +
            "from AdGroup g " +
            "         join AdContent c " +
            "         join AdGroupExtendForDSP g_extend " +
            "where g.AD_GROUP_ID = c.AD_GROUP_ID " +
            "  and g_extend.AD_GROUP_ID = g.AD_GROUP_ID ";

    public static final String SPECIFIC_AD_CONTENT_QUERY = AD_CONTENT_QUERY  +
            "  and c.AD_CONTENT_ID = :adContentId ";

    public static final String BATCH_AD_CONTENT_QUERY = AD_CONTENT_QUERY  +
            "  and c.AD_CONTENT_ID in (:adContentIds) ";

    public static final String PROMOTION_QUERY = "select tdp.promotion_id as promotionId," +
            "       tdp.sponsor_id as sponsorId," +
            "       tdp.promotion_uid as promotionUID," +
            "       tdp.promotion_name as promotionName," +
            "       tdp.promotion_type as promotionType," +
            "       tdp.has_subchannel as hasSubchannel," +
            "       tdp.dest_link as destLink," +
            "       tdp.new_dest_link as newDestLink," +
            "       tdp.sync_click_monitor_link as syncClickMonitorLink," +
            "       tdp.async_click_monitor_link as asyncClickMonitorLink," +
            "       tdp.exposure_monitor_link as exposureMonitorLink," +
            "       tdp.crowd_tag as crowdTag," +
            "       tdp.use_convert_tracking as useConvertTracking," +
            "       tdp.convert_tracking_uid as convertTrackingUID," +
            "       tdp.convert_tracking_action as convertTrackingAction," +
            "       tdp.create_time as createTime," +
            "       tdp.promotion_status as promotionStatus," +
            "       tdp.relate_ad_content_id as adContentId," +
            "       tdp.tdp_platform as tdpPlatform," +
            "       tdp.tdp_creative_ids as tdpCreativeIds," +
            "       tdp.tdp_sponsor_ids as tdpSponsorIds," +
            "       tdp.secret_key as secretKey," +
            "       tdp.client_id as clientId," +
            "       tdp.package_name as packageName," +
            "       tdp.tdp_convert_id as tdpConvertId," +
            "       tdp.strategy as strategy," +
            "       tdp.activity_type as activityType," +
            "       tdp.rta_content_id as rtaContentId," +
            "       tdp.non_rta_content_ids as nonRtaContentIds," +
            "       tdp.filter_no_bid_conv as filterNoBidConv," +
            "       c.order as sponsorCategoryOrder" +
            " from ThirdPartPromotionInfo as tdp " +
            "left join SponsorCategory as sc on tdp.SPONSOR_ID = sc.SPONSOR_ID " +
            "left join Category as c on sc.CATEGORY_ID = c.ID";

    public static final String SPECIFIC_PROMOTION_QUERY = PROMOTION_QUERY +
                    " where tdp.PROMOTION_STATUS = 0" +
            " and tdp.promotion_id =:promotionID";


    public static final String RTA_STRATEGY_QUERY = "select promotion_id," +
                                                    "       STRATEGY," +
                                                    "       activity_type," +
                                                    "       rta_content_id," +
                                                    "       non_rta_content_ids" +
                                                    " from ThirdPartPromotionInfo " +
                                                    " where STRATEGY = 2 " +
                                                    "  and PROMOTION_STATUS = 0 ";

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);;

    public List<ClickRtaStrategy.ActivityStrategy> findAllRtaStrategy() {
        return jdbcTemplate.query(RTA_STRATEGY_QUERY, Collections.emptyMap(), (resultSet, i) -> {
            long activityId = resultSet.getLong("promotion_id");
            int activityType = resultSet.getInt("activity_type");
            String nonRtaContentIds = resultSet.getString("non_rta_content_ids");
            long rtaContentId = resultSet.getLong("rta_content_id");
            ClickRtaStrategy.ActivityStrategy activityStrategy = new ClickRtaStrategy.ActivityStrategy();
            activityStrategy.setActivityId(activityId);
            activityStrategy.setRtaActivity(activityType == ACTIVITY_TYPE_RTA);
            if (StringUtils.isNotBlank(nonRtaContentIds)) {
                List<Long> nonRtaContentIdsList = Arrays.stream(nonRtaContentIds.split("\\s*,\\s*")).map(Long::parseLong).collect(Collectors.toList());
                activityStrategy.setNonRtaAdContentIds(nonRtaContentIdsList);
            }
            if (rtaContentId > 0) {
                activityStrategy.setRtaAdContentId(rtaContentId);
            }
            activityStrategy.setApiRtaEnabled(true);
            return activityStrategy;
        });
    }

    public ThirdPartyPromotionInfo findThirdPartyPromotionInfo(Long activityId) {
        Map<String, Object> activityParam = new HashMap<>();
        activityParam.put("promotionID", activityId);
        return jdbcTemplate.queryForObject(SPECIFIC_PROMOTION_QUERY,
                activityParam,
                new BeanPropertyRowMapper<>(ThirdPartyPromotionInfo.class));
    }

    /**
     * 构造conv_ext宏
     *
     * @param info 推广活动信息
     */
    public ConvExtDTO createConvExt(ThirdPartyPromotionInfo info) {
        Map<String, Object> ctParam = new HashMap<>();
        ctParam.put("convertTrackingUid", info.getConvertTrackingUID());
        ctParam.put("sponsorId", info.getSponsorId());
        ConvertTrackingInfo convertTrackingInfo = jdbcTemplate.queryForObject(
                CONVERT_TRACKING_QUERY, ctParam, new BeanPropertyRowMapper<>(ConvertTrackingInfo.class));
        AdContent adContent = findAdContentById(info.getAdContentId());
        return createConvExt(info, convertTrackingInfo, adContent);
    }

    public ConvExtDTO createConvExt(ThirdPartyPromotionInfo info, ConvertTrackingInfo convertTrackingInfo, AdContent adContent){
        outfox.ead.dsp.protocol.youdao.TrackerCommonDimension.ThirdPartyDimension.Builder thirdPartyDimensionBuilder;
        ConvExtDTO convExtDTO = new ConvExtDTO();
        if (info.getAdContentId() != null && info.getAdContentId() > 0L) {
            thirdPartyDimensionBuilder = TrackerCommonDimension.ThirdPartyDimension.newBuilder()
                    .setSponsorId(adContent.getSponsorId())
                    .setActivityId(info.getPromotionId())
                    .setCampaignId(adContent.getAdCampaignId())
                    .setGroupId(adContent.getAdGroupId())
                    .setVariationId(info.getAdContentId());

            convExtDTO.setActivityId(info.getPromotionId());
            convExtDTO.setAdContentId(info.getAdContentId());
            convExtDTO.setSponsorId(adContent.getSponsorId());
            convExtDTO.setGroupId(adContent.getAdGroupId());
            convExtDTO.setCampaignId(adContent.getAdCampaignId());
            convExtDTO.setDestLink(adContent.getDestLink());
            convExtDTO.setFilterNoBidConv(info.isFilterNoBidConv());
        } else {
            thirdPartyDimensionBuilder = TrackerCommonDimension.ThirdPartyDimension.newBuilder()
                    .setSponsorId(info.getSponsorId())
                    .setActivityId(info.getPromotionId());
        }
        ConvTracker.Conv.Builder builder = ConvTracker.Conv.newBuilder()
                .setDestLink(info.getDestLink())
                .setThirdPartyDimension(thirdPartyDimensionBuilder);
        if (convertTrackingInfo != null) {
            builder.setCtId(convertTrackingInfo.getConvertTrackingUid())
                    .setCtType(convertTrackingInfo.getConvertTrackingType())
                    .setCtDebugStatus(convertTrackingInfo.getConvertTrackingDebugStatus())
                    // 这里的ctAction字段表示当前推广活动的目标转化事件，一般是在第三方推广平台，用于判断是否触发转化回掉，比如小米，具体逻辑见convert-tracking 服务
                    // 和广告主的转化归因逻辑无关
                    .setCtAction(info.getConvertTrackingAction());

            //非落地页加上clickLink
            if (convertTrackingInfo.getConvertTrackingType() != 0 && convertTrackingInfo.getClickTrackingLink() != null) {
                builder.setClkTrackLink(convertTrackingInfo.getClickTrackingLink());
            }
        }
        ConvTracker.Conv conv = builder.build();
        String convExt = Base64.getUrlEncoder()
                .withoutPadding().encodeToString(conv.toByteArray());

        convExtDTO.setConvExt(convExt);
        return convExtDTO;
    }

    /**
     * 根据id查询广告变体信息
     * @param adContentId
     * @return
     */
    public AdContent findAdContentById(Long adContentId) {
        Map<String, Object> groupParam = new HashMap<>();
        groupParam.put("adContentId", adContentId);
        AdContent adContent = jdbcTemplate.queryForObject(SPECIFIC_AD_CONTENT_QUERY, groupParam, new BeanPropertyRowMapper<>(AdContent.class));
        if (adContent == null) {
            throw new BizException(String.format("adContent not found by id: %s", adContentId));
        }
        return adContent;
    }
}
