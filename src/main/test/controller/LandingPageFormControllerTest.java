package controller;

import boot.StarterTest;
import net.sf.json.JSONObject;
import org.codehaus.jackson.JsonProcessingException;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.SpringApplicationConfiguration;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.noah.boot.Starter;
import outfox.ead.noah.controller.LandingPageFormController;
import outfox.ead.noah.dao.AdCampaignDao;
import outfox.ead.noah.dao.AdContentDao;
import outfox.ead.noah.dao.AdGroupDao;
import outfox.ead.noah.dao.LandingPageDao;
import outfox.ead.noah.dao.LandingPageFormContentDao;
import outfox.ead.noah.dao.LandingPageFormDao;
import outfox.ead.noah.dao.SdkDeveloperDao;
import outfox.ead.noah.dao.SdkSlotDao;
import outfox.ead.noah.dao.SponsorDao;
import outfox.ead.noah.service.LandingPageFormContentService;
import outfox.ead.noah.service.LandingPageFormService;
import outfox.ead.noah.service.impl.LandingPageFormContentServiceImpl;
import outfox.ead.noah.service.impl.LandingPageFormServiceImpl;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

/**
 * Created by wangmo on 2017/11/16.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringApplicationConfiguration(StarterTest.class)
@WebAppConfiguration
@Ignore
public class LandingPageFormControllerTest extends AbstractControllerWithH2Test {

    //处理@Current注解用
    @Autowired
    private SponsorDao sponsorDao;
    @Autowired
    private SdkDeveloperDao sdkDeveloperDao;

    //当前Controller间接依赖的ThriftService
    @Autowired private LandingPageFormDao landingPageFormDao;
    @Autowired private LandingPageFormContentDao landingPageFormContentDao;
    @Autowired private LandingPageDao landingPageDao;
    @Autowired private AdCampaignDao adCampaignDao;
    @Autowired private AdGroupDao adGroupDao;
    @Autowired private AdContentDao adContentDao;
    @Autowired private SdkSlotDao sdkSlotDao;

    //当前Controller间接依赖的Service
    private LandingPageFormService landingPageFormService = new LandingPageFormServiceImpl();
    private LandingPageFormContentService landingPageFormContentService = new LandingPageFormContentServiceImpl();

    @InjectMocks
    private LandingPageFormController landingPageFormController = new LandingPageFormController();

    @Before
    public void setup() throws JsonProcessingException {
        super.init();

        ReflectionTestUtils.setField(resolver, "sponsorDao", sponsorDao);
        ReflectionTestUtils.setField(resolver, "sdkDeveloperDao", sdkDeveloperDao);

        ReflectionTestUtils.setField(landingPageFormController, "landingPageFormService", landingPageFormService);
        ReflectionTestUtils.setField(landingPageFormController, "landingPageFormContentService", landingPageFormContentService);

        ReflectionTestUtils.setField(landingPageFormService, "landingPageFormDao", landingPageFormDao);
        ReflectionTestUtils.setField(landingPageFormService, "landingPageFormContentDao", landingPageFormContentDao);
        ReflectionTestUtils.setField(landingPageFormContentService, "landingPageFormDao", landingPageFormDao);
        ReflectionTestUtils.setField(landingPageFormContentService, "landingPageFormContentDao", landingPageFormContentDao);
        ReflectionTestUtils.setField(landingPageFormContentService, "landingPageDao", landingPageDao);
        ReflectionTestUtils.setField(landingPageFormContentService, "adCampaignDao", adCampaignDao);
        ReflectionTestUtils.setField(landingPageFormContentService, "adGroupDao", adGroupDao);
        ReflectionTestUtils.setField(landingPageFormContentService, "adContentDao", adContentDao);
        ReflectionTestUtils.setField(landingPageFormContentService, "sdkSlotDao", sdkSlotDao);

        mvc = MockMvcBuilders
            .standaloneSetup(landingPageFormController)
            .addPlaceHolderValue("version", "v1.0.0").setCustomArgumentResolvers(resolver)
            .build();
    }

    @Test
    public void testCreateLandingPageForm() {
        String formName = "TEST_LPF_NAME" + System.currentTimeMillis() % 100000;
        try {
            ResultActions actions;

            // save empty, expect 400
            actions = mvc.perform(MockMvcRequestBuilders.post("/v1.0.0/LPForms" )
                .header("sponsorIdHeader", 1L)
                .param("landingPageFormName", formName)
                .param("formDefinition", "{}")
            );
            actions.andExpect(jsonPath("$.error_code").value(Matchers.is(400)));

            // save part of formDefinition, expect 400
            actions = mvc.perform(MockMvcRequestBuilders.post("/v1.0.0/LPForms" )
                .header("sponsorIdHeader", 1L)
                .param("landingPageFormName", formName)
                .param("formDefinition", "{\"buttonInfo\":{\"buttonName\":\"立即提交\",\"textColor\":\"rgba(255,255,255,1)\",\"buttonColor\":\"rgba(233,61,52,1)\",\"successPrompt\":\"啦啦啦\",\"isJump\":false,\"jumpLink\":\"\"},\"formItems\":[]}")
            );
            actions.andExpect(jsonPath("$.error_code").value(Matchers.is(400)));

            // save normal, expect success, get id
            actions = mvc.perform(MockMvcRequestBuilders.post("/v1.0.0/LPForms" )
                .header("sponsorIdHeader", 1L)
                .param("landingPageFormName", formName)
                .param("formDefinition", "{\"buttonInfo\":{\"buttonName\":\"提交\",\"textColor\":\"rgba(255,255,255,1)\",\"buttonColor\":\"rgba(233,61,52,1)\",\"successPrompt\":\"dsf\",\"isJump\":false,\"jumpLink\":\"\"},\"formInfo\":{\"formTextColor\":\"rgba(0,0,0,1)\",\"formBackgroundColor\":\"rgba(255,255,255,1)\"},\"formItems\":[{\"itemId\":\"id0\",\"type\":\"姓名\",\"title\":\"姓名\",\"required\":true,\"subItems\":[]},{\"itemId\":\"id1\",\"type\":\"手机\",\"title\":\"手机\",\"required\":true,\"subItems\":[]},{\"itemId\":\"id2\",\"type\":\"下拉框\",\"title\":\"下拉框\",\"required\":false,\"subItems\":[\"dsf,fdsdsf,dsfdsf;\"]}]}")
            );
            actions.andExpect(jsonPath("$.error_code").value(Matchers.is(0)));
            actions.andDo(MockMvcResultHandlers.print());
            actions.andExpect(jsonPath("$.data.landingPageFormId").value(Matchers.greaterThan(0)));
            String resultStr = actions.andReturn().getResponse().getContentAsString();
            Long addedFormId = JSONObject.fromObject(JSONObject.fromObject(resultStr).getString("data")).getLong("landingPageFormId");

            // save with duplicate name, expect conflict
            actions = mvc.perform(MockMvcRequestBuilders.post("/v1.0.0/LPForms" )
                .header("sponsorIdHeader", 1L)
                .param("landingPageFormName", formName)
                .param("formDefinition", "{\"buttonInfo\":{\"buttonName\":\"提交\",\"textColor\":\"rgba(255,255,255,1)\",\"buttonColor\":\"rgba(233,61,52,1)\",\"successPrompt\":\"dsf\",\"isJump\":false,\"jumpLink\":\"\"},\"formInfo\":{\"formTextColor\":\"rgba(0,0,0,1)\",\"formBackgroundColor\":\"rgba(255,255,255,1)\"},\"formItems\":[{\"itemId\":\"id0\",\"type\":\"姓名\",\"title\":\"姓名\",\"required\":true,\"subItems\":[]},{\"itemId\":\"id1\",\"type\":\"手机\",\"title\":\"手机\",\"required\":true,\"subItems\":[]},{\"itemId\":\"id2\",\"type\":\"下拉框\",\"title\":\"下拉框\",\"required\":false,\"subItems\":[\"dsf,fdsdsf,dsfdsf;\"]}]}")
            );
            actions.andExpect(jsonPath("$.error_code").value(Matchers.is(409)));

            // delete origin, expect success
            actions =  mvc.perform(MockMvcRequestBuilders.delete("/v1.0.0/LPForms/{landingPageFormId}", addedFormId)
                .header("sponsorIdHeader", 1L));
            actions.andExpect(jsonPath("$.error_code").value(Matchers.is(0)));

            // save with origin name, expect success
            actions = mvc.perform(MockMvcRequestBuilders.post("/v1.0.0/LPForms" )
                .header("sponsorIdHeader", 1L)
                .param("landingPageFormName", formName)
                .param("formDefinition", "{\"buttonInfo\":{\"buttonName\":\"提交\",\"textColor\":\"rgba(255,255,255,1)\",\"buttonColor\":\"rgba(233,61,52,1)\",\"successPrompt\":\"dsf\",\"isJump\":false,\"jumpLink\":\"\"},\"formInfo\":{\"formTextColor\":\"rgba(1,1,1,1)\",\"formBackgroundColor\":\"rgba(255,255,255,1)\"},\"formItems\":[{\"itemId\":\"id0\",\"type\":\"姓名\",\"title\":\"姓名\",\"required\":true,\"subItems\":[]},{\"itemId\":\"id1\",\"type\":\"手机\",\"title\":\"手机\",\"required\":true,\"subItems\":[]},{\"itemId\":\"id2\",\"type\":\"下拉框\",\"title\":\"下拉框\",\"required\":false,\"subItems\":[\"dsf,fdsdsf,dsfdsf;\"]}]}")
            );
            actions.andExpect(jsonPath("$.error_code").value(Matchers.is(0)));
            // delete test case, expect success
            String newResultStr = actions.andReturn().getResponse().getContentAsString();
            Long newAddedFormId = JSONObject.fromObject(JSONObject.fromObject(newResultStr).getString("data")).getLong("landingPageFormId");
            actions =  mvc.perform(MockMvcRequestBuilders.delete("/v1.0.0/LPForms/{landingPageFormId}", newAddedFormId)
                    .header("sponsorIdHeader", 1L));
            actions.andExpect(jsonPath("$.error_code").value(Matchers.is(0)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
