package dao;

import boot.StarterTest;
import javax.annotation.concurrent.NotThreadSafe;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.SpringApplicationConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;
import outfox.ead.noah.boot.Starter;
import outfox.ead.noah.dao.SdkAppInfoDao;
import outfox.ead.noah.entity.SdkAppInfo;

/**
 * Created by zhaoteng on 2017/5/31.
 */

@NotThreadSafe
@RunWith(SpringJUnit4ClassRunner.class)
@SpringApplicationConfiguration(StarterTest.class)
@WebAppConfiguration
public class SdkAppInfoDaoImplTest {

    @Autowired
    private SdkAppInfoDao sdkAppInfoDao;

    @Test
    public void testGetById(){
        SdkAppInfo sdkAppInfo = new SdkAppInfo();
        sdkAppInfo.setSdkAppId(1);
        sdkAppInfo.setSdkAppDeveloperId(1L);
        sdkAppInfo.setSdkAppName("dd");
        sdkAppInfo.setSdkAppPakageName("dddd");

        sdkAppInfoDao.saveOrUpdateSdkAppInfo(sdkAppInfo);
        Assert.assertNotNull(sdkAppInfoDao.getById(1L));
    }
}
