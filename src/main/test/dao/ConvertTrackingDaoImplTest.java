package dao;

import boot.StarterTest;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.SpringApplicationConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import outfox.ead.noah.dao.ConvertTrackingDao;
import outfox.ead.noah.entity.ConvertTrackingInfo;
import outfox.ead.noah.util.code.TypeCode;

import javax.annotation.concurrent.NotThreadSafe;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

import static outfox.ead.noah.util.code.StatusCode.*;

/**
 *
 * Created by wang<PERSON> on 2018/1/31.
 *
 */
@NotThreadSafe
@RunWith(SpringJUnit4ClassRunner.class)
@SpringApplicationConfiguration(StarterTest.class)
@WebAppConfiguration
public class ConvertTrackingDaoImplTest {

    @Autowired
    private ConvertTrackingDao convertTrackingDao;

    @Test
    public void testSaveAndGet() {
        ConvertTrackingInfo c = new ConvertTrackingInfo();
        c.setConvertTrackingName("转化-王默-测试"+getRandomString());
        c.setSponsorId(1L);
        c.setConvertTrackingActions("landingpage_submitform,landingpage_dial");
        c.setConvertTrackingType(TypeCode.CONVERT_TYPE_LANDINGPAGE);
        c.setConvertTrackingStatus(CONVERT_OP_STATUS_VALID);
        c.setConvertTrackingDebugStatus(CONVERT_DEBUG_STATUS_READY_TO_TEST);
        c.setCreateTime(new Timestamp(System.currentTimeMillis()));
        c.setLastModTime(new Timestamp(System.currentTimeMillis()));
        convertTrackingDao.save(c);
        String udid = c.getConvertTrackingId() + getRandomString();
        c.setConvertTrackingUid(udid);
        convertTrackingDao.update(c);
        ConvertTrackingInfo qc = convertTrackingDao.getConvertTrackingInfoByUid(1L, udid);
        Assert.assertTrue(qc.getConvertTrackingUid().equals(c.getConvertTrackingUid()));
        ConvertTrackingInfo nothing = convertTrackingDao.getConvertTrackingInfoByUid(1L, "13781612366631");
        Assert.assertNull(nothing);
    }

    @Test
    public void testGetConvertTrackingInfoList() {
        ConvertTrackingInfo c = new ConvertTrackingInfo();
        c.setConvertTrackingName("转化-王默-测试"+getRandomString());
        c.setSponsorId(1L);
        c.setConvertTrackingActions("landingpage_submitform,landingpage_dial");
        c.setConvertTrackingType(TypeCode.CONVERT_TYPE_LANDINGPAGE);
        c.setConvertTrackingStatus(CONVERT_OP_STATUS_VALID);
        c.setConvertTrackingDebugStatus(CONVERT_DEBUG_STATUS_READY_TO_TEST);
        c.setCreateTime(new Timestamp(System.currentTimeMillis()));
        c.setLastModTime(new Timestamp(System.currentTimeMillis()));
        convertTrackingDao.save(c);

        List<ConvertTrackingInfo> convertTrackingInfos = convertTrackingDao.getConvertTrackingInfoList(1L, 0, 10);
        Assert.assertTrue(convertTrackingInfos != null);
        Assert.assertTrue(convertTrackingInfos.size() >= 1);
        if (CollectionUtils.isNotEmpty(convertTrackingInfos)) {
            Assert.assertTrue(convertTrackingDao.getConvertTrackingInfoListCount(1L) > 0);
        }
    }

    @Test
    public void testGetAndUpdateMulti() {
        List<String> udids = Arrays.asList("17272018793", "28951587305", "36687498593");
        List<ConvertTrackingInfo> convertTrackingInfos = convertTrackingDao.getConvertTrackingInfoListByUdids(1L, udids);
        Assert.assertTrue(convertTrackingInfos != null);
        for (ConvertTrackingInfo i : convertTrackingInfos) {
            Assert.assertTrue(udids.contains(i.getConvertTrackingUid()));
            i.setConvertTrackingStatus(CONVERT_OP_STATUS_PAUSE);
        }
        convertTrackingDao.update(convertTrackingInfos);
        convertTrackingInfos = convertTrackingDao.getConvertTrackingInfoListByUdids(1L, udids);
        for (ConvertTrackingInfo i : convertTrackingInfos) {
            Assert.assertTrue(i.getConvertTrackingStatus() == CONVERT_OP_STATUS_PAUSE);
            i.setConvertTrackingStatus(CONVERT_OP_STATUS_DELETE);
        }
        convertTrackingDao.update(convertTrackingInfos);
        convertTrackingInfos = convertTrackingDao.getConvertTrackingInfoListByUdids(1L, udids);
        Assert.assertTrue(CollectionUtils.isEmpty(convertTrackingInfos));
    }

    private String getRandomString() {
        StringBuilder str = new StringBuilder();
        Random rm = new Random();
        for (int i = 0; i < 5; i++) {
            /******** 产生一个0-9之间的数 ********/
            int x = (rm.nextInt() >>> 1) % 10;
            str.append(x);
        }
        return str.toString();
    }

}
