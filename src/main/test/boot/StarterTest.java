package boot;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.MultipartAutoConfiguration;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;
import outfox.ead.noah.boot.Starter;
import outfox.ead.noah.conf.Config;
import outfox.ead.noah.conf.SecurityConfig;
import outfox.ead.noah.conf.SwaggerConfig;

/**
 * Created by huanghuan on 16/4/12.
 */
@EnableCircuitBreaker
@SpringBootApplication
@EnableAutoConfiguration(exclude = {MultipartAutoConfiguration.class, Config.class})
@ComponentScan(basePackages = {"outfox.ead.noah", "conf"}, excludeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = {Config.class, Starter.class})})
@EnableAsync
public class StarterTest {

    public static void main(String[] args) {
        SpringApplication.run(StarterTest.class, args);
    }

}

