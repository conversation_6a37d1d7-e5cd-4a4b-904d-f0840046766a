package service;

import boot.StarterTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.SpringApplicationConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import outfox.ead.noah.boot.Starter;
import outfox.ead.noah.dao.AdCampaignDao;
import outfox.ead.noah.dao.AdPlanDao;
import outfox.ead.noah.entity.AdCampaign;
import outfox.ead.noah.entity.AdPlan;
import outfox.ead.noah.service.AdCampaignService;

/**
 * Created by zhaoteng on 2017/6/26.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringApplicationConfiguration(StarterTest.class)
@WebAppConfiguration
public class AdCampaignServiceTest {

    @Autowired
    private AdCampaignService adCampaignService;

    @Autowired
    private AdCampaignDao adCampaignDao;

    @Autowired
    private AdPlanDao adPlanDao;

    @Test
    public void testUpdateAdCampaignDayBudget(){
        AdCampaign adCampaign = new AdCampaign();
        adCampaign.setAdPlanId(1);
        adCampaignDao.saveOrUpdateAdCampaign(adCampaign);
        adCampaign.setAdCampaignId(1L);
        adCampaignDao.saveOrUpdateAdCampaign(adCampaign);

        AdPlan adPlan = new AdPlan();
        adPlan.setDayBudget(12);
        adPlanDao.saveOrUpdateAdPlan(adPlan);
        adPlan.setAdPlanId(adCampaign.getAdPlanId());
        adPlanDao.saveOrUpdateAdPlan(adPlan);

        Long adCampaignId = 1L;
        Integer dailyBudget = 12345;
        adCampaignService.updateAdCampaignDayBudget(adCampaignId, dailyBudget);
    }
}
