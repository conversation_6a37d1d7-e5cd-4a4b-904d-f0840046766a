syntax = "proto2";
package outfox.ead.data.dataserv;
option java_outer_classname = "InvertedIndex";

//广告位索引持有者。
message Index {
    //提取索引。
    repeated ScopeFeatureValueEntry indices = 1;
    //排除索引。
    repeated ScopeFeatureValueEntry disIndices = 2;
    //广告组id及其下标持有者。
    repeated IndexGidEntry index2Gid =3;
}

//特征分类到其下各特征值的Map结构。
//其类型是 map<scope, map<featureValue, groupIdsInBitSet>>
message ScopeFeatureValueEntry {
    required int32 scope = 1;
    repeated FeatureValueGidEntry feature_2_group_ids =2;
}

//特征值到广告组id列表的持有者。
message FeatureValueGidEntry {
    //广告特征值。
    required string feature_value = 1;
    //广告组id对应的bit数组位下标。
    required bytes group_id_bytes = 2;
}

message IndexGidEntry {
    //index of the group id in the bitset.
    required int32 index = 1;
    //group id.
    required int64 group_id =2;
}