#!/usr/bin/env bash
set -e
set -x

path=$(cd "$(dirname "$0")"/../; pwd)

case $1 in
    "prod")
    properties_path=${path}/config/course_data_join_task.properties
    ;;
    "test")
    properties_path=${path}/config/course_data_join_task_test.properties
    ;;
    *)
    echo "Please set environment: prod or test";
    exit 1;
esac

echo "config:""${properties_path}"
nohup "${path}"/bin/run-job.sh --config-factory=org.apache.samza.config.factories.PropertiesConfigFactory --config-path=file:"${properties_path}" >> "${path}"/stdout 2>&1 &
