#!/bin/sh

export JAVA_HOME=/usr/local/youdao/ad/jdk1.8.0_202
export HADOOP_CONF_DIR=/mfs_ead/home/<USER>/online/hadoop-conf
export HADOOP_PATH=/mfs_ead/global/eadata/hadoop-2.7.1/bin
export PATH=$HADOOP_CONF_DIR:$HADOOP_PATH:$PATH

version=$1
file=quipu-samza-${version}-dist.tar.gz
echo ${file}

path=$(cd "$(dirname "$0")"/../; pwd)
cd ${path}/../
mvn clean package -pl quipu-samza -am

hadoop fs -put -f ${path}/target/quipu-samza-${version}-dist.tar.gz /user/eadata/samza/

rm -rf ${path}/server
mkdir ${path}/server
cp ${path}/target/quipu-samza-${version}-dist.tar.gz ${path}/server
cd ${path}/server
tar -zxvf quipu-samza-${version}-dist.tar.gz -C ./


