# Job
#job.factory.class=org.apache.samza.job.yarn.YarnJobFactory
job.factory.class=org.apache.samza.job.local.ThreadJobFactory
job.name=merge_sdk_tdp_task_test
# Task
task.class=com.youdao.quipu.samza.tdp.MergeSdkTdpSamzaTask
task.inputs=kafkaSource.impr_sdk,kafkaSource.click_sdk_charged,kafkaSource.click_third_party,kafkaSource.conv_third_party_avro
task.consumer.batch.size=1000
job.coordinator.system=kafkaSource
# Thread pool to run synchronous tasks in parallel.
job.container.thread.pool.size=10
# Max number of outstanding messages being processed per task at a time, applicable to both StreamTask and AsyncStreamTask.
task.max.concurrency=8

# Systems
systems.kafkaSource.samza.factory=org.apache.samza.system.kafka.KafkaSystemFactory
systems.kafkaSource.samza.msg.serde=byte
systems.kafkaSource.samza.key.serde=byte
systems.kafkaSource.consumer.zookeeper.connect=eadata-zk0.corp.yodao.com:2181,eadata-zk1.corp.yodao.com:2181,eadata-zk2.corp.yodao.com:2181/kafka
systems.kafkaSource.consumer.auto.offset.reset=largest
systems.kafkaSource.producer.bootstrap.servers=ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666

systems.kafkaSink.samza.factory=org.apache.samza.system.kafka.KafkaSystemFactory
systems.kafkaSink.samza.msg.serde=byte
systems.kafkaSink.samza.key.serde=byte
systems.kafkaSink.consumer.zookeeper.connect=eadata-zk0.corp.yodao.com:2181,eadata-zk1.corp.yodao.com:2181,eadata-zk2.corp.yodao.com:2181/kafka
systems.kafkaSink.consumer.auto.offset.reset=largest
systems.kafkaSink.producer.bootstrap.servers=ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666


# Serializers
serializers.registry.byte.class=org.apache.samza.serializers.ByteSerdeFactory

##################### Metrics
# metrics指标见 https://samza.apache.org/learn/documentation/0.14/container/metrics-table.html
metrics.reporter.jmx.class=org.apache.samza.metrics.reporter.JmxReporterFactory
metrics.reporters=kafka-reportor,jmx
metrics.reporter.kafka-reportor.class=org.apache.samza.metrics.reporter.MetricsSnapshotReporterFactory
metrics.reporter.kafka-reportor.stream=kafkaSource.samza_metrics
serializers.registry.metrics-serde.class=org.apache.samza.serializers.MetricsSnapshotSerdeFactory
systems.kafkaSource.streams.samza_metrics.samza.msg.serde=metrics-serde
systems.kafkaSink.streams.samza_metrics.samza.msg.serde=metrics-serde

# YARN
yarn.package.path=hdfs://eadata-hdfs/user/eadata/samza/quipu-samza-${project.version}-dist.tar.gz
yarn.container.count=1
yarn.container.cpu.cores=5
yarn.container.memory.mb=6144
yarn.am.java.home=/usr/local/youdao/ad/jdk1.8.0_202
task.java.home=/usr/local/youdao/ad/jdk1.8.0_202
task.opts=-Xmx5g
task.checkpoint.factory=org.apache.samza.checkpoint.kafka.KafkaCheckpointManagerFactory
task.checkpoint.system=kafkaSource
task.checkpoint.replication.factor=3

# below is not samza config
## Avro codecs
avro.codec.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
avro.codec.etl.schema.registry.url=http://eadata-schema-repo.inner.youdao.com/schema-repo

# kafka Topic
task.input.topic.sdk.impr=impr_sdk
task.input.topic.sdk.click=click_sdk_charged
task.input.topic.tdp.click=click_third_party
task.input.topic.tdp.conv=conv_third_party_avro
task.input.topic.tdp.loc.conv=loc_conv_third_party_avro
task.output.topic.together=sdk_tdp_together_test
task.output.topic.loc.together=loc_conv_sdk_tdp_together_test