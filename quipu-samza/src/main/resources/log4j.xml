<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration>
    <appender name="samzaRollingAppender" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${samza.log.dir}/${samza.container.name}.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss} %c{1}:%L [%p] %m%n"/>
        </layout>
    </appender>

    <appender name="consoleAppender" class="org.apache.log4j.ConsoleAppender">
        <appender-ref ref="consoleRollingAppender"/>
    </appender>

    <appender name="consoleRollingAppender" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${samza.log.dir}/stdout.log" />
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss} %c{1}:%L [%p] %m%n"/>
        </layout>
    </appender>

    <root>
        <priority value="info"/>
        <appender-ref ref="samzaRollingAppender"/>
        <appender-ref ref="consoleAppender"/>
    </root>
</log4j:configuration>
