# Job
job.factory.class=org.apache.samza.job.local.ThreadJobFactory
job.name=avro_data_join_click_task_test_5_ligd

# Task
task.class=com.youdao.quipu.samza.DataJoinClickSamzaTask
task.inputs=kafka.click_sdk_shuffled_test,kafka.conv_sdk_shuffled_test,kafka.app_download_status_test,kafka.deep_link_invoke_status_test,kafka.sdk_req_conv_unjoined
task.window.ms=600000
task.consumer.batch.size=10000
job.coordinator.system=kafka
job.coordinator.replication.factor=1


# Systems
systems.kafka.samza.factory=org.apache.samza.system.kafka.KafkaSystemFactory
systems.kafka.samza.msg.serde=byte
systems.kafka.samza.key.serde=byte
#systems.kafka.consumer.zookeeper.connect=eadata-zk0.corp.yodao.com:2181,eadata-zk1.corp.yodao.com:2181,eadata-zk2.corp.yodao.com:2181/kafka
#systems.kafka.consumer.auto.offset.reset=smallest
#systems.kafka.producer.bootstrap.servers=ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666
systems.kafka.consumer.zookeeper.connect=ead-sandbox-kafka.inner.youdao.com:2181/kafka
systems.kafka.consumer.auto.offset.reset=largest
systems.kafka.producer.bootstrap.servers=ead-sandbox-kafka.inner.youdao.com:9092

# Serializers
serializers.registry.byte.class=org.apache.samza.serializers.ByteSerdeFactory

# YARN
job.container.count=1
yarn.container.cpu.cores=1
yarn.container.memory.mb=8192
task.opts=-Xmx5000m -Xmn500m
task.checkpoint.factory=org.apache.samza.checkpoint.kafka.KafkaCheckpointManagerFactory
task.checkpoint.system=kafka
task.checkpoint.replication.factor=1

# below is not samza config
## Avro codecs
#avro.codec.encoder.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
#avro.codec.encoder.etl.schema.registry.url=http://eadata-schema-repo.inner.youdao.com/schema-repo
#avro.codec.decoder.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
#avro.codec.decoder.etl.schema.registry.url=http://eadata-schema-repo.inner.youdao.com/schema-repo
avro.codec.encoder.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
avro.codec.encoder.etl.schema.registry.url=http://sandbox-schema-repo.inner.youdao.com/schema-repo
avro.codec.decoder.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
avro.codec.decoder.etl.schema.registry.url=http://sandbox-schema-repo.inner.youdao.com/schema-repo
# Click Topic
task.input.topic.click=click_sdk_shuffled_test

hbase.table.click=ClickTest
# Join service
conv.join.service.input.topic=conv_sdk_shuffled_test
conv.join.service.output.topic=conv_sdk_joined_test_2
conv.join.service.output.conv_click_time.topic=conv_sdk_joined_click_time_test
conv.join.service.hbase.table.name=ConvUnjoinedTest
conv.join.service.redis.key.prefix=ConvUnjoinedTest
conv.join.service.timeout=3600000
conv.join.service.expired.data.send=true
conv.join.service.third.party.conv.types=td, umeng, cd, tune, install, reyun, AF, SD, GIO

req-id.conv.join.service.input.topic=sdk_req_conv_unjoined
req-id.conv.join.service.output.topic=conv_sdk_joined
req-id.conv.join.service.hbase.table.name=SdkReqIdConvUnjoined
req-id.conv.join.service.redis.key.prefix=SdkReqIdConvUnjoined
req-id.conv.join.service.timeout=600000
req-id.conv.join.service.expired.data.send=true

app.download.status.join.service.input.topic=app_download_status_test
app.download.status.join.service.output.topic=app_download_status_joined_test
app.download.status.join.service.hbase.table.name=AppDownloadStatusTest
app.download.status.join.service.redis.key.prefix=AppDownloadStatusTest
app.download.status.join.service.timeout=3600000
app.download.status.join.service.expired.data.send=false

deep.link.invoke.status.join.service.input.topic=deep_link_invoke_status_test
deep.link.invoke.status.join.service.output.topic=deep_link_invoke_status_joined_test
deep.link.invoke.status.join.service.hbase.table.name=DeepLinkInvokeStatusTest
deep.link.invoke.status.join.service.redis.key.prefix=DeepLinkInvokeStatusTest
deep.link.invoke.status.join.service.timeout=3600000
deep.link.invoke.status.join.service.expired.data.send=false

redis.host=4ff6b8.redis.baichuan.ynode.cn
redis.port=6379
redis.password=8XSaytOn
redis.db.number=11