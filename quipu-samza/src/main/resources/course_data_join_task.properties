# Job
job.factory.class=org.apache.samza.job.yarn.YarnJobFactory
job.name=course_order_click_join_task

# Task
task.class=com.youdao.quipu.samza.CourseOrderClickJoinTask
task.inputs=kafka.course_order,kafka.course_click
task.window.ms=600000
job.coordinator.system=kafka

# Systems
systems.kafka.samza.factory=org.apache.samza.system.kafka.KafkaSystemFactory
systems.kafka.samza.msg.serde=byte
systems.kafka.samza.key.serde=byte
systems.kafka.consumer.zookeeper.connect=eadata-zk0.corp.yodao.com:2181,eadata-zk1.corp.yodao.com:2181,eadata-zk2.corp.yodao.com:2181/kafka
systems.kafka.consumer.auto.offset.reset=smallest
systems.kafka.producer.bootstrap.servers=ad-prod-01.kafka.yodao.cn:6666,ad-prod-02.kafka.yodao.cn:6666,ad-prod-03.kafka.yodao.cn:6666

# Serializers
serializers.registry.byte.class=org.apache.samza.serializers.ByteSerdeFactory

##################### Metrics
metrics.reporter.jmx.class=org.apache.samza.metrics.reporter.JmxReporterFactory
metrics.reporters=kafka-reportor,jmx
metrics.reporter.kafka-reportor.class=org.apache.samza.metrics.reporter.MetricsSnapshotReporterFactory
metrics.reporter.kafka-reportor.stream=kafka.samza_metrics
serializers.registry.metrics-serde.class=org.apache.samza.serializers.MetricsSnapshotSerdeFactory
systems.kafka.streams.samza_metrics.samza.msg.serde=metrics-serde

# YARN
yarn.package.path=hdfs://eadata-hdfs/user/eadata/samza/quipu-samza-${project.version}-dist.tar.gz
yarn.container.count=1
yarn.container.cpu.cores=1
yarn.container.memory.mb=4096
yarn.am.java.home=/disk2/eadata/jdk1.8.0_40
task.java.home=/disk2/eadata/jdk1.8.0_40
task.opts=-Xmx5000m -Xmn500m
task.checkpoint.factory=org.apache.samza.checkpoint.kafka.KafkaCheckpointManagerFactory
task.checkpoint.system=kafka
task.checkpoint.replication.factor=3

# below is not samza config
## Avro codecs
avro.codec.encoder.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
avro.codec.encoder.etl.schema.registry.url=http://eadata-schema-repo.inner.youdao.com/schema-repo
avro.codec.decoder.kafka.message.coder.schema.registry.class=com.linkedin.camus.schemaregistry.AvroRestSchemaRegistry
avro.codec.decoder.etl.schema.registry.url=http://eadata-schema-repo.inner.youdao.com/schema-repo

topic.course.click=course_click
topic.course.order=course_order
topic.course.order.joined=course_order_joined

hbase.table.course.click=course_click
hbase.table.course.order.unjoined=course_order_unjoined

redis.host=59c9f9.redis.baichuan.ynode.cn
redis.port=6379
redis.password=VUNADGYt
redis.db.number=0
