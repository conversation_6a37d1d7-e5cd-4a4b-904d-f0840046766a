package com.youdao.quipu.samza.tdp;

import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageEncoder;
import com.linkedin.camus.schemaregistry.CachedSchemaRegistry;
import com.linkedin.camus.schemaregistry.SchemaRegistry;
import com.youdao.quipu.avro.schema.SdkTdpTogether;
import com.youdao.quipu.samza.KafkaAvroCodecs;
import com.youdao.quipu.samza.tdp.adapter.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.samza.config.Config;
import org.apache.samza.metrics.Timer;
import org.apache.samza.system.IncomingMessageEnvelope;
import org.apache.samza.system.OutgoingMessageEnvelope;
import org.apache.samza.system.SystemStream;
import org.apache.samza.task.*;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/8/21
 */
@Slf4j
public class MergeSdkTdpSamzaTask extends KafkaAvroCodecs implements StreamTask, InitableTask {

    private String topicSdkImpr;
    private String topicSdkClick;
    private String topicTdpClick;
    private String topicTdpConv;
    private String topicTdpLocConv;
    private String topicTogether;
    private String topicLocTogether;

    private SdkImprAdapter sdkImprAdapter;
    private SdkClickAdapter sdkClickAdapter;
    private TdpClickAdapter tdpClickAdapter;
    private TdpConvAdapter tdpConvAdapter;
    private TdpLocConvAdapter tdpLocConvAdapter;


    @Override
    public void init(Config config, TaskContext context) throws Exception {

        topicSdkImpr = config.get("task.input.topic.sdk.impr");
        topicSdkClick = config.get("task.input.topic.sdk.click");
        topicTdpClick = config.get("task.input.topic.tdp.click");
        topicTdpConv = config.get("task.input.topic.tdp.conv");
        topicTdpLocConv = config.get("task.input.topic.tdp.loc.conv");
        topicTogether = config.get("task.output.topic.together");
        topicLocTogether = config.get("task.output.topic.loc.together");

        Properties properties = new Properties();
        properties.putAll(config.subset("avro.codec."));
        super.decoderProperties = properties;
        super.encoderProperties = properties;

        SchemaRegistry<Schema> registry = (SchemaRegistry<Schema>) Class.forName(
                        properties.getProperty(KafkaAvroMessageEncoder.KAFKA_MESSAGE_CODER_SCHEMA_REGISTRY_CLASS)).newInstance();
        registry.init(properties);
        CachedSchemaRegistry<Schema> cachedSchemaRegistry = new CachedSchemaRegistry<>(registry);
        sdkImprAdapter = new SdkImprAdapter(cachedSchemaRegistry);
        sdkClickAdapter = new SdkClickAdapter(cachedSchemaRegistry);
        tdpClickAdapter = new TdpClickAdapter(cachedSchemaRegistry);
        tdpConvAdapter = new TdpConvAdapter(cachedSchemaRegistry);
        tdpLocConvAdapter = new TdpLocConvAdapter(cachedSchemaRegistry);
    }

    @Override
    public void process(IncomingMessageEnvelope envelope, MessageCollector collector, TaskCoordinator coordinator) throws Exception {
        String msgTopic = null;
        SdkTdpTogether sdkTdpTogether = null;
        String topic = topicTogether;
        try {
            msgTopic = envelope.getSystemStreamPartition().getSystemStream().getStream();
            byte[] message = (byte[]) envelope.getMessage();
            if (topicSdkImpr.equals(msgTopic)) {
                sdkTdpTogether = sdkImprAdapter.deserializeAndMapping(msgTopic, message);
            } else if (topicSdkClick.equals(msgTopic)) {
                sdkTdpTogether = sdkClickAdapter.deserializeAndMapping(msgTopic, message);
            } else if (topicTdpClick.equals(msgTopic)) {
                sdkTdpTogether = tdpClickAdapter.deserializeAndMapping(msgTopic, message);
            } else if (topicTdpConv.equals(msgTopic)) {
                sdkTdpTogether = tdpConvAdapter.deserializeAndMapping(msgTopic, message);
            } else if (topicTdpLocConv.equals(msgTopic)) {
                sdkTdpTogether = tdpLocConvAdapter.deserializeAndMapping(msgTopic, message);
                topic = topicLocTogether;
            } else{
                log.error("Unknown topic: {}", msgTopic);
            }
            if (sdkTdpTogether != null) {
                collector.send(new OutgoingMessageEnvelope(
                        new SystemStream("kafkaSink", topic),
                        getEncoder(topic).toBytes(sdkTdpTogether)
                ));
            }
        } catch (Exception e) {
            String msg = sdkTdpTogether == null ? "deserializeAndMapping failed" : sdkTdpTogether.toString();
            log.error("Msg process error, topic = {}， partition = {}, offset= {}, msg = {}",
                    msgTopic, envelope.getSystemStreamPartition().getPartition().getPartitionId(), envelope.getOffset(), msg, e);
        }
    }

}
