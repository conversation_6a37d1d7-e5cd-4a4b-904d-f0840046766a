package com.youdao.quipu.samza.tdp.adapter;

import com.linkedin.camus.schemaregistry.CachedSchemaRegistry;
import com.youdao.quipu.avro.schema.ConvStrategyAlgRetrievers;
import com.youdao.quipu.avro.schema.SdkTdpTogether;
import com.youdao.quipu.avro.schema.ThirdPartyClick;
import com.youdao.quipu.samza.tdp.adapter.base.AbstractMessageAdapter;
import com.youdao.quipu.samza.tdp.adapter.base.ExtractUtils;
import org.apache.avro.Schema;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/21
 */
public class TdpClickAdapter extends AbstractMessageAdapter<ThirdPartyClick, SdkTdpTogether> {
    public TdpClickAdapter(CachedSchemaRegistry<Schema> registry) {
        super(registry);
    }

    @Override
    protected Schema getClassSchema() {
        return ThirdPartyClick.getClassSchema();
    }

    @Override
    protected SdkTdpTogether recordMapping(ThirdPartyClick record) {
        String channelIdStr = record.getChannelId().toString();
        CharSequence slotId = ExtractUtils.extractSlotId(channelIdStr);
        CharSequence channelIdPrefix = ExtractUtils.extractChannelIdPrefix(channelIdStr);
        List<CharSequence> campId = ExtractUtils.extractCampId(channelIdStr);
        CharSequence identify = ExtractUtils.extractIdentify(channelIdStr);
        SdkTdpTogether.Builder builder = SdkTdpTogether.newBuilder();
        builder.setTimestamp(record.getTimestamp())
                .setGuid(record.getGuid() + "|6")
                .setOs(record.getOs())
                // ThirdPartyClick的appid是极少数广告主要上报的appid，为了满足广告主需求而后台配置无任何含义。而且99.99%的上报日志都没有值，
                // 我们这里不需要上报的appid，所以这里仅使用转化加速器的appid，这个有真实含义方便统计分析
                .setAppId(record.getConvStrategyAppId())
                .setSlotId(slotId)
                .setSponsorId(record.getSponsorId())
                .setCampaignId(record.getCampaignId())
                .setGroupId(record.getGroupId())
                .setVariationId(record.getVariationId())
                .setActivityId(record.getActivityId())
                .setChannelDid(record.getChannelDid())
                .setChannelId(record.getChannelId())
                .setChannelIdPrefix(channelIdPrefix)
                .setReyunCampId(campId)
                .setIdentify(identify)
                .setFromYdbc(StringUtils.isNotBlank(channelIdPrefix))
                .setMediaPkgId(record.getMediaPkgId())
                .setAlgBucketId(algBucketId(record))
                .setRetriever(retrievers(record.getRetrievers()))
                .setRetrieveAlgId(record.getRetrieveAlgId())
                .setProductId(record.getProductId())
                .setTdpClickDeviceId(record.getClickDeviceId())
                .setTdpClick(1);
        return builder.build();
    }

    private int algBucketId(ThirdPartyClick record) {
        if (record.getAlgBucketId() == null) {
            return -1;
        }
        return record.getAlgBucketId();
    }

    private List<CharSequence> retrievers(List<ConvStrategyAlgRetrievers> retrievers) {
        List<CharSequence> res = new ArrayList<>();
        if (retrievers != null) {
            for (ConvStrategyAlgRetrievers retrievers1 : retrievers) {
                res.add(retrievers1.getRetriever());
            }
        }
        return res;
    }
}
