package com.youdao.quipu.samza.join;

import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.IndexedRecord;
import org.apache.hadoop.hbase.TableName;

import java.util.List;

/**
 * Join数据服务接口
 */
public interface JoinService {
    String SEPARATOR = "-";
    String BID_ID = "bid_id";
    String SPONSOR_ID = "sponsor_id";

    /**
     * 需要JOIN的数据存放的topic
     * @return topicName
     */
    String getInputTopic();
    /**
     * JOIN后的数据或者超时的数据发往的topic
     * @return 数据join或者超时后应该发往的topic
     */
    String getOutputTopic();
    /**
     * 超时未JOIN点击的数据发往的topic
     * 如果{@link #expiredDataNeedSend()} 返回是false，这个函数无作用
     * @return 超时未JOIN点击的数据发往的topic
     */
    String getExpiredOutputTopic();
    /**
     * JOIN不上的非点击数据暂存的HBase的TableName
     * @return 非点击数据对应的Hbase的TableName
     */
    TableName getTableName();
    String getRedisKeyPrefix();
    /**
     * 数据无法被join等待的最长时间，即超时时间
     * @return 单位：ms
     */
    long getTimeout();
    /**
     * 超时数据是否需要发到kafka上，
     * 发往的topic由{@link #getOutputTopic()} 决定
     * @return true，超时的数据发到kafka上；false，超时的数据不发到kafka上
     */
    boolean expiredDataNeedSend();
    /**
     * 通过需要被join的数据得到对应的点击数据的rowKey
     * @param data 需要被join的数据
     * @return 对应的点击数据的rowKey
     * @throws Exception when get error
     */
    byte[] getClkRowKeyByData(GenericData.Record data) throws Exception;
    /**
     * 通过点击数据得到对应的待JOIN数据的rowKey
     * 一条点击可能需要join多条数据
     * @param clk 点击数据
     * @return 对应的待JOIN数据的rowKey的list
     * @throws Exception when get error
     */
    List<byte[]> getDataRowKeysByClk(GenericData.Record clk) throws Exception;
    List<String> getDataRedisKeyByClk(GenericData.Record clk) throws Exception;
    /**
     * 根据待JOIN数据得到待JOIN数据存储在Hbase上的rowKey
     * 结果应与{@link #getDataRowKeysByClk(GenericData.Record)} 一致
     * 因为当对应的点击到来时，会调用{@link #getDataRowKeysByClk(GenericData.Record)} 来查找对应的未JOIN上的点击数据
     * 如果不一致，会导致点击永远找不到对应的待JOIN数据
     * @param data 待JOIN数据
     * @return 待JOIN数据将要存储在Hbase上的rowKey
     * @throws Exception when get error
     */
    byte[] getDataRowKeyByData(GenericData.Record data) throws Exception;

    String getRedisKeyByData(GenericData.Record data) throws Exception;
    /**
     * 数据JOIN
     * @param clk 点击数据
     * @param data 待JOIN数据
     * @return join后的数据，必须是avro格式
     * @throws Exception when join failed
     */
    IndexedRecord join(GenericData.Record clk, GenericData.Record data) throws Exception;
    /**
     * 当{@link #expiredDataNeedSend()} 为true时，执行此函数
     * 此函数的调用结果会发到Kafka的{@link #getExpiredOutputTopic()}上
     * @param data 待发送的过期数据
     * @return 处理过后的过期数据，必须是avro格式
     * @throws Exception 处理过程中出现异常，忽略此次处理。下一次定时任务还会继续处理
     */
    IndexedRecord processExpiredData(GenericData.Record data) throws Exception;
}