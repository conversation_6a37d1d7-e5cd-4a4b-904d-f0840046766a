package com.youdao.quipu.samza.hbase;

import lombok.extern.slf4j.Slf4j;
import org.apache.curator.utils.CloseableUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.hadoop.hbase.util.PoolMap;

import java.io.File;
import java.util.NavigableMap;

import static org.apache.hadoop.hbase.HConstants.DEFAULT_HBASE_RPC_TIMEOUT;

@Slf4j
public class HbaseService {
    public static final byte[] HBASE_COLUMN_FAMILY_ALL = Bytes.toBytes("All");
    public static final byte[] HBASE_COLUMN_DATA = Bytes.toBytes("Data");
    public static final byte[] HBASE_COLUMN_TOPIC = Bytes.toBytes("Topic");
    private static final String HBASE_CONF_LOCATION = "/mfs_ead/eadata/online/hadoop-conf/hbase-site.xml";
    private static final String HDFS_CONF_LOCATION = "/mfs_ead/eadata/online/hadoop-conf/hdfs-site.xml";

    private static volatile Connection hBaseConnection = null;

    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            CloseableUtils.closeQuietly(hBaseConnection);
            log.info("Stop success.");
        }));


        File hbaseConf = new File(HBASE_CONF_LOCATION);
        File hdfsConf = new File(HDFS_CONF_LOCATION);
        if (!(hbaseConf.exists() && hdfsConf.exists())) {
            log.error("Hbase conf not exist!");
            System.exit(1);
        }
    }

    private static Connection getHbaseConnection() throws Exception {
        try {
            if (null == hBaseConnection) {
                synchronized (HbaseService.class) {
                    if (null == hBaseConnection) {
                        Configuration configuration = HBaseConfiguration.create();
                        configuration.addResource(new Path(HBASE_CONF_LOCATION));
                        configuration.addResource(new Path(HDFS_CONF_LOCATION));

                        configuration.setInt(HConstants.HBASE_RPC_TIMEOUT_KEY, DEFAULT_HBASE_RPC_TIMEOUT);
                        // 读写都不应该失败，需要一直重试下去
                        // 目前会导致不会继续消费 Kafka 中的数据，但可以获得不丢失数据。
                        // TODO 线上设置为 Int.MAX 后某些 YARN 节点写 HBase 特别慢，所以目前设置为6小时后放弃写入，但些时报警应该已经触发。
                        configuration.setInt(HConstants.HBASE_CLIENT_RETRIES_NUMBER, 60 * 6);
                        // 加大到每个 RS 的连接数
                        configuration.set(HConstants.HBASE_CLIENT_IPC_POOL_TYPE, PoolMap.PoolType.RoundRobin.name());
                        configuration.setInt(HConstants.HBASE_CLIENT_IPC_POOL_SIZE, 10);

                        hBaseConnection = ConnectionFactory.createConnection(configuration);
                        log.info("Init hbase connection success.");
                    }
                }
            }
            return hBaseConnection;
        } catch (Exception e) {
            log.error("Init hbase connection instance failed.");
            throw e;
        }
    }

    public static Table getTable(String hbaseTableName) throws Exception {
        return getHbaseConnection().getTable(TableName.valueOf(hbaseTableName));
    }

    public static Table getTable(TableName tableName) throws Exception {
        return getHbaseConnection().getTable(tableName);
    }

    public static NavigableMap<byte[], NavigableMap<byte[], NavigableMap<Long, byte[]>>> getNavigableMap(Table table, byte[] rowKey) throws Exception {
        Get get = new Get(rowKey);
        Result result = table.get(get);
        if (result.isEmpty()) {
            return null;
        }

        return result.getMap();
    }

    public static byte[] getDataFromHbase(Table table, byte[] rowKey) throws Exception {
        NavigableMap<byte[], NavigableMap<byte[], NavigableMap<Long, byte[]>>> navigableMap = getNavigableMap(table, rowKey);
        if (null == navigableMap) {
            return null;
        } else {
            return navigableMap.get(HBASE_COLUMN_FAMILY_ALL)
                    .get(HBASE_COLUMN_DATA)
                    .pollLastEntry()
                    .getValue();
        }
    }
}
