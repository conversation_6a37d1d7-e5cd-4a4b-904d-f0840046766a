package com.youdao.quipu.samza.tdp.adapter;

import com.linkedin.camus.schemaregistry.CachedSchemaRegistry;
import com.youdao.quipu.avro.schema.SdkClick;
import com.youdao.quipu.avro.schema.SdkTdpTogether;
import com.youdao.quipu.samza.tdp.adapter.base.AbstractMessageAdapter;
import org.apache.avro.Schema;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/8/21
 */
public class SdkClickAdapter extends AbstractMessageAdapter<SdkClick, SdkTdpTogether> {
    public SdkClickAdapter(CachedSchemaRegistry<Schema> registry) {
        super(registry);
    }

    @Override
    protected Schema getClassSchema() {
        return SdkClick.getClassSchema();
    }

    @Override
    protected SdkTdpTogether recordMapping(SdkClick record) {
        SdkTdpTogether.Builder builder = SdkTdpTogether.newBuilder();
        builder.setTimestamp(record.getTimestamp())
                .setGuid(record.getGuid() + "|4")
                .setOs(record.getOs())
                .setAppId(record.getAppId())
                .setSlotId(record.getSlotId())
                .setSponsorId(record.getSponsorId())
                .setCampaignId(record.getCampaignId())
                .setGroupId(record.getGroupId())
                .setVariationId(record.getVariationId())
                .setCharge(record.getCharge())
                .setSdkClick(1);
        return builder.build();
    }
}
