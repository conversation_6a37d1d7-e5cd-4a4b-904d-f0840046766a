package com.youdao.quipu.samza;

import com.youdao.quipu.avro.schema.ThirdPartyConv;
import com.youdao.quipu.samza.join.JoinService;
import com.youdao.quipu.samza.join.ThirdReqIdConvJoinServiceImpl;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.IndexedRecord;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.util.Pair;
import org.apache.samza.config.Config;
import org.apache.samza.task.MessageCollector;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static com.youdao.quipu.avro.examples.StringToAvro.VARIATION_ID;
import static com.youdao.quipu.avro.examples.StringToAvro._long;
import static com.youdao.quipu.samza.join.JoinService.SEPARATOR;

/**
 * <AUTHOR>
 * @date 2024/10/6.
 */
@Slf4j
public class ThirdReqIdConvJoinTask extends DataJoinClickSamzaTask {

    private static final List<Integer> REQ_ID_CONV_CT_TYPES = Arrays.asList(5, 6, 7);

    @Override
    protected void initJoinServices(Config config) {
        joinServices = new ArrayList<>();
        joinServices.add(new ThirdReqIdConvJoinServiceImpl(config));
    }

    @Override
    protected boolean judgeClickJoin(GenericData.Record click) {
        int ctType = Integer.parseInt(click.get("ct_type").toString());
        String reqId = click.get("req_id").toString();

        return REQ_ID_CONV_CT_TYPES.contains(ctType) && StringUtils.isNotBlank(reqId);
    }

    @Override
    protected List<Put> generateClkPuts(@NonNull List<Pair<GenericData.Record, byte[]>> clks) {
        List<Put> puts = new ArrayList<>(clks.size() * 3);
        for (Pair<GenericData.Record, byte[]> pair : clks) {
            GenericData.Record clk = pair.getFirst();
            byte[] clkBytes = pair.getSecond();
            try {
                String reqId = clk.get("req_id").toString();
                String variationId = clk.get(VARIATION_ID).toString();

                String rowKey1 = reqId + SEPARATOR + variationId;
                puts.add(generateClkPut(rowKey1, clkBytes));
            } catch (Exception e) {
                log.error("Generate clk hbase put error, ignored, clk = {}", clk, e);
            }
        }
        return puts;
    }

    /**
     * 待JOIN数据查找点击数据，JOIN后发送到Kafka
     * 若不存在对应的点击数据，则把它暂时存入Redis
     *
     * @param message     待JOIN数据对应的byte数组，解码后与data相等
     * @param data        待JOIN数据
     * @param joinService 待JOIN数据对应的service
     */
    @Override
    protected void dataJoinClkAndSend(MessageCollector collector, byte[] message, GenericData.Record data, JoinService joinService) {
        try {
            byte[] dataRowKey = joinService.getClkRowKeyByData(data);

            GenericData.Record clk = getClickFromHbase(dataRowKey, this);
            if (null != clk) {
                IndexedRecord joinedRecord = joinService.join(clk, data);
                sendData(collector, joinedRecord, dataRowKey, joinService.getOutputTopic());

                if (joinedRecord instanceof ThirdPartyConv) {
                    ThirdPartyConv localConv = convert2LocalConv(clk, (ThirdPartyConv) joinedRecord);
                    sendData(collector, localConv, dataRowKey, topicConvClickTime);
                }
            } else {
                writeUnjoinedDataToRedis(message, data, joinService);
            }
        } catch (Exception e) {
            log.error("Data join clk error, data = {}, joinService name = {}", data.toString(), joinService.getClass().getName(), e);
            writeUnjoinedDataToRedis(message, data, joinService);
        }
    }


    @Override
    protected void clkJoinDataByKey(MessageCollector collector, GenericData.Record click, JoinService joinService, String redisKey) {
        Set<byte[]> converts = redisClient.getRedisTemplate().opsForSet().members(redisKey);
        if (CollectionUtils.isEmpty(converts)) {
            log.info("click: {} not found matched converters", redisKey);
            return;
        }
        List<byte[]> convertsListJoined = new ArrayList<>();
        for (byte[] convert : converts) {
            try {
                IndexedRecord joined = joinService.join(click, getDecoder(joinService.getInputTopic()).decode(convert).getRecord());
                sendData(collector, joined, redisKey.getBytes(StandardCharsets.UTF_8), joinService.getOutputTopic());
                log.info("convert {} join click success.", joined);
                if (joined instanceof ThirdPartyConv) {
                    ThirdPartyConv thirdPartyConv = convert2LocalConv(click, (ThirdPartyConv) joined);
                    sendData(collector, thirdPartyConv, redisKey.getBytes(StandardCharsets.UTF_8), topicConvClickTime);
                    log.info("send third-party convert success, convert: {}", thirdPartyConv);
                }
                convertsListJoined.add(convert);
            } catch (Exception e) {
                log.error("failed to process {}, topic: {}", redisKey, joinService.getInputTopic(), e);
            }
        }
        if (CollectionUtils.isNotEmpty(convertsListJoined)) {
            redisClient.getRedisTemplate().opsForSet().remove(redisKey, convertsListJoined.toArray());
        }
    }


    private ThirdPartyConv convert2LocalConv(GenericData.Record clk, ThirdPartyConv conv) {
        return ThirdPartyConv.newBuilder(conv)
                // 当日转化取点击时间，区别于转化，转化使用收到转化的时间
                .setTimestamp(_long(clk.get("timestamp")))
                .setConv(0)
                .setLocConv(1)
                .setConvCost(0)
                .setLocConvCost(conv.getConvCost())
                .setLocOrderAmount(conv.getOrderAmount())
                .setOrderAmount(0)
                .build();
    }

}
