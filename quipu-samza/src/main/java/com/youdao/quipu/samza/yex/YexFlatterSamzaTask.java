package com.youdao.quipu.samza.yex;

import com.google.common.base.Charsets;
import com.linkedin.camus.etl.kafka.coders.KafkaAvroMessageEncoder;
import com.linkedin.camus.schemaregistry.CachedSchemaRegistry;
import com.linkedin.camus.schemaregistry.SchemaRegistry;
import com.youdao.quipu.avro.schema.GorgonInterfaceType;
import com.youdao.quipu.avro.schema.yex.*;
import com.youdao.quipu.samza.KafkaAvroCodecs;
import com.youdao.quipu.samza.KafkaAvroCoderException;
import lombok.Data;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.avro.Schema;
import org.apache.avro.generic.IndexedRecord;
import org.apache.avro.io.DecoderFactory;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.commons.lang3.StringUtils;
import org.apache.samza.config.Config;
import org.apache.samza.metrics.Counter;
import org.apache.samza.metrics.Timer;
import org.apache.samza.system.IncomingMessageEnvelope;
import org.apache.samza.system.OutgoingMessageEnvelope;
import org.apache.samza.system.SystemStream;
import org.apache.samza.task.*;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
@CommonsLog
public class YexFlatterSamzaTask extends KafkaAvroCodecs implements StreamTask, InitableTask {

    private static final String DSP_ID_ZHIXUAN = "1";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private Timer timestampLag;
    private DecoderFactory decoderFactory = DecoderFactory.get();
    private MessageDigest md5;
    private String yexImpLogTopic;
    private String yexBidRequestLogTopic;
    private String yexBidResponseLogTopic;
    private String yexSeatBidLogTopic;
    private String yexBidLogTopic;
    private String yexAssetLogTopic;
    private String yexMaterialLogTopic;
    private Counter inputCounter;
    private Counter errorCounter;
    private CachedSchemaRegistry<Schema> registry;
    private Map<String, SpecificDatumReader<YexLogJar>> readerCache = new HashMap<>();

    @Override
    public void init(Config config, TaskContext context) throws Exception {

        md5 = MessageDigest.getInstance("MD5");

        yexBidRequestLogTopic = config.get("yex.topic.bid_request_log");
        yexImpLogTopic = config.get("yex.topic.imp_log");
        yexBidResponseLogTopic = config.get("yex.topic.bid_response_log");
        yexSeatBidLogTopic = config.get("yex.topic.seat_bid_log");
        yexBidLogTopic = config.get("yex.topic.bid_log");
        yexAssetLogTopic = config.get("yex.topic.asset_log");
        yexMaterialLogTopic = config.get("yex.topic.material_log");

        timestampLag = context.getMetricsRegistry().newTimer("samza.tasks.yex_flatter", "timestamp.lag");
        inputCounter = context.getMetricsRegistry().newCounter("samza.tasks.yex_flatter", "input.counter");
        errorCounter = context.getMetricsRegistry().newCounter("samza.tasks.yex_flatter", "error.counter");

        Properties properties = new Properties();
        properties.putAll(config.subset("avro.codec."));
        super.decoderProperties = properties;
        super.encoderProperties = properties;

        SchemaRegistry<Schema> registry =
                (SchemaRegistry<Schema>) Class.forName(
                        properties.getProperty(KafkaAvroMessageEncoder.KAFKA_MESSAGE_CODER_SCHEMA_REGISTRY_CLASS)).newInstance();
        registry.init(properties);
        this.registry = new CachedSchemaRegistry<Schema>(registry);
    }

    @Override
    public void process(IncomingMessageEnvelope envelope, MessageCollector collector, TaskCoordinator coordinator) throws Exception {
        inputCounter.inc();
        try {
            byte[] bytes = (byte[]) envelope.getMessage();
            YexLogJar yexLogJar = getReader(envelope.getSystemStreamPartition().getStream(), bytes)
                    .read(null, decoderFactory.binaryDecoder(bytes, 5, bytes.length - 5, null));
            long timestamp = yexLogJar.getTimestamp();
            timestampLag.update(System.currentTimeMillis() - timestamp);
            YexBidRequest yexBidRequest = yexLogJar.getBidRequest();
            CharSequence venderId = yexLogJar.getVenderId();
            CharSequence venderSource = yexLogJar.getVenderSource();
            int brandOrientationType = yexLogJar.getBrandOrientationType();
            long adGroupId = yexLogJar.getAdGroupId();
            boolean supplyDeviceFlag = yexLogJar.getSupplyDeviceFlag();
            CharSequence guid = yexLogJar.getGuid();
            String bidRequestId = yexBidRequest.getBidRequestId().toString();
            List<YexImp> imps = yexBidRequest.getImp();
            Object distributionChannel = yexBidRequest.getDistributionChannel();
            DistributionChannelType dcType = DistributionChannelType.UNKNOWN;
            CharSequence dcId = "";
            CharSequence slotId = "";
            CharSequence bundle = "";
            CharSequence brandSourceSlotId = "";
            Map<CharSequence, Integer> impId2SsidMap = new HashMap<>();
            if (distributionChannel instanceof YexApp) {
                dcType = DistributionChannelType.APP;
                dcId = ((YexApp) distributionChannel).getId();
                bundle = ((YexApp) distributionChannel).getBundle();
            } else if (distributionChannel instanceof YexSite) {
                dcType = DistributionChannelType.SITE;
                dcId = ((YexSite) distributionChannel).getId();
            }

            YexDevice device = yexLogJar.getBidRequest().getDevice();
            int connectionType = 0;
            CharSequence os = "";
            CharSequence appVersion = "";
            if (device != null) {
                connectionType = device.getConnectionType();
                os = device.getOs();
                appVersion = device.getAppVersion();
            }

            ImpType impType = ImpType.UNKNOWN;
            int adRequestCountSum = 0;
            for (YexImp yexImp : imps) {
                Integer ssid = yexImp.getSsid();
                impId2SsidMap.put(yexImp.getId(), ssid);
                int adRequestCount = 0;
                if (yexImp.getBanner() != null) {
                    adRequestCount = 1;
                    impType = ImpType.BANNER;
                } else if (yexImp.getNative$() != null) {
                    adRequestCount = yexImp.getNative$().getPlcmtcnt();
                    // TODO: assume only 1 imp_type in 1 BidRequest
                    impType = ImpType.NATIVE;
                    // TODO: assume only 1 slot_id in 1 BidRequest
                    slotId = yexImp.getTagId();
                    brandSourceSlotId = yexImp.getBrandSourceSlotId();
                }
                adRequestCountSum += adRequestCount;
                YexImpLog yexImpLog = YexImpLog.newBuilder()
                        .setAdRequestCount(adRequestCount)
                        .setYexImp(yexImp)
                        .setImpType(impType)
                        .setTimestamp(timestamp)
                        .setGuid(md5String(guid, yexImp.getId()))
                        .setBidRequestId(bidRequestId)
                        .setDcType(dcType)
                        .setDcId(dcId)
                        .setReqBundle(bundle)
                        .setSlotId(yexImp.getTagId())
                        .setBrandSourceSlotId(brandSourceSlotId)
                        .setOs(os)
                        .setConnectionType(connectionType)
                        .setSsid(ssid)
                        .setVenderId(venderId)
                        .setVenderSource(venderSource)
                        .setSupplyDeviceFlag(supplyDeviceFlag)
                        .setDealId(yexImp.getDealId())
                        .setAppVersion(appVersion)
                        .setBrandOrientationType(brandOrientationType)
                        .setAdGroupId(adGroupId)
                        .build();
                send(collector, yexImpLogTopic, yexImpLog);
            }


            YexBidRequestLog.Builder yexBidRequestLogBuilder = YexBidRequestLog.newBuilder()
                    .setTimestamp(timestamp)
                    .setGuid(guid)
                    .setBidRequest(yexBidRequest)
                    .setImpType(impType)
                    .setDcType(dcType)
                    .setDcId(dcId)
                    .setReqBundle(bundle)
                    .setSlotId(slotId)
                    .setBrandSourceSlotId(brandSourceSlotId)
                    .setHostname(yexLogJar.getHostname())
                    .setGorgonHostname(yexLogJar.getGorgonHostname())
                    .setOs(os)
                    .setConnectionType(connectionType)
                    .setVenderId(venderId)
                    .setVenderSource(venderSource)
                    .setSupplyDeviceFlag(supplyDeviceFlag)
                    .setIvtTags(yexLogJar.getIvtTags())
                    .setIvtRequestCount(yexLogJar.getIvtCount())
                    .setCountryName(yexLogJar.getCountryName())
                    .setProvince(yexLogJar.getProvince())
                    .setCity(yexLogJar.getCity())
                    .setConnectionTypeFilled(connectionType > 0 ? 1 : 0)
                    .setAppBundleIdFilled(StringUtils.isNotBlank(bundle) ? 1 : 0)
                    .setAppVersion(appVersion)
                    .setBrandOrientationType(brandOrientationType)
                    .setAdGroupId(adGroupId)
                    .setGorgonInterfaceType(yexLogJar.getGorgonInterfaceType());
            if (StringUtils.isNotEmpty(yexLogJar.getCountryName()) && "CN".equals(yexLogJar.getCountryName().toString())) {
                yexBidRequestLogBuilder
                        .setProvinceName(yexLogJar.getProvinceName())
                        .setCityName(yexLogJar.getCityName());
            }
            if (device != null) {
                yexBidRequestLogBuilder
                        .setUaFilled(StringUtils.isNotBlank(device.getUa()) ? 1 : 0)
                        .setCarrierFilled(StringUtils.isNotBlank(device.getCarrier()) && !"未知".contentEquals(device.getCarrier()) ? 1 : 0)
                        .setOsVersionFilled(StringUtils.isNotBlank(device.getOsVersion()) ? 1 : 0)
                        .setModelFilled(StringUtils.isNotBlank(device.getModel()) ? 1 : 0)
                        .setManufacturerFilled(StringUtils.isNotBlank(device.getManufacturer()) ? 1 : 0)
                        .setScreenWidthFilled(device.getScreenWidth() > 0 ? 1 : 0)
                        .setScreenHeightFilled(device.getScreenHeight() > 0 ? 1 : 0)
                        .setImeiFilled(StringUtils.isNotBlank(device.getImeiMd5()) ? 1 : 0)
                        .setOaidFilled(StringUtils.isNotBlank(device.getOaid()) ? 1 : 0)
                        .setImeiMd5Filled(StringUtils.isNotBlank(device.getImeiMd5()) ? 1 : 0)
                        .setIdfaFilled(StringUtils.isNotBlank(device.getIfa()) ? 1 : 0)
                        .setCountryCodeFilled(StringUtils.isNotBlank(device.getCountryCode()) ? 1 : 0)
                        .setLatitudeFilled(device.getLatitude() > 0 ? 1 : 0)
                        .setCaidFilled(CollectionUtils.isEmpty(device.getCaids()) ? 0 : 1)
                        .setIpFilled(StringUtils.isNotBlank(device.getIp()) ? 1 : 0)
                        .setDeviceUpdateTimeFilled(device.getDeviceUpdateTime() > 0 ? 1 : 0)
                        .setOsFilled(StringUtils.isNotBlank(os) && !"UNKONWN".contentEquals(os) ? 1 : 0)
                        // 每个bid都有对应的bidResponse，直接取bidResponse的dspId
                        .setDspIds(new ArrayList<>(yexLogJar.getBidResponse().stream().map(YexBidResponse::getDspId).collect(Collectors.toSet())));
            }
            flattenIvtMetrics(yexLogJar, yexBidRequestLogBuilder);
            if (yexLogJar.getDictPostId() != null) {
                yexBidRequestLogBuilder.setDictPostId(yexLogJar.getDictPostId());
            }
            send(collector, yexBidRequestLogTopic, yexBidRequestLogBuilder.build());

            List<YexBidResponse> yexBidResponses = yexLogJar.getBidResponse();
            for (YexBidResponse yexBidResponse : yexBidResponses) {
                CharSequence dspId = yexBidResponse.getDspId();
                CharSequence dspAdType = yexBidResponse.getDspAdType();
                YexBidResponseLog.Builder yexBidResponseLogBuilder = YexBidResponseLog.newBuilder()
                        .setTimestamp(timestamp)
                        .setGuid(md5String(guid, dspId))
                        .setBidRequestId(bidRequestId)
                        .setImpType(impType)
                        .setDcType(dcType)
                        .setDcId(dcId)
                        .setReqBundle(bundle)
                        .setSlotId(slotId)
                        .setBrandSourceSlotId(brandSourceSlotId)
                        .setSsids(new ArrayList<>(impId2SsidMap.values()))
                        .setOs(os)
                        .setConnectionType(connectionType)
                        .setDspId(dspId)
                        .setDspAdType(dspAdType)
                        .setYexBidResponse(yexBidResponse)
                        .setReceivedImpCount(imps.size())
                        .setReceivedAdRequestCount(adRequestCountSum)
                        .setResponseTime(yexBidResponse.getResponseTime())
                        .setVenderId(venderId)
                        .setVenderSource(venderSource)
                        .setSupplyDeviceFlag(supplyDeviceFlag)
                        .setClickInteractType(yexBidResponse.getClickInteractType())
                        .setIvtTags(yexLogJar.getIvtTags())
                        .setIvtResponseCount(yexLogJar.getIvtCount())
                        .setAppVersion(appVersion)
                        .setDealId(findBidResponseDealId(yexBidRequest, yexBidResponse))
                        .setBrandOrientationType(brandOrientationType)
                        .setAdGroupId(adGroupId)
                        .setGorgonInterfaceType(yexLogJar.getGorgonInterfaceType());
                switch (yexBidResponse.getStatus()) {
                    case NO_BID:
                        yexBidResponseLogBuilder.setBidResponseCountNoBid(1);
                        break;
                    case PARSE_ERROR:
                        yexBidResponseLogBuilder.setBidResponseCountParseError(1);
                        break;
                    case UNKNOWN_ERROR:
                        yexBidResponseLogBuilder.setBidResponseCountUnknownError(1);
                        break;
                    case TIMEOUT:
                        yexBidResponseLogBuilder.setBidResponseCountTimeout(1);
                        break;
                    case NO_BD_CLIENT:
                        yexBidResponseLogBuilder.setBidResponseCountNoBdClient(1);
                        break;
                    case CONNECTION_FAILED:
                        yexBidResponseLogBuilder.setBidResponseCountConnectionFailed(1);
                        break;
                    case REQUEST_BUILDING_ERROR:
                        yexBidResponseLogBuilder.setBidResponseCountRequestBuildingError(1);
                        break;
                    case ASSET_REPLACE_FILTER:
                        yexBidResponseLogBuilder.setBidResponseAssetReplaceFilter(1);
                        break;
                    case SERVICE_UNAVAILABLE:
                        yexBidResponseLogBuilder.setBidResponseServiceUnavailable(1);
                        break;
                    default:
                        break;
                }
                send(collector, yexBidResponseLogTopic, yexBidResponseLogBuilder.build());
                for (YexSeatBid yexSeatBid : yexBidResponse.getSeatBid()) {
                    CharSequence seatId = yexSeatBid.getSeatId();
                    YexSeatBidLog yexSeatBidLog = YexSeatBidLog.newBuilder()
                            .setTimestamp(timestamp)
                            .setBidRequestId(bidRequestId)
                            .setImpType(impType)
                            .setDcType(dcType)
                            .setDcId(dcId)
                            .setReqBundle(bundle)
                            .setSlotId(slotId)
                            .setBrandSourceSlotId(brandSourceSlotId)
                            .setOs(os)
                            .setConnectionType(connectionType)
                            .setDspId(dspId)
                            .setDspAdType(dspAdType)
                            .setSeatId(seatId)
                            .setGuid(md5String(guid, dspId, seatId))
                            .setYexSeatBid(yexSeatBid)
                            .setVenderId(venderId)
                            .setVenderSource(venderSource)
                            .setSupplyDeviceFlag(supplyDeviceFlag)
                            .setClickInteractType(yexBidResponse.getClickInteractType())
                            .setIvtTags(yexLogJar.getIvtTags())
                            .setIvtSeatBidCount(yexLogJar.getIvtCount())
                            .setAppVersion(appVersion)
                            .setBrandOrientationType(brandOrientationType)
                            .setAdGroupId(adGroupId)
                            .build();
                    send(collector, yexSeatBidLogTopic, yexSeatBidLog);
                    for (YexBid yexBid : yexSeatBid.getBid()) {
                        YexBidLog.Builder builder = YexBidLog.newBuilder()
                                .setBundle(yexBid.getBundle())
                                .setTimestamp(timestamp)
                                .setGuid(md5String(guid, dspId, seatId, yexBid.getGuid()))
                                .setBidRequestId(bidRequestId)
                                .setImpType(impType)
                                .setDcType(dcType)
                                .setDcId(dcId)
                                .setReqBundle(bundle)
                                .setSlotId(slotId)
                                .setBrandSourceSlotId(brandSourceSlotId)
                                .setHostname(yexLogJar.getHostname())
                                .setGorgonHostname(yexLogJar.getGorgonHostname())
                                .setOs(os)
                                .setConnectionType(connectionType)
                                .setSsid(impId2SsidMap.getOrDefault(yexBid.getImpId(), 0))
                                .setDspId(dspId)
                                .setDspAdType(dspAdType)
                                .setSeatId(seatId)
                                .setWinPrice(yexBid.getWinPrice().intValue())
                                .setYexBid(yexBid)
                                .setCid(yexBid.getCid())
                                .setCrid(yexBid.getCrid())
                                .setCur(yexBid.getCur())
                                .setToCnyFactor(yexBid.getToCnyFactor())
                                .setOriginalCurBidPrice(yexBid.getOriginalCurBidPrice())
                                .setBidPrice(yexBid.getBidPrice().intValue())
                                .setAdxBidPrice(yexBid.getAdxBidPrice())
                                .setVenderId(venderId)
                                .setVenderSource(venderSource)
                                .setSupplyDeviceFlag(supplyDeviceFlag)
                                .setDealId(findBidResponseDealId(yexBidRequest, yexBidResponse))
                                .setClickInteractType(yexBidResponse.getClickInteractType())
                                .setIvtTags(yexLogJar.getIvtTags())
                                .setIvtBidCount(yexLogJar.getIvtCount())
                                .setProvince(yexLogJar.getProvince())
                                .setCity(yexLogJar.getCity())
                                .setAppVersion(appVersion)
                                .setBrandOrientationType(brandOrientationType)
                                .setAdGroupId(adGroupId)
                                .setGorgonInterfaceType(yexLogJar.getGorgonInterfaceType());
                        if (yexLogJar.getDictPostId() != null) {
                            builder.setDictPostId(yexLogJar.getDictPostId());
                        }
                        switch (yexBid.getBidStatus()) {
                            case WIN:
                                builder.setBidCountWin(1);
                                builder.setBidPriceOfWin(yexBid.getBidPrice().intValue());
                                break;
                            case LOSE:
                                builder.setBidCountLose(1);
                                break;
                            case INVALID_BID:
                                builder.setBidCountInvalid(1);
                                break;
                            case TEST:
                                builder.setBidCountTest(1);
                                break;
                            default:
                                break;
                        }
                        switch (yexBid.getInvalidReason()) {
                            case VALID:
                                builder.setBidCountValid(1);
                                break;
                            case INVALID_IMP_ID:
                                builder.setBidCountInvalidImpId(1);
                                break;
                            case INVALID_ASSET_ID:
                                builder.setBidCountInvalidAssetId(1);
                                break;
                            case INVALID_PRICE:
                                builder.setBidCountInvalidPrice(1);
                                break;
                            case INVALID_ATTRI:
                                builder.setBidCountInvalidAttri(1);
                                break;
                            case NO_ATTRI:
                                builder.setBidCountNoAttri(1);
                                break;
                            case INVALID_URL:
                                builder.setBidCountInvalidUrl(1);
                                break;
                            case NO_LANDING_PAGE:
                                builder.setBidCountNoLandingPage(1);
                                break;
                            case ILLEGAL_ASSET:
                                builder.setBidCountIllegalAsset(1);
                                break;
                            case NO_VASTTAG:
                                builder.setBidCountNoVastTag(1);
                                break;
                            case INVALID_VASTTAG:
                                builder.setBidCountInvalidVastTag(1);
                                break;
                            case INVALID_IMAGE_URL:
                                builder.setBidCountInvalidImageUrl(1);
                                break;
                            case INVALID_LANDING_PAGE:
                                builder.setBidCountInvalidLandingPage(1);
                                break;
                            case EMPTY_WHITE_LIST:
                                builder.setBidCountEmptyWhiteList(1);
                                break;
                            case INVALID_DOWNLOAD_APP_INFO:
                                builder.setBidCountInvalidDownloadAppInfo(1);
                                break;
                            case INVALID_YOUDAO_INITIATIVE:
                                builder.setBidCountInvalidYoudaoInitiative(1);
                            case QUICK_APP_AD_BLOCKED:
                                builder.setBidCountQuickAppAdBlocked(1);
                            default:
                                break;
                        }
                        send(collector, yexBidLogTopic, builder.build());
                        if (!String.valueOf(dspId).equals(DSP_ID_ZHIXUAN)) {
                           if (yexBid.getAdmNative() != null) {
                               sendYexAssetLog(collector, timestamp, bidRequestId, dcType, dcId, slotId, impType, dspId, dspAdType, seatId, yexBid, brandSourceSlotId);
                               sendYexMaterialLog(collector, timestamp, dspId, yexBid);
                           }
                        }
                    }
                }
            }
        } catch (Exception e) {
            errorCounter.inc();
            log.error(e, e);
        }
    }

    /**
     * 如果是外部DSP的广告，发送yex asset log，用来做素材后审用
     */
    private void sendYexAssetLog(MessageCollector collector, long timestamp,
                                 String bidRequestId, DistributionChannelType dcType,
                                 CharSequence dcId, CharSequence slotId, ImpType impType,
                                 CharSequence dspId, CharSequence dspAdType, CharSequence seatId,
                                 YexBid yexBid, CharSequence brandSourceSlotId)
            throws IOException, NoSuchAlgorithmException, KafkaAvroCoderException {
        AssetContent assetContent = new AssetContent();

        YexNativeResponse yexNativeResponse = yexBid.getAdmNative();
        List<YexAssetResponse> assets = yexNativeResponse.getAssets();
        YexAssetLog.Builder yexAssetLogBuilder = YexAssetLog.newBuilder()
                .setTimestamp(timestamp)
                .setBidRequestId(bidRequestId)
                .setImpType(impType)
                .setDcType(dcType)
                .setDcId(dcId)
                .setSlotId(slotId)
                .setBrandSourceSlotId(brandSourceSlotId)
                .setDspId(dspId)
                .setDspAdType(dspAdType)
                .setSeatId(seatId)
                .setCid(yexBid.getCid())
                .setCrid(yexBid.getCrid());
        for (YexAssetResponse yexAssetResponse : assets) {
            Object content = yexAssetResponse.getContent();
            if (content instanceof YexImageResponse) {
                final String imageUrl = Objects.toString(((YexImageResponse) content).getUrl(), "");
                assetContent.getImageUrls().add(imageUrl);
            } else if (content instanceof YexTitleResponse) {
                String textUrl = Objects.toString(((YexTitleResponse) content).getText(), "");
                assetContent.getTexts().add(textUrl);
            }
        }

        yexAssetLogBuilder.setLandingUrl(Objects.toString(yexBid.getAdmNative().getLink().getUrl(), ""));
        yexAssetLogBuilder.setFallbackUrl(Objects.toString(yexBid.getAdmNative().getLink().getFallback(), ""));
        yexAssetLogBuilder.setAssetsFlat(OBJECT_MAPPER.writeValueAsString(assetContent));
        // 如果同一DSP、位置、系统、变体且广告物料的图片、文字一致，随机选择一个落地页id进行审核。
        // 使用Guid字段进行去重。
        yexAssetLogBuilder.setGuid(md5String(dspId, seatId, yexBid.getCid(), yexBid.getCrid(), getImageTextUrls(assetContent)));

        send(collector, yexAssetLogTopic, yexAssetLogBuilder.build());
    }



    /**
     * 如果是外部DSP的广告，发送yex material log，记录全部图片素材的url，用来查找素材的dsp来源
     */
    private void sendYexMaterialLog(MessageCollector collector, long timestamp, CharSequence dspId, YexBid yexBid) {
        YexNativeResponse yexNativeResponse = yexBid.getAdmNative();
        List<YexAssetResponse> assets = yexNativeResponse.getAssets();
        for (YexAssetResponse yexAssetResponse : assets) {
            Object content = yexAssetResponse.getContent();
            try {
                if (content instanceof YexImageResponse) {
                    final String imageUrl = Objects.toString(((YexImageResponse) content).getUrl(), "");
                    if (StringUtils.isNotBlank(imageUrl)) {
                        YexMaterialLog yexMaterial = YexMaterialLog.newBuilder()
                                .setTimestamp(timestamp)
                                .setGuid(UUID.randomUUID().toString())
                                .setUrlMd5(md5String(imageUrl))
                                .setAssetUrl(imageUrl)
                                .setCrid(yexBid.getCrid())
                                .setDspId(dspId)
                                .build();
                        send(collector, yexMaterialLogTopic, yexMaterial);
                    }
                }
            } catch (Exception e) {
                log.error("sendYexMaterialLog error, asset content : " + content, e);
            }
        }
    }

    @Data
    private static class AssetContent {
        private List<String> texts = new ArrayList<>(1);
        private List<String> imageUrls = new ArrayList<>(1);

        /**
         * 落地页Url。
         */
        private String lpUrl = "";
    }

    private CharSequence getImageTextUrls(@Nonnull AssetContent content) {
        StringJoiner joiner = new StringJoiner("#@$");

        Collections.sort(content.imageUrls);
        for (String url : content.imageUrls) {
            joiner.add(url);
        }

        Collections.sort(content.texts);
        for (String text : content.texts) {
            joiner.add(text);
        }

        return joiner.toString();
    }

    private SpecificDatumReader<YexLogJar> getReader(String topic, byte[] bytes) {
        String id = String.valueOf(ByteBuffer.wrap(bytes, 1, 4).getInt());
        SpecificDatumReader<YexLogJar> reader = readerCache.get(topic + id);
        if (reader == null) {
            Schema writer = registry.getSchemaByID(topic, id);
            reader = new SpecificDatumReader<YexLogJar>(writer, YexLogJar.getClassSchema());
            readerCache.put(topic + id, reader);
        }
        return reader;
    }

    private void send(MessageCollector collector, String topic, IndexedRecord record) throws KafkaAvroCoderException {
        collector.send(new OutgoingMessageEnvelope(new SystemStream("kafka", topic), String.valueOf(record.hashCode()), getEncoder(topic).toBytes(record)));
    }

    private String md5String(CharSequence... strings) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        if (md5 == null) {
            md5 = MessageDigest.getInstance("MD5");
        } else {
            md5.reset();
        }
        for (CharSequence string : strings) {
            md5.update(string.toString().getBytes(Charsets.UTF_8.name()));
        }
        byte[] digest = md5.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b & 0xff));
        }
        return sb.toString();
    }

    /**
     * 将异常标签数组维度字段转化为指标字段
     *
     * @param ivtTags
     * @return
     */
    private Map<String, Integer> ivtMetricsMap(java.util.List<CharSequence> ivtTags) {
        Map<String, Integer> ivtMetricsMap = new HashMap<>();
        for (CharSequence ivtTag : ivtTags) {
            if (ivtTag.toString().startsWith("slot_ua_")) {
                ivtMetricsMap.put("slot_ua", 1);
            } else if (ivtTag.toString().startsWith("slot_model_")) {
                ivtMetricsMap.put("slot_model", 1);
            } else if (ivtTag.toString().startsWith("slot_osv_")) {
                ivtMetricsMap.put("slot_osv", 1);
            } else if (ivtTag.toString().startsWith("ip_")) {
                ivtMetricsMap.put("ip", 1);
            } else if (ivtTag.toString().startsWith("pv_")) {
                ivtMetricsMap.put("pv", 1);
            } else if (ivtTag.toString().startsWith("impr_")) {
                ivtMetricsMap.put("impr", 1);
            } else if (ivtTag.toString().startsWith("click_")) {
                ivtMetricsMap.put("click", 1);
            } else{
                ivtMetricsMap.put(ivtTag.toString(), 1);
            }
        }
        return ivtMetricsMap;
    }

    /**
     * ivt_tags 展开，行转列
     * @param yexLogJar
     * @param yexBidRequestLogBuilder
     */
    private void flattenIvtMetrics(YexLogJar yexLogJar, YexBidRequestLog.Builder yexBidRequestLogBuilder) {
        Map<String, Integer> ivtMetricsMap = ivtMetricsMap(yexLogJar.getIvtTags());
        yexBidRequestLogBuilder.setIvtSlotUa(ivtMetricsMap.getOrDefault("slot_ua", 0));
        yexBidRequestLogBuilder.setIvtSlotModel(ivtMetricsMap.getOrDefault("slot_model", 0));
        yexBidRequestLogBuilder.setIvtSlotOsv(ivtMetricsMap.getOrDefault("slot_osv", 0));
        yexBidRequestLogBuilder.setIvtIp10m(ivtMetricsMap.getOrDefault("ip", 0));
        yexBidRequestLogBuilder.setIvtPv10m(ivtMetricsMap.getOrDefault("pv", 0));
        yexBidRequestLogBuilder.setIvtImpr10m(ivtMetricsMap.getOrDefault("impr", 0));
        yexBidRequestLogBuilder.setIvtClick10m(ivtMetricsMap.getOrDefault("click", 0));
        yexBidRequestLogBuilder.setIvtWrongModel(ivtMetricsMap.getOrDefault("model-wrong-os", 0));
        yexBidRequestLogBuilder.setIvtWrongDevice(ivtMetricsMap.getOrDefault("device-wrong-os", 0));
        yexBidRequestLogBuilder.setIvtOtherUa(ivtMetricsMap.getOrDefault("ua-other-os", 0));
        yexBidRequestLogBuilder.setIvtWrongUa(ivtMetricsMap.getOrDefault("ua-wrong-os", 0));
        yexBidRequestLogBuilder.setIvtBlacklisted(ivtMetricsMap.getOrDefault("blacklisted", 0));
    }

    /**
     * 查找bidResponse归属的dealId，用第一个有效的dealId值作为此YexBidResponse的dealId
     */
    private CharSequence findBidResponseDealId(YexBidRequest yexBidRequest, YexBidResponse bidResponse){
        for (YexSeatBid seatBid : bidResponse.getSeatBid()) {
            for (YexBid bid : seatBid.getBid()) {
                if (StringUtils.isNotBlank(bid.getDealId())) {
                    return bid.getDealId();
                }
            }
        }
        // 目前的业务场景一个bidrequest只会命中一个deal，所以如果dsp没有响应，那么去bidrequest的imp中取deal_id
        for (YexImp yexImp : yexBidRequest.getImp()) {
            CharSequence dealId = yexImp.getDealId();
            if (StringUtils.isNotBlank(dealId)) {
                return dealId;
            }
        }

        return "";
    }
}
