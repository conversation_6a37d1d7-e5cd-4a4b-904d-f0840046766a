package com.youdao.quipu.samza;

import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericData;
import org.apache.avro.util.Utf8;
import org.apache.samza.config.Config;
import org.apache.samza.system.IncomingMessageEnvelope;
import org.apache.samza.system.OutgoingMessageEnvelope;
import org.apache.samza.system.SystemStream;
import org.apache.samza.task.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
@Slf4j
public class DataShuffleSamzaTask extends KafkaAvroCodecs implements StreamTask, InitableTask {
    private static final String TOPIC_CLICK = "click_sdk_charged";
    private static final String TOPIC_CLICK_FRAUD = "click_ead_fraud";
    private static final String TOPIC_CLICK_DISCARDED = "click_ead_discarded";
    private static final String TOPIC_CONV_SDK = "conv_sdk_avro";
    private static final String TOPIC_CONV_DSP = "conv_dsp_avro";
    private static final String TASK_OUTPUT_TOPICS = "task.output.topics";
    private static final String BID = "bid";
    private static final String BID_ID = "bid_id";
    private static final String SPONSOR_ID = "sponsor_id";
    private static final String USER_ID = "auid";

    private static final String COMMA = ",";
    private static final String SEMICOLON = ":";
    private final Map<String, String> outputTopics = new HashMap<>(16);

    @Override
    public void init(Config config, TaskContext context) throws Exception {
        for (String s : config.get(TASK_OUTPUT_TOPICS).split(COMMA)) {
            String[] kv = s.split(SEMICOLON);
            if (kv.length != 2) {
                log.error("Not correct properties = {}", s);
                throw new Exception("Not correct properties " + s);
            }
            outputTopics.put(kv[0], kv[1]);
        }
        Properties properties = new Properties();
        for (Map.Entry<String, String> entry : config.entrySet()) {
            properties.put(entry.getKey(), entry.getValue());
        }
        super.decoderProperties = properties;
        super.encoderProperties = properties;
    }

    @Override
    public void process(IncomingMessageEnvelope envelope, MessageCollector collector, TaskCoordinator coordinator) throws Exception {
        GenericData.Record record = null;
        String topic = envelope.getSystemStreamPartition().getSystemStream().getStream();
        try {
            String outputTopic = outputTopics.get(topic);

            byte[] message = (byte[]) envelope.getMessage();
            record = getDecoder(topic).decode(message).getRecord();
            String sponsorId = String.valueOf(record.get(SPONSOR_ID));
            Map ext = (Map) record.get("ext");
            String uid = String.valueOf(ext.get(new Utf8(USER_ID)));
            String uidKey = uid + "-" + sponsorId;

            if (TOPIC_CONV_SDK.equals(topic) || TOPIC_CONV_DSP.equals(topic)) {
                String bid = String.valueOf(record.get(BID));
                String bidKey = bid + "-" + sponsorId;
                if (!bid.isEmpty()) {
                    collector.send(new OutgoingMessageEnvelope(new SystemStream("kafka2", outputTopic), bidKey, getEncoder(outputTopic).toBytes(record)));
                    log.info(topic + "'s bid shuffler key:" + bidKey + ":" + new Date(objToLong(record.get("timestamp"))));
                } else {
                    if ("".equals(uid) || "null".equals(uid)) {
                        collector.send(new OutgoingMessageEnvelope(new SystemStream("kafka2", outputTopic), sponsorId, getEncoder(outputTopic).toBytes(record)));
                        log.info(topic + "'s shuffler key is null:" + sponsorId + "," + new Date(objToLong(record.get("timestamp"))));
                    } else {
                        collector.send(new OutgoingMessageEnvelope(new SystemStream("kafka2", outputTopic), uidKey, getEncoder(outputTopic).toBytes(record)));
                        log.info(topic + "'s uid shuffler key:" + uidKey + ":" + new Date(objToLong(record.get("timestamp"))));
                    }
                }
            } else if (TOPIC_CLICK.equals(topic) || TOPIC_CLICK_FRAUD.equals(topic) || TOPIC_CLICK_DISCARDED.equals(topic)) {
                String bid = String.valueOf(record.get(BID_ID));
                String bidKey = bid + "-" + sponsorId;
                collector.send(new OutgoingMessageEnvelope(new SystemStream("kafka2", outputTopic), bidKey, getEncoder(outputTopic).toBytes(record)));
                log.info(topic + "'s bid shuffler key:" + bidKey + ":" + new Date(objToLong(record.get("timestamp"))));
                if ("".equals(uid) || "null".equals(uid)) {
                    log.info(topic + "'s shuffler key is null:" + record.toString() + new Date(objToLong(record.get("timestamp"))));
                } else {
                    collector.send(new OutgoingMessageEnvelope(new SystemStream("kafka2", outputTopic), uidKey, getEncoder(outputTopic).toBytes(record)));
                    log.info(topic + "'s uid shuffler key:" + uidKey + ":" + new Date(objToLong(record.get("timestamp"))));
                }
            } else {
                log.error("Unknown topic: {}", topic);
            }
        } catch (KafkaAvroCoderException e) {
            throw e;
        } catch (Exception e) {
            String msg = record == null ? "decode failed" : record.toString();
            log.error("Msg process error, topic = {}， partition = {}, offset= {}, msg = {}",
                    topic, envelope.getSystemStreamPartition().getPartition().getPartitionId(), envelope.getOffset(), msg, e);
        }
    }

    private long objToLong(Object object) {
        try {
            return object == null ? 0 : Long.parseLong(object.toString());
        } catch (Exception e) {
            return 0L;
        }
    }
}
