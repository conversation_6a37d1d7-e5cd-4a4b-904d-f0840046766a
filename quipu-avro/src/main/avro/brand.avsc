[{"namespace": "com.youdao.quipu.avro.schema", "name": "PreBrandAdFailType", "type": "enum", "symbols": ["UNKNOWN", "TIMEOUT", "REQUEST_FAIL", "RESPONSE_FAIL", "DOWNLOAD_FAIL", "AD_NOT_MATCH"]}, {"doc": "brand impr event", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "BrandImpr", "imports": ["common.avsc"], "fields": [{"name": "timestamp", "type": "long"}, {"name": "guid", "type": "string"}, {"name": "bidId", "type": "string", "default": ""}, {"name": "sponsorId", "type": "long", "default": 0}, {"name": "campaignId", "type": "long", "default": 0}, {"name": "adGroupId", "type": "long", "default": 0}, {"name": "adVariationId", "type": "long", "default": 0}, {"name": "dsp_id", "type": "string", "default": "", "doc": "品牌程序化才有的dsp_id"}, {"name": "deal_id", "type": "string", "default": "", "doc": "品牌程序化才有的deal_id"}, {"name": "imprPos", "type": "int", "default": 0}, {"name": "adType", "type": "string", "default": ""}, {"name": "imprIp", "type": "string", "default": ""}, {"name": "country", "type": "int", "default": -1}, {"name": "province", "type": "int", "default": -1}, {"name": "city", "type": "int", "default": -1}, {"name": "impr_device_id", "type": "string", "default": ""}, {"name": "brand", "type": "string", "default": ""}, {"name": "oaid", "type": "string", "default": ""}, {"name": "imei", "type": "string", "default": ""}, {"name": "idfa", "type": "string", "default": ""}, {"name": "supply_imei", "type": "string", "default": ""}, {"name": "supply_oaid", "type": "string", "default": ""}, {"name": "supply_idfa", "type": "string", "default": ""}, {"name": "dimei", "type": "string", "default": ""}, {"name": "mediaId", "type": "long", "default": 0}, {"name": "styleId", "type": "long", "default": 0}, {"name": "brandClkType", "type": "int", "default": 0}, {"name": "deliveryType", "type": "int", "default": 0}, {"name": "billingType", "type": "int", "default": 0}, {"name": "keyFrom", "type": "string", "default": ""}, {"name": "vendor", "type": "string", "default": ""}, {"name": "impr", "type": "int", "default": 1}, {"name": "hostname", "type": "string", "default": ""}, {"name": "venderId", "type": "string", "default": ""}, {"name": "vender_source", "type": "string", "default": "", "doc": "source of vender"}, {"name": "supply_device_flag", "type": "boolean", "default": false}, {"name": "frequency_type", "type": "int", "default": 0}, {"name": "order_start_timestamp", "type": "long", "default": 0}, {"name": "order_end_timestamp", "type": "long", "default": 0}, {"name": "click_interact_type", "type": "int", "default": 0}, {"name": "mappingPosId", "type": "long", "default": 0}, {"name": "caids", "type": {"type": "array", "items": "Caid"}, "default": [], "doc": "caid list"}, {"name": "bid_timestamp", "type": "long", "default": 0}, {"name": "client_impr_timestamp", "type": "long", "default": 0}, {"name": "imei_md5", "type": "string", "default": ""}, {"name": "oaid_md5", "type": "string", "default": ""}, {"name": "idfa_md5", "type": "string", "default": ""}, {"name": "caid_md5", "type": "string", "default": "", "doc": "md5 value of the first new caid"}, {"name": "caid_md5_v2", "type": "string", "default": "", "doc": "md5 value of the second new caid"}, {"name": "supply_imei_md5", "type": "string", "default": ""}, {"name": "supply_oaid_md5", "type": "string", "default": ""}, {"name": "supply_idfa_md5", "type": "string", "default": ""}, {"name": "gender", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "age", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "interests", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "dictStates", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "installed_pkg_name", "type": {"type": "array", "items": "string"}, "default": [], "doc": "device installed app package name list"}, {"name": "age_v2", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "random_age", "type": "int", "default": 0}, {"name": "random_gender", "type": "int", "default": 0}, {"name": "service_host", "type": "string", "default": "", "doc": "track service host name"}, {"name": "interface_type", "type": "com.youdao.quipu.avro.schema.GorgonInterfaceType", "default": "UNKNOWN"}, {"name": "env", "type": "string", "default": ""}, {"name": "cpm", "type": "double", "default": 0}, {"name": "ext", "type": ["null", {"type": "map", "values": "string"}], "default": null}]}, {"doc": "brand click event", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "BrandClick", "imports": ["common.avsc"], "fields": [{"name": "timestamp", "type": "long"}, {"name": "guid", "type": "string"}, {"name": "bidId", "type": "string", "default": ""}, {"name": "sponsorId", "type": "long", "default": 0}, {"name": "campaignId", "type": "long", "default": 0}, {"name": "adGroupId", "type": "long", "default": 0}, {"name": "adVariationId", "type": "long", "default": 0}, {"name": "dsp_id", "type": "string", "default": "", "doc": "品牌程序化才有的dsp_id"}, {"name": "deal_id", "type": "string", "default": "", "doc": "品牌程序化才有的deal_id"}, {"name": "imprPos", "type": "int", "default": 0}, {"name": "adType", "type": "string", "default": ""}, {"name": "imprIp", "type": "string", "default": ""}, {"name": "country", "type": "int", "default": -1}, {"name": "province", "type": "int", "default": -1}, {"name": "city", "type": "int", "default": -1}, {"name": "click_device_id", "type": "string", "default": ""}, {"name": "mediaId", "type": "long", "default": 0}, {"name": "styleId", "type": "long", "default": 0}, {"name": "brandClkType", "type": "int", "default": 0}, {"name": "deliveryType", "type": "int", "default": 0}, {"name": "billingType", "type": "int", "default": 0}, {"name": "keyFrom", "type": "string", "default": ""}, {"name": "vendor", "type": "string", "default": ""}, {"name": "click", "type": "int", "default": 1}, {"name": "hostname", "type": "string", "default": ""}, {"name": "venderId", "type": "string", "default": ""}, {"name": "vender_source", "type": "string", "default": "", "doc": "source of vender"}, {"name": "supply_device_flag", "type": "boolean", "default": false}, {"name": "click_interact_type", "type": "int", "default": 0}, {"name": "brand", "type": "string", "default": ""}, {"name": "idfa", "type": "string", "default": ""}, {"name": "caid", "type": "string", "default": ""}, {"name": "aaid", "type": "string", "default": ""}, {"name": "imei", "type": "string", "default": ""}, {"name": "oaid", "type": "string", "default": "", "doc": "open anonymous id"}, {"name": "imei_md5", "type": "string", "default": ""}, {"name": "oaid_md5", "type": "string", "default": ""}, {"name": "idfa_md5", "type": "string", "default": ""}, {"name": "caid_md5", "type": "string", "default": "", "doc": "md5 value of the first new caid"}, {"name": "caid_md5_v2", "type": "string", "default": "", "doc": "md5 value of the second new caid"}, {"name": "supply_imei", "type": "string", "default": ""}, {"name": "supply_oaid", "type": "string", "default": ""}, {"name": "supply_idfa", "type": "string", "default": ""}, {"name": "supply_imei_md5", "type": "string", "default": ""}, {"name": "supply_oaid_md5", "type": "string", "default": ""}, {"name": "supply_idfa_md5", "type": "string", "default": ""}, {"name": "dimei", "type": "string", "default": ""}, {"name": "caids", "type": {"type": "array", "items": "Caid"}, "default": [], "doc": "caid list"}, {"name": "gender", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "age", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "interests", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "dictStates", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "installed_pkg_name", "type": {"type": "array", "items": "string"}, "default": [], "doc": "device installed app package name list"}, {"name": "age_v2", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "random_age", "type": "int", "default": 0}, {"name": "random_gender", "type": "int", "default": 0}, {"name": "service_host", "type": "string", "default": "", "doc": "track service host name"}, {"name": "interface_type", "type": "com.youdao.quipu.avro.schema.GorgonInterfaceType", "default": "UNKNOWN"}, {"name": "env", "type": "string", "default": ""}, {"name": "ext", "type": ["null", {"type": "map", "values": "string"}], "default": null}]}, {"doc": "brand pv event", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "BrandPv", "imports": ["common.avsc"], "fields": [{"name": "timestamp", "type": "long"}, {"name": "guid", "type": "string"}, {"name": "bidId", "type": "string", "default": ""}, {"name": "venderId", "type": "string", "default": ""}, {"name": "vender_source", "type": "string", "default": "", "doc": "source of vender"}, {"name": "imprPos", "type": "int", "default": 0}, {"name": "imprIp", "type": "string", "default": ""}, {"name": "country", "type": "int", "default": -1}, {"name": "province", "type": "int", "default": -1}, {"name": "city", "type": "int", "default": -1}, {"name": "pv_device_id", "type": "string", "default": ""}, {"name": "idfa", "type": "string", "default": ""}, {"name": "caid", "type": "string", "default": ""}, {"name": "aaid", "type": "string", "default": ""}, {"name": "android_id", "type": "string", "default": ""}, {"name": "imei", "type": "string", "default": ""}, {"name": "oaid", "type": "string", "default": "", "doc": "open anonymous id"}, {"name": "keyFrom", "type": "string", "default": ""}, {"name": "vendor", "type": "string", "default": ""}, {"name": "pv", "type": "int", "default": 1}, {"name": "hostname", "type": "string", "default": ""}, {"name": "ydUserId", "type": "string", "default": ""}, {"name": "gender", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "age", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "interests", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "dictStates", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "w", "type": "int", "default": -1}, {"name": "h", "type": "int", "default": -1}, {"name": "brand", "type": "string", "default": ""}, {"name": "model", "type": "string", "default": ""}, {"name": "mid", "type": "string", "default": ""}, {"name": "os", "type": "string", "default": ""}, {"name": "ua", "type": "string", "default": ""}, {"name": "imei_md5", "type": "string", "default": ""}, {"name": "oaid_md5", "type": "string", "default": ""}, {"name": "idfa_md5", "type": "string", "default": ""}, {"name": "caid_md5", "type": "string", "default": "", "doc": "md5 value of the first new caid"}, {"name": "caid_md5_v2", "type": "string", "default": "", "doc": "md5 value of the second new caid"}, {"name": "supply_device_flag", "type": "boolean", "default": false}, {"name": "supply_imei", "type": "string", "default": ""}, {"name": "supply_oaid", "type": "string", "default": ""}, {"name": "supply_idfa", "type": "string", "default": ""}, {"name": "supply_imei_md5", "type": "string", "default": ""}, {"name": "supply_oaid_md5", "type": "string", "default": ""}, {"name": "supply_idfa_md5", "type": "string", "default": ""}, {"name": "supply_gender", "type": {"type": "array", "items": "int"}, "default": [], "doc": "gender from supply device"}, {"name": "supply_age", "type": {"type": "array", "items": "int"}, "default": [], "doc": "age from supply device"}, {"name": "supply_interests", "type": {"type": "array", "items": "int"}, "default": [], "doc": "interests from supply device"}, {"name": "supply_dictStates", "type": {"type": "array", "items": "string"}, "default": [], "doc": "dictStates from supply device"}, {"name": "caids", "type": {"type": "array", "items": "Caid"}, "default": [], "doc": "caid list"}, {"name": "installed_pkg_name", "type": {"type": "array", "items": "string"}, "default": [], "doc": "device installed app package name list"}, {"name": "dimei", "type": "string", "default": ""}, {"name": "age_v2", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "random_age", "type": "int", "default": 0}, {"name": "random_gender", "type": "int", "default": 0}, {"name": "brand_crowd_package_ids", "type": {"type": "array", "items": "long"}, "default": []}, {"name": "interface_type", "type": "com.youdao.quipu.avro.schema.GorgonInterfaceType", "default": "UNKNOWN"}, {"name": "env", "type": "string", "default": ""}, {"name": "dark_ip", "type": "int", "default": 0, "doc": "IP黑名单"}, {"name": "dark_device", "type": "int", "default": 0, "doc": "设备黑名单"}, {"name": "ext", "type": ["null", {"type": "map", "values": "string"}], "default": null}]}, {"doc": "brand bid event", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "BrandBid", "fields": [{"name": "timestamp", "type": "long"}, {"name": "guid", "type": "string"}, {"name": "bidId", "type": "string", "default": ""}, {"name": "sponsorId", "type": "long", "default": 0}, {"name": "campaignId", "type": "long", "default": 0}, {"name": "adGroupId", "type": "long", "default": 0}, {"name": "adVariationId", "type": "long", "default": 0}, {"name": "mediaId", "type": "long", "default": 0}, {"name": "styleId", "type": "long", "default": 0}, {"name": "venderId", "type": "string", "default": ""}, {"name": "vender_source", "type": "string", "default": "", "doc": "source of vender"}, {"name": "imprPos", "type": "int", "default": 0}, {"name": "imprIp", "type": "string", "default": ""}, {"name": "country", "type": "int", "default": -1}, {"name": "province", "type": "int", "default": -1}, {"name": "city", "type": "int", "default": -1}, {"name": "bid_device_id", "type": "string", "default": ""}, {"name": "status", "type": "string", "default": ""}, {"name": "keyFrom", "type": "string", "default": ""}, {"name": "vendor", "type": "string", "default": ""}, {"name": "bid", "type": "int", "default": 1}, {"name": "hostname", "type": "string", "default": ""}, {"name": "supply_device_flag", "type": "boolean", "default": false}, {"name": "click_interact_type", "type": "int", "default": 0}, {"name": "mappingPosId", "type": "long", "default": 0}, {"name": "interface_type", "type": "com.youdao.quipu.avro.schema.GorgonInterfaceType", "default": "UNKNOWN"}, {"name": "env", "type": "string", "default": ""}, {"name": "ext", "type": ["null", {"type": "map", "values": "string"}], "default": null}]}, {"doc": "pre brand tracker event", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "PreBrandStatus", "fields": [{"name": "timestamp", "type": "long"}, {"name": "guid", "type": "string"}, {"name": "venderId", "type": "string", "default": ""}, {"name": "vender_source", "type": "string", "default": "", "doc": "source of vender"}, {"name": "sdk_udid", "type": "string", "default": "", "doc": "slot udid"}, {"name": "ip", "type": "string", "default": ""}, {"name": "idfa", "type": "string", "default": ""}, {"name": "caid", "type": "string", "default": ""}, {"name": "aaid", "type": "string", "default": ""}, {"name": "android_id", "type": "string", "default": ""}, {"name": "imei", "type": "string", "default": ""}, {"name": "oaid", "type": "string", "default": "", "doc": "open anonymous id"}, {"name": "udid", "type": "string", "default": "", "doc": "设备ID，如 AndroidID 或 IDFA，要求明文大写"}, {"name": "app_version", "type": "string", "default": ""}, {"name": "sdk_version", "type": "string", "default": ""}, {"name": "os_version", "type": "string", "default": ""}, {"name": "vendor", "type": "string", "default": ""}, {"name": "fail_type", "type": "PreBrandAdFailType"}, {"name": "service_path", "type": "string", "default": ""}, {"name": "ext", "type": ["null", {"type": "map", "values": "string"}], "default": null}]}, {"namespace": "com.youdao.quipu.avro.schema", "name": "BrandAbnormalType", "type": "enum", "symbols": ["UNKNOWN", "DEST_FAIL", "IMPR_FAIL", "DOWNLOAD_FAIL", "UNWANTED_QUICK_APP"]}, {"doc": "brand abnormal url tracker event", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "BrandAbnormalUrl", "fields": [{"name": "timestamp", "type": "long"}, {"name": "guid", "type": "string"}, {"name": "slot_id", "type": "string", "default": ""}, {"name": "adv_id", "type": "string", "default": ""}, {"name": "url", "type": "string", "default": ""}, {"name": "ip", "type": "string", "default": ""}, {"name": "province", "type": "int", "default": -1}, {"name": "city", "type": "int", "default": -1}, {"name": "idfa", "type": "string", "default": ""}, {"name": "caid", "type": "string", "default": ""}, {"name": "aaid", "type": "string", "default": ""}, {"name": "android_id", "type": "string", "default": ""}, {"name": "imei", "type": "string", "default": ""}, {"name": "oaid", "type": "string", "default": "", "doc": "open anonymous id"}, {"name": "app_version", "type": "string", "default": ""}, {"name": "sdk_version", "type": "string", "default": ""}, {"name": "os_version", "type": "string", "default": ""}, {"name": "abnormal_type", "type": ["null", "BrandAbnormalType"], "default": null}, {"name": "model", "type": "string", "default": ""}, {"name": "count", "type": "int", "default": 1}, {"name": "ext", "type": ["null", {"type": "map", "values": "string"}], "default": null}]}, {"doc": "pre brand tracker event", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "BrandTracker", "fields": [{"name": "timestamp", "type": "long"}, {"name": "bidId", "type": "string", "default": ""}, {"name": "guid", "type": "string", "default": ""}, {"name": "sponsorId", "type": "long", "default": 0}, {"name": "campaignId", "type": "long", "default": 0}, {"name": "adGroupId", "type": "long", "default": 0}, {"name": "adVariationId", "type": "long", "default": 0}, {"name": "imprPos", "type": "int", "default": 0}, {"name": "imprIp", "type": "string", "default": ""}, {"name": "province", "type": "int", "default": -1}, {"name": "city", "type": "int", "default": -1}, {"name": "mediaId", "type": "long", "default": 0}, {"name": "styleId", "type": "long", "default": 0}, {"name": "keyFrom", "type": "string", "default": ""}, {"name": "vendor", "type": "string", "default": ""}, {"name": "venderId", "type": "string", "default": ""}, {"name": "vender_source", "type": "string", "default": "", "doc": "source of vender"}, {"name": "supply_device_flag", "type": "boolean", "default": false}, {"name": "idfa", "type": "string", "default": ""}, {"name": "caid", "type": "string", "default": ""}, {"name": "caid_version", "type": "string", "default": ""}, {"name": "aaid", "type": "string", "default": ""}, {"name": "imei", "type": "string", "default": ""}, {"name": "oaid", "type": "string", "default": "", "doc": "open anonymous id"}, {"name": "dimei", "type": "string", "default": ""}, {"name": "supply_imei", "type": "string", "default": ""}, {"name": "supply_oaid", "type": "string", "default": ""}, {"name": "supply_idfa", "type": "string", "default": ""}, {"name": "deeplink_app", "type": "string", "default": ""}, {"name": "wechat_origin_id", "type": "string", "default": ""}, {"name": "dp_call_up", "type": "long", "default": 0}, {"name": "dp_installed", "type": "long", "default": 0}, {"name": "dp_success", "type": "long", "default": 0}, {"name": "dp_not_install", "type": "long", "default": 0}, {"name": "dp_installed_fail", "type": "long", "default": 0}, {"name": "apk_download_start", "type": "long", "default": 0}, {"name": "apk_download_complete", "type": "long", "default": 0}, {"name": "apk_install_start", "type": "long", "default": 0}, {"name": "apk_install_complete", "type": "long", "default": 0}, {"name": "wechat_call_up", "type": "long", "default": 0}, {"name": "wechat_success", "type": "long", "default": 0}, {"name": "wechat_fail", "type": "long", "default": 0}]}, {"doc": "brand valid check response time tracker, time between ad request and ad response", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "BrandAdValidTimeTracker", "fields": [{"name": "load_time", "type": "int", "default": 0, "doc": "time between ad request and ad response, in millisecond"}, {"name": "request_id", "type": "string", "default": ""}, {"name": "ip", "type": "string", "default": ""}, {"name": "os", "type": "string", "default": ""}, {"name": "app_id", "type": "long", "default": 0}, {"name": "slot_id", "type": "string", "default": ""}, {"name": "device_id", "type": "string", "default": ""}, {"name": "idfa", "type": "string", "default": ""}, {"name": "caid", "type": "string", "default": ""}, {"name": "alid", "type": "string", "default": ""}, {"name": "aaid", "type": "string", "default": ""}, {"name": "oaid", "type": "string", "default": ""}, {"name": "dimei", "type": "string", "default": ""}, {"name": "android_id", "type": "string", "default": ""}, {"name": "imei", "type": "string", "default": ""}, {"name": "province", "type": "int"}, {"name": "city", "type": "int"}, {"name": "network_type", "type": "string", "default": ""}, {"name": "device_name", "type": "string", "default": ""}, {"name": "carrier_name", "type": "string", "default": ""}, {"name": "guid", "type": "string", "default": ""}, {"name": "timestamp", "type": "long", "doc": "report to kafka timestamp in ergate"}, {"name": "av", "type": "string", "default": "", "doc": "app version"}, {"name": "nsv", "type": "string", "default": "", "doc": "sdk version"}, {"name": "osv", "type": "string", "default": "", "doc": "os version"}, {"name": "render_timestamp", "type": "long", "doc": "render timestamp in gorgon"}, {"name": "ext", "type": {"type": "map", "values": "string", "default": {}}, "default": {}}]}]