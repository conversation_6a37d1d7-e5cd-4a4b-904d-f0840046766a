[{"doc": "Mediation sdk report. http://confluence.inner.youdao.com/pages/viewpage.action?pageId=2535631, http://confluence.inner.youdao.com/pages/viewpage.action?pageId=14550358", "namespace": "com.youdao.quipu.avro.schema", "name": "MediationSdkReport", "type": "record", "fields": [{"name": "timestamp", "type": "long"}, {"name": "guid", "type": "string", "default": ""}, {"name": "gaid", "type": "string", "default": "", "doc": "device id"}, {"name": "config_id", "type": "string", "default": "", "doc": "config id."}, {"name": "dsp_name", "type": "string", "default": "", "doc": "dsp name"}, {"name": "dsp_slot", "type": "string", "default": "", "doc": "slot name in dsp"}, {"name": "status_code", "type": "int", "default": 0, "doc": "response code defined by specific dsp"}, {"name": "sdkv", "type": "string", "default": "", "doc": "sdk version"}, {"name": "app_id", "type": "string", "default": "", "doc": ""}, {"name": "appv", "type": "string", "default": "", "doc": "app version"}, {"name": "country", "type": "string", "default": "", "doc": ""}, {"name": "os", "type": "string", "default": "", "doc": ""}, {"name": "nt", "type": "string", "default": "", "doc": "network type"}, {"name": "adt", "type": "string", "default": "", "doc": "ad type"}, {"name": "sd_rsp_fail_code", "type": "string", "default": ""}, {"name": "sd_rsp_fail_msg", "type": "string", "default": ""}, {"name": "as_fill", "type": "int", "default": 0, "doc": "app wants to load an ad from sdk to fill ad cache"}, {"name": "app_req", "type": "int", "default": 0, "doc": "app wants an ad from sdk. should be 'as_load'"}, {"name": "as_loaded", "type": "int", "default": 0, "doc": "app receives an ad from sdk"}, {"name": "sdk_req", "type": "int", "default": 0, "doc": "sdk wants an ad from dsp. should be 'sd_req'"}, {"name": "sdk_res", "type": "int", "default": 0, "doc": "sdk receives an ad from dsp. should be 'sd_res'"}, {"name": "sd_rtload", "type": "int", "default": 0, "doc": "sdk wants an real time ad from dsp"}, {"name": "sd_rtloaded", "type": "int", "default": 0, "doc": "sdk receives an real time ad from dsp"}, {"name": "sd_greq", "type": "int", "default": 0, "doc": "group request from sdk to dsp"}, {"name": "sd_gres", "type": "int", "default": 0, "doc": "group response from dsp to sdk"}, {"name": "sc_filled", "type": "int", "default": 0, "doc": "sdk fills an ad into cache"}, {"name": "sc_load", "type": "int", "default": 0, "doc": "sdk wants an ad from cache"}, {"name": "sc_loaded", "type": "int", "default": 0, "doc": "sdk receives an ad from cache"}, {"name": "impr", "type": "int", "default": 0, "doc": "ad impression"}, {"name": "click", "type": "int", "default": 0, "doc": "ad click event"}, {"name": "timeout", "type": "int", "default": 0, "doc": "app wants an ad but timeout"}, {"name": "duration", "type": "long", "default": 0, "doc": "total from ad request to ad response received"}, {"name": "init_success", "type": "int", "default": 0, "doc": "SDK初始化成功"}, {"name": "init_fail", "type": "int", "default": 0, "doc": "SDK初始化失败"}, {"name": "pull_config_success", "type": "int", "default": 0, "doc": "pull config from backend success."}, {"name": "pull_config_fail", "type": "int", "default": 0, "doc": "pull config from backend failed."}, {"name": "sd_bid_price", "type": "double", "default": 0.0, "doc": "DSP bid price, 当DSP有响应时（sdk_res=1）可能设置此值"}, {"name": "sd_bid_win", "type": "int", "default": 0, "doc": "DSP wins a bid"}, {"name": "sd_bid_win_price", "type": "double", "default": 0.0, "doc": "DSP win with this bid price"}, {"name": "sd_app_fill_group_req", "type": "int", "default": 0}, {"name": "sd_app_fill_group_rsp", "type": "int", "default": 0}, {"name": "sd_group_real_time_load", "type": "int", "default": 0}, {"name": "sd_group_real_time_loaded", "type": "int", "default": 0}, {"name": "sd_sdk_fill_group_req", "type": "int", "default": 0}, {"name": "sd_sdk_fill_group_rsp", "type": "int", "default": 0}, {"name": "sd_response_empty", "type": "int", "default": 0, "doc": "set 1 when sd_rsp_fail_code/sd_rsp_fail_msg is not empty."}, {"name": "sd_response_timeout", "type": "int", "default": 0}, {"name": "sc_load_empty", "type": "int", "default": 0}, {"name": "sc_load_out_date", "type": "int", "default": 0}, {"name": "app_ad_show", "type": "int", "default": 0}, {"name": "app_ad_click", "type": "int", "default": 0}, {"name": "as_load_fail_timeout", "type": "int", "default": 0}, {"name": "as_load_fail_no_network", "type": "int", "default": 0}, {"name": "as_load_fail_no_rsp", "type": "int", "default": 0}, {"name": "as_rsp_fail_to_app", "type": "int", "default": 0}, {"name": "rsp_duration_ms", "type": "long", "default": 0}, {"name": "ad_out_date", "type": "int", "default": 0}]}]