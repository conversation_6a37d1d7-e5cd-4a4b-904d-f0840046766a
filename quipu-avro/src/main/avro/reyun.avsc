[{"doc": "youdao click for reyun record", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "<PERSON><PERSON><PERSON><PERSON>ord", "imports": ["common.avsc"], "fields": [{"name": "timestamp", "type": "long"}, {"name": "guid", "type": "string", "doc": "use pv_sdk request id"}, {"name": "yd_device_id", "type": "string", "default": "", "doc": "youdao venus userId"}, {"name": "device_id", "type": "string", "default": "", "doc": "imei_md5 or oaid_md5 or idfa_md5, for request reyun"}, {"name": "device_id_type", "type": "int", "default": -1, "doc": "1：idfa；2：imei；3：idfa-md5；4：imei-md5; 5: oaid-md5"}, {"name": "os", "type": "int", "default": -1}, {"name": "is_success", "type": "boolean", "default": false}, {"name": "req_camp_ids", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "hit_camp_ids", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "req_camp_id_count", "type": "int", "default": 0}, {"name": "hit_camp_id_count", "type": "int", "default": 0}, {"name": "bill_count", "type": "int", "default": 0}, {"name": "repeat_req_bill_count", "type": "int", "default": 0}, {"name": "cache_req_camp_ids", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "cache_hit_camp_ids", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "cache_req_camp_id_count", "type": "int", "default": 0}, {"name": "cache_hit_camp_id_count", "type": "int", "default": 0}, {"name": "pv", "type": "int", "default": 1}]}, {"doc": "youdao click dmp macros info", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "DmpMacros", "fields": [{"name": "timestamp", "type": "long"}, {"name": "yd_device_id", "type": "string", "default": "", "doc": "youdao venus userId"}, {"name": "idfa", "type": "string", "default": ""}, {"name": "caids", "type": {"type": "array", "items": "Caid"}, "default": [], "doc": "caid list"}, {"name": "ip", "type": "string", "default": ""}, {"name": "ua", "type": "string", "default": ""}, {"name": "os", "type": "string", "default": ""}, {"name": "device_model", "type": "string", "default": ""}, {"name": "camp_ids", "type": {"type": "array", "items": "string"}, "default": []}]}]