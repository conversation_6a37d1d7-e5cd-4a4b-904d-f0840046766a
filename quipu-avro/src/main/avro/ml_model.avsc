[{"doc": "FFM Model meta", "type": "record", "name": "FFMModelMeta", "namespace": "com.youdao.quipu.avro.schema.ml", "fields": [{"name": "sample_rate", "type": "double", "doc": "down sample rate of train data"}, {"name": "feature_conf", "type": {"type": "map", "values": "int", "doc": "feature name and field"}}, {"name": "eta", "type": "double", "default": 1.0, "doc": "learning rate"}, {"name": "feat_add_rate", "type": "double", "default": 1.0}, {"name": "model_size", "type": "long", "doc": "count of model param"}, {"name": "model_type", "type": "string", "doc": "deprecated, email of author", "default": ""}, {"name": "model_version", "type": "string", "doc": "model start train time, such as 2019-09-05-20"}, {"name": "online_rig", "type": "double"}, {"name": "last_train_data_path", "type": "string", "doc": "local train data path, such as /disk1/data/onehot"}, {"name": "use_ftrl_training", "type": "boolean"}, {"name": "fill_missing", "type": "boolean"}, {"name": "sample_size", "type": "long", "default": 0}, {"name": "positive_sample_size", "type": "long", "default": 0}, {"name": "raw_hdfs_train_data_path", "type": "string", "default": "", "doc": "root path of raw train data, such as hdfs://eadata-hdfs/quipu/ml_workflow/data/impr_click_joined/hourly/"}, {"name": "train_data_start_timestamp", "type": "long", "default": 0}, {"name": "train_data_end_timestamp", "type": "long", "default": 0}]}, {"doc": "FFM Model param", "type": "record", "name": "FFMModelParam", "namespace": "com.youdao.quipu.avro.schema.ml", "fields": [{"name": "feature", "type": "string", "doc": "feature name and feature value, such as city_v2#297"}, {"name": "params", "type": {"type": "array", "items": "float", "doc": "params of feature, length is latent factor size multiple field size"}}]}, {"namespace": "com.youdao.quipu.avro.schema.ml", "type": "enum", "name": "EncodeType", "symbols": ["DISCRETE", "CONTINUOUS"]}, {"namespace": "com.youdao.quipu.avro.schema.ml", "type": "enum", "name": "FeatVectorType", "symbols": ["INT", "LONG", "FLOAT"]}, {"namespace": "com.youdao.quipu.avro.schema.ml", "type": "record", "name": "FeatItem", "fields": [{"name": "size", "type": "int", "default": 1}, {"name": "name", "type": "string", "default": ""}, {"name": "index", "type": "int", "doc": "在所属特征向量里地起始下标（从0开始）"}]}, {"namespace": "com.youdao.quipu.avro.schema.ml", "type": "record", "name": "FeatVector", "fields": [{"name": "name", "type": "string", "default": ""}, {"name": "type", "type": "FeatVectorType", "default": "LONG"}, {"name": "encode_type", "type": "EncodeType", "default": "DISCRETE"}, {"name": "size", "type": "long", "default": 1}, {"name": "feat_items", "type": {"type": "array", "items": "FeatItem"}, "default": []}]}, {"namespace": "com.youdao.quipu.avro.schema.ml", "type": "record", "name": "DeepModelParam", "fields": [{"name": "name", "type": "string", "default": ""}, {"name": "version", "type": "long", "doc": "模型版本号"}, {"name": "sample_rate", "type": "float", "doc": "训练模型的负采样率，用于最后的预测值调整（如果有需要，推荐场景下一般不需要做）"}, {"name": "user_feat_vectors", "type": {"type": "array", "items": "FeatVector"}, "default": []}, {"name": "item_feat_vectors", "type": {"type": "array", "items": "FeatVector"}, "default": []}, {"name": "feature_dict", "type": ["null", {"type": "map", "values": "long"}], "default": null}]}]