[{"doc": "range condition", "namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "Range", "fields": [{"name": "min", "type": ["null", "int"], "default": null}, {"name": "max", "type": ["null", "int"], "default": null}]}, {"namespace": "com.youdao.quipu.avro.schema", "type": "record", "name": "StoredKolSearchHistory", "fields": [{"name": "guid", "type": "string", "doc": "唯一ID，可以用于消息去重"}, {"name": "timestamp", "type": "long", "doc": "搜索时间戳"}, {"name": "userId", "type": "long", "doc": "搜索人id"}, {"name": "keywords", "type": {"type": "array", "items": "string"}, "default": [], "doc": "搜索词"}, {"name": "searchType", "type": "int", "default": 1, "doc": "搜索类型：1-文本为kol的nickname/user_id/home_page_url，2-文本为media的title/description/tags，3-按广告主搜索，4-文本为media的品牌id，5-kol简介搜索"}, {"name": "interfaceName", "type": "string", "default": "", "doc": "调用的接口名：如KOL查询页面 - search，KOL编辑页面 - filter"}, {"name": "sortType", "type": "int", "default": 2, "doc": "排序方式：1-按合作优先排序，2-相关性排序，3-按粉丝数排序，4-按平均观看量排序，5-按kol创建时间排序，6-观看率降序；30-综合排序"}, {"name": "totalResultNum", "type": "long", "default": 0, "doc": "搜索返回结果数"}, {"name": "filter", "type": {"type": "record", "namespace": "com.youdao.quipu.avro.schema", "name": "SearchKolScreeningCondition", "fields": [{"name": "country", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "contentTag", "type": {"type": "array", "items": "long"}, "default": []}, {"name": "hiddenContentTag", "type": {"type": "array", "items": "long"}, "default": [], "doc": "后端根据前端的父标签隐式补全的子标签"}, {"name": "contentTagLogic", "type": "boolean", "default": true, "doc": "true: OR; false: AND"}, {"name": "contentTagExclude", "type": {"type": "array", "items": "long"}, "default": [], "doc": "按照content tag排除kol"}, {"name": "hiddenContentTagExclude", "type": {"type": "array", "items": "long"}, "default": [], "doc": "后端根据前端的exclude父标签隐式补全的exclude子标签"}, {"name": "contentTagExcludeLogic", "type": "boolean", "default": true, "doc": "true: OR; false: AND"}, {"name": "keywordsTag", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "keywordsTagLogic", "type": "boolean", "default": true, "doc": "true: OR; false: AND"}, {"name": "keywordsTagExclude", "type": {"type": "array", "items": "string"}, "default": [], "doc": "按照keywords tag排除kol"}, {"name": "keywordsTagExcludeLogic", "type": "boolean", "default": true, "doc": "true: OR; false: AND"}, {"name": "videoSubType", "type": "int", "default": 2, "doc": "视频子类型：-1-none，0-short，1-long，2-all"}, {"name": "minRefView", "type": ["null", "double"], "default": null, "doc": "最小参考曝光量"}, {"name": "max<PERSON>ef<PERSON>iew", "type": ["null", "double"], "default": null, "doc": "最大参考曝光量"}, {"name": "minFanNum", "type": "long", "default": 0}, {"name": "maxFunNum", "type": "long", "default": -1, "doc": "没有设置最大粉丝数时赋值为-1"}, {"name": "kolStatus", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "kolTag", "type": {"type": "array", "items": "long"}, "default": []}, {"name": "kolTagLogic", "type": "boolean", "default": true, "doc": "true: OR; false: AND"}, {"name": "platform", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "kolLevel", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "cooperationTypeList", "type": {"type": "array", "items": "int"}, "default": []}, {"name": "languageCode", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "intermediaryId", "type": {"type": "array", "items": "long"}, "default": []}, {"name": "amId", "type": {"type": "array", "items": "long"}, "default": []}, {"name": "mcnId", "type": {"type": "array", "items": "long"}, "default": []}, {"name": "specialOrder", "type": "string", "default": ""}, {"name": "isLooseCondition", "type": "boolean", "default": false}, {"name": "assignStart", "type": ["null", {"type": "long", "logicalType": "date"}], "default": null}, {"name": "obtainMailStart", "type": ["null", {"type": "long", "logicalType": "date"}], "default": null}, {"name": "obtainImStart", "type": ["null", {"type": "long", "logicalType": "date"}], "default": null}, {"name": "assignEnd", "type": ["null", {"type": "long", "logicalType": "date"}], "default": null}, {"name": "obtainMailEnd", "type": ["null", {"type": "long", "logicalType": "date"}], "default": null}, {"name": "obtainImEnd", "type": ["null", {"type": "long", "logicalType": "date"}], "default": null}, {"name": "hasQuote", "type": "boolean", "default": false}, {"name": "hasSaleQuote", "type": "boolean", "default": false, "doc": "该字段业务系统已废弃"}, {"name": "hasMcnQuote", "type": "boolean", "default": false}, {"name": "quoteStart", "type": ["null", {"type": "long", "logicalType": "date"}], "default": null}, {"name": "quoteEnd", "type": ["null", {"type": "long", "logicalType": "date"}], "default": null}, {"name": "fansCountry", "type": "string", "default": ""}, {"name": "fansAge", "type": "string", "default": ""}, {"name": "fansGender", "type": "string", "default": ""}, {"name": "hasMediaInDays", "type": ["null", "int"], "default": null, "doc": "最近n天是否发布过media"}, {"name": "hasMail", "type": ["null", "boolean"], "default": null, "doc": "是否有邮箱"}, {"name": "hasIm", "type": ["null", "boolean"], "default": null, "doc": "是否有im"}, {"name": "avgView30Day", "type": ["null", "Range"], "default": null, "doc": "最近30天media平均观看量"}, {"name": "avgView10Media", "type": ["null", "Range"], "default": null, "doc": "最近10条media平均观看量"}, {"name": "avgViewVolatility30Day", "type": ["null", "Range"], "default": null, "doc": "最近30天media平均观看量波动率"}, {"name": "avgViewVolatility10Media", "type": ["null", "Range"], "default": null, "doc": "最近10条media平均观看量波动率"}, {"name": "hasReferencePrice", "type": "boolean", "default": false}, {"name": "customerProductIds", "type": {"type": "array", "items": "int"}, "default": [], "doc": "产品id列表"}, {"name": "customerCompanyIds", "type": {"type": "array", "items": "int"}, "default": [], "doc": "广告主id列表"}, {"name": "customerIndustryIds", "type": {"type": "array", "items": "int"}, "default": [], "doc": "广告主行业id列表"}]}, "doc": "筛选条件"}]}]