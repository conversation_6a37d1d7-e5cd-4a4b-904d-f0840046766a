package com.youdao.quipu.avro.examples;

import com.google.common.io.Closeables;
import com.google.common.io.Closer;
import org.apache.commons.cli.*;

import java.io.*;
import java.util.zip.Deflater;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.InflaterInputStream;

/**
 * <AUTHOR>
 */
public class DeflateToDeflate {
    public static void main(String[] args) throws ParseException, IOException {
        Options options = new Options();
        options.addOption("i", true, "input text file name");
        options.addOption("o", true, "output avro file name");
        options.addOption("h", false, "help");

        PosixParser parser = new PosixParser();
        CommandLine cmd = parser.parse(options, args);
        if (cmd.hasOption("h")) {
            HelpFormatter hf = new HelpFormatter();
            hf.printHelp("help:", options);
            return;
        }
        long start = System.currentTimeMillis();
        Closer closer = Closer.create();
        try {
            InflaterInputStream iis = closer.register(new InflaterInputStream(new FileInputStream(cmd.getOptionValue("i"))));
            DeflaterOutputStream dos = closer.register(new DeflaterOutputStream(new FileOutputStream(cmd.getOptionValue("o")), new Deflater(6)));
            doCopy(iis, dos);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closer.close();
        }
        System.out.println(System.currentTimeMillis() - start);
    }

    public static void doCopy(InputStream is, OutputStream os)
            throws IOException {
        byte[] buffer = new byte[1024 * 1024];
        int len = 0;
        while ((len = is.read(buffer)) != -1) {
            os.write(buffer, 0, len);
        }
    }
}
