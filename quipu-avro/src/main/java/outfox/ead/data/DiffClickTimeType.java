package outfox.ead.data;

/**
 * 跨aid、sid、cid、pid等维度点击时间的差值
 *
 * <AUTHOR>
 * @create 2025-03-17
 */
public class DiffClickTimeType {

    public static final int SECONDS_0 = 100;
    public static final int SECONDS_1 = 120;
    public static final int SECONDS_5 = 200;
    public static final int SECONDS_10 = 220;
    public static final int SECONDS_20 = 240;
    public static final int SECONDS_30 = 260;

    public static final int MINUTES_1 = 300;
    public static final int MINUTES_2 = 320;
    public static final int MINUTES_5 = 340;
    public static final int MINUTES_10 = 360;
    public static final int MINUTES_30 = 400;

    public static final int HOURS_1 = 500;
    public static final int HOURS_2 = 520;
    public static final int HOURS_4 = 540;
    public static final int HOURS_8 = 580;
    public static final int HOURS_12 = 600;

    public static final int HOURS_24 = 700;

    /**
     * 没有明确的信息判定时间差结果，代表情况未知
     */
    public static final int UNKNOWN = -1;


    public static int getDiffClickTime(long diffClickTime) {
        if (diffClickTime <= 1000L) {
            return SECONDS_1;
        } else if (diffClickTime <= 5 * 1000L) {
            return SECONDS_5;
        } else if (diffClickTime <= 10 * 1000L) {
            return SECONDS_10;
        } else if (diffClickTime <= 20 * 1000L) {
            return SECONDS_20;
        } else if (diffClickTime <= 30 * 1000L) {
            return SECONDS_30;
        } else if (diffClickTime <= 60 * 1000L) {
            return MINUTES_1;
        } else if (diffClickTime <= 2 * 60 * 1000L) {
            return MINUTES_2;
        } else if (diffClickTime <= 5 * 60 * 1000L) {
            return MINUTES_5;
        } else if (diffClickTime <= 10 * 60 * 1000L) {
            return MINUTES_10;
        } else if (diffClickTime <= 30 * 60 * 1000L) {
            return MINUTES_30;
        } else if (diffClickTime <= 60 * 60 * 1000L) {
            return HOURS_1;
        } else if (diffClickTime <= 2 * 60 * 60 * 1000L) {
            return HOURS_2;
        } else if (diffClickTime <= 4 * 60 * 60 * 1000L) {
            return HOURS_4;
        } else if (diffClickTime <= 8 * 60 * 60 * 1000L) {
            return HOURS_8;
        } else if (diffClickTime <= 12 * 60 * 60 * 1000L) {
            return HOURS_12;
        } else if (diffClickTime <= 24 * 60 * 60 * 1000L) {
            return HOURS_24;
        } else {
            return UNKNOWN;
        }
    }

    public static int maxDiffClickTimeId() {
        return UNKNOWN;
    }
}
