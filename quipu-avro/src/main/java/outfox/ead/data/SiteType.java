/**
 * @(#)SiteType.java, 2014-12-05
 *
 * Copyright 2014 Youdao, Inc. All rights reserved.
 * YOUDAO PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package outfox.ead.data;
/**
 * 目前四种渠道方式MUCS分别表示邮件、联盟、频道和搜索，0表示可以投放，1表示不能投放 <br />
 * 
 * <AUTHOR>
 */
public class SiteType {

    /**
     * 搜索
     */
    public static final int EADS = 1;

    /**
     * 频道
     */
    public static final int EADC = 2;

    /**
     * 联盟
     */
    public static final int EADU = 4;

    /**
     * 邮件
     */
    public static final int EADM = 8;

    /**
     * 词典
     */
    public static final int EADD = 16;
    
    /**
     * Ad Exchange，包括：Tanx/Adx/TADE等
     */
    public static final int AD_EXCHANGE = 32;
    
    /**
     * 词典看天下广告
     */
    public static final int EADV = 64;

    /**
     * 线下直销的syndId
     * 
     * @see {@link ChannelType}
     */
    public static final int EADCU_SYND_ID = 16;

    /**
     * 频道左侧的syndId
     * 
     * @see {@link ChannelType}
     */
    public static final int EADC_LEFT_SYND_ID = 14;

    /**
     * 频道右侧的syndId
     * 
     * @see {@link ChannelType}
     */
    public static final int EADC_RIGHT_SYND_ID = 15;

    /**
     * 有道自有媒体的syndId
     * 
     * @see {@link ChannelType}
     */
    public static final int EADOM_SYND_ID = 17;

    /**
     * 网易各类媒体的syndId
     * 
     * @see {@link ChannelType}
     */
    public static final long EADCN_SYND_ID = 18;

    /**
     * 维护一份全局的统一类，以后的修改可以反映到全局
     */
    public static int getSiteType(long syndId) {
        if (syndId == 1) {// YOUDAO
                return EADS;
        } else if (syndId >= 5 && syndId <= 10) {// Mail
                return EADM;
        } else if (syndId > 10 && syndId < 20) {// 163
                return EADC;
        }else if (syndId >= 50 && syndId <= 100) {// dict
                return EADD;
        }else if (syndId > 100 && syndId <= 150) {// ad exchange
            return AD_EXCHANGE;
        }else if (syndId > 150 && syndId <= 200){
            return EADV;
        }
        else if (syndId >= 1000) {// Union
                return EADU;
        }
        return -1;
    }

    public static long getChannelId(long syndId) {
        return getSiteType(syndId);
    }

    public static boolean ableToDeliver(int siteType, int deliveryType) {
        if ((siteType & deliveryType) != 0)
            return true;
        else
            return false;
    }

    public static boolean ableToDeliver(long syndId, int deliveryType) {
        int siteType = getSiteType(syndId);
        return ableToDeliver(siteType, deliveryType);
    }

    public static String getSiteTypeDesc(int siteType) {
        if (siteType == EADS) {
            return "搜索";
        } else if (siteType == EADU) {
            return "联盟";
        } else if (siteType == EADC) {
            return "频道";
        } else if (siteType == EADD) {
            return "词典";
        } else if (siteType == EADM) {
            return "邮件";
        } else if (siteType == AD_EXCHANGE) {
            return "AD_EXCHANGE";
        } else if (siteType == EADV){
            return "看天下广告";
        } else if (siteType == all()) {
            return "所有平台";
        }
        return "未知平台";
    }

    public static void main(String[] args) {
        System.out.println(EADS);
    }

    public static int all() {
        return EADS | EADU | EADC | EADD | EADM | AD_EXCHANGE | EADV | ChannelType.EADCU.getSiteTypes();
    }

    public static int getSiteType(String siteType) {
        if (siteType == null || siteType.trim().length() == 0) {
            return all();
        }
        siteType = siteType.toUpperCase();
        if (siteType.compareTo("EADS") == 0) {
            return EADS;
        } else if (siteType.compareTo("EADU") == 0) {
            return EADU;
        } else if (siteType.compareTo("EADC") == 0) {
            return EADC;
        } else if (siteType.compareTo("EADD") == 0) {
            return EADD;
        } else if (siteType.compareTo("EADM") == 0) {
            return EADM;
        } else if (siteType.compareTo("AD_EXCHANGE") == 0) {
            return AD_EXCHANGE;
        }else if ("EADV".equals(siteType)){
            return EADV;
        } else {
            return all();
        }
    }
}
