# 0.11.0
* 功能
  - 支持互动广告
    [云笔记](http://note.youdao.com/noteshare?id=ed67cf606e8a7eba4dd0a8753f068026)。

# 0.11.1
* 功能
  - 互动广告InteractiveAd添加GUID。
* BugFix
  - iad_charge 提供默认值。
  
# 0.11.2
* 功能
  - [支持品牌广告点击和展示的统计](https://dev.corp.youdao.com/outfoxwiki/huqj/brand-new-stat)

# 0.13.0
* 功能
  - SdkPv中添加referer相关信息。[sdk_stat_new表的aggregation新增点击referer项](http://jira.corp.youdao.com/browse/QUIPU-70)。
  
# 0.14.0
* 功能
  - SdkPv中增加is_secure字段，判断广告位是否要求返回的广告物料的链接都是https链接。

# 0.15.0
* 功能
  - SdkBid中添加candidate_size字段用以记录每次参与竞价的候选广告总数

# 0.16.0
* 功能
  - wiki: [用户点击数 vs. 广告请求数、第三方入口点击上报](https://dev.corp.youdao.com/outfoxwiki/liuhaibo/iad#A.2BhD1XMJh1-pv_vs._.2BdShiN3C5Ufs-)
  - 增加互动广告外部媒体上报avro；
  - 互动广告增加用户点击次数；

# 0.17.0
* 功能
  - bid 日志添加转化率预测算法版本
* 文档
  - [监控线上预测与线下预测](http://confluence.inner.youdao.com/pages/viewpage.action?pageId=2529793)

# 0.18.0
* 功能
  - DspConv增加conv_type和conv_sub_type字段。

# 0.19.0
* 功能
  - 新增素材id到bid/impr/click/conv；

# 0.20.0
* 功能
  - 新增abx_test_marks到sdk/dsp的bid/impr/click/conv；

# 0.21.0
* 功能
  - DspBid/DspImpr/DspClick/DspConv/SdkBid/SdkImpr/SdkClick/SdkConv增加enable_deep_link属性记录广告组是否开启应用直达

# 0.23.0
* 功能
  - 新增 enable_convert_tracking, optimization_goal, ocpc_phase 到 bid/impr/click/conv

# 0.24.0
* 功能
  - 新增场景scenario ids到pv/bid/impr/click/conv；

# 0.25.0
* 功能
  - 新增AppDownloadStatus和DeepLinkInvokeStatus。
  
# 0.26.0
* 功能
  - 新增流量匹配相关性tra_relevancy、广告组相关性定向下限min_relevancy到bid/impr/click/conv；

# 0.27.0
* 功能
  - AppDownloadStatus和DeepLinkInvokeStatus增加dsp_id区分广告来源哪个dsp。

# 0.28.0
* 功能
  - AppDownloadStatus和DeepLinkInvokeStatus增加count字段用于druid统计总数
  
# 0.29.0
* 功能
  - brand 点击展示新增设备号，以统计独立设备数
 
# 0.30.0
* 功能
  - yex记录dealId

# 0.31.0
* 功能
  - SdkLandPage 添加转化事件

# 0.32.0
  - 增加聚合sdk上报；

# 0.33.0
  - 增加落地页短信验证信息；
  
# 0.34.0
* 功能
  - yex记录样式id
  
# 0.35.0
* 功能
  - yex的YexBidRequestLog增加多值的模板维度
  
# 0.36.0
* 功能
  - bid log 增加ct_action
    
# 0.37.0
* 功能
  - 增加 ElectorRanking 记录排序顺序

# 0.38.0
* 功能
  - bid 日志添加 min_cpc, min_cpm

# 0.39.0
* 功能
  - bid日志增加original_actual_cpc

# 0.40.0
* 功能
  - pv 日志添加 match_id(点击率算法ID), cvr_alg_id(转化率算法ID)

# 0.41.0
* 功能
  - SdkImpr, SdkClick, SdkConv添加模板id字段

# 0.42.0
* 功能
  - DspConv添加order_amount转化订单金额字段

# 0.43.0
* 功能
  - 添加机器学习模型描述 avro 定义
 
# 0.44.0
* 功能呢
  - SdkConv添加order_amount转化订单金额字段

# 0.45.0
* 功能
  - SdkPv, SdkBid, SdkImpr, SdkClick, SdkConv新增oaid字段

# 0.46.0
* 功能
  - 增加竞价预估消费BidPredictCost
  - SdkClick增加字段bid_click_delay_seconds,表示竞价到点击的延迟时间，单位是秒

# 0.46.1
* 功能
  - SdkBid增加广告位展示率：slot_impr_ratio,预测点击率：ctr，预测竞价消费：bid_predict_cost
  
# 0.47.0
* 功能
  - 增加BidTimeClick,时间戳设置的是该点击对应的竞价时的时间戳

# 0.48.0
* 功能
  - 新增abx_test_marks到sdk的pv

# 0.48.1
* BugFix
  - conv_cost/loc_conv_cost defalut value is 0.

# 0.49.0
* 功能
  - 增加聚合sdk上报V2数据结构；

# 0.50.0
* 功能
  - DspConv添加ct_time转化发生时间字段

# 0.51.0
* 功能
  - yex添加一个`hostname`维度用于区分统计不同yex实例的数据

# 0.52.0
* 功能
  - yex统计增加adx扣费维度
  
# 0.53.0
* 功能
  - 新增tiny-url.avsc统计短链访问次数
  
# 0.54.0
* 功能
  - sdk.avsc 新增下载类广告对应的应用包名 ad_app_package_name  
  
# 0.55.0
* 功能
  - 新增course.avsc 保存精品课外部投放展示点击  

# 0.56.0
* 功能
  - 新增surefire bid log.
  
# 0.58.0
* 功能
  - third-party-avsc 第三方平台监测增加广告主信息
    
# 0.58.1
* 功能
  - third-party-avsc 第三方平台监测扩展设备 oaid 
  
# 0.59.0
* 功能
  - course.avsc 增加CourseOrderJoined保存精品课订单与精品课回传点击JOIN后的数据

# 0.60.0
* 功能 
  - adx传来的app列表加入日志；

# 0.61.0
* 功能
  - [聚合SDK 1.3.2版本打点需求](http://confluence.inner.youdao.com/pages/viewpage.action?pageId=22009911)

# 0.62.0
* 功能 
  - 增加course-prophet bid log;

# 0.63.0
* 功能 
  - course-prophet bid log增加查词特征等;
  
# 0.65.0
* 功能
  - 支持中国广告协会互联网广告标识（CAID）。
  
# 0.66.0
* 功能
  - 添加 alid 支持阿里广告ID(aaid) 区别于谷歌标准的 aaid(android advertisment ID)

# 0.67.0
* 功能
  - 支持统计yex响应解析错误原因

# 0.67.2
* 功能
  - 添加平滑消费 trace record。

# 0.68.0
* 功能
  - 平滑消费 trace record 添加 alpha 值。

 # 0.69.0
 * 功能 
   - SdkBid中添加actual_sponsor_size和actual_group_size字段用以记录排序前（截断后）参与排序的实际的广告商、广告组数

# 0.70.0
* 功能
  - sdk bid, click, impr 新增微信小程序字段

# 0.71.0
* 功能
  - brand 添加styleId，mediaId,brandClkType,deliveryType,billingType 字段，优选统计需求
  
# 0.74.0
* 功能
  - yex.avsc 新增 YexDebugLog Avro

# 0.75.0
* 功能
  - 增加DSP Taboola的user.session的record
  - 
# 0.76.0
* 功能
  - 增加 wikte kol 及 witake media
  
# 0.77.0
* 功能
  - 词典开屏展示上报记录keyFrom

# 0.77.1
* 功能
  - 记录词典开屏pv,bid

# 0.78.0
* 功能
  - 统计新增记录gorgon host字段

# 0.78.1
* 功能
  - yex_bid统计，dsp响应状态补全

# 0.78.2
* 功能
  - 词典开屏pv bid 设备id字段名修改

# 0.79.0
* 功能
  - 添加 dict prophet bid avro schema。

# 0.80.0
* 功能
  - YexBidInvalidReason 新增安卓应用下载型广告应用信息不完整非法原因

# 0.80.1
* 功能
   - 为 YEX bid 添加 bid_count_invalid_download_app_info。
  
# 0.81.0
* 功能
  - ml_model.avsc增加DeepModel

# 0.81.1
* 功能
  - 更新 dict prophet bid avro schema。
  - [wiki](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=165754798)

# 0.81.2
* 功能
  - 添加词典社区日志schema。
  - [wiki](https://jira.inner.youdao.com/browse/DICTRECSYS-4)

# 0.81.3
* bugfix
  - 词典社区日志schema字段修改

# 0.81.4
* bugfix
  - 词典社区日志schema字段修改,request_id -> requestid

# 0.81.5
* bugfix
  - 品牌pv记录user-id

# 0.81.7
* bugfix
  - 词典社区日志schema字段，新增score、model、client

# 0.81.8
* 功能
  - dict prophet bid avro schema 添加slot,return_size,candidate_size字段。[jira](https://jira.inner.youdao.com/browse/DICTRECSYS-15)

# 0.82.1
* 功能
  - dict prophet bid avro schema 添加排序分桶字段。[jira](https://jira.inner.youdao.com/browse/DICTRECSYS-18)

# 0.83.0
* 功能
  - [V1.3-vender的投放和统计与自然量拆分](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=177663935)
  - 新增vender_id和vender_source

# 0.83.1
* 功能
  - wow-outlog 添加flink任务并行度id字段flink_index，方便后续排查数据倾斜问题。

# 0.84.0
* 功能
  - dict prophet bid avro schema 添加视频时长字段、关注作者列表字段。
  - 添加dnn模型排序多输出结果map，添加输出结果计算权重的配置map。
  - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-26)

# 0.84.1
* 功能
  - wow-outlog 添加排序abtests分组字段。[jira](https://jira.inner.youdao.com/browse/DICTRECSYS-29)

# 0.85.0
* 功能
  - 新增 hasReferencePrice 参数
  - [Kol查询中销售报价调整](https://https://confluence.inner.youdao.com/pages/viewpage.action?pageId=183774690)
  - [技术方案](https://https://confluence.inner.youdao.com/pages/viewpage.action?pageId=183781926)
  - [jira](https://jira.inner.youdao.com/browse/WITAKE-1602)

# 0.85.1
* 功能
  - wow-outlog 添加druid使用字段。[jira](https://jira.inner.youdao.com/browse/DICTRECSYS-18)

# 0.85.2
* 功能
  - wow-outlog 添加relatedid字段。[jira](https://jira.inner.youdao.com/browse/DICTRECSYS-31)

# 0.85.3
* 功能
  - dict_prophet_bid 添加ext字段，用于记录帖子特征新版数据。[jira](https://jira.inner.youdao.com/browse/DICTRECSYS-12)

# 0.86.1
* 功能
  - witake_kol 新增likes和following_count字段。
  - [技术文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=188793303)
  - [jira](https://jira.inner.youdao.com/browse/WITAKE-1629)
  - [TikTok数据更新需求](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=181546472)

# 0.87.1
* 功能
  - dict_prophet_bid 添加user_type字段，用于记录区分新老用户。
  - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-35)
  
 # 0.87.2
 * 功能
   - dict_prophet_bid 添加atchers字段，表示主要阶段的服务处理时间
   - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-35)


# 0.87.3
* 功能
  - 增加词典社区服务端下发客户端，再作为客户端日志字段上报回来的字段，结构为map对象的json格式
  - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-35)

# 0.87.4
* 功能
  - 增加rta_record avro

# 0.87.5
* 功能
  - 词典社区：添加用户观看沉浸流的related_id和related_id相对应的作者related_publisher
  - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-44)

# 0.88.0
* 功能
  - [新增品牌广告新预取接口错误上报统计](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=198284614)

# 0.88.1
* 功能
  - rta 支持广告主指定rtaId 和账户id 
  - [jira](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=198284614)

# 0.88.3
* 功能
  - 词典社区：社区新内容保量策略基础功能，添加帖子分数调权的abtest。
  - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-46)


# 0.88.4
* 功能
  - 词典社区：wow-outlog增添字段
  - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-47)
  
# 0.88.5
* 功能
  - 补全所有log的vender、dealId属性

# 0.89.0
* 功能
  - 新增海外短链点击上报的 avro

# 0.89.1
* 功能
  - 词典社区：用户点击行为序列添加字段
  - [jira](https://jira.inner.youdao.com/browse/DICTRECSYS-49)

# 0.90.0
* 功能
  - KOL搜索记录新增区分调用页面入口的interfaceType字段
  - [jira](https://jira.inner.youdao.com/browse/WITAKE-2107)

# 0.91.0
* 功能
  - 第三方点击转化，增加推广平台创意等字段
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5018)

# 0.92.0
* 功能
  - 建立设备信息资源库，新增设备信息相关字段
  - [prd](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=226263770)

# 0.93.0
* 功能
  - rta请求记录增加推广活动id
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3036)

# 0.94.0
* 功能
  - 第三方转化统计，新增need_conv_callback字段，用于标示此次转化是否回报给第三方
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3038)

# 0.95.0
* 功能
  - 新增kol搜索相关的过滤/排序项；

# 0.96.0
* 功能
  - sdk pv新增更多设备相关字段
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3046)

# 0.97.0
* 功能
  - brandPv增加词典标签数据

# 0.98.0
* 功能
  - rta record 新增字段 from_cache
  - jira：[https://jira.inner.youdao.com/browse/BIDSYSTEM-3051](https://jira.inner.youdao.com/browse/BIDSYSTEM-3051)

# 0.98.5
* 功能
  - 第三方转化topic增加省市，手机品牌型号等信息
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5121)

# 0.99.0
* 功能
  - 第三方点击增加扩展字段tdp_ext
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3074)
  
# 0.100.0
* 功能
  - 统计增加是否补充设备号标识
  - [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1034)

# 0.101.0
* 功能
  - rta record 新增字段 resp_cache_ttl
  - jira：[https://jira.inner.youdao.com/browse/BIDSYSTEM-3100](https://jira.inner.youdao.com/browse/BIDSYSTEM-3100)

# 0.102.0
* 功能
  - witake media新增爬取时间等字段；

# 0.103.0
* 功能
  - witake media以爬取时间作为落地分区列；

# 0.104.0
* 功能
  - sdk click 新增字段 imei_md5、android_id_md5

# 0.105.0
* 功能
  - BrandImpr表新增频控相关字段

# 0.105.1
* 功能
  - BrandImpr表新增supply_imei、supply_idfa、supply_oaid字段


# 0.107.0
* 功能
  - SdkFailBid 新增 RuleFilter 过滤的 ruleId

# 0.107.1
- 修改
  - rule_filter_rule_id 类型：long -> int

# 0.108.0
* 功能
  - [V1.0-流量质量数据分析和管理看板](https://jira.inner.youdao.com/browse/YEX-301)

# 0.109.0
* 功能
  - 新增海外归因平台数据对接的 avro
  - [JIRA](https://jira.inner.youdao.com/browse/WITAKE-2627)

# 0.109.1
* fix
  - 驼峰转下划线

# 0.110.0
* 功能
  - 为渠道点击事件增加model参数
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5186)

# 0.111.0
* 功能
  - [V1.0-流量质量数据分析和管理看板](https://jira.inner.youdao.com/browse/YEX-301)
* fix
  - 指标修正

# 0.112.0
* 功能
  - [V1.0-流量质量数据分析和管理看板](https://jira.inner.youdao.com/browse/YEX-301)
* fix
  - 新增dsp_ids

# 0.114.0
* 功能
  - [品牌和效果广告新增点击交互类型维度](https://jira.inner.youdao.com/browse/YOUXUAN-1113)

# 0.115.0
* 功能
  - bid-fail增加pv

# 0.116.0
* 需求
  -  [品牌广告点击事件日志增加bid_id、设备号等信息](https://jira.inner.youdao.com/browse/YOUXUAN-1125)

# 0.117.0
* 需求
  -  yex 日志增加ivt_tags、 ivt_count、province、city 字段
  
# 0.117.1
*  bugfix
  - yex_stat ivt_count字段名称问题修复，区分字段的不同业务含义

# 0.118.0
*  需求
  - [InstalledApp 补充设备号、操作系统等字段](https://jira.inner.youdao.com/browse/YOUXUAN-1118)
  - [品牌增加异常url日志](https://jira.inner.youdao.com/browse/YOUXUAN-1120)

# 0.119.0
* 需求
  - 新增common-tracker.s接口，用于接受各种触发事件上报
  - [文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=254062677)

# 0.121.0
* 需求
  - 新增click-to-sponsor.avsc，添加发送到广告商失败点击收集。

# 0.122.0
* 需求
  - yex.avsc 增加YexInvalidTraffic，记录被屏蔽的请求数据 

# 0.123.0
* 需求
  - 统计 dsp 在 adx 那边的竞胜数
  - PRD：https://confluence.inner.youdao.com/pages/viewpage.action?pageId=260339906

# 0.124.0
* 需求
  - 海外kol搜索II+

# 0.125.0
* 需求
  - YexAssetLog 增加 landingUrl/falllbackUrl 字段

# 0.126.0
  - 优选品牌广告海外地域定向投放需求，pv日志增加国家id字段

# 0.127.0
* 功能
  - YexImprLog 新增 adx_bid_price 字段

# 0.127.1
* bugfix
  - YexImprLog adx_bid_price -> impr_bid_price

# 0.128.0
* 需求
  - YexBid 新增 adx 出价价格

# 0.129.0
* 需求
  - rta_record 增加设备号字段

# 0.130.0
* 需求
  - sdk 增加 SdkWinNotice 日志

# 0.131.0
* 需求
  - brand bid，impr，click 日志添加ip定位的国家id。

# 0.131.1
* 修改
  - 将 SdkImpr 的 bid_price -> bid_price_of_impr，防止统计时重复

# 0.132.0
* 需求
  - Yex新增brandSourceSlotId字段，标识程序化广告中映射前原始效果广告位id

# 0.132.1
* 需求
  - Yex brandSourceSlotId字段设置默认值

# 0.133.0
* 需求
  - 海外KOL搜索新增频道关键词查询

# 0.134.0
* 需求
  - [gorgon支持媒体传拼多多paid及其版本号](https://jira.inner.youdao.com/browse/YEX-357)
  - [gorgon支持生成paid](https://jira.inner.youdao.com/browse/YEX-358)
  
# 0.135.0
* 需求  
  - sdkClick 增加caid, idfaMd5, oaidMd5, caidMd5 等字段
  - 
# 0.135.0
* 需求
  - 新增热云api请求记录

# 0.136.0
* 需求
  - [yex新增app版本统计维度](https://jira.inner.youdao.com/browse/YOUXUAN-1224)
  - yex新增记录全部展示图片\视频物料url的topic:yex_impr_asset_log

# 0.137.0
* 需求
  - 增加rta竞价日志，统计参竞率
  - 渠道点击日志增加rta_bid_count，计算点击关联rta的比例

# 0.139.0
* 功能
  - YexImprLog、YexAdxBidLog 新增字段 developer_id

# 0.140.0
* 需求
  - SdkPv 将 ext 中的 model 和 brand 提取出来


# 0.141.0
* 需求
  - third-party-click topic增加无效点击数和无效原因

# 0.141.1
* 需求
  - 新增sdk曝光、点击数据+tdp点击、转化数据大表：SdkTdpTogether

# 0.142.0
* 需求
  - brandPv,brandImpr,brandClick记录caids

# 0.142.1
* 功能
  - ReyunRecord 新增字段 repeat_req_bill_count
  
# 0.143.0
* 功能
  - [品牌广告频控新增对md5类型设备号的支持](https://jira.inner.youdao.com/browse/YOUXUAN-1255)

# 0.144.0
* 功能
  - 新增人群画像分析平台数据格式定义；
  - 品牌广告展示点击日志增加人群画像分析所需字段。

# 0.145.0
* 功能
  - 品牌点击与展示表新增用户特征字段

# 0.146.0
* 功能
  - kol搜索：上报隐式标签；

# 0.146.1
* 修改
  - 人群画像分析平台数据格式定义删除，

# 0.146.0
* 功能
  - kol搜索：上报标签与或非逻辑；

# 0.148.0
* 功能
  - 品牌pv新增dimei字段
  - yex全流程统计新增人群定向标签类型brand_orientation_type和品牌推广组id：ad_group_id字段

# 0.149.0
* 功能
  - 品牌统计新增age_v2字段，记录请求的具体年龄值

# 0.150.0
* 功能
  - witake media新增收藏数
  
  
# 0.151.0
* 功能  
  - third-party avro add "api_mode", "rta_enabled" dimension
  - sdk avro add  "rta_enabled" dimension

# 0.152.1
* 功能
  - third-party avro add 省市信息
  - sdk-tdp 汇总信息对有道自有点击增加统计维度

# 0.153.0
- 需求
- [关键词流量匹配策略V1.0](https://jira.inner.youdao.com/browse/ZHIXUAN-5335)
- 文档
  - [文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=290991498)

# 0.154.0
* 功能
  - yex 展示上报、点击上报等记录 caids
  - sdk pv、bid、展示、点击、转化记录 caids
  - third party 展示、点击、转化记录 caids

# 0.155.0
* 功能
  - 品牌统计新增随机年龄和性别维度

# 0.156.0
* 功能
  - YexImprLog 新增字段 bid_response_time

# 0.157.0
* 功能
  - yex无效原因新增主动无效类型字段：INVALID_YOUDAO_INITIATIVE

# 0.158.0
* 功能
  - thrid-party点击转化日志，增加智选转化加速器功能相关信息

# 0.159.0
* 需求
  - yex新增指标caid填充率

# 0.160.0
  - [V1.1-智选支持关键词广告投放](https://jira.inner.youdao.com/browse/ZHIXUAN-5350)

# 0.161.0
- kol波动率搜索

# 0.162.0
- 增加apk安装事件avro

# 0.163.0
* 需求
  - 增加reyun-dmp设备号的各项点击上报宏参数日志格式定义

# 0.163.1
* 需求
  - SdkTdpTogether增加第三方转化设备号，用于统计

# 0.164.0
  - [YEX素材转换器](https://jira.inner.youdao.com/browse/YEX-417)

# 0.165.0
  - yex_bid_flat 记录 bundle

# 0.166.0
  - 新增对品牌广告有效性验证接口的加载时间统计表BrandAdValidTimeTracker

# 0.167.0
  - ThirdPartyClick 增加重复点击数字段：repeat_click

# 0.168.0
  - YexImprLog 增加智选广告商 ID 字段：sponsor_id

# 0.169.0
  - add android id md5

# 0.170.0
  - YexAdxBidLog 增加 bidid、设备号等信息

# 0.171.0
  - ThirdPartyClick 增加重复点击次数字段：repeat_times

# 0.172.0
  - brand pv新增对品牌广告人群包id列表字段的记录

# 0.172.1
  - ThirdPartyClick 去除重复点击次数字段：repeat_times

# 0.173.0
  - yex 统计添加 dsp_ad_type 维度
  - jira：https://jira.inner.youdao.com/browse/YEX-433
- add android id md5

# 0.173.0
- brand pv新增对品牌广告人群包id列表字段的记录

# 0.174.0
- sdk\yex\brand的pv、bid、impr、click表新增对gorgon接口字段的记录gorgon_interface_type

# 0.175.0
* 需求
  - 渠道点击上报增加一系列流量质量相关指标

# 0.176.0
* 需求
  - yex 点击上报记录点击坐标参数结果
  - jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5444

# 0.177.0
* 需求
  - 渠道点击上报增加 android_click、ios_click、android_id_filled 指标

# 0.178.0
* 优化
  - [品牌广告线上统计有收到测试数据](https://jira.inner.youdao.com/browse/YOUXUAN-1434)

# 0.179.0
* 需求
  - third party avro add filterNoBidConv filed
  - [doc](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=323370718)

# 0.180.0
* 需求
  - 海外kol搜索支持多关键词

# 0.181.0
* 需求
    - 品牌点击展示新增三个字段：记录接受服务地址的service_host、竞价时间bid_timestamp、客户端展示时间client_impr_timestamp

# 0.182.0
* 需求
  - SdkFailBid 添加 app_id, 各类设备 id, filter_sub_reason
  - 各日志添加 strategy_id

# 0.182.1
* bugfix
  * sdk-report-data 中添加 strategy_id

# 0.183.0
* 需求
  * third_rta_bid 增加缓存字段

# 0.184.0
* 需求
  * rta_record avro增加caids, ext等字段

# 0.185.0
* 需求
  - yex bid、impr 增加 cur、to_cny_factor 和 original_cur_price 字段

# 0.186.0
* 需求
  - brand impr 增加 cpm 字段

# 0.187.0
* 需求
  - bs的bid日志增加新的广告商行业id信息。
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3256)

# 0.188.0
* 需求
  - sdk impr、conv日志增加归因方式字段
  - [doc](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=331096120)

# 0.189.0
* 需求
  - YexAdxBidLog 添加 bid_timestamp

# 0.190.0
* 需求
  - yex 统计竞价响应快应用广告非法数
  - jira：https://jira.inner.youdao.com/browse/YEX-459

# 0.191.0
- yex pv 增加 country_name、province_name、city_name

# 0.192.0
- third party conv 增加 click_guid

# 0.192.1
- 增加转化加速器中间数据定义

# 0.193.0
- 增加转化留存天数字段
- [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5686)

# 0.193.1
- SdkFailBid 增加 bs_host 维度

# 0.194.0
* 需求
  - 渠道点击、展示、转化增加渠道唯一 did
  - jira：https://jira.inner.youdao.com/browse/ZHIXUAN-5708

# 0.194.1
* 需求
  - 转化加速器增加appid信息

# 0.194.2
* 需求
  - 转化加速器增加一些设备id信息作为宏参数 和 渠道唯一did统计维度

# 0.195.0
* 需求
  - 增加third_party_click topic的反作弊点击等字段

# 0.196.0
* 需求
  - 增加interest_pv topic的新的定向维度字段

# 0.197.0
* 需求
  - 增加oimage_stat_conv topic的转化相关字段

# 0.198.0
* 需求
  - 增加渠道点击过滤相关统计字段
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5788)

# 0.198.1
* bugfix
  - oimage_stat schema 为ct_action添加默认值

# 0.198.2
* 需求
  - third party的rta bid record增加操作系统一列

# 0.199.0
* 需求
  - sdk_pv sdk_bid中增加YDID。
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5828)

# 0.200.0
* 需求
  - sdk 中有地域信息的 Record 增加一个country字段
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5857)

# 0.201.0
* 需求
  - image_stat schema 增加scenario_ids

# 0.202.0
* 需求
  - third_rta_bid 增加api_mode字段，区分rta请求

# 0.203.0
* 需求
  - sdk_tdp_together 增加点击转化（loc_conv）数据
  
# 0.204.0
* 需求
  -  inmobi support banner
  
# 0.205.0
* 需求
  - 使用dp吊起app或者投放小程序时，支持客户使用req_id归因并回传
  - [开发文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=346312563)
  - [prd](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=341704224)

# 0.206.0
* 需求
  - SdkImpr 添加独立指标impr_dedup用于统计去重的展示量
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-5907)

# 0.207.0
* 需求
  - 补充sdk异常日志埋点字段

# 0.208.1
* 需求
  - 补充转化加速器算法数据的分桶id

# 0.208.2
* fix
  - 补充转化加速器算法数据的分桶id

# 0.209.0
* 需求
  - 扩充 BrandAbnormalType 枚举值
  
# 0.210.0
* 需求
  - sdk.avro third_party.avro 增加media_type、slot_style_type等字段
  - https://jira.inner.youdao.com/browse/ZHIXUAN-5980

# 0.211.0
* 需求
  - 渠道反作弊v1.5.0 一期：https://confluence.inner.youdao.com/pages/viewpage.action?pageId=349476047

# 0.212.0
* 需求
  - brand abnormal url 日志增加ext字段

# 0.212.1
* 优化
  - 渠道转化增加点击转化对应的转化付费金额信息

# 0.213.0
* 需求
  - dsp-conv/sdk-conv/SdkReqIdConv/ThirdPartyReqIdConv/ThirdPartyConv 增加首日付费金额和24小时内付费金额字段
  - SdkReqIdConv/ThirdPartyReqIdConv 增加ext

# 0.214.0
* 需求
  - 渠道反作弊v1.5.0 二期：https://confluence.inner.youdao.com/pages/viewpage.action?pageId=357102244

# 0.215.0
* 需求
  - 渠道反作弊v1.5.0 三期：https://confluence.inner.youdao.com/pages/viewpage.action?pageId=357102244
  
# 0.216.0
* 需求
  - 增加ThirdRtaJar, 合并多条third_rta_bid日志
  - [jira](https://jira.inner.youdao.com/browse/BIDSYSTEM-3305)  

# 0.217.0
* 需求
  - brand_pv补齐设备号md5字段
  - [jira](https://jira.inner.youdao.com/browse/YOUXUAN-1508)  


# 0.217.1
* bugfix
  - 修复线上third_rta_bid schema错误问题，更新字段默认值，生成新的avro版本

# 0.217.2
* 需求
    - YexBidResponseStatus添加SERVICE_UNAVAILABLE状态
    - [jira](https://jira.inner.youdao.com/browse/YEX-395)

# 0.218.0
* 需求
   - AdLoadTracker等字段增加扩展字段
   - [jira](https://jira.inner.youdao.com/browse/CEADSDK-1543)

# 0.219.0
* 需求
  - 增加ThirdPartyAppDest avro schema, 记录下载链接访问日志

# 0.219.4
* 需求
  - ThirdPartyClick和ThirdPartyConv增加gaid字段

# 0.219.5
* 需求
  - SdkClick增加字段记录点击时间
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6158)

# 0.220.0
* 需求
  - 渠道流量监控v1.5.4
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6120)  

# 0.221.0
* 需求
  - 增加interest_pv topic的新的定向维度字段(手机品牌定向)
  - [Jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6134)
  - [技术文档](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=370082302)

# 0.221.1
* 需求
  - 渠道流量监控v1.5.4 v3

# 0.221.2
* 需求
  - rta_record日志增肌渠道did字段。

# 0.221.3
* 需求
  - 渠道流量监控-设备清洗池相关指标定义

# 0.221.4
* 需求
  - sdk_pv增加字段installed_apps
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6217)

# 0.223.0
* 需求
  - [聚合SDK上报增加埋点](https://jira.inner.youdao.com/browse/CEADSDK-1546)

# 0.224.0
* 需求
  - gorgon协议支持传包名作为扩展参数
  - [JIRA](https://jira.inner.youdao.com/browse/ZHIXUAN-6219)

# 0.225.0
* 需求
  - bid, impr, click, conv, interest cover增加package name字段
  - pv增加idfa md5字段

# 0.225.1
* 需求
  - yexImpr填加bid_response_time_step分段统计响应时间

# 0.226.0
* 需求
  - 增加激活和付费时间差
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6143)

# 0.227.0
* 需求
  - pvSdk数据补全oaid_md5,android_id_md5
  - 单独一个配置文件配置转化加速器所需数据的schema。
  - 增加算法数据的schema配置。

# 0.228.0
* 需求
  - 增加触点时间 以及 转化和触点时间差
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6280)

# 0.229.0
* 需求
  - sdk_tdp_together统计表里增加“激活日付费数”指标
  - [jira](https://jira.inner.youdao.com/browse/ZHIXUAN-6319)

# 0.230.0
* 需求
  - YexBid、YexAssetLog增加creative_attri字段,标记下载或落地页类型广告
  
# 0.231.0
* 需求
  - 优选无效流量&版本号过滤
  - [Jira](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=383008058)
  - [Jira](https://confluence.inner.youdao.com/pages/viewpage.action?pageId=383008061)
  - [wiki](https://confluence.inner.youdao.com/x/C7npFg)