-- Interface to handle data.
-- @param KEYS[1] deviceId
-- @param ARGV[1] funcName GET or UPDATE or DELETE_EXPIRED
-- @param ARGV[2] adPlanId
-- @param ARGV[3] windowIndex
-- @param ARGV[4] frequency type 0-NONE 1-DAY 2-WEEK 3-ALL
-- @param ARGV[5] order start date format yyyyMMdd
-- @param ARGV[6] currentTimestamp
-- @version 1.0.0

local function createOrUpdate()
    local oldRecord = redis.call('hget', KEYS[1], ARGV[2]);
    local imprObject
    if oldRecord then
        imprObject = cmsgpack.unpack(oldRecord)
    else
        imprObject = {
            type = ARGV[4],
            startDate = ARGV[5],
        }
    end

    if imprObject[ARGV[3]] then
        imprObject[ARGV[3]] = imprObject[ARGV[3]] + 1
    else
        imprObject[ARGV[3]] = 1
    end

    imprObject['updateTime'] = ARGV[6]

    redis.call('hset', KEYS[1], ARGV[2], cmsgpack.pack(imprObject))
end

local function getWithIndex()
    local record = redis.call('hget', KEYS[1], ARGV[2]);
    if record then
        local imprObject = cmsgpack.unpack(record)
        if imprObject[ARGV[3]] then
            return imprObject[ARGV[3]]
        end
    end
    return 0
end

-- 删除指定key下已经过期的field，过期时间为180天
local function deleteExpiredField()
    local keys = redis.call('hkeys', KEYS[1]);
    local removeKeyArray = {}
    for _, key in pairs(keys) do
        local imprObject = cmsgpack.unpack(redis.call('hget', KEYS[1], key))
        if redis.call("time")[1] * 1000 - imprObject['updateTime'] > 180 * 24 * 60 * 60 * 1000 then
            removeKeyArray[#removeKeyArray + 1] = key
        end
    end
    for _, key in pairs(removeKeyArray) do
        redis.call("hdel", KEYS[1], key)
    end
    return 0
end




-- call --
if ARGV[1] == 'GET' then
    return getWithIndex()
else
    if ARGV[1] == 'UPDATE' then
        createOrUpdate()
    else
        if ARGV[1] == 'DELETE_EXPIRED' then
            deleteExpiredField()
        else
            return { err = 'Invalid operation: ' .. ARGV[1] }
        end
    end
end



