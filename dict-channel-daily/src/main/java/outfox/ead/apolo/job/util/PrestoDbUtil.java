package outfox.ead.apolo.job.util;

import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Properties;

@Slf4j
/**
 * <AUTHOR>
 */
public class PrestoDbUtil {
    private static Connection conn = null;

    /**
     * 静态代码块（将加载驱动、连接数据库放入静态块中）
     */
    static {
        try {
            Properties props = new Properties();
            props.load(PrestoDbUtil.class.getClassLoader().getResourceAsStream("dict-channel-feature-daily.job"));

            String url = props.getProperty("presto.url");
            String name = props.getProperty("presto.user");
            //1.加载驱动程序
            Class.forName("com.facebook.presto.jdbc.PrestoDriver");
            //2.获得数据库的连接
            conn = DriverManager.getConnection(url, name, null);
        } catch (Exception e) {
            log.error("presto connection error", e);
        }
    }

    /**
     * 对外提供一个方法来获取数据库连接
     * @return
     */
    public static Connection getConnection() {
        return conn;
    }
}
