package outfox.ead.apolo.job;

import azkaban.utils.Props;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import outfox.ead.apolo.job.model.UserChannel;
import outfox.ead.apolo.job.util.Dbutil;
import outfox.ead.apolo.job.util.PrestoDbUtil;
import outfox.venus.client2.VenusClient;
import outfox.venus.client2.data.BasicData;
import outfox.venus.client2.data.Data;
import outfox.venus.client2.data.Id;

import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
public class DictChannelFeatureJob {
    private final static ObjectMapper OMAPPER = new ObjectMapper();

    private final static String FEATURE_ID = "ID";
    private final static String FEATURE_NAME = "NAME";

    private final static Connection CON = PrestoDbUtil.getConnection();
    private final static String PRESTO_SQL = "select imei,channel_portrait from dict_learngroup_recom_compute.user_profile where snapshot='fresh'";
    private final static String EADB1_SQL = "select ID,NAME from DictCrowdLabel";
    private final static String OLD_TAG_SQL = "select ID from CrowdLabelV2 where IS_DISPLAYABLE = 0";

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("write-2-venus-pool-%d").build();
    private String name;
    private Props props;

    private Integer venusClientNum;
    private CopyOnWriteArraySet<String> deviceSet;
    private VenusClient venusClient;
    private LinkedBlockingQueue<UserChannel> userChannelQueue;
    /**
     * 上一版人群定向标签的id集合，用于兼容老逻辑
     */
    private Set<Integer> oldTagIdSet;

    public static void main(String[] args) {
        try {
            Path path = Paths.get(DictChannelFeatureJob.class.getClassLoader().getResource("dict-channel-feature-daily" +
                    ".job").getPath());

            Props props = new Props(null, path.toFile());

            DictChannelFeatureJob job = new DictChannelFeatureJob("", props);
            job.doWork();
        } catch (Exception e) {
            log.error("run DictChannelFeatureJob error", e);
        }
    }

    public DictChannelFeatureJob(String name, Props props) {
        this.props = props;
    }

    /**
     * 主流程，具体逻辑：
     * 1、从智选数据库中查询每个频道对应的兴趣id
     * 2、从词典hive表中查询出全部设备的关联频道；
     * 3、将每个设备的兴趣特征id列表写入venus
     */
    public void doWork() {
        log.info("==========================start update user channel=============================");
        try {
            this.venusClient = new VenusClient(props.getString("venus.zk.address")
                    , props.getString("venus.redis.uri")
                    , props.getInt("venus.read.timeout"));
        } catch (Exception e) {
            log.error("init venusClient error: ", e);
        }
        ExecutorService executor = null;
        try {
            this.venusClientNum = props.getInt("venus.client.num");
            executor = new ThreadPoolExecutor(venusClientNum, venusClientNum,
                    0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(1024),
                    THREAD_FACTORY, new ThreadPoolExecutor.AbortPolicy());
            this.deviceSet = new CopyOnWriteArraySet<>();
            this.oldTagIdSet = getOldTagSet();
            Map<String, Integer> channel2TagId = getChannel2TagId();
            this.userChannelQueue = getUserChannelFromPresto(channel2TagId);
            if (CollectionUtils.isEmpty(userChannelQueue)) {
                log.error("get user chennel info list is empty, stop update");
                return;
            }
            log.info("user size : {}", userChannelQueue.size());
            write2venusV2(executor);
            log.info("error device id size: {}", deviceSet.size());
        } finally {
            try {
                if (executor != null) {
                    executor.shutdown();
                }
            } catch (Exception ignored) {
            }
            log.info("==========================end update user channel=============================");
        }
    }


    /**
     * 从智选数据库中获取词典频道名称到特征id的映射
     *
     * @return
     */
    public Map<String, Integer> getChannel2TagId() {
        log.info("start get channel to tagId map");
        NamedParameterJdbcTemplate jdbcTemplate = Dbutil.getEadb1JdbcTemplate();
        SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(EADB1_SQL, new HashMap<>());
        Map<String, Integer> channel2TagId = new HashMap<>();
        while (sqlRowSet.next()) {
            channel2TagId.put(sqlRowSet.getString(FEATURE_NAME), sqlRowSet.getInt(FEATURE_ID));
        }
        log.info("end get channel to tagId map , content is : {}", channel2TagId);
        return channel2TagId;
    }

    public Set<Integer> getOldTagSet() {
        log.info("start get old tag set");
        NamedParameterJdbcTemplate jdbcTemplate = Dbutil.getEadb1JdbcTemplate();
        SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(OLD_TAG_SQL, new HashMap<>());
        Set<Integer> res = new HashSet<>();
        while (sqlRowSet.next()) {
            res.add(sqlRowSet.getInt(FEATURE_ID));
        }
        log.info("end get old tag set , content is : {}", res);
        return res;
    }

    /**
     * @param channel2TagId 词典频道名称到特征id的映射
     * @return
     */
    public LinkedBlockingQueue<UserChannel> getUserChannelFromPresto(Map<String, Integer> channel2TagId) {
        if (MapUtils.isEmpty(channel2TagId)) {
            return new LinkedBlockingQueue<>();
        }
        LinkedBlockingQueue<UserChannel> res = new LinkedBlockingQueue<>();
        try {
            Statement ps = CON.createStatement();
            ResultSet resultSet = ps.executeQuery(PRESTO_SQL);
            while (resultSet.next()) {
                String deviceId = StringUtils.upperCase((String) resultSet.getObject("imei"));
                Map<String, Object> channelPortrait = OMAPPER.convertValue(resultSet.getObject("channel_portrait"), new TypeReference<Map<String, Object>>() {
                });
                Set<Integer> channelIdList = channelPortrait.keySet().stream().filter(channel2TagId::containsKey).map(channel2TagId::get).collect(Collectors.toSet());
                UserChannel userChannel = new UserChannel(deviceId, channelIdList);
                res.put(userChannel);
            }
        } catch (Exception e) {
            log.error("get user channel info error ", e);
        }
        return res;
    }

    /**
     * 向venus中写入特征数据，用 venusClientNum 个线程从阻塞队列中拿userChannel写入
     */
    public void write2venusV2(ExecutorService executor) {
        // 用于全部线程写入完成后shutdown掉线程池
        CountDownLatch countDownLatch = new CountDownLatch(venusClientNum);
        for (int i = 0; i < venusClientNum; i++) {
            executor.execute(() -> {
                writeV2(countDownLatch);
            });
        }
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error("countDownLatch or error", e);
        }
    }


    public void writeV2(CountDownLatch countDownLatch) {
        try {
            while (!userChannelQueue.isEmpty()) {
                UserChannel userChannel = new UserChannel();
                try {
                    userChannel = userChannelQueue.poll();
                    Id id = new Id(userChannel.getDeviceId(), "luna", "action");
                    // 获取venus中原有标签的数据并merge到新标签集合中
                    Set<Integer> tagSet = mergeTagSet(venusClient.getAllData(userChannel.getDeviceId(), "luna"), userChannel.getChannelIdSet());
                    if (CollectionUtils.isEmpty(tagSet)) {
                        continue;
                    }
                    BasicData data = new BasicData(id, StringUtils.join(tagSet, ","));
                    venusClient.set(data);
                    //log.info("write in venus success, device id: {},content: {}", userChannel.getDeviceId(), tagSet);
                } catch (Exception e) {
                    deviceSet.add(userChannel.getDeviceId());
                    log.error("write data in venus error,deviceId: {}, tags : {}", userChannel.getDeviceId(), userChannel.getChannelIdSet(), e);
                }
            }
        } finally {
            countDownLatch.countDown();
        }
    }

    /**
     * todo 兼容性考虑，merge改版之前的用户兴趣特征id，没有投上一版人群定向的广告主后，此逻辑可以删除
     *
     * @param currentUserTagDataCollection 当前venus中存储的用户人群定向特征数据
     * @param getChannelIdSet              本次从词典用户画像数据库中获取的此用户的频道id集合
     * @return
     */
    public Set<Integer> mergeTagSet(Collection<Data> currentUserTagDataCollection, Set<Integer> getChannelIdSet) {
        Set<Integer> res = new HashSet<>(getChannelIdSet);
        if (CollectionUtils.isEmpty(currentUserTagDataCollection)) {
            return res;
        }
        currentUserTagDataCollection.forEach(data -> {
            if ("action".equals(data.getId().getDataId())) {
                String currentDictTagIds = new String(data.getValue(), StandardCharsets.UTF_8);
                log.info("old tags is not empty, content: {}", currentDictTagIds);
                if (StringUtils.isNotBlank(currentDictTagIds)) {
                    Set<Integer> currentDictTagIdSet = Arrays.stream(StringUtils.split(currentDictTagIds, ","))
                            .map(Integer::valueOf).filter(oldTagIdSet::contains).collect(Collectors.toSet());
                    res.addAll(currentDictTagIdSet);
                }
            }
        });
        return res;
    }


}
