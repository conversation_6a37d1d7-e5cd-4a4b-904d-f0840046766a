<?xml version="1.0" encoding="UTF-8"?>
<Configuration monitorInterval="60">
    <Appenders>
        <Console name="CONSOLE" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %c{1}:%L - %m%n"/>
        </Console>

        <RollingRandomAccessFile name="NORMAL" fileName="logs/log" filePattern="logs/log.%d{yyyy-MM-dd}.%i">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %c{1}:%L - %m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="2 GB"/>
            </Policies>
            <DefaultRolloverStrategy max="7">
                <Delete basePath="logs" maxDepth="1">
                    <IfFileName glob="log.20*">
                        <IfAny>
                            <IfAccumulatedFileSize exceeds="10 GB" />
                            <IfAccumulatedFileCount exceeds="7" />
                        </IfAny>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <AsyncRoot level="info" includeLocation="true">
            <AppenderRef ref="CONSOLE" />
            <AppenderRef ref="NORMAL" />
        </AsyncRoot>
    </Loggers>
</Configuration>