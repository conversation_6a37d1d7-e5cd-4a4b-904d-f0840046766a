package com.youdao.quipu.azkaban;

import azkaban.utils.Props;
import com.google.common.base.Function;
import com.google.common.collect.Iterables;
import com.linkedin.camus.sweeper.CamusSweeper;
import org.apache.hadoop.conf.Configuration;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 */
public class SweeperInAzkaban extends CamusSweeper {

    private final String name;
    private final Props azkabanProps;

    public SweeperInAzkaban(String name, Props props) throws IOException {
        super();
        this.name = name;
        this.azkabanProps = props;
    }

    public void runFromAzkaban() throws Exception {
        System.out.println("Properties is: " + azkabanProps);
        Map<String, String> args = azkabanProps.getMapByPrefix("-D");
        String[] strings = Iterables.toArray(Iterables.concat(Arrays.asList("-p", "/empty.properties"), Iterables.concat
                (Iterables.transform(args.entrySet(), new Function<Map.Entry<String, String>, List<String>>() {
                    @Override
                    public List<String> apply(Map.Entry<String, String> input) {
                        return Arrays.asList("-D" + input.getKey(), input.getValue());
                    }
                }))), String.class);
        System.out.println("Arguments is: " + Arrays.deepToString(strings));
        super.run(strings);
    }

    @Override
    public Configuration getConf() {
        try {
            Class<?> c = CamusSweeper.class;
            Configuration conf = new Configuration();
            Field chap = c.getDeclaredField("props");
            chap.setAccessible(true);
            Properties props = (Properties) chap.get(this);
            for (Map.Entry<Object, Object> pair : props.entrySet()) {
                String key = (String) pair.getKey();
                conf.set(key, (String) pair.getValue());
            }
            chap.setAccessible(false);
            return conf;
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }
}
