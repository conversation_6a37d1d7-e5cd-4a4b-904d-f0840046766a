package com.youdao.quipu.azkaban;

import azkaban.utils.Props;
import com.google.common.base.Function;
import com.google.common.collect.Iterables;
import com.linkedin.camus.etl.kafka.CamusJob;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CamusIn<PERSON>zkaban extends CamusJob {

    private final String name;
    private final Props props;

    public CamusInAzkaban(String name, Props props) throws IOException {
        super();
        this.name = name;
        this.props = props;
    }

    public void runFromAzkaban() throws Exception {
        System.out.println("Properties is: " + props);
        Map<String, String> args = props.getMapByPrefix("-D");
        String[] strings = Iterables.toArray(Iterables.concat(Arrays.asList("-p", "empty.properties"), Iterables.concat
                (Iterables.transform(args.entrySet(), new Function<Map.Entry<String, String>, List<String>>() {
                    @Override
                    public List<String> apply(Map.Entry<String, String> input) {
                        return Arrays.asList("-D" + input.getKey(), input.getValue());
                    }
                }))), String.class);
        System.out.println("Arguments is: " + Arrays.deepToString(strings));
        super.run(strings);
    }

    @SuppressWarnings("deprecation")
    public static void main(String[] args) {
        System.out.println(new Date(115,2,10,2,0,0));
    }
}
