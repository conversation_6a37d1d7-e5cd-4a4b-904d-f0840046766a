type=java
job.class=com.youdao.quipu.azkaban.HdfsMonitorInAzkaban
method.run=run
jvm.args=
classpath=/mfs_ead/home/<USER>/online/hadoop-conf,lib/*

##############################################
monitor.db.url=***************************************************************************************************************
monitor.db.file_id=3
monitor.hdfs.input.prefix=
monitor.hdfs.input.topicPath=quipu/camus/data/conv_sdk_joined,quipu/ml_workflow/data/impr_click_joined
monitor.hdfs.input.suffix=hourly
monitor.hadoop.conf.path=/mfs_ead/home/<USER>/online/hadoop-conf
monitor.hadoop.need.scan.day=8
monitor.strategy.aggressive=true