type=java
job.class=com.youdao.quipu.azkaban.HdfsMonitorInAzkaban
method.run=run
jvm.args=
classpath=/mfs_ead/home/<USER>/online/hadoop-conf,lib/*

##############################################
monitor.db.url=***************************************************************************************************************
monitor.db.file_id=1
monitor.hdfs.input.prefix=quipu/camus/data
monitor.hdfs.input.topicPath=pv_sdk,bid_sdk,impr_sdk,click_sdk_charged,click_ead_discarded
monitor.hdfs.input.suffix=hourly
monitor.hadoop.conf.path=/mfs_ead/home/<USER>/online/hadoop-conf
monitor.hadoop.need.scan.day=2
monitor.strategy.aggressive=true