<assembly>
    <id>azkaban-camus</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${project.basedir}/src/main/resources/camus</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>*.job</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>/lib</outputDirectory>
            <includes>
                <include>*-shaded.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>