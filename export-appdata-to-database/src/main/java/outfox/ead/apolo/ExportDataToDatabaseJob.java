package outfox.ead.apolo;

import azkaban.utils.Props;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.dbcp2.BasicDataSource;
import org.springframework.jdbc.core.JdbcTemplate;
import outfox.ead.apolo.dao.InstalledAppMysqlClient;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class ExportDataToDatabaseJob {

    private final String name;
    private final Props props;

    /**
     * 当天App统计次数的最小值，低于这个值不会被写入到数据库中
     **/
    private static final int  MIN_INSTALL_COUNT = 200;

    private static final String DRIVER_CLASS_NAME = "com.mysql.cj.jdbc.Driver";
    private static final String DATABASE_URL = "***********************************************************************************************************************";
    private static final String DATABASE_USERNAME = "eadonline4nb";
    private static final String DATABASE_PASSWORD = "new1ife4Th1sAugust";

    public static void main(String[] args) throws IOException {
        Path path = Paths.get(ExportDataToDatabaseJob.class.getClassLoader().getResource("./export-appdata-to-database.job").getPath());
        Props props = new Props(null, path.toFile());
        ExportDataToDatabaseJob task = new ExportDataToDatabaseJob("", props);
        task.run();
    }

    public void run() throws IOException {
        log.info("start the program ExportDataToDatabaseJob...");

        // 从hdfs中读取数据
        log.info("The program is reading data from hdfs！");
        InstalledAppHdfsFile readFile = new InstalledAppHdfsFile();
        Map<String, String> hdfsMap = readFile.getAllInstalledApp();
        log.info("The program has read data from hdfs!");

        // 从MySQL中读取数据
        log.info("The program is reading data from mysql!");
        InstalledAppMysqlClient installAppMysqlClient = new InstalledAppMysqlClient();
        Set<String> mysqlSet = installAppMysqlClient.select(ExportDataToDatabaseJob.getJdbcTemplate());

        // 取hdfsSet与mysqlSet的差集
        Set<String> diffSet = ExportDataToDatabaseJob.getDiffSet(hdfsMap.keySet(), mysqlSet);
        Map<String, Integer> countMap = readFile.getCountMap();

        // 写入数据到MySQL
        log.info("The program starts to write mysql!");
        List<Object[]> appDetailList = ExportDataToDatabaseJob.getAppDetailList(countMap, hdfsMap, diffSet);
        installAppMysqlClient.update(ExportDataToDatabaseJob.getJdbcTemplate(), appDetailList);
        log.info("The program has written {} data into database!", appDetailList.size());
        log.info("The program has finished!");
    }

    /**
     * 将所有数据放入到List中
     */
    private static List<Object[]> getAppDetailList(Map<String, Integer> countMap,
                                                   Map<String, String> hdfsMap, Set<String> diffSet) {
        List<Object[]> appDetailList = new ArrayList<>();
        for (String appPackageName : diffSet) {
            String appName = new String(hdfsMap.get(appPackageName).getBytes(StandardCharsets.UTF_8));
            if (countMap.get(appPackageName) < MIN_INSTALL_COUNT) {
                continue;
            }
            appDetailList.add(new Object[]{appName, appPackageName});
        }
        return appDetailList;
    }

    private static Set<String> getDiffSet(Set<String> set1, Set<String> set2) {
        Set<String> diffSet = new HashSet<>();
        diffSet.addAll(set1);
        diffSet.removeAll(set2);
        return diffSet;
    }

    private static JdbcTemplate getJdbcTemplate() {
        BasicDataSource dataSource = new BasicDataSource();
        dataSource.setDriverClassName(DRIVER_CLASS_NAME);
        dataSource.setUrl(DATABASE_URL);
        dataSource.setUsername(DATABASE_USERNAME);
        dataSource.setPassword(DATABASE_PASSWORD);

        JdbcTemplate jdbcTemplate = new JdbcTemplate();
        jdbcTemplate.setDataSource(dataSource);
        return jdbcTemplate;
    }

}
