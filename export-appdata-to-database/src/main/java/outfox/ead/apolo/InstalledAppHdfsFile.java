package outfox.ead.apolo;

import com.youdao.quipu.avro.schema.InstalledApp;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.file.DataFileReader;
import org.apache.avro.file.SeekableByteArrayInput;
import org.apache.avro.io.DatumReader;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.commons.io.IOUtils;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class InstalledAppHdfsFile {

    private Map<String, Integer> countMap = new HashMap<>(65536);

    /**
     * 返回统计packageName次数的map
     * packageName -> packageNameCount
     */
    public Map<String, Integer> getCountMap() {
        return this.countMap;
    }

    /**
     * 反序列化byte数组
     */
    public DataFileReader<InstalledApp> deSerializeInstalledApp(byte[] out) throws IOException {
        DatumReader<InstalledApp> datumReader = new SpecificDatumReader< >(InstalledApp.class);
        DataFileReader<InstalledApp> dataFileReader =
                new DataFileReader<>(new SeekableByteArrayInput(out), datumReader);
        return dataFileReader;
    }

    /**
     * 读取所有文件中App数据放入Map中
     * @return packageName->name
     */
    public Map<String, String> getAllInstalledApp() throws IOException {
        HdfsClient hdfsClient = new HdfsClient();
        FileSystem fileSystem = FileSystem.get(hdfsClient.getConf());

        Map<String, String> totalMap = new HashMap<>(65536);
        List<Path> pathList = hdfsClient.getPath();

        for (int i = 0; i < pathList.size(); i++) {
            if (i % 10 == 0) {
                log.info("The program is reading " + i + "th file!");
            }
            FSDataInputStream inputStream = fileSystem.open(pathList.get(i));
            byte[] hdfsBytes = IOUtils.toByteArray(inputStream);
            DataFileReader<InstalledApp> dataFileReader = deSerializeInstalledApp(hdfsBytes);
            Map<String, String> tmpMap = getData(dataFileReader);

            for (String tmpData : tmpMap.keySet()) {
                if (totalMap.containsKey(tmpData)) {
                    continue;
                }
                totalMap.put(tmpData, tmpMap.get(tmpData));
            }
            inputStream.close();
        }
        fileSystem.close();
        return totalMap;
    }

    /**
     * 读取某一个文件中App数据放入Map中
     */
    public Map<String, String> getData(DataFileReader<InstalledApp> dataFileReader) {
        Map<String, String> map = new HashMap<>(32768);
        for (InstalledApp app : dataFileReader) {
            int len = app.getApps().size();
            for (int i = 0; i < len; i++) {
                String packageName = app.getApps().get(i).getPackageName().toString();
                if (map.containsKey(packageName)) {
                    continue;
                }
                if (countMap.containsKey(packageName)) {
                    int count = countMap.get(packageName);
                    countMap.put(packageName, count + 1);
                } else {
                    countMap.put(packageName, 1);
                }
                String name = app.getApps().get(i).getName().toString();
                map.put(packageName, name);
            }
        }
        return map;
    }

}
