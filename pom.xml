<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.youdao.ead</groupId>
    <artifactId>gorgon</artifactId>
    <version>3.234.2</version>
    <packaging>war</packaging>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <protobuf.version>3.4.0</protobuf.version>
        <kafka.version>0.11.0.3</kafka.version>
        <spring.springframework.version>4.2.7.RELEASE</spring.springframework.version>
        <log4j2.version>2.16.0</log4j2.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.mortbay.jetty</groupId>
            <artifactId>jetty</artifactId>
            <version>6.1.26</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.springframework.version}</version>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-core</artifactId>
            <version>4.0.6</version>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-jmx</artifactId>
            <version>4.0.6</version>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-healthchecks</artifactId>
            <version>4.0.6</version>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.ant</artifactId>
            <version>0.7.6.201602180812</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore-nio</artifactId>
            <version>4.4.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.youdao.ead</groupId>
            <artifactId>ead-datamodel</artifactId>
            <version>5.83.0</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>ejb3-persistence</artifactId>
            <version>1.0.2.GA</version>
        </dependency>
        <dependency>
            <groupId>com.youdao.ead</groupId>
            <artifactId>dsp-base</artifactId>
            <version>2.14.0</version>
        </dependency>
        <dependency>
            <groupId>com.youdao.ead</groupId>
            <artifactId>dsp-base-ext</artifactId>
            <version>0.151.0</version>
        </dependency>
        <dependency>
            <groupId>com.youdao.openrtb</groupId>
            <artifactId>openrtb-core</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.youdao.ead</groupId>
            <artifactId>venus2-client</artifactId>
            <version>4.6.2</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-resolver</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>in.zapr.druid</groupId>
            <artifactId>druidry</artifactId>
            <version>2.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>jersey-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.media</groupId>
                    <artifactId>jersey-media-json-jackson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.inject</groupId>
                    <artifactId>jersey-hk2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.connectors</groupId>
                    <artifactId>jersey-apache-connector</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.9.6</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.6</version>
        </dependency>
        <dependency>
            <groupId>com.youdao</groupId>
            <artifactId>quipu-kafka</artifactId>
            <version>1.8.5</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.youdao</groupId>
                    <artifactId>quipu-avro</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.youdao</groupId>
            <artifactId>quipu-avro</artifactId>
            <version>0.231.0-zyt-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>2.17.100</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>kms</artifactId>
            <version>2.17.100</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3control</artifactId>
            <version>2.17.100</version>
        </dependency>
        <dependency>
            <groupId>com.linkedin.camus</groupId>
            <artifactId>ead-camus-kafka-coders</artifactId>
            <version>0.0.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro-mapred</artifactId>
            <version>1.7.7</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>27.1-jre</version>
        </dependency>
        <dependency>
            <groupId>com.youdao</groupId>
            <artifactId>ead-ip-core</artifactId>
            <version>1.21.0</version>
        </dependency>
        <dependency>
            <groupId>com.youdao</groupId>
            <artifactId>ead-ip</artifactId>
            <version>1.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.youdao</groupId>
            <artifactId>ead-gps</artifactId>
            <version>0.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.6</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.2.Final</version>
        </dependency>
        <dependency>
            <groupId>io.gromit</groupId>
            <artifactId>uap-java</artifactId>
            <version>0.7.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
            <version>2.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>2.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>2.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
            <version>2.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-core</artifactId>
            <version>1.19</version>
        </dependency>
        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-generator-annprocess</artifactId>
            <version>1.19</version>
        </dependency>
        <dependency>
            <groupId>com.linecorp.centraldogma</groupId>
            <artifactId>centraldogma-client-armeria-legacy</artifactId>
            <version>0.31.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-resolver</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-tcnative-boringssl-static</artifactId>
            <version>2.0.25.Final</version>
        </dependency>
        <dependency>
            <groupId>net.spy</groupId>
            <artifactId>memcached</artifactId>
            <version>2.5</version>
        </dependency>
        <!--toolbox-->
        <dependency>
            <groupId>toolbox</groupId>
            <artifactId>misc-tools</artifactId>
            <version>2.0.320069.0</version>
        </dependency>
        <dependency>
            <groupId>toolbox</groupId>
            <artifactId>ip-location</artifactId>
            <version>1.14</version>
        </dependency>
        <dependency>
            <groupId>toolbox</groupId>
            <artifactId>ead-adbrand</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>toolbox</groupId>
            <artifactId>outlog</artifactId>
            <version>3.2.6</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>toolbox</groupId>-->
<!--            <artifactId>ead</artifactId>-->
<!--            <version>2.5.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>toolbox</groupId>
            <artifactId>sigar</artifactId>
            <version>1.6.4</version>
        </dependency>
        <dependency>
            <groupId>toolbox</groupId>
            <artifactId>libsigar-amd64-linux</artifactId>
            <version>1.6.4</version>
            <type>so</type>
        </dependency>
        <dependency>
            <groupId>toolbox</groupId>
            <artifactId>libsigar-x86-linux</artifactId>
            <version>1.6.4</version>
            <type>so</type>
        </dependency>

        <!--outfox-->
        <dependency>
            <groupId>outfox.ead</groupId>
            <artifactId>ead-ping</artifactId>
            <version>1.14</version>
        </dependency>
        <dependency>
            <groupId>outfox</groupId>
            <artifactId>bs-client</artifactId>
            <version>2.4.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>misc</artifactId>
                    <groupId>toolbox</groupId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>outfox</groupId>
            <artifactId>dataserv2</artifactId>
            <version>3.2.0</version>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.13</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>1.9</version>
        </dependency>
        <dependency>
            <groupId>commons-digester</groupId>
            <artifactId>commons-digester</artifactId>
            <version>1.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
        </dependency>
        <dependency>
            <groupId>commons-validator</groupId>
            <artifactId>commons-validator</artifactId>
            <version>1.3.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.10</version>
        </dependency>
        <dependency>
            <groupId>outfox</groupId>
            <artifactId>odis-app</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20090211</version>
        </dependency>

        <!-- log -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.30</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.2</version>
            <!--  to enable async logger  -->
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
            <version>${log4j2.version}</version>
            <!--  this dependency request to remove log4j 1.x dependencies   -->
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jcl</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jul</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <version>${log4j2.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jcommon</artifactId>
            <version>1.0.17</version>
        </dependency>

        <dependency>
            <groupId>toolbox</groupId>
            <artifactId>web-tools</artifactId>
            <version>2.0.164506.0</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.9.0</version>
        </dependency>

        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>

        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-resolver</artifactId>
            <version>4.1.76.Final</version>
        </dependency>
        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>msgpack-core</artifactId>
            <version>0.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.youdao.ead</groupId>
            <artifactId>trace-tool</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
            <version>1.8.8</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-transport-simple-http</artifactId>
            <version>1.8.8</version>
        </dependency>
        <dependency>
            <groupId>com.youdao.ead</groupId>
            <artifactId>mobile-models</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.27.0-GA</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <finalName>gorgon</finalName>
        <resources>
            <resource>
                <directory>conf</directory><!-- 资源根目录排除各环境的配置，防止在生成目录中多余其它目录 -->
                <excludes>
                    <exclude>filters/**/*</exclude>
                    <exclude>*.csv</exclude>
                </excludes>
                <targetPath>conf</targetPath>
            </resource>
            <resource>
                <directory>conf</directory>
                <includes>
                    <include>*.xml</include>
                    <include>*.csv</include>
                    <include>*.lua</include>
                </includes>
            </resource>
            <resource>
                <directory>conf/filters/${profiles.active}</directory>
                <targetPath>conf</targetPath>
            </resource>
            <resource>
                <directory>conf/lua</directory>
                <targetPath>lua</targetPath>
            </resource>
            <resource>
                <directory>conf/filters/${profiles.active}/properties</directory>
                <includes>
                    <include>**/at_least_once_kafka_producer.properties</include>
                    <include>**/sentinel.properties</include>
                </includes>
            </resource>
        </resources>
        <sourceDirectory>src/java</sourceDirectory>
        <testSourceDirectory>src/test</testSourceDirectory>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <includes>
                        <include>**/*.java</include>
                    </includes>
                    <argLine>-Xmx8000m -Xms4096m --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.net.util=ALL-UNNAMED --add-opens=java.xml/jdk.xml.internal=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED
                        --add-opens=java.base/java.lang.reflect=ALL-UNNAMED   --add-opens=java.base/java.util.stream=ALL-UNNAMED --add-opens=java.base/java.util.regex=ALL-UNNAMED  --add-opens=java.base/sun.net.util=ALL-UNNAMED</argLine>
                    <useSystemClassLoader>false</useSystemClassLoader>
                    <parallel>classes</parallel>
                    <threadCount>4</threadCount>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.surefire</groupId>
                        <artifactId>surefire-junit4</artifactId>
                        <version>2.22.2</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.3.2</version>
                <configuration>
                    <attachClasses>true</attachClasses>
                    <webXml>web/WEB-INF/web.xml</webXml>
                    <webResources>
                        <resource>
                            <directory>web</directory>
                            <filtering>true</filtering>
                        </resource>
                        <resource>
                            <directory>target/lib</directory>
                            <targetPath>WEB-INF/lib</targetPath>
                            <includes>
                                <include>**/*.so</include>
                                <include>**/*.dll</include>
                            </includes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.4</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <injectAllReactorProjects>true</injectAllReactorProjects>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <dotGitDirectory>${project.basedir}/../.git</dotGitDirectory>
                    <gitDescribe>
                        <!--提交操作ID显式字符长度,最大值为:40;默认值:7;0代表特殊意义;-->
                        <abbrev>7</abbrev>
                        <!--构建触发时,代码有修改时(即"dirty state"),添加指定后缀;默认值:"";-->
                        <dirty>-dirty</dirty>
                    </gitDescribe>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <compilerArgs>
                        <arg>-Xlint:all,-options,-path</arg>
                        <arg>--add-exports=java.base/sun.net.util=ALL-UNNAMED</arg>
                    </compilerArgs>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

        </plugins>
    </build>


    <profiles>
        <profile><!-- 开发测试环境 -->
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile><!-- 生产环境 -->
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
        <profile><!-- 生产环境 -->
            <id>prod-gz</id>
            <properties>
                <profiles.active>prod-gz</profiles.active>
            </properties>
        </profile>
        <profile><!-- 沙箱环境 -->
            <id>sandbox</id>
            <properties>
                <profiles.active>sandbox</profiles.active>
            </properties>
        </profile>
        <profile><!-- 引流环境 -->
            <id>tcpcopy</id>
            <properties>
                <profiles.active>tcpcopy</profiles.active>
            </properties>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>ead</id>
            <url>https://nexus.corp.youdao.com/nexus/content/repositories/ead/</url>
        </repository>
        <snapshotRepository>
            <id>ead_snapshot</id>
            <url>https://nexus.corp.youdao.com/nexus/content/repositories/ead_snapshot/</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>ead</id>
            <url>https://nexus.corp.youdao.com/nexus/content/repositories/ead/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>ead_snapshot</id>
            <url>https://nexus.corp.youdao.com/nexus/content/repositories/ead_snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>nexus3</id>
            <name>internal nexus3 repository</name>
            <url>https://nexus3.corp.youdao.com/repository/maven-central/</url>
        </repository>
        <repository>
            <id>nexus3-release</id>
            <name>internal nexus3 release repository</name>
            <url>https://nexus3.corp.youdao.com/repository/maven-releases/</url>
        </repository>
        <repository>
            <id>nexus3-snapshot</id>
            <name>internal nexus3 snapshot repository</name>
            <url>https://nexus3.corp.youdao.com/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>dict</id>
            <url>https://nexus.corp.youdao.com/nexus/content/repositories/dict</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>youdao</id>
            <url>https://nexus.corp.youdao.com/nexus/content/repositories/youdao</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>public</id>
            <url>https://nexus.corp.youdao.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
