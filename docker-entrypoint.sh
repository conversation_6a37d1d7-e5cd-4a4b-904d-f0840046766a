#!/bin/bash
export JAVA_HOME=/usr/local/youdao/ad/jdk8u382-b05
cat /tmp/hosts >> /etc/hosts
echo "Asia/Shanghai" > /etc/timezone
export remote_debug_options='-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8749'


JAVA_OPTS="-Dserver=converttracking -server -Xms2g -Xmx4g -XX:+UnlockExperimentalVMOptions -XX:+UseG1GC
                        -XX:MaxGCPauseMillis=100  -XX:+AlwaysPreTouch -XX:+UseStringDeduplication -XX:-ResizePLAB
                        -XX:+ParallelRefProcEnabled -XX:-ReduceInitialCardMarks -XX:-UseBiasedLocking
                        -verbose:gc -XX:+PrintGCDetails -XX:+PrintAdaptiveSizePolicy -XX:+PrintTenuringDistribution
                        -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=3
                        -XX:GCLogFileSize=200M -XX:+PrintGCDateStamps -Xloggc:logs/gc.log.%t
                        -XX:-OmitStackTraceInFastThrow

                        -Dcom.sun.management.jmxremote.port=18091 -Dcom.sun.management.jmxremote.authenticate=false
                        -Dcom.sun.management.jmxremote.ssl=false
                        -Dserver.port=8080"

if [[ $1 == 'test' ]] ; then
   JAVA_OPTS="$JAVA_OPTS -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8012"
fi

echo "JAVA_OPTS=$JAVA_OPTS"
nohup $JAVA_HOME/bin/java $JAVA_OPTS -jar target/convert-tracking.jar 2>&1 >log &

tail -f log
