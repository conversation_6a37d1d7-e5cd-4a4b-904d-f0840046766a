package com.youdao.quipu.druid.parser;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Charsets;
import com.metamx.common.parsers.ParseException;
import io.druid.data.input.ByteBufferInputRowParser;
import io.druid.data.input.InputRow;
import io.druid.data.input.impl.ParseSpec;
import io.druid.data.input.impl.TimestampSpec;
import org.joda.time.DateTime;

import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.CoderResult;
import java.nio.charset.CodingErrorAction;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaown on 15/5/5.
 */
@JsonTypeName("json_extend")
public class JsonInputRowParser implements ByteBufferInputRowParser {

    private final ParseSpec parseSpec;

    private final Map<String, String> properties;
    private final List<String> dimensions;

    private final ObjectMapper mapper = new ObjectMapper();
    private CharBuffer chars;

    public JsonInputRowParser(@JsonProperty("parseSpec") ParseSpec parseSpec,
                              @JsonProperty("properties") Map<String, String> properties) {
        this.dimensions = parseSpec.getDimensionsSpec().getDimensions();
        this.parseSpec = parseSpec;
        this.properties = properties;
    }

    @Override
    public InputRow parse(ByteBuffer input) {
        int payloadSize = input.remaining();

        if (chars == null || chars.remaining() < payloadSize) {
            chars = CharBuffer.allocate(payloadSize);
        }

        final CoderResult coderResult = Charsets.UTF_8.newDecoder()
                .onMalformedInput(CodingErrorAction.REPLACE)
                .onUnmappableCharacter(CodingErrorAction.REPLACE)
                .decode(input, chars, true);

        if (coderResult.isUnderflow()) {
            chars.flip();
            try {
                return parseString(chars.toString());
            } finally {
                chars.clear();
            }
        } else {
            throw new ParseException("Failed with CoderResult[%s]", coderResult);
        }
    }

    private InputRow parseString(String string) {
        try {
            Map<String, Object> map = mapper.readValue(string, new TypeReference<HashMap<String, Object>>() {
            });
            map = lowerKey(map);
            TimestampSpec timestampSpec = parseSpec.getTimestampSpec();
            DateTime dateTime = timestampSpec.extractTimestamp(Collections.singletonMap(timestampSpec.getTimestampColumn
                    (), map.get(timestampSpec.getTimestampColumn())));
            return new MapInputRow(map, dimensions, dateTime);
        } catch (Exception e) {
            throw new ParseException("Error parse json string: " + string, e);
        }
    }

    private Map<String,Object> lowerKey(Map<String, Object> map) {
        Map<String, Object> newMap = new HashMap<String, Object>(map.size());
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            String key = entry.getKey().toLowerCase();
            if (value instanceof Map){
                newMap.put(key, lowerKey((Map<String, Object>) value));
            }else{
                newMap.put(key, value);
            }
        }
        return newMap;
    }

    @Override
    public ParseSpec getParseSpec() {
        return parseSpec;
    }

    @Override
    public ByteBufferInputRowParser withParseSpec(ParseSpec parseSpec) {
        return new JsonInputRowParser(parseSpec, properties);
    }
}
