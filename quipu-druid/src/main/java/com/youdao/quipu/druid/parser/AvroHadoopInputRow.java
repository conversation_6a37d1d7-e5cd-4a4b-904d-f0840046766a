package com.youdao.quipu.druid.parser;

import org.apache.avro.generic.GenericRecord;
import org.joda.time.DateTime;

import java.util.List;
import java.util.Map;

/**
 * Created by z<PERSON><PERSON> on 15/10/17.
 */
public class AvroHadoopInputRow extends AvroInputRow {
    public AvroHadoopInputRow(GenericRecord record, List<String> dimensions, DateTime timestamp) {
        super(record, dimensions, timestamp);
    }

    @Override
    protected Object getFieldFromAvroObject(Map map, String key) {
        return map.get(key);
    }
}
