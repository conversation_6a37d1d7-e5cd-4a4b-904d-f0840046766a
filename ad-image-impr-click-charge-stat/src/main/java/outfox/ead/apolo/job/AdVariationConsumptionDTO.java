package outfox.ead.apolo.job;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 11:21 2019/7/24
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdVariationConsumptionDTO {
    private String timestamp;
    private String version;
    private Event event;
}

/**
 * <AUTHOR>
 * @Date 11:21 2019/7/24
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
class Event {
    private String sponsorId;
    private String sponsorName;
    private String sponsorEmail;
    private String variationId;
    private String slotId;
    private Long impr;
    private Long click;
    private Long charge;
}


