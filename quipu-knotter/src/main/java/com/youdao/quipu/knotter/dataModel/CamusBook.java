package com.youdao.quipu.knotter.dataModel;

import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import com.linkedin.camus.etl.kafka.common.EtlCounts;
import com.linkedin.camus.etl.kafka.common.Source;
import com.youdao.quipu.knotter.dao.ormlitePersister.TimestampLongPersister;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

import static java.lang.Math.*;
import static java.lang.Math.max;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@DatabaseTable(tableName = "camus_book")
public class CamusBook implements Comparable {
    @DatabaseField(generatedId = true)
    private long id;
    @DatabaseField(canBeNull = false)
    private String camusBookId;
    @DatabaseField(canBeNull = false)
    private String topic;
    @DatabaseField(canBeNull = false)
    private String historyPath;
    @DatabaseField(persisterClass = TimestampLongPersister.class, canBeNull = false)
    private long startTime;
    @DatabaseField(persisterClass = TimestampLongPersister.class, canBeNull = false)
    private long stopTime;
    @DatabaseField(persisterClass = TimestampLongPersister.class, canBeNull = false)
    private long firstTimestamp;
    @DatabaseField(persisterClass = TimestampLongPersister.class, canBeNull = false)
    private long lastTimestamp;
    @DatabaseField(canBeNull = false)
    private long count;
    @DatabaseField(canBeNull = false)
    private long errorCount;

    private transient Set<EtlCounts> mergedEtlCounts = new HashSet<EtlCounts>();

    public CamusBook(String camusBookId, String historyPath, EtlCounts counts) {
        this.camusBookId = camusBookId;
        this.historyPath = StringUtils.substringBeforeLast(historyPath, "/");
        startTime = counts.getStartTime();
        stopTime = counts.getEndTime();
        firstTimestamp = counts.getFirstTimestamp();
        lastTimestamp = counts.getLastTimestamp();
        errorCount = counts.getErrorCount();
        mergedEtlCounts.add(counts);
        topic = counts.getTopic();
    }

    public void mergeSource(Source other) {
        count += other.getCount();
    }

    public void mergeCount(EtlCounts other) {
        if (!mergedEtlCounts.contains(other)) {
            startTime = min(startTime, other.getStartTime());
            stopTime = max(stopTime, other.getEndTime());
            firstTimestamp = min(firstTimestamp, other.getFirstTimestamp());
            lastTimestamp = max(firstTimestamp, other.getLastTimestamp());
            errorCount += other.getErrorCount();
        }
    }

    @Override
    public int compareTo(Object o) {
        if (o instanceof CamusBook && camusBookId != null) {
            return camusBookId.compareTo(((CamusBook) o).getCamusBookId());
        } else {
            return this.compareTo(o);
        }
    }
}