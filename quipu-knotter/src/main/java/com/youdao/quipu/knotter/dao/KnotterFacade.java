package com.youdao.quipu.knotter.dao;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.common.collect.Lists;
import com.google.common.io.Closeables;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.dao.DaoManager;
import com.j256.ormlite.jdbc.JdbcConnectionSource;
import com.j256.ormlite.misc.TransactionManager;
import com.j256.ormlite.table.TableUtils;
import com.youdao.quipu.knotter.TimeUtils;
import com.youdao.quipu.knotter.dataModel.*;
import lombok.Getter;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.Path;
import org.jetbrains.annotations.Contract;
import org.joda.time.DateTimeZone;
import org.joda.time.Interval;
import org.joda.time.Period;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import javax.validation.constraints.NotNull;
import java.io.Closeable;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;

import static com.youdao.quipu.knotter.TimeUtils.getHourlyIntervals;

/**
 * 使用OrmLite的Mysql
 *
 * <AUTHOR>
 */
@CommonsLog
public class KnotterFacade implements Closeable {
    private static final DateTimeFormatter HDFS_PATH_HOURLY_FORMATTER = DateTimeFormat.forPattern("yyyy/MM/dd/HH")
            .withZone(DateTimeZone.getDefault());
    public static long scanTime = 1466041635559L;
    public static String DATE_FORMAT = "yyyy/MM/dd";
    private SimpleDateFormat dateFormat;
    public static String DEFAULT_CAMUS_LAST_HISTORY =
            "/quipu/camus/execution/history/1986-10-17-00-00-00";

    private final String minCamusLastHistoryPath;
    private final Dao<CamusBook, Long> camusBookDao;
    private final Dao<CamusMeta, Long> camusMetaDao;
    private final Dao<DruidMeta, String> druidMetaDao;
    private final Dao<DruidBook, Long> druidBookDao;
    private final Dao<HdfsFileMeta, Long> hdfsFileMetaDao;
    private final JdbcConnectionSource knotterConnection;
    private final Dao<DruidJob, String> druidJobDao;
    private final Dao<HdfsBook, Long> hdfsBooksDao;

    private final String inputHdfsPrefix;
    private final DruidClient druidClient;

    private final CamusClient camusClient;
    @Getter
    private final HdfsClient hdfsClient;
    private final JdbcConnectionSource druidConnection;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public KnotterFacade(@NotNull JdbcConnectionSource knotterConnection, @NotNull JdbcConnectionSource
            druidConnection, @NotNull String inputHdfsPrefix, @NotNull DruidClient druidClient, CamusClient camusClient,
                         HdfsClient hdfsClient, String minCamusLastHistoryPath) throws SQLException {
        this.knotterConnection = knotterConnection;
        this.druidConnection = druidConnection;
        this.druidClient = druidClient;
        this.inputHdfsPrefix = inputHdfsPrefix;
        this.camusClient = camusClient;
        this.hdfsClient = hdfsClient;
        this.minCamusLastHistoryPath = minCamusLastHistoryPath == null ? DEFAULT_CAMUS_LAST_HISTORY :
                minCamusLastHistoryPath;
        camusBookDao = DaoManager.createDao(knotterConnection, CamusBook.class);
        camusMetaDao = DaoManager.createDao(knotterConnection, CamusMeta.class);
        druidBookDao = DaoManager.createDao(knotterConnection, DruidBook.class);
        druidMetaDao = DaoManager.createDao(knotterConnection, DruidMeta.class);
        hdfsFileMetaDao = DaoManager.createDao(knotterConnection, HdfsFileMeta.class);
        druidJobDao = DaoManager.createDao(druidConnection, DruidJob.class);
        hdfsBooksDao = DaoManager.createDao(knotterConnection, HdfsBook.class);
        dateFormat = new SimpleDateFormat(DATE_FORMAT);
    }

    public KnotterFacade(@NotNull JdbcConnectionSource knotterConnection, @NotNull JdbcConnectionSource druidConnection,
                         @NotNull String inputHdfsPrefix, @NotNull DruidClient
                                 druidClient, CamusClient camusClient) throws SQLException {
        this(knotterConnection, druidConnection, inputHdfsPrefix, druidClient, camusClient, null, null);
    }

    public KnotterFacade(@NotNull String knotterDataBaseUrl, @NotNull String druidDataBaseUrl, @NotNull String
            inputHdfsPrefix, DruidClient druidClient, CamusClient camusClient) throws SQLException {
        this(new JdbcConnectionSource(knotterDataBaseUrl), new JdbcConnectionSource(druidDataBaseUrl),
                inputHdfsPrefix, druidClient, camusClient);
    }

    public KnotterFacade(@NotNull String knotterDataBaseUrl, @NotNull String druidDataBaseUrl, @NotNull String
            inputHdfsPrefix, DruidClient druidClient, HdfsClient hdfsClient) throws SQLException {
        this(new JdbcConnectionSource(knotterDataBaseUrl), new JdbcConnectionSource(druidDataBaseUrl),
                inputHdfsPrefix, druidClient, null, hdfsClient, null);
    }

    /**
     * 获取新拉取到hdfs上的文件
     * <p>
     *
     * @return 从上次扫描结束到当前时间新增的文件路径
     * @throws SQLException
     */
    public Map<String, List<Path>> scanByHour(int day) throws SQLException {
        long start = System.currentTimeMillis();
        scanTime = queryHdfsFileLastModifyTime();
        log.info("Last scan time is:" + new Date(scanTime));
        Map<String, List<Path>> datasourceAndPath = new HashMap<String, List<Path>>();
        Iterator<DruidMeta> druidMetas = queryAllDruidMetas().iterator();
        List<String> days = getScanDay(day);
        while (druidMetas.hasNext()) {
            DruidMeta druidMeta = druidMetas.next();
            List<Path> paths = new ArrayList<Path>();
            for (String topic : druidMeta.getTopics()) {// topic
                long size = 0;
                for (String dayString : days) {//hour
                    Path dayPath = new Path(inputHdfsPrefix + "/" + topic + "/" + dayString);
                    log.info("Scan day path is:" + dayPath.toString());
                    size += hdfsClient.getModFilesSizeAndAddToPaths(dayPath, scanTime, paths);
                }
                createToHdfsBook(topic, size);
            }
            datasourceAndPath.put(druidMeta.getDataSource(), paths);
        }
        log.info("Scan time: " + (System.currentTimeMillis() - start));
        return datasourceAndPath;
    }

    public int createToHdfsBook(String topic , long size) throws SQLException {
        HdfsBook hdfsBook = new HdfsBook();
        hdfsBook.setTopic(topic);
        hdfsBook.setSizeInByte(size);
        hdfsBook.setLastScanTime(System.currentTimeMillis());
        return createHdfsBook(hdfsBook);
    }

    public List<String> getScanDay(int day) {
        List<String> hourPath = new ArrayList<String>();
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTime(new Date());
        for (int i = day; i > 0; i--) {
            hourPath.add("hourly/" + dateFormat.format(gc.getTime()));
            gc.add(GregorianCalendar.DAY_OF_MONTH, -1);
        }
        return hourPath;
    }

    public List<Interval> getCheckDruidBookIdIntervals(int day, int checkDayCount) {
        List<Interval> intervals = new ArrayList<Interval>();
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTime(new Date());
        gc.add(GregorianCalendar.DAY_OF_MONTH, -day);
        gc.set(Calendar.AM_PM, 0);
        gc.set(Calendar.HOUR, 0);
        gc.set(Calendar.MINUTE, 0);
        gc.set(Calendar.MILLISECOND, 0);
        gc.set(Calendar.SECOND, 0);
        for (int i = 0; i < checkDayCount; i++) {
            intervals.add(TimeUtils.getDayInterval(gc.getTimeInMillis()));
            gc.add(GregorianCalendar.DAY_OF_MONTH, -1);
        }
        return intervals;
    }


    public int createCamusBooks(final Collection<CamusBook> values) throws Exception {
        ArrayList<CamusBook> list = Lists.newArrayList(values);
        Collections.sort(list);
        return camusBookDao.callBatchTasks(new Callable<Integer>() {
            @Override
            public Integer call() throws Exception {
                int count = 0;
                for (CamusBook value : values) {
                    count += camusBookDao.create(value);
                }
                return count;
            }
        });
    }

    public void callInTransaction(Callable<Void> callable) throws SQLException {
        TransactionManager.callInTransaction(camusBookDao.getConnectionSource(), callable);
    }

    public int createOrUpdateCamusLastHistoryPath(String path) throws SQLException {
        return camusMetaDao.createOrUpdate(new CamusMeta(path)).getNumLinesChanged();
    }

    public int createOrUpdateHdfsFileLastScanTime(long lastScanTime) throws SQLException {
        return hdfsFileMetaDao.createOrUpdate(new HdfsFileMeta(lastScanTime)).getNumLinesChanged();
    }

    /**
     * @return 初始时默认从当前时间开始扫描
     * @throws SQLException
     */
    public long queryHdfsFileLastModifyTime() throws SQLException {
        HdfsFileMeta hdfsFileMeta = hdfsFileMetaDao.queryForId(HdfsFileMeta.ID);
        return hdfsFileMeta == null ? System.currentTimeMillis() : hdfsFileMeta.getLastScanTime();
    }

    @Contract("null -> null")
    public String queryCamusLastHistoryPath() throws SQLException {
        CamusMeta camusMeta = camusMetaDao.queryForId(CamusMeta.ID);
        return camusMeta == null ? null : camusMeta.getLastHistoryPath();
    }

    public int createCamusBook(CamusBook camusBook) throws SQLException {
        return camusBookDao.create(camusBook);
    }

    public int createOrUpdateDruidMeta(DruidMeta druidMeta) throws SQLException {
        return druidMetaDao.createOrUpdate(druidMeta).getNumLinesChanged();
    }

    protected Collection<String> getHourlyHdfsPath(@NotNull String topic, @NotNull Interval interval) throws IOException {
        Collection<Interval> intervals = getHourlyIntervals(interval);
        List<String> paths = new ArrayList<String>();
        for (Interval hourlyInterval : intervals) {
            String path = String.format("%s/%s/hourly/%s", inputHdfsPrefix, topic, hourlyInterval.getStart().toLocalDateTime()
                    .toString(HDFS_PATH_HOURLY_FORMATTER));
            if(hdfsClient.isNonEmptyDirectory(path)){//修复bug：同一个dataSource里，不同的topic在同一个interval内不是都存在的情况
                paths.add(path);
            }else{
                log.info(String.format("getHourlyHdfsPath get empty path,topic: %s ,path: %s",topic,path));
            }
        }
        return paths;
    }

    public DruidMeta queryDruidMeta(String dataSource) throws SQLException {
        return druidMetaDao.queryForId(dataSource);
    }

    public CamusBook queryCamusBook(long id) throws SQLException {
        return camusBookDao.queryForId(id);
    }

    public Iterable<CamusBook> queryAllCamusBooks() throws SQLException {
        return camusBookDao.queryForAll();
    }

    /**
     * hdfs_book相关操作
     */
    public Iterable<HdfsBook> queryAllHdfsBooks() throws SQLException {
        return hdfsBooksDao.queryForAll();
    }

    public HdfsBook queryOneHdfsBooksByTopic(String topic) throws SQLException {
        return hdfsBooksDao.queryForEq("topic",topic).size()==0 ? null:hdfsBooksDao.queryForEq("topic",topic).get(0);
    }

    public int createHdfsBook(HdfsBook hdfsBook) throws SQLException {
        return hdfsBooksDao.create(hdfsBook);
    }

    public int createOrUpdateHdfsBook(HdfsBook hdfsBook) throws SQLException {
        return hdfsBooksDao.createOrUpdate(hdfsBook).getNumLinesChanged();
    }

    @Override
    public void close() throws IOException {
        knotterConnection.closeQuietly();
        druidConnection.closeQuietly();
        Closeables.close(hdfsClient,true);
    }

    public Iterable<DruidBook> queryAllDruidBooks() throws SQLException {
        return druidBookDao.queryForAll();
    }

    public Iterable<DruidBook> queryRunningDruidBook() throws SQLException {
        return druidBookDao.queryForEq(DruidBook.FIELD_NAME_STATUS, DruidBook.STATUS_RUNNING);
    }

    public Iterable<DruidBook> queryWaitingDruidBook() throws SQLException {
        return druidBookDao.queryForEq(DruidBook.FIELD_NAME_STATUS, DruidBook.STATUS_WAITING);
    }

    public List<DruidBook> queryDruidBookBy(String druidBookId) throws SQLException {
        return druidBookDao.queryForEq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, druidBookId);
    }

    public Iterable<DruidBook> queryNeedNoRetryDruidBooks() throws SQLException {
        return druidBookDao.queryForEq(DruidBook.FIELD_NAME_STATUS, DruidBook.STATUS_NEED_NO_RETRY);
    }

    public Iterable<DruidBook> queryFailedDruidBooks() throws SQLException {
        return druidBookDao.queryForEq(DruidBook.FIELD_NAME_STATUS, DruidBook.STATUS_FAILED);
    }

    public DruidJob queryDruidJob(String jobId) throws SQLException {
        return druidJobDao.queryForId(jobId);
    }

    public Iterable<DruidJob> queryAllDruidJobs() throws SQLException {
        return druidJobDao.queryForAll();
    }

    protected int updateDruidJob(DruidJob job) throws SQLException {
        return druidJobDao.update(job);
    }

    public void createOrUpdateDruidBook(DruidBook druidBook) throws SQLException {
        druidBookDao.createOrUpdate(druidBook);
    }

    public int updateDruidBook(DruidBook druidBook) throws SQLException {
        return druidBookDao.update(druidBook);
    }

    public int createWaitingDruidBookIfNotExist(String dataSource, Interval interval) throws SQLException {
        return createWaitingDruidBookIfNotExist(dataSource, interval, DruidBook.TYPE_NORMAL);
    }

    public int createWaitingDruidBookIfNotExist(String dataSource, Interval interval, String type) throws SQLException {
        String id = DruidBook.id(dataSource, interval);
        List<DruidBook> books = druidBookDao.queryBuilder().where().eq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, id).and()
                .eq(DruidBook.FIELD_NAME_STATUS, DruidBook.STATUS_WAITING).query();
        if (books.size() == 0) {
            // 没有正在waiting的同bookId的book
            DruidBook book = new DruidBook();
            if (type == DruidBook.TYPE_AUTO_REDO) {
                book.setTypeAutoRedo();
            } else {
                book.setTypeNormal();
            }
            book.setDruidBookId(id);
            book.setWaiting();
            int count = druidBookDao.create(book);
            if (count != 1) {
                throw new SQLException("Cannot create druid book!");
            }
            log.info("Created waiting druid book, book id: " + id);
            return 1;
        }
        log.info("No need to create waiting druid book, already has one, book id: " + id);
        return 0;
    }

    public int createRunningDruidBook(String dataSource, Interval interval, String druidJobId) throws SQLException {
        return createRunningDruidBook(dataSource, interval, druidJobId, 0);
    }

    public int createRunningDruidBook(String dataSource, Interval interval, String druidJobId, int retryTimes) throws SQLException {
        String bookId = DruidBook.id(dataSource, interval);
        DruidBook book = new DruidBook();
        book.setDruidBookId(bookId);
        book.setDruidJobId(druidJobId);
        book.setRunning();
        book.setRetryTimes(retryTimes);
        book.setStartTime(System.currentTimeMillis());
        List<DruidBook> druidBooks = druidBookDao.queryForEq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, bookId);
        if (druidBooks.size() > 0) {
            book.setTypeAutoRedo();
        } else {
            book.setTypeNormal();
        }
        return druidBookDao.create(book);
    }

    public int createFailedDruidBook(String dataSource, Interval interval, long startTime, long stopTime, int retryTimes)
            throws SQLException {
        if (startTime > stopTime) {
            throw new SQLException("Start time MUST NOT be greater than stop time!");
        }
        String bookId = DruidBook.id(dataSource, interval);
        DruidBook book = new DruidBook();
        book.setDruidBookId(bookId);
        book.setFailed();
        book.setRetryTimes(retryTimes);
        book.setStartTime(startTime);
        book.setStopTime(stopTime);
        List<DruidBook> druidBooks = druidBookDao.queryForEq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, bookId);
        if (druidBooks.size() > 0) {
            book.setTypeAutoRedo();
        } else {
            book.setTypeNormal();
        }
        return druidBookDao.create(book);
    }

    public Iterable<DruidBook> queryDruidBookForJobId(String jobId) throws SQLException {
        return druidBookDao.queryForEq(DruidBook.FIELD_NAME_DRUID_JOB_ID, jobId);
    }

    /**
     * 提交druid batch ingestion job到druid overloard
     *
     * @param dataSource druid data source
     * @param interval   job interval
     * @return druid job id
     */
    public String commitDruidJob(String dataSource, Interval interval) throws SQLException, IOException, URISyntaxException {
        DruidMeta druidMeta = druidMetaDao.queryForId(dataSource);
        if (druidMeta == null) {
            log.error("cannot find druid meta by data source: " + dataSource);
            return null;
        }
        String ingestionConf = loadAndResetIngestionConf(dataSource, druidMeta.getTopics(), interval, druidMeta
                .getIngestionConfFile());
        return druidClient.commitJob(ingestionConf);
    }

    protected String loadAndResetIngestionConf(@NotNull String dataSource, @NotNull Collection<String> topics,
                                               @NotNull Interval interval, @NotNull String ingestionConfFile) throws
            IOException {
        InputStream inputStream = null;
        ObjectNode ingestionConf;
        try {
            if (ingestionConfFile.startsWith("classpath:")) {
                inputStream = this.getClass().getResourceAsStream(StringUtils.substringAfter(ingestionConfFile,
                        "classpath:"));
            } else {
                inputStream = new FileInputStream(ingestionConfFile);
            }
            ingestionConf = (ObjectNode) new ObjectMapper().readTree(inputStream);
        } finally {
            Closeables.close(inputStream, true);
        }
        ((ObjectNode) ingestionConf.get("spec").get("dataSchema")).set("dataSource", new TextNode(StringUtils.substringBeforeLast(dataSource, "_rollup")));
        ObjectNode granularitySpec = (ObjectNode) ingestionConf.get("spec").get("dataSchema").get("granularitySpec");
        ArrayNode intervals = (ArrayNode) granularitySpec.get("intervals");
        intervals.removeAll();
        // 其实druid hadoop job一次可以提交多个interval，但是为了实现简单这里只提交一个
        intervals.add(interval.toString());
        ObjectNode ioConfig = (ObjectNode) ingestionConf.get("spec").get("ioConfig");
        if (dataSource.endsWith("_rollup")) {
            granularitySpec.set("segmentGranularity", getSegmentGranularityForLocalTimezone("P1D", TimeZone.getTimeZone("Asia/Shanghai")));
            granularitySpec.put("queryGranularity", "DAY");
            ioConfig.set("inputSpec", rollUpInputSpec(StringUtils.substringBeforeLast(dataSource, "_rollup"), interval.toString()));
        } else {
            ObjectNode inputSpec = (ObjectNode) ioConfig.get("inputSpec");
            List<String> paths = new ArrayList<String>();
            for (String topic : topics) {
                paths.addAll(getHourlyHdfsPath(topic, interval));
            }
            if (paths.size() == 0) {
                log.error(String.format("Cannot find hdfs path! Topics: %s, interval: %s, ingestionConfFile: %s.", topics
                        .toString(), interval.toString(), ingestionConfFile.toString()));
            }
            inputSpec.put("paths", StringUtils.join(paths, ","));
        }
        return ingestionConf.toString();
    }

    /**
     * When running in non-UTC timezones, behavior of predefined segmentGranularity constants such as "day" will change
     * when upgrading. Formerly, this would generate segments using local days. In Druid 0.10.0, this will generate
     * segments using UTC days. If you were running Druid in the UTC timezone, this change has no effect. Please note
     * that running Druid in a non-UTC timezone is not officially supported (see http://druid.io/docs/0.10.0/configuration/index.html)
     * and we recommend always running all processes in UTC timezones. To create local-day segments using Druid 0.10.0,
     * you can use a local-time segmentGranularity such as:</br>
     * <pre>
     * "segmentGranularity" : {
     *  "type" : "period",
     *  "period" : "P1D",
     *  "timeZone" : "Asia/Shanghai"
     * }
     * </pre>
     */
    private ObjectNode getSegmentGranularityForLocalTimezone(String period, TimeZone timeZone) {
        ObjectNode segmentGranularity = OBJECT_MAPPER.createObjectNode();
        segmentGranularity.put("type", "period");
        segmentGranularity.put("period", period);
        segmentGranularity.put("timeZone", timeZone.getID());
        return segmentGranularity;
    }

    /**
     * "inputSpec": {
     * "type": "dataSource",
     * "ingestionSpec": {
     * "dataSource": "readease_stat",
     * "interval": "2016-01-20T00:00:00.000+08:00/2016-01-21T00:00:00.000+08:00"
     * }
     * }
     *
     * @param dataSource
     * @param interval
     */
    public ObjectNode rollUpInputSpec(String dataSource, String interval) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode inputSpec = mapper.createObjectNode();
        inputSpec.put("type", "dataSource");
        ObjectNode ingestionSpec = mapper.createObjectNode();
        ingestionSpec.put("dataSource", dataSource);
        ingestionSpec.put("interval", interval);
        inputSpec.set("ingestionSpec", ingestionSpec);
        return inputSpec;
    }

    protected Collection<String> buildHourlyHdfsPath(@NotNull String topic, @NotNull Interval interval) throws IOException {
        Collection<Interval> intervals = getHourlyIntervals(interval);
        List<String> paths = new ArrayList<String>();
        for (Interval hourlyInterval : intervals) {
            String path = String.format("%s/%s/hourly/%s", inputHdfsPrefix, topic, hourlyInterval.getStart().toLocalDateTime()
                    .toString(HDFS_PATH_HOURLY_FORMATTER));
            if (camusClient.isNonEmptyDirectory(path)) {
                log.info("Adding hdfs path: " + path);
                paths.add(path);
            } else {
                log.info("Hdfs path is empty or not exist! Ignored: " + path);
            }
        }
        return paths;
    }

    private static void createTables(String url) throws SQLException {
        JdbcConnectionSource source = new JdbcConnectionSource(url);
        TableUtils.createTableIfNotExists(source, CamusBook.class);
        TableUtils.createTableIfNotExists(source, CamusMeta.class);
        TableUtils.createTableIfNotExists(source, HdfsFileMeta.class);
        TableUtils.createTableIfNotExists(source, DruidMeta.class);
        TableUtils.createTableIfNotExists(source, DruidBook.class);

    }

    public static void main(String[] args) throws SQLException {
        JdbcConnectionSource c = new JdbcConnectionSource("*******************************" +
                ".com:3306/knotter?user=root&password=root");
        final Dao<DruidBook, Long> dao = DaoManager.createDao(c, DruidBook.class);
        TransactionManager.callInTransaction(c, new Callable<Void>() {
            @Override
            public Void call() throws Exception {
                DruidBook druidBook = dao.queryForId((long) 243);
                druidBook.setNeedNoRetry();
                dao.update(druidBook);
                druidBook = dao.queryForId((long) 243);
                System.out.println(druidBook.getStatus());
                return null;
            }
        });
    }

    private static void createTable(String[] args) throws SQLException {
        createTables("***********************************************************************");
    }

    /**
     * @param camusBooks
     * @param path
     * @param granularity
     * @return camus books, key = camus book id, value = book
     * @throws IOException
     */
    public void readAndMergeEtlCounts(Map<String, CamusBook> camusBooks, String path, Period granularity) throws IOException {
        camusClient.readAndMergeEtlCounts(camusBooks, path, granularity);
    }

    public String commitDruidJobQuietly(String dataSource, Interval interval) {
        try {
            return commitDruidJob(dataSource, interval);
        } catch (Exception e) {
            log.error(e, e);
        }
        return null;
    }

    /**
     * only for unit test
     */
    public int createDruidMeta(DruidMeta druidMeta) throws SQLException {
        return druidMetaDao.create(druidMeta);
    }

    /**
     * only for unit test
     */
    public int createOrUpdateDruidJob(DruidJob job) throws SQLException {
        return druidJobDao.createOrUpdate(job).getNumLinesChanged();
    }

    public boolean hasDruidBookForBookIdAfter(String druidBookId, long ms) throws SQLException {
        return druidBookDao.queryBuilder().where().eq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, druidBookId).and().gt
                (DruidBook.FIELD_NAME_START_TIME, ms).queryForFirst() != null;
    }

    public Iterable<DruidMeta> queryAllDruidMetas() throws SQLException {
        return druidMetaDao.queryForAll();
    }

    public Iterable<DruidBook> queryWaitingDruidBookForBookId(String druidBookId) throws SQLException {
        return druidBookDao.queryBuilder().where().eq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, druidBookId).and().eq
                (DruidBook.FIELD_NAME_STATUS, DruidBook.STATUS_WAITING).query();
    }

    public Iterable<DruidBook> queryDruidBookForBookId(String druidBookId) throws SQLException {
        return druidBookDao.queryForEq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, druidBookId);
    }

    public Iterable<DruidBook> queryFailedDruidBookForBookId(String druidBookId) throws SQLException {
        return druidBookDao.queryBuilder().where().eq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, druidBookId).and().eq
                (DruidBook.FIELD_NAME_STATUS, DruidBook.STATUS_FAILED).query();
    }

    public Iterable<String> queryCamusHistories() throws SQLException, IOException {
        String path = queryCamusLastHistoryPath();
        return camusClient.getCamusHistoriesAfter(path == null ? minCamusLastHistoryPath : path);
    }

    public Iterable<DruidBook> queryRunningDruidBookForBookId(String druidBookId) throws SQLException {
        return druidBookDao.queryBuilder().where().eq(DruidBook.FIELD_NAME_DRUID_BOOK_ID, druidBookId).and().eq
                (DruidBook.FIELD_NAME_STATUS, DruidBook.STATUS_RUNNING).query();
    }
}