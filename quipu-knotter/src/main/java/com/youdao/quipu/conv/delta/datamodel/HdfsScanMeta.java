package com.youdao.quipu.conv.delta.datamodel;

import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@DatabaseTable(tableName = "hdfs_scan_meta")
@Data
@NoArgsConstructor
public class HdfsScanMeta {
    @DatabaseField(id = true)
    private Long id = 0L;

    @DatabaseField(columnName = "lastScanTime")
    private long lastScanTime;

    /**
     * <p>上次扫描应该提交druid task的时间段。<p/>
     * 但最近几小时的数据可能没有完全落地(去重)，所以先不提交，待下次提交。
     */
    @DatabaseField(columnName = "paths")
    private String paths;
}
