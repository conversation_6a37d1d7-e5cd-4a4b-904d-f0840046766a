{"type": "index_hadoop", "spec": {"dataSchema": {"dataSource": "readease_stat", "parser": {"type": "hadoopyString", "parseSpec": {"format": "json", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["ctId", "ctType", "sour", "level", "level2", "category", "grade", "nom", "sc", "pos", "type", "province", "city", "dayOfWeek", "hourOfWeek", "srcHost"]}}}, "metricsSpec": [{"type": "longSum", "name": "pv", "fieldName": "readease_pv"}, {"type": "longSum", "name": "impr", "fieldName": "readease_impr"}, {"type": "longSum", "name": "click", "fieldName": "readease_click"}, {"type": "longSum", "name": "content", "fieldName": "readease_content"}, {"type": "longSum", "name": "duration", "fieldName": "readease_duration"}, {"type": "cardinality", "name": "uv", "fieldNames": ["imei"]}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR", "intervals": []}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "static", "paths": ""}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "hashed", "targetPartitionSize": 1000000}, "shardSpecs": {}, "rowFlushBoundary": 1000000, "leaveIntermediate": false, "cleanupOnFailure": true, "overwriteFiles": false, "ignoreInvalidRows": false, "useCombiner": true, "jobProperties": {"mapreduce.map.memory.mb": "6000", "mapreduce.map.java.opts": "-Xmx4G", "mapreduce.reduce.memory.mb": "10000", "mapreduce.reduce.java.opts": "-Xmx7G", "mapreduce.map.output.compress": "true", "mapreduce.map.output.compress.codec": "org.apache.hadoop.io.compress.DeflateCodec", "mapreduce.job.user.classpath.first": "true"}, "combineText": false}}}