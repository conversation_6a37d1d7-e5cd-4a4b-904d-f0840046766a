{"type": "index_hadoop", "hadoopDependencyCoordinates": ["com.youdao:quipu-druid:0.0.10", "commons-io:commons-io:2.4"], "spec": {"dataSchema": {"dataSource": "sdk_stat", "parser": {"type": "avro_hadoop", "parseSpec": {"format": "avro", "timestampSpec": {"column": "timestamp", "format": "millis"}, "dimensionsSpec": {"dimensions": ["city", "province", "synd_id", "app_id", "slot_id", "network_type", "device_name", "carrier_name", "os", "agent_id", "sponsor_id", "campaign_id", "group_id", "variation_id"]}}}, "metricsSpec": [{"type": "longSum", "name": "pv", "fieldName": "pv"}, {"type": "longSum", "name": "request_ad_num", "fieldName": "request_ad_num"}, {"type": "cardinality", "name": "uv", "fieldNames": ["pv_device_id"]}, {"type": "longSum", "name": "bid", "fieldName": "bid"}, {"type": "cardinality", "name": "ub", "fieldNames": ["bid_device_id"]}, {"type": "longSum", "name": "impr", "fieldName": "impr"}, {"type": "cardinality", "name": "ui", "fieldNames": ["impr_device_id"]}, {"type": "longSum", "name": "charge", "fieldName": "charge"}, {"type": "longSum", "name": "click", "fieldName": "click"}, {"type": "cardinality", "name": "uc", "fieldNames": ["ext.cdeviceid"]}, {"type": "longSum", "name": "conv", "fieldName": "conv"}, {"type": "longSum", "name": "loc_conv", "fieldName": "loc_conv"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "HOUR", "queryGranularity": "HOUR", "intervals": []}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "static", "inputFormat": "com.youdao.quipu.druid.avro.AvroValueInputFormat", "paths": ""}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "hashed", "targetPartitionSize": 1000000}, "shardSpecs": {}, "rowFlushBoundary": 1000000, "leaveIntermediate": false, "cleanupOnFailure": true, "overwriteFiles": false, "ignoreInvalidRows": false, "useCombiner": true, "jobProperties": {"mapreduce.map.memory.mb": "6000", "mapreduce.map.java.opts": "-Xmx4G", "mapreduce.reduce.memory.mb": "10000", "mapreduce.reduce.java.opts": "-Xmx7G", "mapreduce.map.output.compress": "true", "mapreduce.map.output.compress.codec": "org.apache.hadoop.io.compress.DeflateCodec", "mapreduce.job.user.classpath.first": "true"}, "combineText": false}}}