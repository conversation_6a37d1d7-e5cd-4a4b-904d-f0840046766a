{"type": "index_hadoop", "hadoopCoordinates": "org.apache.hadoop:hadoop-client:2.4.0", "spec": {"dataSchema": {"dataSource": "ead_stat", "parser": {"type": "eadStat", "parseSpec": {"format": "json", "timestampSpec": {"column": "impr_time", "format": "millis"}, "dimensionsSpec": {"dimensions": ["campaign_id", "channel_id", "group_id", "height", "keyword_id", "match_id", "pos", "province", "site_id", "slot_id", "sponsor_id", "style_id", "synd_id", "variation_id", "width"], "dimensionExclusions": ["impr_time"], "spatialDimensions": []}}}, "metricsSpec": [{"type": "count", "name": "impr"}], "granularitySpec": {"type": "uniform", "segmentGranularity": "DAY", "queryGranularity": {"type": "none"}, "intervals": ["2014-10-29T00:00:00.000+08:00/2014-10-29T01:00:00.000+08:00"]}}, "ioConfig": {"type": "hadoop", "inputSpec": {"type": "dataSource", "ingestionSpec": {"dataSource": "sdk_stat", "interval": "2016-01-20T00:00:00.000+08:00/2016-01-21T00:00:00.000+08:00"}}, "metadataUpdateSpec": null, "segmentOutputPath": null}, "tuningConfig": {"type": "hadoop", "workingPath": null, "partitionsSpec": {"type": "dimension", "partitionDimension": null, "targetPartitionSize": 5000000, "maxPartitionSize": 7500000, "assumeGrouped": false, "numShards": -1}, "shardSpecs": {}, "rowFlushBoundary": 500000, "leaveIntermediate": false, "cleanupOnFailure": false, "overwriteFiles": false, "ignoreInvalidRows": true, "jobProperties": {"mapred.child.java.opts": "-Xmx6500M", "mapreduce.map.memory.mb": "8000", "mapreduce.reduce.memory.mb": "8000"}, "combineText": false}}}