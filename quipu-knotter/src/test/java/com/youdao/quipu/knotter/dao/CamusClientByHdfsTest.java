package com.youdao.quipu.knotter.dao;

import org.apache.hadoop.conf.Configuration;
import org.joda.time.Period;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import static com.youdao.quipu.knotter.TimeUtils.getIsoTimestamp;

/**
 * <AUTHOR>
 */
public class CamusClientByHdfsTest {
    private CamusClientByHdfs client;
    @Before
    public void before(){
        client = new CamusClientByHdfs(new Configuration());
    }

    @Test
    public void testGetCamusBookKey() {
        String key = client.getCamusBookKey("topic", Period.minutes(10), "{a,b," + getIsoTimestamp
                ("2014-11-11T16:04:04") + "}");
        Assert.assertEquals("topic_2014-11-11T16:00:00.000+08:00/2014-11-11T16:10:00.000+08:00", key);
        key = client.getCamusBookKey("topic", Period.days(1), "{a,b," + getIsoTimestamp("2014-11-11T16:04:04+08:00") + "}");
        Assert.assertEquals("topic_2014-11-11T00:00:00.000+08:00/2014-11-12T00:00:00.000+08:00", key);
    }
}
