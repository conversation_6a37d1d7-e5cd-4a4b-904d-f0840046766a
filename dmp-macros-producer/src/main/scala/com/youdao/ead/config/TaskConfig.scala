package com.youdao.ead.config

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import scopt.OptionParser

/**
 * 命令行参数解析
 */

case class TaskConfig(task: String, startHour: LocalDateTime, endHour: LocalDateTime)

object TaskConfigParser {
  private val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd/HH")

  implicit val localDateRead: scopt.Read[LocalDateTime] =
    scopt.Read.reads(LocalDateTime.parse(_, dateTimeFormatter))

  val parser: OptionParser[TaskConfig] = new scopt.OptionParser[TaskConfig]("scopt") {
    // ignore unknown parameter
    override def errorOnUnknownArgument: Boolean = false

    opt[String]('t', "task").action((x, c) =>
      c.copy(task = x)).text("task name")

    opt[LocalDateTime]('s', "startHour").action((x, c) =>
      c.copy(startHour = x)).text("the start hour, eg. 2017-07-24-00")

    opt[LocalDateTime]('e', "endHour").action((x, c) =>
      c.copy(endHour = x)).text("the end hour, min: startHour + 1")

    checkConfig(c =>
      if (!c.endHour.isBefore(c.startHour) ) success
      else failure("The param must be delayHour >= 0 and if azkaban.sh endHour >= startHour else endHour > startHour")
    )
  }
}