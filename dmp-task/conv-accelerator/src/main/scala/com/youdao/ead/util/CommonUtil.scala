package com.youdao.ead.util

import outfox.venus.client2.VenusClient

import java.time.format.DateTimeFormatter

object CommonUtil {

  val hdfsTimeFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd")

  val INVALID_DEVICE_ID_SEQ = Set("UNKNOWN", "00000000-0000-0000-0000-000000000000", "00000000000000000000000000000000")

//  // test
//  val venusAddressTest = "venus-test-zk.corp.youdao.com:2181"
//  val venusRedisUriTest = "redis://<EMAIL>:6379"
//  val venusTimeoutTest = 200L
  // pord
  val venusAddress = "venus-prod-zk.corp.youdao.com:2181"
  val venusRedisUri = "redis://<EMAIL>:6379"
  val venusTimeout = 200L

  def venusClient() : VenusClient = {
//    new VenusClient(venusAddressTest, venusRedisUriTest, venusTimeoutTest)
    new VenusClient(venusAddress, venusRedisUri, venusTimeout)
  }
}
