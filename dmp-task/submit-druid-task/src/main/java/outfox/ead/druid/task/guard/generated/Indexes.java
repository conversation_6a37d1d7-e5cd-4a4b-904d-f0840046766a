/*
 * This file is generated by jOOQ.
*/
package outfox.ead.druid.task.guard.generated;


import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.Internal;
import outfox.ead.druid.task.guard.generated.tables.DruidTask;

import javax.annotation.Generated;


/**
 * A class modelling indexes of tables of the <code>apolo</code> schema.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.10.7"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index DRUID_TASK_PRIMARY = Indexes0.DRUID_TASK_PRIMARY;

    // -------------------------------------------------------------------------
    // [#1459] distribute members to avoid static initialisers > 64kb
    // -------------------------------------------------------------------------

    private static class Indexes0 {
        public static Index DRUID_TASK_PRIMARY = Internal.createIndex("PRIMARY", DruidTask.DRUID_TASK, new OrderField[] { DruidTask.DRUID_TASK.ID }, true);
    }
}
