package com.youdao.ead;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.io.Closeables;
import com.youdao.ead.util.DruidUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeZone;
import org.joda.time.Interval;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.io.FileInputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;

/**
 * <AUTHOR>
 * @date 2023/9/6
 */
@Slf4j
public class SubmitDruidTaskMain {
    private static final DateTimeFormatter HDFS_PATH_HOURLY_FORMATTER = DateTimeFormat.forPattern("yyyy/MM/dd/HH")
            .withZone(DateTimeZone.getDefault());
    private static final DateTimeFormatter HDFS_PATH_DAILY_FORMATTER = DateTimeFormat.forPattern("yyyy/MM/dd")
            .withZone(DateTimeZone.getDefault());

    /**
     *
     * @param args useTaskGuard + startDateTime + endDateTime + intervalFixed + ingestionConfFile + ioConfigRootPath
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        log.info("args:{}", StringUtils.join(args," "));
        boolean useTaskGuard = args.length <= 0 || "true".equals(args[0]);
        LocalDateTime startDateTime = parseDate(args.length > 1 ? args[1] : "", LocalDate.now().minusDays(1)).atStartOfDay();
        LocalDateTime endDateTime = parseDate(args.length > 2 ? args[2] : "", LocalDate.now()).atStartOfDay();
        if (dateInvalid(startDateTime, endDateTime)) {
            log.error("invalid date param");
            return;
        }
        Interval interval = new Interval(startDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli(),
                endDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        // druid摄入数据范围，特殊处理逻辑 ！！
        boolean intervalFixed = args.length > 3 && "true".equals(args[3]);
        Interval intervalForDruid = intervalFixed ? new Interval(
                // test
//                LocalDateTime.of(2023, 12, 1, 0, 0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli(),
//                LocalDateTime.of(2023, 12, 2, 0, 0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli()
                LocalDateTime.of(2024, 1, 1, 0, 0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli(),
                LocalDateTime.of(2024, 1, 2, 0, 0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli()
        ) : interval;

        Properties properties = new Properties();
        properties.load(SubmitDruidTaskMain.class.getClassLoader().getResourceAsStream("application.properties"));
        String ingestionConfFile = args.length > 4 ? args[4] : properties.getProperty("druid.ingestion_conf_file");
        String ioConfigRootPath = args.length > 5 ? args[5] : properties.getProperty("druid.io_config.root_path");
        String ingestionConf = loadAndResetIngestionConf(interval, intervalForDruid, ingestionConfFile, ioConfigRootPath);
        log.info("loadAndResetIngestionConf: conf={}", ingestionConf);

        String druidOverloadUrl = properties.getProperty("druid.overload.rest.url");

        String taskId = DruidUtils.commitJob(ingestionConf, druidOverloadUrl, useTaskGuard);
        log.info("commitJob success: taskId={}", taskId);
    }



    private static LocalDate parseDate(String dateString, LocalDate defaultDate) {
        try {
            return LocalDate.parse(dateString, ISO_LOCAL_DATE);
        } catch (Exception e) {
            log.error("Convert query string error...", e);
        }
        return defaultDate;
    }

    private static boolean dateInvalid(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (startDateTime.isBefore(endDateTime)) {
            if (startDateTime.isBefore(LocalDateTime.now())) {
                return false;
            }
        }
        return true;
    }

    private static String loadAndResetIngestionConf(Interval interval, Interval intervalForDruid, String ingestionConfFile, String ioConfigRootPaths) throws Exception{
        InputStream inputStream = null;
        ObjectNode ingestionConf;
        // read conf from file
        try {
            if (ingestionConfFile.startsWith("classpath:")) {
                inputStream = SubmitDruidTaskMain.class.getResourceAsStream(StringUtils.substringAfter(ingestionConfFile,
                        "classpath:"));
            } else {
                inputStream = new FileInputStream(ingestionConfFile);
            }
            ingestionConf = (ObjectNode) new ObjectMapper().readTree(inputStream);
        } finally {
            Closeables.close(inputStream, true);
        }
        // reset intervals
        ObjectNode granularitySpec = (ObjectNode) ingestionConf.get("spec").get("dataSchema").get("granularitySpec");
        ArrayNode intervals = (ArrayNode) granularitySpec.get("intervals");
        intervals.removeAll();
        // 特殊处理逻辑 ！！
        intervals.add(intervalForDruid.toString());
        // reset ioConfig
        boolean hourly = "HOUR".equals(granularitySpec.get("segmentGranularity").asText());
        List<String> paths = new ArrayList<String>();
        if (StringUtils.isNotBlank(ioConfigRootPaths)) {
            for (String rootPath : ioConfigRootPaths.split(",")) {
                if (StringUtils.isNotBlank(rootPath)) {
                    org.joda.time.LocalDateTime start = interval.getStart().toLocalDateTime();
                    org.joda.time.LocalDateTime end = interval.getEnd().toLocalDateTime();
                    while (start.isBefore(end)) {
                        paths.add(rootPath + start.toString((hourly ? HDFS_PATH_HOURLY_FORMATTER : HDFS_PATH_DAILY_FORMATTER)));
                        start = hourly ? start.plusHours(1) : start.plusDays(1);
                    }
                }
            }
        }
        if (paths.size() == 0) {
            log.error(String.format("loadAndResetIngestionConf cannot find hdfs path! ioConfigRootPaths: %s, interval: %s, intervalForDruid: %s, ingestionConfFile: %s.", ioConfigRootPaths, interval.toString(), intervalForDruid.toString(), ingestionConfFile.toString()));
        }
        ObjectNode ioConfig = (ObjectNode) ingestionConf.get("spec").get("ioConfig");
        ObjectNode inputSpec = (ObjectNode) ioConfig.get("inputSpec");
        inputSpec.put("paths", StringUtils.join(paths, ","));

        return ingestionConf.toString();
    }
}
