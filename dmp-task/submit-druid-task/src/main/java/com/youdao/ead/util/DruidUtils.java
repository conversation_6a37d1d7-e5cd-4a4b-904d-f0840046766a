package com.youdao.ead.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.WebResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.SQLDialect;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultConfiguration;
import outfox.ead.druid.task.guard.generated.tables.records.DruidTaskRecord;
import outfox.ead.druid.task.guard.jdbc.ConnectionPool;

import javax.ws.rs.core.MediaType;

import static outfox.ead.druid.task.guard.generated.Tables.DRUID_TASK;

/**
 * <AUTHOR>
 * @date 2023/9/6
 */
@Slf4j
public class DruidUtils {

    private static DSLContext dslContext;

    static {
        try {
            dslContext = DSL.using(new DefaultConfiguration()
                    .set(new ConnectionPool().getConnection())
                    .set(SQLDialect.MYSQL));
        } catch (Exception e) {
            log.error("init jooq failed!", e);
        }
    }

    public static String commitJob(String ingestionConf, String druidOverlordUrl, boolean useTaskGuard) {
        Client client = null;
        String taskId = null;
        try {
            client = Client.create();
            WebResource webResource = client.resource(druidOverlordUrl);
            ClientResponse response = webResource.type(MediaType.APPLICATION_JSON).post(ClientResponse.class, ingestionConf);
            taskId = new ObjectMapper().readTree(response.getEntity(String.class)).get("task").asText();
            if (StringUtils.isNotBlank(taskId)) {
                if (useTaskGuard) {
                    write2ApoloDruidTask(taskId, ingestionConf);
                }
                // !!!! 这里的日志关键字不能改，在check_job.sh中有用到
                log.info("success submit druid task, id {}", taskId);
            } else {
                log.error("failed submit druid task, ingestionConf {}", ingestionConf);
            }
        } catch (Exception e) {
            log.error("Exception while commit job to druid: " + ingestionConf, e);
        } finally {
            if (client != null) {
                client.destroy();
            }
        }
        return taskId;
    }

    private static void write2ApoloDruidTask(String taskId, String ingestionConf) {
        DruidTaskRecord task = dslContext.newRecord(DRUID_TASK);
        task.setTaskId(taskId);
        task.setPalyload(ingestionConf);
        task.setRetriedTimes(0);
        task.setStatus("RUNNING");
        dslContext.executeInsert(task);
        log.info("insert new druid task to apolo.\n{}", task);
    }
}
