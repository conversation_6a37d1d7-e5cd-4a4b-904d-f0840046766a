package com.youdao.ead.getui.dwm

import com.youdao.ead.ProduceEadDmp.logger
import com.youdao.ead.config.ProduceEadDmpConfig
import com.youdao.ead.config.ProduceEadDmpConfig.GETUI_CURRENT_INSTALL_DIR
import com.youdao.ead.util.HdfsUtil
import org.apache.spark.sql.{DataFrame, SparkSession}

import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * DWM：个推当前安装列表的 app_id 数据
 *
 * <AUTHOR>
 * @date 2024/9/12
 */
object GetuiCurrentInstall {
  def main(args: Array[String]): Unit = {
    implicit val spark: SparkSession = SparkSession
      .builder()
      .appName("ead-dmp-getui-current-install")
      .getOrCreate()
    val date = LocalDate.now()
    val validDate = HdfsUtil.getLatestDate(spark, GETUI_CURRENT_INSTALL_DIR, "yyyy/MM/dd", date)
    logger.info(s"latest data rootPath=$GETUI_CURRENT_INSTALL_DIR, validDate=$validDate")
    val lastestData = spark.read.format("avro")
      .load(GETUI_CURRENT_INSTALL_DIR + DateTimeFormatter.ofPattern("yyyy/MM/dd").format(validDate) + "/*.avro")
      .select("device_id", "idfa", "oaid", "install_applist", "last_get_day")
    val newData = incrementalUpdate(lastestData)
    newData.coalesce(10)
      .write
      .format("avro")
      .mode("overwrite")
      .save(ProduceEadDmpConfig.GETUI_CURRENT_INSTALL_DIR + DateTimeFormatter.ofPattern("yyyy/MM/dd").format(date) + "/")
  }

  private def incrementalUpdate(lastestData: DataFrame): DataFrame = {
    // todo 读取新的数据。然后与原来数据merge更新
    val newData = lastestData
    newData
  }
}
