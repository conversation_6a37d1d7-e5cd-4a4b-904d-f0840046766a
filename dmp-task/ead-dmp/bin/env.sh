#!/usr/bin/env bash

export JAVA_HOME=/usr/local/youdao/ad/jdk1.8.0_202
export PATH=$JAVA_HOME/bin:$PATH

export MAVEN_HOME=/usr/local/youdao/ad/apache-maven-3.5.4
export PATH=$MAVEN_HOME/bin:$PATH

export SPARK_HOME="/mfs_ead/global/ead/spark-2.4.0-bin-hadoop2.7/"
export PATH=$SPARK_HOME/bin:$PATH

export HADOOP_HOME=/mfs_ead/global/eadata/hadoop-2.7.1
export PATH=$HADOOP_HOME/bin:$PATH

export HADOOP_CONF_DIR=/mfs_ead/eadata/online/hadoop-conf
source $HADOOP_CONF_DIR/hadoop-env.sh

mfs_path="/mfs_ead"

if ! grep -qs ${mfs_path} /proc/mounts; then
    echo "${mfs_path} should be mounted before start task";
    exit 1;
fi

username=$(whoami)
user_home="${mfs_path}/${username}"
if [[ ! -d ${user_home} ]]; then
  echo "${user_home} not exist, please create it"
  exit 1;
fi

