#!/bin/bash
set -e
source ./bin/env.sh

$SPARK_HOME/bin/spark-submit \
  --class com.youdao.ead.conv.SponsorIndustryConv \
  --master yarn \
  --deploy-mode client \
  --driver-memory 4G \
  --queue prior \
  --executor-memory 12G \
  --executor-cores 10 \
  --num-executors 20 \
  --packages org.apache.spark:spark-avro_2.11:2.4.0 \
  --conf spark.network.timeout=600 \
  --conf spark.yarn.am.memory=2048 \
  --conf spark.executor.heartbeatInterval=60 \
  --conf spark.sql.shuffle.partitions=200 \
  --conf spark.port.maxRetries=300 \
  --conf spark.hadoop.yarn.timeline-service.enabled=false \
  --conf spark.executor.extraJavaOptions='-XX:+PrintGCDetails -XX:+HeapDumpOnOutOfMemoryError' \
  $(pwd)/target/ead-dmp.jar 2>&1

# 定义一个方法，传入 HDFS 路径作为参数
delete_hdfs_path() {
    local hdfs_path=$1  # 传入的 HDFS 路径
    if hadoop fs -test -e $hdfs_path; then
        echo "delete bak dir：$hdfs_path"
        hadoop fs -rm -r "$hdfs_path"  # 如果路径存在，执行删除操作
    fi
}
day=/user/eadata/ead_dmp/conv/sponsor_industry_conv/snapshot_daily/$(date -d "$current_date -30 day" +%Y-%m-%d)
delete_hdfs_path $day
day=/user/eadata/ead_dmp/conv/sponsor_industry_conv/snapshot_daily/$(date -d "$current_date -31 day" +%Y-%m-%d)
delete_hdfs_path $day
